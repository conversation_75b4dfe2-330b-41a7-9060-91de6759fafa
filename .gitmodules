[submodule "lowcode4j-apps/app-dahvcdreport"]
	path = lowcode4j-apps/app-dahvcdreport
	url = http://***********:9090/dataapp/app-dahvcdreport.git
	branch = V1.1.2_Beta2
[submodule "toolpub"]
	path = toolpub
	url = http://***********:9090/dataapp/toolpub.git
	branch = dev
[submodule "modelpub"]
	path = modelpub
	url = http://***********:9090/dataapp/modelpub.git
	branch = dev
[submodule "stupub"]
	path = stupub
	url = http://***********:9090/dataapp/stupub.git
[submodule "teapub"]
	path = teapub
	url = http://***********:9090/dataapp/teapub.git
	branch = dev
[submodule "lowcode4j-apps/app-grsjzx"]
	path = lowcode4j-apps/app-grsjzx
	url = http://***********:9090/dataapp/app-grsjzx.git
	branch = dev
[submodule "hvspub"]
	path = hvspub
	url = http://***********:9090/dataapp/hvspub.git
	branch = V1.1.2_Beta2
[submodule "jspub"]
	path = jspub
	url = http://***********:9090/dataapp/jspub.git
	branch = dev
[submodule "portraittoolpub"]
	path = portraittoolpub
	url = http://***********:9090/dataapp/portraittoolpub.git
	branch = dev
[submodule "lowcode4j-apps/app-stuportrait"]
	path = lowcode4j-apps/app-stuportrait
	url = http://***********:9090/dataapp/app-stuportrait.git
[submodule "lowcode4j-apps/app-vedashboard"]
	path = lowcode4j-apps/app-vedashboard
	url = http://***********:9090/dataapp/app-vedashboard.git
	branch = V1.1.1_Beta3
[submodule "lowcode4j-apps/app-studatacenter"]
	path = lowcode4j-apps/app-studatacenter
	url = http://***********:9090/dataapp/app-studatacenter.git
	branch = dev
[submodule "lowcode4j-apps/app-portraitdemo"]
    path = lowcode4j-apps/app-portraitdemo
    url = http://***********:9090/dataapp/app-portraitdemo.git
[submodule "lowcode4j-apps/app-servicecenter"]
	path = lowcode4j-apps/app-servicecenter
	url = http://***********:9090/dataapp/app-servicecenter.git
	branch = dev
[submodule "lowcode4j-apps/app-teaportrait"]
	path = lowcode4j-apps/app-teaportrait
	url = http://***********:9090/dataapp/app-teaportrait.git
[submodule "custom-hr"]
	path = custom-hr
	url = http://***********:9090/dataapp/custom-hr.git
[submodule "lowcode4j-apps/app-stufrportrait"]
	path = lowcode4j-apps/app-stufrportrait
	url = http://***********:9090/dataapp/app-stufrportrait.git
	branch = dev
[submodule "lowcode4j-apps/app-doublefirstclassdata"]
	path = lowcode4j-apps/app-doublefirstclassdata
	url = http://***********:9090/dataapp/doublefirstclassdata.git
	branch = 1.1.2_R1
[submodule "lowcode4j-apps/app-pgeducationquality"]
	path = lowcode4j-apps/app-pgeducationquality
	url = http://***********:9090/dataapp/pgeducationquality.git
	branch = 1.1.2_R1
[submodule "lowcode4j-apps/app-portrait"]
	path = lowcode4j-apps/app-portrait
	url = http://***********:9090/dataapp/app-portrait.git
	branch = dev
[submodule "lowcode4j-apps/app-monitor"]
	path = lowcode4j-apps/app-monitor
	url = http://***********:9090/dataapp/app-monitor.git
	branch = 1.1.1_R3
[submodule "lowcode4j-apps/app-modelquery"]
	path = lowcode4j-apps/app-modelquery
	url = http://***********:9090/dataapp/app-modelquery.git
[submodule "rstbfwpub"]
	path = rstbfwpub
	url = http://***********:9090/dataapp/rstbfwpub.git
[submodule "lowcode4j-apps/app-rstbfwcs"]
	path = lowcode4j-apps/app-rstbfwcs
	url = http://***********:9090/dataapp/app-rstbfwcs.git
[submodule "lowcode4j-apps/app-pgedudataanalytics"]
	path = lowcode4j-apps/app-pgedudataanalytics
	url = http://***********:9090/dataapp/pgedudataanalytics.git
	branch = 1.1.1_R1
[submodule "lowcode4j-apps/app-doublefirstclass"]
	path = lowcode4j-apps/app-doublefirstclass
	url = http://***********:9090/dataapp/app-doublefirstclass.git
	branch = dev
[submodule "lowcode4j-apps/app-infobenchmark"]
	path = lowcode4j-apps/app-infobenchmark
	url = http://***********:9090/dataapp/app-infobenchmark.git
	branch = dev
[submodule "aipub"]
	path = aipub
	url = http://***********:9090/dataapp/aipub.git
[submodule "lowcode4j-apps/app-dataqueryai"]
	path = lowcode4j-apps/app-dataqueryai
	url = http://***********:9090/dataapp/app-dataqueryai.git
	branch = master
[submodule "metadata"]
	path = metadata
	url = http://***********:9090/dataapp/metadata.git
[submodule "yjs-metadata"]
	path = yjs-metadata
	url = http://***********:9090/dataapp/yjs-metadata.git
[submodule "docs"]
	path = docs
	url = http://***********:9090/dataapp/dataapp-doc.git
	branch = dev
[submodule "lowcode4j-apps/app-szindex"]
	path = lowcode4j-apps/app-szindex
	url = http://***********:9090/dataapp/app-szindex.git
	branch = dev
[submodule "lowcode4j-apps/app-bkteachingeval"]
	path = lowcode4j-apps/app-bkteachingeval
	url = http://***********:9090/teaching-group/app-bkteachingeval.git
	branch = master
[submodule "lowcode4j-apps/app-szjwtodo"]
	path = lowcode4j-apps/app-szjwtodo
	url = http://***********:9090/teaching-group/app-szjwtodo.git
	branch = master
[submodule "lowcode4j-apps/app-qualitydashboard"]
	path = lowcode4j-apps/app-qualitydashboard
	url = http://***********:9090/teaching-group/app-qualitydashboard.git
	branch = master
[submodule "lowcode4j-apps/app-qualityperformance"]
	path = lowcode4j-apps/app-qualityperformance
	url = http://***********:9090/teaching-group/app-qualityperformance.git
	branch = master
[submodule "lowcode4j-apps/app-sjtbgl"]
	path = lowcode4j-apps/app-sjtbgl
	url = http://***********:9090/teaching-group/app-sjtbgl.git
	branch = master
[submodule "njnu"]
	path = njnu
	url = http://***********:9090/HanJunJun/njnu.git
	branch = dev
[submodule "lowcode4j-apps/app-modeltool"]
	path = lowcode4j-apps/app-modeltool
	url = http://***********:9090/dataapp/app-modeltool.git
	branch = dev
[submodule "lowcode4j-apps/app-qualitycompareanalys"]
	path = lowcode4j-apps/app-qualitycompareanalys
	url = http://***********:9090/teaching-group/app-qualitycompareanalys.git
	branch = master
[submodule "lowcode4j-apps/app-researchdecisionanal"]
	path = lowcode4j-apps/app-researchdecisionanal
	url = http://***********:9090/dataapp/app-researchdecisionanal.git
	branch = dev
[submodule "lowcode4j-apps/app-compquery"]
	path = lowcode4j-apps/app-compquery
	url = http://***********:9090/dataapp/app-compquery.git
[submodule "qcapub"]
	path = qcapub
	url = http://***********:9090/teaching-group/qcapub.git
	branch = master
[submodule "lowcode4j-apps/app-majorcompareanalys"]
	path = lowcode4j-apps/app-majorcompareanalys
	url = http://***********:9090/teaching-group/app-majorcompareanalys.git
	branch = master
[submodule "drctpub"]
	path = drctpub
	url = http://***********:9090/dataapp/drctpub.git
	branch = dev
[submodule "lowcode4j-apps/app-jwqualityeval"]
	path = lowcode4j-apps/app-jwqualityeval
	url = http://***********:9090/teaching-group/app-jwqualityeval.git
	branch = master
[submodule "lowcode4j-apps/app-szjwhome"]
	path = lowcode4j-apps/app-szjwhome
	url = http://***********:9090/teaching-group/app-szjwhome.git
	branch = master
[submodule "jwdatapub"]
	path = jwdatapub
	url = http://***********:9090/teaching-group/jwdatapub.git
	branch = master
[submodule "lowcode4j-apps/app-monitorwarn"]
	path = lowcode4j-apps/app-monitorwarn
	url = http://***********:9090/dataapp/app-monitorwarn.git
	branch = dev
