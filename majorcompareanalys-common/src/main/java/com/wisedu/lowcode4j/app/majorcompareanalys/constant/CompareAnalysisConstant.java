package com.wisedu.lowcode4j.app.majorcompareanalys.constant;

import com.wisedu.lowcode4j.main.qcapub.enums.QualityAnalysisModelClassify;

/**
 * @ClassName: CompareAnalysisConstant
 * @Author: leiyy
 * @Date: 2025/5/13 10:35
 * @Description: 群体对比分析常量类
 */
public class CompareAnalysisConstant {
    /**
     * 指标分类
     */
    public static final QualityAnalysisModelClassify MODEL_CLASSIFY = QualityAnalysisModelClassify.MAJOR_ANALYSIS;

    /**
     * SQL ID
     */
    public interface SQL_ID {
        /**
         * 获取专业指标数据
         */
        String JWQCA_ZY_ZB = "jwqca_zy_zb";

        /**
         * 群体分析-专业列表
         */
        String JWQCA_QTFX_ZY_LB = "page_zyjbxxlb_zyjbxxlb";
    }

    /**
     * 基础表信息
     */
    public interface BASE_TABLE_INFO {
        /**
         * 本专科生基本情况表
         */
        String BASE_TABLE_NAME = "T_JWQCA_ZYJBQK";

        /**
         * 基础表时间标识字段
         */
        String BASE_TABLE_TIME_COLUMN_NAME = BASE_TABLE_NAME + ".TJNF";

        /**
         * 基础表实体标识字段
         */
        String BASE_TABLE_ENTITY_COLUMN_NAME = BASE_TABLE_NAME + ".XNZYDM";

        /**
         * 学院
         */
        String COLLEGE_COLUMN_NAME = BASE_TABLE_NAME + ".SSDWH";

        /**
         * 校内专业代码
         */
        String MAJOR_COLUMN_NAME = BASE_TABLE_NAME + ".XNZYDM";
    }

    public interface SQL_TEMPLATE {
        /**
         * 群体对比item查询SQL模板
         */
        String GROUP_DB_ITEM_SQL_TEMPLATE = "SELECT T_JWQCA_ZYJBQK.TJNF AS STATISTIC_YEAR, " +
                " ${groupColumns}, " +
                " '${modelId}' AS MODEL_ID, " +
                " '${groupName}' AS GROUP_NAME, " +
                " '${classifyCode}' AS CLASSIFY_CODE, " +
                " '${classifyName}' AS CLASSIFY_NAME, " +
                " ROUND(AVG(${modelTable}.${valueColumnName}), 2) AS VALUE, " +
                " T_JWQCA_ZYJBQK.XNZYDM as ITEM_CODE, " +
                " T_JWQCA_ZYJBQK.SSDWH as GROUP_CODE, " +
                " T_JWQCA_ZYJBQK.SSDWMC as ITEM_SUB_NAME" +
                " FROM T_JWQCA_ZYJBQK " +
                " LEFT JOIN ${modelTable} ON ${joinColumn} = T_JWQCA_ZYJBQK.TJNF AND ${modelTable}.${entityColumnName} = T_JWQCA_ZYJBQK.XNZYDM " +
                " WHERE T_JWQCA_ZYJBQK.TJNF = ${year} AND ${condition}" +
                " GROUP BY T_JWQCA_ZYJBQK.TJNF, ${groupColumns}, T_JWQCA_ZYJBQK.SSDWH, T_JWQCA_ZYJBQK.SSDWMC, T_JWQCA_ZYJBQK.XNZYDM";
    }
}
