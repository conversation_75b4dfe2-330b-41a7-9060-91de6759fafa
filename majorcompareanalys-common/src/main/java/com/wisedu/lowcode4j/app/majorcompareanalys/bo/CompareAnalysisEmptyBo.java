package com.wisedu.lowcode4j.app.majorcompareanalys.bo;

import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;
import com.wisedu.lowcode4j.common.model.constant.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;
import org.sagacity.sqltoy.model.SecureType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;
import com.alibaba.fastjson.annotation.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */


@ApiModel(value = "compareanalysisemptybo", description = "群体分析空模型")
@Comment("群体分析空模型")
@ModelDefine(renderType="form")
@Processor(processorBean="compareAnalysisModelProcessor")
//end_dynamic_declare
@Data
public class CompareAnalysisEmptyBo extends BaseBizModel {

    private static final long serialVersionUID = 4431474721041958236L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zymc", value = "专业名称" ,notes = "2025-06-10 14:54:45")
    @Comment("专业名称")
    @Column(value = "zymc")
    @ColumnDefine(columnXtype="text")
    private String zymc;

    @ApiModelProperty(name = "id", value = "id" ,notes = "2025-06-10 14:54:33")
    @Comment("id")
    @Column(value = "id")
    @ColumnDefine(columnDisplay=false,columnHidden=true,columnXtype="text",tableHidden=true,formHidden=true,searchHidden=true)
    private String id;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades

    //region start_dynamic_dict_column
	//endregion end_dynamic_dict_column

}
