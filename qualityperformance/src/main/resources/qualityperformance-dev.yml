spring:
  resources:
    static-locations: classpath:page,classpath:static
  thymeleaf:
    prefix: templates
    suffix: .html
    mode: html
    encoding: utf-8
    cache: false
    templateResolverOrder: 1
  flyway:
    enabled: true
    locations: classpath:dbsql/
    table: lowcode_schema_szjwzlbx
    baseline-on-migrate: true
context:
  listener:
    classes: com.wisedu.lowcode4j.app.common.listener.MyListener
nutz:
  dao:
    enabled: true
    datasource: master
    create: true #是否自动建表 默认true
    migration: true #是否自动变更 默认true
    add-column: true # 是否添加列 默认true
    delete-column: false # 是否删除列 默认true
    foce-create: false # 是否删表重建，注意此功能会删除全部表及数据，一般应用于demo或测试 默认false
    check-index: false # 是否检查索引 默认true
    basepackage: # 相关实体所在包
    - com.wisedu.lowcode4j.app.qualityperformance.po
    - com.wisedu.lowcode4j.app.qualityperformance.qtbx.po
    - com.wisedu.lowcode4j.app.qualityperformance.ztbx.po
    popackage: # 数据模型所在包
    - com.wisedu.lowcode4j.app.qualityperformance.po
    - com.wisedu.lowcode4j.app.qualityperformance.qtbx.po
    - com.wisedu.lowcode4j.app.qualityperformance.ztbx.po
    bopackage: # 业务模型所在包
    - com.wisedu.lowcode4j.app.qualityperformance.bo
    - com.wisedu.lowcode4j.app.qualityperformance.qtbx.bo
    - com.wisedu.lowcode4j.app.qualityperformance.ztbx.bo
dict:
  packages:
  - com.wisedu.lowcode4j.app.qualityperformance.constant
  - com.wisedu.lowcode4j.app.qualityperformance.qtbx.constant
  - com.wisedu.lowcode4j.app.qualityperformance.ztbx.constant
