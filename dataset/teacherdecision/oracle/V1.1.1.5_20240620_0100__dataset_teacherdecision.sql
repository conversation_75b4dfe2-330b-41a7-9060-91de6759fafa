/*
 Description		: [师资决策分析(teacherdecision)]应用数据集
 Author				: dataapp
 Date				: 2024-06-20 01:00:00
 Lowcode Version	: V1.1.1_Beta5
 Database           : oracle
*/

-- 数据集模型字段
BEGIN
    EXECUTE IMMEDIATE 'CREATE TABLE t_da_dataset_model_column ( dataset_id VARCHAR2(50) NOT NULL,
column_id VARCHAR2(50) NOT NULL,
model_id VARCHAR2(50) NOT NULL,
id VARCHAR2(100),
create_by VARCHAR2(100),
create_time DATE,
update_by VARCHAR2(100),
update_time DATE,PRIMARY KEY (id) )';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

COMMENT ON TABLE t_da_dataset_model_column IS '数据集模型字段';
COMMENT ON COLUMN t_da_dataset_model_column.dataset_id IS '数据集ID';
COMMENT ON COLUMN t_da_dataset_model_column.column_id IS '字段ID';
COMMENT ON COLUMN t_da_dataset_model_column.model_id IS '模型ID';
COMMENT ON COLUMN t_da_dataset_model_column.id IS 'ID';
COMMENT ON COLUMN t_da_dataset_model_column.create_by IS '创建人';
COMMENT ON COLUMN t_da_dataset_model_column.create_time IS '创建时间';
COMMENT ON COLUMN t_da_dataset_model_column.update_by IS '更新人';
COMMENT ON COLUMN t_da_dataset_model_column.update_time IS '更新时间';

-- 师资决策分析-数据集
INSERT INTO t_da_dataset (id,sjjmc,
create_by,create_time,update_by,update_time,
copy_label,primary_app_id)
SELECT 'dataset-szjcfx','师资决策分析',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'','teacherdecision'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset
    WHERE id = 'dataset-szjcfx'
);

-- dataset-szjcfx-teacherdecision-数据集和应用关联表
INSERT INTO t_da_dataset_app (id,sjjid,appid,
create_by,create_time,update_by,update_time)
SELECT 'dataset-szjcfx-teacherdecision','dataset-szjcfx','teacherdecision',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_app
    WHERE id = 'dataset-szjcfx-teacherdecision'
);

-- 学校信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'szjcfx_xxxx','dataset-szjcfx','学校信息',NULL,1,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'szjcfx_xxxx'
);

-- 师资队伍-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'szjcfx_szdw','dataset-szjcfx','师资队伍',NULL,2,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'szjcfx_szdw'
);

-- 教职工信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'szjcfx_jzgxx','dataset-szjcfx','教职工信息',NULL,3,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'szjcfx_jzgxx'
);

-- 基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'szjcfx_jzgxx_jbxx','dataset-szjcfx','基本信息','szjcfx_jzgxx',4,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'szjcfx_jzgxx_jbxx'
);

-- 人事信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'szjcfx_jzgxx_rsxx','dataset-szjcfx','人事信息','szjcfx_jzgxx',5,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'szjcfx_jzgxx_rsxx'
);

-- 教育教学-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'szjcfx_jzgxx_jyjx','dataset-szjcfx','教育教学','szjcfx_jzgxx',6,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'szjcfx_jzgxx_jyjx'
);

-- 科学研究-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'szjcfx_jzgxx_kxyj','dataset-szjcfx','科学研究','szjcfx_jzgxx',7,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'szjcfx_jzgxx_kxyj'
);

-- 师资决策分析_学校信息_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_xxxx%main-abdschxxjbxx','dataset-szjcfx','szjcfx_xxxx','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_xxxx%main-abdschxxjbxx'
);

-- 师资决策分析_学校信息_学年-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_xxxx%main-abdschxn','dataset-szjcfx','szjcfx_xxxx','main-abdschxn',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_xxxx%main-abdschxn'
);

-- 师资决策分析_学校信息_学年学期-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_xxxx%main-abdschxnxq','dataset-szjcfx','szjcfx_xxxx','main-abdschxnxq',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_xxxx%main-abdschxnxq'
);

-- 师资决策分析_学校信息_组织机构信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_xxxx%main-abdschzzjgxx','dataset-szjcfx','szjcfx_xxxx','main-abdschzzjgxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_xxxx%main-abdschzzjgxx'
);

-- 师资决策分析_师资队伍_每年在职教职工数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_szdw%main-teasisch0323','dataset-szjcfx','szjcfx_szdw','main-teasisch0323',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_szdw%main-teasisch0323'
);

-- 师资决策分析_师资队伍_每年在职专任教师数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_szdw%main-teasisch0354','dataset-szjcfx','szjcfx_szdw','main-teasisch0354',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_szdw%main-teasisch0354'
);

-- 师资决策分析_师资队伍_每年在职高层次人才数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_szdw%main-teasisch0355','dataset-szjcfx','szjcfx_szdw','main-teasisch0355',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_szdw%main-teasisch0355'
);

-- 师资决策分析_师资队伍_每年各第一学历毕业院校类型在职高层次人才数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_szdw%main-teasisch0356','dataset-szjcfx','szjcfx_szdw','main-teasisch0356',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_szdw%main-teasisch0356'
);

-- 师资决策分析_师资队伍_每年各博士毕业院校类型在职高层次人才数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_szdw%main-teasisch0357','dataset-szjcfx','szjcfx_szdw','main-teasisch0357',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_szdw%main-teasisch0357'
);

-- 师资决策分析_教职工信息_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_jbxx%main-abdteajzgjbxx','dataset-szjcfx','szjcfx_jzgxx_jbxx','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_jbxx%main-abdteajzgjbxx'
);

-- 师资决策分析_教职工信息_基本信息_教职工教育经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_jbxx%main-abdteajzgjyjl','dataset-szjcfx','szjcfx_jzgxx_jbxx','main-abdteajzgjyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_jbxx%main-abdteajzgjyjl'
);

-- 师资决策分析_教职工信息_基本信息_教职工工作经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_jbxx%main-abdteajzggzjl','dataset-szjcfx','szjcfx_jzgxx_jbxx','main-abdteajzggzjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_jbxx%main-abdteajzggzjl'
);

-- 师资决策分析_教职工信息_基本信息_学缘关系-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_jbxx%main-tealabel-xygx','dataset-szjcfx','szjcfx_jzgxx_jbxx','main-tealabel-xygx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_jbxx%main-tealabel-xygx'
);

-- 师资决策分析_教职工信息_基本信息_第一学历毕业院校类型-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_jbxx%main-tealabel-dyxlbyyxlx','dataset-szjcfx','szjcfx_jzgxx_jbxx','main-tealabel-dyxlbyyxlx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_jbxx%main-tealabel-dyxlbyyxlx'
);

-- 师资决策分析_教职工信息_基本信息_博士毕业院校类型-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_jbxx%main-tealabel-bsbyyxlx','dataset-szjcfx','szjcfx_jzgxx_jbxx','main-tealabel-bsbyyxlx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_jbxx%main-tealabel-bsbyyxlx'
);

-- 师资决策分析_教职工信息_基本信息_海外经历-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_jbxx%main-tealabel-hwjl','dataset-szjcfx','szjcfx_jzgxx_jbxx','main-tealabel-hwjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_jbxx%main-tealabel-hwjl'
);

-- 师资决策分析_教职工信息_基本信息_海外学习-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_jbxx%main-tealabel-hwxx','dataset-szjcfx','szjcfx_jzgxx_jbxx','main-tealabel-hwxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_jbxx%main-tealabel-hwxx'
);

-- 师资决策分析_教职工信息_基本信息_海外工作-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_jbxx%main-tealabel-hwgz','dataset-szjcfx','szjcfx_jzgxx_jbxx','main-tealabel-hwgz',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_jbxx%main-tealabel-hwgz'
);

-- 师资决策分析_教职工信息_人事信息_高层次人才信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_rsxx%main-abdteagccrcxx','dataset-szjcfx','szjcfx_jzgxx_rsxx','main-abdteagccrcxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_rsxx%main-abdteagccrcxx'
);

-- 师资决策分析_教职工信息_人事信息_海外研修-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_rsxx%main-abdteahwyx','dataset-szjcfx','szjcfx_jzgxx_rsxx','main-abdteahwyx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_rsxx%main-abdteahwyx'
);

-- 师资决策分析_教职工信息_人事信息_海外培训-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_rsxx%main-tealabel-hwpx','dataset-szjcfx','szjcfx_jzgxx_rsxx','main-tealabel-hwpx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_rsxx%main-tealabel-hwpx'
);

-- 师资决策分析_教职工信息_人事信息_学科领域-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_rsxx%main-tealabel-xkly','dataset-szjcfx','szjcfx_jzgxx_rsxx','main-tealabel-xkly',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_rsxx%main-tealabel-xkly'
);

-- 师资决策分析_教职工信息_人事信息_人才类别-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_rsxx%main-tealabel-rclb','dataset-szjcfx','szjcfx_jzgxx_rsxx','main-tealabel-rclb',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_rsxx%main-tealabel-rclb'
);

-- 师资决策分析_教职工信息_教育教学_教职工教学工作量-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_jyjx%main-abdteajzgjxgzl','dataset-szjcfx','szjcfx_jzgxx_jyjx','main-abdteajzgjxgzl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_jyjx%main-abdteajzgjxgzl'
);

-- 师资决策分析_教职工信息_教育教学_教职工授课记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_jyjx%main-abdteajzgskjl','dataset-szjcfx','szjcfx_jzgxx_jyjx','main-abdteajzgskjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_jyjx%main-abdteajzgskjl'
);

-- 师资决策分析_教职工信息_教育教学_教职工参与教改项目-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_jyjx%main-abdteajzgcyjgxm','dataset-szjcfx','szjcfx_jzgxx_jyjx','main-abdteajzgcyjgxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_jyjx%main-abdteajzgcyjgxm'
);

-- 师资决策分析_教职工信息_教育教学_教职工教学成果获奖信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_jyjx%main-abdteajzgjxcghjxx','dataset-szjcfx','szjcfx_jzgxx_jyjx','main-abdteajzgjxcghjxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_jyjx%main-abdteajzgjxcghjxx'
);

-- 师资决策分析_教职工信息_教育教学_教职工指导学生情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_jyjx%main-abdteajzgzdxsqk','dataset-szjcfx','szjcfx_jzgxx_jyjx','main-abdteajzgzdxsqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_jyjx%main-abdteajzgzdxsqk'
);

-- 师资决策分析_教职工信息_教育教学_教职工指导创新创业项目情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_jyjx%main-abdteajzgzdcxcyxmqk','dataset-szjcfx','szjcfx_jzgxx_jyjx','main-abdteajzgzdcxcyxmqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_jyjx%main-abdteajzgzdcxcyxmqk'
);

-- 师资决策分析_教职工信息_教育教学_教职工指导毕业论文（设计）情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_jyjx%main-abdteajzgzdbylwsjqk','dataset-szjcfx','szjcfx_jzgxx_jyjx','main-abdteajzgzdbylwsjqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_jyjx%main-abdteajzgzdbylwsjqk'
);

-- 师资决策分析_教职工信息_教育教学_教职工指导学生实习实践情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_jyjx%main-abdteajzgzdxssxsjqk','dataset-szjcfx','szjcfx_jzgxx_jyjx','main-abdteajzgzdxssxsjqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_jyjx%main-abdteajzgzdxssxsjqk'
);

-- 师资决策分析_教职工信息_教育教学_每学期每位教师教学工作量-TEA_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_jyjx%main-teasitea0050','dataset-szjcfx','szjcfx_jzgxx_jyjx','main-teasitea0050',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_jyjx%main-teasitea0050'
);

-- 师资决策分析_教职工信息_教育教学_每学期每位教师授课门数及学时-TEA_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_jyjx%main-teasitea0051','dataset-szjcfx','szjcfx_jzgxx_jyjx','main-teasitea0051',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_jyjx%main-teasitea0051'
);

-- 师资决策分析_教职工信息_教育教学_每学期每位教师指导学生人数-TEA_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_jyjx%main-teasitea0052','dataset-szjcfx','szjcfx_jzgxx_jyjx','main-teasitea0052',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_jyjx%main-teasitea0052'
);

-- 师资决策分析_教职工信息_教育教学_每年每位教师指导双创竞赛获奖数量-TEA_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_jyjx%main-teasitea0053','dataset-szjcfx','szjcfx_jzgxx_jyjx','main-teasitea0053',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_jyjx%main-teasitea0053'
);

-- 师资决策分析_教职工信息_教育教学_每年每位教师指导学生实习人数-TEA_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_jyjx%main-teasitea0054','dataset-szjcfx','szjcfx_jzgxx_jyjx','main-teasitea0054',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_jyjx%main-teasitea0054'
);

-- 师资决策分析_教职工信息_教育教学_每年每位教师指导毕业论文（设计）数量-TEA_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_jyjx%main-teasitea0055','dataset-szjcfx','szjcfx_jzgxx_jyjx','main-teasitea0055',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_jyjx%main-teasitea0055'
);

-- 师资决策分析_教职工信息_教育教学_每年每位教师教改项目数量-TEA_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_jyjx%main-teasitea0056','dataset-szjcfx','szjcfx_jzgxx_jyjx','main-teasitea0056',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_jyjx%main-teasitea0056'
);

-- 师资决策分析_教职工信息_教育教学_每年每位教师获得的教学成果奖数量-TEA_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_jyjx%main-teasitea0057','dataset-szjcfx','szjcfx_jzgxx_jyjx','main-teasitea0057',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_jyjx%main-teasitea0057'
);

-- 师资决策分析_教职工信息_科学研究_教职工科研工作量-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-abdteajzgkygzl','dataset-szjcfx','szjcfx_jzgxx_kxyj','main-abdteajzgkygzl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-abdteajzgkygzl'
);

-- 师资决策分析_教职工信息_科学研究_教职工参与科研项目-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-abdteajzgcykyxm','dataset-szjcfx','szjcfx_jzgxx_kxyj','main-abdteajzgcykyxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-abdteajzgcykyxm'
);

-- 师资决策分析_教职工信息_科学研究_教职工发表论文-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-abdteajzgfblw','dataset-szjcfx','szjcfx_jzgxx_kxyj','main-abdteajzgfblw',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-abdteajzgfblw'
);

-- 师资决策分析_教职工信息_科学研究_教职工发明专利-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-abdteajzgfmzl','dataset-szjcfx','szjcfx_jzgxx_kxyj','main-abdteajzgfmzl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-abdteajzgfmzl'
);

-- 师资决策分析_教职工信息_科学研究_教职工出版著作-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-abdteajzgcbzz','dataset-szjcfx','szjcfx_jzgxx_kxyj','main-abdteajzgcbzz',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-abdteajzgcbzz'
);

-- 师资决策分析_教职工信息_科学研究_教职工资政报告采纳记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-abdteajzgzzbgcnjl','dataset-szjcfx','szjcfx_jzgxx_kxyj','main-abdteajzgzzbgcnjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-abdteajzgzzbgcnjl'
);

-- 师资决策分析_教职工信息_科学研究_教职工科研获奖-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-abdteajzgkyhj','dataset-szjcfx','szjcfx_jzgxx_kxyj','main-abdteajzgkyhj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-abdteajzgkyhj'
);

-- 师资决策分析_教职工信息_科学研究_教职工负责项目经费记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-abdteajzgfzxmjfjl','dataset-szjcfx','szjcfx_jzgxx_kxyj','main-abdteajzgfzxmjfjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-abdteajzgfzxmjfjl'
);

-- 师资决策分析_教职工信息_科学研究_每年每位教师科研工作量-TEA_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-teasitea0049','dataset-szjcfx','szjcfx_jzgxx_kxyj','main-teasitea0049',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-teasitea0049'
);

-- 师资决策分析_教职工信息_科学研究_每年每位教师科研项目数-TEA_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-teasitea0047','dataset-szjcfx','szjcfx_jzgxx_kxyj','main-teasitea0047',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-teasitea0047'
);

-- 师资决策分析_教职工信息_科学研究_每年每位教师科研项目经费-TEA_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-teasitea0048','dataset-szjcfx','szjcfx_jzgxx_kxyj','main-teasitea0048',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-teasitea0048'
);

-- 师资决策分析_教职工信息_科学研究_每年教师发表论文数量-TEA_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-teasitea0040','dataset-szjcfx','szjcfx_jzgxx_kxyj','main-teasitea0040',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-teasitea0040'
);

-- 师资决策分析_教职工信息_科学研究_每年每位教师发表的各收录类别论文数量-TEA_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-teasitea0041','dataset-szjcfx','szjcfx_jzgxx_kxyj','main-teasitea0041',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-teasitea0041'
);

-- 师资决策分析_教职工信息_科学研究_每年每位教师发表的各SCI分区论文数量-TEA_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-teasitea0042','dataset-szjcfx','szjcfx_jzgxx_kxyj','main-teasitea0042',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-teasitea0042'
);

-- 师资决策分析_教职工信息_科学研究_每年每位教师出版著作数量-TEA_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-teasitea0043','dataset-szjcfx','szjcfx_jzgxx_kxyj','main-teasitea0043',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-teasitea0043'
);

-- 师资决策分析_教职工信息_科学研究_每年每位教师发明专利数量-TEA_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-teasitea0044','dataset-szjcfx','szjcfx_jzgxx_kxyj','main-teasitea0044',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-teasitea0044'
);

-- 师资决策分析_教职工信息_科学研究_每年每位教师被采纳的资政报告数量-TEA_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-teasitea0045','dataset-szjcfx','szjcfx_jzgxx_kxyj','main-teasitea0045',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-teasitea0045'
);

-- 师资决策分析_教职工信息_科学研究_每年每位教师获得的科研奖励数-TEA_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-teasitea0046','dataset-szjcfx','szjcfx_jzgxx_kxyj','main-teasitea0046',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-szjcfx%szjcfx_jzgxx_kxyj%main-teasitea0046'
);

-- 师资决策分析_学校信息_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschxxjbxx-xxbsm','dataset-szjcfx','main-abdschxxjbxx-xxbsm','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschxxjbxx-xxbsm'
);

-- 师资决策分析_学校信息_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschxxjbxx-xxmc','dataset-szjcfx','main-abdschxxjbxx-xxmc','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschxxjbxx-xxmc'
);

-- 师资决策分析_学校信息_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschxxjbxx-xxywmc','dataset-szjcfx','main-abdschxxjbxx-xxywmc','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschxxjbxx-xxywmc'
);

-- 师资决策分析_学校信息_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschxxjbxx-xxdz','dataset-szjcfx','main-abdschxxjbxx-xxdz','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschxxjbxx-xxdz'
);

-- 师资决策分析_学校信息_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschxxjbxx-xxyzbm','dataset-szjcfx','main-abdschxxjbxx-xxyzbm','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschxxjbxx-xxyzbm'
);

-- 师资决策分析_学校信息_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschxxjbxx-xzqh','dataset-szjcfx','main-abdschxxjbxx-xzqh','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschxxjbxx-xzqh'
);

-- 师资决策分析_学校信息_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschxxjbxx-szdcxlx','dataset-szjcfx','main-abdschxxjbxx-szdcxlx','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschxxjbxx-szdcxlx'
);

-- 师资决策分析_学校信息_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschxxjbxx-jxny','dataset-szjcfx','main-abdschxxjbxx-jxny','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschxxjbxx-jxny'
);

-- 师资决策分析_学校信息_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschxxjbxx-xqr','dataset-szjcfx','main-abdschxxjbxx-xqr','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschxxjbxx-xqr'
);

-- 师资决策分析_学校信息_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschxxjbxx-xxbxlx','dataset-szjcfx','main-abdschxxjbxx-xxbxlx','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschxxjbxx-xxbxlx'
);

-- 师资决策分析_学校信息_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschxxjbxx-xxjbz','dataset-szjcfx','main-abdschxxjbxx-xxjbz','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschxxjbxx-xxjbz'
);

-- 师资决策分析_学校信息_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschxxjbxx-xxzgbm','dataset-szjcfx','main-abdschxxjbxx-xxzgbm','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschxxjbxx-xxzgbm'
);

-- 师资决策分析_学校信息_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschxxjbxx-fddbrh','dataset-szjcfx','main-abdschxxjbxx-fddbrh','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschxxjbxx-fddbrh'
);

-- 师资决策分析_学校信息_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschxxjbxx-frzsh','dataset-szjcfx','main-abdschxxjbxx-frzsh','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschxxjbxx-frzsh'
);

-- 师资决策分析_学校信息_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschxxjbxx-xzxm','dataset-szjcfx','main-abdschxxjbxx-xzxm','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschxxjbxx-xzxm'
);

-- 师资决策分析_学校信息_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschxxjbxx-dwfzr','dataset-szjcfx','main-abdschxxjbxx-dwfzr','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschxxjbxx-dwfzr'
);

-- 师资决策分析_学校信息_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschxxjbxx-zzjgm','dataset-szjcfx','main-abdschxxjbxx-zzjgm','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschxxjbxx-zzjgm'
);

-- 师资决策分析_学校信息_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschxxjbxx-lxdh','dataset-szjcfx','main-abdschxxjbxx-lxdh','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschxxjbxx-lxdh'
);

-- 师资决策分析_学校信息_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschxxjbxx-czdh','dataset-szjcfx','main-abdschxxjbxx-czdh','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschxxjbxx-czdh'
);

-- 师资决策分析_学校信息_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschxxjbxx-dzxx','dataset-szjcfx','main-abdschxxjbxx-dzxx','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschxxjbxx-dzxx'
);

-- 师资决策分析_学校信息_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschxxjbxx-xxbb','dataset-szjcfx','main-abdschxxjbxx-xxbb','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschxxjbxx-xxbb'
);

-- 师资决策分析_学校信息_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschxxjbxx-xxxz','dataset-szjcfx','main-abdschxxjbxx-xxxz','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschxxjbxx-xxxz'
);

-- 师资决策分析_学校信息_学年-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschxn-xxbsm','dataset-szjcfx','main-abdschxn-xxbsm','main-abdschxn',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschxn-xxbsm'
);

-- 师资决策分析_学校信息_学年-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschxn-xnbm','dataset-szjcfx','main-abdschxn-xnbm','main-abdschxn',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschxn-xnbm'
);

-- 师资决策分析_学校信息_学年-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschxn-xnmc','dataset-szjcfx','main-abdschxn-xnmc','main-abdschxn',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschxn-xnmc'
);

-- 师资决策分析_学校信息_学年-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschxn-xnksrq','dataset-szjcfx','main-abdschxn-xnksrq','main-abdschxn',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschxn-xnksrq'
);

-- 师资决策分析_学校信息_学年学期-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschxnxq-xxbsm','dataset-szjcfx','main-abdschxnxq-xxbsm','main-abdschxnxq',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschxnxq-xxbsm'
);

-- 师资决策分析_学校信息_学年学期-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschxnxq-xnxqbm','dataset-szjcfx','main-abdschxnxq-xnxqbm','main-abdschxnxq',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschxnxq-xnxqbm'
);

-- 师资决策分析_学校信息_学年学期-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschxnxq-xnxqmc','dataset-szjcfx','main-abdschxnxq-xnxqmc','main-abdschxnxq',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschxnxq-xnxqmc'
);

-- 师资决策分析_学校信息_学年学期-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschxnxq-xqksrq','dataset-szjcfx','main-abdschxnxq-xqksrq','main-abdschxnxq',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschxnxq-xqksrq'
);

-- 师资决策分析_学校信息_组织机构信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschzzjgxx-xxbsm','dataset-szjcfx','main-abdschzzjgxx-xxbsm','main-abdschzzjgxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschzzjgxx-xxbsm'
);

-- 师资决策分析_学校信息_组织机构信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschzzjgxx-jgdm','dataset-szjcfx','main-abdschzzjgxx-jgdm','main-abdschzzjgxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschzzjgxx-jgdm'
);

-- 师资决策分析_学校信息_组织机构信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschzzjgxx-jgmc','dataset-szjcfx','main-abdschzzjgxx-jgmc','main-abdschzzjgxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschzzjgxx-jgmc'
);

-- 师资决策分析_学校信息_组织机构信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschzzjgxx-jgjc','dataset-szjcfx','main-abdschzzjgxx-jgjc','main-abdschzzjgxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschzzjgxx-jgjc'
);

-- 师资决策分析_学校信息_组织机构信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschzzjgxx-lsjgdm','dataset-szjcfx','main-abdschzzjgxx-lsjgdm','main-abdschzzjgxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschzzjgxx-lsjgdm'
);

-- 师资决策分析_学校信息_组织机构信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschzzjgxx-lsjg','dataset-szjcfx','main-abdschzzjgxx-lsjg','main-abdschzzjgxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschzzjgxx-lsjg'
);

-- 师资决策分析_学校信息_组织机构信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschzzjgxx-jglb','dataset-szjcfx','main-abdschzzjgxx-jglb','main-abdschzzjgxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschzzjgxx-jglb'
);

-- 师资决策分析_学校信息_组织机构信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschzzjgxx-jlny','dataset-szjcfx','main-abdschzzjgxx-jlny','main-abdschzzjgxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschzzjgxx-jlny'
);

-- 师资决策分析_学校信息_组织机构信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdschzzjgxx-jgpx','dataset-szjcfx','main-abdschzzjgxx-jgpx','main-abdschzzjgxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdschzzjgxx-jgpx'
);

-- 师资决策分析_教职工信息_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjbxx-zgh','dataset-szjcfx','main-abdteajzgjbxx-zgh','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjbxx-zgh'
);

-- 师资决策分析_教职工信息_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjbxx-xm','dataset-szjcfx','main-abdteajzgjbxx-xm','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjbxx-xm'
);

-- 师资决策分析_教职工信息_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjbxx-xb','dataset-szjcfx','main-abdteajzgjbxx-xb','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjbxx-xb'
);

-- 师资决策分析_教职工信息_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjbxx-csrq','dataset-szjcfx','main-abdteajzgjbxx-csrq','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjbxx-csrq'
);

-- 师资决策分析_教职工信息_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjbxx-gjdq','dataset-szjcfx','main-abdteajzgjbxx-gjdq','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjbxx-gjdq'
);

-- 师资决策分析_教职工信息_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjbxx-zgxl','dataset-szjcfx','main-abdteajzgjbxx-zgxl','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjbxx-zgxl'
);

-- 师资决策分析_教职工信息_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjbxx-zgxw','dataset-szjcfx','main-abdteajzgjbxx-zgxw','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjbxx-zgxw'
);

-- 师资决策分析_教职工信息_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjbxx-zhbyyx','dataset-szjcfx','main-abdteajzgjbxx-zhbyyx','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjbxx-zhbyyx'
);

-- 师资决策分析_教职工信息_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjbxx-sfbxby','dataset-szjcfx','main-abdteajzgjbxx-sfbxby','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjbxx-sfbxby'
);

-- 师资决策分析_教职工信息_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjbxx-ssjgdm','dataset-szjcfx','main-abdteajzgjbxx-ssjgdm','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjbxx-ssjgdm'
);

-- 师资决策分析_教职工信息_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjbxx-ssjg','dataset-szjcfx','main-abdteajzgjbxx-ssjg','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjbxx-ssjg'
);

-- 师资决策分析_教职工信息_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjbxx-ssxdm','dataset-szjcfx','main-abdteajzgjbxx-ssxdm','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjbxx-ssxdm'
);

-- 师资决策分析_教职工信息_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjbxx-ssx','dataset-szjcfx','main-abdteajzgjbxx-ssx','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjbxx-ssx'
);

-- 师资决策分析_教职工信息_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjbxx-jzglb','dataset-szjcfx','main-abdteajzgjbxx-jzglb','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjbxx-jzglb'
);

-- 师资决策分析_教职工信息_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjbxx-jzgdqzt','dataset-szjcfx','main-abdteajzgjbxx-jzgdqzt','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjbxx-jzgdqzt'
);

-- 师资决策分析_教职工信息_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjbxx-yjtxrq','dataset-szjcfx','main-abdteajzgjbxx-yjtxrq','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjbxx-yjtxrq'
);

-- 师资决策分析_教职工信息_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjbxx-zyjszw','dataset-szjcfx','main-abdteajzgjbxx-zyjszw','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjbxx-zyjszw'
);

-- 师资决策分析_教职工信息_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjbxx-zyjszwjb','dataset-szjcfx','main-abdteajzgjbxx-zyjszwjb','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjbxx-zyjszwjb'
);

-- 师资决策分析_教职工信息_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjbxx-nl','dataset-szjcfx','main-abdteajzgjbxx-nl','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjbxx-nl'
);

-- 师资决策分析_教职工信息_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjbxx-zgxwcc','dataset-szjcfx','main-abdteajzgjbxx-zgxwcc','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjbxx-zgxwcc'
);

-- 师资决策分析_教职工信息_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjbxx-zyjszwdm','dataset-szjcfx','main-abdteajzgjbxx-zyjszwdm','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjbxx-zyjszwdm'
);

-- 师资决策分析_教职工信息_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjbxx-zyjszwjbdm','dataset-szjcfx','main-abdteajzgjbxx-zyjszwjbdm','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjbxx-zyjszwjbdm'
);

-- 师资决策分析_教职工信息_基本信息_教职工教育经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjyjl-zgh','dataset-szjcfx','main-abdteajzgjyjl-zgh','main-abdteajzgjyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjyjl-zgh'
);

-- 师资决策分析_教职工信息_基本信息_教职工教育经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjyjl-rxny','dataset-szjcfx','main-abdteajzgjyjl-rxny','main-abdteajzgjyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjyjl-rxny'
);

-- 师资决策分析_教职工信息_基本信息_教职工教育经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjyjl-byny','dataset-szjcfx','main-abdteajzgjyjl-byny','main-abdteajzgjyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjyjl-byny'
);

-- 师资决策分析_教职工信息_基本信息_教职工教育经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjyjl-byyxxhdw','dataset-szjcfx','main-abdteajzgjyjl-byyxxhdw','main-abdteajzgjyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjyjl-byyxxhdw'
);

-- 师资决策分析_教职工信息_基本信息_教职工教育经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjyjl-xl','dataset-szjcfx','main-abdteajzgjyjl-xl','main-abdteajzgjyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjyjl-xl'
);

-- 师资决策分析_教职工信息_基本信息_教职工教育经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjyjl-xw','dataset-szjcfx','main-abdteajzgjyjl-xw','main-abdteajzgjyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjyjl-xw'
);

-- 师资决策分析_教职工信息_基本信息_教职工教育经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjyjl-sxzy','dataset-szjcfx','main-abdteajzgjyjl-sxzy','main-abdteajzgjyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjyjl-sxzy'
);

-- 师资决策分析_教职工信息_基本信息_教职工教育经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjyjl-xwsygj','dataset-szjcfx','main-abdteajzgjyjl-xwsygj','main-abdteajzgjyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjyjl-xwsygj'
);

-- 师资决策分析_教职工信息_基本信息_教职工教育经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjyjl-gxlb','dataset-szjcfx','main-abdteajzgjyjl-gxlb','main-abdteajzgjyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjyjl-gxlb'
);

-- 师资决策分析_教职工信息_基本信息_教职工工作经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzggzjl-zgh','dataset-szjcfx','main-abdteajzggzjl-zgh','main-abdteajzggzjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzggzjl-zgh'
);

-- 师资决策分析_教职工信息_基本信息_教职工工作经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzggzjl-szdwmc','dataset-szjcfx','main-abdteajzggzjl-szdwmc','main-abdteajzggzjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzggzjl-szdwmc'
);

-- 师资决策分析_教职工信息_基本信息_教职工工作经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzggzjl-gjdq','dataset-szjcfx','main-abdteajzggzjl-gjdq','main-abdteajzggzjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzggzjl-gjdq'
);

-- 师资决策分析_教职工信息_基本信息_教职工工作经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzggzjl-sfhwjl','dataset-szjcfx','main-abdteajzggzjl-sfhwjl','main-abdteajzggzjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzggzjl-sfhwjl'
);

-- 师资决策分析_教职工信息_基本信息_教职工工作经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzggzjl-qsny','dataset-szjcfx','main-abdteajzggzjl-qsny','main-abdteajzggzjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzggzjl-qsny'
);

-- 师资决策分析_教职工信息_基本信息_教职工工作经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzggzjl-jzny','dataset-szjcfx','main-abdteajzggzjl-jzny','main-abdteajzggzjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzggzjl-jzny'
);

-- 师资决策分析_教职工信息_人事信息_高层次人才信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteagccrcxx-zgh','dataset-szjcfx','main-abdteagccrcxx-zgh','main-abdteagccrcxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteagccrcxx-zgh'
);

-- 师资决策分析_教职工信息_人事信息_高层次人才信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteagccrcxx-rclb','dataset-szjcfx','main-abdteagccrcxx-rclb','main-abdteagccrcxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteagccrcxx-rclb'
);

-- 师资决策分析_教职工信息_人事信息_高层次人才信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteagccrcxx-pzny','dataset-szjcfx','main-abdteagccrcxx-pzny','main-abdteagccrcxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteagccrcxx-pzny'
);

-- 师资决策分析_教职工信息_人事信息_海外研修-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteahwyx-zgh','dataset-szjcfx','main-abdteahwyx-zgh','main-abdteahwyx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteahwyx-zgh'
);

-- 师资决策分析_教职工信息_人事信息_海外研修-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteahwyx-ksrq','dataset-szjcfx','main-abdteahwyx-ksrq','main-abdteahwyx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteahwyx-ksrq'
);

-- 师资决策分析_教职工信息_人事信息_海外研修-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteahwyx-jsrq','dataset-szjcfx','main-abdteahwyx-jsrq','main-abdteahwyx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteahwyx-jsrq'
);

-- 师资决策分析_教职工信息_人事信息_海外研修-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteahwyx-gjdq','dataset-szjcfx','main-abdteahwyx-gjdq','main-abdteahwyx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteahwyx-gjdq'
);

-- 师资决策分析_教职工信息_人事信息_海外研修-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteahwyx-yxfxjg','dataset-szjcfx','main-abdteahwyx-yxfxjg','main-abdteahwyx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteahwyx-yxfxjg'
);

-- 师资决策分析_教职工信息_人事信息_海外研修-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteahwyx-xmmc','dataset-szjcfx','main-abdteahwyx-xmmc','main-abdteahwyx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteahwyx-xmmc'
);

-- 师资决策分析_教职工信息_教育教学_教职工教学工作量-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjxgzl-zgh','dataset-szjcfx','main-abdteajzgjxgzl-zgh','main-abdteajzgjxgzl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjxgzl-zgh'
);

-- 师资决策分析_教职工信息_教育教学_教职工教学工作量-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjxgzl-xnxq','dataset-szjcfx','main-abdteajzgjxgzl-xnxq','main-abdteajzgjxgzl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjxgzl-xnxq'
);

-- 师资决策分析_教职工信息_教育教学_教职工教学工作量-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjxgzl-gzl','dataset-szjcfx','main-abdteajzgjxgzl-gzl','main-abdteajzgjxgzl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjxgzl-gzl'
);

-- 师资决策分析_教职工信息_教育教学_教职工授课记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgskjl-zgh','dataset-szjcfx','main-abdteajzgskjl-zgh','main-abdteajzgskjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgskjl-zgh'
);

-- 师资决策分析_教职工信息_教育教学_教职工授课记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgskjl-kcmc','dataset-szjcfx','main-abdteajzgskjl-kcmc','main-abdteajzgskjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgskjl-kcmc'
);

-- 师资决策分析_教职工信息_教育教学_教职工授课记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgskjl-kclx','dataset-szjcfx','main-abdteajzgskjl-kclx','main-abdteajzgskjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgskjl-kclx'
);

-- 师资决策分析_教职工信息_教育教学_教职工授课记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgskjl-kclb','dataset-szjcfx','main-abdteajzgskjl-kclb','main-abdteajzgskjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgskjl-kclb'
);

-- 师资决策分析_教职工信息_教育教学_教职工授课记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgskjl-kcsx','dataset-szjcfx','main-abdteajzgskjl-kcsx','main-abdteajzgskjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgskjl-kcsx'
);

-- 师资决策分析_教职工信息_教育教学_教职工授课记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgskjl-xnxq','dataset-szjcfx','main-abdteajzgskjl-xnxq','main-abdteajzgskjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgskjl-xnxq'
);

-- 师资决策分析_教职工信息_教育教学_教职工授课记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgskjl-skxs','dataset-szjcfx','main-abdteajzgskjl-skxs','main-abdteajzgskjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgskjl-skxs'
);

-- 师资决策分析_教职工信息_教育教学_教职工参与教改项目-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgcyjgxm-zgh','dataset-szjcfx','main-abdteajzgcyjgxm-zgh','main-abdteajzgcyjgxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgcyjgxm-zgh'
);

-- 师资决策分析_教职工信息_教育教学_教职工参与教改项目-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgcyjgxm-xmbh','dataset-szjcfx','main-abdteajzgcyjgxm-xmbh','main-abdteajzgcyjgxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgcyjgxm-xmbh'
);

-- 师资决策分析_教职工信息_教育教学_教职工参与教改项目-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgcyjgxm-xmmc','dataset-szjcfx','main-abdteajzgcyjgxm-xmmc','main-abdteajzgcyjgxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgcyjgxm-xmmc'
);

-- 师资决策分析_教职工信息_教育教学_教职工参与教改项目-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgcyjgxm-xmjb','dataset-szjcfx','main-abdteajzgcyjgxm-xmjb','main-abdteajzgcyjgxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgcyjgxm-xmjb'
);

-- 师资决策分析_教职工信息_教育教学_教职工参与教改项目-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgcyjgxm-lxrq','dataset-szjcfx','main-abdteajzgcyjgxm-lxrq','main-abdteajzgcyjgxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgcyjgxm-lxrq'
);

-- 师资决策分析_教职工信息_教育教学_教职工参与教改项目-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgcyjgxm-jxrq','dataset-szjcfx','main-abdteajzgcyjgxm-jxrq','main-abdteajzgcyjgxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgcyjgxm-jxrq'
);

-- 师资决策分析_教职工信息_教育教学_教职工参与教改项目-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgcyjgxm-xmjf','dataset-szjcfx','main-abdteajzgcyjgxm-xmjf','main-abdteajzgcyjgxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgcyjgxm-xmjf'
);

-- 师资决策分析_教职工信息_教育教学_教职工参与教改项目-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgcyjgxm-xmzt','dataset-szjcfx','main-abdteajzgcyjgxm-xmzt','main-abdteajzgcyjgxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgcyjgxm-xmzt'
);

-- 师资决策分析_教职工信息_教育教学_教职工参与教改项目-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgcyjgxm-brcyqk','dataset-szjcfx','main-abdteajzgcyjgxm-brcyqk','main-abdteajzgcyjgxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgcyjgxm-brcyqk'
);

-- 师资决策分析_教职工信息_教育教学_教职工参与教改项目-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgcyjgxm-xmjbdm','dataset-szjcfx','main-abdteajzgcyjgxm-xmjbdm','main-abdteajzgcyjgxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgcyjgxm-xmjbdm'
);

-- 师资决策分析_教职工信息_教育教学_教职工教学成果获奖信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjxcghjxx-zgh','dataset-szjcfx','main-abdteajzgjxcghjxx-zgh','main-abdteajzgjxcghjxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjxcghjxx-zgh'
);

-- 师资决策分析_教职工信息_教育教学_教职工教学成果获奖信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjxcghjxx-jxcgmc','dataset-szjcfx','main-abdteajzgjxcghjxx-jxcgmc','main-abdteajzgjxcghjxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjxcghjxx-jxcgmc'
);

-- 师资决策分析_教职工信息_教育教学_教职工教学成果获奖信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjxcghjxx-brwc','dataset-szjcfx','main-abdteajzgjxcghjxx-brwc','main-abdteajzgjxcghjxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjxcghjxx-brwc'
);

-- 师资决策分析_教职工信息_教育教学_教职工教学成果获奖信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjxcghjxx-jxmc','dataset-szjcfx','main-abdteajzgjxcghjxx-jxmc','main-abdteajzgjxcghjxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjxcghjxx-jxmc'
);

-- 师资决策分析_教职工信息_教育教学_教职工教学成果获奖信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjxcghjxx-jljb','dataset-szjcfx','main-abdteajzgjxcghjxx-jljb','main-abdteajzgjxcghjxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjxcghjxx-jljb'
);

-- 师资决策分析_教职工信息_教育教学_教职工教学成果获奖信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjxcghjxx-jldj','dataset-szjcfx','main-abdteajzgjxcghjxx-jldj','main-abdteajzgjxcghjxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjxcghjxx-jldj'
);

-- 师资决策分析_教职工信息_教育教学_教职工教学成果获奖信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjxcghjxx-hjrq','dataset-szjcfx','main-abdteajzgjxcghjxx-hjrq','main-abdteajzgjxcghjxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjxcghjxx-hjrq'
);

-- 师资决策分析_教职工信息_教育教学_教职工教学成果获奖信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgjxcghjxx-jljbdm','dataset-szjcfx','main-abdteajzgjxcghjxx-jljbdm','main-abdteajzgjxcghjxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgjxcghjxx-jljbdm'
);

-- 师资决策分析_教职工信息_教育教学_教职工指导学生情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzdxsqk-zgh','dataset-szjcfx','main-abdteajzgzdxsqk-zgh','main-abdteajzgzdxsqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzdxsqk-zgh'
);

-- 师资决策分析_教职工信息_教育教学_教职工指导学生情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzdxsqk-xnxq','dataset-szjcfx','main-abdteajzgzdxsqk-xnxq','main-abdteajzgzdxsqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzdxsqk-xnxq'
);

-- 师资决策分析_教职工信息_教育教学_教职工指导学生情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzdxsqk-pycc','dataset-szjcfx','main-abdteajzgzdxsqk-pycc','main-abdteajzgzdxsqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzdxsqk-pycc'
);

-- 师资决策分析_教职工信息_教育教学_教职工指导学生情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzdxsqk-zdxsrs','dataset-szjcfx','main-abdteajzgzdxsqk-zdxsrs','main-abdteajzgzdxsqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzdxsqk-zdxsrs'
);

-- 师资决策分析_教职工信息_教育教学_教职工指导创新创业项目情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzdcxcyxmqk-zgh','dataset-szjcfx','main-abdteajzgzdcxcyxmqk-zgh','main-abdteajzgzdcxcyxmqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzdcxcyxmqk-zgh'
);

-- 师资决策分析_教职工信息_教育教学_教职工指导创新创业项目情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzdcxcyxmqk-xh','dataset-szjcfx','main-abdteajzgzdcxcyxmqk-xh','main-abdteajzgzdcxcyxmqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzdcxcyxmqk-xh'
);

-- 师资决策分析_教职工信息_教育教学_教职工指导创新创业项目情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzdcxcyxmqk-xsxm','dataset-szjcfx','main-abdteajzgzdcxcyxmqk-xsxm','main-abdteajzgzdcxcyxmqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzdcxcyxmqk-xsxm'
);

-- 师资决策分析_教职工信息_教育教学_教职工指导创新创业项目情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzdcxcyxmqk-pycc','dataset-szjcfx','main-abdteajzgzdcxcyxmqk-pycc','main-abdteajzgzdcxcyxmqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzdcxcyxmqk-pycc'
);

-- 师资决策分析_教职工信息_教育教学_教职工指导创新创业项目情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzdcxcyxmqk-xmmc','dataset-szjcfx','main-abdteajzgzdcxcyxmqk-xmmc','main-abdteajzgzdcxcyxmqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzdcxcyxmqk-xmmc'
);

-- 师资决策分析_教职工信息_教育教学_教职工指导创新创业项目情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzdcxcyxmqk-xmlx','dataset-szjcfx','main-abdteajzgzdcxcyxmqk-xmlx','main-abdteajzgzdcxcyxmqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzdcxcyxmqk-xmlx'
);

-- 师资决策分析_教职工信息_教育教学_教职工指导创新创业项目情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzdcxcyxmqk-xmjb','dataset-szjcfx','main-abdteajzgzdcxcyxmqk-xmjb','main-abdteajzgzdcxcyxmqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzdcxcyxmqk-xmjb'
);

-- 师资决策分析_教职工信息_教育教学_教职工指导创新创业项目情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzdcxcyxmqk-hjdj','dataset-szjcfx','main-abdteajzgzdcxcyxmqk-hjdj','main-abdteajzgzdcxcyxmqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzdcxcyxmqk-hjdj'
);

-- 师资决策分析_教职工信息_教育教学_教职工指导创新创业项目情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzdcxcyxmqk-xn','dataset-szjcfx','main-abdteajzgzdcxcyxmqk-xn','main-abdteajzgzdcxcyxmqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzdcxcyxmqk-xn'
);

-- 师资决策分析_教职工信息_教育教学_教职工指导毕业论文（设计）情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzdbylwsjqk-zgh','dataset-szjcfx','main-abdteajzgzdbylwsjqk-zgh','main-abdteajzgzdbylwsjqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzdbylwsjqk-zgh'
);

-- 师资决策分析_教职工信息_教育教学_教职工指导毕业论文（设计）情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzdbylwsjqk-xh','dataset-szjcfx','main-abdteajzgzdbylwsjqk-xh','main-abdteajzgzdbylwsjqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzdbylwsjqk-xh'
);

-- 师资决策分析_教职工信息_教育教学_教职工指导毕业论文（设计）情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzdbylwsjqk-xsxm','dataset-szjcfx','main-abdteajzgzdbylwsjqk-xsxm','main-abdteajzgzdbylwsjqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzdbylwsjqk-xsxm'
);

-- 师资决策分析_教职工信息_教育教学_教职工指导毕业论文（设计）情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzdbylwsjqk-pycc','dataset-szjcfx','main-abdteajzgzdbylwsjqk-pycc','main-abdteajzgzdbylwsjqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzdbylwsjqk-pycc'
);

-- 师资决策分析_教职工信息_教育教学_教职工指导毕业论文（设计）情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzdbylwsjqk-lwmc','dataset-szjcfx','main-abdteajzgzdbylwsjqk-lwmc','main-abdteajzgzdbylwsjqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzdbylwsjqk-lwmc'
);

-- 师资决策分析_教职工信息_教育教学_教职工指导毕业论文（设计）情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzdbylwsjqk-sftg','dataset-szjcfx','main-abdteajzgzdbylwsjqk-sftg','main-abdteajzgzdbylwsjqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzdbylwsjqk-sftg'
);

-- 师资决策分析_教职工信息_教育教学_教职工指导毕业论文（设计）情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzdbylwsjqk-nf','dataset-szjcfx','main-abdteajzgzdbylwsjqk-nf','main-abdteajzgzdbylwsjqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzdbylwsjqk-nf'
);

-- 师资决策分析_教职工信息_教育教学_教职工指导学生实习实践情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzdxssxsjqk-zgh','dataset-szjcfx','main-abdteajzgzdxssxsjqk-zgh','main-abdteajzgzdxssxsjqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzdxssxsjqk-zgh'
);

-- 师资决策分析_教职工信息_教育教学_教职工指导学生实习实践情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzdxssxsjqk-xh','dataset-szjcfx','main-abdteajzgzdxssxsjqk-xh','main-abdteajzgzdxssxsjqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzdxssxsjqk-xh'
);

-- 师资决策分析_教职工信息_教育教学_教职工指导学生实习实践情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzdxssxsjqk-xsxm','dataset-szjcfx','main-abdteajzgzdxssxsjqk-xsxm','main-abdteajzgzdxssxsjqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzdxssxsjqk-xsxm'
);

-- 师资决策分析_教职工信息_教育教学_教职工指导学生实习实践情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzdxssxsjqk-pycc','dataset-szjcfx','main-abdteajzgzdxssxsjqk-pycc','main-abdteajzgzdxssxsjqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzdxssxsjqk-pycc'
);

-- 师资决策分析_教职工信息_教育教学_教职工指导学生实习实践情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzdxssxsjqk-qyjd','dataset-szjcfx','main-abdteajzgzdxssxsjqk-qyjd','main-abdteajzgzdxssxsjqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzdxssxsjqk-qyjd'
);

-- 师资决策分析_教职工信息_教育教学_教职工指导学生实习实践情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzdxssxsjqk-ksrq','dataset-szjcfx','main-abdteajzgzdxssxsjqk-ksrq','main-abdteajzgzdxssxsjqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzdxssxsjqk-ksrq'
);

-- 师资决策分析_教职工信息_教育教学_教职工指导学生实习实践情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzdxssxsjqk-jsrq','dataset-szjcfx','main-abdteajzgzdxssxsjqk-jsrq','main-abdteajzgzdxssxsjqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzdxssxsjqk-jsrq'
);

-- 师资决策分析_教职工信息_科学研究_教职工科研工作量-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgkygzl-zgh','dataset-szjcfx','main-abdteajzgkygzl-zgh','main-abdteajzgkygzl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgkygzl-zgh'
);

-- 师资决策分析_教职工信息_科学研究_教职工科研工作量-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgkygzl-tjnf','dataset-szjcfx','main-abdteajzgkygzl-tjnf','main-abdteajzgkygzl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgkygzl-tjnf'
);

-- 师资决策分析_教职工信息_科学研究_教职工科研工作量-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgkygzl-gzl','dataset-szjcfx','main-abdteajzgkygzl-gzl','main-abdteajzgkygzl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgkygzl-gzl'
);

-- 师资决策分析_教职工信息_科学研究_教职工参与科研项目-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgcykyxm-zgh','dataset-szjcfx','main-abdteajzgcykyxm-zgh','main-abdteajzgcykyxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgcykyxm-zgh'
);

-- 师资决策分析_教职工信息_科学研究_教职工参与科研项目-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgcykyxm-xmbh','dataset-szjcfx','main-abdteajzgcykyxm-xmbh','main-abdteajzgcykyxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgcykyxm-xmbh'
);

-- 师资决策分析_教职工信息_科学研究_教职工参与科研项目-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgcykyxm-xmmc','dataset-szjcfx','main-abdteajzgcykyxm-xmmc','main-abdteajzgcykyxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgcykyxm-xmmc'
);

-- 师资决策分析_教职工信息_科学研究_教职工参与科研项目-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgcykyxm-xmlb','dataset-szjcfx','main-abdteajzgcykyxm-xmlb','main-abdteajzgcykyxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgcykyxm-xmlb'
);

-- 师资决策分析_教职工信息_科学研究_教职工参与科研项目-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgcykyxm-xmjb','dataset-szjcfx','main-abdteajzgcykyxm-xmjb','main-abdteajzgcykyxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgcykyxm-xmjb'
);

-- 师资决策分析_教职工信息_科学研究_教职工参与科研项目-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgcykyxm-xmly','dataset-szjcfx','main-abdteajzgcykyxm-xmly','main-abdteajzgcykyxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgcykyxm-xmly'
);

-- 师资决策分析_教职工信息_科学研究_教职工参与科研项目-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgcykyxm-xmlydw','dataset-szjcfx','main-abdteajzgcykyxm-xmlydw','main-abdteajzgcykyxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgcykyxm-xmlydw'
);

-- 师资决策分析_教职工信息_科学研究_教职工参与科研项目-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgcykyxm-xmzxzt','dataset-szjcfx','main-abdteajzgcykyxm-xmzxzt','main-abdteajzgcykyxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgcykyxm-xmzxzt'
);

-- 师资决策分析_教职工信息_科学研究_教职工参与科研项目-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgcykyxm-smsx','dataset-szjcfx','main-abdteajzgcykyxm-smsx','main-abdteajzgcykyxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgcykyxm-smsx'
);

-- 师资决策分析_教职工信息_科学研究_教职工参与科研项目-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgcykyxm-lxrq','dataset-szjcfx','main-abdteajzgcykyxm-lxrq','main-abdteajzgcykyxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgcykyxm-lxrq'
);

-- 师资决策分析_教职工信息_科学研究_教职工参与科研项目-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgcykyxm-jxrq','dataset-szjcfx','main-abdteajzgcykyxm-jxrq','main-abdteajzgcykyxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgcykyxm-jxrq'
);

-- 师资决策分析_教职工信息_科学研究_教职工参与科研项目-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgcykyxm-xmid','dataset-szjcfx','main-abdteajzgcykyxm-xmid','main-abdteajzgcykyxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgcykyxm-xmid'
);

-- 师资决策分析_教职工信息_科学研究_教职工参与科研项目-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgcykyxm-xmjbdm','dataset-szjcfx','main-abdteajzgcykyxm-xmjbdm','main-abdteajzgcykyxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgcykyxm-xmjbdm'
);

-- 师资决策分析_教职工信息_科学研究_教职工发表论文-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgfblw-zgh','dataset-szjcfx','main-abdteajzgfblw-zgh','main-abdteajzgfblw',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgfblw-zgh'
);

-- 师资决策分析_教职工信息_科学研究_教职工发表论文-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgfblw-lwmc','dataset-szjcfx','main-abdteajzgfblw-lwmc','main-abdteajzgfblw',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgfblw-lwmc'
);

-- 师资决策分析_教职工信息_科学研究_教职工发表论文-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgfblw-sllb','dataset-szjcfx','main-abdteajzgfblw-sllb','main-abdteajzgfblw',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgfblw-sllb'
);

-- 师资决策分析_教职工信息_科学研究_教职工发表论文-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgfblw-lwfblx','dataset-szjcfx','main-abdteajzgfblw-lwfblx','main-abdteajzgfblw',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgfblw-lwfblx'
);

-- 师资决策分析_教职工信息_科学研究_教职工发表论文-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgfblw-hymc','dataset-szjcfx','main-abdteajzgfblw-hymc','main-abdteajzgfblw',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgfblw-hymc'
);

-- 师资决策分析_教职工信息_科学研究_教职工发表论文-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgfblw-hydj','dataset-szjcfx','main-abdteajzgfblw-hydj','main-abdteajzgfblw',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgfblw-hydj'
);

-- 师资决策分析_教职工信息_科学研究_教职工发表论文-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgfblw-kwjb','dataset-szjcfx','main-abdteajzgfblw-kwjb','main-abdteajzgfblw',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgfblw-kwjb'
);

-- 师资决策分析_教职工信息_科学研究_教职工发表论文-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgfblw-kwmc','dataset-szjcfx','main-abdteajzgfblw-kwmc','main-abdteajzgfblw',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgfblw-kwmc'
);

-- 师资决策分析_教职工信息_科学研究_教职工发表论文-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgfblw-fbrq','dataset-szjcfx','main-abdteajzgfblw-fbrq','main-abdteajzgfblw',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgfblw-fbrq'
);

-- 师资决策分析_教职工信息_科学研究_教职工发表论文-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgfblw-smsx','dataset-szjcfx','main-abdteajzgfblw-smsx','main-abdteajzgfblw',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgfblw-smsx'
);

-- 师资决策分析_教职工信息_科学研究_教职工发表论文-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgfblw-sftxzz','dataset-szjcfx','main-abdteajzgfblw-sftxzz','main-abdteajzgfblw',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgfblw-sftxzz'
);

-- 师资决策分析_教职工信息_科学研究_教职工发表论文-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgfblw-slwfq','dataset-szjcfx','main-abdteajzgfblw-slwfq','main-abdteajzgfblw',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgfblw-slwfq'
);

-- 师资决策分析_教职工信息_科学研究_教职工发明专利-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgfmzl-zgh','dataset-szjcfx','main-abdteajzgfmzl-zgh','main-abdteajzgfmzl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgfmzl-zgh'
);

-- 师资决策分析_教职工信息_科学研究_教职工发明专利-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgfmzl-zlmc','dataset-szjcfx','main-abdteajzgfmzl-zlmc','main-abdteajzgfmzl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgfmzl-zlmc'
);

-- 师资决策分析_教职工信息_科学研究_教职工发明专利-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgfmzl-zllx','dataset-szjcfx','main-abdteajzgfmzl-zllx','main-abdteajzgfmzl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgfmzl-zllx'
);

-- 师资决策分析_教职工信息_科学研究_教职工发明专利-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgfmzl-zlsqggrq','dataset-szjcfx','main-abdteajzgfmzl-zlsqggrq','main-abdteajzgfmzl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgfmzl-zlsqggrq'
);

-- 师资决策分析_教职工信息_科学研究_教职工发明专利-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgfmzl-zlgj','dataset-szjcfx','main-abdteajzgfmzl-zlgj','main-abdteajzgfmzl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgfmzl-zlgj'
);

-- 师资决策分析_教职工信息_科学研究_教职工发明专利-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgfmzl-fmrpm','dataset-szjcfx','main-abdteajzgfmzl-fmrpm','main-abdteajzgfmzl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgfmzl-fmrpm'
);

-- 师资决策分析_教职工信息_科学研究_教职工出版著作-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgcbzz-zgh','dataset-szjcfx','main-abdteajzgcbzz-zgh','main-abdteajzgcbzz',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgcbzz-zgh'
);

-- 师资决策分析_教职工信息_科学研究_教职工出版著作-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgcbzz-zzmc','dataset-szjcfx','main-abdteajzgcbzz-zzmc','main-abdteajzgcbzz',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgcbzz-zzmc'
);

-- 师资决策分析_教职工信息_科学研究_教职工出版著作-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgcbzz-cbsmc','dataset-szjcfx','main-abdteajzgcbzz-cbsmc','main-abdteajzgcbzz',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgcbzz-cbsmc'
);

-- 师资决策分析_教职工信息_科学研究_教职工出版著作-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgcbzz-cbsjb','dataset-szjcfx','main-abdteajzgcbzz-cbsjb','main-abdteajzgcbzz',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgcbzz-cbsjb'
);

-- 师资决策分析_教职工信息_科学研究_教职工出版著作-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgcbzz-cbrq','dataset-szjcfx','main-abdteajzgcbzz-cbrq','main-abdteajzgcbzz',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgcbzz-cbrq'
);

-- 师资决策分析_教职工信息_科学研究_教职工出版著作-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgcbzz-lzlb','dataset-szjcfx','main-abdteajzgcbzz-lzlb','main-abdteajzgcbzz',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgcbzz-lzlb'
);

-- 师资决策分析_教职工信息_科学研究_教职工出版著作-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgcbzz-smsx','dataset-szjcfx','main-abdteajzgcbzz-smsx','main-abdteajzgcbzz',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgcbzz-smsx'
);

-- 师资决策分析_教职工信息_科学研究_教职工出版著作-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgcbzz-cbsjbdm','dataset-szjcfx','main-abdteajzgcbzz-cbsjbdm','main-abdteajzgcbzz',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgcbzz-cbsjbdm'
);

-- 师资决策分析_教职工信息_科学研究_教职工资政报告采纳记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzzbgcnjl-zgh','dataset-szjcfx','main-abdteajzgzzbgcnjl-zgh','main-abdteajzgzzbgcnjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzzbgcnjl-zgh'
);

-- 师资决策分析_教职工信息_科学研究_教职工资政报告采纳记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzzbgcnjl-yjbgmc','dataset-szjcfx','main-abdteajzgzzbgcnjl-yjbgmc','main-abdteajzgzzbgcnjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzzbgcnjl-yjbgmc'
);

-- 师资决策分析_教职工信息_科学研究_教职工资政报告采纳记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzzbgcnjl-cndx','dataset-szjcfx','main-abdteajzgzzbgcnjl-cndx','main-abdteajzgzzbgcnjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzzbgcnjl-cndx'
);

-- 师资决策分析_教职工信息_科学研究_教职工资政报告采纳记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzzbgcnjl-psjbdm','dataset-szjcfx','main-abdteajzgzzbgcnjl-psjbdm','main-abdteajzgzzbgcnjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzzbgcnjl-psjbdm'
);

-- 师资决策分析_教职工信息_科学研究_教职工资政报告采纳记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzzbgcnjl-psjb','dataset-szjcfx','main-abdteajzgzzbgcnjl-psjb','main-abdteajzgzzbgcnjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzzbgcnjl-psjb'
);

-- 师资决策分析_教职工信息_科学研究_教职工资政报告采纳记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzzbgcnjl-psrq','dataset-szjcfx','main-abdteajzgzzbgcnjl-psrq','main-abdteajzgzzbgcnjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzzbgcnjl-psrq'
);

-- 师资决策分析_教职工信息_科学研究_教职工资政报告采纳记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgzzbgcnjl-smsx','dataset-szjcfx','main-abdteajzgzzbgcnjl-smsx','main-abdteajzgzzbgcnjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgzzbgcnjl-smsx'
);

-- 师资决策分析_教职工信息_科学研究_教职工科研获奖-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgkyhj-zgh','dataset-szjcfx','main-abdteajzgkyhj-zgh','main-abdteajzgkyhj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgkyhj-zgh'
);

-- 师资决策分析_教职工信息_科学研究_教职工科研获奖-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgkyhj-hjcgmc','dataset-szjcfx','main-abdteajzgkyhj-hjcgmc','main-abdteajzgkyhj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgkyhj-hjcgmc'
);

-- 师资决策分析_教职工信息_科学研究_教职工科研获奖-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgkyhj-jlmc','dataset-szjcfx','main-abdteajzgkyhj-jlmc','main-abdteajzgkyhj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgkyhj-jlmc'
);

-- 师资决策分析_教职工信息_科学研究_教职工科研获奖-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgkyhj-hjjb','dataset-szjcfx','main-abdteajzgkyhj-hjjb','main-abdteajzgkyhj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgkyhj-hjjb'
);

-- 师资决策分析_教职工信息_科学研究_教职工科研获奖-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgkyhj-hjdj','dataset-szjcfx','main-abdteajzgkyhj-hjdj','main-abdteajzgkyhj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgkyhj-hjdj'
);

-- 师资决策分析_教职工信息_科学研究_教职工科研获奖-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgkyhj-hjrq','dataset-szjcfx','main-abdteajzgkyhj-hjrq','main-abdteajzgkyhj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgkyhj-hjrq'
);

-- 师资决策分析_教职工信息_科学研究_教职工科研获奖-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgkyhj-smsx','dataset-szjcfx','main-abdteajzgkyhj-smsx','main-abdteajzgkyhj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgkyhj-smsx'
);

-- 师资决策分析_教职工信息_科学研究_教职工科研获奖-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgkyhj-hjjbdm','dataset-szjcfx','main-abdteajzgkyhj-hjjbdm','main-abdteajzgkyhj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgkyhj-hjjbdm'
);

-- 师资决策分析_教职工信息_科学研究_教职工负责项目经费记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgfzxmjfjl-zgh','dataset-szjcfx','main-abdteajzgfzxmjfjl-zgh','main-abdteajzgfzxmjfjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgfzxmjfjl-zgh'
);

-- 师资决策分析_教职工信息_科学研究_教职工负责项目经费记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgfzxmjfjl-xmbh','dataset-szjcfx','main-abdteajzgfzxmjfjl-xmbh','main-abdteajzgfzxmjfjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgfzxmjfjl-xmbh'
);

-- 师资决策分析_教职工信息_科学研究_教职工负责项目经费记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgfzxmjfjl-xmjfze','dataset-szjcfx','main-abdteajzgfzxmjfjl-xmjfze','main-abdteajzgfzxmjfjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgfzxmjfjl-xmjfze'
);

-- 师资决策分析_教职工信息_科学研究_教职工负责项目经费记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgfzxmjfjl-dzjf','dataset-szjcfx','main-abdteajzgfzxmjfjl-dzjf','main-abdteajzgfzxmjfjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgfzxmjfjl-dzjf'
);

-- 师资决策分析_教职工信息_科学研究_教职工负责项目经费记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgfzxmjfjl-dzrq','dataset-szjcfx','main-abdteajzgfzxmjfjl-dzrq','main-abdteajzgfzxmjfjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgfzxmjfjl-dzrq'
);

-- 师资决策分析_教职工信息_科学研究_教职工负责项目经费记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-szjcfx%main-abdteajzgfzxmjfjl-xmid','dataset-szjcfx','main-abdteajzgfzxmjfjl-xmid','main-abdteajzgfzxmjfjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-szjcfx%main-abdteajzgfzxmjfjl-xmid'
);