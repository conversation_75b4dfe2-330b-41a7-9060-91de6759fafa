/*
 Description		: [学生画像(stuportrait)]应用数据集
 Author				: dataapp
 Date				: 2024-06-20 01:00:00
 Lowcode Version	: V1.1.1_Beta5
 Database           : oracle
*/

-- 数据集模型字段
BEGIN
    EXECUTE IMMEDIATE 'CREATE TABLE t_da_dataset_model_column ( dataset_id VARCHAR2(50) NOT NULL,
column_id VARCHAR2(50) NOT NULL,
model_id VARCHAR2(50) NOT NULL,
id VARCHAR2(100),
create_by VARCHAR2(100),
create_time DATE,
update_by VARCHAR2(100),
update_time DATE,PRIMARY KEY (id) )';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

COMMENT ON TABLE t_da_dataset_model_column IS '数据集模型字段';
COMMENT ON COLUMN t_da_dataset_model_column.dataset_id IS '数据集ID';
COMMENT ON COLUMN t_da_dataset_model_column.column_id IS '字段ID';
COMMENT ON COLUMN t_da_dataset_model_column.model_id IS '模型ID';
COMMENT ON COLUMN t_da_dataset_model_column.id IS 'ID';
COMMENT ON COLUMN t_da_dataset_model_column.create_by IS '创建人';
COMMENT ON COLUMN t_da_dataset_model_column.create_time IS '创建时间';
COMMENT ON COLUMN t_da_dataset_model_column.update_by IS '更新人';
COMMENT ON COLUMN t_da_dataset_model_column.update_time IS '更新时间';

-- 学生画像-数据集
INSERT INTO t_da_dataset (id,sjjmc,
create_by,create_time,update_by,update_time,
copy_label,primary_app_id)
SELECT 'dataset-xshx','学生画像',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'','stuportrait'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset
    WHERE id = 'dataset-xshx'
);

-- dataset-xshx-stuportrait-数据集和应用关联表
INSERT INTO t_da_dataset_app (id,sjjid,appid,
create_by,create_time,update_by,update_time)
SELECT 'dataset-xshx-stuportrait','dataset-xshx','stuportrait',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_app
    WHERE id = 'dataset-xshx-stuportrait'
);

-- 基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_jbxx','dataset-xshx','基本信息',NULL,1,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_jbxx'
);

-- -SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_','dataset-xshx','',NULL,2,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_'
);

-- 日常生活-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_rcsh','dataset-xshx','日常生活',NULL,3,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_rcsh'
);

-- 学生请假-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_rcsh_xsqj','dataset-xshx','学生请假','xshx_rcsh',4,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_rcsh_xsqj'
);

-- 图书借阅-UND_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_rcsh_tsjy','dataset-xshx','图书借阅','xshx_rcsh',5,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_rcsh_tsjy'
);

-- 学生上网-UND_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_rcsh_xssw','dataset-xshx','学生上网','xshx_rcsh',6,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_rcsh_xssw'
);

-- 一卡通-UND_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_rcsh_ykt','dataset-xshx','一卡通','xshx_rcsh',7,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_rcsh_ykt'
);

-- 奖惩情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_jcqk','dataset-xshx','奖惩情况',NULL,8,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_jcqk'
);

-- 奖学金-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_jcqk_jxj','dataset-xshx','奖学金','xshx_jcqk',9,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_jcqk_jxj'
);

-- 奖励信息-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_jcqk_jlxx','dataset-xshx','奖励信息','xshx_jcqk',10,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_jcqk_jlxx'
);

-- 荣誉称号-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_jcqk_rych','dataset-xshx','荣誉称号','xshx_jcqk',11,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_jcqk_rych'
);

-- 违纪处分-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_jcqk_wjcf','dataset-xshx','违纪处分','xshx_jcqk',12,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_jcqk_wjcf'
);

-- 资助情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_zzqk','dataset-xshx','资助情况',NULL,13,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_zzqk'
);

-- 勤工助学-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_zzqk_qgzx','dataset-xshx','勤工助学','xshx_zzqk',14,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_zzqk_qgzx'
);

-- 困难补助-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_zzqk_knbz','dataset-xshx','困难补助','xshx_zzqk',15,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_zzqk_knbz'
);

-- 助学金-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_zzqk_zxj','dataset-xshx','助学金','xshx_zzqk',16,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_zzqk_zxj'
);

-- 助学贷款-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_zzqk_zxdk','dataset-xshx','助学贷款','xshx_zzqk',17,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_zzqk_zxdk'
);

-- 学生发展-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_xsfz','dataset-xshx','学生发展',NULL,18,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_xsfz'
);

-- 德育-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_xsfz_dy','dataset-xshx','德育','xshx_xsfz',19,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_xsfz_dy'
);

-- 智育-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_xsfz_zy','dataset-xshx','智育','xshx_xsfz',20,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_xsfz_zy'
);

-- 体育-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_xsfz_ty','dataset-xshx','体育','xshx_xsfz',21,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_xsfz_ty'
);

-- 美育-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_xsfz_my','dataset-xshx','美育','xshx_xsfz',22,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_xsfz_my'
);

-- 劳育-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_xsfz_ly','dataset-xshx','劳育','xshx_xsfz',23,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_xsfz_ly'
);

-- 毕业生去向-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_xsfz_bysqx','dataset-xshx','毕业生去向','xshx_xsfz',24,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_xsfz_bysqx'
);

-- 综合测评-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_xsfz_zhcp','dataset-xshx','综合测评','xshx_xsfz',25,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_xsfz_zhcp'
);

-- 资助信息-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_zzxx','dataset-xshx','资助信息',NULL,26,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_zzxx'
);

-- 资助情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_zzxx_zzqk','dataset-xshx','资助情况','xshx_zzxx',27,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_zzxx_zzqk'
);

-- 困难生情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_zzxx_knsqk','dataset-xshx','困难生情况','xshx_zzxx',28,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_zzxx_knsqk'
);

-- 资助总额-UND_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_zzxx_zzze','dataset-xshx','资助总额','xshx_zzxx',29,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_zzxx_zzze'
);

-- 奖惩信息-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_jcxx','dataset-xshx','奖惩信息',NULL,30,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_jcxx'
);

-- 奖学金-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_jcxx_jxj','dataset-xshx','奖学金','xshx_jcxx',31,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_jcxx_jxj'
);

-- 荣誉称号-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_jcxx_rych','dataset-xshx','荣誉称号','xshx_jcxx',32,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_jcxx_rych'
);

-- 违纪处分-UND_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_jcxx_wjcf','dataset-xshx','违纪处分','xshx_jcxx',33,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_jcxx_wjcf'
);

-- 日常事务-UND_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_rcsw','dataset-xshx','日常事务',NULL,34,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_rcsw'
);

-- 宿舍情况-UND_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_ssqk','dataset-xshx','宿舍情况',NULL,35,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_ssqk'
);

-- 学生活动-UND_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_xshd','dataset-xshx','学生活动',NULL,36,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_xshd'
);

-- 毕业就业-UND_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_byjy','dataset-xshx','毕业就业',NULL,37,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_byjy'
);

-- 学业成绩-UND_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xshx_xycj','dataset-xshx','学业成绩',NULL,38,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xshx_xycj'
);

-- 学生画像_基本信息_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jbxx%main-abdschxxjbxx','dataset-xshx','xshx_jbxx','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jbxx%main-abdschxxjbxx'
);

-- 学生画像_基本信息_学院基本信息-COL_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jbxx%main-abdcolxyjbxx','dataset-xshx','xshx_jbxx','main-abdcolxyjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jbxx%main-abdcolxyjbxx'
);

-- 学生画像_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jbxx%main-abdundbzksjbxx','dataset-xshx','xshx_jbxx','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jbxx%main-abdundbzksjbxx'
);

-- 学生画像_基本信息_本专科生教育经历-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jbxx%main-abdundbzksjyjl','dataset-xshx','xshx_jbxx','main-abdundbzksjyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jbxx%main-abdundbzksjyjl'
);

-- 学生画像_基本信息_本专科生新生信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jbxx%main-abdundbzksxsxx','dataset-xshx','xshx_jbxx','main-abdundbzksxsxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jbxx%main-abdundbzksxsxx'
);

-- 学生画像_基本信息_本专科来华留学生信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jbxx%main-abdundbzklhlxsxx','dataset-xshx','xshx_jbxx','main-abdundbzklhlxsxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jbxx%main-abdundbzklhlxsxx'
);

-- 学生画像_基本信息_本专科生学籍异动信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jbxx%main-abdundbzksxjydxx','dataset-xshx','xshx_jbxx','main-abdundbzksxjydxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jbxx%main-abdundbzksxjydxx'
);

-- 学生画像_基本信息_本专科生家庭情况-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jbxx%main-abdundbzksjtqk','dataset-xshx','xshx_jbxx','main-abdundbzksjtqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jbxx%main-abdundbzksjtqk'
);

-- 学生画像_基本信息_本专科生家庭成员-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jbxx%main-abdundbzksjtcy','dataset-xshx','xshx_jbxx','main-abdundbzksjtcy',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jbxx%main-abdundbzksjtcy'
);

-- 学生画像__本专科生奖学金-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_%main-abdschbzksjxj','dataset-xshx','xshx_','main-abdschbzksjxj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_%main-abdschbzksjxj'
);

-- 学生画像__本专科生荣誉称号-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_%main-abdschbzksrych','dataset-xshx','xshx_','main-abdschbzksrych',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_%main-abdschbzksrych'
);

-- 学生画像__本专科生活动-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_%main-abdschbzkshd','dataset-xshx','xshx_','main-abdschbzkshd',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_%main-abdschbzkshd'
);

-- 学生画像__本专科生社会实践-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_%main-abdschbzksshsj','dataset-xshx','xshx_','main-abdschbzksshsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_%main-abdschbzksshsj'
);

-- 学生画像__本专科生志愿服务-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_%main-abdschbzkszyfw','dataset-xshx','xshx_','main-abdschbzkszyfw',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_%main-abdschbzkszyfw'
);

-- 学生画像__本科生培养方案信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_%main-abdschbkspyfaxx','dataset-xshx','xshx_','main-abdschbkspyfaxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_%main-abdschbkspyfaxx'
);

-- 学生画像_日常生活_学生请假_学校本专科生请假人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_rcsh_xsqj%main-undsisch0083','dataset-xshx','xshx_rcsh_xsqj','main-undsisch0083',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_rcsh_xsqj%main-undsisch0083'
);

-- 学生画像_日常生活_学生请假_学校本专科生平均请假天数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_rcsh_xsqj%main-undsisch0084','dataset-xshx','xshx_rcsh_xsqj','main-undsisch0084',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_rcsh_xsqj%main-undsisch0084'
);

-- 学生画像_日常生活_学生请假_每月本专科生请假情况-UND_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_rcsh_xsqj%main-undsiund0003','dataset-xshx','xshx_rcsh_xsqj','main-undsiund0003',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_rcsh_xsqj%main-undsiund0003'
);

-- 学生画像_日常生活_图书借阅_每月本专科生图书借阅情况-UND_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_rcsh_tsjy%main-undsiund0001','dataset-xshx','xshx_rcsh_tsjy','main-undsiund0001',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_rcsh_tsjy%main-undsiund0001'
);

-- 学生画像_日常生活_学生上网_本专科生每日上网时长-UND_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_rcsh_xssw%main-undsiund0002','dataset-xshx','xshx_rcsh_xssw','main-undsiund0002',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_rcsh_xssw%main-undsiund0002'
);

-- 学生画像_日常生活_一卡通_本专科生一卡通每日消费情况-UND_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_rcsh_ykt%main-undsiund0010','dataset-xshx','xshx_rcsh_ykt','main-undsiund0010',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_rcsh_ykt%main-undsiund0010'
);

-- 学生画像_日常生活_一卡通_本专科生食堂每日消费情况-UND_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_rcsh_ykt%main-undsiund0011','dataset-xshx','xshx_rcsh_ykt','main-undsiund0011',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_rcsh_ykt%main-undsiund0011'
);

-- 学生画像_奖惩情况_奖学金_每年学校本专科生获得奖学金情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcqk_jxj%main-undsisch0085','dataset-xshx','xshx_jcqk_jxj','main-undsisch0085',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcqk_jxj%main-undsisch0085'
);

-- 学生画像_奖惩情况_奖学金_每年学校本专科生获得各级别奖学金情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcqk_jxj%main-undsisch0086','dataset-xshx','xshx_jcqk_jxj','main-undsisch0086',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcqk_jxj%main-undsisch0086'
);

-- 学生画像_奖惩情况_奖学金_每年各学院本专科生获得奖学金情况-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcqk_jxj%main-undsicol0055','dataset-xshx','xshx_jcqk_jxj','main-undsicol0055',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcqk_jxj%main-undsicol0055'
);

-- 学生画像_奖惩情况_奖学金_每年各学院本专科生获得各级别奖学金情况-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcqk_jxj%main-undsicol0056','dataset-xshx','xshx_jcqk_jxj','main-undsicol0056',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcqk_jxj%main-undsicol0056'
);

-- 学生画像_奖惩情况_奖励信息_每年学校本专科生获得奖励情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcqk_jlxx%main-undsisch0087','dataset-xshx','xshx_jcqk_jlxx','main-undsisch0087',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcqk_jlxx%main-undsisch0087'
);

-- 学生画像_奖惩情况_奖励信息_每年学校本专科生获得各级别奖励情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcqk_jlxx%main-undsisch0088','dataset-xshx','xshx_jcqk_jlxx','main-undsisch0088',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcqk_jlxx%main-undsisch0088'
);

-- 学生画像_奖惩情况_奖励信息_每年学院本专科生获得奖励情况-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcqk_jlxx%main-undsicol0057','dataset-xshx','xshx_jcqk_jlxx','main-undsicol0057',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcqk_jlxx%main-undsicol0057'
);

-- 学生画像_奖惩情况_奖励信息_每年学院本专科生获得各级别奖励情况-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcqk_jlxx%main-undsicol0058','dataset-xshx','xshx_jcqk_jlxx','main-undsicol0058',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcqk_jlxx%main-undsicol0058'
);

-- 学生画像_奖惩情况_荣誉称号_每年学校本专科生获荣誉称号情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcqk_rych%main-undsisch0089','dataset-xshx','xshx_jcqk_rych','main-undsisch0089',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcqk_rych%main-undsisch0089'
);

-- 学生画像_奖惩情况_荣誉称号_每年学校本专科生获各类荣誉称号情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcqk_rych%main-undsisch0090','dataset-xshx','xshx_jcqk_rych','main-undsisch0090',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcqk_rych%main-undsisch0090'
);

-- 学生画像_奖惩情况_荣誉称号_每年学校本专科生获各级别荣誉称号情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcqk_rych%main-undsisch0091','dataset-xshx','xshx_jcqk_rych','main-undsisch0091',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcqk_rych%main-undsisch0091'
);

-- 学生画像_奖惩情况_荣誉称号_每年学院本专科生获荣誉称号情况-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcqk_rych%main-undsicol0059','dataset-xshx','xshx_jcqk_rych','main-undsicol0059',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcqk_rych%main-undsicol0059'
);

-- 学生画像_奖惩情况_荣誉称号_每年学院本专科生获各级别荣誉称号情况-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcqk_rych%main-undsicol0060','dataset-xshx','xshx_jcqk_rych','main-undsicol0060',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcqk_rych%main-undsicol0060'
);

-- 学生画像_奖惩情况_荣誉称号_每年学院本专科生获各类荣誉称号情况-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcqk_rych%main-undsicol0061','dataset-xshx','xshx_jcqk_rych','main-undsicol0061',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcqk_rych%main-undsicol0061'
);

-- 学生画像_奖惩情况_违纪处分_每年学校本专科生违纪处分人次-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcqk_wjcf%main-undsisch0092','dataset-xshx','xshx_jcqk_wjcf','main-undsisch0092',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcqk_wjcf%main-undsisch0092'
);

-- 学生画像_奖惩情况_违纪处分_每年学校本专科生各类违纪处分人次-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcqk_wjcf%main-undsisch0093','dataset-xshx','xshx_jcqk_wjcf','main-undsisch0093',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcqk_wjcf%main-undsisch0093'
);

-- 学生画像_奖惩情况_违纪处分_每年学校本专科生各处分名称人次-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcqk_wjcf%main-undsisch0094','dataset-xshx','xshx_jcqk_wjcf','main-undsisch0094',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcqk_wjcf%main-undsisch0094'
);

-- 学生画像_奖惩情况_违纪处分_每年各学院本专科生违纪处分人次-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcqk_wjcf%main-undsicol0062','dataset-xshx','xshx_jcqk_wjcf','main-undsicol0062',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcqk_wjcf%main-undsicol0062'
);

-- 学生画像_奖惩情况_违纪处分_每年各学院本专科生各类违纪处分人次-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcqk_wjcf%main-undsicol0063','dataset-xshx','xshx_jcqk_wjcf','main-undsicol0063',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcqk_wjcf%main-undsicol0063'
);

-- 学生画像_奖惩情况_违纪处分_每年各学院本专科生各处分名称人次-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcqk_wjcf%main-undsicol0064','dataset-xshx','xshx_jcqk_wjcf','main-undsicol0064',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcqk_wjcf%main-undsicol0064'
);

-- 学生画像_奖惩情况_本专科生综合测评结果-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcqk%main-abdundbzkszhcpjg','dataset-xshx','xshx_jcqk','main-abdundbzkszhcpjg',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcqk%main-abdundbzkszhcpjg'
);

-- 学生画像_奖惩情况_本专科生综合测评成绩明细-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcqk%main-abdundbzkszhcpcjmx','dataset-xshx','xshx_jcqk','main-abdundbzkszhcpcjmx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcqk%main-abdundbzkszhcpcjmx'
);

-- 学生画像_奖惩情况_本专科生获得奖学金记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcqk%main-abdundbzkshdjxjjl','dataset-xshx','xshx_jcqk','main-abdundbzkshdjxjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcqk%main-abdundbzkshdjxjjl'
);

-- 学生画像_奖惩情况_本专科生获得荣誉称号记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcqk%main-abdundbzkshdrychjl','dataset-xshx','xshx_jcqk','main-abdundbzkshdrychjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcqk%main-abdundbzkshdrychjl'
);

-- 学生画像_奖惩情况_本专科生违纪处分-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcqk%main-abdundbzkswjcf','dataset-xshx','xshx_jcqk','main-abdundbzkswjcf',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcqk%main-abdundbzkswjcf'
);

-- 学生画像_奖惩情况_本专科生获奖记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcqk%main-abdundbzkshjjl','dataset-xshx','xshx_jcqk','main-abdundbzkshjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcqk%main-abdundbzkshjjl'
);

-- 学生画像_资助情况_勤工助学_每年学校本专科生勤工助学情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzqk_qgzx%main-undsisch0095','dataset-xshx','xshx_zzqk_qgzx','main-undsisch0095',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzqk_qgzx%main-undsisch0095'
);

-- 学生画像_资助情况_勤工助学_每年学校校内外岗位勤工助学情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzqk_qgzx%main-undsisch0096','dataset-xshx','xshx_zzqk_qgzx','main-undsisch0096',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzqk_qgzx%main-undsisch0096'
);

-- 学生画像_资助情况_勤工助学_每年学校本专科生各岗位类型勤工助学情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzqk_qgzx%main-undsisch0097','dataset-xshx','xshx_zzqk_qgzx','main-undsisch0097',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzqk_qgzx%main-undsisch0097'
);

-- 学生画像_资助情况_勤工助学_每月学校本专科生勤工助学平均工作时长-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzqk_qgzx%main-undsisch0098','dataset-xshx','xshx_zzqk_qgzx','main-undsisch0098',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzqk_qgzx%main-undsisch0098'
);

-- 学生画像_资助情况_勤工助学_学校本专科生校内岗位勤工助学每小时平均酬金-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzqk_qgzx%main-undsisch0099','dataset-xshx','xshx_zzqk_qgzx','main-undsisch0099',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzqk_qgzx%main-undsisch0099'
);

-- 学生画像_资助情况_勤工助学_每年学校勤工助学困难生比例-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzqk_qgzx%main-undsisch0100','dataset-xshx','xshx_zzqk_qgzx','main-undsisch0100',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzqk_qgzx%main-undsisch0100'
);

-- 学生画像_资助情况_勤工助学_每年学院本专科生勤工助学情况-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzqk_qgzx%main-undsicol0065','dataset-xshx','xshx_zzqk_qgzx','main-undsicol0065',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzqk_qgzx%main-undsicol0065'
);

-- 学生画像_资助情况_勤工助学_每年学院本专科生各岗位类型勤工助学情况-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzqk_qgzx%main-undsicol0066','dataset-xshx','xshx_zzqk_qgzx','main-undsicol0066',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzqk_qgzx%main-undsicol0066'
);

-- 学生画像_资助情况_勤工助学_每年学院校内外岗位勤工助学情况-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzqk_qgzx%main-undsicol0067','dataset-xshx','xshx_zzqk_qgzx','main-undsicol0067',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzqk_qgzx%main-undsicol0067'
);

-- 学生画像_资助情况_勤工助学_每月学院本专科生勤工助学平均工作时长-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzqk_qgzx%main-undsicol0068','dataset-xshx','xshx_zzqk_qgzx','main-undsicol0068',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzqk_qgzx%main-undsicol0068'
);

-- 学生画像_资助情况_勤工助学_学院本专科生校内岗位勤工助学每小时平均酬金-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzqk_qgzx%main-undsicol0069','dataset-xshx','xshx_zzqk_qgzx','main-undsicol0069',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzqk_qgzx%main-undsicol0069'
);

-- 学生画像_资助情况_困难补助_每年学校本专科生困难补助情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzqk_knbz%main-undsisch0101','dataset-xshx','xshx_zzqk_knbz','main-undsisch0101',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzqk_knbz%main-undsisch0101'
);

-- 学生画像_资助情况_困难补助_每年学校本专科生困难补助资金来源情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzqk_knbz%main-undsisch0102','dataset-xshx','xshx_zzqk_knbz','main-undsisch0102',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzqk_knbz%main-undsisch0102'
);

-- 学生画像_资助情况_困难补助_每年学校困难生获困难补助覆盖率-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzqk_knbz%main-undsisch0103','dataset-xshx','xshx_zzqk_knbz','main-undsisch0103',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzqk_knbz%main-undsisch0103'
);

-- 学生画像_资助情况_助学金_每年学校本专科生获助学金情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzqk_zxj%main-undsisch0104','dataset-xshx','xshx_zzqk_zxj','main-undsisch0104',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzqk_zxj%main-undsisch0104'
);

-- 学生画像_资助情况_助学金_每年学校本专科生获各奖励级别助学金情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzqk_zxj%main-undsisch0105','dataset-xshx','xshx_zzqk_zxj','main-undsisch0105',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzqk_zxj%main-undsisch0105'
);

-- 学生画像_资助情况_助学贷款_每年学校本专科助学贷款情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzqk_zxdk%main-undsisch0106','dataset-xshx','xshx_zzqk_zxdk','main-undsisch0106',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzqk_zxdk%main-undsisch0106'
);

-- 学生画像_资助情况_助学贷款_每年学校本专科生各类助学贷款情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzqk_zxdk%main-undsisch0107','dataset-xshx','xshx_zzqk_zxdk','main-undsisch0107',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzqk_zxdk%main-undsisch0107'
);

-- 学生画像_资助情况_本专科生困难生信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzqk%main-abdundbzksknsxx','dataset-xshx','xshx_zzqk','main-abdundbzksknsxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzqk%main-abdundbzksknsxx'
);

-- 学生画像_资助情况_本专科生获得助学金记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzqk%main-abdundbzkshdzxjjl','dataset-xshx','xshx_zzqk','main-abdundbzkshdzxjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzqk%main-abdundbzkshdzxjjl'
);

-- 学生画像_资助情况_本专科生助学金发放记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzqk%main-abdundbzkszxjffjl','dataset-xshx','xshx_zzqk','main-abdundbzkszxjffjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzqk%main-abdundbzkszxjffjl'
);

-- 学生画像_资助情况_本专科生勤工助学上岗记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzqk%main-abdundbzksqgzxsgjl','dataset-xshx','xshx_zzqk','main-abdundbzksqgzxsgjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzqk%main-abdundbzksqgzxsgjl'
);

-- 学生画像_资助情况_本专科生勤工助学报酬发放记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzqk%main-abdundbzksqgzxbcffjl','dataset-xshx','xshx_zzqk','main-abdundbzksqgzxbcffjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzqk%main-abdundbzksqgzxbcffjl'
);

-- 学生画像_资助情况_本专科生获得困难补助记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzqk%main-abdundbzkshdknbzjl','dataset-xshx','xshx_zzqk','main-abdundbzkshdknbzjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzqk%main-abdundbzkshdknbzjl'
);

-- 学生画像_资助情况_本专科生困难补助发放记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzqk%main-abdundbzksknbzffjl','dataset-xshx','xshx_zzqk','main-abdundbzksknbzffjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzqk%main-abdundbzksknbzffjl'
);

-- 学生画像_资助情况_本专科生获得助学贷款记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzqk%main-abdundbzkshdzxdkjl','dataset-xshx','xshx_zzqk','main-abdundbzkshdzxdkjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzqk%main-abdundbzkshdzxdkjl'
);

-- 学生画像_资助情况_本专科生助学贷款发放记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzqk%main-abdundbzkszxdkffjl','dataset-xshx','xshx_zzqk','main-abdundbzkszxdkffjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzqk%main-abdundbzkszxdkffjl'
);

-- 学生画像_资助情况_本专科生助学贷款还款记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzqk%main-abdundbzkszxdkhkjl','dataset-xshx','xshx_zzqk','main-abdundbzkszxdkhkjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzqk%main-abdundbzkszxdkhkjl'
);

-- 学生画像_学生发展_德育_学校本专科生政治面貌占比-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_dy%main-undsisch0108','dataset-xshx','xshx_xsfz_dy','main-undsisch0108',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_dy%main-undsisch0108'
);

-- 学生画像_学生发展_德育_每年学校党建培训情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_dy%main-undsisch0109','dataset-xshx','xshx_xsfz_dy','main-undsisch0109',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_dy%main-undsisch0109'
);

-- 学生画像_学生发展_德育_每年学校社团活动情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_dy%main-undsisch0110','dataset-xshx','xshx_xsfz_dy','main-undsisch0110',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_dy%main-undsisch0110'
);

-- 学生画像_学生发展_德育_每年学校德育活动情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_dy%main-undsisch0111','dataset-xshx','xshx_xsfz_dy','main-undsisch0111',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_dy%main-undsisch0111'
);

-- 学生画像_学生发展_德育_每年学校本专科生人均参与德育活动次数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_dy%main-undsisch0112','dataset-xshx','xshx_xsfz_dy','main-undsisch0112',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_dy%main-undsisch0112'
);

-- 学生画像_学生发展_德育_每年学校本专科生参与德育活动比例-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_dy%main-undsisch0113','dataset-xshx','xshx_xsfz_dy','main-undsisch0113',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_dy%main-undsisch0113'
);

-- 学生画像_学生发展_德育_各学院本专科生政治面貌占比-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_dy%main-undsicol0070','dataset-xshx','xshx_xsfz_dy','main-undsicol0070',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_dy%main-undsicol0070'
);

-- 学生画像_学生发展_智育_每年学校本专科生学业成绩挂科率-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_zy%main-undsisch0114','dataset-xshx','xshx_xsfz_zy','main-undsisch0114',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_zy%main-undsisch0114'
);

-- 学生画像_学生发展_智育_每年学校本专科生四六级通过率-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_zy%main-undsisch0115','dataset-xshx','xshx_xsfz_zy','main-undsisch0115',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_zy%main-undsisch0115'
);

-- 学生画像_学生发展_智育_每年各学院本专科生专业平均分-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_zy%main-undsisch0116','dataset-xshx','xshx_xsfz_zy','main-undsisch0116',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_zy%main-undsisch0116'
);

-- 学生画像_学生发展_智育_每年学校本专科生科研成果总数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_zy%main-undsisch0117','dataset-xshx','xshx_xsfz_zy','main-undsisch0117',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_zy%main-undsisch0117'
);

-- 学生画像_学生发展_智育_每年学校本专科生发表论文数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_zy%main-undsisch0118','dataset-xshx','xshx_xsfz_zy','main-undsisch0118',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_zy%main-undsisch0118'
);

-- 学生画像_学生发展_智育_每年学校本专科生各刊物级别论文发表数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_zy%main-undsisch0119','dataset-xshx','xshx_xsfz_zy','main-undsisch0119',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_zy%main-undsisch0119'
);

-- 学生画像_学生发展_智育_每年学校本专科生发明专利数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_zy%main-undsisch0120','dataset-xshx','xshx_xsfz_zy','main-undsisch0120',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_zy%main-undsisch0120'
);

-- 学生画像_学生发展_智育_每年学校本专科生各发明专利类别数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_zy%main-undsisch0121','dataset-xshx','xshx_xsfz_zy','main-undsisch0121',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_zy%main-undsisch0121'
);

-- 学生画像_学生发展_智育_每年学校本专科生科研项目数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_zy%main-undsisch0122','dataset-xshx','xshx_xsfz_zy','main-undsisch0122',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_zy%main-undsisch0122'
);

-- 学生画像_学生发展_智育_每年学校本专科生各科研项目级别数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_zy%main-undsisch0123','dataset-xshx','xshx_xsfz_zy','main-undsisch0123',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_zy%main-undsisch0123'
);

-- 学生画像_学生发展_智育_每年学校举办智育活动情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_zy%main-undsisch0124','dataset-xshx','xshx_xsfz_zy','main-undsisch0124',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_zy%main-undsisch0124'
);

-- 学生画像_学生发展_智育_每年学校本专科生人均参与智育活动数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_zy%main-undsisch0125','dataset-xshx','xshx_xsfz_zy','main-undsisch0125',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_zy%main-undsisch0125'
);

-- 学生画像_学生发展_智育_每年学校智育活动本专科生参加比例-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_zy%main-undsisch0126','dataset-xshx','xshx_xsfz_zy','main-undsisch0126',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_zy%main-undsisch0126'
);

-- 学生画像_学生发展_智育_每年学校应届生学分完成情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_zy%main-undsisch0523','dataset-xshx','xshx_xsfz_zy','main-undsisch0523',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_zy%main-undsisch0523'
);

-- 学生画像_学生发展_智育_每年各学院本专科生学业成绩挂科率-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_zy%main-undsicol0071','dataset-xshx','xshx_xsfz_zy','main-undsicol0071',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_zy%main-undsicol0071'
);

-- 学生画像_学生发展_智育_每年各学院本专科生四六级通过率-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_zy%main-undsicol0072','dataset-xshx','xshx_xsfz_zy','main-undsicol0072',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_zy%main-undsicol0072'
);

-- 学生画像_学生发展_智育_每年各专业本专科生专业平均分-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_zy%main-undsicol0073','dataset-xshx','xshx_xsfz_zy','main-undsicol0073',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_zy%main-undsicol0073'
);

-- 学生画像_学生发展_智育_每年学院本专科生获智育竞赛奖项数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_zy%main-undsicol0074','dataset-xshx','xshx_xsfz_zy','main-undsicol0074',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_zy%main-undsicol0074'
);

-- 学生画像_学生发展_智育_每年学院本专科生获智育竞赛各级别奖项数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_zy%main-undsicol0075','dataset-xshx','xshx_xsfz_zy','main-undsicol0075',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_zy%main-undsicol0075'
);

-- 学生画像_学生发展_智育_每学期本专科生课程平均分-UND_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_zy%main-undsiund0007','dataset-xshx','xshx_xsfz_zy','main-undsiund0007',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_zy%main-undsiund0007'
);

-- 学生画像_学生发展_智育_每学期本专科生课程平均分排名-UND_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_zy%main-undsiund0008','dataset-xshx','xshx_xsfz_zy','main-undsiund0008',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_zy%main-undsiund0008'
);

-- 学生画像_学生发展_智育_每学期本专科生不及格课程数量-UND_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_zy%main-undsiund0009','dataset-xshx','xshx_xsfz_zy','main-undsiund0009',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_zy%main-undsiund0009'
);

-- 学生画像_学生发展_智育_本专科生平均学分绩点-UND_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_zy%main-undsiund0014','dataset-xshx','xshx_xsfz_zy','main-undsiund0014',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_zy%main-undsiund0014'
);

-- 学生画像_学生发展_体育_每学期学校本专科生体育成绩挂科率-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_ty%main-undsisch0127','dataset-xshx','xshx_xsfz_ty','main-undsisch0127',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_ty%main-undsisch0127'
);

-- 学生画像_学生发展_体育_每学年学校本专科生体测成绩及格率-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_ty%main-undsisch0128','dataset-xshx','xshx_xsfz_ty','main-undsisch0128',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_ty%main-undsisch0128'
);

-- 学生画像_学生发展_体育_每学年各年级体测成绩均分-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_ty%main-undsisch0129','dataset-xshx','xshx_xsfz_ty','main-undsisch0129',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_ty%main-undsisch0129'
);

-- 学生画像_学生发展_体育_每年学校举办体育活动情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_ty%main-undsisch0130','dataset-xshx','xshx_xsfz_ty','main-undsisch0130',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_ty%main-undsisch0130'
);

-- 学生画像_学生发展_体育_每年学校本专科生人均参与体育活动次数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_ty%main-undsisch0131','dataset-xshx','xshx_xsfz_ty','main-undsisch0131',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_ty%main-undsisch0131'
);

-- 学生画像_学生发展_体育_每年学校体育活动本专科生参与比例-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_ty%main-undsisch0132','dataset-xshx','xshx_xsfz_ty','main-undsisch0132',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_ty%main-undsisch0132'
);

-- 学生画像_学生发展_体育_每学期各学院本专科生体育成绩挂科率-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_ty%main-undsicol0076','dataset-xshx','xshx_xsfz_ty','main-undsicol0076',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_ty%main-undsicol0076'
);

-- 学生画像_学生发展_体育_每学期各学院体育成绩均分-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_ty%main-undsicol0077','dataset-xshx','xshx_xsfz_ty','main-undsicol0077',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_ty%main-undsicol0077'
);

-- 学生画像_学生发展_体育_每学年各学院本专科生体测成绩及格率-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_ty%main-undsicol0078','dataset-xshx','xshx_xsfz_ty','main-undsicol0078',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_ty%main-undsicol0078'
);

-- 学生画像_学生发展_体育_每学年各学院体测成绩均分-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_ty%main-undsicol0079','dataset-xshx','xshx_xsfz_ty','main-undsicol0079',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_ty%main-undsicol0079'
);

-- 学生画像_学生发展_美育_每年学校美育活动情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_my%main-undsisch0133','dataset-xshx','xshx_xsfz_my','main-undsisch0133',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_my%main-undsisch0133'
);

-- 学生画像_学生发展_美育_每年学校本专科生人均参与美育活动次数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_my%main-undsisch0134','dataset-xshx','xshx_xsfz_my','main-undsisch0134',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_my%main-undsisch0134'
);

-- 学生画像_学生发展_美育_每年学校本专科生参与美育活动比例-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_my%main-undsisch0135','dataset-xshx','xshx_xsfz_my','main-undsisch0135',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_my%main-undsisch0135'
);

-- 学生画像_学生发展_劳育_每年学校劳育活动情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_ly%main-undsisch0136','dataset-xshx','xshx_xsfz_ly','main-undsisch0136',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_ly%main-undsisch0136'
);

-- 学生画像_学生发展_劳育_每年学校本专科生人均参与劳育活动次数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_ly%main-undsisch0137','dataset-xshx','xshx_xsfz_ly','main-undsisch0137',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_ly%main-undsisch0137'
);

-- 学生画像_学生发展_劳育_每年学校本专科生参与劳育活动比例-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_ly%main-undsisch0138','dataset-xshx','xshx_xsfz_ly','main-undsisch0138',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_ly%main-undsisch0138'
);

-- 学生画像_学生发展_劳育_每年学校本专科生参与志愿服务情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_ly%main-undsisch0139','dataset-xshx','xshx_xsfz_ly','main-undsisch0139',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_ly%main-undsisch0139'
);

-- 学生画像_学生发展_劳育_学校志愿团队数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_ly%main-undsisch0140','dataset-xshx','xshx_xsfz_ly','main-undsisch0140',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_ly%main-undsisch0140'
);

-- 学生画像_学生发展_劳育_每年学校本专科生人均志愿服务时长-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_ly%main-undsisch0141','dataset-xshx','xshx_xsfz_ly','main-undsisch0141',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_ly%main-undsisch0141'
);

-- 学生画像_学生发展_劳育_每年学校本专科生参与社会实践情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_ly%main-undsisch0142','dataset-xshx','xshx_xsfz_ly','main-undsisch0142',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_ly%main-undsisch0142'
);

-- 学生画像_学生发展_劳育_每年学校本专科生创新创业情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_ly%main-undsisch0143','dataset-xshx','xshx_xsfz_ly','main-undsisch0143',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_ly%main-undsisch0143'
);

-- 学生画像_学生发展_劳育_每年学校各项目级别创新创业数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_ly%main-undsisch0144','dataset-xshx','xshx_xsfz_ly','main-undsisch0144',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_ly%main-undsisch0144'
);

-- 学生画像_学生发展_劳育_每年各学院本专科生参与志愿活动人数比例-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_ly%main-undsicol0080','dataset-xshx','xshx_xsfz_ly','main-undsicol0080',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_ly%main-undsicol0080'
);

-- 学生画像_学生发展_劳育_每年各学院本专科生人均志愿服务时长-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_ly%main-undsicol0081','dataset-xshx','xshx_xsfz_ly','main-undsicol0081',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_ly%main-undsicol0081'
);

-- 学生画像_学生发展_劳育_每年各学院本专科生参与社会实践人数比例-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_ly%main-undsicol0082','dataset-xshx','xshx_xsfz_ly','main-undsicol0082',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_ly%main-undsicol0082'
);

-- 学生画像_学生发展_劳育_每年各学院平均创新创业数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_ly%main-undsicol0083','dataset-xshx','xshx_xsfz_ly','main-undsicol0083',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_ly%main-undsicol0083'
);

-- 学生画像_学生发展_毕业生去向_每年学校本专科生毕业去向情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_bysqx%main-undsisch0145','dataset-xshx','xshx_xsfz_bysqx','main-undsisch0145',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_bysqx%main-undsisch0145'
);

-- 学生画像_学生发展_毕业生去向_每年学校本专科生就业单位行业分布情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_bysqx%main-undsisch0146','dataset-xshx','xshx_xsfz_bysqx','main-undsisch0146',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_bysqx%main-undsisch0146'
);

-- 学生画像_学生发展_毕业生去向_每年学校本专科生就业单位性质分布情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_bysqx%main-undsisch0147','dataset-xshx','xshx_xsfz_bysqx','main-undsisch0147',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_bysqx%main-undsisch0147'
);

-- 学生画像_学生发展_毕业生去向_每年学校本专科生就业区域分布情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_bysqx%main-undsisch0148','dataset-xshx','xshx_xsfz_bysqx','main-undsisch0148',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_bysqx%main-undsisch0148'
);

-- 学生画像_学生发展_综合测评_每年学校本专科生综合测评平均分-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xsfz_zhcp%main-undsisch0524','dataset-xshx','xshx_xsfz_zhcp','main-undsisch0524',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xsfz_zhcp%main-undsisch0524'
);

-- 学生画像_资助信息_资助情况_每年学校资助学生情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzxx_zzqk%main-undsisch0515','dataset-xshx','xshx_zzxx_zzqk','main-undsisch0515',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzxx_zzqk%main-undsisch0515'
);

-- 学生画像_资助信息_资助情况_每年学生资助各困难程度学生情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzxx_zzqk%main-undsisch0516','dataset-xshx','xshx_zzxx_zzqk','main-undsisch0516',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzxx_zzqk%main-undsisch0516'
);

-- 学生画像_资助信息_困难生情况_学校各民族困难生情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzxx_knsqk%main-undsisch0517','dataset-xshx','xshx_zzxx_knsqk','main-undsisch0517',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzxx_knsqk%main-undsisch0517'
);

-- 学生画像_资助信息_困难生情况_学校各省份困难生情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzxx_knsqk%main-undsisch0518','dataset-xshx','xshx_zzxx_knsqk','main-undsisch0518',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzxx_knsqk%main-undsisch0518'
);

-- 学生画像_资助信息_资助总额_本专生获得资助情况-UND_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_zzxx_zzze%main-undsiund0012','dataset-xshx','xshx_zzxx_zzze','main-undsiund0012',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_zzxx_zzze%main-undsiund0012'
);

-- 学生画像_奖惩信息_奖学金_每年学校奖学金性别分布情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcxx_jxj%main-undsisch0519','dataset-xshx','xshx_jcxx_jxj','main-undsisch0519',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcxx_jxj%main-undsisch0519'
);

-- 学生画像_奖惩信息_奖学金_每年学校困难生奖学金获奖情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcxx_jxj%main-undsisch0520','dataset-xshx','xshx_jcxx_jxj','main-undsisch0520',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcxx_jxj%main-undsisch0520'
);

-- 学生画像_奖惩信息_奖学金_每年学校非困难生奖学金获奖情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcxx_jxj%main-undsisch0521','dataset-xshx','xshx_jcxx_jxj','main-undsisch0521',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcxx_jxj%main-undsisch0521'
);

-- 学生画像_奖惩信息_奖学金_每年本专科生获得奖学金情况-UND_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcxx_jxj%main-undsiund0004','dataset-xshx','xshx_jcxx_jxj','main-undsiund0004',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcxx_jxj%main-undsiund0004'
);

-- 学生画像_奖惩信息_奖学金_每年本专科生获得各级别奖学金情况-UND_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcxx_jxj%main-undsiund0013','dataset-xshx','xshx_jcxx_jxj','main-undsiund0013',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcxx_jxj%main-undsiund0013'
);

-- 学生画像_奖惩信息_荣誉称号_每年学校学生获各荣誉称号类型情况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcxx_rych%main-undsisch0522','dataset-xshx','xshx_jcxx_rych','main-undsisch0522',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcxx_rych%main-undsisch0522'
);

-- 学生画像_奖惩信息_荣誉称号_每学期本专科生获各级荣誉称号情况-UND_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcxx_rych%main-undsiund0005','dataset-xshx','xshx_jcxx_rych','main-undsiund0005',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcxx_rych%main-undsiund0005'
);

-- 学生画像_奖惩信息_违纪处分_每学期本专科生违纪处分次数-UND_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_jcxx_wjcf%main-undsiund0006','dataset-xshx','xshx_jcxx_wjcf','main-undsiund0006',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_jcxx_wjcf%main-undsiund0006'
);

-- 学生画像_日常事务_本专科生学生证补办信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_rcsw%main-abdundbzksxszbbxx','dataset-xshx','xshx_rcsw','main-abdundbzksxszbbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_rcsw%main-abdundbzksxszbbxx'
);

-- 学生画像_日常事务_本专科生请假信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_rcsw%main-abdundbzksqjxx','dataset-xshx','xshx_rcsw','main-abdundbzksqjxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_rcsw%main-abdundbzksqjxx'
);

-- 学生画像_日常事务_本专科生节假日离返校信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_rcsw%main-abdundbzksjjrlfxxx','dataset-xshx','xshx_rcsw','main-abdundbzksjjrlfxxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_rcsw%main-abdundbzksjjrlfxxx'
);

-- 学生画像_日常事务_本专科生图书借阅信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_rcsw%main-abdundbzkstsjyxx','dataset-xshx','xshx_rcsw','main-abdundbzkstsjyxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_rcsw%main-abdundbzkstsjyxx'
);

-- 学生画像_日常事务_本专科生一卡通消费信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_rcsw%main-abdundbzksyktxfxx','dataset-xshx','xshx_rcsw','main-abdundbzksyktxfxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_rcsw%main-abdundbzksyktxfxx'
);

-- 学生画像_日常事务_本专科生上网信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_rcsw%main-abdundbzksswxx','dataset-xshx','xshx_rcsw','main-abdundbzksswxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_rcsw%main-abdundbzksswxx'
);

-- 学生画像_日常事务_本专科生入伍记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_rcsw%main-abdundbzksrwjl','dataset-xshx','xshx_rcsw','main-abdundbzksrwjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_rcsw%main-abdundbzksrwjl'
);

-- 学生画像_日常事务_本专科生出国(境)留学记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_rcsw%main-abdundbzkscgjlxjl','dataset-xshx','xshx_rcsw','main-abdundbzkscgjlxjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_rcsw%main-abdundbzkscgjlxjl'
);

-- 学生画像_日常事务_本专科生出国(境)项目-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_rcsw%main-abdundbzkscgjxm','dataset-xshx','xshx_rcsw','main-abdundbzkscgjxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_rcsw%main-abdundbzkscgjxm'
);

-- 学生画像_宿舍情况_本专科生住宿信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_ssqk%main-abdundbzkszsxx','dataset-xshx','xshx_ssqk','main-abdundbzkszsxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_ssqk%main-abdundbzkszsxx'
);

-- 学生画像_宿舍情况_本专科生宿舍查寝信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_ssqk%main-abdundbzkssscqxx','dataset-xshx','xshx_ssqk','main-abdundbzkssscqxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_ssqk%main-abdundbzkssscqxx'
);

-- 学生画像_学生活动_本专科生学科竞赛信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xshd%main-abdundbzksxkjsxx','dataset-xshx','xshx_xshd','main-abdundbzksxkjsxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xshd%main-abdundbzksxkjsxx'
);

-- 学生画像_毕业就业_本专科生毕业信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_byjy%main-abdundbzksbyxx','dataset-xshx','xshx_byjy','main-abdundbzksbyxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_byjy%main-abdundbzksbyxx'
);

-- 学生画像_毕业就业_本专科生就业信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_byjy%main-abdundbzksjyxx','dataset-xshx','xshx_byjy','main-abdundbzksjyxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_byjy%main-abdundbzksjyxx'
);

-- 学生画像_学业成绩_本专科生学业成绩-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xycj%main-abdundbzksxycj','dataset-xshx','xshx_xycj','main-abdundbzksxycj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xycj%main-abdundbzksxycj'
);

-- 学生画像_学业成绩_本专科生体测成绩-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xshx%xshx_xycj%main-abdundbzkstccj','dataset-xshx','xshx_xycj','main-abdundbzkstccj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xshx%xshx_xycj%main-abdundbzkstccj'
);