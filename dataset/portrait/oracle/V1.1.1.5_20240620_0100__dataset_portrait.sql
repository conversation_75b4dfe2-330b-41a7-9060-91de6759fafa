/*
 Description		: [教师画像(portrait)]应用数据集
 Author				: dataapp
 Date				: 2024-06-20 01:00:00
 Lowcode Version	: V1.1.1_Beta5
 Database           : oracle
*/

-- 数据集模型字段
BEGIN
    EXECUTE IMMEDIATE 'CREATE TABLE t_da_dataset_model_column ( dataset_id VARCHAR2(50) NOT NULL,
column_id VARCHAR2(50) NOT NULL,
model_id VARCHAR2(50) NOT NULL,
id VARCHAR2(100),
create_by VARCHAR2(100),
create_time DATE,
update_by VARCHAR2(100),
update_time DATE,PRIMARY KEY (id) )';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

COMMENT ON TABLE t_da_dataset_model_column IS '数据集模型字段';
COMMENT ON COLUMN t_da_dataset_model_column.dataset_id IS '数据集ID';
COMMENT ON COLUMN t_da_dataset_model_column.column_id IS '字段ID';
COMMENT ON COLUMN t_da_dataset_model_column.model_id IS '模型ID';
COMMENT ON COLUMN t_da_dataset_model_column.id IS 'ID';
COMMENT ON COLUMN t_da_dataset_model_column.create_by IS '创建人';
COMMENT ON COLUMN t_da_dataset_model_column.create_time IS '创建时间';
COMMENT ON COLUMN t_da_dataset_model_column.update_by IS '更新人';
COMMENT ON COLUMN t_da_dataset_model_column.update_time IS '更新时间';

-- 教师画像-数据集
INSERT INTO t_da_dataset (id,sjjmc,
create_by,create_time,update_by,update_time,
copy_label,primary_app_id)
SELECT 'dataset-jshx','教师画像',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'','portrait'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset
    WHERE id = 'dataset-jshx'
);

-- dataset-jshx-portrait-数据集和应用关联表
INSERT INTO t_da_dataset_app (id,sjjid,appid,
create_by,create_time,update_by,update_time)
SELECT 'dataset-jshx-portrait','dataset-jshx','portrait',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_app
    WHERE id = 'dataset-jshx-portrait'
);

-- 基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'jshx_jbxx','dataset-jshx','基本信息',NULL,1,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'jshx_jbxx'
);

-- 基本标签-TEA_SL.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'jshx_jbbq','dataset-jshx','基本标签',NULL,2,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'jshx_jbbq'
);

-- 教学标签-TEA_SL.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'jshx_jxbq','dataset-jshx','教学标签',NULL,3,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'jshx_jxbq'
);

-- 科研标签-TEA_SL.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'jshx_kybq','dataset-jshx','科研标签',NULL,4,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'jshx_kybq'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_jbxx%main-abdteajzgjbxx','dataset-jshx','jshx_jbxx','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_jbxx%main-abdteajzgjbxx'
);

-- 教师画像_基本标签_年代-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_jbbq%main-tealabel-nd','dataset-jshx','jshx_jbbq','main-tealabel-nd',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_jbbq%main-tealabel-nd'
);

-- 教师画像_基本标签_年龄段-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_jbbq%main-tealabel-nld','dataset-jshx','jshx_jbbq','main-tealabel-nld',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_jbbq%main-tealabel-nld'
);

-- 教师画像_基本标签_海外经历-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_jbbq%main-tealabel-hwjl','dataset-jshx','jshx_jbbq','main-tealabel-hwjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_jbbq%main-tealabel-hwjl'
);

-- 教师画像_基本标签_本科学历学缘-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_jbbq%main-tealabel-bkxlxy','dataset-jshx','jshx_jbbq','main-tealabel-bkxlxy',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_jbbq%main-tealabel-bkxlxy'
);

-- 教师画像_基本标签_研究生学历学缘-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_jbbq%main-tealabel-yjsxlxy','dataset-jshx','jshx_jbbq','main-tealabel-yjsxlxy',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_jbbq%main-tealabel-yjsxlxy'
);

-- 教师画像_基本标签_博士学历学缘-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_jbbq%main-tealabel-bsxlxy','dataset-jshx','jshx_jbbq','main-tealabel-bsxlxy',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_jbbq%main-tealabel-bsxlxy'
);

-- 教师画像_基本标签_学缘关系-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_jbbq%main-tealabel-xygx','dataset-jshx','jshx_jbbq','main-tealabel-xygx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_jbbq%main-tealabel-xygx'
);

-- 教师画像_基本标签_海外学习-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_jbbq%main-tealabel-hwxx','dataset-jshx','jshx_jbbq','main-tealabel-hwxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_jbbq%main-tealabel-hwxx'
);

-- 教师画像_基本标签_本校工龄-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_jbbq%main-tealabel-bxgl','dataset-jshx','jshx_jbbq','main-tealabel-bxgl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_jbbq%main-tealabel-bxgl'
);

-- 教师画像_基本标签_从业工龄-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_jbbq%main-tealabel-cygl','dataset-jshx','jshx_jbbq','main-tealabel-cygl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_jbbq%main-tealabel-cygl'
);

-- 教师画像_基本标签_教师岗位类型-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_jbbq%main-tealabel-jsgwlx','dataset-jshx','jshx_jbbq','main-tealabel-jsgwlx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_jbbq%main-tealabel-jsgwlx'
);

-- 教师画像_基本标签_海外工作-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_jbbq%main-tealabel-hwgz','dataset-jshx','jshx_jbbq','main-tealabel-hwgz',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_jbbq%main-tealabel-hwgz'
);

-- 教师画像_基本标签_双肩挑-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_jbbq%main-tealabel-sjt','dataset-jshx','jshx_jbbq','main-tealabel-sjt',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_jbbq%main-tealabel-sjt'
);

-- 教师画像_基本标签_双师型教师-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_jbbq%main-tealabel-ssxjs','dataset-jshx','jshx_jbbq','main-tealabel-ssxjs',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_jbbq%main-tealabel-ssxjs'
);

-- 教师画像_基本标签_职称在全校的水平-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_jbbq%main-tealabel-zczqxdsp','dataset-jshx','jshx_jbbq','main-tealabel-zczqxdsp',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_jbbq%main-tealabel-zczqxdsp'
);

-- 教师画像_基本标签_海外培训-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_jbbq%main-tealabel-hwpx','dataset-jshx','jshx_jbbq','main-tealabel-hwpx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_jbbq%main-tealabel-hwpx'
);

-- 教师画像_教学标签_教学名师-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_jxbq%main-tealabel-jxms','dataset-jshx','jshx_jxbq','main-tealabel-jxms',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_jxbq%main-tealabel-jxms'
);

-- 教师画像_教学标签_教改带头人-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_jxbq%main-tealabel-jgdtr','dataset-jshx','jshx_jxbq','main-tealabel-jgdtr',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_jxbq%main-tealabel-jgdtr'
);

-- 教师画像_教学标签_国家级一流课程-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_jxbq%main-tealabel-gjjylkc','dataset-jshx','jshx_jxbq','main-tealabel-gjjylkc',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_jxbq%main-tealabel-gjjylkc'
);

-- 教师画像_教学标签_省部级一流课程-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_jxbq%main-tealabel-sbjylkc','dataset-jshx','jshx_jxbq','main-tealabel-sbjylkc',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_jxbq%main-tealabel-sbjylkc'
);

-- 教师画像_教学标签_优质课程授课者-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_jxbq%main-tealabel-yzkcskz','dataset-jshx','jshx_jxbq','main-tealabel-yzkcskz',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_jxbq%main-tealabel-yzkcskz'
);

-- 教师画像_教学标签_学生好评-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_jxbq%main-tealabel-xshp','dataset-jshx','jshx_jxbq','main-tealabel-xshp',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_jxbq%main-tealabel-xshp'
);

-- 教师画像_教学标签_督导好评-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_jxbq%main-tealabel-ddhp','dataset-jshx','jshx_jxbq','main-tealabel-ddhp',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_jxbq%main-tealabel-ddhp'
);

-- 教师画像_科研标签_千人计划-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_kybq%main-tealabel-qrjh','dataset-jshx','jshx_kybq','main-tealabel-qrjh',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_kybq%main-tealabel-qrjh'
);

-- 教师画像_科研标签_国家杰出青年科学基金项目-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_kybq%main-tealabel-gjjcqnkxjjxm','dataset-jshx','jshx_kybq','main-tealabel-gjjcqnkxjjxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_kybq%main-tealabel-gjjcqnkxjjxm'
);

-- 教师画像_科研标签_长江学者奖励计划-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_kybq%main-tealabel-zjxzjljh','dataset-jshx','jshx_kybq','main-tealabel-zjxzjljh',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_kybq%main-tealabel-zjxzjljh'
);

-- 教师画像_科研标签_万人计划-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_kybq%main-tealabel-wrjh','dataset-jshx','jshx_kybq','main-tealabel-wrjh',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_kybq%main-tealabel-wrjh'
);

-- 教师画像_科研标签_青年千人计划-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_kybq%main-tealabel-qnqrjh','dataset-jshx','jshx_kybq','main-tealabel-qnqrjh',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_kybq%main-tealabel-qnqrjh'
);

-- 教师画像_科研标签_青年长江学者-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_kybq%main-tealabel-qnzjxz','dataset-jshx','jshx_kybq','main-tealabel-qnzjxz',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_kybq%main-tealabel-qnzjxz'
);

-- 教师画像_科研标签_优秀青年科学基金项目-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_kybq%main-tealabel-yxqnkxjjxm','dataset-jshx','jshx_kybq','main-tealabel-yxqnkxjjxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_kybq%main-tealabel-yxqnkxjjxm'
);

-- 教师画像_科研标签_万人青拔-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_kybq%main-tealabel-wrqb','dataset-jshx','jshx_kybq','main-tealabel-wrqb',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_kybq%main-tealabel-wrqb'
);

-- 教师画像_科研标签_高被引学者-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_kybq%main-tealabel-gbyxz','dataset-jshx','jshx_kybq','main-tealabel-gbyxz',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_kybq%main-tealabel-gbyxz'
);

-- 教师画像_科研标签_科研经费达人-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_kybq%main-tealabel-kyjfdr','dataset-jshx','jshx_kybq','main-tealabel-kyjfdr',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_kybq%main-tealabel-kyjfdr'
);

-- 教师画像_科研标签_成果高产出-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-jshx%jshx_kybq%main-tealabel-cggcc','dataset-jshx','jshx_kybq','main-tealabel-cggcc',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-jshx%jshx_kybq%main-tealabel-cggcc'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-zgh','dataset-jshx','main-abdteajzgjbxx-zgh','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-zgh'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-xm','dataset-jshx','main-abdteajzgjbxx-xm','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-xm'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-xb','dataset-jshx','main-abdteajzgjbxx-xb','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-xb'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-mz','dataset-jshx','main-abdteajzgjbxx-mz','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-mz'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-sfzjlx','dataset-jshx','main-abdteajzgjbxx-sfzjlx','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-sfzjlx'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-gatqw','dataset-jshx','main-abdteajzgjbxx-gatqw','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-gatqw'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-zjxy','dataset-jshx','main-abdteajzgjbxx-zjxy','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-zjxy'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-zzmm','dataset-jshx','main-abdteajzgjbxx-zzmm','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-zzmm'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-jg','dataset-jshx','main-abdteajzgjbxx-jg','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-jg'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-hkszs','dataset-jshx','main-abdteajzgjbxx-hkszs','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-hkszs'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-hkszds','dataset-jshx','main-abdteajzgjbxx-hkszds','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-hkszds'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-hkszd','dataset-jshx','main-abdteajzgjbxx-hkszd','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-hkszd'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-xx','dataset-jshx','main-abdteajzgjbxx-xx','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-xx'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-csrq','dataset-jshx','main-abdteajzgjbxx-csrq','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-csrq'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-csd','dataset-jshx','main-abdteajzgjbxx-csd','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-csd'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-gjdq','dataset-jshx','main-abdteajzgjbxx-gjdq','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-gjdq'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-hyzk','dataset-jshx','main-abdteajzgjbxx-hyzk','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-hyzk'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-zgxl','dataset-jshx','main-abdteajzgjbxx-zgxl','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-zgxl'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-zgxw','dataset-jshx','main-abdteajzgjbxx-zgxw','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-zgxw'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-dyxl','dataset-jshx','main-abdteajzgjbxx-dyxl','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-dyxl'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-dyxlbyyx','dataset-jshx','main-abdteajzgjbxx-dyxlbyyx','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-dyxlbyyx'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-dyxlbyyxlx','dataset-jshx','main-abdteajzgjbxx-dyxlbyyxlx','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-dyxlbyyxlx'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-zgxlbyyx','dataset-jshx','main-abdteajzgjbxx-zgxlbyyx','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-zgxlbyyx'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-zgxlbyyxlx','dataset-jshx','main-abdteajzgjbxx-zgxlbyyxlx','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-zgxlbyyxlx'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-zhbyyx','dataset-jshx','main-abdteajzgjbxx-zhbyyx','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-zhbyyx'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-zhbyyxlx','dataset-jshx','main-abdteajzgjbxx-zhbyyxlx','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-zhbyyxlx'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-sfbxby','dataset-jshx','main-abdteajzgjbxx-sfbxby','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-sfbxby'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-sjhm','dataset-jshx','main-abdteajzgjbxx-sjhm','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-sjhm'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-dzyx','dataset-jshx','main-abdteajzgjbxx-dzyx','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-dzyx'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-jtdz','dataset-jshx','main-abdteajzgjbxx-jtdz','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-jtdz'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-ssjgdm','dataset-jshx','main-abdteajzgjbxx-ssjgdm','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-ssjgdm'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-ssjg','dataset-jshx','main-abdteajzgjbxx-ssjg','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-ssjg'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-ssxdm','dataset-jshx','main-abdteajzgjbxx-ssxdm','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-ssxdm'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-ssx','dataset-jshx','main-abdteajzgjbxx-ssx','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-ssx'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-jzglb','dataset-jshx','main-abdteajzgjbxx-jzglb','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-jzglb'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-jzgly','dataset-jshx','main-abdteajzgjbxx-jzgly','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-jzgly'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-bzlb','dataset-jshx','main-abdteajzgjbxx-bzlb','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-bzlb'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-yrfs','dataset-jshx','main-abdteajzgjbxx-yrfs','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-yrfs'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-cjny','dataset-jshx','main-abdteajzgjbxx-cjny','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-cjny'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-lxny','dataset-jshx','main-abdteajzgjbxx-lxny','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-lxny'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-jzjsprlb','dataset-jshx','main-abdteajzgjbxx-jzjsprlb','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-jzjsprlb'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-dslb','dataset-jshx','main-abdteajzgjbxx-dslb','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-dslb'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-fdylb','dataset-jshx','main-abdteajzgjbxx-fdylb','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-fdylb'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-sfssxjs','dataset-jshx','main-abdteajzgjbxx-sfssxjs','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-sfssxjs'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-sfsjt','dataset-jshx','main-abdteajzgjbxx-sfsjt','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-sfsjt'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-xkml','dataset-jshx','main-abdteajzgjbxx-xkml','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-xkml'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-yjxk','dataset-jshx','main-abdteajzgjbxx-yjxk','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-yjxk'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-ejxk','dataset-jshx','main-abdteajzgjbxx-ejxk','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-ejxk'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-yjfx','dataset-jshx','main-abdteajzgjbxx-yjfx','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-yjfx'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-jzgdqzt','dataset-jshx','main-abdteajzgjbxx-jzgdqzt','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-jzgdqzt'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-lxrq','dataset-jshx','main-abdteajzgjbxx-lxrq','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-lxrq'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-yjtxrq','dataset-jshx','main-abdteajzgjbxx-yjtxrq','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-yjtxrq'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-zyjszw','dataset-jshx','main-abdteajzgjbxx-zyjszw','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-zyjszw'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-zyjszwjb','dataset-jshx','main-abdteajzgjbxx-zyjszwjb','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-zyjszwjb'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-zyjsgwdj','dataset-jshx','main-abdteajzgjbxx-zyjsgwdj','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-zyjsgwdj'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-glgwdj','dataset-jshx','main-abdteajzgjbxx-glgwdj','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-glgwdj'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-gqgwdj','dataset-jshx','main-abdteajzgjbxx-gqgwdj','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-gqgwdj'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-zygwlx','dataset-jshx','main-abdteajzgjbxx-zygwlx','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-zygwlx'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-gwmc','dataset-jshx','main-abdteajzgjbxx-gwmc','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-gwmc'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-gbzw','dataset-jshx','main-abdteajzgjbxx-gbzw','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-gbzw'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-gbzwjb','dataset-jshx','main-abdteajzgjbxx-gbzwjb','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-gbzwjb'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-nl','dataset-jshx','main-abdteajzgjbxx-nl','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-nl'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-rjlx','dataset-jshx','main-abdteajzgjbxx-rjlx','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-rjlx'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-rjzymc','dataset-jshx','main-abdteajzgjbxx-rjzymc','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-rjzymc'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-rjzydm','dataset-jshx','main-abdteajzgjbxx-rjzydm','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-rjzydm'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-zyrjsj','dataset-jshx','main-abdteajzgjbxx-zyrjsj','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-zyrjsj'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-sfsyjsry','dataset-jshx','main-abdteajzgjbxx-sfsyjsry','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-sfsyjsry'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-sfwp','dataset-jshx','main-abdteajzgjbxx-sfwp','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-sfwp'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-glrylb','dataset-jshx','main-abdteajzgjbxx-glrylb','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-glrylb'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-sfbds','dataset-jshx','main-abdteajzgjbxx-sfbds','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-sfbds'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-sfskjs','dataset-jshx','main-abdteajzgjbxx-sfskjs','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-sfskjs'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-sfszkjs','dataset-jshx','main-abdteajzgjbxx-sfszkjs','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-sfszkjs'
);

-- 教师画像_基本信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-jshx%main-abdteajzgjbxx-kzrq','dataset-jshx','main-abdteajzgjbxx-kzrq','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-jshx%main-abdteajzgjbxx-kzrq'
);