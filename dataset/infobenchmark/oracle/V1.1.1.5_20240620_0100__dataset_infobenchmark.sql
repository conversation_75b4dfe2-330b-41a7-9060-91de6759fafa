/*
 Description		: [信息化标杆监测(infobenchmark)]应用数据集
 Author				: dataapp
 Date				: 2024-06-20 01:00:00
 Lowcode Version	: V1.1.1_Beta5
 Database           : oracle
*/

-- 数据集模型字段
BEGIN
    EXECUTE IMMEDIATE 'CREATE TABLE t_da_dataset_model_column ( dataset_id VARCHAR2(50) NOT NULL,
column_id VARCHAR2(50) NOT NULL,
model_id VARCHAR2(50) NOT NULL,
id VARCHAR2(100),
create_by VARCHAR2(100),
create_time DATE,
update_by VARCHAR2(100),
update_time DATE,PRIMARY KEY (id) )';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

COMMENT ON TABLE t_da_dataset_model_column IS '数据集模型字段';
COMMENT ON COLUMN t_da_dataset_model_column.dataset_id IS '数据集ID';
COMMENT ON COLUMN t_da_dataset_model_column.column_id IS '字段ID';
COMMENT ON COLUMN t_da_dataset_model_column.model_id IS '模型ID';
COMMENT ON COLUMN t_da_dataset_model_column.id IS 'ID';
COMMENT ON COLUMN t_da_dataset_model_column.create_by IS '创建人';
COMMENT ON COLUMN t_da_dataset_model_column.create_time IS '创建时间';
COMMENT ON COLUMN t_da_dataset_model_column.update_by IS '更新人';
COMMENT ON COLUMN t_da_dataset_model_column.update_time IS '更新时间';

-- 信息化标杆监测-数据集
INSERT INTO t_da_dataset (id,sjjmc,
create_by,create_time,update_by,update_time,
copy_label,primary_app_id)
SELECT 'dataset-xxhbgjc','信息化标杆监测',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'','infobenchmark'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset
    WHERE id = 'dataset-xxhbgjc'
);

-- dataset-xxhbgjc-infobenchmark-数据集和应用关联表
INSERT INTO t_da_dataset_app (id,sjjid,appid,
create_by,create_time,update_by,update_time)
SELECT 'dataset-xxhbgjc-infobenchmark','dataset-xxhbgjc','infobenchmark',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_app
    WHERE id = 'dataset-xxhbgjc-infobenchmark'
);

-- 学校概况数据子集-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_xxgksjzj','dataset-xxhbgjc','学校概况数据子集',NULL,1,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_xxgksjzj'
);

-- 学校基本数据类-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_xxgksjzj_xxjbsjl','dataset-xxhbgjc','学校基本数据类','xxhbgjc_xxgksjzj',2,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_xxgksjzj_xxjbsjl'
);

-- 教学管理数据子集-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jxglsjzj','dataset-xxhbgjc','教学管理数据子集',NULL,3,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jxglsjzj'
);

-- 课程数据类-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jxglsjzj_kcsjl','dataset-xxhbgjc','课程数据类','xxhbgjc_jxglsjzj',4,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jxglsjzj_kcsjl'
);

-- 专业信息数据类-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jxglsjzj_zyxxsjl','dataset-xxhbgjc','专业信息数据类','xxhbgjc_jxglsjzj',5,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jxglsjzj_zyxxsjl'
);

-- 实训管理数据类-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jxglsjzj_sxglsjl','dataset-xxhbgjc','实训管理数据类','xxhbgjc_jxglsjzj',6,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jxglsjzj_sxglsjl'
);

-- 排课选课数据类-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jxglsjzj_pkxksjl','dataset-xxhbgjc','排课选课数据类','xxhbgjc_jxglsjzj',7,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jxglsjzj_pkxksjl'
);

-- 课程教学数据类-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jxglsjzj_kcjxsjl','dataset-xxhbgjc','课程教学数据类','xxhbgjc_jxglsjzj',8,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jxglsjzj_kcjxsjl'
);

-- 教学质量与评价数据类-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jxglsjzj_jxzlypjsjl','dataset-xxhbgjc','教学质量与评价数据类','xxhbgjc_jxglsjzj',9,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jxglsjzj_jxzlypjsjl'
);

-- 数字资源数据类-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jxglsjzj_szzysjl','dataset-xxhbgjc','数字资源数据类','xxhbgjc_jxglsjzj',10,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jxglsjzj_szzysjl'
);

-- 产学合作数据类-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jxglsjzj_cxhzsjl','dataset-xxhbgjc','产学合作数据类','xxhbgjc_jxglsjzj',11,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jxglsjzj_cxhzsjl'
);

-- 教学计划数据类-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jxglsjzj_jxjhsjl','dataset-xxhbgjc','教学计划数据类','xxhbgjc_jxglsjzj',12,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jxglsjzj_jxjhsjl'
);

-- 教材数据类-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jxglsjzj_jcsjl','dataset-xxhbgjc','教材数据类','xxhbgjc_jxglsjzj',13,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jxglsjzj_jcsjl'
);

-- 教室管理数据类-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jxglsjzj_jsglsjl','dataset-xxhbgjc','教室管理数据类','xxhbgjc_jxglsjzj',14,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jxglsjzj_jsglsjl'
);

-- 岗位实习数据类-UND_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jxglsjzj_gwsxsjl','dataset-xxhbgjc','岗位实习数据类','xxhbgjc_jxglsjzj',15,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jxglsjzj_gwsxsjl'
);

-- 学生管理数据子集-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_xsglsjzj','dataset-xxhbgjc','学生管理数据子集',NULL,16,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_xsglsjzj'
);

-- 社团（协会）辅助数据类-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_xsglsjzj_st（xh）fzsjl','dataset-xxhbgjc','社团（协会）辅助数据类','xxhbgjc_xsglsjzj',17,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_xsglsjzj_st（xh）fzsjl'
);

-- 学生基本数据类-UND_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_xsglsjzj_xsjbsjl','dataset-xxhbgjc','学生基本数据类','xxhbgjc_xsglsjzj',18,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_xsglsjzj_xsjbsjl'
);

-- 经济资助数据类-UND_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_xsglsjzj_jjzzsjl','dataset-xxhbgjc','经济资助数据类','xxhbgjc_xsglsjzj',19,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_xsglsjzj_jjzzsjl'
);

-- 生活健康数据类-UND_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_xsglsjzj_shjksjl','dataset-xxhbgjc','生活健康数据类','xxhbgjc_xsglsjzj',20,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_xsglsjzj_shjksjl'
);

-- 毕业相关数据类-UND_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_xsglsjzj_byxgsjl','dataset-xxhbgjc','毕业相关数据类','xxhbgjc_xsglsjzj',21,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_xsglsjzj_byxgsjl'
);

-- 党建思政数据子集-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_djszsjzj','dataset-xxhbgjc','党建思政数据子集',NULL,22,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_djszsjzj'
);

-- 党建基础数据类-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_djszsjzj_djjcsjl','dataset-xxhbgjc','党建基础数据类','xxhbgjc_djszsjzj',23,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_djszsjzj_djjcsjl'
);

-- 党建活动数据类-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_djszsjzj_djhdsjl','dataset-xxhbgjc','党建活动数据类','xxhbgjc_djszsjzj',24,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_djszsjzj_djhdsjl'
);

-- 思政活动数据类-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_djszsjzj_szhdsjl','dataset-xxhbgjc','思政活动数据类','xxhbgjc_djszsjzj',25,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_djszsjzj_szhdsjl'
);

-- 资产与设备管理数据子集-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_zcysbglsjzj','dataset-xxhbgjc','资产与设备管理数据子集',NULL,26,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_zcysbglsjzj'
);

-- 仪器设备管理数据类-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_zcysbglsjzj_yqsbglsjl','dataset-xxhbgjc','仪器设备管理数据类','xxhbgjc_zcysbglsjzj',27,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_zcysbglsjzj_yqsbglsjl'
);

-- 科研管理数据子集-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_kyglsjzj','dataset-xxhbgjc','科研管理数据子集',NULL,28,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_kyglsjzj'
);

-- 科研项目数据类-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_kyglsjzj_kyxmsjl','dataset-xxhbgjc','科研项目数据类','xxhbgjc_kyglsjzj',29,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_kyglsjzj_kyxmsjl'
);

-- 科研成果数据类-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_kyglsjzj_kycgsjl','dataset-xxhbgjc','科研成果数据类','xxhbgjc_kyglsjzj',30,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_kyglsjzj_kycgsjl'
);

-- 学术交流数据类-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_kyglsjzj_xsjlsjl','dataset-xxhbgjc','学术交流数据类','xxhbgjc_kyglsjzj',31,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_kyglsjzj_xsjlsjl'
);

-- 服务管理数据子集-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_fwglsjzj','dataset-xxhbgjc','服务管理数据子集',NULL,32,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_fwglsjzj'
);

-- 认证消费数据类-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_fwglsjzj_rzxfsjl','dataset-xxhbgjc','认证消费数据类','xxhbgjc_fwglsjzj',33,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_fwglsjzj_rzxfsjl'
);

-- 在线事务办理数据类-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_fwglsjzj_zxswblsjl','dataset-xxhbgjc','在线事务办理数据类','xxhbgjc_fwglsjzj',34,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_fwglsjzj_zxswblsjl'
);

-- 信息服务数据类-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_fwglsjzj_xxfwsjl','dataset-xxhbgjc','信息服务数据类','xxhbgjc_fwglsjzj',35,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_fwglsjzj_xxfwsjl'
);

-- 图书期刊数据类-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_fwglsjzj_tsqksjl','dataset-xxhbgjc','图书期刊数据类','xxhbgjc_fwglsjzj',36,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_fwglsjzj_tsqksjl'
);

-- 校内赛事活动数据类-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_fwglsjzj_xnsshdsjl','dataset-xxhbgjc','校内赛事活动数据类','xxhbgjc_fwglsjzj',37,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_fwglsjzj_xnsshdsjl'
);

-- 网络安全管理数据类-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_fwglsjzj_wlaqglsjl','dataset-xxhbgjc','网络安全管理数据类','xxhbgjc_fwglsjzj',38,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_fwglsjzj_wlaqglsjl'
);

-- 数据集成情况数据类-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_fwglsjzj_sjjcqksjl','dataset-xxhbgjc','数据集成情况数据类','xxhbgjc_fwglsjzj',39,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_fwglsjzj_sjjcqksjl'
);

-- 信息化系统集成认证数据类-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_fwglsjzj_xxhxtjcrzsjl','dataset-xxhbgjc','信息化系统集成认证数据类','xxhbgjc_fwglsjzj',40,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_fwglsjzj_xxhxtjcrzsjl'
);

-- 补采数据子集-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_bcsjzj','dataset-xxhbgjc','补采数据子集',NULL,41,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_bcsjzj'
);

-- 信息化系统集成认证数据类-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_bcsjzj_xxhxtjcrzsjl','dataset-xxhbgjc','信息化系统集成认证数据类','xxhbgjc_bcsjzj',42,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_bcsjzj_xxhxtjcrzsjl'
);

-- 师生发展-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_ssfz','dataset-xxhbgjc','师生发展',NULL,43,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_ssfz'
);

-- 学生发展-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_ssfz_xsfz','dataset-xxhbgjc','学生发展','xxhbgjc_ssfz',44,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_ssfz_xsfz'
);

-- 学生了解培养过程-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_ssfz_xsfz_xsljpygc','dataset-xxhbgjc','学生了解培养过程','xxhbgjc_ssfz_xsfz',45,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_ssfz_xsfz_xsljpygc'
);

-- 学生获取就业服务-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_ssfz_xsfz_xshqjyfw','dataset-xxhbgjc','学生获取就业服务','xxhbgjc_ssfz_xsfz',46,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_ssfz_xsfz_xshqjyfw'
);

-- 学生查看个人画像-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_ssfz_xsfz_xsckgrhx','dataset-xxhbgjc','学生查看个人画像','xxhbgjc_ssfz_xsfz',47,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_ssfz_xsfz_xsckgrhx'
);

-- 教师发展-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_ssfz_jsfz','dataset-xxhbgjc','教师发展','xxhbgjc_ssfz',48,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_ssfz_jsfz'
);

-- 教师参与发展活动-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_ssfz_jsfz_jscyfzhd','dataset-xxhbgjc','教师参与发展活动','xxhbgjc_ssfz_jsfz',49,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_ssfz_jsfz_jscyfzhd'
);

-- 教师参与校企合作活动-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_ssfz_jsfz_jscyxqhzhd','dataset-xxhbgjc','教师参与校企合作活动','xxhbgjc_ssfz_jsfz',50,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_ssfz_jsfz_jscyxqhzhd'
);

-- 教师查看个人画像-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_ssfz_jsfz_jsckgrhx','dataset-xxhbgjc','教师查看个人画像','xxhbgjc_ssfz_jsfz',51,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_ssfz_jsfz_jsckgrhx'
);

-- 教育教学-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jyjx','dataset-xxhbgjc','教育教学',NULL,52,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jyjx'
);

-- 产教融合办学-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jyjx_cjrhbx','dataset-xxhbgjc','产教融合办学','xxhbgjc_jyjx',53,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jyjx_cjrhbx'
);

-- 专业改造-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jyjx_cjrhbx_zygz','dataset-xxhbgjc','专业改造','xxhbgjc_jyjx_cjrhbx',54,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jyjx_cjrhbx_zygz'
);

-- 专业设置与调整-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jyjx_cjrhbx_zyszytz','dataset-xxhbgjc','专业设置与调整','xxhbgjc_jyjx_cjrhbx',55,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jyjx_cjrhbx_zyszytz'
);

-- 学生学习-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jyjx_xsxx','dataset-xxhbgjc','学生学习','xxhbgjc_jyjx',56,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jyjx_xsxx'
);

-- 学生思政学习-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jyjx_xsxx_xsszxx','dataset-xxhbgjc','学生思政学习','xxhbgjc_jyjx_xsxx',57,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jyjx_xsxx_xsszxx'
);

-- 学生课程学习-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jyjx_xsxx_xskcxx','dataset-xxhbgjc','学生课程学习','xxhbgjc_jyjx_xsxx',58,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jyjx_xsxx_xskcxx'
);

-- 学生进行实验实训-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jyjx_xsxx_xsjxsysx','dataset-xxhbgjc','学生进行实验实训','xxhbgjc_jyjx_xsxx',59,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jyjx_xsxx_xsjxsysx'
);

-- 学生进行岗位实习-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jyjx_xsxx_xsjxgwsx','dataset-xxhbgjc','学生进行岗位实习','xxhbgjc_jyjx_xsxx',60,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jyjx_xsxx_xsjxgwsx'
);

-- 学生参与实践活动-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jyjx_xsxx_xscysjhd','dataset-xxhbgjc','学生参与实践活动','xxhbgjc_jyjx_xsxx',61,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jyjx_xsxx_xscysjhd'
);

-- 学生处理学习事务-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jyjx_xsxx_xsclxxsw','dataset-xxhbgjc','学生处理学习事务','xxhbgjc_jyjx_xsxx',62,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jyjx_xsxx_xsclxxsw'
);

-- 教师教学-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jyjx_jsjx','dataset-xxhbgjc','教师教学','xxhbgjc_jyjx',63,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jyjx_jsjx'
);

-- 教师思政教学-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jyjx_jsjx_jsszjx','dataset-xxhbgjc','教师思政教学','xxhbgjc_jyjx_jsjx',64,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jyjx_jsjx_jsszjx'
);

-- 教师课程教学-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jyjx_jsjx_jskcjx','dataset-xxhbgjc','教师课程教学','xxhbgjc_jyjx_jsjx',65,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jyjx_jsjx_jskcjx'
);

-- 教师实训教学-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jyjx_jsjx_jssxjx','dataset-xxhbgjc','教师实训教学','xxhbgjc_jyjx_jsjx',66,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jyjx_jsjx_jssxjx'
);

-- 教师开发与应用数字资源-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jyjx_jsjx_jskfyyyszzy','dataset-xxhbgjc','教师开发与应用数字资源','xxhbgjc_jyjx_jsjx',67,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jyjx_jsjx_jskfyyyszzy'
);

-- 教师获取教学评价信息-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jyjx_jsjx_jshqjxpjxx','dataset-xxhbgjc','教师获取教学评价信息','xxhbgjc_jyjx_jsjx',68,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jyjx_jsjx_jshqjxpjxx'
);

-- 管理服务-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_glfw','dataset-xxhbgjc','管理服务',NULL,69,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_glfw'
);

-- 学生生活-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_glfw_xssh','dataset-xxhbgjc','学生生活','xxhbgjc_glfw',70,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_glfw_xssh'
);

-- 学生离校-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_glfw_xssh_xslx','dataset-xxhbgjc','学生离校','xxhbgjc_glfw_xssh',71,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_glfw_xssh_xslx'
);

-- 学生心理咨询-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_glfw_xssh_xsxlzx','dataset-xxhbgjc','学生心理咨询','xxhbgjc_glfw_xssh',72,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_glfw_xssh_xsxlzx'
);

-- 学生申请奖助贷补-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_glfw_xssh_xssqjzdb','dataset-xxhbgjc','学生申请奖助贷补','xxhbgjc_glfw_xssh',73,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_glfw_xssh_xssqjzdb'
);

-- 教师事务-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_glfw_jssw','dataset-xxhbgjc','教师事务','xxhbgjc_glfw',74,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_glfw_jssw'
);

-- 教师参与人事考核-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_glfw_jssw_jscyrskh','dataset-xxhbgjc','教师参与人事考核','xxhbgjc_glfw_jssw',75,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_glfw_jssw_jscyrskh'
);

-- 教师获取财务与采购信息-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_glfw_jssw_jshqcwycgxx','dataset-xxhbgjc','教师获取财务与采购信息','xxhbgjc_glfw_jssw',76,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_glfw_jssw_jshqcwycgxx'
);

-- 教师获取资产信息-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_glfw_jssw_jshqzcxx','dataset-xxhbgjc','教师获取资产信息','xxhbgjc_glfw_jssw',77,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_glfw_jssw_jshqzcxx'
);

-- 信息化管理-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_glfw_xxhgl','dataset-xxhbgjc','信息化管理','xxhbgjc_glfw',78,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_glfw_xxhgl'
);

-- 统一身份认证-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_glfw_xxhgl_tysfrz','dataset-xxhbgjc','统一身份认证','xxhbgjc_glfw_xxhgl',79,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_glfw_xxhgl_tysfrz'
);

-- 数据治理-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_glfw_xxhgl_sjzl','dataset-xxhbgjc','数据治理','xxhbgjc_glfw_xxhgl',80,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_glfw_xxhgl_sjzl'
);

-- 教学督导-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_glfw_xxhgl_jxdd','dataset-xxhbgjc','教学督导','xxhbgjc_glfw_xxhgl',81,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_glfw_xxhgl_jxdd'
);

-- 信息化成果管理-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_glfw_xxhgl_xxhcggl','dataset-xxhbgjc','信息化成果管理','xxhbgjc_glfw_xxhgl',82,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_glfw_xxhgl_xxhcggl'
);

-- 公共服务-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_glfw_ggfw','dataset-xxhbgjc','公共服务','xxhbgjc_glfw',83,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_glfw_ggfw'
);

-- 一卡通服务-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_glfw_ggfw_yktfw','dataset-xxhbgjc','一卡通服务','xxhbgjc_glfw_ggfw',84,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_glfw_ggfw_yktfw'
);

-- 一站式服务大厅-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_glfw_ggfw_yzsfwdt','dataset-xxhbgjc','一站式服务大厅','xxhbgjc_glfw_ggfw',85,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_glfw_ggfw_yzsfwdt'
);

-- 信息发布-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_glfw_ggfw_xxfb','dataset-xxhbgjc','信息发布','xxhbgjc_glfw_ggfw',86,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_glfw_ggfw_xxfb'
);

-- 校园服务-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_glfw_xyfw','dataset-xxhbgjc','校园服务','xxhbgjc_glfw',87,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_glfw_xyfw'
);

-- 故障报修-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_glfw_xyfw_gzbx','dataset-xxhbgjc','故障报修','xxhbgjc_glfw_xyfw',88,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_glfw_xyfw_gzbx'
);

-- 服务预约-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_glfw_xyfw_fwyy','dataset-xxhbgjc','服务预约','xxhbgjc_glfw_xyfw',89,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_glfw_xyfw_fwyy'
);

-- 支撑条件与网络安全-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_zctjywlaq','dataset-xxhbgjc','支撑条件与网络安全',NULL,90,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_zctjywlaq'
);

-- 校园管理-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_zctjywlaq_xygl','dataset-xxhbgjc','校园管理','xxhbgjc_zctjywlaq',91,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_zctjywlaq_xygl'
);

-- 校园网络管理-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_zctjywlaq_xygl_xywlgl','dataset-xxhbgjc','校园网络管理','xxhbgjc_zctjywlaq_xygl',92,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_zctjywlaq_xygl_xywlgl'
);

-- 教学环境管理-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_zctjywlaq_xygl_jxhjgl','dataset-xxhbgjc','教学环境管理','xxhbgjc_zctjywlaq_xygl',93,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_zctjywlaq_xygl_jxhjgl'
);

-- 校园安全及能源管理-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_zctjywlaq_xygl_xyaqjnygl','dataset-xxhbgjc','校园安全及能源管理','xxhbgjc_zctjywlaq_xygl',94,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_zctjywlaq_xygl_xyaqjnygl'
);

-- 参考指标-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_ckzb','dataset-xxhbgjc','参考指标',NULL,95,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_ckzb'
);

-- 教职工管理数据子集-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jzgglsjzj','dataset-xxhbgjc','教职工管理数据子集',NULL,96,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jzgglsjzj'
);

-- 教师基本数据类-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jzgglsjzj_jsjbsjl','dataset-xxhbgjc','教师基本数据类','xxhbgjc_jzgglsjzj',97,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jzgglsjzj_jsjbsjl'
);

-- 学习进修数据类-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jzgglsjzj_xxjxsjl','dataset-xxhbgjc','学习进修数据类','xxhbgjc_jzgglsjzj',98,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jzgglsjzj_xxjxsjl'
);

-- 教师考核数据类-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'xxhbgjc_jzgglsjzj_jskhsjl','dataset-xxhbgjc','教师考核数据类','xxhbgjc_jzgglsjzj',99,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'xxhbgjc_jzgglsjzj_jskhsjl'
);

-- 信息化标杆监测_学校概况数据子集_学校基本数据类_学校基本数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_xxgksjzj_xxjbsjl%main-abdschxxjbsj','dataset-xxhbgjc','xxhbgjc_xxgksjzj_xxjbsjl','main-abdschxxjbsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_xxgksjzj_xxjbsjl%main-abdschxxjbsj'
);

-- 信息化标杆监测_学校概况数据子集_学校基本数据类_信息化系统建设数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_xxgksjzj_xxjbsjl%main-abdschxxhxtjssj','dataset-xxhbgjc','xxhbgjc_xxgksjzj_xxjbsjl','main-abdschxxhxtjssj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_xxgksjzj_xxjbsjl%main-abdschxxhxtjssj'
);

-- 信息化标杆监测_学校概况数据子集_学校基本数据类_信息化系统访问记录数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_xxgksjzj_xxjbsjl%main-abdschxxhxtfwjlsj','dataset-xxhbgjc','xxhbgjc_xxgksjzj_xxjbsjl','main-abdschxxhxtfwjlsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_xxgksjzj_xxjbsjl%main-abdschxxhxtfwjlsj'
);

-- 信息化标杆监测_学校概况数据子集_学校基本数据类_学校特色信息化系统应用数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_xxgksjzj_xxjbsjl%main-abdschxxtsxxhxtyysj','dataset-xxhbgjc','xxhbgjc_xxgksjzj_xxjbsjl','main-abdschxxtsxxhxtyysj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_xxgksjzj_xxjbsjl%main-abdschxxtsxxhxtyysj'
);

-- 信息化标杆监测_教学管理数据子集_课程数据类_课程基本数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_kcsjl%main-abdschkcjbsj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_kcsjl','main-abdschkcjbsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_kcsjl%main-abdschkcjbsj'
);

-- 信息化标杆监测_教学管理数据子集_课程数据类_课程建设数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_kcsjl%main-abdschkcjssj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_kcsjl','main-abdschkcjssj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_kcsjl%main-abdschkcjssj'
);

-- 信息化标杆监测_教学管理数据子集_课程数据类_国家平台资源对接数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_kcsjl%main-abdschgjptzydjsj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_kcsjl','main-abdschgjptzydjsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_kcsjl%main-abdschgjptzydjsj'
);

-- 信息化标杆监测_教学管理数据子集_专业信息数据类_专业建设情况数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_zyxxsjl%main-abdschzyjsqksj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_zyxxsjl','main-abdschzyjsqksj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_zyxxsjl%main-abdschzyjsqksj'
);

-- 信息化标杆监测_教学管理数据子集_专业信息数据类_专业设置数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_zyxxsjl%main-abdschzyszsj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_zyxxsjl','main-abdschzyszsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_zyxxsjl%main-abdschzyszsj'
);

-- 信息化标杆监测_教学管理数据子集_实训管理数据类_实训基地数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_sxglsjl%main-abdschsxjdsj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_sxglsjl','main-abdschsxjdsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_sxglsjl%main-abdschsxjdsj'
);

-- 信息化标杆监测_教学管理数据子集_实训管理数据类_实训室基本数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_sxglsjl%main-abdschsxsjbsj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_sxglsjl','main-abdschsxsjbsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_sxglsjl%main-abdschsxsjbsj'
);

-- 信息化标杆监测_教学管理数据子集_实训管理数据类_实训项目数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_sxglsjl%main-abdschsxxmsj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_sxglsjl','main-abdschsxxmsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_sxglsjl%main-abdschsxxmsj'
);

-- 信息化标杆监测_教学管理数据子集_实训管理数据类_实训教学过程数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_sxglsjl%main-abdschsxjxgcsj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_sxglsjl','main-abdschsxjxgcsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_sxglsjl%main-abdschsxjxgcsj'
);

-- 信息化标杆监测_教学管理数据子集_实训管理数据类_虚拟仿真基地对外服务数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_sxglsjl%main-abdschxnfzjddwfwsj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_sxglsjl','main-abdschxnfzjddwfwsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_sxglsjl%main-abdschxnfzjddwfwsj'
);

-- 信息化标杆监测_教学管理数据子集_排课选课数据类_排课数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_pkxksjl%main-abdschpksj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_pkxksjl','main-abdschpksj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_pkxksjl%main-abdschpksj'
);

-- 信息化标杆监测_教学管理数据子集_排课选课数据类_学生选课数据-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_pkxksjl%main-abdundxsxksj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_pkxksjl','main-abdundxsxksj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_pkxksjl%main-abdundxsxksj'
);

-- 信息化标杆监测_教学管理数据子集_课程教学数据类_访问教学资源情况数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_kcjxsjl%main-abdschfwjxzyqksj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_kcjxsjl','main-abdschfwjxzyqksj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_kcjxsjl%main-abdschfwjxzyqksj'
);

-- 信息化标杆监测_教学管理数据子集_课程教学数据类_网络课程在线学习记录数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_kcjxsjl%main-abdschwlkczxxxjlsj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_kcjxsjl','main-abdschwlkczxxxjlsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_kcjxsjl%main-abdschwlkczxxxjlsj'
);

-- 信息化标杆监测_教学管理数据子集_课程教学数据类_课堂互动数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_kcjxsjl%main-abdschkthdsj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_kcjxsjl','main-abdschkthdsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_kcjxsjl%main-abdschkthdsj'
);

-- 信息化标杆监测_教学管理数据子集_课程教学数据类_访问数字图书馆资源数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_kcjxsjl%main-abdschfwsztsgzysj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_kcjxsjl','main-abdschfwsztsgzysj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_kcjxsjl%main-abdschfwsztsgzysj'
);

-- 信息化标杆监测_教学管理数据子集_教学质量与评价数据类_教学质量与评价数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_jxzlypjsjl%main-abdschjxzlypjsj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_jxzlypjsjl','main-abdschjxzlypjsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_jxzlypjsjl%main-abdschjxzlypjsj'
);

-- 信息化标杆监测_教学管理数据子集_教学质量与评价数据类_巡课数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_jxzlypjsjl%main-abdschxksj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_jxzlypjsjl','main-abdschxksj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_jxzlypjsjl%main-abdschxksj'
);

-- 信息化标杆监测_教学管理数据子集_数字资源数据类_数字资源基本数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_szzysjl%main-abdschszzyjbsj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_szzysjl','main-abdschszzyjbsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_szzysjl%main-abdschszzyjbsj'
);

-- 信息化标杆监测_教学管理数据子集_数字资源数据类_虚拟仿真数字资源基本数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_szzysjl%main-abdschxnfzszzyjbsj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_szzysjl','main-abdschxnfzszzyjbsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_szzysjl%main-abdschxnfzszzyjbsj'
);

-- 信息化标杆监测_教学管理数据子集_产学合作数据类_产学合作数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_cxhzsjl%main-abdschcxhzsj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_cxhzsjl','main-abdschcxhzsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_cxhzsjl%main-abdschcxhzsj'
);

-- 信息化标杆监测_教学管理数据子集_产学合作数据类_1+X 证书数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_cxhzsjl%main-abdschgzxzssj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_cxhzsjl','main-abdschgzxzssj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_cxhzsjl%main-abdschgzxzssj'
);

-- 信息化标杆监测_教学管理数据子集_产学合作数据类_职业技能鉴定机构数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_cxhzsjl%main-abdschzyjnjdjgsj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_cxhzsjl','main-abdschzyjnjdjgsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_cxhzsjl%main-abdschzyjnjdjgsj'
);

-- 信息化标杆监测_教学管理数据子集_教学计划数据类_总体计划数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_jxjhsjl%main-abdschztjhsj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_jxjhsjl','main-abdschztjhsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_jxjhsjl%main-abdschztjhsj'
);

-- 信息化标杆监测_教学管理数据子集_教学计划数据类_计划课程数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_jxjhsjl%main-abdschjhkcsj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_jxjhsjl','main-abdschjhkcsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_jxjhsjl%main-abdschjhkcsj'
);

-- 信息化标杆监测_教学管理数据子集_教材数据类_教材基本数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_jcsjl%main-abdschjcjbsj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_jcsjl','main-abdschjcjbsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_jcsjl%main-abdschjcjbsj'
);

-- 信息化标杆监测_教学管理数据子集_教材数据类_获奖教材数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_jcsjl%main-abdschhjjcsj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_jcsjl','main-abdschhjjcsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_jcsjl%main-abdschhjjcsj'
);

-- 信息化标杆监测_教学管理数据子集_教室管理数据类_教室基本数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_jsglsjl%main-abdschjsjbsj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_jsglsjl','main-abdschjsjbsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_jsglsjl%main-abdschjsjbsj'
);

-- 信息化标杆监测_教学管理数据子集_教室管理数据类_教室视频流数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_jsglsjl%main-abdschjssplsj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_jsglsjl','main-abdschjssplsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_jsglsjl%main-abdschjssplsj'
);

-- 信息化标杆监测_教学管理数据子集_岗位实习数据类_实习基础数据-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_gwsxsjl%main-abdundsxjcsj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_gwsxsjl','main-abdundsxjcsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_gwsxsjl%main-abdundsxjcsj'
);

-- 信息化标杆监测_教学管理数据子集_岗位实习数据类_实习保险购买情况数据-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_gwsxsjl%main-abdundsxbxgmqksj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_gwsxsjl','main-abdundsxbxgmqksj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_gwsxsjl%main-abdundsxbxgmqksj'
);

-- 信息化标杆监测_教学管理数据子集_岗位实习数据类_实习违规行为情况数据-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_gwsxsjl%main-abdundsxwgxwqksj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_gwsxsjl','main-abdundsxwgxwqksj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_gwsxsjl%main-abdundsxwgxwqksj'
);

-- 信息化标杆监测_教学管理数据子集_岗位实习数据类_实习报告记录数据-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_gwsxsjl%main-abdundsxbgjlsj','dataset-xxhbgjc','xxhbgjc_jxglsjzj_gwsxsjl','main-abdundsxbgjlsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jxglsjzj_gwsxsjl%main-abdundsxbgjlsj'
);

-- 信息化标杆监测_学生管理数据子集_社团（协会）辅助数据类_社团（协会）基本数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_xsglsjzj_st（xh）fzsjl%main-abdschstxhjbsj','dataset-xxhbgjc','xxhbgjc_xsglsjzj_st（xh）fzsjl','main-abdschstxhjbsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_xsglsjzj_st（xh）fzsjl%main-abdschstxhjbsj'
);

-- 信息化标杆监测_学生管理数据子集_社团（协会）辅助数据类_举办社团活动数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_xsglsjzj_st（xh）fzsjl%main-abdschjbsthdsj','dataset-xxhbgjc','xxhbgjc_xsglsjzj_st（xh）fzsjl','main-abdschjbsthdsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_xsglsjzj_st（xh）fzsjl%main-abdschjbsthdsj'
);

-- 信息化标杆监测_学生管理数据子集_社团（协会）辅助数据类_学生参与社团数据-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_xsglsjzj_st（xh）fzsjl%main-abdundxscystsj','dataset-xxhbgjc','xxhbgjc_xsglsjzj_st（xh）fzsjl','main-abdundxscystsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_xsglsjzj_st（xh）fzsjl%main-abdundxscystsj'
);

-- 信息化标杆监测_学生管理数据子集_社团（协会）辅助数据类_参加社团活动数据-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_xsglsjzj_st（xh）fzsjl%main-abdundcjsthdsj','dataset-xxhbgjc','xxhbgjc_xsglsjzj_st（xh）fzsjl','main-abdundcjsthdsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_xsglsjzj_st（xh）fzsjl%main-abdundcjsthdsj'
);

-- 信息化标杆监测_学生管理数据子集_学生基本数据类_学生画像数据-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_xsglsjzj_xsjbsjl%main-abdundxshxsj','dataset-xxhbgjc','xxhbgjc_xsglsjzj_xsjbsjl','main-abdundxshxsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_xsglsjzj_xsjbsjl%main-abdundxshxsj'
);

-- 信息化标杆监测_学生管理数据子集_学生基本数据类_学生技能证书数据-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_xsglsjzj_xsjbsjl%main-abdundxsjnzssj','dataset-xxhbgjc','xxhbgjc_xsglsjzj_xsjbsjl','main-abdundxsjnzssj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_xsglsjzj_xsjbsjl%main-abdundxsjnzssj'
);

-- 信息化标杆监测_学生管理数据子集_学生基本数据类_综合成绩与评价数据-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_xsglsjzj_xsjbsjl%main-abdundzhcjypjsj','dataset-xxhbgjc','xxhbgjc_xsglsjzj_xsjbsjl','main-abdundzhcjypjsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_xsglsjzj_xsjbsjl%main-abdundzhcjypjsj'
);

-- 信息化标杆监测_学生管理数据子集_经济资助数据类_奖助贷申请数据-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_xsglsjzj_jjzzsjl%main-abdundjzdsqsj','dataset-xxhbgjc','xxhbgjc_xsglsjzj_jjzzsjl','main-abdundjzdsqsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_xsglsjzj_jjzzsjl%main-abdundjzdsqsj'
);

-- 信息化标杆监测_学生管理数据子集_生活健康数据类_心理咨询记录数据-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_xsglsjzj_shjksjl%main-abdundxlzxjlsj','dataset-xxhbgjc','xxhbgjc_xsglsjzj_shjksjl','main-abdundxlzxjlsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_xsglsjzj_shjksjl%main-abdundxlzxjlsj'
);

-- 信息化标杆监测_学生管理数据子集_毕业相关数据类_离校手续办理数据-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_xsglsjzj_byxgsjl%main-abdundlxsxblsj','dataset-xxhbgjc','xxhbgjc_xsglsjzj_byxgsjl','main-abdundlxsxblsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_xsglsjzj_byxgsjl%main-abdundlxsxblsj'
);

-- 信息化标杆监测_学生管理数据子集_毕业相关数据类_毕业去向【升学】数据-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_xsglsjzj_byxgsjl%main-abdundbyqxsxsj','dataset-xxhbgjc','xxhbgjc_xsglsjzj_byxgsjl','main-abdundbyqxsxsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_xsglsjzj_byxgsjl%main-abdundbyqxsxsj'
);

-- 信息化标杆监测_学生管理数据子集_毕业相关数据类_毕业去向【就业】数据-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_xsglsjzj_byxgsjl%main-abdundbyqxjysj','dataset-xxhbgjc','xxhbgjc_xsglsjzj_byxgsjl','main-abdundbyqxjysj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_xsglsjzj_byxgsjl%main-abdundbyqxjysj'
);

-- 信息化标杆监测_学生管理数据子集_毕业相关数据类_毕业去向【未就业】数据-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_xsglsjzj_byxgsjl%main-abdundbyqxwjysj','dataset-xxhbgjc','xxhbgjc_xsglsjzj_byxgsjl','main-abdundbyqxwjysj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_xsglsjzj_byxgsjl%main-abdundbyqxwjysj'
);

-- 信息化标杆监测_党建思政数据子集_党建基础数据类_党组织情况基础数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_djszsjzj_djjcsjl%main-abdschdzzqkjcsj','dataset-xxhbgjc','xxhbgjc_djszsjzj_djjcsjl','main-abdschdzzqkjcsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_djszsjzj_djjcsjl%main-abdschdzzqkjcsj'
);

-- 信息化标杆监测_党建思政数据子集_党建基础数据类_党员发展数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_djszsjzj_djjcsjl%main-abdschdyfzsj','dataset-xxhbgjc','xxhbgjc_djszsjzj_djjcsjl','main-abdschdyfzsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_djszsjzj_djjcsjl%main-abdschdyfzsj'
);

-- 信息化标杆监测_党建思政数据子集_党建活动数据类_党员干部学习数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_djszsjzj_djhdsjl%main-abdschdygbxxsj','dataset-xxhbgjc','xxhbgjc_djszsjzj_djhdsjl','main-abdschdygbxxsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_djszsjzj_djhdsjl%main-abdschdygbxxsj'
);

-- 信息化标杆监测_党建思政数据子集_党建活动数据类_党课数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_djszsjzj_djhdsjl%main-abdschdksj','dataset-xxhbgjc','xxhbgjc_djszsjzj_djhdsjl','main-abdschdksj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_djszsjzj_djhdsjl%main-abdschdksj'
);

-- 信息化标杆监测_党建思政数据子集_党建活动数据类_党员日常活动数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_djszsjzj_djhdsjl%main-abdschdyrchdsj','dataset-xxhbgjc','xxhbgjc_djszsjzj_djhdsjl','main-abdschdyrchdsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_djszsjzj_djhdsjl%main-abdschdyrchdsj'
);

-- 信息化标杆监测_党建思政数据子集_党建活动数据类_三会数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_djszsjzj_djhdsjl%main-abdschshsj','dataset-xxhbgjc','xxhbgjc_djszsjzj_djhdsjl','main-abdschshsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_djszsjzj_djhdsjl%main-abdschshsj'
);

-- 信息化标杆监测_党建思政数据子集_党建活动数据类_党员主题党日数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_djszsjzj_djhdsjl%main-abdschdyztdrsj','dataset-xxhbgjc','xxhbgjc_djszsjzj_djhdsjl','main-abdschdyztdrsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_djszsjzj_djhdsjl%main-abdschdyztdrsj'
);

-- 信息化标杆监测_党建思政数据子集_思政活动数据类_思政活动数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_djszsjzj_szhdsjl%main-abdschszhdsj','dataset-xxhbgjc','xxhbgjc_djszsjzj_szhdsjl','main-abdschszhdsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_djszsjzj_szhdsjl%main-abdschszhdsj'
);

-- 信息化标杆监测_党建思政数据子集_思政活动数据类_思政教职工数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_djszsjzj_szhdsjl%main-abdschszjzgsj','dataset-xxhbgjc','xxhbgjc_djszsjzj_szhdsjl','main-abdschszjzgsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_djszsjzj_szhdsjl%main-abdschszjzgsj'
);

-- 信息化标杆监测_资产与设备管理数据子集_仪器设备管理数据类_仪器设备基本数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_zcysbglsjzj_yqsbglsjl%main-abdschyqsbjbsj','dataset-xxhbgjc','xxhbgjc_zcysbglsjzj_yqsbglsjl','main-abdschyqsbjbsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_zcysbglsjzj_yqsbglsjl%main-abdschyqsbjbsj'
);

-- 信息化标杆监测_资产与设备管理数据子集_仪器设备管理数据类_智慧教室设备运行-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_zcysbglsjzj_yqsbglsjl%main-abdschzhjssbyxsj','dataset-xxhbgjc','xxhbgjc_zcysbglsjzj_yqsbglsjl','main-abdschzhjssbyxsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_zcysbglsjzj_yqsbglsjl%main-abdschzhjssbyxsj'
);

-- 信息化标杆监测_科研管理数据子集_科研项目数据类_科研项目基本数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_kyglsjzj_kyxmsjl%main-abdschkyxmjbsj','dataset-xxhbgjc','xxhbgjc_kyglsjzj_kyxmsjl','main-abdschkyxmjbsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_kyglsjzj_kyxmsjl%main-abdschkyxmjbsj'
);

-- 信息化标杆监测_科研管理数据子集_科研项目数据类_科研项目研究活动数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_kyglsjzj_kyxmsjl%main-abdschkyxmyjhdsj','dataset-xxhbgjc','xxhbgjc_kyglsjzj_kyxmsjl','main-abdschkyxmyjhdsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_kyglsjzj_kyxmsjl%main-abdschkyxmyjhdsj'
);

-- 信息化标杆监测_科研管理数据子集_科研项目数据类_科研经费支出数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_kyglsjzj_kyxmsjl%main-abdschkyjfzcsj','dataset-xxhbgjc','xxhbgjc_kyglsjzj_kyxmsjl','main-abdschkyjfzcsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_kyglsjzj_kyxmsjl%main-abdschkyjfzcsj'
);

-- 信息化标杆监测_科研管理数据子集_科研成果数据类_论文发表数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_kyglsjzj_kycgsjl%main-abdschlwfbsj','dataset-xxhbgjc','xxhbgjc_kyglsjzj_kycgsjl','main-abdschlwfbsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_kyglsjzj_kycgsjl%main-abdschlwfbsj'
);

-- 信息化标杆监测_科研管理数据子集_科研成果数据类_专利发布数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_kyglsjzj_kycgsjl%main-abdschzlfbsj','dataset-xxhbgjc','xxhbgjc_kyglsjzj_kycgsjl','main-abdschzlfbsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_kyglsjzj_kycgsjl%main-abdschzlfbsj'
);

-- 信息化标杆监测_科研管理数据子集_科研成果数据类_专著发表数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_kyglsjzj_kycgsjl%main-abdschzzfbsj','dataset-xxhbgjc','xxhbgjc_kyglsjzj_kycgsjl','main-abdschzzfbsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_kyglsjzj_kycgsjl%main-abdschzzfbsj'
);

-- 信息化标杆监测_科研管理数据子集_学术交流数据类_学术讲座数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_kyglsjzj_xsjlsjl%main-abdschxsjzsj','dataset-xxhbgjc','xxhbgjc_kyglsjzj_xsjlsjl','main-abdschxsjzsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_kyglsjzj_xsjlsjl%main-abdschxsjzsj'
);

-- 信息化标杆监测_服务管理数据子集_认证消费数据类_学生消费数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_fwglsjzj_rzxfsjl%main-abdschxsxfsj','dataset-xxhbgjc','xxhbgjc_fwglsjzj_rzxfsjl','main-abdschxsxfsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_fwglsjzj_rzxfsjl%main-abdschxsxfsj'
);

-- 信息化标杆监测_服务管理数据子集_认证消费数据类_一卡通认证数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_fwglsjzj_rzxfsjl%main-abdschyktrzsj','dataset-xxhbgjc','xxhbgjc_fwglsjzj_rzxfsjl','main-abdschyktrzsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_fwglsjzj_rzxfsjl%main-abdschyktrzsj'
);

-- 信息化标杆监测_服务管理数据子集_在线事务办理数据类_在线事务办理数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_fwglsjzj_zxswblsjl%main-abdschzxswblsj','dataset-xxhbgjc','xxhbgjc_fwglsjzj_zxswblsjl','main-abdschzxswblsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_fwglsjzj_zxswblsjl%main-abdschzxswblsj'
);

-- 信息化标杆监测_服务管理数据子集_信息服务数据类_校园动态信息数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_fwglsjzj_xxfwsjl%main-abdschxydtxxsj','dataset-xxhbgjc','xxhbgjc_fwglsjzj_xxfwsjl','main-abdschxydtxxsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_fwglsjzj_xxfwsjl%main-abdschxydtxxsj'
);

-- 信息化标杆监测_服务管理数据子集_图书期刊数据类_图书基本数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_fwglsjzj_tsqksjl%main-abdschtsjbsj','dataset-xxhbgjc','xxhbgjc_fwglsjzj_tsqksjl','main-abdschtsjbsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_fwglsjzj_tsqksjl%main-abdschtsjbsj'
);

-- 信息化标杆监测_服务管理数据子集_图书期刊数据类_图书借阅数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_fwglsjzj_tsqksjl%main-abdschtsjysj','dataset-xxhbgjc','xxhbgjc_fwglsjzj_tsqksjl','main-abdschtsjysj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_fwglsjzj_tsqksjl%main-abdschtsjysj'
);

-- 信息化标杆监测_服务管理数据子集_图书期刊数据类_期刊基本数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_fwglsjzj_tsqksjl%main-abdschqkjbsj','dataset-xxhbgjc','xxhbgjc_fwglsjzj_tsqksjl','main-abdschqkjbsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_fwglsjzj_tsqksjl%main-abdschqkjbsj'
);

-- 信息化标杆监测_服务管理数据子集_校内赛事活动数据类_校内学生赛事活动数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_fwglsjzj_xnsshdsjl%main-abdschxnxssshdsj','dataset-xxhbgjc','xxhbgjc_fwglsjzj_xnsshdsjl','main-abdschxnxssshdsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_fwglsjzj_xnsshdsjl%main-abdschxnxssshdsj'
);

-- 信息化标杆监测_服务管理数据子集_网络安全管理数据类_网络安全管理数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_fwglsjzj_wlaqglsjl%main-abdschwlaqglsj','dataset-xxhbgjc','xxhbgjc_fwglsjzj_wlaqglsjl','main-abdschwlaqglsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_fwglsjzj_wlaqglsjl%main-abdschwlaqglsj'
);

-- 信息化标杆监测_服务管理数据子集_数据集成情况数据类_校本数据中心数据集成情况数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_fwglsjzj_sjjcqksjl%main-abdschxbsjzxsjjcqksj','dataset-xxhbgjc','xxhbgjc_fwglsjzj_sjjcqksjl','main-abdschxbsjzxsjjcqksj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_fwglsjzj_sjjcqksjl%main-abdschxbsjzxsjjcqksj'
);

-- 信息化标杆监测_服务管理数据子集_数据集成情况数据类_数据标准与信息系统映射关系数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_fwglsjzj_sjjcqksjl%main-abdschsjbzyxxxtysgxsj','dataset-xxhbgjc','xxhbgjc_fwglsjzj_sjjcqksjl','main-abdschsjbzyxxxtysgxsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_fwglsjzj_sjjcqksjl%main-abdschsjbzyxxxtysgxsj'
);

-- 信息化标杆监测_服务管理数据子集_信息化系统集成认证数据类_信息化系统集成情况数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_fwglsjzj_xxhxtjcrzsjl%main-abdschxxhxtjcqksj','dataset-xxhbgjc','xxhbgjc_fwglsjzj_xxhxtjcrzsjl','main-abdschxxhxtjcqksj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_fwglsjzj_xxhxtjcrzsjl%main-abdschxxhxtjcqksj'
);

-- 信息化标杆监测_服务管理数据子集_信息化系统集成认证数据类_统一用户认证登录数据-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_fwglsjzj_xxhxtjcrzsjl%main-abdschtyyhrzdlsj','dataset-xxhbgjc','xxhbgjc_fwglsjzj_xxhxtjcrzsjl','main-abdschtyyhrzdlsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_fwglsjzj_xxhxtjcrzsjl%main-abdschtyyhrzdlsj'
);

-- 信息化标杆监测_补采数据子集_信息化系统集成认证数据类_实习交流分享活动记录-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_bcsjzj_xxhxtjcrzsjl%main-abdschsxjlfxhdjl','dataset-xxhbgjc','xxhbgjc_bcsjzj_xxhxtjcrzsjl','main-abdschsxjlfxhdjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_bcsjzj_xxhxtjcrzsjl%main-abdschsxjlfxhdjl'
);

-- 信息化标杆监测_补采数据子集_信息化系统集成认证数据类_校外网站访问记录-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_bcsjzj_xxhxtjcrzsjl%main-abdschxwwzfwjl','dataset-xxhbgjc','xxhbgjc_bcsjzj_xxhxtjcrzsjl','main-abdschxwwzfwjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_bcsjzj_xxhxtjcrzsjl%main-abdschxwwzfwjl'
);

-- 信息化标杆监测_补采数据子集_信息化系统集成认证数据类_上报数据模型确权记录-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_bcsjzj_xxhxtjcrzsjl%main-abdschsbsjmxqqjl','dataset-xxhbgjc','xxhbgjc_bcsjzj_xxhxtjcrzsjl','main-abdschsbsjmxqqjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_bcsjzj_xxhxtjcrzsjl%main-abdschsbsjmxqqjl'
);

-- 信息化标杆监测_补采数据子集_信息化系统集成认证数据类_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_bcsjzj_xxhxtjcrzsjl%main-abdschxxjbxx','dataset-xxhbgjc','xxhbgjc_bcsjzj_xxhxtjcrzsjl','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_bcsjzj_xxhxtjcrzsjl%main-abdschxxjbxx'
);

-- 信息化标杆监测_补采数据子集_信息化系统集成认证数据类_教师参与岗课赛证融通的记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_bcsjzj_xxhxtjcrzsjl%main-abdteajscygkszrtdjl','dataset-xxhbgjc','xxhbgjc_bcsjzj_xxhxtjcrzsjl','main-abdteajscygkszrtdjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_bcsjzj_xxhxtjcrzsjl%main-abdteajscygkszrtdjl'
);

-- 信息化标杆监测_补采数据子集_信息化系统集成认证数据类_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_bcsjzj_xxhxtjcrzsjl%main-abdteajzgjbxx','dataset-xxhbgjc','xxhbgjc_bcsjzj_xxhxtjcrzsjl','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_bcsjzj_xxhxtjcrzsjl%main-abdteajzgjbxx'
);

-- 信息化标杆监测_补采数据子集_信息化系统集成认证数据类_学生查看课表记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_bcsjzj_xxhxtjcrzsjl%main-abdundxsckkbjl','dataset-xxhbgjc','xxhbgjc_bcsjzj_xxhxtjcrzsjl','main-abdundxsckkbjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_bcsjzj_xxhxtjcrzsjl%main-abdundxsckkbjl'
);

-- 信息化标杆监测_补采数据子集_信息化系统集成认证数据类_学生打印成绩单记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_bcsjzj_xxhxtjcrzsjl%main-abdundxsdycjdjl','dataset-xxhbgjc','xxhbgjc_bcsjzj_xxhxtjcrzsjl','main-abdundxsdycjdjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_bcsjzj_xxhxtjcrzsjl%main-abdundxsdycjdjl'
);

-- 信息化标杆监测_补采数据子集_信息化系统集成认证数据类_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_bcsjzj_xxhxtjcrzsjl%main-abdundbzksjbxx','dataset-xxhbgjc','xxhbgjc_bcsjzj_xxhxtjcrzsjl','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_bcsjzj_xxhxtjcrzsjl%main-abdundbzksjbxx'
);

-- 信息化标杆监测_师生发展_学生发展_学生了解培养过程_本学年拟制教学总体计划的专业数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ssfz_xsfz_xsljpygc%main-ivesisch0221','dataset-xxhbgjc','xxhbgjc_ssfz_xsfz_xsljpygc','main-ivesisch0221',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ssfz_xsfz_xsljpygc%main-ivesisch0221'
);

-- 信息化标杆监测_师生发展_学生发展_学生了解培养过程_本学期综合成绩与评价的总记录数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ssfz_xsfz_xsljpygc%main-ivesisch0222','dataset-xxhbgjc','xxhbgjc_ssfz_xsfz_xsljpygc','main-ivesisch0222',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ssfz_xsfz_xsljpygc%main-ivesisch0222'
);

-- 信息化标杆监测_师生发展_学生发展_学生获取就业服务_本学年产学合作企业接受毕业生人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ssfz_xsfz_xshqjyfw%main-ivesisch0223','dataset-xxhbgjc','xxhbgjc_ssfz_xsfz_xshqjyfw','main-ivesisch0223',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ssfz_xsfz_xshqjyfw%main-ivesisch0223'
);

-- 信息化标杆监测_师生发展_学生发展_学生查看个人画像_本月学生画像访问次数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ssfz_xsfz_xsckgrhx%main-ivesisch0224','dataset-xxhbgjc','xxhbgjc_ssfz_xsfz_xsckgrhx','main-ivesisch0224',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ssfz_xsfz_xsckgrhx%main-ivesisch0224'
);

-- 信息化标杆监测_师生发展_教师发展_教师参与发展活动_本月参加党建活动的教师人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ssfz_jsfz_jscyfzhd%main-ivesisch0225','dataset-xxhbgjc','xxhbgjc_ssfz_jsfz_jscyfzhd','main-ivesisch0225',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ssfz_jsfz_jscyfzhd%main-ivesisch0225'
);

-- 信息化标杆监测_师生发展_教师发展_教师参与发展活动_本月参加学习进修的教师人次-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ssfz_jsfz_jscyfzhd%main-ivesisch0226','dataset-xxhbgjc','xxhbgjc_ssfz_jsfz_jscyfzhd','main-ivesisch0226',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ssfz_jsfz_jscyfzhd%main-ivesisch0226'
);

-- 信息化标杆监测_师生发展_教师发展_教师参与发展活动_本学期参加“科研和教研类别”项目的教师人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ssfz_jsfz_jscyfzhd%main-ivesisch0227','dataset-xxhbgjc','xxhbgjc_ssfz_jsfz_jscyfzhd','main-ivesisch0227',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ssfz_jsfz_jscyfzhd%main-ivesisch0227'
);

-- 信息化标杆监测_师生发展_教师发展_教师参与发展活动_本学期参与学术讲座的教师人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ssfz_jsfz_jscyfzhd%main-ivesisch0228','dataset-xxhbgjc','xxhbgjc_ssfz_jsfz_jscyfzhd','main-ivesisch0228',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ssfz_jsfz_jscyfzhd%main-ivesisch0228'
);

-- 信息化标杆监测_师生发展_教师发展_教师参与校企合作活动_本学期企业兼职教师数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ssfz_jsfz_jscyxqhzhd%main-ivesisch0229','dataset-xxhbgjc','xxhbgjc_ssfz_jsfz_jscyxqhzhd','main-ivesisch0229',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ssfz_jsfz_jscyxqhzhd%main-ivesisch0229'
);

-- 信息化标杆监测_师生发展_教师发展_教师参与校企合作活动_本学期参加“校企合作类别”项目的教师人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ssfz_jsfz_jscyxqhzhd%main-ivesisch0230','dataset-xxhbgjc','xxhbgjc_ssfz_jsfz_jscyxqhzhd','main-ivesisch0230',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ssfz_jsfz_jscyxqhzhd%main-ivesisch0230'
);

-- 信息化标杆监测_师生发展_教师发展_教师参与校企合作活动_本学期参加企业实践的教师人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ssfz_jsfz_jscyxqhzhd%main-ivesisch0231','dataset-xxhbgjc','xxhbgjc_ssfz_jsfz_jscyxqhzhd','main-ivesisch0231',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ssfz_jsfz_jscyxqhzhd%main-ivesisch0231'
);

-- 信息化标杆监测_师生发展_教师发展_教师查看个人画像_本月教师画像访问次数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ssfz_jsfz_jsckgrhx%main-ivesisch0232','dataset-xxhbgjc','xxhbgjc_ssfz_jsfz_jsckgrhx','main-ivesisch0232',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ssfz_jsfz_jsckgrhx%main-ivesisch0232'
);

-- 信息化标杆监测_教育教学_产教融合办学_专业改造_本学年总体计划内容更新总次数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jyjx_cjrhbx_zygz%main-ivesisch0233','dataset-xxhbgjc','xxhbgjc_jyjx_cjrhbx_zygz','main-ivesisch0233',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jyjx_cjrhbx_zygz%main-ivesisch0233'
);

-- 信息化标杆监测_教育教学_产教融合办学_专业设置与调整_本学年专业设置变化记录数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jyjx_cjrhbx_zyszytz%main-ivesisch0234','dataset-xxhbgjc','xxhbgjc_jyjx_cjrhbx_zyszytz','main-ivesisch0234',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jyjx_cjrhbx_zyszytz%main-ivesisch0234'
);

-- 信息化标杆监测_教育教学_学生学习_学生思政学习_本月参加思政活动的学生人次-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jyjx_xsxx_xsszxx%main-ivesisch0235','dataset-xxhbgjc','xxhbgjc_jyjx_xsxx_xsszxx','main-ivesisch0235',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jyjx_xsxx_xsszxx%main-ivesisch0235'
);

-- 信息化标杆监测_教育教学_学生学习_学生课程学习_本月通过网络课程在线学习的学生人次-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jyjx_xsxx_xskcxx%main-ivesisch0236','dataset-xxhbgjc','xxhbgjc_jyjx_xsxx_xskcxx','main-ivesisch0236',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jyjx_xsxx_xskcxx%main-ivesisch0236'
);

-- 信息化标杆监测_教育教学_学生学习_学生课程学习_本月在智慧教室上课的学生人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jyjx_xsxx_xskcxx%main-ivesisch0237','dataset-xxhbgjc','xxhbgjc_jyjx_xsxx_xskcxx','main-ivesisch0237',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jyjx_xsxx_xskcxx%main-ivesisch0237'
);

-- 信息化标杆监测_教育教学_学生学习_学生课程学习_本月借阅图书的学生人次-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jyjx_xsxx_xskcxx%main-ivesisch0238','dataset-xxhbgjc','xxhbgjc_jyjx_xsxx_xskcxx','main-ivesisch0238',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jyjx_xsxx_xskcxx%main-ivesisch0238'
);

-- 信息化标杆监测_教育教学_学生学习_学生课程学习_本月访问数字图书馆人次-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jyjx_xsxx_xskcxx%main-ivesisch0239','dataset-xxhbgjc','xxhbgjc_jyjx_xsxx_xskcxx','main-ivesisch0239',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jyjx_xsxx_xskcxx%main-ivesisch0239'
);

-- 信息化标杆监测_教育教学_学生学习_学生进行实验实训_本学期学生虚拟仿真实训课时-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jyjx_xsxx_xsjxsysx%main-ivesisch0240','dataset-xxhbgjc','xxhbgjc_jyjx_xsxx_xsjxsysx','main-ivesisch0240',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jyjx_xsxx_xsjxsysx%main-ivesisch0240'
);

-- 信息化标杆监测_教育教学_学生学习_学生进行岗位实习_本月实习学生人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jyjx_xsxx_xsjxgwsx%main-ivesisch0241','dataset-xxhbgjc','xxhbgjc_jyjx_xsxx_xsjxgwsx','main-ivesisch0241',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jyjx_xsxx_xsjxgwsx%main-ivesisch0241'
);

-- 信息化标杆监测_教育教学_学生学习_学生进行岗位实习_本学期举办实习交流分享会活动次数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jyjx_xsxx_xsjxgwsx%main-ivesisch0242','dataset-xxhbgjc','xxhbgjc_jyjx_xsxx_xsjxgwsx','main-ivesisch0242',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jyjx_xsxx_xsjxgwsx%main-ivesisch0242'
);

-- 信息化标杆监测_教育教学_学生学习_学生进行岗位实习_本月实习报告提交数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jyjx_xsxx_xsjxgwsx%main-ivesisch0243','dataset-xxhbgjc','xxhbgjc_jyjx_xsxx_xsjxgwsx','main-ivesisch0243',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jyjx_xsxx_xsjxgwsx%main-ivesisch0243'
);

-- 信息化标杆监测_教育教学_学生学习_学生参与实践活动_本月参加赛事活动的学生人次-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jyjx_xsxx_xscysjhd%main-ivesisch0244','dataset-xxhbgjc','xxhbgjc_jyjx_xsxx_xscysjhd','main-ivesisch0244',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jyjx_xsxx_xscysjhd%main-ivesisch0244'
);

-- 信息化标杆监测_教育教学_学生学习_学生参与实践活动_本学期获得技能证书的学生人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jyjx_xsxx_xscysjhd%main-ivesisch0245','dataset-xxhbgjc','xxhbgjc_jyjx_xsxx_xscysjhd','main-ivesisch0245',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jyjx_xsxx_xscysjhd%main-ivesisch0245'
);

-- 信息化标杆监测_教育教学_学生学习_学生处理学习事务_本月查看课表的学生人次-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jyjx_xsxx_xsclxxsw%main-ivesisch0246','dataset-xxhbgjc','xxhbgjc_jyjx_xsxx_xsclxxsw','main-ivesisch0246',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jyjx_xsxx_xsclxxsw%main-ivesisch0246'
);

-- 信息化标杆监测_教育教学_学生学习_学生处理学习事务_本学期参与选课的学生人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jyjx_xsxx_xsclxxsw%main-ivesisch0247','dataset-xxhbgjc','xxhbgjc_jyjx_xsxx_xsclxxsw','main-ivesisch0247',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jyjx_xsxx_xsclxxsw%main-ivesisch0247'
);

-- 信息化标杆监测_教育教学_学生学习_学生处理学习事务_本学期自助打印成绩单的学生人次-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jyjx_xsxx_xsclxxsw%main-ivesisch0248','dataset-xxhbgjc','xxhbgjc_jyjx_xsxx_xsclxxsw','main-ivesisch0248',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jyjx_xsxx_xsclxxsw%main-ivesisch0248'
);

-- 信息化标杆监测_教育教学_教师教学_教师思政教学_本月思政示范课程的授课教师人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jyjx_jsjx_jsszjx%main-ivesisch0249','dataset-xxhbgjc','xxhbgjc_jyjx_jsjx_jsszjx','main-ivesisch0249',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jyjx_jsjx_jsszjx%main-ivesisch0249'
);

-- 信息化标杆监测_教育教学_教师教学_教师课程教学_本月网络课程的授课教师人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jyjx_jsjx_jskcjx%main-ivesisch0250','dataset-xxhbgjc','xxhbgjc_jyjx_jsjx_jskcjx','main-ivesisch0250',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jyjx_jsjx_jskcjx%main-ivesisch0250'
);

-- 信息化标杆监测_教育教学_教师教学_教师课程教学_本月在智慧教室授课的教师人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jyjx_jsjx_jskcjx%main-ivesisch0251','dataset-xxhbgjc','xxhbgjc_jyjx_jsjx_jskcjx','main-ivesisch0251',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jyjx_jsjx_jskcjx%main-ivesisch0251'
);

-- 信息化标杆监测_教育教学_教师教学_教师课程教学_本学期参与岗课赛证融通教学的教师人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jyjx_jsjx_jskcjx%main-ivesisch0252','dataset-xxhbgjc','xxhbgjc_jyjx_jsjx_jskcjx','main-ivesisch0252',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jyjx_jsjx_jskcjx%main-ivesisch0252'
);

-- 信息化标杆监测_教育教学_教师教学_教师实训教学_本学期虚拟仿真实训项目数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jyjx_jsjx_jssxjx%main-ivesisch0253','dataset-xxhbgjc','xxhbgjc_jyjx_jsjx_jssxjx','main-ivesisch0253',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jyjx_jsjx_jssxjx%main-ivesisch0253'
);

-- 信息化标杆监测_教育教学_教师教学_教师开发与应用数字资源_本学期开发数字教材或虚拟仿真实训资源数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jyjx_jsjx_jskfyyyszzy%main-ivesisch0254','dataset-xxhbgjc','xxhbgjc_jyjx_jsjx_jskfyyyszzy','main-ivesisch0254',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jyjx_jsjx_jskfyyyszzy%main-ivesisch0254'
);

-- 信息化标杆监测_教育教学_教师教学_教师开发与应用数字资源_本学期应用数字教材或虚拟仿真数字资源的课程数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jyjx_jsjx_jskfyyyszzy%main-ivesisch0255','dataset-xxhbgjc','xxhbgjc_jyjx_jsjx_jskfyyyszzy','main-ivesisch0255',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jyjx_jsjx_jskfyyyszzy%main-ivesisch0255'
);

-- 信息化标杆监测_教育教学_教师教学_教师开发与应用数字资源_本学期对接国家平台的资源数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jyjx_jsjx_jskfyyyszzy%main-ivesisch0256','dataset-xxhbgjc','xxhbgjc_jyjx_jsjx_jskfyyyszzy','main-ivesisch0256',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jyjx_jsjx_jskfyyyszzy%main-ivesisch0256'
);

-- 信息化标杆监测_教育教学_教师教学_教师获取教学评价信息_本学期参与教学质量评价的课程数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jyjx_jsjx_jshqjxpjxx%main-ivesisch0257','dataset-xxhbgjc','xxhbgjc_jyjx_jsjx_jshqjxpjxx','main-ivesisch0257',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jyjx_jsjx_jshqjxpjxx%main-ivesisch0257'
);

-- 信息化标杆监测_管理服务_学生生活_学生离校_本学年办理离校手续的学生人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_glfw_xssh_xslx%main-ivesisch0258','dataset-xxhbgjc','xxhbgjc_glfw_xssh_xslx','main-ivesisch0258',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_glfw_xssh_xslx%main-ivesisch0258'
);

-- 信息化标杆监测_管理服务_学生生活_学生心理咨询_本月心理咨询人次-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_glfw_xssh_xsxlzx%main-ivesisch0259','dataset-xxhbgjc','xxhbgjc_glfw_xssh_xsxlzx','main-ivesisch0259',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_glfw_xssh_xsxlzx%main-ivesisch0259'
);

-- 信息化标杆监测_管理服务_学生生活_学生申请奖助贷补_本学期申请奖助贷的学生人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_glfw_xssh_xssqjzdb%main-ivesisch0260','dataset-xxhbgjc','xxhbgjc_glfw_xssh_xssqjzdb','main-ivesisch0260',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_glfw_xssh_xssqjzdb%main-ivesisch0260'
);

-- 信息化标杆监测_管理服务_教师事务_教师参与人事考核_本学期参与教师考核人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_glfw_jssw_jscyrskh%main-ivesisch0261','dataset-xxhbgjc','xxhbgjc_glfw_jssw_jscyrskh','main-ivesisch0261',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_glfw_jssw_jscyrskh%main-ivesisch0261'
);

-- 信息化标杆监测_管理服务_教师事务_教师获取财务与采购信息_本学期支出科研经费的项目数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_glfw_jssw_jshqcwycgxx%main-ivesisch0262','dataset-xxhbgjc','xxhbgjc_glfw_jssw_jshqcwycgxx','main-ivesisch0262',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_glfw_jssw_jshqcwycgxx%main-ivesisch0262'
);

-- 信息化标杆监测_管理服务_教师事务_教师获取资产信息_本月仪器设备信息维护次数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_glfw_jssw_jshqzcxx%main-ivesisch0263','dataset-xxhbgjc','xxhbgjc_glfw_jssw_jshqzcxx','main-ivesisch0263',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_glfw_jssw_jshqzcxx%main-ivesisch0263'
);

-- 信息化标杆监测_管理服务_信息化管理_统一身份认证_本月统一身份认证登录人次-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_glfw_xxhgl_tysfrz%main-ivesisch0264','dataset-xxhbgjc','xxhbgjc_glfw_xxhgl_tysfrz','main-ivesisch0264',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_glfw_xxhgl_tysfrz%main-ivesisch0264'
);

-- 信息化标杆监测_管理服务_信息化管理_数据治理_本月校本数据中心集成接口调用次数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_glfw_xxhgl_sjzl%main-ivesisch0265','dataset-xxhbgjc','xxhbgjc_glfw_xxhgl_sjzl','main-ivesisch0265',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_glfw_xxhgl_sjzl%main-ivesisch0265'
);

-- 信息化标杆监测_管理服务_信息化管理_教学督导_本月视频巡课次数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_glfw_xxhgl_jxdd%main-ivesisch0266','dataset-xxhbgjc','xxhbgjc_glfw_xxhgl_jxdd','main-ivesisch0266',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_glfw_xxhgl_jxdd%main-ivesisch0266'
);

-- 信息化标杆监测_管理服务_信息化管理_教学督导_本月巡课次数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_glfw_xxhgl_jxdd%main-ivesisch0267','dataset-xxhbgjc','xxhbgjc_glfw_xxhgl_jxdd','main-ivesisch0267',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_glfw_xxhgl_jxdd%main-ivesisch0267'
);

-- 信息化标杆监测_管理服务_信息化管理_信息化成果管理_本月信息化系统访问师生人次-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_glfw_xxhgl_xxhcggl%main-ivesisch0268','dataset-xxhbgjc','xxhbgjc_glfw_xxhgl_xxhcggl','main-ivesisch0268',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_glfw_xxhgl_xxhcggl%main-ivesisch0268'
);

-- 信息化标杆监测_管理服务_公共服务_一卡通服务_本月一卡通人均消费金额-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_glfw_ggfw_yktfw%main-ivesisch0269','dataset-xxhbgjc','xxhbgjc_glfw_ggfw_yktfw','main-ivesisch0269',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_glfw_ggfw_yktfw%main-ivesisch0269'
);

-- 信息化标杆监测_管理服务_公共服务_一站式服务大厅_本月“开具证明”在线事务办理数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_glfw_ggfw_yzsfwdt%main-ivesisch0270','dataset-xxhbgjc','xxhbgjc_glfw_ggfw_yzsfwdt','main-ivesisch0270',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_glfw_ggfw_yzsfwdt%main-ivesisch0270'
);

-- 信息化标杆监测_管理服务_公共服务_一站式服务大厅_本月“报销发票”在线事务办理数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_glfw_ggfw_yzsfwdt%main-ivesisch0271','dataset-xxhbgjc','xxhbgjc_glfw_ggfw_yzsfwdt','main-ivesisch0271',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_glfw_ggfw_yzsfwdt%main-ivesisch0271'
);

-- 信息化标杆监测_管理服务_公共服务_信息发布_本月校园动态发布数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_glfw_ggfw_xxfb%main-ivesisch0272','dataset-xxhbgjc','xxhbgjc_glfw_ggfw_xxfb','main-ivesisch0272',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_glfw_ggfw_xxfb%main-ivesisch0272'
);

-- 信息化标杆监测_管理服务_校园服务_故障报修_本月“后勤报修”在线事务办理数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_glfw_xyfw_gzbx%main-ivesisch0273','dataset-xxhbgjc','xxhbgjc_glfw_xyfw_gzbx','main-ivesisch0273',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_glfw_xyfw_gzbx%main-ivesisch0273'
);

-- 信息化标杆监测_管理服务_校园服务_服务预约_本月“场地预约”在线事务办理数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_glfw_xyfw_fwyy%main-ivesisch0274','dataset-xxhbgjc','xxhbgjc_glfw_xyfw_fwyy','main-ivesisch0274',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_glfw_xyfw_fwyy%main-ivesisch0274'
);

-- 信息化标杆监测_支撑条件与网络安全_校园管理_校园网络管理_本月校外网站访问人次-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_zctjywlaq_xygl_xywlgl%main-ivesisch0275','dataset-xxhbgjc','xxhbgjc_zctjywlaq_xygl_xywlgl','main-ivesisch0275',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_zctjywlaq_xygl_xywlgl%main-ivesisch0275'
);

-- 信息化标杆监测_支撑条件与网络安全_校园管理_校园网络管理_本月网络使用记录数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_zctjywlaq_xygl_xywlgl%main-ivesisch0276','dataset-xxhbgjc','xxhbgjc_zctjywlaq_xygl_xywlgl','main-ivesisch0276',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_zctjywlaq_xygl_xywlgl%main-ivesisch0276'
);

-- 信息化标杆监测_支撑条件与网络安全_校园管理_校园网络管理_本月网络攻击识别次数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_zctjywlaq_xygl_xywlgl%main-ivesisch0277','dataset-xxhbgjc','xxhbgjc_zctjywlaq_xygl_xywlgl','main-ivesisch0277',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_zctjywlaq_xygl_xywlgl%main-ivesisch0277'
);

-- 信息化标杆监测_支撑条件与网络安全_校园管理_教学环境管理_多媒体教室建设数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_zctjywlaq_xygl_jxhjgl%main-ivesisch0278','dataset-xxhbgjc','xxhbgjc_zctjywlaq_xygl_jxhjgl','main-ivesisch0278',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_zctjywlaq_xygl_jxhjgl%main-ivesisch0278'
);

-- 信息化标杆监测_支撑条件与网络安全_校园管理_教学环境管理_实训室建设数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_zctjywlaq_xygl_jxhjgl%main-ivesisch0279','dataset-xxhbgjc','xxhbgjc_zctjywlaq_xygl_jxhjgl','main-ivesisch0279',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_zctjywlaq_xygl_jxhjgl%main-ivesisch0279'
);

-- 信息化标杆监测_支撑条件与网络安全_校园管理_校园安全及能源管理_本月智慧教室设备运行时长-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_zctjywlaq_xygl_xyaqjnygl%main-ivesisch0280','dataset-xxhbgjc','xxhbgjc_zctjywlaq_xygl_xyaqjnygl','main-ivesisch0280',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_zctjywlaq_xygl_xyaqjnygl%main-ivesisch0280'
);

-- 信息化标杆监测_支撑条件与网络安全_校园管理_校园安全及能源管理_本月进出校门或宿舍的刷卡人次-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_zctjywlaq_xygl_xyaqjnygl%main-ivesisch0281','dataset-xxhbgjc','xxhbgjc_zctjywlaq_xygl_xyaqjnygl','main-ivesisch0281',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_zctjywlaq_xygl_xyaqjnygl%main-ivesisch0281'
);

-- 信息化标杆监测_参考指标_本学年毕业生人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0283','dataset-xxhbgjc','xxhbgjc_ckzb','main-ivesisch0283',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0283'
);

-- 信息化标杆监测_参考指标_本月在校教师人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0284','dataset-xxhbgjc','xxhbgjc_ckzb','main-ivesisch0284',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0284'
);

-- 信息化标杆监测_参考指标_本月学习进修活动数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0285','dataset-xxhbgjc','xxhbgjc_ckzb','main-ivesisch0285',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0285'
);

-- 信息化标杆监测_参考指标_本学期“科研和教研类别”项目数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0286','dataset-xxhbgjc','xxhbgjc_ckzb','main-ivesisch0286',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0286'
);

-- 信息化标杆监测_参考指标_本学期学术讲座举办数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0287','dataset-xxhbgjc','xxhbgjc_ckzb','main-ivesisch0287',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0287'
);

-- 信息化标杆监测_参考指标_本学期“校企合作类别”项目数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0288','dataset-xxhbgjc','xxhbgjc_ckzb','main-ivesisch0288',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0288'
);

-- 信息化标杆监测_参考指标_本学年教学总体计划拟制数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0289','dataset-xxhbgjc','xxhbgjc_ckzb','main-ivesisch0289',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0289'
);

-- 信息化标杆监测_参考指标_本学年专业设置数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0290','dataset-xxhbgjc','xxhbgjc_ckzb','main-ivesisch0290',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0290'
);

-- 信息化标杆监测_参考指标_本月在校学生人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0291','dataset-xxhbgjc','xxhbgjc_ckzb','main-ivesisch0291',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0291'
);

-- 信息化标杆监测_参考指标_本学期虚拟仿真实训项目数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0292','dataset-xxhbgjc','xxhbgjc_ckzb','main-ivesisch0292',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0292'
);

-- 信息化标杆监测_参考指标_本月实习生人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0293','dataset-xxhbgjc','xxhbgjc_ckzb','main-ivesisch0293',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0293'
);

-- 信息化标杆监测_参考指标_本月赛事活动举办次数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0294','dataset-xxhbgjc','xxhbgjc_ckzb','main-ivesisch0294',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0294'
);

-- 信息化标杆监测_参考指标_本月思政示范课排课数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0295','dataset-xxhbgjc','xxhbgjc_ckzb','main-ivesisch0295',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0295'
);

-- 信息化标杆监测_参考指标_本月网络课程课排课数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0296','dataset-xxhbgjc','xxhbgjc_ckzb','main-ivesisch0296',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0296'
);

-- 信息化标杆监测_参考指标_本月在智慧教室上课的课程数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0297','dataset-xxhbgjc','xxhbgjc_ckzb','main-ivesisch0297',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0297'
);

-- 信息化标杆监测_参考指标_本学期课程数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0298','dataset-xxhbgjc','xxhbgjc_ckzb','main-ivesisch0298',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0298'
);

-- 信息化标杆监测_参考指标_本学期科研项目数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0299','dataset-xxhbgjc','xxhbgjc_ckzb','main-ivesisch0299',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0299'
);

-- 信息化标杆监测_参考指标_本月仪器设备总数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0300','dataset-xxhbgjc','xxhbgjc_ckzb','main-ivesisch0300',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0300'
);

-- 信息化标杆监测_参考指标_在校师生人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0301','dataset-xxhbgjc','xxhbgjc_ckzb','main-ivesisch0301',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0301'
);

-- 信息化标杆监测_参考指标_本月集成接口总数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0302','dataset-xxhbgjc','xxhbgjc_ckzb','main-ivesisch0302',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0302'
);

-- 信息化标杆监测_参考指标_本月在安装视频设备教室的上课数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0303','dataset-xxhbgjc','xxhbgjc_ckzb','main-ivesisch0303',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0303'
);

-- 信息化标杆监测_参考指标_本月排课数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0304','dataset-xxhbgjc','xxhbgjc_ckzb','main-ivesisch0304',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0304'
);

-- 信息化标杆监测_参考指标_本学期在校教师人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0305','dataset-xxhbgjc','xxhbgjc_ckzb','main-ivesisch0305',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0305'
);

-- 信息化标杆监测_参考指标_本学期在校学生人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0306','dataset-xxhbgjc','xxhbgjc_ckzb','main-ivesisch0306',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_ckzb%main-ivesisch0306'
);

-- 信息化标杆监测_教职工管理数据子集_教师基本数据类_教师画像数据-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jzgglsjzj_jsjbsjl%main-abdteajshxsj','dataset-xxhbgjc','xxhbgjc_jzgglsjzj_jsjbsjl','main-abdteajshxsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jzgglsjzj_jsjbsjl%main-abdteajshxsj'
);

-- 信息化标杆监测_教职工管理数据子集_教师基本数据类_教师企业兼职/实践数据-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jzgglsjzj_jsjbsjl%main-abdteajsqyjzsjsj','dataset-xxhbgjc','xxhbgjc_jzgglsjzj_jsjbsjl','main-abdteajsqyjzsjsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jzgglsjzj_jsjbsjl%main-abdteajsqyjzsjsj'
);

-- 信息化标杆监测_教职工管理数据子集_学习进修数据类_学习进修数据-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jzgglsjzj_xxjxsjl%main-abdteaxxjxsj','dataset-xxhbgjc','xxhbgjc_jzgglsjzj_xxjxsjl','main-abdteaxxjxsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jzgglsjzj_xxjxsjl%main-abdteaxxjxsj'
);

-- 信息化标杆监测_教职工管理数据子集_教师考核数据类_教师考核数据-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-xxhbgjc%xxhbgjc_jzgglsjzj_jskhsjl%main-abdteajskhsj','dataset-xxhbgjc','xxhbgjc_jzgglsjzj_jskhsjl','main-abdteajskhsj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-xxhbgjc%xxhbgjc_jzgglsjzj_jskhsjl%main-abdteajskhsj'
);