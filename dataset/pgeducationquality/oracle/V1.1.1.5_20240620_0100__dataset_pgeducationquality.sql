/*
 Description		: [研究生教育教学质量监测(pgeducationquality)]应用数据集
 Author				: dataapp
 Date				: 2024-06-20 01:00:00
 Lowcode Version	: V1.1.1_Beta5
 Database           : oracle
*/

-- 数据集模型字段
BEGIN
    EXECUTE IMMEDIATE 'CREATE TABLE t_da_dataset_model_column ( dataset_id VARCHAR2(50) NOT NULL,
column_id VARCHAR2(50) NOT NULL,
model_id VARCHAR2(50) NOT NULL,
id VARCHAR2(100),
create_by VARCHAR2(100),
create_time DATE,
update_by VARCHAR2(100),
update_time DATE,PRIMARY KEY (id) )';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

COMMENT ON TABLE t_da_dataset_model_column IS '数据集模型字段';
COMMENT ON COLUMN t_da_dataset_model_column.dataset_id IS '数据集ID';
COMMENT ON COLUMN t_da_dataset_model_column.column_id IS '字段ID';
COMMENT ON COLUMN t_da_dataset_model_column.model_id IS '模型ID';
COMMENT ON COLUMN t_da_dataset_model_column.id IS 'ID';
COMMENT ON COLUMN t_da_dataset_model_column.create_by IS '创建人';
COMMENT ON COLUMN t_da_dataset_model_column.create_time IS '创建时间';
COMMENT ON COLUMN t_da_dataset_model_column.update_by IS '更新人';
COMMENT ON COLUMN t_da_dataset_model_column.update_time IS '更新时间';

-- 研究生教育教学质量监测-数据集
INSERT INTO t_da_dataset (id,sjjmc,
create_by,create_time,update_by,update_time,
copy_label,primary_app_id)
SELECT 'dataset-yjsjyjxzljc','研究生教育教学质量监测',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'','pgeducationquality'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset
    WHERE id = 'dataset-yjsjyjxzljc'
);

-- dataset-yjsjyjxzljc-pgeducationquality-数据集和应用关联表
INSERT INTO t_da_dataset_app (id,sjjid,appid,
create_by,create_time,update_by,update_time)
SELECT 'dataset-yjsjyjxzljc-pgeducationquality','dataset-yjsjyjxzljc','pgeducationquality',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_app
    WHERE id = 'dataset-yjsjyjxzljc-pgeducationquality'
);

-- 教学资源-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_jxzy','dataset-yjsjyjxzljc','教学资源',NULL,1,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_jxzy'
);

-- 教学成果-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_jxzy_jxcg','dataset-yjsjyjxzljc','教学成果','yjsjyjxzljc_jxzy',2,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_jxzy_jxcg'
);

-- 课程建设-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_jxzy_kcjs','dataset-yjsjyjxzljc','课程建设','yjsjyjxzljc_jxzy',3,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_jxzy_kcjs'
);

-- 科研平台-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_jxzy_kypt','dataset-yjsjyjxzljc','科研平台','yjsjyjxzljc_jxzy',4,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_jxzy_kypt'
);

-- 实践基地-COL_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_jxzy_sjjd','dataset-yjsjyjxzljc','实践基地','yjsjyjxzljc_jxzy',5,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_jxzy_sjjd'
);

-- 基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_jbxx','dataset-yjsjyjxzljc','基本信息',NULL,6,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_jbxx'
);

-- 基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_jbxx_jbxx','dataset-yjsjyjxzljc','基本信息','yjsjyjxzljc_jbxx',7,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_jbxx_jbxx'
);

-- 师资队伍-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_szdw','dataset-yjsjyjxzljc','师资队伍',NULL,8,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_szdw'
);

-- 导师队伍-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_szdw_dsdw','dataset-yjsjyjxzljc','导师队伍','yjsjyjxzljc_szdw',9,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_szdw_dsdw'
);

-- 教育教学-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_szdw_jyjx','dataset-yjsjyjxzljc','教育教学','yjsjyjxzljc_szdw',10,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_szdw_jyjx'
);

-- 师资水平-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_szdw_szsp','dataset-yjsjyjxzljc','师资水平','yjsjyjxzljc_szdw',11,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_szdw_szsp'
);

-- 学生工作-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_xsgz','dataset-yjsjyjxzljc','学生工作',NULL,12,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_xsgz'
);

-- 奖学金-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_xsgz_jxj','dataset-yjsjyjxzljc','奖学金','yjsjyjxzljc_xsgz',13,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_xsgz_jxj'
);

-- 研究生三助-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_xsgz_yjssz','dataset-yjsjyjxzljc','研究生三助','yjsjyjxzljc_xsgz',14,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_xsgz_yjssz'
);

-- 学生预警-COL_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_xsgz_xsyj','dataset-yjsjyjxzljc','学生预警','yjsjyjxzljc_xsgz',15,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_xsgz_xsyj'
);

-- 助学金-COL_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_xsgz_zxj','dataset-yjsjyjxzljc','助学金','yjsjyjxzljc_xsgz',16,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_xsgz_zxj'
);

-- 助学贷款-COL_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_xsgz_zxdk','dataset-yjsjyjxzljc','助学贷款','yjsjyjxzljc_xsgz',17,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_xsgz_zxdk'
);

-- 奖助学金-POS_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_xsgz_jzxj','dataset-yjsjyjxzljc','奖助学金','yjsjyjxzljc_xsgz',18,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_xsgz_jzxj'
);

-- 培养过程-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_pygc','dataset-yjsjyjxzljc','培养过程',NULL,19,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_pygc'
);

-- 在校概况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_pygc_zxgk','dataset-yjsjyjxzljc','在校概况','yjsjyjxzljc_pygc',20,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_pygc_zxgk'
);

-- 国际交流-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_pygc_gjjl','dataset-yjsjyjxzljc','国际交流','yjsjyjxzljc_pygc',21,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_pygc_gjjl'
);

-- 学生成果-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_pygc_xscg','dataset-yjsjyjxzljc','学生成果','yjsjyjxzljc_pygc',22,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_pygc_xscg'
);

-- 教学评估-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_pygc_jxpg','dataset-yjsjyjxzljc','教学评估','yjsjyjxzljc_pygc',23,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_pygc_jxpg'
);

-- 学位过程-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_xwgc','dataset-yjsjyjxzljc','学位过程',NULL,24,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_xwgc'
);

-- 论文查重-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_xwgc_lwcz','dataset-yjsjyjxzljc','论文查重','yjsjyjxzljc_xwgc',25,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_xwgc_lwcz'
);

-- 论文评阅-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_xwgc_lwpy','dataset-yjsjyjxzljc','论文评阅','yjsjyjxzljc_xwgc',26,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_xwgc_lwpy'
);

-- 论文答辩-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_xwgc_lwdb','dataset-yjsjyjxzljc','论文答辩','yjsjyjxzljc_xwgc',27,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_xwgc_lwdb'
);

-- 学位申请-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_xwgc_xwsq','dataset-yjsjyjxzljc','学位申请','yjsjyjxzljc_xwgc',28,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_xwgc_xwsq'
);

-- 优秀论文-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_xwgc_yxlw','dataset-yjsjyjxzljc','优秀论文','yjsjyjxzljc_xwgc',29,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_xwgc_yxlw'
);

-- 上会讨论-POS_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_xwgc_shtl','dataset-yjsjyjxzljc','上会讨论','yjsjyjxzljc_xwgc',30,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_xwgc_shtl'
);

-- 科学研究-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_kxyj','dataset-yjsjyjxzljc','科学研究',NULL,31,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_kxyj'
);

-- 导师信息-MEN_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_dsxx','dataset-yjsjyjxzljc','导师信息',NULL,32,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_dsxx'
);

-- 课程-COU_SL.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'yjsjyjxzljc_kc','dataset-yjsjyjxzljc','课程',NULL,33,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'yjsjyjxzljc_kc'
);

-- 研究生教育教学质量监测_教学资源_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy%main-abdschxxjbxx','dataset-yjsjyjxzljc','yjsjyjxzljc_jxzy','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy%main-abdschxxjbxx'
);

-- 研究生教育教学质量监测_教学资源_教学成果_教学成果信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_jxcg%main-abdschjxcgxx','dataset-yjsjyjxzljc','yjsjyjxzljc_jxzy_jxcg','main-abdschjxcgxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_jxcg%main-abdschjxcgxx'
);

-- 研究生教育教学质量监测_教学资源_教学成果_学院教材建设与出版-COL_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_jxcg%main-abdcolxyjcjsycb','dataset-yjsjyjxzljc','yjsjyjxzljc_jxzy_jxcg','main-abdcolxyjcjsycb',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_jxcg%main-abdcolxyjcjsycb'
);

-- 研究生教育教学质量监测_教学资源_课程建设_每学年学校课程开设平均学时数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_kcjs%main-possisch0309','dataset-yjsjyjxzljc','yjsjyjxzljc_jxzy_kcjs','main-possisch0309',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_kcjs%main-possisch0309'
);

-- 研究生教育教学质量监测_教学资源_课程建设_每学年学校各课程类别开设平均学时数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_kcjs%main-possisch0310','dataset-yjsjyjxzljc','yjsjyjxzljc_jxzy_kcjs','main-possisch0310',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_kcjs%main-possisch0310'
);

-- 研究生教育教学质量监测_教学资源_课程建设_每学年学校开设课程规模-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_kcjs%main-possisch0311','dataset-yjsjyjxzljc','yjsjyjxzljc_jxzy_kcjs','main-possisch0311',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_kcjs%main-possisch0311'
);

-- 研究生教育教学质量监测_教学资源_课程建设_每学年学校各课程类别开设课程规模-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_kcjs%main-possisch0312','dataset-yjsjyjxzljc','yjsjyjxzljc_jxzy_kcjs','main-possisch0312',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_kcjs%main-possisch0312'
);

-- 研究生教育教学质量监测_教学资源_课程建设_课程教学班开设信息-COL_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_kcjs%main-abdcolkcjxbksxx','dataset-yjsjyjxzljc','yjsjyjxzljc_jxzy_kcjs','main-abdcolkcjxbksxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_kcjs%main-abdcolkcjxbksxx'
);

-- 研究生教育教学质量监测_教学资源_课程建设_学院一流课程及案例信息-COL_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_kcjs%main-abdcolxyylkcjalxx','dataset-yjsjyjxzljc','yjsjyjxzljc_jxzy_kcjs','main-abdcolxyylkcjalxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_kcjs%main-abdcolxyylkcjalxx'
);

-- 研究生教育教学质量监测_教学资源_课程建设_每学年各学院课程开设门数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_kcjs%main-possicol0102','dataset-yjsjyjxzljc','yjsjyjxzljc_jxzy_kcjs','main-possicol0102',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_kcjs%main-possicol0102'
);

-- 研究生教育教学质量监测_教学资源_课程建设_每学年各学院课程开设门次数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_kcjs%main-possicol0103','dataset-yjsjyjxzljc','yjsjyjxzljc_jxzy_kcjs','main-possicol0103',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_kcjs%main-possicol0103'
);

-- 研究生教育教学质量监测_教学资源_课程建设_每学年各学院各课程类别课程开设平均学时数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_kcjs%main-possicol0104','dataset-yjsjyjxzljc','yjsjyjxzljc_jxzy_kcjs','main-possicol0104',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_kcjs%main-possicol0104'
);

-- 研究生教育教学质量监测_教学资源_课程建设_每学年各学院各课程类别开设课程规模-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_kcjs%main-possicol0105','dataset-yjsjyjxzljc','yjsjyjxzljc_jxzy_kcjs','main-possicol0105',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_kcjs%main-possicol0105'
);

-- 研究生教育教学质量监测_教学资源_科研平台_全校科研平台累计数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_kypt%main-possisch0313','dataset-yjsjyjxzljc','yjsjyjxzljc_jxzy_kypt','main-possisch0313',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_kypt%main-possisch0313'
);

-- 研究生教育教学质量监测_教学资源_科研平台_每年各学院科研项目数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_kypt%main-possicol0107','dataset-yjsjyjxzljc','yjsjyjxzljc_jxzy_kypt','main-possicol0107',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_kypt%main-possicol0107'
);

-- 研究生教育教学质量监测_教学资源_科研平台_每年各学院科研项目经费金额-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_kypt%main-possicol0108','dataset-yjsjyjxzljc','yjsjyjxzljc_jxzy_kypt','main-possicol0108',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_kypt%main-possicol0108'
);

-- 研究生教育教学质量监测_教学资源_实践基地_学院实践基地信息-COL_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_sjjd%main-abdcolxysjjdxx','dataset-yjsjyjxzljc','yjsjyjxzljc_jxzy_sjjd','main-abdcolxysjjdxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_sjjd%main-abdcolxysjjdxx'
);

-- 研究生教育教学质量监测_教学资源_实践基地_各学院实践企业基地累计数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_sjjd%main-possicol0106','dataset-yjsjyjxzljc','yjsjyjxzljc_jxzy_sjjd','main-possicol0106',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy_sjjd%main-possicol0106'
);

-- 研究生教育教学质量监测_教学资源_课程基本信息-COU_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy%main-abdcoukcjbxx','dataset-yjsjyjxzljc','yjsjyjxzljc_jxzy','main-abdcoukcjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_jxzy%main-abdcoukcjbxx'
);

-- 研究生教育教学质量监测_基本信息_基本信息_学位批次-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_jbxx_jbxx%main-abdschxwpc','dataset-yjsjyjxzljc','yjsjyjxzljc_jbxx_jbxx','main-abdschxwpc',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_jbxx_jbxx%main-abdschxwpc'
);

-- 研究生教育教学质量监测_基本信息_基本信息_组织机构信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_jbxx_jbxx%main-abdschzzjgxx','dataset-yjsjyjxzljc','yjsjyjxzljc_jbxx_jbxx','main-abdschzzjgxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_jbxx_jbxx%main-abdschzzjgxx'
);

-- 研究生教育教学质量监测_基本信息_基本信息_学院基本信息-COL_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_jbxx_jbxx%main-abdcolxyjbxx','dataset-yjsjyjxzljc','yjsjyjxzljc_jbxx_jbxx','main-abdcolxyjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_jbxx_jbxx%main-abdcolxyjbxx'
);

-- 研究生教育教学质量监测_基本信息_基本信息_研究生基本信息-POS_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_jbxx_jbxx%main-abdposyjsjbxx','dataset-yjsjyjxzljc','yjsjyjxzljc_jbxx_jbxx','main-abdposyjsjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_jbxx_jbxx%main-abdposyjsjbxx'
);

-- 研究生教育教学质量监测_师资队伍_导师队伍_每年学校导师生师比-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_dsdw%main-possisch0307','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw_dsdw','main-possisch0307',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_dsdw%main-possisch0307'
);

-- 研究生教育教学质量监测_师资队伍_导师队伍_各学科导师累计人数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_dsdw%main-possisub0115','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw_dsdw','main-possisub0115',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_dsdw%main-possisub0115'
);

-- 研究生教育教学质量监测_师资队伍_导师队伍_学院导师培训信息-COL_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_dsdw%main-abdcolxydspxxx','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw_dsdw','main-abdcolxydspxxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_dsdw%main-abdcolxydspxxx'
);

-- 研究生教育教学质量监测_师资队伍_导师队伍_各学院导师累计人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_dsdw%main-possicol0094','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw_dsdw','main-possicol0094',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_dsdw%main-possicol0094'
);

-- 研究生教育教学质量监测_师资队伍_导师队伍_每年学校各学院导师生师比-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_dsdw%main-possicol0095','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw_dsdw','main-possicol0095',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_dsdw%main-possicol0095'
);

-- 研究生教育教学质量监测_师资队伍_导师队伍_各学院各导师类别导师累计人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_dsdw%main-possicol0096','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw_dsdw','main-possicol0096',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_dsdw%main-possicol0096'
);

-- 研究生教育教学质量监测_师资队伍_导师队伍_每年各学院导师培训次数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_dsdw%main-possicol0097','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw_dsdw','main-possicol0097',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_dsdw%main-possicol0097'
);

-- 研究生教育教学质量监测_师资队伍_导师队伍_每年各院系临近退休导师人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_dsdw%main-possicol0098','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw_dsdw','main-possicol0098',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_dsdw%main-possicol0098'
);

-- 研究生教育教学质量监测_师资队伍_教育教学_每年学校研究生授课教师人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_jyjx%main-possisch0308','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw_jyjx','main-possisch0308',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_jyjx%main-possisch0308'
);

-- 研究生教育教学质量监测_师资队伍_教育教学_每年全校主讲研究生课程教授占比-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_jyjx%main-possisch0358','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw_jyjx','main-possisch0358',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_jyjx%main-possisch0358'
);

-- 研究生教育教学质量监测_师资队伍_教育教学_每年全校授课人均学时-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_jyjx%main-possisch0320','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw_jyjx','main-possisch0320',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_jyjx%main-possisch0320'
);

-- 研究生教育教学质量监测_师资队伍_教育教学_每年全校高层次人才授课教师占比-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_jyjx%main-possisch0321','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw_jyjx','main-possisch0321',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_jyjx%main-possisch0321'
);

-- 研究生教育教学质量监测_师资队伍_教育教学_每年各学院研究生授课教师人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_jyjx%main-possicol0099','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw_jyjx','main-possicol0099',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_jyjx%main-possicol0099'
);

-- 研究生教育教学质量监测_师资队伍_教育教学_每年各学院研究生授课教师博士层次教师占比-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_jyjx%main-possicol0100','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw_jyjx','main-possicol0100',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_jyjx%main-possicol0100'
);

-- 研究生教育教学质量监测_师资队伍_教育教学_每年各学院外聘教师占比-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_jyjx%main-possicol0101','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw_jyjx','main-possicol0101',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_jyjx%main-possicol0101'
);

-- 研究生教育教学质量监测_师资队伍_教育教学_每年各学院主讲研究生课程教授占比-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_jyjx%main-possicol0183','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw_jyjx','main-possicol0183',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_jyjx%main-possicol0183'
);

-- 研究生教育教学质量监测_师资队伍_教育教学_每年各学院授课人均学时-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_jyjx%main-possicol0127','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw_jyjx','main-possicol0127',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_jyjx%main-possicol0127'
);

-- 研究生教育教学质量监测_师资队伍_教育教学_每年各学院高层次人才授课教师占比-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_jyjx%main-possicol0128','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw_jyjx','main-possicol0128',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_jyjx%main-possicol0128'
);

-- 研究生教育教学质量监测_师资队伍_教育教学_每年各学院未上课教师人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_jyjx%main-possicol0129','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw_jyjx','main-possicol0129',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_jyjx%main-possicol0129'
);

-- 研究生教育教学质量监测_师资队伍_师资水平_全校科研获奖累计数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_szsp%main-possisch0314','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw_szsp','main-possisch0314',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_szsp%main-possisch0314'
);

-- 研究生教育教学质量监测_师资队伍_师资水平_每年全校论文发表数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_szsp%main-possisch0315','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw_szsp','main-possisch0315',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_szsp%main-possisch0315'
);

-- 研究生教育教学质量监测_师资队伍_师资水平_各学院高层次教师累计人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_szsp%main-possicol0109','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw_szsp','main-possicol0109',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_szsp%main-possicol0109'
);

-- 研究生教育教学质量监测_师资队伍_师资水平_每年各学院论文发表数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_szsp%main-possicol0110','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw_szsp','main-possicol0110',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_szsp%main-possicol0110'
);

-- 研究生教育教学质量监测_师资队伍_师资水平_每年各学院人均发表论文数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_szsp%main-possicol0111','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw_szsp','main-possicol0111',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_szsp%main-possicol0111'
);

-- 研究生教育教学质量监测_师资队伍_师资水平_每年各学院重要会议做报告数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_szsp%main-possicol0112','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw_szsp','main-possicol0112',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_szsp%main-possicol0112'
);

-- 研究生教育教学质量监测_师资队伍_师资水平_每年各学院专著发表数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_szsp%main-possicol0113','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw_szsp','main-possicol0113',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_szsp%main-possicol0113'
);

-- 研究生教育教学质量监测_师资队伍_师资水平_每年各学院专利发表数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_szsp%main-possicol0114','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw_szsp','main-possicol0114',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_szsp%main-possicol0114'
);

-- 研究生教育教学质量监测_师资队伍_师资水平_各学院重要期刊任职累计人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_szsp%main-possicol0115','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw_szsp','main-possicol0115',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_szsp%main-possicol0115'
);

-- 研究生教育教学质量监测_师资队伍_师资水平_各学院国内外重要学术组织任职累计人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_szsp%main-possicol0116','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw_szsp','main-possicol0116',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw_szsp%main-possicol0116'
);

-- 研究生教育教学质量监测_师资队伍_导师维度1-MEN_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw%main-menlabel-dswd1','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw','main-menlabel-dswd1',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw%main-menlabel-dswd1'
);

-- 研究生教育教学质量监测_师资队伍_导师维度2-MEN_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw%main-menlabel-dswd2','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw','main-menlabel-dswd2',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw%main-menlabel-dswd2'
);

-- 研究生教育教学质量监测_师资队伍_教师维度1-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw%main-tealabel-jswd1','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw','main-tealabel-jswd1',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw%main-tealabel-jswd1'
);

-- 研究生教育教学质量监测_师资队伍_人才称号分类-TEA_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw%main-tealabel-rcchfl','dataset-yjsjyjxzljc','yjsjyjxzljc_szdw','main-tealabel-rcchfl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_szdw%main-tealabel-rcchfl'
);

-- 研究生教育教学质量监测_学生工作_奖学金_每学年全校奖学金覆盖率-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_jxj%main-possisch0316','dataset-yjsjyjxzljc','yjsjyjxzljc_xsgz_jxj','main-possisch0316',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_jxj%main-possisch0316'
);

-- 研究生教育教学质量监测_学生工作_奖学金_每学年各学院奖学金金额-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_jxj%main-possicol0121','dataset-yjsjyjxzljc','yjsjyjxzljc_xsgz_jxj','main-possicol0121',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_jxj%main-possicol0121'
);

-- 研究生教育教学质量监测_学生工作_奖学金_每学年各学院奖学金覆盖率-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_jxj%main-possicol0122','dataset-yjsjyjxzljc','yjsjyjxzljc_xsgz_jxj','main-possicol0122',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_jxj%main-possicol0122'
);

-- 研究生教育教学质量监测_学生工作_奖学金_每年全校各学院奖学金获奖人次-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_jxj%main-possicol0123','dataset-yjsjyjxzljc','yjsjyjxzljc_xsgz_jxj','main-possicol0123',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_jxj%main-possicol0123'
);

-- 研究生教育教学质量监测_学生工作_奖学金_研究生奖学金信息-POS_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_jxj%main-abdposyjsjxjxx','dataset-yjsjyjxzljc','yjsjyjxzljc_xsgz_jxj','main-abdposyjsjxjxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_jxj%main-abdposyjsjxjxx'
);

-- 研究生教育教学质量监测_学生工作_研究生三助_每学年全校三助资助人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_yjssz%main-possisch0317','dataset-yjsjyjxzljc','yjsjyjxzljc_xsgz_yjssz','main-possisch0317',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_yjssz%main-possisch0317'
);

-- 研究生教育教学质量监测_学生工作_研究生三助_每学年各学院三助资助金额-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_yjssz%main-possicol0125','dataset-yjsjyjxzljc','yjsjyjxzljc_xsgz_yjssz','main-possicol0125',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_yjssz%main-possicol0125'
);

-- 研究生教育教学质量监测_学生工作_研究生三助_研究生三助申请信息-POS_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_yjssz%main-abdposyjsszsqxx','dataset-yjsjyjxzljc','yjsjyjxzljc_xsgz_yjssz','main-abdposyjsszsqxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_yjssz%main-abdposyjsszsqxx'
);

-- 研究生教育教学质量监测_学生工作_学生预警_各学院超学制累计人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_xsyj%main-possicol0117','dataset-yjsjyjxzljc','yjsjyjxzljc_xsgz_xsyj','main-possicol0117',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_xsyj%main-possicol0117'
);

-- 研究生教育教学质量监测_学生工作_学生预警_各学院即将超学习年限累计人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_xsyj%main-possicol0118','dataset-yjsjyjxzljc','yjsjyjxzljc_xsgz_xsyj','main-possicol0118',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_xsyj%main-possicol0118'
);

-- 研究生教育教学质量监测_学生工作_学生预警_各学院超学习年限累计人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_xsyj%main-possicol0119','dataset-yjsjyjxzljc','yjsjyjxzljc_xsgz_xsyj','main-possicol0119',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_xsyj%main-possicol0119'
);

-- 研究生教育教学质量监测_学生工作_学生预警_每学年各学院学籍异动人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_xsyj%main-possicol0120','dataset-yjsjyjxzljc','yjsjyjxzljc_xsgz_xsyj','main-possicol0120',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_xsyj%main-possicol0120'
);

-- 研究生教育教学质量监测_学生工作_学生预警_研究生学籍异动信息-POS_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_xsyj%main-abdposyjsxjydxx','dataset-yjsjyjxzljc','yjsjyjxzljc_xsgz_xsyj','main-abdposyjsxjydxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_xsyj%main-abdposyjsxjydxx'
);

-- 研究生教育教学质量监测_学生工作_助学金_每学年各学院助学金资助金额-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_zxj%main-possicol0124','dataset-yjsjyjxzljc','yjsjyjxzljc_xsgz_zxj','main-possicol0124',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_zxj%main-possicol0124'
);

-- 研究生教育教学质量监测_学生工作_助学金_研究生助学金发放信息-POS_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_zxj%main-abdposyjszxjffxx','dataset-yjsjyjxzljc','yjsjyjxzljc_xsgz_zxj','main-abdposyjszxjffxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_zxj%main-abdposyjszxjffxx'
);

-- 研究生教育教学质量监测_学生工作_助学贷款_每学年各学院助学贷款人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_zxdk%main-possicol0126','dataset-yjsjyjxzljc','yjsjyjxzljc_xsgz_zxdk','main-possicol0126',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_zxdk%main-possicol0126'
);

-- 研究生教育教学质量监测_学生工作_助学贷款_研究生助学贷款信息-POS_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_zxdk%main-abdposyjszxdkxx','dataset-yjsjyjxzljc','yjsjyjxzljc_xsgz_zxdk','main-abdposyjszxdkxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_zxdk%main-abdposyjszxdkxx'
);

-- 研究生教育教学质量监测_学生工作_奖助学金_研究生三助发放信息-POS_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_jzxj%main-abdposyjsszffxx','dataset-yjsjyjxzljc','yjsjyjxzljc_xsgz_jzxj','main-abdposyjsszffxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xsgz_jzxj%main-abdposyjsszffxx'
);

-- 研究生教育教学质量监测_培养过程_在校概况_全校在籍学生累计数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_zxgk%main-possisch0322','dataset-yjsjyjxzljc','yjsjyjxzljc_pygc_zxgk','main-possisch0322',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_zxgk%main-possisch0322'
);

-- 研究生教育教学质量监测_培养过程_国际交流_全校在校留学生累计数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_gjjl%main-possisch0359','dataset-yjsjyjxzljc','yjsjyjxzljc_pygc_gjjl','main-possisch0359',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_gjjl%main-possisch0359'
);

-- 研究生教育教学质量监测_培养过程_国际交流_每年全校国家公派研究生项目数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_gjjl%main-possisch0360','dataset-yjsjyjxzljc','yjsjyjxzljc_pygc_gjjl','main-possisch0360',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_gjjl%main-possisch0360'
);

-- 研究生教育教学质量监测_培养过程_国际交流_全校竞赛获奖数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_gjjl%main-possisch0481','dataset-yjsjyjxzljc','yjsjyjxzljc_pygc_gjjl','main-possisch0481',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_gjjl%main-possisch0481'
);

-- 研究生教育教学质量监测_培养过程_国际交流_每年各学院参与国际交流人次-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_gjjl%main-possicol0184','dataset-yjsjyjxzljc','yjsjyjxzljc_pygc_gjjl','main-possicol0184',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_gjjl%main-possicol0184'
);

-- 研究生教育教学质量监测_培养过程_国际交流_研究生国际交流信息-POS_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_gjjl%main-abdposyjsgjjlxx','dataset-yjsjyjxzljc','yjsjyjxzljc_pygc_gjjl','main-abdposyjsgjjlxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_gjjl%main-abdposyjsgjjlxx'
);

-- 研究生教育教学质量监测_培养过程_学生成果_每年全校发表论文数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_xscg%main-possisch0361','dataset-yjsjyjxzljc','yjsjyjxzljc_pygc_xscg','main-possisch0361',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_xscg%main-possisch0361'
);

-- 研究生教育教学质量监测_培养过程_学生成果_每年各学院竞赛获奖数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_xscg%main-possicol0185','dataset-yjsjyjxzljc','yjsjyjxzljc_pygc_xscg','main-possicol0185',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_xscg%main-possicol0185'
);

-- 研究生教育教学质量监测_培养过程_学生成果_每年各学院发表专利数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_xscg%main-possicol0186','dataset-yjsjyjxzljc','yjsjyjxzljc_pygc_xscg','main-possicol0186',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_xscg%main-possicol0186'
);

-- 研究生教育教学质量监测_培养过程_学生成果_研究生科研竞赛获奖信息-POS_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_xscg%main-abdposyjskyjshjxx','dataset-yjsjyjxzljc','yjsjyjxzljc_pygc_xscg','main-abdposyjskyjshjxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_xscg%main-abdposyjskyjshjxx'
);

-- 研究生教育教学质量监测_培养过程_学生成果_研究生发表论文信息-POS_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_xscg%main-abdposyjsfblwxx','dataset-yjsjyjxzljc','yjsjyjxzljc_pygc_xscg','main-abdposyjsfblwxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_xscg%main-abdposyjsfblwxx'
);

-- 研究生教育教学质量监测_培养过程_学生成果_研究生发表专利信息-POS_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_xscg%main-abdposyjsfbzlxx','dataset-yjsjyjxzljc','yjsjyjxzljc_pygc_xscg','main-abdposyjsfbzlxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_xscg%main-abdposyjsfbzlxx'
);

-- 研究生教育教学质量监测_培养过程_教学评估_每学年全校研究生评教参评率-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_jxpg%main-possisch0505','dataset-yjsjyjxzljc','yjsjyjxzljc_pygc_jxpg','main-possisch0505',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_jxpg%main-possisch0505'
);

-- 研究生教育教学质量监测_培养过程_教学评估_每学年全校各课程类别评教平均分-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_jxpg%main-possisch0506','dataset-yjsjyjxzljc','yjsjyjxzljc_pygc_jxpg','main-possisch0506',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_jxpg%main-possisch0506'
);

-- 研究生教育教学质量监测_培养过程_教学评估_每学年全校任课教师评教平均分-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_jxpg%main-possisch0507','dataset-yjsjyjxzljc','yjsjyjxzljc_pygc_jxpg','main-possisch0507',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_jxpg%main-possisch0507'
);

-- 研究生教育教学质量监测_培养过程_教学评估_每学年各学院各课程类别评教平均分-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_jxpg%main-possicol0269','dataset-yjsjyjxzljc','yjsjyjxzljc_pygc_jxpg','main-possicol0269',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_jxpg%main-possicol0269'
);

-- 研究生教育教学质量监测_培养过程_教学评估_每学年各学院各专业技术职务级别任课教师评教平均分-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_jxpg%main-possicol0270','dataset-yjsjyjxzljc','yjsjyjxzljc_pygc_jxpg','main-possicol0270',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_jxpg%main-possicol0270'
);

-- 研究生教育教学质量监测_培养过程_教学评估_任课教师评教结果信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_jxpg%main-abdtearkjspjjgxx','dataset-yjsjyjxzljc','yjsjyjxzljc_pygc_jxpg','main-abdtearkjspjjgxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_jxpg%main-abdtearkjspjjgxx'
);

-- 研究生教育教学质量监测_培养过程_教学评估_课程评教结果信息-COU_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_jxpg%main-abdcoukcpjjgxx','dataset-yjsjyjxzljc','yjsjyjxzljc_pygc_jxpg','main-abdcoukcpjjgxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_jxpg%main-abdcoukcpjjgxx'
);

-- 研究生教育教学质量监测_培养过程_教学评估_教学班评教结果信息-COU_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_jxpg%main-abdcoujxbpjjgxx','dataset-yjsjyjxzljc','yjsjyjxzljc_pygc_jxpg','main-abdcoujxbpjjgxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_pygc_jxpg%main-abdcoujxbpjjgxx'
);

-- 研究生教育教学质量监测_学位过程_论文查重_每学年全校查重通过人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_lwcz%main-possisch0362','dataset-yjsjyjxzljc','yjsjyjxzljc_xwgc_lwcz','main-possisch0362',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_lwcz%main-possisch0362'
);

-- 研究生教育教学质量监测_学位过程_论文查重_每学年全校查重通过率-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_lwcz%main-possisch0363','dataset-yjsjyjxzljc','yjsjyjxzljc_xwgc_lwcz','main-possisch0363',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_lwcz%main-possisch0363'
);

-- 研究生教育教学质量监测_学位过程_论文查重_研究生重复率检测-POS_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_lwcz%main-abdposyjszfljc','dataset-yjsjyjxzljc','yjsjyjxzljc_xwgc_lwcz','main-abdposyjszfljc',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_lwcz%main-abdposyjszfljc'
);

-- 研究生教育教学质量监测_学位过程_论文评阅_每年全校硕士抽盲率-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_lwpy%main-possisch0364','dataset-yjsjyjxzljc','yjsjyjxzljc_xwgc_lwpy','main-possisch0364',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_lwpy%main-possisch0364'
);

-- 研究生教育教学质量监测_学位过程_论文评阅_每学年全校送审通过人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_lwpy%main-possisch0365','dataset-yjsjyjxzljc','yjsjyjxzljc_xwgc_lwpy','main-possisch0365',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_lwpy%main-possisch0365'
);

-- 研究生教育教学质量监测_学位过程_论文评阅_每学年全校初审通过率-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_lwpy%main-possisch0366','dataset-yjsjyjxzljc','yjsjyjxzljc_xwgc_lwpy','main-possisch0366',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_lwpy%main-possisch0366'
);

-- 研究生教育教学质量监测_学位过程_论文评阅_每学年全校初审异议率-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_lwpy%main-possisch0367','dataset-yjsjyjxzljc','yjsjyjxzljc_xwgc_lwpy','main-possisch0367',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_lwpy%main-possisch0367'
);

-- 研究生教育教学质量监测_学位过程_论文评阅_每学年全校初审优良人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_lwpy%main-possisch0369','dataset-yjsjyjxzljc','yjsjyjxzljc_xwgc_lwpy','main-possisch0369',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_lwpy%main-possisch0369'
);

-- 研究生教育教学质量监测_学位过程_论文评阅_每学年各学院初审异议率-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_lwpy%main-possicol0248','dataset-yjsjyjxzljc','yjsjyjxzljc_xwgc_lwpy','main-possicol0248',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_lwpy%main-possicol0248'
);

-- 研究生教育教学质量监测_学位过程_论文评阅_研究生抽检结果-POS_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_lwpy%main-abdposyjscjjg','dataset-yjsjyjxzljc','yjsjyjxzljc_xwgc_lwpy','main-abdposyjscjjg',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_lwpy%main-abdposyjscjjg'
);

-- 研究生教育教学质量监测_学位过程_论文评阅_研究生论文评阅-POS_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_lwpy%main-abdposyjslwpy','dataset-yjsjyjxzljc','yjsjyjxzljc_xwgc_lwpy','main-abdposyjslwpy',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_lwpy%main-abdposyjslwpy'
);

-- 研究生教育教学质量监测_学位过程_论文答辩_每学年全校答辩通过人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_lwdb%main-possisch0370','dataset-yjsjyjxzljc','yjsjyjxzljc_xwgc_lwdb','main-possisch0370',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_lwdb%main-possisch0370'
);

-- 研究生教育教学质量监测_学位过程_论文答辩_每学年全校一次答辩通过率-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_lwdb%main-possisch0371','dataset-yjsjyjxzljc','yjsjyjxzljc_xwgc_lwdb','main-possisch0371',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_lwdb%main-possisch0371'
);

-- 研究生教育教学质量监测_学位过程_论文答辩_每学年各学院一次答辩通过率-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_lwdb%main-possicol0187','dataset-yjsjyjxzljc','yjsjyjxzljc_xwgc_lwdb','main-possicol0187',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_lwdb%main-possicol0187'
);

-- 研究生教育教学质量监测_学位过程_论文答辩_研究生论文答辩-POS_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_lwdb%main-abdposyjslwdb','dataset-yjsjyjxzljc','yjsjyjxzljc_xwgc_lwdb','main-abdposyjslwdb',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_lwdb%main-abdposyjslwdb'
);

-- 研究生教育教学质量监测_学位过程_学位申请_每学年全校学位申请通过人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_xwsq%main-possisch0372','dataset-yjsjyjxzljc','yjsjyjxzljc_xwgc_xwsq','main-possisch0372',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_xwsq%main-possisch0372'
);

-- 研究生教育教学质量监测_学位过程_学位申请_研究生学位申请-POS_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_xwsq%main-abdposyjsxwsq','dataset-yjsjyjxzljc','yjsjyjxzljc_xwgc_xwsq','main-abdposyjsxwsq',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_xwsq%main-abdposyjsxwsq'
);

-- 研究生教育教学质量监测_学位过程_优秀论文_每学年全校优秀论文获得人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_yxlw%main-possisch0373','dataset-yjsjyjxzljc','yjsjyjxzljc_xwgc_yxlw','main-possisch0373',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_yxlw%main-possisch0373'
);

-- 研究生教育教学质量监测_学位过程_优秀论文_研究生优秀论文获得情况-POS_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_yxlw%main-abdposyjsyxlwhdqk','dataset-yjsjyjxzljc','yjsjyjxzljc_xwgc_yxlw','main-abdposyjsyxlwhdqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_yxlw%main-abdposyjsyxlwhdqk'
);

-- 研究生教育教学质量监测_学位过程_上会讨论_研究生上会讨论结果-POS_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_shtl%main-abdposyjsshtljg','dataset-yjsjyjxzljc','yjsjyjxzljc_xwgc_shtl','main-abdposyjsshtljg',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_xwgc_shtl%main-abdposyjsshtljg'
);

-- 研究生教育教学质量监测_科学研究_教师科研成果获奖-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_kxyj%main-abdsubjskycghj','dataset-yjsjyjxzljc','yjsjyjxzljc_kxyj','main-abdsubjskycghj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_kxyj%main-abdsubjskycghj'
);

-- 研究生教育教学质量监测_科学研究_教师发表论文-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_kxyj%main-abdsubjsfblw','dataset-yjsjyjxzljc','yjsjyjxzljc_kxyj','main-abdsubjsfblw',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_kxyj%main-abdsubjsfblw'
);

-- 研究生教育教学质量监测_科学研究_教师参加学术会议-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_kxyj%main-abdsubjscjxshy','dataset-yjsjyjxzljc','yjsjyjxzljc_kxyj','main-abdsubjscjxshy',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_kxyj%main-abdsubjscjxshy'
);

-- 研究生教育教学质量监测_科学研究_教师出版著作-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_kxyj%main-abdsubjscbzz','dataset-yjsjyjxzljc','yjsjyjxzljc_kxyj','main-abdsubjscbzz',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_kxyj%main-abdsubjscbzz'
);

-- 研究生教育教学质量监测_科学研究_教师发明专利-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_kxyj%main-abdsubjsfmzl','dataset-yjsjyjxzljc','yjsjyjxzljc_kxyj','main-abdsubjsfmzl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_kxyj%main-abdsubjsfmzl'
);

-- 研究生教育教学质量监测_科学研究_教师学术期刊任职-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_kxyj%main-abdsubjsxsqkrz','dataset-yjsjyjxzljc','yjsjyjxzljc_kxyj','main-abdsubjsxsqkrz',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_kxyj%main-abdsubjsxsqkrz'
);

-- 研究生教育教学质量监测_科学研究_教师学术团体任职-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_kxyj%main-abdsubjsxsttrz','dataset-yjsjyjxzljc','yjsjyjxzljc_kxyj','main-abdsubjsxsttrz',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_kxyj%main-abdsubjsxsttrz'
);

-- 研究生教育教学质量监测_科学研究_科研机构信息-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_kxyj%main-abdsubkyjgxx','dataset-yjsjyjxzljc','yjsjyjxzljc_kxyj','main-abdsubkyjgxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_kxyj%main-abdsubkyjgxx'
);

-- 研究生教育教学质量监测_科学研究_教师参与科研项目-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_kxyj%main-abdsubjscykyxm','dataset-yjsjyjxzljc','yjsjyjxzljc_kxyj','main-abdsubjscykyxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_kxyj%main-abdsubjscykyxm'
);

-- 研究生教育教学质量监测_科学研究_学科信息-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_kxyj%main-abdsubxkxx','dataset-yjsjyjxzljc','yjsjyjxzljc_kxyj','main-abdsubxkxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_kxyj%main-abdsubxkxx'
);

-- 研究生教育教学质量监测_导师信息_导师基本信息-MEN_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_dsxx%main-abdmendsjbxx','dataset-yjsjyjxzljc','yjsjyjxzljc_dsxx','main-abdmendsjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_dsxx%main-abdmendsjbxx'
);

-- 研究生教育教学质量监测_导师信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_dsxx%main-abdteajzgjbxx','dataset-yjsjyjxzljc','yjsjyjxzljc_dsxx','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_dsxx%main-abdteajzgjbxx'
);

-- 研究生教育教学质量监测_导师信息_高层次人才信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_dsxx%main-abdteagccrcxx','dataset-yjsjyjxzljc','yjsjyjxzljc_dsxx','main-abdteagccrcxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_dsxx%main-abdteagccrcxx'
);

-- 研究生教育教学质量监测_导师信息_教职工授课记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_dsxx%main-abdteajzgskjl','dataset-yjsjyjxzljc','yjsjyjxzljc_dsxx','main-abdteajzgskjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_dsxx%main-abdteajzgskjl'
);

-- 研究生教育教学质量监测_课程_授课类型-COU_SL.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-yjsjyjxzljc%yjsjyjxzljc_kc%main-coulabel-sklx','dataset-yjsjyjxzljc','yjsjyjxzljc_kc','main-coulabel-sklx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-yjsjyjxzljc%yjsjyjxzljc_kc%main-coulabel-sklx'
);