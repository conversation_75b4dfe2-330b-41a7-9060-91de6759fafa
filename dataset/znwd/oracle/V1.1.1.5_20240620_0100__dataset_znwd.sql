/*
 Description		: [智能问答(znwd)]应用数据集
 Author				: dataapp
 Date				: 2024-06-20 01:00:00
 Lowcode Version	: V1.1.1_Beta5
 Database           : oracle
*/

-- 数据集模型字段
BEGIN
    EXECUTE IMMEDIATE 'CREATE TABLE t_da_dataset_model_column ( dataset_id VARCHAR2(50) NOT NULL,
column_id VARCHAR2(50) NOT NULL,
model_id VARCHAR2(50) NOT NULL,
id VARCHAR2(100),
create_by VARCHAR2(100),
create_time DATE,
update_by VARCHAR2(100),
update_time DATE,PRIMARY KEY (id) )';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

COMMENT ON TABLE t_da_dataset_model_column IS '数据集模型字段';
COMMENT ON COLUMN t_da_dataset_model_column.dataset_id IS '数据集ID';
COMMENT ON COLUMN t_da_dataset_model_column.column_id IS '字段ID';
COMMENT ON COLUMN t_da_dataset_model_column.model_id IS '模型ID';
COMMENT ON COLUMN t_da_dataset_model_column.id IS 'ID';
COMMENT ON COLUMN t_da_dataset_model_column.create_by IS '创建人';
COMMENT ON COLUMN t_da_dataset_model_column.create_time IS '创建时间';
COMMENT ON COLUMN t_da_dataset_model_column.update_by IS '更新人';
COMMENT ON COLUMN t_da_dataset_model_column.update_time IS '更新时间';

-- 智能问答-数据集
INSERT INTO t_da_dataset (id,sjjmc,
create_by,create_time,update_by,update_time,
copy_label,primary_app_id)
SELECT 'dataset-znwd','智能问答',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'','znwd'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset
    WHERE id = 'dataset-znwd'
);

-- dataset-znwd-znwd-数据集和应用关联表
INSERT INTO t_da_dataset_app (id,sjjid,appid,
create_by,create_time,update_by,update_time)
SELECT 'dataset-znwd-znwd','dataset-znwd','znwd',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_app
    WHERE id = 'dataset-znwd-znwd'
);

-- 公共信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'znwd_ggxx','dataset-znwd','公共信息',NULL,1,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'znwd_ggxx'
);

-- 时间-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'znwd_ggxx_sj','dataset-znwd','时间','znwd_ggxx',2,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'znwd_ggxx_sj'
);

-- 组织机构-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'znwd_ggxx_zzjg','dataset-znwd','组织机构','znwd_ggxx',3,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'znwd_ggxx_zzjg'
);

-- 地点-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'znwd_ggxx_dd','dataset-znwd','地点','znwd_ggxx',4,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'znwd_ggxx_dd'
);

-- 教学科研-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'znwd_ggxx_jxky','dataset-znwd','教学科研','znwd_ggxx',5,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'znwd_ggxx_jxky'
);

-- 本专科生管理-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'znwd_bzksgl','dataset-znwd','本专科生管理',NULL,6,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'znwd_bzksgl'
);

-- 奖惩情况-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'znwd_bzksgl_jcqk','dataset-znwd','奖惩情况','znwd_bzksgl',7,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'znwd_bzksgl_jcqk'
);

-- 资助情况-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'znwd_bzksgl_zzqk','dataset-znwd','资助情况','znwd_bzksgl',8,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'znwd_bzksgl_zzqk'
);

-- 政工队伍建设-PIW_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'znwd_bzksgl_zgdwjs','dataset-znwd','政工队伍建设','znwd_bzksgl',9,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'znwd_bzksgl_zgdwjs'
);

-- 基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'znwd_bzksgl_jbxx','dataset-znwd','基本信息','znwd_bzksgl',10,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'znwd_bzksgl_jbxx'
);

-- 日常事务-UND_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'znwd_bzksgl_rcsw','dataset-znwd','日常事务','znwd_bzksgl',11,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'znwd_bzksgl_rcsw'
);

-- 学生就业-UND_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'znwd_bzksgl_xsjy','dataset-znwd','学生就业','znwd_bzksgl',12,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'znwd_bzksgl_xsjy'
);

-- 教职工管理-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'znwd_jzggl','dataset-znwd','教职工管理',NULL,13,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'znwd_jzggl'
);

-- 教职工信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'znwd_jzggl_jzgxx','dataset-znwd','教职工信息','znwd_jzggl',14,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'znwd_jzggl_jzgxx'
);

-- 日常管理-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'znwd_jzggl_rcgl','dataset-znwd','日常管理','znwd_jzggl',15,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'znwd_jzggl_rcgl'
);

-- 职称岗聘-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'znwd_jzggl_zcgp','dataset-znwd','职称岗聘','znwd_jzggl',16,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'znwd_jzggl_zcgp'
);

-- 考核评价-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'znwd_jzggl_khpj','dataset-znwd','考核评价','znwd_jzggl',17,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'znwd_jzggl_khpj'
);

-- 智能问答_公共信息_时间_学年-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_ggxx_sj%main-abdschxn','dataset-znwd','znwd_ggxx_sj','main-abdschxn',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_ggxx_sj%main-abdschxn'
);

-- 智能问答_公共信息_时间_学年学期-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_ggxx_sj%main-abdschxnxq','dataset-znwd','znwd_ggxx_sj','main-abdschxnxq',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_ggxx_sj%main-abdschxnxq'
);

-- 智能问答_公共信息_组织机构_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_ggxx_zzjg%main-abdschxxjbxx','dataset-znwd','znwd_ggxx_zzjg','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_ggxx_zzjg%main-abdschxxjbxx'
);

-- 智能问答_公共信息_组织机构_学院基本信息-COL_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_ggxx_zzjg%main-abdcolxyjbxx','dataset-znwd','znwd_ggxx_zzjg','main-abdcolxyjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_ggxx_zzjg%main-abdcolxyjbxx'
);

-- 智能问答_公共信息_地点_校区基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_ggxx_dd%main-abdschxqjbxx','dataset-znwd','znwd_ggxx_dd','main-abdschxqjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_ggxx_dd%main-abdschxqjbxx'
);

-- 智能问答_公共信息_教学科研_本专科生专业信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_ggxx_jxky%main-abdschbzkszyxx','dataset-znwd','znwd_ggxx_jxky','main-abdschbzkszyxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_ggxx_jxky%main-abdschbzkszyxx'
);

-- 智能问答_公共信息_教学科研_本专科生行政班级信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_ggxx_jxky%main-abdschbzksxzbjxx','dataset-znwd','znwd_ggxx_jxky','main-abdschbzksxzbjxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_ggxx_jxky%main-abdschbzksxzbjxx'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生奖学金-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_bzksgl_jcqk%main-abdschbzksjxj','dataset-znwd','znwd_bzksgl_jcqk','main-abdschbzksjxj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_bzksgl_jcqk%main-abdschbzksjxj'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生荣誉称号-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_bzksgl_jcqk%main-abdschbzksrych','dataset-znwd','znwd_bzksgl_jcqk','main-abdschbzksrych',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_bzksgl_jcqk%main-abdschbzksrych'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生综合测评结果-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_bzksgl_jcqk%main-abdundbzkszhcpjg','dataset-znwd','znwd_bzksgl_jcqk','main-abdundbzkszhcpjg',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_bzksgl_jcqk%main-abdundbzkszhcpjg'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生获得奖学金记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_bzksgl_jcqk%main-abdundbzkshdjxjjl','dataset-znwd','znwd_bzksgl_jcqk','main-abdundbzkshdjxjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_bzksgl_jcqk%main-abdundbzkshdjxjjl'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生获得荣誉称号记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_bzksgl_jcqk%main-abdundbzkshdrychjl','dataset-znwd','znwd_bzksgl_jcqk','main-abdundbzkshdrychjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_bzksgl_jcqk%main-abdundbzkshdrychjl'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生违纪处分-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_bzksgl_jcqk%main-abdundbzkswjcf','dataset-znwd','znwd_bzksgl_jcqk','main-abdundbzkswjcf',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_bzksgl_jcqk%main-abdundbzkswjcf'
);

-- 智能问答_本专科生管理_资助情况_本专科生助学金-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_bzksgl_zzqk%main-abdschbzkszxj','dataset-znwd','znwd_bzksgl_zzqk','main-abdschbzkszxj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_bzksgl_zzqk%main-abdschbzkszxj'
);

-- 智能问答_本专科生管理_资助情况_勤工助学岗位-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_bzksgl_zzqk%main-abdschqgzxgw','dataset-znwd','znwd_bzksgl_zzqk','main-abdschqgzxgw',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_bzksgl_zzqk%main-abdschqgzxgw'
);

-- 智能问答_本专科生管理_资助情况_本专科生困难补助-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_bzksgl_zzqk%main-abdschbzksknbz','dataset-znwd','znwd_bzksgl_zzqk','main-abdschbzksknbz',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_bzksgl_zzqk%main-abdschbzksknbz'
);

-- 智能问答_本专科生管理_资助情况_本专科生困难生信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_bzksgl_zzqk%main-abdundbzksknsxx','dataset-znwd','znwd_bzksgl_zzqk','main-abdundbzksknsxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_bzksgl_zzqk%main-abdundbzksknsxx'
);

-- 智能问答_本专科生管理_资助情况_本专科生获得助学金记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_bzksgl_zzqk%main-abdundbzkshdzxjjl','dataset-znwd','znwd_bzksgl_zzqk','main-abdundbzkshdzxjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_bzksgl_zzqk%main-abdundbzkshdzxjjl'
);

-- 智能问答_本专科生管理_资助情况_本专科生助学金发放记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_bzksgl_zzqk%main-abdundbzkszxjffjl','dataset-znwd','znwd_bzksgl_zzqk','main-abdundbzkszxjffjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_bzksgl_zzqk%main-abdundbzkszxjffjl'
);

-- 智能问答_本专科生管理_资助情况_本专科生勤工助学上岗记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_bzksgl_zzqk%main-abdundbzksqgzxsgjl','dataset-znwd','znwd_bzksgl_zzqk','main-abdundbzksqgzxsgjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_bzksgl_zzqk%main-abdundbzksqgzxsgjl'
);

-- 智能问答_本专科生管理_资助情况_本专科生勤工助学报酬发放记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_bzksgl_zzqk%main-abdundbzksqgzxbcffjl','dataset-znwd','znwd_bzksgl_zzqk','main-abdundbzksqgzxbcffjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_bzksgl_zzqk%main-abdundbzksqgzxbcffjl'
);

-- 智能问答_本专科生管理_资助情况_本专科生获得困难补助记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_bzksgl_zzqk%main-abdundbzkshdknbzjl','dataset-znwd','znwd_bzksgl_zzqk','main-abdundbzkshdknbzjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_bzksgl_zzqk%main-abdundbzkshdknbzjl'
);

-- 智能问答_本专科生管理_资助情况_本专科生困难补助发放记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_bzksgl_zzqk%main-abdundbzksknbzffjl','dataset-znwd','znwd_bzksgl_zzqk','main-abdundbzksknbzffjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_bzksgl_zzqk%main-abdundbzksknbzffjl'
);

-- 智能问答_本专科生管理_资助情况_本专科生获得助学贷款记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_bzksgl_zzqk%main-abdundbzkshdzxdkjl','dataset-znwd','znwd_bzksgl_zzqk','main-abdundbzkshdzxdkjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_bzksgl_zzqk%main-abdundbzkshdzxdkjl'
);

-- 智能问答_本专科生管理_政工队伍建设_政工人员基本信息-PIW_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_bzksgl_zgdwjs%main-abdpiwzgryjbxx','dataset-znwd','znwd_bzksgl_zgdwjs','main-abdpiwzgryjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_bzksgl_zgdwjs%main-abdpiwzgryjbxx'
);

-- 智能问答_本专科生管理_政工队伍建设_政工人员获奖记录-PIW_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_bzksgl_zgdwjs%main-abdpiwzgryhjjl','dataset-znwd','znwd_bzksgl_zgdwjs','main-abdpiwzgryhjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_bzksgl_zgdwjs%main-abdpiwzgryhjjl'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_bzksgl_jbxx%main-abdundbzksjbxx','dataset-znwd','znwd_bzksgl_jbxx','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_bzksgl_jbxx%main-abdundbzksjbxx'
);

-- 智能问答_本专科生管理_基本信息_本专科生新生信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_bzksgl_jbxx%main-abdundbzksxsxx','dataset-znwd','znwd_bzksgl_jbxx','main-abdundbzksxsxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_bzksgl_jbxx%main-abdundbzksxsxx'
);

-- 智能问答_本专科生管理_日常事务_本专科生宿舍查寝信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_bzksgl_rcsw%main-abdundbzkssscqxx','dataset-znwd','znwd_bzksgl_rcsw','main-abdundbzkssscqxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_bzksgl_rcsw%main-abdundbzkssscqxx'
);

-- 智能问答_本专科生管理_日常事务_本专科生一卡通消费信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_bzksgl_rcsw%main-abdundbzksyktxfxx','dataset-znwd','znwd_bzksgl_rcsw','main-abdundbzksyktxfxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_bzksgl_rcsw%main-abdundbzksyktxfxx'
);

-- 智能问答_本专科生管理_日常事务_本专科生入伍记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_bzksgl_rcsw%main-abdundbzksrwjl','dataset-znwd','znwd_bzksgl_rcsw','main-abdundbzksrwjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_bzksgl_rcsw%main-abdundbzksrwjl'
);

-- 智能问答_本专科生管理_学生就业_本专科生就业信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_bzksgl_xsjy%main-abdundbzksjyxx','dataset-znwd','znwd_bzksgl_xsjy','main-abdundbzksjyxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_bzksgl_xsjy%main-abdundbzksjyxx'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_jzggl_jzgxx%main-abdteajzgjbxx','dataset-znwd','znwd_jzggl_jzgxx','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_jzggl_jzgxx%main-abdteajzgjbxx'
);

-- 智能问答_教职工管理_教职工信息_教职工教育经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_jzggl_jzgxx%main-abdteajzgjyjl','dataset-znwd','znwd_jzggl_jzgxx','main-abdteajzgjyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_jzggl_jzgxx%main-abdteajzgjyjl'
);

-- 智能问答_教职工管理_教职工信息_教职工工作经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_jzggl_jzgxx%main-abdteajzggzjl','dataset-znwd','znwd_jzggl_jzgxx','main-abdteajzggzjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_jzggl_jzgxx%main-abdteajzggzjl'
);

-- 智能问答_教职工管理_教职工信息_高层次人才信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_jzggl_jzgxx%main-abdteagccrcxx','dataset-znwd','znwd_jzggl_jzgxx','main-abdteagccrcxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_jzggl_jzgxx%main-abdteagccrcxx'
);

-- 智能问答_教职工管理_教职工信息_教职工兼职信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_jzggl_jzgxx%main-abdteajzgjzxx','dataset-znwd','znwd_jzggl_jzgxx','main-abdteajzgjzxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_jzggl_jzgxx%main-abdteajzgjzxx'
);

-- 智能问答_教职工管理_教职工信息_干部职务信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_jzggl_jzgxx%main-abdteagbzwxx','dataset-znwd','znwd_jzggl_jzgxx','main-abdteagbzwxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_jzggl_jzgxx%main-abdteagbzwxx'
);

-- 智能问答_教职工管理_教职工信息_教职工奖励及荣誉信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_jzggl_jzgxx%main-abdteajzgjljryxx','dataset-znwd','znwd_jzggl_jzgxx','main-abdteajzgjljryxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_jzggl_jzgxx%main-abdteajzgjljryxx'
);

-- 智能问答_教职工管理_日常管理_国内培训-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_jzggl_rcgl%main-abdteagnpx','dataset-znwd','znwd_jzggl_rcgl','main-abdteagnpx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_jzggl_rcgl%main-abdteagnpx'
);

-- 智能问答_教职工管理_日常管理_海外研修-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_jzggl_rcgl%main-abdteahwyx','dataset-znwd','znwd_jzggl_rcgl','main-abdteahwyx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_jzggl_rcgl%main-abdteahwyx'
);

-- 智能问答_教职工管理_日常管理_教职工校内调动记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_jzggl_rcgl%main-abdteajzgxnddjl','dataset-znwd','znwd_jzggl_rcgl','main-abdteajzgxnddjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_jzggl_rcgl%main-abdteajzgxnddjl'
);

-- 智能问答_教职工管理_日常管理_教职工离岗记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_jzggl_rcgl%main-abdteajzglgjl','dataset-znwd','znwd_jzggl_rcgl','main-abdteajzglgjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_jzggl_rcgl%main-abdteajzglgjl'
);

-- 智能问答_教职工管理_日常管理_教职工离校记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_jzggl_rcgl%main-abdteajzglxjl','dataset-znwd','znwd_jzggl_rcgl','main-abdteajzglxjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_jzggl_rcgl%main-abdteajzglxjl'
);

-- 智能问答_教职工管理_日常管理_教职工出国境记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_jzggl_rcgl%main-abdteajzgcgjjl','dataset-znwd','znwd_jzggl_rcgl','main-abdteajzgcgjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_jzggl_rcgl%main-abdteajzgcgjjl'
);

-- 智能问答_教职工管理_日常管理_教职工离退休记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_jzggl_rcgl%main-abdteajzgltxjl','dataset-znwd','znwd_jzggl_rcgl','main-abdteajzgltxjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_jzggl_rcgl%main-abdteajzgltxjl'
);

-- 智能问答_教职工管理_日常管理_教职工返聘记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_jzggl_rcgl%main-abdteajzgfpjl','dataset-znwd','znwd_jzggl_rcgl','main-abdteajzgfpjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_jzggl_rcgl%main-abdteajzgfpjl'
);

-- 智能问答_教职工管理_日常管理_教职工去世记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_jzggl_rcgl%main-abdteajzgqsjl','dataset-znwd','znwd_jzggl_rcgl','main-abdteajzgqsjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_jzggl_rcgl%main-abdteajzgqsjl'
);

-- 智能问答_教职工管理_职称岗聘_教职工岗位聘用记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_jzggl_zcgp%main-abdteajzggwpyjl','dataset-znwd','znwd_jzggl_zcgp','main-abdteajzggwpyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_jzggl_zcgp%main-abdteajzggwpyjl'
);

-- 智能问答_教职工管理_职称岗聘_教职工岗位等级聘用记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_jzggl_zcgp%main-abdteajzggwdjpyjl','dataset-znwd','znwd_jzggl_zcgp','main-abdteajzggwdjpyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_jzggl_zcgp%main-abdteajzggwdjpyjl'
);

-- 智能问答_教职工管理_职称岗聘_职称评聘记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_jzggl_zcgp%main-abdteazcppjl','dataset-znwd','znwd_jzggl_zcgp','main-abdteazcppjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_jzggl_zcgp%main-abdteazcppjl'
);

-- 智能问答_教职工管理_考核评价_教职工年度考核记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-znwd%znwd_jzggl_khpj%main-abdteajzgndkhjl','dataset-znwd','znwd_jzggl_khpj','main-abdteajzgndkhjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-znwd%znwd_jzggl_khpj%main-abdteajzgndkhjl'
);

-- 智能问答_公共信息_组织机构_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschxxjbxx-xxbsm','dataset-znwd','main-abdschxxjbxx-xxbsm','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschxxjbxx-xxbsm'
);

-- 智能问答_公共信息_组织机构_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschxxjbxx-xxmc','dataset-znwd','main-abdschxxjbxx-xxmc','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschxxjbxx-xxmc'
);

-- 智能问答_公共信息_组织机构_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschxxjbxx-xxywmc','dataset-znwd','main-abdschxxjbxx-xxywmc','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschxxjbxx-xxywmc'
);

-- 智能问答_公共信息_组织机构_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschxxjbxx-xxdz','dataset-znwd','main-abdschxxjbxx-xxdz','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschxxjbxx-xxdz'
);

-- 智能问答_公共信息_组织机构_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschxxjbxx-xxyzbm','dataset-znwd','main-abdschxxjbxx-xxyzbm','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschxxjbxx-xxyzbm'
);

-- 智能问答_公共信息_组织机构_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschxxjbxx-xzqh','dataset-znwd','main-abdschxxjbxx-xzqh','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschxxjbxx-xzqh'
);

-- 智能问答_公共信息_组织机构_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschxxjbxx-szdcxlx','dataset-znwd','main-abdschxxjbxx-szdcxlx','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschxxjbxx-szdcxlx'
);

-- 智能问答_公共信息_组织机构_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschxxjbxx-jxny','dataset-znwd','main-abdschxxjbxx-jxny','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschxxjbxx-jxny'
);

-- 智能问答_公共信息_组织机构_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschxxjbxx-xqr','dataset-znwd','main-abdschxxjbxx-xqr','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschxxjbxx-xqr'
);

-- 智能问答_公共信息_组织机构_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschxxjbxx-xxbxlx','dataset-znwd','main-abdschxxjbxx-xxbxlx','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschxxjbxx-xxbxlx'
);

-- 智能问答_公共信息_组织机构_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschxxjbxx-xxjbz','dataset-znwd','main-abdschxxjbxx-xxjbz','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschxxjbxx-xxjbz'
);

-- 智能问答_公共信息_组织机构_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschxxjbxx-xxzgbm','dataset-znwd','main-abdschxxjbxx-xxzgbm','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschxxjbxx-xxzgbm'
);

-- 智能问答_公共信息_组织机构_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschxxjbxx-fddbrh','dataset-znwd','main-abdschxxjbxx-fddbrh','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschxxjbxx-fddbrh'
);

-- 智能问答_公共信息_组织机构_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschxxjbxx-frzsh','dataset-znwd','main-abdschxxjbxx-frzsh','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschxxjbxx-frzsh'
);

-- 智能问答_公共信息_组织机构_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschxxjbxx-xzxm','dataset-znwd','main-abdschxxjbxx-xzxm','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschxxjbxx-xzxm'
);

-- 智能问答_公共信息_组织机构_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschxxjbxx-dwfzr','dataset-znwd','main-abdschxxjbxx-dwfzr','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschxxjbxx-dwfzr'
);

-- 智能问答_公共信息_组织机构_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschxxjbxx-zzjgm','dataset-znwd','main-abdschxxjbxx-zzjgm','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschxxjbxx-zzjgm'
);

-- 智能问答_公共信息_组织机构_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschxxjbxx-lxdh','dataset-znwd','main-abdschxxjbxx-lxdh','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschxxjbxx-lxdh'
);

-- 智能问答_公共信息_组织机构_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschxxjbxx-czdh','dataset-znwd','main-abdschxxjbxx-czdh','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschxxjbxx-czdh'
);

-- 智能问答_公共信息_组织机构_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschxxjbxx-dzxx','dataset-znwd','main-abdschxxjbxx-dzxx','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschxxjbxx-dzxx'
);

-- 智能问答_公共信息_组织机构_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschxxjbxx-xxbb','dataset-znwd','main-abdschxxjbxx-xxbb','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschxxjbxx-xxbb'
);

-- 智能问答_公共信息_组织机构_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschxxjbxx-xxxz','dataset-znwd','main-abdschxxjbxx-xxxz','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschxxjbxx-xxxz'
);

-- 智能问答_公共信息_组织机构_学院基本信息-COL_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdcolxyjbxx-xybh','dataset-znwd','main-abdcolxyjbxx-xybh','main-abdcolxyjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdcolxyjbxx-xybh'
);

-- 智能问答_公共信息_组织机构_学院基本信息-COL_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdcolxyjbxx-xymc','dataset-znwd','main-abdcolxyjbxx-xymc','main-abdcolxyjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdcolxyjbxx-xymc'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生奖学金-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschbzksjxj-jxjbm','dataset-znwd','main-abdschbzksjxj-jxjbm','main-abdschbzksjxj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschbzksjxj-jxjbm'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生奖学金-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschbzksjxj-xnxq','dataset-znwd','main-abdschbzksjxj-xnxq','main-abdschbzksjxj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschbzksjxj-xnxq'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生奖学金-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschbzksjxj-jxjmc','dataset-znwd','main-abdschbzksjxj-jxjmc','main-abdschbzksjxj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschbzksjxj-jxjmc'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生奖学金-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschbzksjxj-jxjdj','dataset-znwd','main-abdschbzksjxj-jxjdj','main-abdschbzksjxj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschbzksjxj-jxjdj'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生奖学金-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschbzksjxj-jxjlx','dataset-znwd','main-abdschbzksjxj-jxjlx','main-abdschbzksjxj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschbzksjxj-jxjlx'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生奖学金-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschbzksjxj-jljb','dataset-znwd','main-abdschbzksjxj-jljb','main-abdschbzksjxj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschbzksjxj-jljb'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生奖学金-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschbzksjxj-sldwhgr','dataset-znwd','main-abdschbzksjxj-sldwhgr','main-abdschbzksjxj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschbzksjxj-sldwhgr'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生奖学金-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschbzksjxj-zjly','dataset-znwd','main-abdschbzksjxj-zjly','main-abdschbzksjxj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschbzksjxj-zjly'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生奖学金-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschbzksjxj-jlje','dataset-znwd','main-abdschbzksjxj-jlje','main-abdschbzksjxj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschbzksjxj-jlje'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生荣誉称号-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschbzksrych-rychbm','dataset-znwd','main-abdschbzksrych-rychbm','main-abdschbzksrych',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschbzksrych-rychbm'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生荣誉称号-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschbzksrych-rychmc','dataset-znwd','main-abdschbzksrych-rychmc','main-abdschbzksrych',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschbzksrych-rychmc'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生荣誉称号-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschbzksrych-sldw','dataset-znwd','main-abdschbzksrych-sldw','main-abdschbzksrych',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschbzksrych-sldw'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生荣誉称号-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschbzksrych-rychlx','dataset-znwd','main-abdschbzksrych-rychlx','main-abdschbzksrych',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschbzksrych-rychlx'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生荣誉称号-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschbzksrych-jljb','dataset-znwd','main-abdschbzksrych-jljb','main-abdschbzksrych',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschbzksrych-jljb'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生荣誉称号-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdschbzksrych-xnxq','dataset-znwd','main-abdschbzksrych-xnxq','main-abdschbzksrych',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdschbzksrych-xnxq'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生综合测评结果-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkszhcpjg-xh','dataset-znwd','main-abdundbzkszhcpjg-xh','main-abdundbzkszhcpjg',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkszhcpjg-xh'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生综合测评结果-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkszhcpjg-xnxq','dataset-znwd','main-abdundbzkszhcpjg-xnxq','main-abdundbzkszhcpjg',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkszhcpjg-xnxq'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生综合测评结果-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkszhcpjg-cpzcj','dataset-znwd','main-abdundbzkszhcpjg-cpzcj','main-abdundbzkszhcpjg',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkszhcpjg-cpzcj'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生综合测评结果-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkszhcpjg-dj','dataset-znwd','main-abdundbzkszhcpjg-dj','main-abdundbzkszhcpjg',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkszhcpjg-dj'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生综合测评结果-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkszhcpjg-cpzcjpm','dataset-znwd','main-abdundbzkszhcpjg-cpzcjpm','main-abdundbzkszhcpjg',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkszhcpjg-cpzcjpm'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生综合测评结果-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkszhcpjg-cpzcjbjpm','dataset-znwd','main-abdundbzkszhcpjg-cpzcjbjpm','main-abdundbzkszhcpjg',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkszhcpjg-cpzcjbjpm'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生综合测评结果-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkszhcpjg-cpzcjzypm','dataset-znwd','main-abdundbzkszhcpjg-cpzcjzypm','main-abdundbzkszhcpjg',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkszhcpjg-cpzcjzypm'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生获得奖学金记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdjxjjl-xh','dataset-znwd','main-abdundbzkshdjxjjl-xh','main-abdundbzkshdjxjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdjxjjl-xh'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生获得奖学金记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdjxjjl-jxjbm','dataset-znwd','main-abdundbzkshdjxjjl-jxjbm','main-abdundbzkshdjxjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdjxjjl-jxjbm'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生获得奖学金记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdjxjjl-jxjmc','dataset-znwd','main-abdundbzkshdjxjjl-jxjmc','main-abdundbzkshdjxjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdjxjjl-jxjmc'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生获得奖学金记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdjxjjl-jxjlx','dataset-znwd','main-abdundbzkshdjxjjl-jxjlx','main-abdundbzkshdjxjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdjxjjl-jxjlx'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生获得奖学金记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdjxjjl-jljb','dataset-znwd','main-abdundbzkshdjxjjl-jljb','main-abdundbzkshdjxjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdjxjjl-jljb'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生获得奖学金记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdjxjjl-jxjdj','dataset-znwd','main-abdundbzkshdjxjjl-jxjdj','main-abdundbzkshdjxjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdjxjjl-jxjdj'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生获得奖学金记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdjxjjl-xnxq','dataset-znwd','main-abdundbzkshdjxjjl-xnxq','main-abdundbzkshdjxjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdjxjjl-xnxq'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生获得奖学金记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdjxjjl-hjje','dataset-znwd','main-abdundbzkshdjxjjl-hjje','main-abdundbzkshdjxjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdjxjjl-hjje'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生获得奖学金记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdjxjjl-ffrq','dataset-znwd','main-abdundbzkshdjxjjl-ffrq','main-abdundbzkshdjxjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdjxjjl-ffrq'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生获得奖学金记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdjxjjl-ffje','dataset-znwd','main-abdundbzkshdjxjjl-ffje','main-abdundbzkshdjxjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdjxjjl-ffje'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生获得荣誉称号记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdrychjl-xh','dataset-znwd','main-abdundbzkshdrychjl-xh','main-abdundbzkshdrychjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdrychjl-xh'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生获得荣誉称号记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdrychjl-rychbm','dataset-znwd','main-abdundbzkshdrychjl-rychbm','main-abdundbzkshdrychjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdrychjl-rychbm'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生获得荣誉称号记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdrychjl-rychmc','dataset-znwd','main-abdundbzkshdrychjl-rychmc','main-abdundbzkshdrychjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdrychjl-rychmc'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生获得荣誉称号记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdrychjl-sldw','dataset-znwd','main-abdundbzkshdrychjl-sldw','main-abdundbzkshdrychjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdrychjl-sldw'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生获得荣誉称号记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdrychjl-rychlx','dataset-znwd','main-abdundbzkshdrychjl-rychlx','main-abdundbzkshdrychjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdrychjl-rychlx'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生获得荣誉称号记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdrychjl-jljb','dataset-znwd','main-abdundbzkshdrychjl-jljb','main-abdundbzkshdrychjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdrychjl-jljb'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生获得荣誉称号记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdrychjl-xnxq','dataset-znwd','main-abdundbzkshdrychjl-xnxq','main-abdundbzkshdrychjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdrychjl-xnxq'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生获得荣誉称号记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdrychjl-hjje','dataset-znwd','main-abdundbzkshdrychjl-hjje','main-abdundbzkshdrychjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdrychjl-hjje'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生违纪处分-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkswjcf-xh','dataset-znwd','main-abdundbzkswjcf-xh','main-abdundbzkswjcf',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkswjcf-xh'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生违纪处分-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkswjcf-wjlb','dataset-znwd','main-abdundbzkswjcf-wjlb','main-abdundbzkswjcf',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkswjcf-wjlb'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生违纪处分-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkswjcf-xnxq','dataset-znwd','main-abdundbzkswjcf-xnxq','main-abdundbzkswjcf',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkswjcf-xnxq'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生违纪处分-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkswjcf-wjrq','dataset-znwd','main-abdundbzkswjcf-wjrq','main-abdundbzkswjcf',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkswjcf-wjrq'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生违纪处分-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkswjcf-wjqkms','dataset-znwd','main-abdundbzkswjcf-wjqkms','main-abdundbzkswjcf',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkswjcf-wjqkms'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生违纪处分-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkswjcf-cfmc','dataset-znwd','main-abdundbzkswjcf-cfmc','main-abdundbzkswjcf',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkswjcf-cfmc'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生违纪处分-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkswjcf-cfyy','dataset-znwd','main-abdundbzkswjcf-cfyy','main-abdundbzkswjcf',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkswjcf-cfyy'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生违纪处分-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkswjcf-cfwh','dataset-znwd','main-abdundbzkswjcf-cfwh','main-abdundbzkswjcf',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkswjcf-cfwh'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生违纪处分-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkswjcf-ckqy','dataset-znwd','main-abdundbzkswjcf-ckqy','main-abdundbzkswjcf',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkswjcf-ckqy'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生违纪处分-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkswjcf-ckjzrq','dataset-znwd','main-abdundbzkswjcf-ckjzrq','main-abdundbzkswjcf',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkswjcf-ckjzrq'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生违纪处分-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkswjcf-ssrq','dataset-znwd','main-abdundbzkswjcf-ssrq','main-abdundbzkswjcf',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkswjcf-ssrq'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生违纪处分-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkswjcf-ssjg','dataset-znwd','main-abdundbzkswjcf-ssjg','main-abdundbzkswjcf',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkswjcf-ssjg'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生违纪处分-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkswjcf-cfsfcx','dataset-znwd','main-abdundbzkswjcf-cfsfcx','main-abdundbzkswjcf',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkswjcf-cfsfcx'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生违纪处分-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkswjcf-cfcxrq','dataset-znwd','main-abdundbzkswjcf-cfcxrq','main-abdundbzkswjcf',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkswjcf-cfcxrq'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生违纪处分-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkswjcf-cfcxwh','dataset-znwd','main-abdundbzkswjcf-cfcxwh','main-abdundbzkswjcf',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkswjcf-cfcxwh'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生违纪处分-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkswjcf-cfcxyy','dataset-znwd','main-abdundbzkswjcf-cfcxyy','main-abdundbzkswjcf',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkswjcf-cfcxyy'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生违纪处分-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkswjcf-cfsfjc','dataset-znwd','main-abdundbzkswjcf-cfsfjc','main-abdundbzkswjcf',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkswjcf-cfsfjc'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生违纪处分-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkswjcf-cfjcrq','dataset-znwd','main-abdundbzkswjcf-cfjcrq','main-abdundbzkswjcf',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkswjcf-cfjcrq'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生违纪处分-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkswjcf-cfjcwh','dataset-znwd','main-abdundbzkswjcf-cfjcwh','main-abdundbzkswjcf',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkswjcf-cfjcwh'
);

-- 智能问答_本专科生管理_奖惩情况_本专科生违纪处分-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkswjcf-cfjcyy','dataset-znwd','main-abdundbzkswjcf-cfjcyy','main-abdundbzkswjcf',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkswjcf-cfjcyy'
);

-- 智能问答_本专科生管理_资助情况_本专科生困难生信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksknsxx-xh','dataset-znwd','main-abdundbzksknsxx-xh','main-abdundbzksknsxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksknsxx-xh'
);

-- 智能问答_本专科生管理_资助情况_本专科生困难生信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksknsxx-rdxn','dataset-znwd','main-abdundbzksknsxx-rdxn','main-abdundbzksknsxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksknsxx-rdxn'
);

-- 智能问答_本专科生管理_资助情况_本专科生困难生信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksknsxx-rdrq','dataset-znwd','main-abdundbzksknsxx-rdrq','main-abdundbzksknsxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksknsxx-rdrq'
);

-- 智能问答_本专科生管理_资助情况_本专科生困难生信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksknsxx-rdknslx','dataset-znwd','main-abdundbzksknsxx-rdknslx','main-abdundbzksknsxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksknsxx-rdknslx'
);

-- 智能问答_本专科生管理_资助情况_本专科生获得助学金记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdzxjjl-xh','dataset-znwd','main-abdundbzkshdzxjjl-xh','main-abdundbzkshdzxjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdzxjjl-xh'
);

-- 智能问答_本专科生管理_资助情况_本专科生获得助学金记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdzxjjl-zxjbm','dataset-znwd','main-abdundbzkshdzxjjl-zxjbm','main-abdundbzkshdzxjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdzxjjl-zxjbm'
);

-- 智能问答_本专科生管理_资助情况_本专科生获得助学金记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdzxjjl-zxjmc','dataset-znwd','main-abdundbzkshdzxjjl-zxjmc','main-abdundbzkshdzxjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdzxjjl-zxjmc'
);

-- 智能问答_本专科生管理_资助情况_本专科生获得助学金记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdzxjjl-jljb','dataset-znwd','main-abdundbzkshdzxjjl-jljb','main-abdundbzkshdzxjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdzxjjl-jljb'
);

-- 智能问答_本专科生管理_资助情况_本专科生获得助学金记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdzxjjl-zxjdj','dataset-znwd','main-abdundbzkshdzxjjl-zxjdj','main-abdundbzkshdzxjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdzxjjl-zxjdj'
);

-- 智能问答_本专科生管理_资助情况_本专科生获得助学金记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdzxjjl-zxje','dataset-znwd','main-abdundbzkshdzxjjl-zxje','main-abdundbzkshdzxjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdzxjjl-zxje'
);

-- 智能问答_本专科生管理_资助情况_本专科生获得助学金记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdzxjjl-zzdwhgr','dataset-znwd','main-abdundbzkshdzxjjl-zzdwhgr','main-abdundbzkshdzxjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdzxjjl-zzdwhgr'
);

-- 智能问答_本专科生管理_资助情况_本专科生获得助学金记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdzxjjl-zjly','dataset-znwd','main-abdundbzkshdzxjjl-zjly','main-abdundbzkshdzxjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdzxjjl-zjly'
);

-- 智能问答_本专科生管理_资助情况_本专科生获得助学金记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdzxjjl-xnxq','dataset-znwd','main-abdundbzkshdzxjjl-xnxq','main-abdundbzkshdzxjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdzxjjl-xnxq'
);

-- 智能问答_本专科生管理_资助情况_本专科生助学金发放记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkszxjffjl-xh','dataset-znwd','main-abdundbzkszxjffjl-xh','main-abdundbzkszxjffjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkszxjffjl-xh'
);

-- 智能问答_本专科生管理_资助情况_本专科生助学金发放记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkszxjffjl-zxjbm','dataset-znwd','main-abdundbzkszxjffjl-zxjbm','main-abdundbzkszxjffjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkszxjffjl-zxjbm'
);

-- 智能问答_本专科生管理_资助情况_本专科生助学金发放记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkszxjffjl-zxjmc','dataset-znwd','main-abdundbzkszxjffjl-zxjmc','main-abdundbzkszxjffjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkszxjffjl-zxjmc'
);

-- 智能问答_本专科生管理_资助情况_本专科生助学金发放记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkszxjffjl-zzdwhgr','dataset-znwd','main-abdundbzkszxjffjl-zzdwhgr','main-abdundbzkszxjffjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkszxjffjl-zzdwhgr'
);

-- 智能问答_本专科生管理_资助情况_本专科生助学金发放记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkszxjffjl-zjly','dataset-znwd','main-abdundbzkszxjffjl-zjly','main-abdundbzkszxjffjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkszxjffjl-zjly'
);

-- 智能问答_本专科生管理_资助情况_本专科生助学金发放记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkszxjffjl-jljb','dataset-znwd','main-abdundbzkszxjffjl-jljb','main-abdundbzkszxjffjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkszxjffjl-jljb'
);

-- 智能问答_本专科生管理_资助情况_本专科生助学金发放记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkszxjffjl-zxjdj','dataset-znwd','main-abdundbzkszxjffjl-zxjdj','main-abdundbzkszxjffjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkszxjffjl-zxjdj'
);

-- 智能问答_本专科生管理_资助情况_本专科生助学金发放记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkszxjffjl-xnxq','dataset-znwd','main-abdundbzkszxjffjl-xnxq','main-abdundbzkszxjffjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkszxjffjl-xnxq'
);

-- 智能问答_本专科生管理_资助情况_本专科生助学金发放记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkszxjffjl-ffrq','dataset-znwd','main-abdundbzkszxjffjl-ffrq','main-abdundbzkszxjffjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkszxjffjl-ffrq'
);

-- 智能问答_本专科生管理_资助情况_本专科生助学金发放记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkszxjffjl-ffje','dataset-znwd','main-abdundbzkszxjffjl-ffje','main-abdundbzkszxjffjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkszxjffjl-ffje'
);

-- 智能问答_本专科生管理_资助情况_本专科生勤工助学上岗记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksqgzxsgjl-xh','dataset-znwd','main-abdundbzksqgzxsgjl-xh','main-abdundbzksqgzxsgjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksqgzxsgjl-xh'
);

-- 智能问答_本专科生管理_资助情况_本专科生勤工助学上岗记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksqgzxsgjl-gwbm','dataset-znwd','main-abdundbzksqgzxsgjl-gwbm','main-abdundbzksqgzxsgjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksqgzxsgjl-gwbm'
);

-- 智能问答_本专科生管理_资助情况_本专科生勤工助学上岗记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksqgzxsgjl-gwmc','dataset-znwd','main-abdundbzksqgzxsgjl-gwmc','main-abdundbzksqgzxsgjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksqgzxsgjl-gwmc'
);

-- 智能问答_本专科生管理_资助情况_本专科生勤工助学上岗记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksqgzxsgjl-gwlx','dataset-znwd','main-abdundbzksqgzxsgjl-gwlx','main-abdundbzksqgzxsgjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksqgzxsgjl-gwlx'
);

-- 智能问答_本专科生管理_资助情况_本专科生勤工助学上岗记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksqgzxsgjl-ygdw','dataset-znwd','main-abdundbzksqgzxsgjl-ygdw','main-abdundbzksqgzxsgjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksqgzxsgjl-ygdw'
);

-- 智能问答_本专科生管理_资助情况_本专科生勤工助学上岗记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksqgzxsgjl-gzksrq','dataset-znwd','main-abdundbzksqgzxsgjl-gzksrq','main-abdundbzksqgzxsgjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksqgzxsgjl-gzksrq'
);

-- 智能问答_本专科生管理_资助情况_本专科生勤工助学上岗记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksqgzxsgjl-gzjsrq','dataset-znwd','main-abdundbzksqgzxsgjl-gzjsrq','main-abdundbzksqgzxsgjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksqgzxsgjl-gzjsrq'
);

-- 智能问答_本专科生管理_资助情况_本专科生勤工助学上岗记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksqgzxsgjl-gs','dataset-znwd','main-abdundbzksqgzxsgjl-gs','main-abdundbzksqgzxsgjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksqgzxsgjl-gs'
);

-- 智能问答_本专科生管理_资助情况_本专科生勤工助学上岗记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksqgzxsgjl-zgzt','dataset-znwd','main-abdundbzksqgzxsgjl-zgzt','main-abdundbzksqgzxsgjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksqgzxsgjl-zgzt'
);

-- 智能问答_本专科生管理_资助情况_本专科生勤工助学报酬发放记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksqgzxbcffjl-xh','dataset-znwd','main-abdundbzksqgzxbcffjl-xh','main-abdundbzksqgzxbcffjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksqgzxbcffjl-xh'
);

-- 智能问答_本专科生管理_资助情况_本专科生勤工助学报酬发放记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksqgzxbcffjl-gwmc','dataset-znwd','main-abdundbzksqgzxbcffjl-gwmc','main-abdundbzksqgzxbcffjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksqgzxbcffjl-gwmc'
);

-- 智能问答_本专科生管理_资助情况_本专科生勤工助学报酬发放记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksqgzxbcffjl-gwlx','dataset-znwd','main-abdundbzksqgzxbcffjl-gwlx','main-abdundbzksqgzxbcffjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksqgzxbcffjl-gwlx'
);

-- 智能问答_本专科生管理_资助情况_本专科生勤工助学报酬发放记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksqgzxbcffjl-gwbm','dataset-znwd','main-abdundbzksqgzxbcffjl-gwbm','main-abdundbzksqgzxbcffjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksqgzxbcffjl-gwbm'
);

-- 智能问答_本专科生管理_资助情况_本专科生勤工助学报酬发放记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksqgzxbcffjl-sfje','dataset-znwd','main-abdundbzksqgzxbcffjl-sfje','main-abdundbzksqgzxbcffjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksqgzxbcffjl-sfje'
);

-- 智能问答_本专科生管理_资助情况_本专科生勤工助学报酬发放记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksqgzxbcffjl-ffrq','dataset-znwd','main-abdundbzksqgzxbcffjl-ffrq','main-abdundbzksqgzxbcffjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksqgzxbcffjl-ffrq'
);

-- 智能问答_本专科生管理_资助情况_本专科生获得困难补助记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdknbzjl-xh','dataset-znwd','main-abdundbzkshdknbzjl-xh','main-abdundbzkshdknbzjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdknbzjl-xh'
);

-- 智能问答_本专科生管理_资助情况_本专科生获得困难补助记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdknbzjl-knbzbm','dataset-znwd','main-abdundbzkshdknbzjl-knbzbm','main-abdundbzkshdknbzjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdknbzjl-knbzbm'
);

-- 智能问答_本专科生管理_资助情况_本专科生获得困难补助记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdknbzjl-knbzmc','dataset-znwd','main-abdundbzkshdknbzjl-knbzmc','main-abdundbzkshdknbzjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdknbzjl-knbzmc'
);

-- 智能问答_本专科生管理_资助情况_本专科生获得困难补助记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdknbzjl-knbzdj','dataset-znwd','main-abdundbzkshdknbzjl-knbzdj','main-abdundbzkshdknbzjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdknbzjl-knbzdj'
);

-- 智能问答_本专科生管理_资助情况_本专科生获得困难补助记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdknbzjl-zjly','dataset-znwd','main-abdundbzkshdknbzjl-zjly','main-abdundbzkshdknbzjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdknbzjl-zjly'
);

-- 智能问答_本专科生管理_资助情况_本专科生获得困难补助记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdknbzjl-xnxq','dataset-znwd','main-abdundbzkshdknbzjl-xnxq','main-abdundbzkshdknbzjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdknbzjl-xnxq'
);

-- 智能问答_本专科生管理_资助情况_本专科生获得困难补助记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdknbzjl-bzje','dataset-znwd','main-abdundbzkshdknbzjl-bzje','main-abdundbzkshdknbzjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdknbzjl-bzje'
);

-- 智能问答_本专科生管理_资助情况_本专科生困难补助发放记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksknbzffjl-xh','dataset-znwd','main-abdundbzksknbzffjl-xh','main-abdundbzksknbzffjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksknbzffjl-xh'
);

-- 智能问答_本专科生管理_资助情况_本专科生困难补助发放记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksknbzffjl-knbzbm','dataset-znwd','main-abdundbzksknbzffjl-knbzbm','main-abdundbzksknbzffjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksknbzffjl-knbzbm'
);

-- 智能问答_本专科生管理_资助情况_本专科生困难补助发放记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksknbzffjl-knbzmc','dataset-znwd','main-abdundbzksknbzffjl-knbzmc','main-abdundbzksknbzffjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksknbzffjl-knbzmc'
);

-- 智能问答_本专科生管理_资助情况_本专科生困难补助发放记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksknbzffjl-knbzdj','dataset-znwd','main-abdundbzksknbzffjl-knbzdj','main-abdundbzksknbzffjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksknbzffjl-knbzdj'
);

-- 智能问答_本专科生管理_资助情况_本专科生困难补助发放记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksknbzffjl-zjly','dataset-znwd','main-abdundbzksknbzffjl-zjly','main-abdundbzksknbzffjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksknbzffjl-zjly'
);

-- 智能问答_本专科生管理_资助情况_本专科生困难补助发放记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksknbzffjl-xnxq','dataset-znwd','main-abdundbzksknbzffjl-xnxq','main-abdundbzksknbzffjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksknbzffjl-xnxq'
);

-- 智能问答_本专科生管理_资助情况_本专科生困难补助发放记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksknbzffjl-ffrq','dataset-znwd','main-abdundbzksknbzffjl-ffrq','main-abdundbzksknbzffjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksknbzffjl-ffrq'
);

-- 智能问答_本专科生管理_资助情况_本专科生困难补助发放记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksknbzffjl-ffje','dataset-znwd','main-abdundbzksknbzffjl-ffje','main-abdundbzksknbzffjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksknbzffjl-ffje'
);

-- 智能问答_本专科生管理_资助情况_本专科生获得助学贷款记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdzxdkjl-xh','dataset-znwd','main-abdundbzkshdzxdkjl-xh','main-abdundbzkshdzxdkjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdzxdkjl-xh'
);

-- 智能问答_本专科生管理_资助情况_本专科生获得助学贷款记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdzxdkjl-jbxn','dataset-znwd','main-abdundbzkshdzxdkjl-jbxn','main-abdundbzkshdzxdkjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdzxdkjl-jbxn'
);

-- 智能问答_本专科生管理_资助情况_本专科生获得助学贷款记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdzxdkjl-dklx','dataset-znwd','main-abdundbzkshdzxdkjl-dklx','main-abdundbzkshdzxdkjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdzxdkjl-dklx'
);

-- 智能问答_本专科生管理_资助情况_本专科生获得助学贷款记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdzxdkjl-dkhth','dataset-znwd','main-abdundbzkshdzxdkjl-dkhth','main-abdundbzkshdzxdkjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdzxdkjl-dkhth'
);

-- 智能问答_本专科生管理_资助情况_本专科生获得助学贷款记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdzxdkjl-dkze','dataset-znwd','main-abdundbzkshdzxdkjl-dkze','main-abdundbzkshdzxdkjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdzxdkjl-dkze'
);

-- 智能问答_本专科生管理_资助情况_本专科生获得助学贷款记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkshdzxdkjl-dknx','dataset-znwd','main-abdundbzkshdzxdkjl-dknx','main-abdundbzkshdzxdkjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkshdzxdkjl-dknx'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-xh','dataset-znwd','main-abdundbzksjbxx-xh','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-xh'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-xm','dataset-znwd','main-abdundbzksjbxx-xm','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-xm'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-xmpy','dataset-znwd','main-abdundbzksjbxx-xmpy','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-xmpy'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-cym','dataset-znwd','main-abdundbzksjbxx-cym','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-cym'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-xb','dataset-znwd','main-abdundbzksjbxx-xb','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-xb'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-nl','dataset-znwd','main-abdundbzksjbxx-nl','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-nl'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-xx','dataset-znwd','main-abdundbzksjbxx-xx','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-xx'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-csrq','dataset-znwd','main-abdundbzksjbxx-csrq','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-csrq'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-csd','dataset-znwd','main-abdundbzksjbxx-csd','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-csd'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-jg','dataset-znwd','main-abdundbzksjbxx-jg','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-jg'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-gjdq','dataset-znwd','main-abdundbzksjbxx-gjdq','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-gjdq'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-mz','dataset-znwd','main-abdundbzksjbxx-mz','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-mz'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-sfzjlx','dataset-znwd','main-abdundbzksjbxx-sfzjlx','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-sfzjlx'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-hyzk','dataset-znwd','main-abdundbzksjbxx-hyzk','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-hyzk'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-jkzk','dataset-znwd','main-abdundbzksjbxx-jkzk','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-jkzk'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-gatqw','dataset-znwd','main-abdundbzksjbxx-gatqw','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-gatqw'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-zjxy','dataset-znwd','main-abdundbzksjbxx-zjxy','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-zjxy'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-zzmm','dataset-znwd','main-abdundbzksjbxx-zzmm','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-zzmm'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-sstzz','dataset-znwd','main-abdundbzksjbxx-sstzz','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-sstzz'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-ssdzz','dataset-znwd','main-abdundbzksjbxx-ssdzz','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-ssdzz'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-yktkh','dataset-znwd','main-abdundbzksjbxx-yktkh','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-yktkh'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-wlzh','dataset-znwd','main-abdundbzksjbxx-wlzh','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-wlzh'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-yxzh','dataset-znwd','main-abdundbzksjbxx-yxzh','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-yxzh'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-xqh','dataset-znwd','main-abdundbzksjbxx-xqh','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-xqh'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-xybm','dataset-znwd','main-abdundbzksjbxx-xybm','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-xybm'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-bh','dataset-znwd','main-abdundbzksjbxx-bh','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-bh'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-zybm','dataset-znwd','main-abdundbzksjbxx-zybm','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-zybm'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-nj','dataset-znwd','main-abdundbzksjbxx-nj','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-nj'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-ssq','dataset-znwd','main-abdundbzksjbxx-ssq','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-ssq'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-ssl','dataset-znwd','main-abdundbzksjbxx-ssl','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-ssl'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-ssdz','dataset-znwd','main-abdundbzksjbxx-ssdz','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-ssdz'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-rxzp','dataset-znwd','main-abdundbzksjbxx-rxzp','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-rxzp'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-xslb','dataset-znwd','main-abdundbzksjbxx-xslb','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-xslb'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-pyfs','dataset-znwd','main-abdundbzksjbxx-pyfs','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-pyfs'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-pycc','dataset-znwd','main-abdundbzksjbxx-pycc','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-pycc'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-xz','dataset-znwd','main-abdundbzksjbxx-xz','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-xz'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-yjbyrq','dataset-znwd','main-abdundbzksjbxx-yjbyrq','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-yjbyrq'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-xjzt','dataset-znwd','main-abdundbzksjbxx-xjzt','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-xjzt'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-xsdqzt','dataset-znwd','main-abdundbzksjbxx-xsdqzt','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-xsdqzt'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-dszgh','dataset-znwd','main-abdundbzksjbxx-dszgh','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-dszgh'
);

-- 智能问答_本专科生管理_基本信息_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjbxx-kzrq','dataset-znwd','main-abdundbzksjbxx-kzrq','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjbxx-kzrq'
);

-- 智能问答_本专科生管理_基本信息_本专科生新生信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksxsxx-xh','dataset-znwd','main-abdundbzksxsxx-xh','main-abdundbzksxsxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksxsxx-xh'
);

-- 智能问答_本专科生管理_基本信息_本专科生新生信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksxsxx-ksh','dataset-znwd','main-abdundbzksxsxx-ksh','main-abdundbzksxsxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksxsxx-ksh'
);

-- 智能问答_本专科生管理_基本信息_本专科生新生信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksxsxx-kslb','dataset-znwd','main-abdundbzksxsxx-kslb','main-abdundbzksxsxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksxsxx-kslb'
);

-- 智能问答_本专科生管理_基本信息_本专科生新生信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksxsxx-lqlb','dataset-znwd','main-abdundbzksxsxx-lqlb','main-abdundbzksxsxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksxsxx-lqlb'
);

-- 智能问答_本专科生管理_基本信息_本专科生新生信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksxsxx-tzsh','dataset-znwd','main-abdundbzksxsxx-tzsh','main-abdundbzksxsxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksxsxx-tzsh'
);

-- 智能问答_本专科生管理_基本信息_本专科生新生信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksxsxx-rxny','dataset-znwd','main-abdundbzksxsxx-rxny','main-abdundbzksxsxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksxsxx-rxny'
);

-- 智能问答_本专科生管理_基本信息_本专科生新生信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksxsxx-rxjj','dataset-znwd','main-abdundbzksxsxx-rxjj','main-abdundbzksxsxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksxsxx-rxjj'
);

-- 智能问答_本专科生管理_基本信息_本专科生新生信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksxsxx-rxfs','dataset-znwd','main-abdundbzksxsxx-rxfs','main-abdundbzksxsxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksxsxx-rxfs'
);

-- 智能问答_本专科生管理_基本信息_本专科生新生信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksxsxx-rxnl','dataset-znwd','main-abdundbzksxsxx-rxnl','main-abdundbzksxsxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksxsxx-rxnl'
);

-- 智能问答_本专科生管理_基本信息_本专科生新生信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksxsxx-sydsf','dataset-znwd','main-abdundbzksxsxx-sydsf','main-abdundbzksxsxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksxsxx-sydsf'
);

-- 智能问答_本专科生管理_基本信息_本专科生新生信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksxsxx-gkzf','dataset-znwd','main-abdundbzksxsxx-gkzf','main-abdundbzksxsxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksxsxx-gkzf'
);

-- 智能问答_本专科生管理_基本信息_本专科生新生信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksxsxx-hkszd','dataset-znwd','main-abdundbzksxsxx-hkszd','main-abdundbzksxsxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksxsxx-hkszd'
);

-- 智能问答_本专科生管理_基本信息_本专科生新生信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksxsxx-rxqdw','dataset-znwd','main-abdundbzksxsxx-rxqdw','main-abdundbzksxsxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksxsxx-rxqdw'
);

-- 智能问答_本专科生管理_基本信息_本专科生新生信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksxsxx-dxhwpdw','dataset-znwd','main-abdundbzksxsxx-dxhwpdw','main-abdundbzksxsxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksxsxx-dxhwpdw'
);

-- 智能问答_本专科生管理_基本信息_本专科生新生信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksxsxx-gkzp','dataset-znwd','main-abdundbzksxsxx-gkzp','main-abdundbzksxsxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksxsxx-gkzp'
);

-- 智能问答_本专科生管理_日常事务_本专科生宿舍查寝信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkssscqxx-xh','dataset-znwd','main-abdundbzkssscqxx-xh','main-abdundbzkssscqxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkssscqxx-xh'
);

-- 智能问答_本专科生管理_日常事务_本专科生宿舍查寝信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkssscqxx-xn','dataset-znwd','main-abdundbzkssscqxx-xn','main-abdundbzkssscqxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkssscqxx-xn'
);

-- 智能问答_本专科生管理_日常事务_本专科生宿舍查寝信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkssscqxx-kqrq','dataset-znwd','main-abdundbzkssscqxx-kqrq','main-abdundbzkssscqxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkssscqxx-kqrq'
);

-- 智能问答_本专科生管理_日常事务_本专科生宿舍查寝信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkssscqxx-gqlx','dataset-znwd','main-abdundbzkssscqxx-gqlx','main-abdundbzkssscqxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkssscqxx-gqlx'
);

-- 智能问答_本专科生管理_日常事务_本专科生宿舍查寝信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkssscqxx-gqsj','dataset-znwd','main-abdundbzkssscqxx-gqsj','main-abdundbzkssscqxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkssscqxx-gqsj'
);

-- 智能问答_本专科生管理_日常事务_本专科生宿舍查寝信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzkssscqxx-wgyy','dataset-znwd','main-abdundbzkssscqxx-wgyy','main-abdundbzkssscqxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzkssscqxx-wgyy'
);

-- 智能问答_本专科生管理_日常事务_本专科生一卡通消费信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksyktxfxx-xh','dataset-znwd','main-abdundbzksyktxfxx-xh','main-abdundbzksyktxfxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksyktxfxx-xh'
);

-- 智能问答_本专科生管理_日常事务_本专科生一卡通消费信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksyktxfxx-kh','dataset-znwd','main-abdundbzksyktxfxx-kh','main-abdundbzksyktxfxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksyktxfxx-kh'
);

-- 智能问答_本专科生管理_日常事务_本专科生一卡通消费信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksyktxfxx-lsh','dataset-znwd','main-abdundbzksyktxfxx-lsh','main-abdundbzksyktxfxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksyktxfxx-lsh'
);

-- 智能问答_本专科生管理_日常事务_本专科生一卡通消费信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksyktxfxx-shmc','dataset-znwd','main-abdundbzksyktxfxx-shmc','main-abdundbzksyktxfxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksyktxfxx-shmc'
);

-- 智能问答_本专科生管理_日常事务_本专科生一卡通消费信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksyktxfxx-jysj','dataset-znwd','main-abdundbzksyktxfxx-jysj','main-abdundbzksyktxfxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksyktxfxx-jysj'
);

-- 智能问答_本专科生管理_日常事务_本专科生一卡通消费信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksyktxfxx-xfje','dataset-znwd','main-abdundbzksyktxfxx-xfje','main-abdundbzksyktxfxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksyktxfxx-xfje'
);

-- 智能问答_本专科生管理_日常事务_本专科生入伍记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksrwjl-xh','dataset-znwd','main-abdundbzksrwjl-xh','main-abdundbzksrwjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksrwjl-xh'
);

-- 智能问答_本专科生管理_日常事务_本专科生入伍记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksrwjl-rwtzsbh','dataset-znwd','main-abdundbzksrwjl-rwtzsbh','main-abdundbzksrwjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksrwjl-rwtzsbh'
);

-- 智能问答_本专科生管理_日常事务_本专科生入伍记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksrwjl-rwrq','dataset-znwd','main-abdundbzksrwjl-rwrq','main-abdundbzksrwjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksrwjl-rwrq'
);

-- 智能问答_本专科生管理_日常事务_本专科生入伍记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksrwjl-rwlb','dataset-znwd','main-abdundbzksrwjl-rwlb','main-abdundbzksrwjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksrwjl-rwlb'
);

-- 智能问答_本专科生管理_日常事务_本专科生入伍记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksrwjl-rwddlb','dataset-znwd','main-abdundbzksrwjl-rwddlb','main-abdundbzksrwjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksrwjl-rwddlb'
);

-- 智能问答_本专科生管理_日常事务_本专科生入伍记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksrwjl-zbbgs','dataset-znwd','main-abdundbzksrwjl-zbbgs','main-abdundbzksrwjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksrwjl-zbbgs'
);

-- 智能问答_本专科生管理_日常事务_本专科生入伍记录-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksrwjl-bylb','dataset-znwd','main-abdundbzksrwjl-bylb','main-abdundbzksrwjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksrwjl-bylb'
);

-- 智能问答_本专科生管理_学生就业_本专科生就业信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjyxx-xh','dataset-znwd','main-abdundbzksjyxx-xh','main-abdundbzksjyxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjyxx-xh'
);

-- 智能问答_本专科生管理_学生就业_本专科生就业信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjyxx-xysbh','dataset-znwd','main-abdundbzksjyxx-xysbh','main-abdundbzksjyxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjyxx-xysbh'
);

-- 智能问答_本专科生管理_学生就业_本专科生就业信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjyxx-xyqdrq','dataset-znwd','main-abdundbzksjyxx-xyqdrq','main-abdundbzksjyxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjyxx-xyqdrq'
);

-- 智能问答_本专科生管理_学生就业_本专科生就业信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjyxx-xynx','dataset-znwd','main-abdundbzksjyxx-xynx','main-abdundbzksjyxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjyxx-xynx'
);

-- 智能问答_本专科生管理_学生就业_本专科生就业信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjyxx-jylsfs','dataset-znwd','main-abdundbzksjyxx-jylsfs','main-abdundbzksjyxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjyxx-jylsfs'
);

-- 智能问答_本专科生管理_学生就业_本专科生就业信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjyxx-byqx','dataset-znwd','main-abdundbzksjyxx-byqx','main-abdundbzksjyxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjyxx-byqx'
);

-- 智能问答_本专科生管理_学生就业_本专科生就业信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjyxx-jydwhsxxx','dataset-znwd','main-abdundbzksjyxx-jydwhsxxx','main-abdundbzksjyxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjyxx-jydwhsxxx'
);

-- 智能问答_本专科生管理_学生就业_本专科生就业信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjyxx-dwhxxszgjhdq','dataset-znwd','main-abdundbzksjyxx-dwhxxszgjhdq','main-abdundbzksjyxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjyxx-dwhxxszgjhdq'
);

-- 智能问答_本专科生管理_学生就业_本专科生就业信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjyxx-dwhxxszsf','dataset-znwd','main-abdundbzksjyxx-dwhxxszsf','main-abdundbzksjyxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjyxx-dwhxxszsf'
);

-- 智能问答_本专科生管理_学生就业_本专科生就业信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjyxx-dwhxxszcs','dataset-znwd','main-abdundbzksjyxx-dwhxxszcs','main-abdundbzksjyxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjyxx-dwhxxszcs'
);

-- 智能问答_本专科生管理_学生就业_本专科生就业信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjyxx-dwhxxzgbm','dataset-znwd','main-abdundbzksjyxx-dwhxxzgbm','main-abdundbzksjyxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjyxx-dwhxxzgbm'
);

-- 智能问答_本专科生管理_学生就业_本专科生就业信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjyxx-shdwxz','dataset-znwd','main-abdundbzksjyxx-shdwxz','main-abdundbzksjyxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjyxx-shdwxz'
);

-- 智能问答_本专科生管理_学生就业_本专科生就业信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjyxx-dwjjxz','dataset-znwd','main-abdundbzksjyxx-dwjjxz','main-abdundbzksjyxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjyxx-dwjjxz'
);

-- 智能问答_本专科生管理_学生就业_本专科生就业信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjyxx-dwtgddy','dataset-znwd','main-abdundbzksjyxx-dwtgddy','main-abdundbzksjyxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjyxx-dwtgddy'
);

-- 智能问答_本专科生管理_学生就业_本专科生就业信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjyxx-dajsdz','dataset-znwd','main-abdundbzksjyxx-dajsdz','main-abdundbzksjyxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjyxx-dajsdz'
);

-- 智能问答_本专科生管理_学生就业_本专科生就业信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjyxx-jsdyzbm','dataset-znwd','main-abdundbzksjyxx-jsdyzbm','main-abdundbzksjyxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjyxx-jsdyzbm'
);

-- 智能问答_本专科生管理_学生就业_本专科生就业信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjyxx-gzgwxz','dataset-znwd','main-abdundbzksjyxx-gzgwxz','main-abdundbzksjyxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjyxx-gzgwxz'
);

-- 智能问答_本专科生管理_学生就业_本专科生就业信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjyxx-dwxy','dataset-znwd','main-abdundbzksjyxx-dwxy','main-abdundbzksjyxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjyxx-dwxy'
);

-- 智能问答_本专科生管理_学生就业_本专科生就业信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjyxx-bdzh','dataset-znwd','main-abdundbzksjyxx-bdzh','main-abdundbzksjyxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjyxx-bdzh'
);

-- 智能问答_本专科生管理_学生就业_本专科生就业信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdundbzksjyxx-yrdwyrxs','dataset-znwd','main-abdundbzksjyxx-yrdwyrxs','main-abdundbzksjyxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdundbzksjyxx-yrdwyrxs'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-zgh','dataset-znwd','main-abdteajzgjbxx-zgh','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-zgh'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-xm','dataset-znwd','main-abdteajzgjbxx-xm','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-xm'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-xb','dataset-znwd','main-abdteajzgjbxx-xb','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-xb'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-mz','dataset-znwd','main-abdteajzgjbxx-mz','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-mz'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-sfzjlx','dataset-znwd','main-abdteajzgjbxx-sfzjlx','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-sfzjlx'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-gatqw','dataset-znwd','main-abdteajzgjbxx-gatqw','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-gatqw'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-zjxy','dataset-znwd','main-abdteajzgjbxx-zjxy','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-zjxy'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-zzmm','dataset-znwd','main-abdteajzgjbxx-zzmm','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-zzmm'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-jg','dataset-znwd','main-abdteajzgjbxx-jg','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-jg'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-hkszs','dataset-znwd','main-abdteajzgjbxx-hkszs','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-hkszs'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-hkszds','dataset-znwd','main-abdteajzgjbxx-hkszds','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-hkszds'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-hkszd','dataset-znwd','main-abdteajzgjbxx-hkszd','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-hkszd'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-xx','dataset-znwd','main-abdteajzgjbxx-xx','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-xx'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-csrq','dataset-znwd','main-abdteajzgjbxx-csrq','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-csrq'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-csd','dataset-znwd','main-abdteajzgjbxx-csd','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-csd'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-gjdq','dataset-znwd','main-abdteajzgjbxx-gjdq','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-gjdq'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-hyzk','dataset-znwd','main-abdteajzgjbxx-hyzk','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-hyzk'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-zgxl','dataset-znwd','main-abdteajzgjbxx-zgxl','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-zgxl'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-zgxw','dataset-znwd','main-abdteajzgjbxx-zgxw','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-zgxw'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-zhbyyx','dataset-znwd','main-abdteajzgjbxx-zhbyyx','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-zhbyyx'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-zhbyyxlx','dataset-znwd','main-abdteajzgjbxx-zhbyyxlx','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-zhbyyxlx'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-sfbxby','dataset-znwd','main-abdteajzgjbxx-sfbxby','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-sfbxby'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-sjhm','dataset-znwd','main-abdteajzgjbxx-sjhm','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-sjhm'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-dzyx','dataset-znwd','main-abdteajzgjbxx-dzyx','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-dzyx'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-jtdz','dataset-znwd','main-abdteajzgjbxx-jtdz','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-jtdz'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-ssjgdm','dataset-znwd','main-abdteajzgjbxx-ssjgdm','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-ssjgdm'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-jzglb','dataset-znwd','main-abdteajzgjbxx-jzglb','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-jzglb'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-jzgly','dataset-znwd','main-abdteajzgjbxx-jzgly','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-jzgly'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-bzlb','dataset-znwd','main-abdteajzgjbxx-bzlb','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-bzlb'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-yrfs','dataset-znwd','main-abdteajzgjbxx-yrfs','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-yrfs'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-cjny','dataset-znwd','main-abdteajzgjbxx-cjny','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-cjny'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-lxny','dataset-znwd','main-abdteajzgjbxx-lxny','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-lxny'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-jzjsprlb','dataset-znwd','main-abdteajzgjbxx-jzjsprlb','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-jzjsprlb'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-dslb','dataset-znwd','main-abdteajzgjbxx-dslb','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-dslb'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-fdylb','dataset-znwd','main-abdteajzgjbxx-fdylb','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-fdylb'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-sfssxjs','dataset-znwd','main-abdteajzgjbxx-sfssxjs','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-sfssxjs'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-sfsjt','dataset-znwd','main-abdteajzgjbxx-sfsjt','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-sfsjt'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-xkml','dataset-znwd','main-abdteajzgjbxx-xkml','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-xkml'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-yjxk','dataset-znwd','main-abdteajzgjbxx-yjxk','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-yjxk'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-ejxk','dataset-znwd','main-abdteajzgjbxx-ejxk','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-ejxk'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-yjfx','dataset-znwd','main-abdteajzgjbxx-yjfx','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-yjfx'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-jzgdqzt','dataset-znwd','main-abdteajzgjbxx-jzgdqzt','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-jzgdqzt'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-lxrq','dataset-znwd','main-abdteajzgjbxx-lxrq','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-lxrq'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-yjtxrq','dataset-znwd','main-abdteajzgjbxx-yjtxrq','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-yjtxrq'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-zyjszw','dataset-znwd','main-abdteajzgjbxx-zyjszw','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-zyjszw'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-zyjszwjb','dataset-znwd','main-abdteajzgjbxx-zyjszwjb','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-zyjszwjb'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-zyjsgwdj','dataset-znwd','main-abdteajzgjbxx-zyjsgwdj','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-zyjsgwdj'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-glgwdj','dataset-znwd','main-abdteajzgjbxx-glgwdj','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-glgwdj'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-gqgwdj','dataset-znwd','main-abdteajzgjbxx-gqgwdj','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-gqgwdj'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-zygwlx','dataset-znwd','main-abdteajzgjbxx-zygwlx','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-zygwlx'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-gwmc','dataset-znwd','main-abdteajzgjbxx-gwmc','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-gwmc'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-gbzw','dataset-znwd','main-abdteajzgjbxx-gbzw','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-gbzw'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-gbzwjb','dataset-znwd','main-abdteajzgjbxx-gbzwjb','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-gbzwjb'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-nl','dataset-znwd','main-abdteajzgjbxx-nl','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-nl'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-kzrq','dataset-znwd','main-abdteajzgjbxx-kzrq','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-kzrq'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-yjjgdm','dataset-znwd','main-abdteajzgjbxx-yjjgdm','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-yjjgdm'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-cjgzny','dataset-znwd','main-abdteajzgjbxx-cjgzny','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-cjgzny'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-sffdy','dataset-znwd','main-abdteajzgjbxx-sffdy','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-sffdy'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-zyjsgwlb','dataset-znwd','main-abdteajzgjbxx-zyjsgwlb','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-zyjsgwlb'
);

-- 智能问答_教职工管理_教职工信息_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjbxx-zrjslx','dataset-znwd','main-abdteajzgjbxx-zrjslx','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjbxx-zrjslx'
);

-- 智能问答_教职工管理_教职工信息_教职工教育经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjyjl-zgh','dataset-znwd','main-abdteajzgjyjl-zgh','main-abdteajzgjyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjyjl-zgh'
);

-- 智能问答_教职工管理_教职工信息_教职工教育经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjyjl-rxny','dataset-znwd','main-abdteajzgjyjl-rxny','main-abdteajzgjyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjyjl-rxny'
);

-- 智能问答_教职工管理_教职工信息_教职工教育经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjyjl-byny','dataset-znwd','main-abdteajzgjyjl-byny','main-abdteajzgjyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjyjl-byny'
);

-- 智能问答_教职工管理_教职工信息_教职工教育经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjyjl-byyxxhdw','dataset-znwd','main-abdteajzgjyjl-byyxxhdw','main-abdteajzgjyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjyjl-byyxxhdw'
);

-- 智能问答_教职工管理_教职工信息_教职工教育经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjyjl-xl','dataset-znwd','main-abdteajzgjyjl-xl','main-abdteajzgjyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjyjl-xl'
);

-- 智能问答_教职工管理_教职工信息_教职工教育经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjyjl-xw','dataset-znwd','main-abdteajzgjyjl-xw','main-abdteajzgjyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjyjl-xw'
);

-- 智能问答_教职工管理_教职工信息_教职工教育经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjyjl-sxzy','dataset-znwd','main-abdteajzgjyjl-sxzy','main-abdteajzgjyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjyjl-sxzy'
);

-- 智能问答_教职工管理_教职工信息_教职工教育经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjyjl-xwsygj','dataset-znwd','main-abdteajzgjyjl-xwsygj','main-abdteajzgjyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjyjl-xwsygj'
);

-- 智能问答_教职工管理_教职工信息_教职工教育经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjyjl-gxlb','dataset-znwd','main-abdteajzgjyjl-gxlb','main-abdteajzgjyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjyjl-gxlb'
);

-- 智能问答_教职工管理_教职工信息_教职工教育经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjyjl-xz','dataset-znwd','main-abdteajzgjyjl-xz','main-abdteajzgjyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjyjl-xz'
);

-- 智能问答_教职工管理_教职工信息_教职工工作经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzggzjl-zgh','dataset-znwd','main-abdteajzggzjl-zgh','main-abdteajzggzjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzggzjl-zgh'
);

-- 智能问答_教职工管理_教职工信息_教职工工作经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzggzjl-szdwmc','dataset-znwd','main-abdteajzggzjl-szdwmc','main-abdteajzggzjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzggzjl-szdwmc'
);

-- 智能问答_教职工管理_教职工信息_教职工工作经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzggzjl-gjdq','dataset-znwd','main-abdteajzggzjl-gjdq','main-abdteajzggzjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzggzjl-gjdq'
);

-- 智能问答_教职工管理_教职工信息_教职工工作经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzggzjl-crdzzw','dataset-znwd','main-abdteajzggzjl-crdzzw','main-abdteajzggzjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzggzjl-crdzzw'
);

-- 智能问答_教职工管理_教职工信息_教职工工作经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzggzjl-crzyjszw','dataset-znwd','main-abdteajzggzjl-crzyjszw','main-abdteajzggzjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzggzjl-crzyjszw'
);

-- 智能问答_教职工管理_教职工信息_教职工工作经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzggzjl-sfhwjl','dataset-znwd','main-abdteajzggzjl-sfhwjl','main-abdteajzggzjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzggzjl-sfhwjl'
);

-- 智能问答_教职工管理_教职工信息_教职工工作经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzggzjl-qsny','dataset-znwd','main-abdteajzggzjl-qsny','main-abdteajzggzjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzggzjl-qsny'
);

-- 智能问答_教职工管理_教职工信息_教职工工作经历-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzggzjl-jzny','dataset-znwd','main-abdteajzggzjl-jzny','main-abdteajzggzjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzggzjl-jzny'
);

-- 智能问答_教职工管理_教职工信息_高层次人才信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteagccrcxx-zgh','dataset-znwd','main-abdteagccrcxx-zgh','main-abdteagccrcxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteagccrcxx-zgh'
);

-- 智能问答_教职工管理_教职工信息_高层次人才信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteagccrcxx-rclb','dataset-znwd','main-abdteagccrcxx-rclb','main-abdteagccrcxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteagccrcxx-rclb'
);

-- 智能问答_教职工管理_教职工信息_高层次人才信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteagccrcxx-pzdw','dataset-znwd','main-abdteagccrcxx-pzdw','main-abdteagccrcxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteagccrcxx-pzdw'
);

-- 智能问答_教职工管理_教职工信息_高层次人才信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteagccrcxx-pzdwjb','dataset-znwd','main-abdteagccrcxx-pzdwjb','main-abdteagccrcxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteagccrcxx-pzdwjb'
);

-- 智能问答_教职工管理_教职工信息_高层次人才信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteagccrcxx-pzny','dataset-znwd','main-abdteagccrcxx-pzny','main-abdteagccrcxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteagccrcxx-pzny'
);

-- 智能问答_教职工管理_教职工信息_教职工兼职信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjzxx-zgh','dataset-znwd','main-abdteajzgjzxx-zgh','main-abdteajzgjzxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjzxx-zgh'
);

-- 智能问答_教职工管理_教职工信息_教职工兼职信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjzxx-jzlx','dataset-znwd','main-abdteajzgjzxx-jzlx','main-abdteajzgjzxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjzxx-jzlx'
);

-- 智能问答_教职工管理_教职工信息_教职工兼职信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjzxx-jzdw','dataset-znwd','main-abdteajzgjzxx-jzdw','main-abdteajzgjzxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjzxx-jzdw'
);

-- 智能问答_教职工管理_教职工信息_教职工兼职信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjzxx-jzdwjb','dataset-znwd','main-abdteajzgjzxx-jzdwjb','main-abdteajzgjzxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjzxx-jzdwjb'
);

-- 智能问答_教职工管理_教职工信息_教职工兼职信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjzxx-jzzw','dataset-znwd','main-abdteajzgjzxx-jzzw','main-abdteajzgjzxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjzxx-jzzw'
);

-- 智能问答_教职工管理_教职工信息_教职工兼职信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjzxx-jzzwjb','dataset-znwd','main-abdteajzgjzxx-jzzwjb','main-abdteajzgjzxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjzxx-jzzwjb'
);

-- 智能问答_教职工管理_教职工信息_教职工兼职信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjzxx-gjdq','dataset-znwd','main-abdteajzgjzxx-gjdq','main-abdteajzgjzxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjzxx-gjdq'
);

-- 智能问答_教职工管理_教职工信息_教职工兼职信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjzxx-qsrq','dataset-znwd','main-abdteajzgjzxx-qsrq','main-abdteajzgjzxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjzxx-qsrq'
);

-- 智能问答_教职工管理_教职工信息_教职工兼职信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjzxx-zzrq','dataset-znwd','main-abdteajzgjzxx-zzrq','main-abdteajzgjzxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjzxx-zzrq'
);

-- 智能问答_教职工管理_教职工信息_干部职务信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteagbzwxx-zgh','dataset-znwd','main-abdteagbzwxx-zgh','main-abdteagbzwxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteagbzwxx-zgh'
);

-- 智能问答_教职工管理_教职工信息_干部职务信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteagbzwxx-zwmc','dataset-znwd','main-abdteagbzwxx-zwmc','main-abdteagbzwxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteagbzwxx-zwmc'
);

-- 智能问答_教职工管理_教职工信息_干部职务信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteagbzwxx-zwlb','dataset-znwd','main-abdteagbzwxx-zwlb','main-abdteagbzwxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteagbzwxx-zwlb'
);

-- 智能问答_教职工管理_教职工信息_干部职务信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteagbzwxx-zwjb','dataset-znwd','main-abdteagbzwxx-zwjb','main-abdteagbzwxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteagbzwxx-zwjb'
);

-- 智能问答_教职工管理_教职工信息_干部职务信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteagbzwxx-rzny','dataset-znwd','main-abdteagbzwxx-rzny','main-abdteagbzwxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteagbzwxx-rzny'
);

-- 智能问答_教职工管理_教职工信息_干部职务信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteagbzwxx-rzfs','dataset-znwd','main-abdteagbzwxx-rzfs','main-abdteagbzwxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteagbzwxx-rzfs'
);

-- 智能问答_教职工管理_教职工信息_干部职务信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteagbzwxx-rzqx','dataset-znwd','main-abdteagbzwxx-rzqx','main-abdteagbzwxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteagbzwxx-rzqx'
);

-- 智能问答_教职工管理_教职工信息_干部职务信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteagbzwxx-sfzr','dataset-znwd','main-abdteagbzwxx-sfzr','main-abdteagbzwxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteagbzwxx-sfzr'
);

-- 智能问答_教职工管理_教职工信息_干部职务信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteagbzwxx-mzrq','dataset-znwd','main-abdteagbzwxx-mzrq','main-abdteagbzwxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteagbzwxx-mzrq'
);

-- 智能问答_教职工管理_教职工信息_干部职务信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteagbzwxx-rzdwbm','dataset-znwd','main-abdteagbzwxx-rzdwbm','main-abdteagbzwxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteagbzwxx-rzdwbm'
);

-- 智能问答_教职工管理_教职工信息_教职工奖励及荣誉信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjljryxx-zgh','dataset-znwd','main-abdteajzgjljryxx-zgh','main-abdteajzgjljryxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjljryxx-zgh'
);

-- 智能问答_教职工管理_教职工信息_教职工奖励及荣誉信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjljryxx-jlmc','dataset-znwd','main-abdteajzgjljryxx-jlmc','main-abdteajzgjljryxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjljryxx-jlmc'
);

-- 智能问答_教职工管理_教职工信息_教职工奖励及荣誉信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjljryxx-rych','dataset-znwd','main-abdteajzgjljryxx-rych','main-abdteajzgjljryxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjljryxx-rych'
);

-- 智能问答_教职工管理_教职工信息_教职工奖励及荣誉信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjljryxx-hjrq','dataset-znwd','main-abdteajzgjljryxx-hjrq','main-abdteajzgjljryxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjljryxx-hjrq'
);

-- 智能问答_教职工管理_教职工信息_教职工奖励及荣誉信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjljryxx-jljb','dataset-znwd','main-abdteajzgjljryxx-jljb','main-abdteajzgjljryxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjljryxx-jljb'
);

-- 智能问答_教职工管理_教职工信息_教职工奖励及荣誉信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjljryxx-jldj','dataset-znwd','main-abdteajzgjljryxx-jldj','main-abdteajzgjljryxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjljryxx-jldj'
);

-- 智能问答_教职工管理_教职工信息_教职工奖励及荣誉信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjljryxx-jllb','dataset-znwd','main-abdteajzgjljryxx-jllb','main-abdteajzgjljryxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjljryxx-jllb'
);

-- 智能问答_教职工管理_教职工信息_教职工奖励及荣誉信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjljryxx-bjdw','dataset-znwd','main-abdteajzgjljryxx-bjdw','main-abdteajzgjljryxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjljryxx-bjdw'
);

-- 智能问答_教职工管理_教职工信息_教职工奖励及荣誉信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjljryxx-hjxm','dataset-znwd','main-abdteajzgjljryxx-hjxm','main-abdteajzgjljryxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjljryxx-hjxm'
);

-- 智能问答_教职工管理_教职工信息_教职工奖励及荣誉信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgjljryxx-brpm','dataset-znwd','main-abdteajzgjljryxx-brpm','main-abdteajzgjljryxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgjljryxx-brpm'
);

-- 智能问答_教职工管理_日常管理_国内培训-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteagnpx-zgh','dataset-znwd','main-abdteagnpx-zgh','main-abdteagnpx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteagnpx-zgh'
);

-- 智能问答_教职工管理_日常管理_国内培训-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteagnpx-pxxmmc','dataset-znwd','main-abdteagnpx-pxxmmc','main-abdteagnpx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteagnpx-pxxmmc'
);

-- 智能问答_教职工管理_日常管理_国内培训-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteagnpx-pxjgmc','dataset-znwd','main-abdteagnpx-pxjgmc','main-abdteagnpx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteagnpx-pxjgmc'
);

-- 智能问答_教职工管理_日常管理_国内培训-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteagnpx-pxjb','dataset-znwd','main-abdteagnpx-pxjb','main-abdteagnpx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteagnpx-pxjb'
);

-- 智能问答_教职工管理_日常管理_国内培训-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteagnpx-pxfs','dataset-znwd','main-abdteagnpx-pxfs','main-abdteagnpx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteagnpx-pxfs'
);

-- 智能问答_教职工管理_日常管理_国内培训-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteagnpx-pxhdxs','dataset-znwd','main-abdteagnpx-pxhdxs','main-abdteagnpx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteagnpx-pxhdxs'
);

-- 智能问答_教职工管理_日常管理_国内培训-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteagnpx-pxnd','dataset-znwd','main-abdteagnpx-pxnd','main-abdteagnpx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteagnpx-pxnd'
);

-- 智能问答_教职工管理_日常管理_海外研修-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteahwyx-zgh','dataset-znwd','main-abdteahwyx-zgh','main-abdteahwyx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteahwyx-zgh'
);

-- 智能问答_教职工管理_日常管理_海外研修-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteahwyx-ksrq','dataset-znwd','main-abdteahwyx-ksrq','main-abdteahwyx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteahwyx-ksrq'
);

-- 智能问答_教职工管理_日常管理_海外研修-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteahwyx-jsrq','dataset-znwd','main-abdteahwyx-jsrq','main-abdteahwyx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteahwyx-jsrq'
);

-- 智能问答_教职工管理_日常管理_海外研修-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteahwyx-gjdq','dataset-znwd','main-abdteahwyx-gjdq','main-abdteahwyx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteahwyx-gjdq'
);

-- 智能问答_教职工管理_日常管理_海外研修-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteahwyx-yxfxjg','dataset-znwd','main-abdteahwyx-yxfxjg','main-abdteahwyx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteahwyx-yxfxjg'
);

-- 智能问答_教职工管理_日常管理_海外研修-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteahwyx-xmmc','dataset-znwd','main-abdteahwyx-xmmc','main-abdteahwyx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteahwyx-xmmc'
);

-- 智能问答_教职工管理_日常管理_海外研修-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteahwyx-xmzzdw','dataset-znwd','main-abdteahwyx-xmzzdw','main-abdteahwyx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteahwyx-xmzzdw'
);

-- 智能问答_教职工管理_日常管理_教职工校内调动记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgxnddjl-zgh','dataset-znwd','main-abdteajzgxnddjl-zgh','main-abdteajzgxnddjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgxnddjl-zgh'
);

-- 智能问答_教职工管理_日常管理_教职工校内调动记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgxnddjl-ddrq','dataset-znwd','main-abdteajzgxnddjl-ddrq','main-abdteajzgxnddjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgxnddjl-ddrq'
);

-- 智能问答_教职工管理_日常管理_教职工校内调动记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgxnddjl-sfzg','dataset-znwd','main-abdteajzgxnddjl-sfzg','main-abdteajzgxnddjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgxnddjl-sfzg'
);

-- 智能问答_教职工管理_日常管理_教职工校内调动记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgxnddjl-dcgw','dataset-znwd','main-abdteajzgxnddjl-dcgw','main-abdteajzgxnddjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgxnddjl-dcgw'
);

-- 智能问答_教职工管理_日常管理_教职工校内调动记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgxnddjl-drgw','dataset-znwd','main-abdteajzgxnddjl-drgw','main-abdteajzgxnddjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgxnddjl-drgw'
);

-- 智能问答_教职工管理_日常管理_教职工校内调动记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgxnddjl-dcdwbm','dataset-znwd','main-abdteajzgxnddjl-dcdwbm','main-abdteajzgxnddjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgxnddjl-dcdwbm'
);

-- 智能问答_教职工管理_日常管理_教职工校内调动记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgxnddjl-drdwbm','dataset-znwd','main-abdteajzgxnddjl-drdwbm','main-abdteajzgxnddjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgxnddjl-drdwbm'
);

-- 智能问答_教职工管理_日常管理_教职工离岗记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzglgjl-zgh','dataset-znwd','main-abdteajzglgjl-zgh','main-abdteajzglgjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzglgjl-zgh'
);

-- 智能问答_教职工管理_日常管理_教职工离岗记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzglgjl-lgyy','dataset-znwd','main-abdteajzglgjl-lgyy','main-abdteajzglgjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzglgjl-lgyy'
);

-- 智能问答_教职工管理_日常管理_教职工离岗记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzglgjl-lgrq','dataset-znwd','main-abdteajzglgjl-lgrq','main-abdteajzglgjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzglgjl-lgrq'
);

-- 智能问答_教职工管理_日常管理_教职工离岗记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzglgjl-fgrq','dataset-znwd','main-abdteajzglgjl-fgrq','main-abdteajzglgjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzglgjl-fgrq'
);

-- 智能问答_教职工管理_日常管理_教职工离岗记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzglgjl-sqdwmc','dataset-znwd','main-abdteajzglgjl-sqdwmc','main-abdteajzglgjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzglgjl-sqdwmc'
);

-- 智能问答_教职工管理_日常管理_教职工离校记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzglxjl-zgh','dataset-znwd','main-abdteajzglxjl-zgh','main-abdteajzglxjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzglxjl-zgh'
);

-- 智能问答_教职工管理_日常管理_教职工离校记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzglxjl-lxyy','dataset-znwd','main-abdteajzglxjl-lxyy','main-abdteajzglxjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzglxjl-lxyy'
);

-- 智能问答_教职工管理_日常管理_教职工离校记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzglxjl-lxrq','dataset-znwd','main-abdteajzglxjl-lxrq','main-abdteajzglxjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzglxjl-lxrq'
);

-- 智能问答_教职工管理_日常管理_教职工离校记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzglxjl-lxqx','dataset-znwd','main-abdteajzglxjl-lxqx','main-abdteajzglxjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzglxjl-lxqx'
);

-- 智能问答_教职工管理_日常管理_教职工出国境记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgcgjjl-zgh','dataset-znwd','main-abdteajzgcgjjl-zgh','main-abdteajzgcgjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgcgjjl-zgh'
);

-- 智能问答_教职工管理_日常管理_教职工出国境记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgcgjjl-cgjgb','dataset-znwd','main-abdteajzgcgjjl-cgjgb','main-abdteajzgcgjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgcgjjl-cgjgb'
);

-- 智能问答_教职工管理_日常管理_教职工出国境记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgcgjjl-sqdwywmc','dataset-znwd','main-abdteajzgcgjjl-sqdwywmc','main-abdteajzgcgjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgcgjjl-sqdwywmc'
);

-- 智能问答_教职工管理_日常管理_教职工出国境记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgcgjjl-sqdwzwmc','dataset-znwd','main-abdteajzgcgjjl-sqdwzwmc','main-abdteajzgcgjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgcgjjl-sqdwzwmc'
);

-- 智能问答_教职工管理_日常管理_教职工出国境记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgcgjjl-tzmc','dataset-znwd','main-abdteajzgcgjjl-tzmc','main-abdteajzgcgjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgcgjjl-tzmc'
);

-- 智能问答_教职工管理_日常管理_教职工出国境记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgcgjjl-jfly','dataset-znwd','main-abdteajzgcgjjl-jfly','main-abdteajzgcgjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgcgjjl-jfly'
);

-- 智能问答_教职工管理_日常管理_教职工出国境记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgcgjjl-spdw','dataset-znwd','main-abdteajzgcgjjl-spdw','main-abdteajzgcgjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgcgjjl-spdw'
);

-- 智能问答_教职工管理_日常管理_教职工出国境记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgcgjjl-sprq','dataset-znwd','main-abdteajzgcgjjl-sprq','main-abdteajzgcgjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgcgjjl-sprq'
);

-- 智能问答_教职工管理_日常管理_教职工出国境记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgcgjjl-spwh','dataset-znwd','main-abdteajzgcgjjl-spwh','main-abdteajzgcgjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgcgjjl-spwh'
);

-- 智能问答_教职工管理_日常管理_教职工出国境记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgcgjjl-cgjmd','dataset-znwd','main-abdteajzgcgjjl-cgjmd','main-abdteajzgcgjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgcgjjl-cgjmd'
);

-- 智能问答_教职工管理_日常管理_教职工出国境记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgcgjjl-xxgznr','dataset-znwd','main-abdteajzgcgjjl-xxgznr','main-abdteajzgcgjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgcgjjl-xxgznr'
);

-- 智能问答_教职工管理_日常管理_教职工出国境记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgcgjjl-hzhhtxzh','dataset-znwd','main-abdteajzgcgjjl-hzhhtxzh','main-abdteajzgcgjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgcgjjl-hzhhtxzh'
);

-- 智能问答_教职工管理_日常管理_教职工出国境记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgcgjjl-cgjrq','dataset-znwd','main-abdteajzgcgjjl-cgjrq','main-abdteajzgcgjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgcgjjl-cgjrq'
);

-- 智能问答_教职工管理_日常管理_教职工出国境记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgcgjjl-hgrq','dataset-znwd','main-abdteajzgcgjjl-hgrq','main-abdteajzgcgjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgcgjjl-hgrq'
);

-- 智能问答_教职工管理_日常管理_教职工出国境记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgcgjjl-xxgzcj','dataset-znwd','main-abdteajzgcgjjl-xxgzcj','main-abdteajzgcgjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgcgjjl-xxgzcj'
);

-- 智能问答_教职工管理_日常管理_教职工出国境记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgcgjjl-xxcdfy','dataset-znwd','main-abdteajzgcgjjl-xxcdfy','main-abdteajzgcgjjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgcgjjl-xxcdfy'
);

-- 智能问答_教职工管理_日常管理_教职工离退休记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgltxjl-zgh','dataset-znwd','main-abdteajzgltxjl-zgh','main-abdteajzgltxjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgltxjl-zgh'
);

-- 智能问答_教职工管理_日常管理_教职工离退休记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgltxjl-ltlb','dataset-znwd','main-abdteajzgltxjl-ltlb','main-abdteajzgltxjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgltxjl-ltlb'
);

-- 智能问答_教职工管理_日常管理_教职工离退休记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgltxjl-ltrq','dataset-znwd','main-abdteajzgltxjl-ltrq','main-abdteajzgltxjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgltxjl-ltrq'
);

-- 智能问答_教职工管理_日常管理_教职工离退休记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgltxjl-lthxsjb','dataset-znwd','main-abdteajzgltxjl-lthxsjb','main-abdteajzgltxjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgltxjl-lthxsjb'
);

-- 智能问答_教职工管理_日常管理_教职工离退休记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgltxjl-lthgldw','dataset-znwd','main-abdteajzgltxjl-lthgldw','main-abdteajzgltxjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgltxjl-lthgldw'
);

-- 智能问答_教职工管理_日常管理_教职工离退休记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgltxjl-ltxfzfdw','dataset-znwd','main-abdteajzgltxjl-ltxfzfdw','main-abdteajzgltxjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgltxjl-ltxfzfdw'
);

-- 智能问答_教职工管理_日常管理_教职工离退休记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgltxjl-ydazdd','dataset-znwd','main-abdteajzgltxjl-ydazdd','main-abdteajzgltxjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgltxjl-ydazdd'
);

-- 智能问答_教职工管理_日常管理_教职工返聘记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgfpjl-zgh','dataset-znwd','main-abdteajzgfpjl-zgh','main-abdteajzgfpjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgfpjl-zgh'
);

-- 智能问答_教职工管理_日常管理_教职工返聘记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgfpjl-fpdwbm','dataset-znwd','main-abdteajzgfpjl-fpdwbm','main-abdteajzgfpjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgfpjl-fpdwbm'
);

-- 智能问答_教职工管理_日常管理_教职工返聘记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgfpjl-fpqsrq','dataset-znwd','main-abdteajzgfpjl-fpqsrq','main-abdteajzgfpjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgfpjl-fpqsrq'
);

-- 智能问答_教职工管理_日常管理_教职工返聘记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgfpjl-fpzzrq','dataset-znwd','main-abdteajzgfpjl-fpzzrq','main-abdteajzgfpjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgfpjl-fpzzrq'
);

-- 智能问答_教职工管理_日常管理_教职工返聘记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgfpjl-gwmc','dataset-znwd','main-abdteajzgfpjl-gwmc','main-abdteajzgfpjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgfpjl-gwmc'
);

-- 智能问答_教职工管理_日常管理_教职工返聘记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgfpjl-fpcj','dataset-znwd','main-abdteajzgfpjl-fpcj','main-abdteajzgfpjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgfpjl-fpcj'
);

-- 智能问答_教职工管理_日常管理_教职工返聘记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgfpjl-fpjly','dataset-znwd','main-abdteajzgfpjl-fpjly','main-abdteajzgfpjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgfpjl-fpjly'
);

-- 智能问答_教职工管理_日常管理_教职工去世记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgqsjl-zgh','dataset-znwd','main-abdteajzgqsjl-zgh','main-abdteajzgqsjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgqsjl-zgh'
);

-- 智能问答_教职工管理_日常管理_教职工去世记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgqsjl-qsrq','dataset-znwd','main-abdteajzgqsjl-qsrq','main-abdteajzgqsjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgqsjl-qsrq'
);

-- 智能问答_教职工管理_日常管理_教职工去世记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgqsjl-qsdd','dataset-znwd','main-abdteajzgqsjl-qsdd','main-abdteajzgqsjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgqsjl-qsdd'
);

-- 智能问答_教职工管理_日常管理_教职工去世记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgqsjl-qsyy','dataset-znwd','main-abdteajzgqsjl-qsyy','main-abdteajzgqsjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgqsjl-qsyy'
);

-- 智能问答_教职工管理_日常管理_教职工去世记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgqsjl-qslb','dataset-znwd','main-abdteajzgqsjl-qslb','main-abdteajzgqsjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgqsjl-qslb'
);

-- 智能问答_教职工管理_日常管理_教职工去世记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgqsjl-qslx','dataset-znwd','main-abdteajzgqsjl-qslx','main-abdteajzgqsjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgqsjl-qslx'
);

-- 智能问答_教职工管理_日常管理_教职工去世记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgqsjl-szbzj','dataset-znwd','main-abdteajzgqsjl-szbzj','main-abdteajzgqsjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgqsjl-szbzj'
);

-- 智能问答_教职工管理_日常管理_教职工去世记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgqsjl-ycxfxj','dataset-znwd','main-abdteajzgqsjl-ycxfxj','main-abdteajzgqsjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgqsjl-ycxfxj'
);

-- 智能问答_教职工管理_职称岗聘_教职工岗位聘用记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzggwpyjl-zgh','dataset-znwd','main-abdteajzggwpyjl-zgh','main-abdteajzggwpyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzggwpyjl-zgh'
);

-- 智能问答_教职工管理_职称岗聘_教职工岗位聘用记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzggwpyjl-gwmc','dataset-znwd','main-abdteajzggwpyjl-gwmc','main-abdteajzggwpyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzggwpyjl-gwmc'
);

-- 智能问答_教职工管理_职称岗聘_教职工岗位聘用记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzggwpyjl-prqsrq','dataset-znwd','main-abdteajzggwpyjl-prqsrq','main-abdteajzggwpyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzggwpyjl-prqsrq'
);

-- 智能问答_教职工管理_职称岗聘_教职工岗位聘用记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzggwpyjl-przzrq','dataset-znwd','main-abdteajzggwpyjl-przzrq','main-abdteajzggwpyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzggwpyjl-przzrq'
);

-- 智能问答_教职工管理_职称岗聘_教职工岗位聘用记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzggwpyjl-sfzr','dataset-znwd','main-abdteajzggwpyjl-sfzr','main-abdteajzggwpyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzggwpyjl-sfzr'
);

-- 智能问答_教职工管理_职称岗聘_教职工岗位等级聘用记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzggwdjpyjl-zgh','dataset-znwd','main-abdteajzggwdjpyjl-zgh','main-abdteajzggwdjpyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzggwdjpyjl-zgh'
);

-- 智能问答_教职工管理_职称岗聘_教职工岗位等级聘用记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzggwdjpyjl-gwlx','dataset-znwd','main-abdteajzggwdjpyjl-gwlx','main-abdteajzggwdjpyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzggwdjpyjl-gwlx'
);

-- 智能问答_教职工管理_职称岗聘_教职工岗位等级聘用记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzggwdjpyjl-gwdj','dataset-znwd','main-abdteajzggwdjpyjl-gwdj','main-abdteajzggwdjpyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzggwdjpyjl-gwdj'
);

-- 智能问答_教职工管理_职称岗聘_教职工岗位等级聘用记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzggwdjpyjl-spdw','dataset-znwd','main-abdteajzggwdjpyjl-spdw','main-abdteajzggwdjpyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzggwdjpyjl-spdw'
);

-- 智能问答_教职工管理_职称岗聘_教职工岗位等级聘用记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzggwdjpyjl-prqsrq','dataset-znwd','main-abdteajzggwdjpyjl-prqsrq','main-abdteajzggwdjpyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzggwdjpyjl-prqsrq'
);

-- 智能问答_教职工管理_职称岗聘_教职工岗位等级聘用记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzggwdjpyjl-przzrq','dataset-znwd','main-abdteajzggwdjpyjl-przzrq','main-abdteajzggwdjpyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzggwdjpyjl-przzrq'
);

-- 智能问答_教职工管理_职称岗聘_职称评聘记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteazcppjl-zgh','dataset-znwd','main-abdteazcppjl-zgh','main-abdteazcppjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteazcppjl-zgh'
);

-- 智能问答_教职工管理_职称岗聘_职称评聘记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteazcppjl-przyjszw','dataset-znwd','main-abdteazcppjl-przyjszw','main-abdteazcppjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteazcppjl-przyjszw'
);

-- 智能问答_教职工管理_职称岗聘_职称评聘记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteazcppjl-przyjszwjb','dataset-znwd','main-abdteazcppjl-przyjszwjb','main-abdteazcppjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteazcppjl-przyjszwjb'
);

-- 智能问答_教职工管理_职称岗聘_职称评聘记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteazcppjl-prqsrq','dataset-znwd','main-abdteazcppjl-prqsrq','main-abdteazcppjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteazcppjl-prqsrq'
);

-- 智能问答_教职工管理_职称岗聘_职称评聘记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteazcppjl-przzrq','dataset-znwd','main-abdteazcppjl-przzrq','main-abdteazcppjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteazcppjl-przzrq'
);

-- 智能问答_教职工管理_职称岗聘_职称评聘记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteazcppjl-prdw','dataset-znwd','main-abdteazcppjl-prdw','main-abdteazcppjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteazcppjl-prdw'
);

-- 智能问答_教职工管理_职称岗聘_职称评聘记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteazcppjl-pdzyjszw','dataset-znwd','main-abdteazcppjl-pdzyjszw','main-abdteazcppjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteazcppjl-pdzyjszw'
);

-- 智能问答_教职工管理_职称岗聘_职称评聘记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteazcppjl-pdzyjszwjb','dataset-znwd','main-abdteazcppjl-pdzyjszwjb','main-abdteazcppjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteazcppjl-pdzyjszwjb'
);

-- 智能问答_教职工管理_职称岗聘_职称评聘记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteazcppjl-pdrq','dataset-znwd','main-abdteazcppjl-pdrq','main-abdteazcppjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteazcppjl-pdrq'
);

-- 智能问答_教职工管理_职称岗聘_职称评聘记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteazcppjl-psdw','dataset-znwd','main-abdteazcppjl-psdw','main-abdteazcppjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteazcppjl-psdw'
);

-- 智能问答_教职工管理_考核评价_教职工年度考核记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgndkhjl-zgh','dataset-znwd','main-abdteajzgndkhjl-zgh','main-abdteajzgndkhjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgndkhjl-zgh'
);

-- 智能问答_教职工管理_考核评价_教职工年度考核记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgndkhjl-khdw','dataset-znwd','main-abdteajzgndkhjl-khdw','main-abdteajzgndkhjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgndkhjl-khdw'
);

-- 智能问答_教职工管理_考核评价_教职工年度考核记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgndkhjl-khmc','dataset-znwd','main-abdteajzgndkhjl-khmc','main-abdteajzgndkhjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgndkhjl-khmc'
);

-- 智能问答_教职工管理_考核评价_教职工年度考核记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgndkhjl-khlb','dataset-znwd','main-abdteajzgndkhjl-khlb','main-abdteajzgndkhjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgndkhjl-khlb'
);

-- 智能问答_教职工管理_考核评价_教职工年度考核记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgndkhjl-khnf','dataset-znwd','main-abdteajzgndkhjl-khnf','main-abdteajzgndkhjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgndkhjl-khnf'
);

-- 智能问答_教职工管理_考核评价_教职工年度考核记录-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model_column (ID,DATASET_ID,COLUMN_ID,MODEL_ID,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME)
SELECT 'dataset-znwd%main-abdteajzgndkhjl-khjl','dataset-znwd','main-abdteajzgndkhjl-khjl','main-abdteajzgndkhjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model_column
    WHERE id = 'dataset-znwd%main-abdteajzgndkhjl-khjl'
);