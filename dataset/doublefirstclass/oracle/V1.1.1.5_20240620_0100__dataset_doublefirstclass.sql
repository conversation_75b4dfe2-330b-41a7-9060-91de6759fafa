/*
 Description		: [双一流评估监测(doublefirstclass)]应用数据集
 Author				: dataapp
 Date				: 2024-06-20 01:00:00
 Lowcode Version	: V1.1.1_Beta5
 Database           : oracle
*/

-- 数据集模型字段
BEGIN
    EXECUTE IMMEDIATE 'CREATE TABLE t_da_dataset_model_column ( dataset_id VARCHAR2(50) NOT NULL,
column_id VARCHAR2(50) NOT NULL,
model_id VARCHAR2(50) NOT NULL,
id VARCHAR2(100),
create_by VARCHAR2(100),
create_time DATE,
update_by VARCHAR2(100),
update_time DATE,PRIMARY KEY (id) )';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

COMMENT ON TABLE t_da_dataset_model_column IS '数据集模型字段';
COMMENT ON COLUMN t_da_dataset_model_column.dataset_id IS '数据集ID';
COMMENT ON COLUMN t_da_dataset_model_column.column_id IS '字段ID';
COMMENT ON COLUMN t_da_dataset_model_column.model_id IS '模型ID';
COMMENT ON COLUMN t_da_dataset_model_column.id IS 'ID';
COMMENT ON COLUMN t_da_dataset_model_column.create_by IS '创建人';
COMMENT ON COLUMN t_da_dataset_model_column.create_time IS '创建时间';
COMMENT ON COLUMN t_da_dataset_model_column.update_by IS '更新人';
COMMENT ON COLUMN t_da_dataset_model_column.update_time IS '更新时间';

-- 双一流评估监测-数据集
INSERT INTO t_da_dataset (id,sjjmc,
create_by,create_time,update_by,update_time,
copy_label,primary_app_id)
SELECT 'dataset-sylpgjc','双一流评估监测',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'','doublefirstclass'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset
    WHERE id = 'dataset-sylpgjc'
);

-- dataset-sylpgjc-doublefirstclass-数据集和应用关联表
INSERT INTO t_da_dataset_app (id,sjjid,appid,
create_by,create_time,update_by,update_time)
SELECT 'dataset-sylpgjc-doublefirstclass','dataset-sylpgjc','doublefirstclass',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_app
    WHERE id = 'dataset-sylpgjc-doublefirstclass'
);

-- 大学建设进展-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_dxjsjz','dataset-sylpgjc','大学建设进展',NULL,1,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_dxjsjz'
);

-- 国际交流合作-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_gjjlhz','dataset-sylpgjc','国际交流合作',NULL,2,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_gjjlhz'
);

-- 经费投入使用-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_jftrsy','dataset-sylpgjc','经费投入使用',NULL,3,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_jftrsy'
);

-- 着力推进成果转化-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_zltjcgzh','dataset-sylpgjc','着力推进成果转化',NULL,4,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_zltjcgzh'
);

-- 传承创新优秀文化-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_cccxyxwh','dataset-sylpgjc','传承创新优秀文化',NULL,5,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_cccxyxwh'
);

-- 关键环节突破-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_gjhjtp','dataset-sylpgjc','关键环节突破',NULL,6,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_gjhjtp'
);

-- 社会参与机制-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_shcyjz','dataset-sylpgjc','社会参与机制',NULL,7,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_shcyjz'
);

-- 培养拔尖创新人才-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_pybjcxrc','dataset-sylpgjc','培养拔尖创新人才',NULL,8,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_pybjcxrc'
);

-- 培养过程-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_pybjcxrc_pygc','dataset-sylpgjc','培养过程','sylpgjc_pybjcxrc',9,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_pybjcxrc_pygc'
);

-- 招生与授位情况-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_pybjcxrc_pygc_zsyswqk','dataset-sylpgjc','招生与授位情况','sylpgjc_pybjcxrc_pygc',10,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_pybjcxrc_pygc_zsyswqk'
);

-- 国家级教学成果奖-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_pybjcxrc_pygc_gjjjxcgj','dataset-sylpgjc','国家级教学成果奖','sylpgjc_pybjcxrc_pygc',11,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_pybjcxrc_pygc_gjjjxcgj'
);

-- 硕士导师数和博士导师数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_pybjcxrc_pygc_ssdsshbsdss','dataset-sylpgjc','硕士导师数和博士导师数','sylpgjc_pybjcxrc_pygc',12,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_pybjcxrc_pygc_ssdsshbsdss'
);

-- 给本科生上课的正教授人数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_pybjcxrc_pygc_gbksskdzjsrs','dataset-sylpgjc','给本科生上课的正教授人数','sylpgjc_pybjcxrc_pygc',13,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_pybjcxrc_pygc_gbksskdzjsrs'
);

-- 学生国内外竞赛获奖项目清单-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_pybjcxrc_pygc_xsgnwjshjxmqd','dataset-sylpgjc','学生国内外竞赛获奖项目清单','sylpgjc_pybjcxrc_pygc',14,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_pybjcxrc_pygc_xsgnwjshjxmqd'
);

-- 毕业就业-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_pybjcxrc_byjy','dataset-sylpgjc','毕业就业','sylpgjc_pybjcxrc',15,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_pybjcxrc_byjy'
);

-- 学科领域突出贡献者清单-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_pybjcxrc_byjy_xklytcgxzqd','dataset-sylpgjc','学科领域突出贡献者清单','sylpgjc_pybjcxrc_byjy',16,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_pybjcxrc_byjy_xklytcgxzqd'
);

-- 学生就业情况-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_pybjcxrc_byjy_xsjyqk','dataset-sylpgjc','学生就业情况','sylpgjc_pybjcxrc_byjy',17,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_pybjcxrc_byjy_xsjyqk'
);

-- 国际交流合作-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_pybjcxrc_gjjlhz','dataset-sylpgjc','国际交流合作','sylpgjc_pybjcxrc',18,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_pybjcxrc_gjjlhz'
);

-- 来本学科攻读学位的留学生和交流学者人数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_pybjcxrc_gjjlhz_lbxkgdxwdlxshjlxzrs','dataset-sylpgjc','来本学科攻读学位的留学生和交流学者人数','sylpgjc_pybjcxrc_gjjlhz',19,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_pybjcxrc_gjjlhz_lbxkgdxwdlxshjlxzrs'
);

-- 学生参加本领域国内外重要学术会议并作报告人员清单-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_pybjcxrc_gjjlhz_xscjblygnwzyxshybzbgryqd','dataset-sylpgjc','学生参加本领域国内外重要学术会议并作报告人员清单','sylpgjc_pybjcxrc_gjjlhz',20,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_pybjcxrc_gjjlhz_xscjblygnwzyxshybzbgryqd'
);

-- 建设一流师资队伍-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_jsylszdw','dataset-sylpgjc','建设一流师资队伍',NULL,21,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_jsylszdw'
);

-- 专任教师队伍-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_jsylszdw_zrjsdw','dataset-sylpgjc','专任教师队伍','sylpgjc_jsylszdw',22,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_jsylszdw_zrjsdw'
);

-- 杰出人才清单-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_jsylszdw_zrjsdw_jcrcqd','dataset-sylpgjc','杰出人才清单','sylpgjc_jsylszdw_zrjsdw',23,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_jsylszdw_zrjsdw_jcrcqd'
);

-- 学科专任教师数量及结构-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_jsylszdw_zrjsdw_xkzrjssljjg','dataset-sylpgjc','学科专任教师数量及结构','sylpgjc_jsylszdw_zrjsdw',24,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_jsylszdw_zrjsdw_xkzrjssljjg'
);

-- 博士后和科研助理数量-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_jsylszdw_zrjsdw_bshhkyzlsl','dataset-sylpgjc','博士后和科研助理数量','sylpgjc_jsylszdw_zrjsdw',25,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_jsylszdw_zrjsdw_bshhkyzlsl'
);

-- 外籍专任教师数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_jsylszdw_zrjsdw_wjzrjss','dataset-sylpgjc','外籍专任教师数','sylpgjc_jsylszdw_zrjsdw',26,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_jsylszdw_zrjsdw_wjzrjss'
);

-- 师资队伍国际水平-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_jsylszdw_szdwgjsp','dataset-sylpgjc','师资队伍国际水平','sylpgjc_jsylszdw',27,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_jsylszdw_szdwgjsp'
);

-- 教师担任国内外重要期刊负责人清单-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_jsylszdw_szdwgjsp_jsdrgnwzyqkfzrqd','dataset-sylpgjc','教师担任国内外重要期刊负责人清单','sylpgjc_jsylszdw_szdwgjsp',28,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_jsylszdw_szdwgjsp_jsdrgnwzyqkfzrqd'
);

-- 教师在国内外重要学术组织任职主要负责人清单-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_jsylszdw_szdwgjsp_jszgnwzyxszzrzzyfzrqd','dataset-sylpgjc','教师在国内外重要学术组织任职主要负责人清单','sylpgjc_jsylszdw_szdwgjsp',29,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_jsylszdw_szdwgjsp_jszgnwzyxszzrzzyfzrqd'
);

-- 教师参加本领域重要学术会议并作报告人员清单-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_jsylszdw_szdwgjsp_jscjblyzyxshybzbgryqd','dataset-sylpgjc','教师参加本领域重要学术会议并作报告人员清单','sylpgjc_jsylszdw_szdwgjsp',30,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_jsylszdw_szdwgjsp_jscjblyzyxshybzbgryqd'
);

-- 教师担任国际比赛评委、裁判人员清单-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_jsylszdw_szdwgjsp_jsdrgjbspw、cpryqd','dataset-sylpgjc','教师担任国际比赛评委、裁判人员清单','sylpgjc_jsylszdw_szdwgjsp',31,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_jsylszdw_szdwgjsp_jsdrgjbspw、cpryqd'
);

-- 提升科学研究水平-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_tskxyjsp','dataset-sylpgjc','提升科学研究水平',NULL,32,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_tskxyjsp'
);

-- 科学研究与实践创新-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_tskxyjsp_kxyjysjcx','dataset-sylpgjc','科学研究与实践创新','sylpgjc_tskxyjsp',33,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_tskxyjsp_kxyjysjcx'
);

-- 教师获得的国内外重要奖项清单-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_tskxyjsp_kxyjysjcx_jshddgnwzyjxqd','dataset-sylpgjc','教师获得的国内外重要奖项清单','sylpgjc_tskxyjsp_kxyjysjcx',34,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_tskxyjsp_kxyjysjcx_jshddgnwzyjxqd'
);

-- 教师公开出版的专著清单-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_tskxyjsp_kxyjysjcx_jsgkcbdzzqd','dataset-sylpgjc','教师公开出版的专著清单','sylpgjc_tskxyjsp_kxyjysjcx',35,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_tskxyjsp_kxyjysjcx_jsgkcbdzzqd'
);

-- 教师在国内外重要期刊发表的代表性论文清单-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_tskxyjsp_kxyjysjcx_jszgnwzyqkfbddbxlwqd','dataset-sylpgjc','教师在国内外重要期刊发表的代表性论文清单','sylpgjc_tskxyjsp_kxyjysjcx',36,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_tskxyjsp_kxyjysjcx_jszgnwzyqkfbddbxlwqd'
);

-- 授权发明专利-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_tskxyjsp_kxyjysjcx_sqfmzl','dataset-sylpgjc','授权发明专利','sylpgjc_tskxyjsp_kxyjysjcx',37,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_tskxyjsp_kxyjysjcx_sqfmzl'
);

-- 承担科研项目-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_tskxyjsp_kxyjysjcx_cdkyxm','dataset-sylpgjc','承担科研项目','sylpgjc_tskxyjsp_kxyjysjcx',38,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_tskxyjsp_kxyjysjcx_cdkyxm'
);

-- 承担国内外重大设计与展演任务清单-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_tskxyjsp_kxyjysjcx_cdgnwzdsjyzyrwqd','dataset-sylpgjc','承担国内外重大设计与展演任务清单','sylpgjc_tskxyjsp_kxyjysjcx',39,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_tskxyjsp_kxyjysjcx_cdgnwzdsjyzyrwqd'
);

-- 科研平台建设-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_tskxyjsp_kyptjs','dataset-sylpgjc','科研平台建设','sylpgjc_tskxyjsp',40,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_tskxyjsp_kyptjs'
);

-- 国家重大科技创新平台和基地清单、绩效评估情况-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_tskxyjsp_kyptjs_gjzdkjcxpthjdqd、jxpgqk','dataset-sylpgjc','国家重大科技创新平台和基地清单、绩效评估情况','sylpgjc_tskxyjsp_kyptjs',41,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_tskxyjsp_kyptjs_gjzdkjcxpthjdqd、jxpgqk'
);

-- 部省级重点研究基地清单、绩效评估情况-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_tskxyjsp_kyptjs_bsjzdyjjdqd、jxpgqk','dataset-sylpgjc','部省级重点研究基地清单、绩效评估情况','sylpgjc_tskxyjsp_kyptjs',42,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_tskxyjsp_kyptjs_bsjzdyjjdqd、jxpgqk'
);

-- 纵向、横向到校科研经费数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_tskxyjsp_kyptjs_zx、hxdxkyjfs','dataset-sylpgjc','纵向、横向到校科研经费数','sylpgjc_tskxyjsp_kyptjs',43,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_tskxyjsp_kyptjs_zx、hxdxkyjfs'
);

-- 主办的国际学术期刊清单-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_tskxyjsp_kyptjs_zbdgjxsqkqd','dataset-sylpgjc','主办的国际学术期刊清单','sylpgjc_tskxyjsp_kyptjs',44,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_tskxyjsp_kyptjs_zbdgjxsqkqd'
);

-- 国际影响力-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_tskxyjsp_gjyxl','dataset-sylpgjc','国际影响力','sylpgjc_tskxyjsp',45,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_tskxyjsp_gjyxl'
);

-- 参与国内外标准制定项目清单-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_tskxyjsp_gjyxl_cygnwbzzdxmqd','dataset-sylpgjc','参与国内外标准制定项目清单','sylpgjc_tskxyjsp_gjyxl',46,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_tskxyjsp_gjyxl_cygnwbzzdxmqd'
);

-- 学科建设进展-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_xkjsjz','dataset-sylpgjc','学科建设进展',NULL,47,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_xkjsjz'
);

-- 建设进展-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_xkjsjz_jsjz','dataset-sylpgjc','建设进展','sylpgjc_xkjsjz',48,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_xkjsjz_jsjz'
);

-- 学科建设经费数额-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_xkjsjz_jsjz_xkjsjfse','dataset-sylpgjc','学科建设经费数额','sylpgjc_xkjsjz_jsjz',49,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_xkjsjz_jsjz_xkjsjfse'
);

-- 社会服务-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_shfw','dataset-sylpgjc','社会服务',NULL,50,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_shfw'
);

-- 成果转化-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_shfw_cgzh','dataset-sylpgjc','成果转化','sylpgjc_shfw',51,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_shfw_cgzh'
);

-- 成果转化和咨询服务到校金额-SUB_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'sylpgjc_shfw_cgzh_cgzhhzxfwdxje','dataset-sylpgjc','成果转化和咨询服务到校金额','sylpgjc_shfw_cgzh',52,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'sylpgjc_shfw_cgzh_cgzhhzxfwdxje'
);

-- 双一流评估监测_大学建设进展_学校机构信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_dxjsjz%main-abdschxxjgxx','dataset-sylpgjc','sylpgjc_dxjsjz','main-abdschxxjgxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_dxjsjz%main-abdschxxjgxx'
);

-- 双一流评估监测_大学建设进展_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_dxjsjz%main-abdschxxjbxx','dataset-sylpgjc','sylpgjc_dxjsjz','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_dxjsjz%main-abdschxxjbxx'
);

-- 双一流评估监测_大学建设进展_学科信息-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_dxjsjz%main-abdsubxkxx','dataset-sylpgjc','sylpgjc_dxjsjz','main-abdsubxkxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_dxjsjz%main-abdsubxkxx'
);

-- 双一流评估监测_大学建设进展_科研机构信息-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_dxjsjz%main-abdsubkyjgxx','dataset-sylpgjc','sylpgjc_dxjsjz','main-abdsubkyjgxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_dxjsjz%main-abdsubkyjgxx'
);

-- 双一流评估监测_国际交流合作_学科评估排名-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_gjjlhz%main-abdschxkpgpm','dataset-sylpgjc','sylpgjc_gjjlhz','main-abdschxkpgpm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_gjjlhz%main-abdschxkpgpm'
);

-- 双一流评估监测_国际交流合作_四大国际学科排名-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_gjjlhz%main-abdschsdgjxkpm','dataset-sylpgjc','sylpgjc_gjjlhz','main-abdschsdgjxkpm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_gjjlhz%main-abdschsdgjxkpm'
);

-- 双一流评估监测_国际交流合作_ESI学科排名-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_gjjlhz%main-abdschexkpm','dataset-sylpgjc','sylpgjc_gjjlhz','main-abdschexkpm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_gjjlhz%main-abdschexkpm'
);

-- 双一流评估监测_国际交流合作_参与国际科学工程计划-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_gjjlhz%main-abdschcygjkxgcjh','dataset-sylpgjc','sylpgjc_gjjlhz','main-abdschcygjkxgcjh',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_gjjlhz%main-abdschcygjkxgcjh'
);

-- 双一流评估监测_国际交流合作_创建学术期刊-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_gjjlhz%main-abdschcjxsqk','dataset-sylpgjc','sylpgjc_gjjlhz','main-abdschcjxsqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_gjjlhz%main-abdschcjxsqk'
);

-- 双一流评估监测_国际交流合作_赴境外办学机构项目-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_gjjlhz%main-abdschfjwbxjgxm','dataset-sylpgjc','sylpgjc_gjjlhz','main-abdschfjwbxjgxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_gjjlhz%main-abdschfjwbxjgxm'
);

-- 双一流评估监测_国际交流合作_重点学科建设情况-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_gjjlhz%main-abdsubzdxkjsqk','dataset-sylpgjc','sylpgjc_gjjlhz','main-abdsubzdxkjsqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_gjjlhz%main-abdsubzdxkjsqk'
);

-- 双一流评估监测_国际交流合作_学生留学记录-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_gjjlhz%main-abdsubxslxjl','dataset-sylpgjc','sylpgjc_gjjlhz','main-abdsubxslxjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_gjjlhz%main-abdsubxslxjl'
);

-- 双一流评估监测_国际交流合作_教师参加学术会议-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_gjjlhz%main-abdsubjscjxshy','dataset-sylpgjc','sylpgjc_gjjlhz','main-abdsubjscjxshy',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_gjjlhz%main-abdsubjscjxshy'
);

-- 双一流评估监测_国际交流合作_学生参加学术会议-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_gjjlhz%main-abdsubxscjxshy','dataset-sylpgjc','sylpgjc_gjjlhz','main-abdsubxscjxshy',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_gjjlhz%main-abdsubxscjxshy'
);

-- 双一流评估监测_国际交流合作_参与国际学术组织与联盟-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_gjjlhz%main-abdsubcygjxszzylm','dataset-sylpgjc','sylpgjc_gjjlhz','main-abdsubcygjxszzylm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_gjjlhz%main-abdsubcygjxszzylm'
);

-- 双一流评估监测_经费投入使用_双一流建设经费统计-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jftrsy%main-abdschsyljsjftj','dataset-sylpgjc','sylpgjc_jftrsy','main-abdschsyljsjftj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jftrsy%main-abdschsyljsjftj'
);

-- 双一流评估监测_经费投入使用_学科建设经费统计-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jftrsy%main-abdsubxkjsjftj','dataset-sylpgjc','sylpgjc_jftrsy','main-abdsubxkjsjftj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jftrsy%main-abdsubxkjsjftj'
);

-- 双一流评估监测_经费投入使用_科研经费到帐-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jftrsy%main-abdsubkyjfdz','dataset-sylpgjc','sylpgjc_jftrsy','main-abdsubkyjfdz',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jftrsy%main-abdsubkyjfdz'
);

-- 双一流评估监测_经费投入使用_获社会捐赠统计-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jftrsy%main-abdsubhshjztj','dataset-sylpgjc','sylpgjc_jftrsy','main-abdsubhshjztj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jftrsy%main-abdsubhshjztj'
);

-- 双一流评估监测_着力推进成果转化_智库建设发展情况统计-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_zltjcgzh%main-abdschzkjsfzqktj','dataset-sylpgjc','sylpgjc_zltjcgzh','main-abdschzkjsfzqktj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_zltjcgzh%main-abdschzkjsfzqktj'
);

-- 双一流评估监测_着力推进成果转化_科研成果转化-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_zltjcgzh%main-abdsubkycgzh','dataset-sylpgjc','sylpgjc_zltjcgzh','main-abdsubkycgzh',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_zltjcgzh%main-abdsubkycgzh'
);

-- 双一流评估监测_传承创新优秀文化_文化传承基地情况统计-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_cccxyxwh%main-abdschwhccjdqktj','dataset-sylpgjc','sylpgjc_cccxyxwh','main-abdschwhccjdqktj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_cccxyxwh%main-abdschwhccjdqktj'
);

-- 双一流评估监测_传承创新优秀文化_博物馆艺术馆情况统计-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_cccxyxwh%main-abdschbwgysgqktj','dataset-sylpgjc','sylpgjc_cccxyxwh','main-abdschbwgysgqktj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_cccxyxwh%main-abdschbwgysgqktj'
);

-- 双一流评估监测_关键环节突破_协同实践育人项目统计-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_gjhjtp%main-abdschxtsjyrxmtj','dataset-sylpgjc','sylpgjc_gjhjtp','main-abdschxtsjyrxmtj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_gjhjtp%main-abdschxtsjyrxmtj'
);

-- 双一流评估监测_关键环节突破_国家急需人才培养项目统计-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_gjhjtp%main-abdschgjjxrcpyxmtj','dataset-sylpgjc','sylpgjc_gjhjtp','main-abdschgjjxrcpyxmtj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_gjhjtp%main-abdschgjjxrcpyxmtj'
);

-- 双一流评估监测_社会参与机制_产教融合研发机构-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_shcyjz%main-abdschcjrhyfjg','dataset-sylpgjc','sylpgjc_shcyjz','main-abdschcjrhyfjg',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_shcyjz%main-abdschcjrhyfjg'
);

-- 双一流评估监测_培养拔尖创新人才_本科生专业信息-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc%main-abdsubbkszyxx','dataset-sylpgjc','sylpgjc_pybjcxrc','main-abdsubbkszyxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc%main-abdsubbkszyxx'
);

-- 双一流评估监测_培养拔尖创新人才_辅导员任职记录-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc%main-abdsubfdyrzjl','dataset-sylpgjc','sylpgjc_pybjcxrc','main-abdsubfdyrzjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc%main-abdsubfdyrzjl'
);

-- 双一流评估监测_培养拔尖创新人才_学生信息-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc%main-abdsubxsxx','dataset-sylpgjc','sylpgjc_pybjcxrc','main-abdsubxsxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc%main-abdsubxsxx'
);

-- 双一流评估监测_培养拔尖创新人才_突出贡献者-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc%main-abdsubtcgxz','dataset-sylpgjc','sylpgjc_pybjcxrc','main-abdsubtcgxz',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc%main-abdsubtcgxz'
);

-- 双一流评估监测_培养拔尖创新人才_学生就业信息-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc%main-abdsubxsjyxx','dataset-sylpgjc','sylpgjc_pybjcxrc','main-abdsubxsjyxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc%main-abdsubxsjyxx'
);

-- 双一流评估监测_培养拔尖创新人才_教师获教学成果奖-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc%main-abdsubjshjxcgj','dataset-sylpgjc','sylpgjc_pybjcxrc','main-abdsubjshjxcgj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc%main-abdsubjshjxcgj'
);

-- 双一流评估监测_培养拔尖创新人才_学生竞赛获奖-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc%main-abdsubxsjshj','dataset-sylpgjc','sylpgjc_pybjcxrc','main-abdsubxsjshj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc%main-abdsubxsjshj'
);

-- 双一流评估监测_培养拔尖创新人才_专业认证信息-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc%main-abdsubzyrzxx','dataset-sylpgjc','sylpgjc_pybjcxrc','main-abdsubzyrzxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc%main-abdsubzyrzxx'
);

-- 双一流评估监测_培养拔尖创新人才_入选一流专业-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc%main-abdsubrxylzy','dataset-sylpgjc','sylpgjc_pybjcxrc','main-abdsubrxylzy',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc%main-abdsubrxylzy'
);

-- 双一流评估监测_培养拔尖创新人才_入选一流课程-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc%main-abdsubrxylkc','dataset-sylpgjc','sylpgjc_pybjcxrc','main-abdsubrxylkc',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc%main-abdsubrxylkc'
);

-- 双一流评估监测_培养拔尖创新人才_本科生授课记录-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc%main-abdsubbksskjl','dataset-sylpgjc','sylpgjc_pybjcxrc','main-abdsubbksskjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc%main-abdsubbksskjl'
);

-- 双一流评估监测_培养拔尖创新人才_教师出版教材-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc%main-abdsubjscbjc','dataset-sylpgjc','sylpgjc_pybjcxrc','main-abdsubjscbjc',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc%main-abdsubjscbjc'
);

-- 双一流评估监测_培养拔尖创新人才_培养过程_招生与授位情况_每年招生人数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc_pygc_zsyswqk%main-hldsisub0007','dataset-sylpgjc','sylpgjc_pybjcxrc_pygc_zsyswqk','main-hldsisub0007',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc_pygc_zsyswqk%main-hldsisub0007'
);

-- 双一流评估监测_培养拔尖创新人才_培养过程_招生与授位情况_每年授位人数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc_pygc_zsyswqk%main-hldsisub0008','dataset-sylpgjc','sylpgjc_pybjcxrc_pygc_zsyswqk','main-hldsisub0008',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc_pygc_zsyswqk%main-hldsisub0008'
);

-- 双一流评估监测_培养拔尖创新人才_培养过程_国家级教学成果奖_每年国家级教学成果奖数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc_pygc_gjjjxcgj%main-hldsisub0009','dataset-sylpgjc','sylpgjc_pybjcxrc_pygc_gjjjxcgj','main-hldsisub0009',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc_pygc_gjjjxcgj%main-hldsisub0009'
);

-- 双一流评估监测_培养拔尖创新人才_培养过程_国家级教学成果奖_每年新增国家级教学成果奖特等奖数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc_pygc_gjjjxcgj%main-hldsisub0010','dataset-sylpgjc','sylpgjc_pybjcxrc_pygc_gjjjxcgj','main-hldsisub0010',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc_pygc_gjjjxcgj%main-hldsisub0010'
);

-- 双一流评估监测_培养拔尖创新人才_培养过程_国家级教学成果奖_每年新增国家级教学成果奖一等奖数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc_pygc_gjjjxcgj%main-hldsisub0011','dataset-sylpgjc','sylpgjc_pybjcxrc_pygc_gjjjxcgj','main-hldsisub0011',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc_pygc_gjjjxcgj%main-hldsisub0011'
);

-- 双一流评估监测_培养拔尖创新人才_培养过程_硕士导师数和博士导师数_每学年硕士导师数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc_pygc_ssdsshbsdss%main-hldsisub0012','dataset-sylpgjc','sylpgjc_pybjcxrc_pygc_ssdsshbsdss','main-hldsisub0012',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc_pygc_ssdsshbsdss%main-hldsisub0012'
);

-- 双一流评估监测_培养拔尖创新人才_培养过程_硕士导师数和博士导师数_每学年博士导师数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc_pygc_ssdsshbsdss%main-hldsisub0013','dataset-sylpgjc','sylpgjc_pybjcxrc_pygc_ssdsshbsdss','main-hldsisub0013',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc_pygc_ssdsshbsdss%main-hldsisub0013'
);

-- 双一流评估监测_培养拔尖创新人才_培养过程_给本科生上课的正教授人数_每学年正教授总人数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc_pygc_gbksskdzjsrs%main-hldsisub0014','dataset-sylpgjc','sylpgjc_pybjcxrc_pygc_gbksskdzjsrs','main-hldsisub0014',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc_pygc_gbksskdzjsrs%main-hldsisub0014'
);

-- 双一流评估监测_培养拔尖创新人才_培养过程_给本科生上课的正教授人数_每学年给本科生上课的正教授总人数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc_pygc_gbksskdzjsrs%main-hldsisub0015','dataset-sylpgjc','sylpgjc_pybjcxrc_pygc_gbksskdzjsrs','main-hldsisub0015',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc_pygc_gbksskdzjsrs%main-hldsisub0015'
);

-- 双一流评估监测_培养拔尖创新人才_培养过程_给本科生上课的正教授人数_每学年给本科生上课的正教授比例-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc_pygc_gbksskdzjsrs%main-hldsisub0016','dataset-sylpgjc','sylpgjc_pybjcxrc_pygc_gbksskdzjsrs','main-hldsisub0016',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc_pygc_gbksskdzjsrs%main-hldsisub0016'
);

-- 双一流评估监测_培养拔尖创新人才_培养过程_学生国内外竞赛获奖项目清单_每年获得国内外竞赛获奖项目总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc_pygc_xsgnwjshjxmqd%main-hldsisub0017','dataset-sylpgjc','sylpgjc_pybjcxrc_pygc_xsgnwjshjxmqd','main-hldsisub0017',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc_pygc_xsgnwjshjxmqd%main-hldsisub0017'
);

-- 双一流评估监测_培养拔尖创新人才_培养过程_学生国内外竞赛获奖项目清单_每年获得全国互联网加获奖数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc_pygc_xsgnwjshjxmqd%main-hldsisub0018','dataset-sylpgjc','sylpgjc_pybjcxrc_pygc_xsgnwjshjxmqd','main-hldsisub0018',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc_pygc_xsgnwjshjxmqd%main-hldsisub0018'
);

-- 双一流评估监测_培养拔尖创新人才_培养过程_学生国内外竞赛获奖项目清单_每年获得全国挑战杯获奖数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc_pygc_xsgnwjshjxmqd%main-hldsisub0019','dataset-sylpgjc','sylpgjc_pybjcxrc_pygc_xsgnwjshjxmqd','main-hldsisub0019',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc_pygc_xsgnwjshjxmqd%main-hldsisub0019'
);

-- 双一流评估监测_培养拔尖创新人才_毕业就业_学科领域突出贡献者清单_每年突出贡献者数量-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc_byjy_xklytcgxzqd%main-hldsisub0020','dataset-sylpgjc','sylpgjc_pybjcxrc_byjy_xklytcgxzqd','main-hldsisub0020',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc_byjy_xklytcgxzqd%main-hldsisub0020'
);

-- 双一流评估监测_培养拔尖创新人才_毕业就业_学生就业情况_每年学生就业率-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc_byjy_xsjyqk%main-hldsisub0021','dataset-sylpgjc','sylpgjc_pybjcxrc_byjy_xsjyqk','main-hldsisub0021',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc_byjy_xsjyqk%main-hldsisub0021'
);

-- 双一流评估监测_培养拔尖创新人才_毕业就业_学生就业情况_每年不同培养层次学生就业去向人数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc_byjy_xsjyqk%main-hldsisub0022','dataset-sylpgjc','sylpgjc_pybjcxrc_byjy_xsjyqk','main-hldsisub0022',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc_byjy_xsjyqk%main-hldsisub0022'
);

-- 双一流评估监测_培养拔尖创新人才_国际交流合作_来本学科攻读学位的留学生和交流学者人数_每学年攻读不同学位层次的入学留学生数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc_gjjlhz_lbxkgdxwdlxshjlxzrs%main-hldsisub0023','dataset-sylpgjc','sylpgjc_pybjcxrc_gjjlhz_lbxkgdxwdlxshjlxzrs','main-hldsisub0023',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc_gjjlhz_lbxkgdxwdlxshjlxzrs%main-hldsisub0023'
);

-- 双一流评估监测_培养拔尖创新人才_国际交流合作_来本学科攻读学位的留学生和交流学者人数_每学年攻读不同学位层次的在校生留学生数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc_gjjlhz_lbxkgdxwdlxshjlxzrs%main-hldsisub0024','dataset-sylpgjc','sylpgjc_pybjcxrc_gjjlhz_lbxkgdxwdlxshjlxzrs','main-hldsisub0024',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc_gjjlhz_lbxkgdxwdlxshjlxzrs%main-hldsisub0024'
);

-- 双一流评估监测_培养拔尖创新人才_国际交流合作_来本学科攻读学位的留学生和交流学者人数_每学年交流学者数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc_gjjlhz_lbxkgdxwdlxshjlxzrs%main-hldsisub0025','dataset-sylpgjc','sylpgjc_pybjcxrc_gjjlhz_lbxkgdxwdlxshjlxzrs','main-hldsisub0025',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc_gjjlhz_lbxkgdxwdlxshjlxzrs%main-hldsisub0025'
);

-- 双一流评估监测_培养拔尖创新人才_国际交流合作_来本学科攻读学位的留学生和交流学者人数_每学年入学留学生占当年入学学生比例-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc_gjjlhz_lbxkgdxwdlxshjlxzrs%main-hldsisub0026','dataset-sylpgjc','sylpgjc_pybjcxrc_gjjlhz_lbxkgdxwdlxshjlxzrs','main-hldsisub0026',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc_gjjlhz_lbxkgdxwdlxshjlxzrs%main-hldsisub0026'
);

-- 双一流评估监测_培养拔尖创新人才_国际交流合作_学生参加本领域国内外重要学术会议并作报告人员清单_每年不同学生层次参加不同国家重要学术会议并作报告总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_pybjcxrc_gjjlhz_xscjblygnwzyxshybzbgryqd%main-hldsisub0027','dataset-sylpgjc','sylpgjc_pybjcxrc_gjjlhz_xscjblygnwzyxshybzbgryqd','main-hldsisub0027',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_pybjcxrc_gjjlhz_xscjblygnwzyxshybzbgryqd%main-hldsisub0027'
);

-- 双一流评估监测_建设一流师资队伍_教职工信息-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jsylszdw%main-abdsubjzgxx','dataset-sylpgjc','sylpgjc_jsylszdw','main-abdsubjzgxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jsylszdw%main-abdsubjzgxx'
);

-- 双一流评估监测_建设一流师资队伍_高级人才获选-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jsylszdw%main-abdsubgjrchx','dataset-sylpgjc','sylpgjc_jsylszdw','main-abdsubgjrchx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jsylszdw%main-abdsubgjrchx'
);

-- 双一流评估监测_建设一流师资队伍_教师获奖励及荣誉-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jsylszdw%main-abdsubjshjljry','dataset-sylpgjc','sylpgjc_jsylszdw','main-abdsubjshjljry',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jsylszdw%main-abdsubjshjljry'
);

-- 双一流评估监测_建设一流师资队伍_教师学术期刊任职-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jsylszdw%main-abdsubjsxsqkrz','dataset-sylpgjc','sylpgjc_jsylszdw','main-abdsubjsxsqkrz',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jsylszdw%main-abdsubjsxsqkrz'
);

-- 双一流评估监测_建设一流师资队伍_教师学术团体任职-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jsylszdw%main-abdsubjsxsttrz','dataset-sylpgjc','sylpgjc_jsylszdw','main-abdsubjsxsttrz',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jsylszdw%main-abdsubjsxsttrz'
);

-- 双一流评估监测_建设一流师资队伍_教师重大比赛任职-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jsylszdw%main-abdsubjszdbsrz','dataset-sylpgjc','sylpgjc_jsylszdw','main-abdsubjszdbsrz',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jsylszdw%main-abdsubjszdbsrz'
);

-- 双一流评估监测_建设一流师资队伍_博士后任职记录-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jsylszdw%main-abdsubbshrzjl','dataset-sylpgjc','sylpgjc_jsylszdw','main-abdsubbshrzjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jsylszdw%main-abdsubbshrzjl'
);

-- 双一流评估监测_建设一流师资队伍_科研助理聘任记录-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jsylszdw%main-abdsubkyzlprjl','dataset-sylpgjc','sylpgjc_jsylszdw','main-abdsubkyzlprjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jsylszdw%main-abdsubkyzlprjl'
);

-- 双一流评估监测_建设一流师资队伍_学科方向信息-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jsylszdw%main-abdsubxkfxxx','dataset-sylpgjc','sylpgjc_jsylszdw','main-abdsubxkfxxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jsylszdw%main-abdsubxkfxxx'
);

-- 双一流评估监测_建设一流师资队伍_专任教师队伍_杰出人才清单_每年杰出项目获得总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jsylszdw_zrjsdw_jcrcqd%main-hldsisub0028','dataset-sylpgjc','sylpgjc_jsylszdw_zrjsdw_jcrcqd','main-hldsisub0028',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jsylszdw_zrjsdw_jcrcqd%main-hldsisub0028'
);

-- 双一流评估监测_建设一流师资队伍_专任教师队伍_学科专任教师数量及结构_每学年专任教师总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jsylszdw_zrjsdw_xkzrjssljjg%main-hldsisub0029','dataset-sylpgjc','sylpgjc_jsylszdw_zrjsdw_xkzrjssljjg','main-hldsisub0029',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jsylszdw_zrjsdw_xkzrjssljjg%main-hldsisub0029'
);

-- 双一流评估监测_建设一流师资队伍_专任教师队伍_学科专任教师数量及结构_每学年不同年龄段专任教师总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jsylszdw_zrjsdw_xkzrjssljjg%main-hldsisub0030','dataset-sylpgjc','sylpgjc_jsylszdw_zrjsdw_xkzrjssljjg','main-hldsisub0030',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jsylszdw_zrjsdw_xkzrjssljjg%main-hldsisub0030'
);

-- 双一流评估监测_建设一流师资队伍_专任教师队伍_学科专任教师数量及结构_每学年不同职称层次专任教师总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jsylszdw_zrjsdw_xkzrjssljjg%main-hldsisub0031','dataset-sylpgjc','sylpgjc_jsylszdw_zrjsdw_xkzrjssljjg','main-hldsisub0031',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jsylszdw_zrjsdw_xkzrjssljjg%main-hldsisub0031'
);

-- 双一流评估监测_建设一流师资队伍_专任教师队伍_学科专任教师数量及结构_每学年不同学位层次专任教师总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jsylszdw_zrjsdw_xkzrjssljjg%main-hldsisub0032','dataset-sylpgjc','sylpgjc_jsylszdw_zrjsdw_xkzrjssljjg','main-hldsisub0032',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jsylszdw_zrjsdw_xkzrjssljjg%main-hldsisub0032'
);

-- 双一流评估监测_建设一流师资队伍_专任教师队伍_学科专任教师数量及结构_每学年具有海外经历专任教师比例-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jsylszdw_zrjsdw_xkzrjssljjg%main-hldsisub0033','dataset-sylpgjc','sylpgjc_jsylszdw_zrjsdw_xkzrjssljjg','main-hldsisub0033',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jsylszdw_zrjsdw_xkzrjssljjg%main-hldsisub0033'
);

-- 双一流评估监测_建设一流师资队伍_专任教师队伍_学科专任教师数量及结构_每学年博士学位专任教师比例-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jsylszdw_zrjsdw_xkzrjssljjg%main-hldsisub0034','dataset-sylpgjc','sylpgjc_jsylszdw_zrjsdw_xkzrjssljjg','main-hldsisub0034',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jsylszdw_zrjsdw_xkzrjssljjg%main-hldsisub0034'
);

-- 双一流评估监测_建设一流师资队伍_专任教师队伍_博士后和科研助理数量_每年不同类型的博士后总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jsylszdw_zrjsdw_bshhkyzlsl%main-hldsisub0035','dataset-sylpgjc','sylpgjc_jsylszdw_zrjsdw_bshhkyzlsl','main-hldsisub0035',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jsylszdw_zrjsdw_bshhkyzlsl%main-hldsisub0035'
);

-- 双一流评估监测_建设一流师资队伍_专任教师队伍_博士后和科研助理数量_每年不同聘任类型的科研助理总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jsylszdw_zrjsdw_bshhkyzlsl%main-hldsisub0036','dataset-sylpgjc','sylpgjc_jsylszdw_zrjsdw_bshhkyzlsl','main-hldsisub0036',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jsylszdw_zrjsdw_bshhkyzlsl%main-hldsisub0036'
);

-- 双一流评估监测_建设一流师资队伍_专任教师队伍_外籍专任教师数_每学年外籍专任教师总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jsylszdw_zrjsdw_wjzrjss%main-hldsisub0037','dataset-sylpgjc','sylpgjc_jsylszdw_zrjsdw_wjzrjss','main-hldsisub0037',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jsylszdw_zrjsdw_wjzrjss%main-hldsisub0037'
);

-- 双一流评估监测_建设一流师资队伍_专任教师队伍_外籍专任教师数_每学年不同职称层次下的外籍专任教师总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jsylszdw_zrjsdw_wjzrjss%main-hldsisub0038','dataset-sylpgjc','sylpgjc_jsylszdw_zrjsdw_wjzrjss','main-hldsisub0038',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jsylszdw_zrjsdw_wjzrjss%main-hldsisub0038'
);

-- 双一流评估监测_建设一流师资队伍_专任教师队伍_外籍专任教师数_每学年外籍专任教师比例-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jsylszdw_zrjsdw_wjzrjss%main-hldsisub0039','dataset-sylpgjc','sylpgjc_jsylszdw_zrjsdw_wjzrjss','main-hldsisub0039',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jsylszdw_zrjsdw_wjzrjss%main-hldsisub0039'
);

-- 双一流评估监测_建设一流师资队伍_专任教师队伍_外籍专任教师数_每学年语言类外籍专任教师总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jsylszdw_zrjsdw_wjzrjss%main-hldsisub0040','dataset-sylpgjc','sylpgjc_jsylszdw_zrjsdw_wjzrjss','main-hldsisub0040',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jsylszdw_zrjsdw_wjzrjss%main-hldsisub0040'
);

-- 双一流评估监测_建设一流师资队伍_专任教师队伍_外籍专任教师数_每学年专业类外籍专任教师总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jsylszdw_zrjsdw_wjzrjss%main-hldsisub0041','dataset-sylpgjc','sylpgjc_jsylszdw_zrjsdw_wjzrjss','main-hldsisub0041',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jsylszdw_zrjsdw_wjzrjss%main-hldsisub0041'
);

-- 双一流评估监测_建设一流师资队伍_专任教师队伍_外籍专任教师数_每学年不同学位层次下的外籍专任教师总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jsylszdw_zrjsdw_wjzrjss%main-hldsisub0042','dataset-sylpgjc','sylpgjc_jsylszdw_zrjsdw_wjzrjss','main-hldsisub0042',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jsylszdw_zrjsdw_wjzrjss%main-hldsisub0042'
);

-- 双一流评估监测_建设一流师资队伍_师资队伍国际水平_教师担任国内外重要期刊负责人清单_每年教师担任不同职务国内外重要期刊负责人总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jsylszdw_szdwgjsp_jsdrgnwzyqkfzrqd%main-hldsisub0043','dataset-sylpgjc','sylpgjc_jsylszdw_szdwgjsp_jsdrgnwzyqkfzrqd','main-hldsisub0043',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jsylszdw_szdwgjsp_jsdrgnwzyqkfzrqd%main-hldsisub0043'
);

-- 双一流评估监测_建设一流师资队伍_师资队伍国际水平_教师担任国内外重要期刊负责人清单_每年新增担任SCI期刊职务人数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jsylszdw_szdwgjsp_jsdrgnwzyqkfzrqd%main-hldsisub0044','dataset-sylpgjc','sylpgjc_jsylszdw_szdwgjsp_jsdrgnwzyqkfzrqd','main-hldsisub0044',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jsylszdw_szdwgjsp_jsdrgnwzyqkfzrqd%main-hldsisub0044'
);

-- 双一流评估监测_建设一流师资队伍_师资队伍国际水平_教师担任国内外重要期刊负责人清单_每年新增担任EI期刊职务人数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jsylszdw_szdwgjsp_jsdrgnwzyqkfzrqd%main-hldsisub0046','dataset-sylpgjc','sylpgjc_jsylszdw_szdwgjsp_jsdrgnwzyqkfzrqd','main-hldsisub0046',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jsylszdw_szdwgjsp_jsdrgnwzyqkfzrqd%main-hldsisub0046'
);

-- 双一流评估监测_建设一流师资队伍_师资队伍国际水平_教师在国内外重要学术组织任职主要负责人清单_每年教师新增在重要学术组织任职数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jsylszdw_szdwgjsp_jszgnwzyxszzrzzyfzrqd%main-hldsisub0047','dataset-sylpgjc','sylpgjc_jsylszdw_szdwgjsp_jszgnwzyxszzrzzyfzrqd','main-hldsisub0047',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jsylszdw_szdwgjsp_jszgnwzyxszzrzzyfzrqd%main-hldsisub0047'
);

-- 双一流评估监测_建设一流师资队伍_师资队伍国际水平_教师参加本领域重要学术会议并作报告人员清单_每年教师参加本领域重要学术会议并作报告总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jsylszdw_szdwgjsp_jscjblyzyxshybzbgryqd%main-hldsisub0048','dataset-sylpgjc','sylpgjc_jsylszdw_szdwgjsp_jscjblyzyxshybzbgryqd','main-hldsisub0048',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jsylszdw_szdwgjsp_jscjblyzyxshybzbgryqd%main-hldsisub0048'
);

-- 双一流评估监测_建设一流师资队伍_师资队伍国际水平_教师担任国际比赛评委、裁判人员清单_每年教师担任国际比赛评委、裁判人员总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_jsylszdw_szdwgjsp_jsdrgjbspw、cpryqd%main-hldsisub0049','dataset-sylpgjc','sylpgjc_jsylszdw_szdwgjsp_jsdrgjbspw、cpryqd','main-hldsisub0049',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_jsylszdw_szdwgjsp_jsdrgjbspw、cpryqd%main-hldsisub0049'
);

-- 双一流评估监测_提升科学研究水平_教师发表论文-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp%main-abdsubjsfblw','dataset-sylpgjc','sylpgjc_tskxyjsp','main-abdsubjsfblw',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp%main-abdsubjsfblw'
);

-- 双一流评估监测_提升科学研究水平_教师出版著作-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp%main-abdsubjscbzz','dataset-sylpgjc','sylpgjc_tskxyjsp','main-abdsubjscbzz',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp%main-abdsubjscbzz'
);

-- 双一流评估监测_提升科学研究水平_教师参与科研项目-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp%main-abdsubjscykyxm','dataset-sylpgjc','sylpgjc_tskxyjsp','main-abdsubjscykyxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp%main-abdsubjscykyxm'
);

-- 双一流评估监测_提升科学研究水平_教师科研成果获奖-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp%main-abdsubjskycghj','dataset-sylpgjc','sylpgjc_tskxyjsp','main-abdsubjskycghj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp%main-abdsubjskycghj'
);

-- 双一流评估监测_提升科学研究水平_教师发明专利-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp%main-abdsubjsfmzl','dataset-sylpgjc','sylpgjc_tskxyjsp','main-abdsubjsfmzl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp%main-abdsubjsfmzl'
);

-- 双一流评估监测_提升科学研究水平_教师制定标准-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp%main-abdsubjszdbz','dataset-sylpgjc','sylpgjc_tskxyjsp','main-abdsubjszdbz',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp%main-abdsubjszdbz'
);

-- 双一流评估监测_提升科学研究水平_承担国内外设计与展演-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp%main-abdsubcdgnwsjyzy','dataset-sylpgjc','sylpgjc_tskxyjsp','main-abdsubcdgnwsjyzy',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp%main-abdsubcdgnwsjyzy'
);

-- 双一流评估监测_提升科学研究水平_科学研究与实践创新_教师获得的国内外重要奖项清单_每年教师获得的国家三大奖总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_jshddgnwzyjxqd%main-hldsisub0050','dataset-sylpgjc','sylpgjc_tskxyjsp_kxyjysjcx_jshddgnwzyjxqd','main-hldsisub0050',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_jshddgnwzyjxqd%main-hldsisub0050'
);

-- 双一流评估监测_提升科学研究水平_科学研究与实践创新_教师获得的国内外重要奖项清单_每年教师获得中国社会科学优秀成果奖数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_jshddgnwzyjxqd%main-hldsisub0051','dataset-sylpgjc','sylpgjc_tskxyjsp_kxyjysjcx_jshddgnwzyjxqd','main-hldsisub0051',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_jshddgnwzyjxqd%main-hldsisub0051'
);

-- 双一流评估监测_提升科学研究水平_科学研究与实践创新_教师获得的国内外重要奖项清单_每年教师获得中国医学科学奖数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_jshddgnwzyjxqd%main-hldsisub0052','dataset-sylpgjc','sylpgjc_tskxyjsp_kxyjysjcx_jshddgnwzyjxqd','main-hldsisub0052',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_jshddgnwzyjxqd%main-hldsisub0052'
);

-- 双一流评估监测_提升科学研究水平_科学研究与实践创新_教师获得的国内外重要奖项清单_每年教师获得不同组织单位的国内外重要奖项总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_jshddgnwzyjxqd%main-hldsisub0053','dataset-sylpgjc','sylpgjc_tskxyjsp_kxyjysjcx_jshddgnwzyjxqd','main-hldsisub0053',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_jshddgnwzyjxqd%main-hldsisub0053'
);

-- 双一流评估监测_提升科学研究水平_科学研究与实践创新_教师公开出版的专著清单_每年教师公开出版的专著总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_jsgkcbdzzqd%main-hldsisub0054','dataset-sylpgjc','sylpgjc_tskxyjsp_kxyjysjcx_jsgkcbdzzqd','main-hldsisub0054',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_jsgkcbdzzqd%main-hldsisub0054'
);

-- 双一流评估监测_提升科学研究水平_科学研究与实践创新_教师在国内外重要期刊发表的代表性论文清单_每年教师在国内外重要期刊发表的代表性论文总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_jszgnwzyqkfbddbxlwqd%main-hldsisub0055','dataset-sylpgjc','sylpgjc_tskxyjsp_kxyjysjcx_jszgnwzyqkfbddbxlwqd','main-hldsisub0055',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_jszgnwzyqkfbddbxlwqd%main-hldsisub0055'
);

-- 双一流评估监测_提升科学研究水平_科学研究与实践创新_教师在国内外重要期刊发表的代表性论文清单_每年新增SCI收录期刊论文数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_jszgnwzyqkfbddbxlwqd%main-hldsisub0056','dataset-sylpgjc','sylpgjc_tskxyjsp_kxyjysjcx_jszgnwzyqkfbddbxlwqd','main-hldsisub0056',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_jszgnwzyqkfbddbxlwqd%main-hldsisub0056'
);

-- 双一流评估监测_提升科学研究水平_科学研究与实践创新_教师在国内外重要期刊发表的代表性论文清单_每年新增EI收录期刊论文数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_jszgnwzyqkfbddbxlwqd%main-hldsisub0058','dataset-sylpgjc','sylpgjc_tskxyjsp_kxyjysjcx_jszgnwzyqkfbddbxlwqd','main-hldsisub0058',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_jszgnwzyqkfbddbxlwqd%main-hldsisub0058'
);

-- 双一流评估监测_提升科学研究水平_科学研究与实践创新_教师在国内外重要期刊发表的代表性论文清单_每年新增Nature期刊论文数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_jszgnwzyqkfbddbxlwqd%main-hldsisub0059','dataset-sylpgjc','sylpgjc_tskxyjsp_kxyjysjcx_jszgnwzyqkfbddbxlwqd','main-hldsisub0059',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_jszgnwzyqkfbddbxlwqd%main-hldsisub0059'
);

-- 双一流评估监测_提升科学研究水平_科学研究与实践创新_教师在国内外重要期刊发表的代表性论文清单_每年新增Science期刊论文数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_jszgnwzyqkfbddbxlwqd%main-hldsisub0060','dataset-sylpgjc','sylpgjc_tskxyjsp_kxyjysjcx_jszgnwzyqkfbddbxlwqd','main-hldsisub0060',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_jszgnwzyqkfbddbxlwqd%main-hldsisub0060'
);

-- 双一流评估监测_提升科学研究水平_科学研究与实践创新_教师在国内外重要期刊发表的代表性论文清单_每年新增Cell期刊论文数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_jszgnwzyqkfbddbxlwqd%main-hldsisub0061','dataset-sylpgjc','sylpgjc_tskxyjsp_kxyjysjcx_jszgnwzyqkfbddbxlwqd','main-hldsisub0061',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_jszgnwzyqkfbddbxlwqd%main-hldsisub0061'
);

-- 双一流评估监测_提升科学研究水平_科学研究与实践创新_教师在国内外重要期刊发表的代表性论文清单_每年教师在不同收录类别下发表的代表性论文总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_jszgnwzyqkfbddbxlwqd%main-hldsisub0062','dataset-sylpgjc','sylpgjc_tskxyjsp_kxyjysjcx_jszgnwzyqkfbddbxlwqd','main-hldsisub0062',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_jszgnwzyqkfbddbxlwqd%main-hldsisub0062'
);

-- 双一流评估监测_提升科学研究水平_科学研究与实践创新_授权发明专利_每年新增授权发明专利数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_sqfmzl%main-hldsisub0063','dataset-sylpgjc','sylpgjc_tskxyjsp_kxyjysjcx_sqfmzl','main-hldsisub0063',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_sqfmzl%main-hldsisub0063'
);

-- 双一流评估监测_提升科学研究水平_科学研究与实践创新_承担科研项目_每年新增国家自然科学基金数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_cdkyxm%main-hldsisub0064','dataset-sylpgjc','sylpgjc_tskxyjsp_kxyjysjcx_cdkyxm','main-hldsisub0064',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_cdkyxm%main-hldsisub0064'
);

-- 双一流评估监测_提升科学研究水平_科学研究与实践创新_承担科研项目_每年新增国家自然科学基金重大项目数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_cdkyxm%main-hldsisub0065','dataset-sylpgjc','sylpgjc_tskxyjsp_kxyjysjcx_cdkyxm','main-hldsisub0065',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_cdkyxm%main-hldsisub0065'
);

-- 双一流评估监测_提升科学研究水平_科学研究与实践创新_承担科研项目_每年新增国家社会科学基金项目数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_cdkyxm%main-hldsisub0067','dataset-sylpgjc','sylpgjc_tskxyjsp_kxyjysjcx_cdkyxm','main-hldsisub0067',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_cdkyxm%main-hldsisub0067'
);

-- 双一流评估监测_提升科学研究水平_科学研究与实践创新_承担国内外重大设计与展演任务清单_每年承担不同任务的国内外重大设计与展演任务总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_cdgnwzdsjyzyrwqd%main-hldsisub0068','dataset-sylpgjc','sylpgjc_tskxyjsp_kxyjysjcx_cdgnwzdsjyzyrwqd','main-hldsisub0068',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kxyjysjcx_cdgnwzdsjyzyrwqd%main-hldsisub0068'
);

-- 双一流评估监测_提升科学研究水平_科研平台建设_国家重大科技创新平台和基地清单、绩效评估情况_国家级重大平台总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_gjzdkjcxpthjdqd、jxpgqk%main-hldsisub0069','dataset-sylpgjc','sylpgjc_tskxyjsp_kyptjs_gjzdkjcxpthjdqd、jxpgqk','main-hldsisub0069',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_gjzdkjcxpthjdqd、jxpgqk%main-hldsisub0069'
);

-- 双一流评估监测_提升科学研究水平_科研平台建设_国家重大科技创新平台和基地清单、绩效评估情况_每年国家级重大平台获得经费总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_gjzdkjcxpthjdqd、jxpgqk%main-hldsisub0070','dataset-sylpgjc','sylpgjc_tskxyjsp_kyptjs_gjzdkjcxpthjdqd、jxpgqk','main-hldsisub0070',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_gjzdkjcxpthjdqd、jxpgqk%main-hldsisub0070'
);

-- 双一流评估监测_提升科学研究水平_科研平台建设_国家重大科技创新平台和基地清单、绩效评估情况_每年国家级重大平台支持项目数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_gjzdkjcxpthjdqd、jxpgqk%main-hldsisub0071','dataset-sylpgjc','sylpgjc_tskxyjsp_kyptjs_gjzdkjcxpthjdqd、jxpgqk','main-hldsisub0071',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_gjzdkjcxpthjdqd、jxpgqk%main-hldsisub0071'
);

-- 双一流评估监测_提升科学研究水平_科研平台建设_国家重大科技创新平台和基地清单、绩效评估情况_每年新增国家重点实验室数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_gjzdkjcxpthjdqd、jxpgqk%main-hldsisub0072','dataset-sylpgjc','sylpgjc_tskxyjsp_kyptjs_gjzdkjcxpthjdqd、jxpgqk','main-hldsisub0072',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_gjzdkjcxpthjdqd、jxpgqk%main-hldsisub0072'
);

-- 双一流评估监测_提升科学研究水平_科研平台建设_国家重大科技创新平台和基地清单、绩效评估情况_每年新增国家工程研究中心数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_gjzdkjcxpthjdqd、jxpgqk%main-hldsisub0073','dataset-sylpgjc','sylpgjc_tskxyjsp_kyptjs_gjzdkjcxpthjdqd、jxpgqk','main-hldsisub0073',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_gjzdkjcxpthjdqd、jxpgqk%main-hldsisub0073'
);

-- 双一流评估监测_提升科学研究水平_科研平台建设_国家重大科技创新平台和基地清单、绩效评估情况_每年新增国家临床医学中心数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_gjzdkjcxpthjdqd、jxpgqk%main-hldsisub0074','dataset-sylpgjc','sylpgjc_tskxyjsp_kyptjs_gjzdkjcxpthjdqd、jxpgqk','main-hldsisub0074',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_gjzdkjcxpthjdqd、jxpgqk%main-hldsisub0074'
);

-- 双一流评估监测_提升科学研究水平_科研平台建设_国家重大科技创新平台和基地清单、绩效评估情况_每年新增教育部人文社科重点研究基地数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_gjzdkjcxpthjdqd、jxpgqk%main-hldsisub0075','dataset-sylpgjc','sylpgjc_tskxyjsp_kyptjs_gjzdkjcxpthjdqd、jxpgqk','main-hldsisub0075',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_gjzdkjcxpthjdqd、jxpgqk%main-hldsisub0075'
);

-- 双一流评估监测_提升科学研究水平_科研平台建设_部省级重点研究基地清单、绩效评估情况_省部级重大平台总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_bsjzdyjjdqd、jxpgqk%main-hldsisub0076','dataset-sylpgjc','sylpgjc_tskxyjsp_kyptjs_bsjzdyjjdqd、jxpgqk','main-hldsisub0076',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_bsjzdyjjdqd、jxpgqk%main-hldsisub0076'
);

-- 双一流评估监测_提升科学研究水平_科研平台建设_部省级重点研究基地清单、绩效评估情况_每年省部级重大平台获得经费总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_bsjzdyjjdqd、jxpgqk%main-hldsisub0077','dataset-sylpgjc','sylpgjc_tskxyjsp_kyptjs_bsjzdyjjdqd、jxpgqk','main-hldsisub0077',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_bsjzdyjjdqd、jxpgqk%main-hldsisub0077'
);

-- 双一流评估监测_提升科学研究水平_科研平台建设_部省级重点研究基地清单、绩效评估情况_每年省部级重大平台支持项目数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_bsjzdyjjdqd、jxpgqk%main-hldsisub0078','dataset-sylpgjc','sylpgjc_tskxyjsp_kyptjs_bsjzdyjjdqd、jxpgqk','main-hldsisub0078',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_bsjzdyjjdqd、jxpgqk%main-hldsisub0078'
);

-- 双一流评估监测_提升科学研究水平_科研平台建设_纵向、横向到校科研经费数_每月纵向科研项目经费到账数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_zx、hxdxkyjfs%main-hldsisub0079','dataset-sylpgjc','sylpgjc_tskxyjsp_kyptjs_zx、hxdxkyjfs','main-hldsisub0079',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_zx、hxdxkyjfs%main-hldsisub0079'
);

-- 双一流评估监测_提升科学研究水平_科研平台建设_纵向、横向到校科研经费数_每月纵向科研项目立项数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_zx、hxdxkyjfs%main-hldsisub0080','dataset-sylpgjc','sylpgjc_tskxyjsp_kyptjs_zx、hxdxkyjfs','main-hldsisub0080',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_zx、hxdxkyjfs%main-hldsisub0080'
);

-- 双一流评估监测_提升科学研究水平_科研平台建设_纵向、横向到校科研经费数_每月横向科研经费到账总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_zx、hxdxkyjfs%main-hldsisub0081','dataset-sylpgjc','sylpgjc_tskxyjsp_kyptjs_zx、hxdxkyjfs','main-hldsisub0081',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_zx、hxdxkyjfs%main-hldsisub0081'
);

-- 双一流评估监测_提升科学研究水平_科研平台建设_纵向、横向到校科研经费数_每月横向科研项目立项数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_zx、hxdxkyjfs%main-hldsisub0082','dataset-sylpgjc','sylpgjc_tskxyjsp_kyptjs_zx、hxdxkyjfs','main-hldsisub0082',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_zx、hxdxkyjfs%main-hldsisub0082'
);

-- 双一流评估监测_提升科学研究水平_科研平台建设_纵向、横向到校科研经费数_每年地方政府投入项目总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_zx、hxdxkyjfs%main-hldsisub0083','dataset-sylpgjc','sylpgjc_tskxyjsp_kyptjs_zx、hxdxkyjfs','main-hldsisub0083',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_zx、hxdxkyjfs%main-hldsisub0083'
);

-- 双一流评估监测_提升科学研究水平_科研平台建设_纵向、横向到校科研经费数_每年地方政府投入项目经费总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_zx、hxdxkyjfs%main-hldsisub0084','dataset-sylpgjc','sylpgjc_tskxyjsp_kyptjs_zx、hxdxkyjfs','main-hldsisub0084',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_zx、hxdxkyjfs%main-hldsisub0084'
);

-- 双一流评估监测_提升科学研究水平_科研平台建设_纵向、横向到校科研经费数_每年人均到账科研经费-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_zx、hxdxkyjfs%main-hldsisub0085','dataset-sylpgjc','sylpgjc_tskxyjsp_kyptjs_zx、hxdxkyjfs','main-hldsisub0085',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_zx、hxdxkyjfs%main-hldsisub0085'
);

-- 双一流评估监测_提升科学研究水平_科研平台建设_主办的国际学术期刊清单_每年主办的国际学术期刊总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_zbdgjxsqkqd%main-hldsisub0086','dataset-sylpgjc','sylpgjc_tskxyjsp_kyptjs_zbdgjxsqkqd','main-hldsisub0086',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_kyptjs_zbdgjxsqkqd%main-hldsisub0086'
);

-- 双一流评估监测_提升科学研究水平_国际影响力_参与国内外标准制定项目清单_每年参与制定的国内外标准总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_tskxyjsp_gjyxl_cygnwbzzdxmqd%main-hldsisub0087','dataset-sylpgjc','sylpgjc_tskxyjsp_gjyxl_cygnwbzzdxmqd','main-hldsisub0087',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_tskxyjsp_gjyxl_cygnwbzzdxmqd%main-hldsisub0087'
);

-- 双一流评估监测_学科建设进展_建设进展_学科建设经费数额_每年建设预算经费总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_xkjsjz_jsjz_xkjsjfse%main-hldsisub0001','dataset-sylpgjc','sylpgjc_xkjsjz_jsjz_xkjsjfse','main-hldsisub0001',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_xkjsjz_jsjz_xkjsjfse%main-hldsisub0001'
);

-- 双一流评估监测_学科建设进展_建设进展_学科建设经费数额_每年建设经费支出总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_xkjsjz_jsjz_xkjsjfse%main-hldsisub0002','dataset-sylpgjc','sylpgjc_xkjsjz_jsjz_xkjsjfse','main-hldsisub0002',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_xkjsjz_jsjz_xkjsjfse%main-hldsisub0002'
);

-- 双一流评估监测_学科建设进展_建设进展_学科建设经费数额_每年不同项目类型预算经费-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_xkjsjz_jsjz_xkjsjfse%main-hldsisub0003','dataset-sylpgjc','sylpgjc_xkjsjz_jsjz_xkjsjfse','main-hldsisub0003',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_xkjsjz_jsjz_xkjsjfse%main-hldsisub0003'
);

-- 双一流评估监测_学科建设进展_建设进展_学科建设经费数额_每年不同项目类型实际经费投入-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_xkjsjz_jsjz_xkjsjfse%main-hldsisub0004','dataset-sylpgjc','sylpgjc_xkjsjz_jsjz_xkjsjfse','main-hldsisub0004',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_xkjsjz_jsjz_xkjsjfse%main-hldsisub0004'
);

-- 双一流评估监测_学科建设进展_建设进展_学科建设经费数额_每年不同投入方对应预算经费总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_xkjsjz_jsjz_xkjsjfse%main-hldsisub0005','dataset-sylpgjc','sylpgjc_xkjsjz_jsjz_xkjsjfse','main-hldsisub0005',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_xkjsjz_jsjz_xkjsjfse%main-hldsisub0005'
);

-- 双一流评估监测_学科建设进展_建设进展_学科建设经费数额_每年不同投入方对应实际支出总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_xkjsjz_jsjz_xkjsjfse%main-hldsisub0006','dataset-sylpgjc','sylpgjc_xkjsjz_jsjz_xkjsjfse','main-hldsisub0006',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_xkjsjz_jsjz_xkjsjfse%main-hldsisub0006'
);

-- 双一流评估监测_社会服务_成果转化_成果转化和咨询服务到校金额_每年成果转化总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_shfw_cgzh_cgzhhzxfwdxje%main-hldsisub0088','dataset-sylpgjc','sylpgjc_shfw_cgzh_cgzhhzxfwdxje','main-hldsisub0088',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_shfw_cgzh_cgzhhzxfwdxje%main-hldsisub0088'
);

-- 双一流评估监测_社会服务_成果转化_成果转化和咨询服务到校金额_每年人均转化合同经费-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_shfw_cgzh_cgzhhzxfwdxje%main-hldsisub0089','dataset-sylpgjc','sylpgjc_shfw_cgzh_cgzhhzxfwdxje','main-hldsisub0089',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_shfw_cgzh_cgzhhzxfwdxje%main-hldsisub0089'
);

-- 双一流评估监测_社会服务_成果转化_成果转化和咨询服务到校金额_每年人均横向项目合同经费-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_shfw_cgzh_cgzhhzxfwdxje%main-hldsisub0090','dataset-sylpgjc','sylpgjc_shfw_cgzh_cgzhhzxfwdxje','main-hldsisub0090',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_shfw_cgzh_cgzhhzxfwdxje%main-hldsisub0090'
);

-- 双一流评估监测_社会服务_成果转化_成果转化和咨询服务到校金额_每年咨询服务到校金额总数-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-sylpgjc%sylpgjc_shfw_cgzh_cgzhhzxfwdxje%main-hldsisub0091','dataset-sylpgjc','sylpgjc_shfw_cgzh_cgzhhzxfwdxje','main-hldsisub0091',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-sylpgjc%sylpgjc_shfw_cgzh_cgzhhzxfwdxje%main-hldsisub0091'
);