/*
 Description		: [本科审核评估(bkteachingeval)]应用数据集
 Author				: dataapp
 Date				: 2024-06-20 01:00:00
 Lowcode Version	: V1.1.1_Beta5
 Database           : oracle
*/

-- 数据集模型字段
BEGIN
    EXECUTE IMMEDIATE 'CREATE TABLE t_da_dataset_model_column ( dataset_id VARCHAR2(50) NOT NULL,
column_id VARCHAR2(50) NOT NULL,
model_id VARCHAR2(50) NOT NULL,
id VARCHAR2(100),
create_by VARCHAR2(100),
create_time DATE,
update_by VARCHAR2(100),
update_time DATE,PRIMARY KEY (id) )';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

COMMENT ON TABLE t_da_dataset_model_column IS '数据集模型字段';
COMMENT ON COLUMN t_da_dataset_model_column.dataset_id IS '数据集ID';
COMMENT ON COLUMN t_da_dataset_model_column.column_id IS '字段ID';
COMMENT ON COLUMN t_da_dataset_model_column.model_id IS '模型ID';
COMMENT ON COLUMN t_da_dataset_model_column.id IS 'ID';
COMMENT ON COLUMN t_da_dataset_model_column.create_by IS '创建人';
COMMENT ON COLUMN t_da_dataset_model_column.create_time IS '创建时间';
COMMENT ON COLUMN t_da_dataset_model_column.update_by IS '更新人';
COMMENT ON COLUMN t_da_dataset_model_column.update_time IS '更新时间';

-- 本科审核评估-数据集
INSERT INTO t_da_dataset (id,sjjmc,
create_by,create_time,update_by,update_time,
copy_label,primary_app_id)
SELECT 'dataset-bkshpg','本科审核评估',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'','bkteachingeval'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset
    WHERE id = 'dataset-bkshpg'
);

-- dataset-bkshpg-bkteachingeval-数据集和应用关联表
INSERT INTO t_da_dataset_app (id,sjjid,appid,
create_by,create_time,update_by,update_time)
SELECT 'dataset-bkshpg-bkteachingeval','dataset-bkshpg','bkteachingeval',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_app
    WHERE id = 'dataset-bkshpg-bkteachingeval'
);

-- 教学资源-MAJ_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_jxzy','dataset-bkshpg','教学资源',NULL,1,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_jxzy'
);

-- 专业建设-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_jxzy_zyjs','dataset-bkshpg','专业建设','bkshpg_jxzy',2,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_jxzy_zyjs'
);

-- 实践资源-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_jxzy_sjzy','dataset-bkshpg','实践资源','bkshpg_jxzy',3,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_jxzy_sjzy'
);

-- 教学设施-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_jxzy_jxss','dataset-bkshpg','教学设施','bkshpg_jxzy',4,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_jxzy_jxss'
);

-- 课程资源-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_jxzy_kczy','dataset-bkshpg','课程资源','bkshpg_jxzy',5,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_jxzy_kczy'
);

-- 经费投入-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_jxzy_jftr','dataset-bkshpg','经费投入','bkshpg_jxzy',6,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_jxzy_jftr'
);

-- 培养过程-MAJ_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_pygc','dataset-bkshpg','培养过程',NULL,7,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_pygc'
);

-- 教学改革-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_pygc_jxgg','dataset-bkshpg','教学改革','bkshpg_pygc',8,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_pygc_jxgg'
);

-- 双创教育-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_pygc_scjy','dataset-bkshpg','双创教育','bkshpg_pygc',9,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_pygc_scjy'
);

-- 实践实习-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_pygc_sjsx','dataset-bkshpg','实践实习','bkshpg_pygc',10,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_pygc_sjsx'
);

-- 毕业实践-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_pygc_bysj','dataset-bkshpg','毕业实践','bkshpg_pygc',11,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_pygc_bysj'
);

-- 师资队伍-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_szdw','dataset-bkshpg','师资队伍',NULL,12,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_szdw'
);

-- 数量结构-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_szdw_sljg','dataset-bkshpg','数量结构','bkshpg_szdw',13,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_szdw_sljg'
);

-- 生师比-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_szdw_ssb','dataset-bkshpg','生师比','bkshpg_szdw',14,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_szdw_ssb'
);

-- 教学投入-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_szdw_jxtr','dataset-bkshpg','教学投入','bkshpg_szdw',15,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_szdw_jxtr'
);

-- 教师发展-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_szdw_jsfz','dataset-bkshpg','教师发展','bkshpg_szdw',16,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_szdw_jsfz'
);

-- 教学能力-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_szdw_jxnl','dataset-bkshpg','教学能力','bkshpg_szdw',17,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_szdw_jxnl'
);

-- 招生情况-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_zsqk','dataset-bkshpg','招生情况',NULL,18,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_zsqk'
);

-- 生源情况-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_zsqk_syqk','dataset-bkshpg','生源情况','bkshpg_zsqk',19,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_zsqk_syqk'
);

-- 报到情况-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_zsqk_bdqk','dataset-bkshpg','报到情况','bkshpg_zsqk',20,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_zsqk_bdqk'
);

-- 质量评估-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_zlpg','dataset-bkshpg','质量评估',NULL,21,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_zlpg'
);

-- 学生评教-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_zlpg_xspj','dataset-bkshpg','学生评教','bkshpg_zlpg',22,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_zlpg_xspj'
);

-- 随堂听课-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_zlpg_sttk','dataset-bkshpg','随堂听课','bkshpg_zlpg',23,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_zlpg_sttk'
);

-- 专项检查-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_zlpg_zxjc','dataset-bkshpg','专项检查','bkshpg_zlpg',24,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_zlpg_zxjc'
);

-- 质量队伍-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_zlpg_zldw','dataset-bkshpg','质量队伍','bkshpg_zlpg',25,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_zlpg_zldw'
);

-- 就业与发展-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_jyyfz','dataset-bkshpg','就业与发展',NULL,26,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_jyyfz'
);

-- 学生就业-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_jyyfz_xsjy','dataset-bkshpg','学生就业','bkshpg_jyyfz',27,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_jyyfz_xsjy'
);

-- 学业预警-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_jyyfz_xyyj','dataset-bkshpg','学业预警','bkshpg_jyyfz',28,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_jyyfz_xyyj'
);

-- 学生发展-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_jyyfz_xsfz','dataset-bkshpg','学生发展','bkshpg_jyyfz',29,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_jyyfz_xsfz'
);

-- 学生工作-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_xsgz','dataset-bkshpg','学生工作',NULL,30,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_xsgz'
);

-- 学生工作-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_xsgz_xsgz','dataset-bkshpg','学生工作','bkshpg_xsgz',31,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_xsgz_xsgz'
);

-- 学生发展-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_xsfz','dataset-bkshpg','学生发展',NULL,32,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_xsfz'
);

-- 质量保障-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_zlbz','dataset-bkshpg','质量保障',NULL,33,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_zlbz'
);

-- 成果特色-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_cgts','dataset-bkshpg','成果特色',NULL,34,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_cgts'
);

-- 成果特色-SCH_SI.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_cgts_cgts','dataset-bkshpg','成果特色','bkshpg_cgts',35,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_cgts_cgts'
);

-- 教学建设-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_jxjs','dataset-bkshpg','教学建设',NULL,36,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_jxjs'
);

-- 教学成果-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_classify (id,sjjid,flmc,fjid,flpx,classify_desc,
create_by,create_time,update_by,update_time)
SELECT 'bkshpg_jxcg','dataset-bkshpg','教学成果',NULL,37,'自动生成 2024-06-20 01:00:00',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_classify
    WHERE id = 'bkshpg_jxcg'
);

-- 本科审核评估_教学资源_学校基层教学组织-MAJ_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-insxxjcjxzz','dataset-bkshpg','bkshpg_jxzy','main-insxxjcjxzz',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-insxxjcjxzz'
);

-- 本科审核评估_教学资源_专业基本情况-MAJ_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-inszyjbqk','dataset-bkshpg','bkshpg_jxzy','main-inszyjbqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-inszyjbqk'
);

-- 本科审核评估_教学资源_专业方案表-MAJ_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-abdmajzyfab','dataset-bkshpg','bkshpg_jxzy','main-abdmajzyfab',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-abdmajzyfab'
);

-- 本科审核评估_教学资源_专业建设_每年专业课程数量-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssimaj0022','dataset-bkshpg','bkshpg_jxzy_zyjs','main-inssimaj0022',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssimaj0022'
);

-- 本科审核评估_教学资源_专业建设_每年学校新设专业-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssimaj0018','dataset-bkshpg','bkshpg_jxzy_zyjs','main-inssimaj0018',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssimaj0018'
);

-- 本科审核评估_教学资源_专业建设_全校各专业教师数量-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssimaj0003','dataset-bkshpg','bkshpg_jxzy_zyjs','main-inssimaj0003',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssimaj0003'
);

-- 本科审核评估_教学资源_专业建设_全校各年级专业学生数量-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssimaj0004','dataset-bkshpg','bkshpg_jxzy_zyjs','main-inssimaj0004',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssimaj0004'
);

-- 本科审核评估_教学资源_专业建设_各专业学生毕业情况-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssimaj0014','dataset-bkshpg','bkshpg_jxzy_zyjs','main-inssimaj0014',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssimaj0014'
);

-- 本科审核评估_教学资源_专业建设_每年各专业毕业率-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssimaj0027','dataset-bkshpg','bkshpg_jxzy_zyjs','main-inssimaj0027',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssimaj0027'
);

-- 本科审核评估_教学资源_专业建设_全校各专业在籍学生数量-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssimaj0031','dataset-bkshpg','bkshpg_jxzy_zyjs','main-inssimaj0031',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssimaj0031'
);

-- 本科审核评估_教学资源_专业建设_专业人才培养情况-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssimaj0032','dataset-bkshpg','bkshpg_jxzy_zyjs','main-inssimaj0032',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssimaj0032'
);

-- 本科审核评估_教学资源_专业建设_全校专业平均总学分-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0509','dataset-bkshpg','bkshpg_jxzy_zyjs','main-inssisch0509',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0509'
);

-- 本科审核评估_教学资源_专业建设_每年学校不同类型优势专业数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0411','dataset-bkshpg','bkshpg_jxzy_zyjs','main-inssisch0411',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0411'
);

-- 本科审核评估_教学资源_专业建设_每年学校优势专业数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0508','dataset-bkshpg','bkshpg_jxzy_zyjs','main-inssisch0508',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0508'
);

-- 本科审核评估_教学资源_专业建设_每年一流专业数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0491','dataset-bkshpg','bkshpg_jxzy_zyjs','main-inssisch0491',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0491'
);

-- 本科审核评估_教学资源_专业建设_每年学校特色专业数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0492','dataset-bkshpg','bkshpg_jxzy_zyjs','main-inssisch0492',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0492'
);

-- 本科审核评估_教学资源_专业建设_每年特色专业数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0412','dataset-bkshpg','bkshpg_jxzy_zyjs','main-inssisch0412',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0412'
);

-- 本科审核评估_教学资源_专业建设_每年学校国家级双万专业数占比-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0413','dataset-bkshpg','bkshpg_jxzy_zyjs','main-inssisch0413',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0413'
);

-- 本科审核评估_教学资源_专业建设_每年学校新设专业数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0414','dataset-bkshpg','bkshpg_jxzy_zyjs','main-inssisch0414',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0414'
);

-- 本科审核评估_教学资源_专业建设_全校年度专业数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0416','dataset-bkshpg','bkshpg_jxzy_zyjs','main-inssisch0416',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0416'
);

-- 本科审核评估_教学资源_专业建设_每年全校文科招生人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0417','dataset-bkshpg','bkshpg_jxzy_zyjs','main-inssisch0417',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0417'
);

-- 本科审核评估_教学资源_专业建设_每年全校理科招生人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0418','dataset-bkshpg','bkshpg_jxzy_zyjs','main-inssisch0418',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0418'
);

-- 本科审核评估_教学资源_专业建设_全校专业调整数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0419','dataset-bkshpg','bkshpg_jxzy_zyjs','main-inssisch0419',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0419'
);

-- 本科审核评估_教学资源_专业建设_全校通过行业认证专业数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0421','dataset-bkshpg','bkshpg_jxzy_zyjs','main-inssisch0421',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0421'
);

-- 本科审核评估_教学资源_专业建设_每年学生数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0422','dataset-bkshpg','bkshpg_jxzy_zyjs','main-inssisch0422',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0422'
);

-- 本科审核评估_教学资源_专业建设_全校学生流转数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0423','dataset-bkshpg','bkshpg_jxzy_zyjs','main-inssisch0423',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0423'
);

-- 本科审核评估_教学资源_专业建设_学年内学生流动净值-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0511','dataset-bkshpg','bkshpg_jxzy_zyjs','main-inssisch0511',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0511'
);

-- 本科审核评估_教学资源_专业建设_全校毕业率-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0424','dataset-bkshpg','bkshpg_jxzy_zyjs','main-inssisch0424',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0424'
);

-- 本科审核评估_教学资源_专业建设_交叉复合型人才培养对比-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0425','dataset-bkshpg','bkshpg_jxzy_zyjs','main-inssisch0425',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisch0425'
);

-- 本科审核评估_教学资源_专业建设_专业培养方案学分结构-SUB_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisub0092','dataset-bkshpg','bkshpg_jxzy_zyjs','main-inssisub0092',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssisub0092'
);

-- 本科审核评估_教学资源_专业建设_每年学校各学院不同类型优势专业数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssicol0196','dataset-bkshpg','bkshpg_jxzy_zyjs','main-inssicol0196',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssicol0196'
);

-- 本科审核评估_教学资源_专业建设_每年各级别一流专业数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssicol0273','dataset-bkshpg','bkshpg_jxzy_zyjs','main-inssicol0273',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssicol0273'
);

-- 本科审核评估_教学资源_专业建设_每年学校各学院国家级双万专业占专业总数比例-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssicol0197','dataset-bkshpg','bkshpg_jxzy_zyjs','main-inssicol0197',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssicol0197'
);

-- 本科审核评估_教学资源_专业建设_每年各学院不同学籍状态学生数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssicol0305','dataset-bkshpg','bkshpg_jxzy_zyjs','main-inssicol0305',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_zyjs%main-inssicol0305'
);

-- 本科审核评估_教学资源_实践资源_每年各专业实习基地数量-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_sjzy%main-inssimaj0026','dataset-bkshpg','bkshpg_jxzy_sjzy','main-inssimaj0026',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_sjzy%main-inssimaj0026'
);

-- 本科审核评估_教学资源_实践资源_每年临近报废实验设备数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_sjzy%main-inssisch0490','dataset-bkshpg','bkshpg_jxzy_sjzy','main-inssisch0490',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_sjzy%main-inssisch0490'
);

-- 本科审核评估_教学资源_实践资源_全校生均核心设备占比-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_sjzy%main-inssisch0346','dataset-bkshpg','bkshpg_jxzy_sjzy','main-inssisch0346',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_sjzy%main-inssisch0346'
);

-- 本科审核评估_教学资源_实践资源_虚拟仿真实验教学中心-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_sjzy%main-inssisch0404','dataset-bkshpg','bkshpg_jxzy_sjzy','main-inssisch0404',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_sjzy%main-inssisch0404'
);

-- 本科审核评估_教学资源_实践资源_实验教学示范中心-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_sjzy%main-inssisch0405','dataset-bkshpg','bkshpg_jxzy_sjzy','main-inssisch0405',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_sjzy%main-inssisch0405'
);

-- 本科审核评估_教学资源_实践资源_每年产业学院数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_sjzy%main-inssisch0526','dataset-bkshpg','bkshpg_jxzy_sjzy','main-inssisch0526',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_sjzy%main-inssisch0526'
);

-- 本科审核评估_教学资源_实践资源_每年各级别实习基地数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_sjzy%main-inssisch0527','dataset-bkshpg','bkshpg_jxzy_sjzy','main-inssisch0527',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_sjzy%main-inssisch0527'
);

-- 本科审核评估_教学资源_实践资源_学院签约基地数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_sjzy%main-inssicol0170','dataset-bkshpg','bkshpg_jxzy_sjzy','main-inssicol0170',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_sjzy%main-inssicol0170'
);

-- 本科审核评估_教学资源_实践资源_校外实习基地-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_sjzy%main-inssicol0175','dataset-bkshpg','bkshpg_jxzy_sjzy','main-inssicol0175',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_sjzy%main-inssicol0175'
);

-- 本科审核评估_教学资源_实践资源_每年实验课程所用实验场所数量-COU_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_sjzy%main-inssicou0001','dataset-bkshpg','bkshpg_jxzy_sjzy','main-inssicou0001',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_sjzy%main-inssicou0001'
);

-- 本科审核评估_教学资源_实践资源_每年实验设备种类数量-COU_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_sjzy%main-inssicou0002','dataset-bkshpg','bkshpg_jxzy_sjzy','main-inssicou0002',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_sjzy%main-inssicou0002'
);

-- 本科审核评估_教学资源_实践资源_每年实验设备数量-COU_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_sjzy%main-inssicou0003','dataset-bkshpg','bkshpg_jxzy_sjzy','main-inssicou0003',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_sjzy%main-inssicou0003'
);

-- 本科审核评估_教学资源_实践资源_每年学校实验设备数量-COU_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_sjzy%main-inssicou0004','dataset-bkshpg','bkshpg_jxzy_sjzy','main-inssicou0004',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_sjzy%main-inssicou0004'
);

-- 本科审核评估_教学资源_实践资源_课程生均核心设备占比-COU_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_sjzy%main-inssicou0005','dataset-bkshpg','bkshpg_jxzy_sjzy','main-inssicou0005',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_sjzy%main-inssicou0005'
);

-- 本科审核评估_教学资源_本科实验场所-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-insbkssycs','dataset-bkshpg','bkshpg_jxzy','main-insbkssycs',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-insbkssycs'
);

-- 本科审核评估_教学资源_本科实验设备情况-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-insbksysbqk','dataset-bkshpg','bkshpg_jxzy','main-insbksysbqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-insbksysbqk'
);

-- 本科审核评估_教学资源_本科在线课程情况-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-insbkzxkcqk','dataset-bkshpg','bkshpg_jxzy','main-insbkzxkcqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-insbkzxkcqk'
);

-- 本科审核评估_教学资源_固定资产-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-insgdzc','dataset-bkshpg','bkshpg_jxzy','main-insgdzc',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-insgdzc'
);

-- 本科审核评估_教学资源_高校创新创业教育实践基地（平台）-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-insgxcxcyjysjjd','dataset-bkshpg','bkshpg_jxzy','main-insgxcxcyjysjjd',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-insgxcxcyjysjjd'
);

-- 本科审核评估_教学资源_教师教学发展机构-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-insjsjxfzjg','dataset-bkshpg','bkshpg_jxzy','main-insjsjxfzjg',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-insjsjxfzjg'
);

-- 本科审核评估_教学资源_教学行政用房面积-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-insjxxzyfmj','dataset-bkshpg','bkshpg_jxzy','main-insjxxzyfmj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-insjxxzyfmj'
);

-- 本科审核评估_教学资源_教育经费概况-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-insjyjfgk','dataset-bkshpg','bkshpg_jxzy','main-insjyjfgk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-insjyjfgk'
);

-- 本科审核评估_教学资源_教育经费收支情况-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-insjyjfszqk','dataset-bkshpg','bkshpg_jxzy','main-insjyjfszqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-insjyjfszqk'
);

-- 本科审核评估_教学资源_科研基地-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-inskyjd','dataset-bkshpg','bkshpg_jxzy','main-inskyjd',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-inskyjd'
);

-- 本科审核评估_教学资源_实验教学示范中心、虚拟仿真实验示范中心-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-inssyjxxnfzsysfzx','dataset-bkshpg','bkshpg_jxzy','main-inssyjxxnfzsysfzx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-inssyjxxnfzsysfzx'
);

-- 本科审核评估_教学资源_图书馆-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-instsg','dataset-bkshpg','bkshpg_jxzy','main-instsg',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-instsg'
);

-- 本科审核评估_教学资源_图书新增情况-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-instsxzqk','dataset-bkshpg','bkshpg_jxzy','main-instsxzqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-instsxzqk'
);

-- 本科审核评估_教学资源_虚拟仿真实验教学项目-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-insxnfzsyjxxm','dataset-bkshpg','bkshpg_jxzy','main-insxnfzsyjxxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-insxnfzsyjxxm'
);

-- 本科审核评估_教学资源_校内外实习、实践、实训基地-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-insxnwsxsjsxjd','dataset-bkshpg','bkshpg_jxzy','main-insxnwsxsjsxjd',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-insxnwsxsjsxjd'
);

-- 本科审核评估_教学资源_学校概况-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-insxxgk','dataset-bkshpg','bkshpg_jxzy','main-insxxgk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-insxxgk'
);

-- 本科审核评估_教学资源_学校教学科研单位-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-insxxjxkydw','dataset-bkshpg','bkshpg_jxzy','main-insxxjxkydw',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-insxxjxkydw'
);

-- 本科审核评估_教学资源_学校相关党政单位-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-insxxxgdzdw','dataset-bkshpg','bkshpg_jxzy','main-insxxxgdzdw',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-insxxxgdzdw'
);

-- 本科审核评估_教学资源_优势（一流）专业情况-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-insyszyqk','dataset-bkshpg','bkshpg_jxzy','main-insyszyqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-insyszyqk'
);

-- 本科审核评估_教学资源_占地与建筑面积-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-inszdyjzmj','dataset-bkshpg','bkshpg_jxzy','main-inszdyjzmj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-inszdyjzmj'
);

-- 本科审核评估_教学资源_专业大类情况表-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-inszydlqk','dataset-bkshpg','bkshpg_jxzy','main-inszydlqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-inszydlqk'
);

-- 本科审核评估_教学资源_产学合作协同育人立项项目-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-abdschcxhzxtyr','dataset-bkshpg','bkshpg_jxzy','main-abdschcxhzxtyr',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-abdschcxhzxtyr'
);

-- 本科审核评估_教学资源_思政示范课程-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-abdschszsfkc','dataset-bkshpg','bkshpg_jxzy','main-abdschszsfkc',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-abdschszsfkc'
);

-- 本科审核评估_教学资源_学校基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-abdschxxjbxx','dataset-bkshpg','bkshpg_jxzy','main-abdschxxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-abdschxxjbxx'
);

-- 本科审核评估_教学资源_教室基本信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-abdschjsjbxx','dataset-bkshpg','bkshpg_jxzy','main-abdschjsjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-abdschjsjbxx'
);

-- 本科审核评估_教学资源_课程规模情况-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-abdschkcgmqk','dataset-bkshpg','bkshpg_jxzy','main-abdschkcgmqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-abdschkcgmqk'
);

-- 本科审核评估_教学资源_特色专业情况-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-abdschtszyqk','dataset-bkshpg','bkshpg_jxzy','main-abdschtszyqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-abdschtszyqk'
);

-- 本科审核评估_教学资源_组织机构信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-abdschzzjgxx','dataset-bkshpg','bkshpg_jxzy','main-abdschzzjgxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-abdschzzjgxx'
);

-- 本科审核评估_教学资源_课程思政情况-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-abdschkcszqk','dataset-bkshpg','bkshpg_jxzy','main-abdschkcszqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-abdschkcszqk'
);

-- 本科审核评估_教学资源_课程类教研奖励-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-abdschkcljyjl','dataset-bkshpg','bkshpg_jxzy','main-abdschkcljyjl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-abdschkcljyjl'
);

-- 本科审核评估_教学资源_教学设施_每年学校教学行政用房面积-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_jxss%main-inssisch0382','dataset-bkshpg','bkshpg_jxzy_jxss','main-inssisch0382',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_jxss%main-inssisch0382'
);

-- 本科审核评估_教学资源_教学设施_每年教室数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_jxss%main-inssisch0383','dataset-bkshpg','bkshpg_jxzy_jxss','main-inssisch0383',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_jxss%main-inssisch0383'
);

-- 本科审核评估_教学资源_教学设施_每年学校实验场所教学科研仪器数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_jxss%main-inssisch0384','dataset-bkshpg','bkshpg_jxzy_jxss','main-inssisch0384',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_jxss%main-inssisch0384'
);

-- 本科审核评估_教学资源_教学设施_每年学校生均教学科研仪器设备值-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_jxss%main-inssisch0385','dataset-bkshpg','bkshpg_jxzy_jxss','main-inssisch0385',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_jxss%main-inssisch0385'
);

-- 本科审核评估_教学资源_教学设施_每年学校年新增教学科研仪器投入比-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_jxss%main-inssisch0386','dataset-bkshpg','bkshpg_jxzy_jxss','main-inssisch0386',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_jxss%main-inssisch0386'
);

-- 本科审核评估_教学资源_教学设施_每年学校教学、科研仪器设备资产及新增-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_jxss%main-inssisch0387','dataset-bkshpg','bkshpg_jxzy_jxss','main-inssisch0387',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_jxss%main-inssisch0387'
);

-- 本科审核评估_教学资源_教学设施_每年学校实验室及实习实训场所设备数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_jxss%main-inssisch0388','dataset-bkshpg','bkshpg_jxzy_jxss','main-inssisch0388',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_jxss%main-inssisch0388'
);

-- 本科审核评估_教学资源_教学设施_每年学校实验室及实习实训场所设备概况-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_jxss%main-inssisch0389','dataset-bkshpg','bkshpg_jxzy_jxss','main-inssisch0389',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_jxss%main-inssisch0389'
);

-- 本科审核评估_教学资源_教学设施_每年学校图书流通量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_jxss%main-inssisch0390','dataset-bkshpg','bkshpg_jxzy_jxss','main-inssisch0390',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_jxss%main-inssisch0390'
);

-- 本科审核评估_教学资源_教学设施_每年学校不同类型图书变化-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_jxss%main-inssisch0391','dataset-bkshpg','bkshpg_jxzy_jxss','main-inssisch0391',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_jxss%main-inssisch0391'
);

-- 本科审核评估_教学资源_课程资源_每年不同规模课程门次数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0392','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssisch0392',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0392'
);

-- 本科审核评估_教学资源_课程资源_每年开设课程规模-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0393','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssisch0393',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0393'
);

-- 本科审核评估_教学资源_课程资源_每年学校在线开放课程门数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0394','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssisch0394',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0394'
);

-- 本科审核评估_教学资源_课程资源_课程门数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0395','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssisch0395',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0395'
);

-- 本科审核评估_教学资源_课程资源_每学年学校开课平均学时-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0479','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssisch0479',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0479'
);

-- 本科审核评估_教学资源_课程资源_每学期全校开课平均学时-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0486','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssisch0486',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0486'
);

-- 本科审核评估_教学资源_课程资源_每学年全校开课平均学时-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0487','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssisch0487',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0487'
);

-- 本科审核评估_教学资源_课程资源_每学年全校开课平均班规模-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0488','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssisch0488',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0488'
);

-- 本科审核评估_教学资源_课程资源_每年优秀课程-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0396','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssisch0396',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0396'
);

-- 本科审核评估_教学资源_课程资源_每年思政课及课程思政-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0397','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssisch0397',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0397'
);

-- 本科审核评估_教学资源_课程资源_每年全英文课程数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0398','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssisch0398',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0398'
);

-- 本科审核评估_教学资源_课程资源_一流课程-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0399','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssisch0399',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0399'
);

-- 本科审核评估_教学资源_课程资源_思政示范课程-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0400','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssisch0400',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0400'
);

-- 本科审核评估_教学资源_课程资源_每年各类课程思政数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0525','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssisch0525',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0525'
);

-- 本科审核评估_教学资源_课程资源_名师获奖-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0401','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssisch0401',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0401'
);

-- 本科审核评估_教学资源_课程资源_每年优秀教材数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0402','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssisch0402',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0402'
);

-- 本科审核评估_教学资源_课程资源_教师获奖数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0403','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssisch0403',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0403'
);

-- 本科审核评估_教学资源_课程资源_每年全校课程平均成绩-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0544','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssisch0544',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssisch0544'
);

-- 本科审核评估_教学资源_课程资源_每学年课程开设门数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicol0247','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssicol0247',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicol0247'
);

-- 本科审核评估_教学资源_课程资源_每学期课程开设门数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicol0191','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssicol0191',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicol0191'
);

-- 本科审核评估_教学资源_课程资源_每学期课程开设门次数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicol0243','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssicol0243',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicol0243'
);

-- 本科审核评估_教学资源_课程资源_每学年课程开设门次数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicol0302','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssicol0302',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicol0302'
);

-- 本科审核评估_教学资源_课程资源_每学期课程开设学时数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicol0192','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssicol0192',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicol0192'
);

-- 本科审核评估_教学资源_课程资源_每年各学院开设课程规模-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicol0244','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssicol0244',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicol0244'
);

-- 本科审核评估_教学资源_课程资源_每年双语课程开设门数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicol0193','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssicol0193',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicol0193'
);

-- 本科审核评估_教学资源_课程资源_每学期开课平均学时-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicol0194','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssicol0194',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicol0194'
);

-- 本科审核评估_教学资源_课程资源_每学期教学班数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicol0195','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssicol0195',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicol0195'
);

-- 本科审核评估_教学资源_课程资源_每年各学院一流课程数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicol0264','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssicol0264',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicol0264'
);

-- 本科审核评估_教学资源_课程资源_每年各学院出版教材数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicol0265','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssicol0265',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicol0265'
);

-- 本科审核评估_教学资源_课程资源_每年每门课程开设门次数-COU_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicou0007','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssicou0007',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicou0007'
);

-- 本科审核评估_教学资源_课程资源_每年每门课程开设专业数-COU_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicou0008','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssicou0008',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicou0008'
);

-- 本科审核评估_教学资源_课程资源_每年每门课程开设教学班数-COU_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicou0009','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssicou0009',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicou0009'
);

-- 本科审核评估_教学资源_课程资源_每年每门课程上课人数-COU_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicou0010','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssicou0010',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicou0010'
);

-- 本科审核评估_教学资源_课程资源_每年每门课程平均成绩-COU_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicou0011','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssicou0011',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicou0011'
);

-- 本科审核评估_教学资源_课程资源_每年每门课程评教分数-COU_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicou0012','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssicou0012',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicou0012'
);

-- 本科审核评估_教学资源_课程资源_每年每门课程资源建设数量-COU_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicou0013','dataset-bkshpg','bkshpg_jxzy_kczy','main-inssicou0013',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_kczy%main-inssicou0013'
);

-- 本科审核评估_教学资源_经费投入_每年学校生均网络思政工作专项经费-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_jftr%main-inssisch0406','dataset-bkshpg','bkshpg_jxzy_jftr','main-inssisch0406',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_jftr%main-inssisch0406'
);

-- 本科审核评估_教学资源_经费投入_每年学校生均思政工作和党务工作队伍建设经费-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_jftr%main-inssisch0407','dataset-bkshpg','bkshpg_jxzy_jftr','main-inssisch0407',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_jftr%main-inssisch0407'
);

-- 本科审核评估_教学资源_经费投入_每年学校生均教学日常运行支出-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_jftr%main-inssisch0408','dataset-bkshpg','bkshpg_jxzy_jftr','main-inssisch0408',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_jftr%main-inssisch0408'
);

-- 本科审核评估_教学资源_经费投入_教学日常运行支出占经常性预算内教育事业费拨款与学费收入之和的比例-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_jftr%main-inssisch0409','dataset-bkshpg','bkshpg_jxzy_jftr','main-inssisch0409',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_jftr%main-inssisch0409'
);

-- 本科审核评估_教学资源_经费投入_学校年度教学改革与建设专项经费-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy_jftr%main-inssisch0410','dataset-bkshpg','bkshpg_jxzy_jftr','main-inssisch0410',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy_jftr%main-inssisch0410'
);

-- 本科审核评估_教学资源_学科建设-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-insxkjs','dataset-bkshpg','bkshpg_jxzy','main-insxkjs',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-insxkjs'
);

-- 本科审核评估_教学资源_一流学科-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-insylxk','dataset-bkshpg','bkshpg_jxzy','main-insylxk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-insylxk'
);

-- 本科审核评估_教学资源_学院基本信息-COL_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-abdcolxyjbxx','dataset-bkshpg','bkshpg_jxzy','main-abdcolxyjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-abdcolxyjbxx'
);

-- 本科审核评估_教学资源_产业学院-COL_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-abdcolcyxy','dataset-bkshpg','bkshpg_jxzy','main-abdcolcyxy',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-abdcolcyxy'
);

-- 本科审核评估_教学资源_课程基本信息-COU_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-abdcoukcjbxx','dataset-bkshpg','bkshpg_jxzy','main-abdcoukcjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-abdcoukcjbxx'
);

-- 本科审核评估_教学资源_本科课程基本信息-COU_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-abdcoubkkcjbxx','dataset-bkshpg','bkshpg_jxzy','main-abdcoubkkcjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-abdcoubkkcjbxx'
);

-- 本科审核评估_教学资源_课程评教情况-COU_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxzy%main-abdcoukcpjqk','dataset-bkshpg','bkshpg_jxzy','main-abdcoukcpjqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxzy%main-abdcoukcpjqk'
);

-- 本科审核评估_培养过程_方案表-MAJ_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc%main-abdmajfab','dataset-bkshpg','bkshpg_pygc','main-abdmajfab',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc%main-abdmajfab'
);

-- 本科审核评估_培养过程_教学质量评估统计表-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc%main-insjxzlpgtjb','dataset-bkshpg','bkshpg_pygc','main-insjxzlpgtjb',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc%main-insjxzlpgtjb'
);

-- 本科审核评估_培养过程_本科生学习成效-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc%main-insbksxxcx','dataset-bkshpg','bkshpg_pygc','main-insbksxxcx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc%main-insbksxxcx'
);

-- 本科审核评估_培养过程_创新创业教育情况-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc%main-inscxcyjyqk','dataset-bkshpg','bkshpg_pygc','main-inscxcyjyqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc%main-inscxcyjyqk'
);

-- 本科审核评估_培养过程_分专业（大类）专业实验课情况-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc%main-insfzyzysykqk','dataset-bkshpg','bkshpg_pygc','main-insfzyzysykqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc%main-insfzyzysykqk'
);

-- 本科审核评估_培养过程_教育教学研究与改革项目-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc%main-insjyjxyjyggxm','dataset-bkshpg','bkshpg_pygc','main-insjyjxyjyggxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc%main-insjyjxyjyggxm'
);

-- 本科审核评估_培养过程_开课情况-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc%main-inskkqk','dataset-bkshpg','bkshpg_pygc','main-inskkqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc%main-inskkqk'
);

-- 本科审核评估_培养过程_省级及以上本科教学项目建设情况-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc%main-inssjjysbkjxxmjsqk','dataset-bkshpg','bkshpg_pygc','main-inssjjysbkjxxmjsqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc%main-inssjjysbkjxxmjsqk'
);

-- 本科审核评估_培养过程_学生体质健康达标率-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc%main-insxstzjkdbl','dataset-bkshpg','bkshpg_pygc','main-insxstzjkdbl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc%main-insxstzjkdbl'
);

-- 本科审核评估_培养过程_专业课教学实施情况-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc%main-inszykjxssqk','dataset-bkshpg','bkshpg_pygc','main-inszykjxssqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc%main-inszykjxssqk'
);

-- 本科审核评估_培养过程_专业培养计划表-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc%main-inszypyjhb','dataset-bkshpg','bkshpg_pygc','main-inszypyjhb',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc%main-inszypyjhb'
);

-- 本科审核评估_培养过程_创新性实验项目-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc%main-abdschcxxsyxm','dataset-bkshpg','bkshpg_pygc','main-abdschcxxsyxm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc%main-abdschcxxsyxm'
);

-- 本科审核评估_培养过程_教学改革_每年学校卓越计划项目数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_jxgg%main-inssisch0435','dataset-bkshpg','bkshpg_pygc_jxgg','main-inssisch0435',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_jxgg%main-inssisch0435'
);

-- 本科审核评估_培养过程_教学改革_每年学校各类型教改项目立项项目-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_jxgg%main-inssicol0202','dataset-bkshpg','bkshpg_pygc_jxgg','main-inssicol0202',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_jxgg%main-inssicol0202'
);

-- 本科审核评估_培养过程_教学改革_每年学校各级别教学改革项目-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_jxgg%main-inssicol0203','dataset-bkshpg','bkshpg_pygc_jxgg','main-inssicol0203',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_jxgg%main-inssicol0203'
);

-- 本科审核评估_培养过程_双创教育_每年学校双创竞赛获奖数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0494','dataset-bkshpg','bkshpg_pygc_scjy','main-inssisch0494',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0494'
);

-- 本科审核评估_培养过程_双创教育_每年双创教育实践基地数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0436','dataset-bkshpg','bkshpg_pygc_scjy','main-inssisch0436',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0436'
);

-- 本科审核评估_培养过程_双创教育_每年学校双创参与学生比例-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0437','dataset-bkshpg','bkshpg_pygc_scjy','main-inssisch0437',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0437'
);

-- 本科审核评估_培养过程_双创教育_学科竞赛立项数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0438','dataset-bkshpg','bkshpg_pygc_scjy','main-inssisch0438',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0438'
);

-- 本科审核评估_培养过程_双创教育_省级及以上重点竞赛获奖数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0439','dataset-bkshpg','bkshpg_pygc_scjy','main-inssisch0439',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0439'
);

-- 本科审核评估_培养过程_双创教育_双创投入-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0440','dataset-bkshpg','bkshpg_pygc_scjy','main-inssisch0440',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0440'
);

-- 本科审核评估_培养过程_双创教育_每年学校大创项目数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0495','dataset-bkshpg','bkshpg_pygc_scjy','main-inssisch0495',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0495'
);

-- 本科审核评估_培养过程_双创教育_每年大创项目学生参与比例-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0482','dataset-bkshpg','bkshpg_pygc_scjy','main-inssisch0482',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0482'
);

-- 本科审核评估_培养过程_双创教育_创新创业成果分布-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0441','dataset-bkshpg','bkshpg_pygc_scjy','main-inssisch0441',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0441'
);

-- 本科审核评估_培养过程_双创教育_学科竞赛排行榜（单年排名）-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0442','dataset-bkshpg','bkshpg_pygc_scjy','main-inssisch0442',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0442'
);

-- 本科审核评估_培养过程_双创教育_学科竞赛排行榜（五年排名）-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0443','dataset-bkshpg','bkshpg_pygc_scjy','main-inssisch0443',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0443'
);

-- 本科审核评估_培养过程_双创教育_每年学生参与“互联网+”大学生创新创业大赛各赛道项目数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0444','dataset-bkshpg','bkshpg_pygc_scjy','main-inssisch0444',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0444'
);

-- 本科审核评估_培养过程_双创教育_每年学生参与“互联网+”大学生创新创业大赛获奖数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0445','dataset-bkshpg','bkshpg_pygc_scjy','main-inssisch0445',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0445'
);

-- 本科审核评估_培养过程_双创教育_学科竞赛立项项目数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0446','dataset-bkshpg','bkshpg_pygc_scjy','main-inssisch0446',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0446'
);

-- 本科审核评估_培养过程_双创教育_品牌竞赛创新团队获奖数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0447','dataset-bkshpg','bkshpg_pygc_scjy','main-inssisch0447',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0447'
);

-- 本科审核评估_培养过程_双创教育_每年学校本科生发表学术论文-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0496','dataset-bkshpg','bkshpg_pygc_scjy','main-inssisch0496',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0496'
);

-- 本科审核评估_培养过程_双创教育_每年学校本科生获准专利著作权数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0497','dataset-bkshpg','bkshpg_pygc_scjy','main-inssisch0497',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0497'
);

-- 本科审核评估_培养过程_双创教育_创新创业类课程-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0448','dataset-bkshpg','bkshpg_pygc_scjy','main-inssisch0448',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssisch0448'
);

-- 本科审核评估_培养过程_双创教育_每年学校各学院双创竞赛获奖数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssicol0204','dataset-bkshpg','bkshpg_pygc_scjy','main-inssicol0204',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssicol0204'
);

-- 本科审核评估_培养过程_双创教育_每年学院大创项目数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssicol0205','dataset-bkshpg','bkshpg_pygc_scjy','main-inssicol0205',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssicol0205'
);

-- 本科审核评估_培养过程_双创教育_每年大创项目参与人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssicol0260','dataset-bkshpg','bkshpg_pygc_scjy','main-inssicol0260',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssicol0260'
);

-- 本科审核评估_培养过程_双创教育_每年学院各大创项目级别参与人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssicol0261','dataset-bkshpg','bkshpg_pygc_scjy','main-inssicol0261',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssicol0261'
);

-- 本科审核评估_培养过程_双创教育_每年学院各大创项目类型参与人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssicol0262','dataset-bkshpg','bkshpg_pygc_scjy','main-inssicol0262',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssicol0262'
);

-- 本科审核评估_培养过程_双创教育_每年学院双创项目参与人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssicol0263','dataset-bkshpg','bkshpg_pygc_scjy','main-inssicol0263',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssicol0263'
);

-- 本科审核评估_培养过程_双创教育_学科竞赛获奖人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssicol0208','dataset-bkshpg','bkshpg_pygc_scjy','main-inssicol0208',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssicol0208'
);

-- 本科审核评估_培养过程_双创教育_每年本科生发表学术论文-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssicol0209','dataset-bkshpg','bkshpg_pygc_scjy','main-inssicol0209',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssicol0209'
);

-- 本科审核评估_培养过程_双创教育_每年本科生获准专利著作权数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssicol0210','dataset-bkshpg','bkshpg_pygc_scjy','main-inssicol0210',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssicol0210'
);

-- 本科审核评估_培养过程_双创教育_每年每位学生学术成果数量-UND_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssiund0015','dataset-bkshpg','bkshpg_pygc_scjy','main-inssiund0015',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_scjy%main-inssiund0015'
);

-- 本科审核评估_培养过程_实践实习_创新性实验项目数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_sjsx%main-inssisch0347','dataset-bkshpg','bkshpg_pygc_sjsx','main-inssisch0347',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_sjsx%main-inssisch0347'
);

-- 本科审核评估_培养过程_实践实习_实验课上课人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_sjsx%main-inssisch0348','dataset-bkshpg','bkshpg_pygc_sjsx','main-inssisch0348',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_sjsx%main-inssisch0348'
);

-- 本科审核评估_培养过程_实践实习_虚拟仿真实验项目-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_sjsx%main-inssisch0349','dataset-bkshpg','bkshpg_pygc_sjsx','main-inssisch0349',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_sjsx%main-inssisch0349'
);

-- 本科审核评估_培养过程_实践实习_虚拟仿真项目浏览数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_sjsx%main-inssisch0350','dataset-bkshpg','bkshpg_pygc_sjsx','main-inssisch0350',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_sjsx%main-inssisch0350'
);

-- 本科审核评估_培养过程_实践实习_虚拟实验项目参与人数对比-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_sjsx%main-inssisch0351','dataset-bkshpg','bkshpg_pygc_sjsx','main-inssisch0351',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_sjsx%main-inssisch0351'
);

-- 本科审核评估_培养过程_实践实习_独立实验课程-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_sjsx%main-inssicol0176','dataset-bkshpg','bkshpg_pygc_sjsx','main-inssicol0176',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_sjsx%main-inssicol0176'
);

-- 本科审核评估_培养过程_实践实习_产学合作协同育人立项-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_sjsx%main-inssicol0177','dataset-bkshpg','bkshpg_pygc_sjsx','main-inssicol0177',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_sjsx%main-inssicol0177'
);

-- 本科审核评估_培养过程_实践实习_每年学校各学院实习开展次数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_sjsx%main-inssicol0178','dataset-bkshpg','bkshpg_pygc_sjsx','main-inssicol0178',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_sjsx%main-inssicol0178'
);

-- 本科审核评估_培养过程_实践实习_每年每位学生实践实习次数-UND_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_sjsx%main-inssiund0016','dataset-bkshpg','bkshpg_pygc_sjsx','main-inssiund0016',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_sjsx%main-inssiund0016'
);

-- 本科审核评估_培养过程_毕业实践_每年毕业综合训练指导教师数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_bysj%main-inssisch0449','dataset-bkshpg','bkshpg_pygc_bysj','main-inssisch0449',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_bysj%main-inssisch0449'
);

-- 本科审核评估_培养过程_毕业实践_每年学校毕业综合训练指导教师数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_bysj%main-inssisch0493','dataset-bkshpg','bkshpg_pygc_bysj','main-inssisch0493',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_bysj%main-inssisch0493'
);

-- 本科审核评估_培养过程_毕业实践_每年毕业论文通过率-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_bysj%main-inssisch0450','dataset-bkshpg','bkshpg_pygc_bysj','main-inssisch0450',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_bysj%main-inssisch0450'
);

-- 本科审核评估_培养过程_毕业实践_每年以实验、实习、工程实践和社会调查等为基础的论文占比-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_bysj%main-inssisch0451','dataset-bkshpg','bkshpg_pygc_bysj','main-inssisch0451',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_bysj%main-inssisch0451'
);

-- 本科审核评估_培养过程_毕业实践_每年学校毕业综合训练课题完成率-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_bysj%main-inssisch0452','dataset-bkshpg','bkshpg_pygc_bysj','main-inssisch0452',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_bysj%main-inssisch0452'
);

-- 本科审核评估_培养过程_毕业实践_每年学校指导毕业生数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_bysj%main-inssisch0453','dataset-bkshpg','bkshpg_pygc_bysj','main-inssisch0453',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_bysj%main-inssisch0453'
);

-- 本科审核评估_培养过程_毕业实践_每年学校平均指导毕业生数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_bysj%main-inssisch0454','dataset-bkshpg','bkshpg_pygc_bysj','main-inssisch0454',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_bysj%main-inssisch0454'
);

-- 本科审核评估_培养过程_毕业实践_每年各学院毕业综合训练指导教师数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_bysj%main-inssicol0249','dataset-bkshpg','bkshpg_pygc_bysj','main-inssicol0249',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_bysj%main-inssicol0249'
);

-- 本科审核评估_培养过程_毕业实践_每年毕业综合训练课题数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_bysj%main-inssicol0211','dataset-bkshpg','bkshpg_pygc_bysj','main-inssicol0211',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_bysj%main-inssicol0211'
);

-- 本科审核评估_培养过程_毕业实践_每年各学院以实验、实习、工程实践和社会调查等为基础的论文占比-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_bysj%main-inssicol0212','dataset-bkshpg','bkshpg_pygc_bysj','main-inssicol0212',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_bysj%main-inssicol0212'
);

-- 本科审核评估_培养过程_毕业实践_每年学校各学院毕业综合训练课题完成率-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_bysj%main-inssicol0213','dataset-bkshpg','bkshpg_pygc_bysj','main-inssicol0213',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_bysj%main-inssicol0213'
);

-- 本科审核评估_培养过程_毕业实践_每年学校各学院及专业平均指导毕业生数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_bysj%main-inssicol0214','dataset-bkshpg','bkshpg_pygc_bysj','main-inssicol0214',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_bysj%main-inssicol0214'
);

-- 本科审核评估_培养过程_毕业实践_学院不同毕业论文等级学生数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_bysj%main-inssicol0245','dataset-bkshpg','bkshpg_pygc_bysj','main-inssicol0245',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_bysj%main-inssicol0245'
);

-- 本科审核评估_培养过程_毕业实践_每年毕业综合训练指导学生数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_bysj%main-inssicol0246','dataset-bkshpg','bkshpg_pygc_bysj','main-inssicol0246',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_bysj%main-inssicol0246'
);

-- 本科审核评估_培养过程_毕业实践_每年不同课题性质的综合训练数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc_bysj%main-inssicol0306','dataset-bkshpg','bkshpg_pygc_bysj','main-inssicol0306',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc_bysj%main-inssicol0306'
);

-- 本科审核评估_培养过程_学科竞赛学院排名-COL_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc%main-insxkjsxxpm','dataset-bkshpg','bkshpg_pygc','main-insxkjsxxpm',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc%main-insxkjsxxpm'
);

-- 本科审核评估_培养过程_教学任务-TCL_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc%main-abdtcljxrw','dataset-bkshpg','bkshpg_pygc','main-abdtcljxrw',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc%main-abdtcljxrw'
);

-- 本科审核评估_培养过程_教学任务相关信息-TCL_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc%main-abdtcljxrwxgxx','dataset-bkshpg','bkshpg_pygc','main-abdtcljxrwxgxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc%main-abdtcljxrwxgxx'
);

-- 本科审核评估_培养过程_教材信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc%main-abdteajcxx','dataset-bkshpg','bkshpg_pygc','main-abdteajcxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc%main-abdteajcxx'
);

-- 本科审核评估_培养过程_教师课程表-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc%main-abdteajskcb','dataset-bkshpg','bkshpg_pygc','main-abdteajskcb',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc%main-abdteajskcb'
);

-- 本科审核评估_培养过程_本科生辅修、双学位情况-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc%main-insbksfxsxwqk','dataset-bkshpg','bkshpg_pygc','main-insbksfxsxwqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc%main-insbksfxsxwqk'
);

-- 本科审核评估_培养过程_本科生转专业情况-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc%main-insbkszzyqk','dataset-bkshpg','bkshpg_pygc','main-insbkszzyqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc%main-insbkszzyqk'
);

-- 本科审核评估_培养过程_学生毕业综合训练情况-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc%main-insxsbyzhxlqk','dataset-bkshpg','bkshpg_pygc','main-insxsbyzhxlqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc%main-insxsbyzhxlqk'
);

-- 本科审核评估_培养过程_毕业生毕业信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc%main-abdundbysbyxx','dataset-bkshpg','bkshpg_pygc','main-abdundbysbyxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc%main-abdundbysbyxx'
);

-- 本科审核评估_培养过程_英语四六级通过情况-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc%main-abdundyysljtgqk','dataset-bkshpg','bkshpg_pygc','main-abdundyysljtgqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc%main-abdundyysljtgqk'
);

-- 本科审核评估_培养过程_学业预警结果-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc%main-abdundxyyjjg','dataset-bkshpg','bkshpg_pygc','main-abdundxyyjjg',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc%main-abdundxyyjjg'
);

-- 本科审核评估_培养过程_实习基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc%main-abdundsxjbxx','dataset-bkshpg','bkshpg_pygc','main-abdundsxjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc%main-abdundsxjbxx'
);

-- 本科审核评估_培养过程_学生成绩信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc%main-abdundxscjxx','dataset-bkshpg','bkshpg_pygc','main-abdundxscjxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc%main-abdundxscjxx'
);

-- 本科审核评估_培养过程_学生课程表-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc%main-abdundxskcb','dataset-bkshpg','bkshpg_pygc','main-abdundxskcb',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc%main-abdundxskcb'
);

-- 本科审核评估_培养过程_方案课程表-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_pygc%main-abdundfakcb','dataset-bkshpg','bkshpg_pygc','main-abdundfakcb',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_pygc%main-abdundfakcb'
);

-- 本科审核评估_师资队伍_数量结构_每年教职工人数-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssimaj0019','dataset-bkshpg','bkshpg_szdw_sljg','main-inssimaj0019',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssimaj0019'
);

-- 本科审核评估_师资队伍_数量结构_每学年各学院及专业教授人数-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssimaj0034','dataset-bkshpg','bkshpg_szdw_sljg','main-inssimaj0034',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssimaj0034'
);

-- 本科审核评估_师资队伍_数量结构_每年全体教职工人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssisch0324','dataset-bkshpg','bkshpg_szdw_sljg','main-inssisch0324',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssisch0324'
);

-- 本科审核评估_师资队伍_数量结构_每年专任教师人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssisch0325','dataset-bkshpg','bkshpg_szdw_sljg','main-inssisch0325',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssisch0325'
);

-- 本科审核评估_师资队伍_数量结构_每年专任教师比例-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssisch0326','dataset-bkshpg','bkshpg_szdw_sljg','main-inssisch0326',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssisch0326'
);

-- 本科审核评估_师资队伍_数量结构_每年授课教师人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssisch0327','dataset-bkshpg','bkshpg_szdw_sljg','main-inssisch0327',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssisch0327'
);

-- 本科审核评估_师资队伍_数量结构_每年实验技术人员数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssisch0352','dataset-bkshpg','bkshpg_szdw_sljg','main-inssisch0352',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssisch0352'
);

-- 本科审核评估_师资队伍_数量结构_每年正高级职称专任教师比例-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssisch0502','dataset-bkshpg','bkshpg_szdw_sljg','main-inssisch0502',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssisch0502'
);

-- 本科审核评估_师资队伍_数量结构_每年高级职称专任教师比例-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssisch0328','dataset-bkshpg','bkshpg_szdw_sljg','main-inssisch0328',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssisch0328'
);

-- 本科审核评估_师资队伍_数量结构_每年专任教师中高层次人才比例-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssisch0329','dataset-bkshpg','bkshpg_szdw_sljg','main-inssisch0329',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssisch0329'
);

-- 本科审核评估_师资队伍_数量结构_每年学校不同类型管理人员数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssisch0528','dataset-bkshpg','bkshpg_szdw_sljg','main-inssisch0528',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssisch0528'
);

-- 本科审核评估_师资队伍_数量结构_每年思政课专任教师数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssisch0217','dataset-bkshpg','bkshpg_szdw_sljg','main-inssisch0217',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssisch0217'
);

-- 本科审核评估_师资队伍_数量结构_每年专任教师与实验技术队伍比-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssisch0330','dataset-bkshpg','bkshpg_szdw_sljg','main-inssisch0330',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssisch0330'
);

-- 本科审核评估_师资队伍_数量结构_每年实验技术队伍最高学位博、硕、本之比-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssisch0331','dataset-bkshpg','bkshpg_szdw_sljg','main-inssisch0331',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssisch0331'
);

-- 本科审核评估_师资队伍_数量结构_每年外聘教师人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0136','dataset-bkshpg','bkshpg_szdw_sljg','main-inssicol0136',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0136'
);

-- 本科审核评估_师资队伍_数量结构_每年各职称教职工人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0137','dataset-bkshpg','bkshpg_szdw_sljg','main-inssicol0137',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0137'
);

-- 本科审核评估_师资队伍_数量结构_每年正高级职称教职工比例-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0257','dataset-bkshpg','bkshpg_szdw_sljg','main-inssicol0257',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0257'
);

-- 本科审核评估_师资队伍_数量结构_每年各年龄教职工人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0138','dataset-bkshpg','bkshpg_szdw_sljg','main-inssicol0138',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0138'
);

-- 本科审核评估_师资队伍_数量结构_每年各年龄教职工比例-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0258','dataset-bkshpg','bkshpg_szdw_sljg','main-inssicol0258',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0258'
);

-- 本科审核评估_师资队伍_数量结构_每年各最高学位教职工人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0139','dataset-bkshpg','bkshpg_szdw_sljg','main-inssicol0139',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0139'
);

-- 本科审核评估_师资队伍_数量结构_每年各学缘教职工人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0140','dataset-bkshpg','bkshpg_szdw_sljg','main-inssicol0140',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0140'
);

-- 本科审核评估_师资队伍_数量结构_每年各职称专任教师人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0141','dataset-bkshpg','bkshpg_szdw_sljg','main-inssicol0141',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0141'
);

-- 本科审核评估_师资队伍_数量结构_每年各年龄专任教师人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0142','dataset-bkshpg','bkshpg_szdw_sljg','main-inssicol0142',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0142'
);

-- 本科审核评估_师资队伍_数量结构_每年各年龄专任教师比例-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0259','dataset-bkshpg','bkshpg_szdw_sljg','main-inssicol0259',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0259'
);

-- 本科审核评估_师资队伍_数量结构_每年各最高学位专任教师人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0143','dataset-bkshpg','bkshpg_szdw_sljg','main-inssicol0143',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0143'
);

-- 本科审核评估_师资队伍_数量结构_每年各学缘专任教师人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0144','dataset-bkshpg','bkshpg_szdw_sljg','main-inssicol0144',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0144'
);

-- 本科审核评估_师资队伍_数量结构_每年专任教师中高层次人才数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0145','dataset-bkshpg','bkshpg_szdw_sljg','main-inssicol0145',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0145'
);

-- 本科审核评估_师资队伍_数量结构_每年各专任教师类型人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0146','dataset-bkshpg','bkshpg_szdw_sljg','main-inssicol0146',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0146'
);

-- 本科审核评估_师资队伍_数量结构_每年各专任教师类型比例-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0147','dataset-bkshpg','bkshpg_szdw_sljg','main-inssicol0147',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0147'
);

-- 本科审核评估_师资队伍_数量结构_每年临近退休专任教师人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0148','dataset-bkshpg','bkshpg_szdw_sljg','main-inssicol0148',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0148'
);

-- 本科审核评估_师资队伍_数量结构_每年管理人员数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0149','dataset-bkshpg','bkshpg_szdw_sljg','main-inssicol0149',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0149'
);

-- 本科审核评估_师资队伍_数量结构_每年各职称实验技术人员数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0150','dataset-bkshpg','bkshpg_szdw_sljg','main-inssicol0150',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0150'
);

-- 本科审核评估_师资队伍_数量结构_每年高级职称实验技术人员比例-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0253','dataset-bkshpg','bkshpg_szdw_sljg','main-inssicol0253',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0253'
);

-- 本科审核评估_师资队伍_数量结构_每年各年龄实验技术人员数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0151','dataset-bkshpg','bkshpg_szdw_sljg','main-inssicol0151',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0151'
);

-- 本科审核评估_师资队伍_数量结构_每年各年龄实验技术人员比例-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0254','dataset-bkshpg','bkshpg_szdw_sljg','main-inssicol0254',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0254'
);

-- 本科审核评估_师资队伍_数量结构_每年各最高学位实验技术人员数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0152','dataset-bkshpg','bkshpg_szdw_sljg','main-inssicol0152',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0152'
);

-- 本科审核评估_师资队伍_数量结构_每年未上课教授人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0153','dataset-bkshpg','bkshpg_szdw_sljg','main-inssicol0153',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0153'
);

-- 本科审核评估_师资队伍_数量结构_每年未上课高层次人才数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0154','dataset-bkshpg','bkshpg_szdw_sljg','main-inssicol0154',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0154'
);

-- 本科审核评估_师资队伍_数量结构_每年临近退休授课教师人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0155','dataset-bkshpg','bkshpg_szdw_sljg','main-inssicol0155',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0155'
);

-- 本科审核评估_师资队伍_数量结构_每年专任教师最高学历毕业的前十名学校-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0156','dataset-bkshpg','bkshpg_szdw_sljg','main-inssicol0156',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_sljg%main-inssicol0156'
);

-- 本科审核评估_师资队伍_生师比_每年专业生师比-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_ssb%main-inssimaj0001','dataset-bkshpg','bkshpg_szdw_ssb','main-inssimaj0001',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_ssb%main-inssimaj0001'
);

-- 本科审核评估_师资队伍_生师比_每年专业生师比均值-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_ssb%main-inssimaj0020','dataset-bkshpg','bkshpg_szdw_ssb','main-inssimaj0020',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_ssb%main-inssimaj0020'
);

-- 本科审核评估_师资队伍_生师比_每年专业班导师与在校生比-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_ssb%main-inssimaj0021','dataset-bkshpg','bkshpg_szdw_ssb','main-inssimaj0021',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_ssb%main-inssimaj0021'
);

-- 本科审核评估_师资队伍_生师比_每年专业思政课专任教师与在校生比-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_ssb%main-inssimaj0015','dataset-bkshpg','bkshpg_szdw_ssb','main-inssimaj0015',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_ssb%main-inssimaj0015'
);

-- 本科审核评估_师资队伍_生师比_每年生师比-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_ssb%main-inssisch0374','dataset-bkshpg','bkshpg_szdw_ssb','main-inssisch0374',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_ssb%main-inssisch0374'
);

-- 本科审核评估_师资队伍_生师比_每年专职辅导员与在校生比-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_ssb%main-inssisch0375','dataset-bkshpg','bkshpg_szdw_ssb','main-inssisch0375',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_ssb%main-inssisch0375'
);

-- 本科审核评估_师资队伍_生师比_每年班导师与在校生比-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_ssb%main-inssisch0376','dataset-bkshpg','bkshpg_szdw_ssb','main-inssisch0376',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_ssb%main-inssisch0376'
);

-- 本科审核评估_师资队伍_生师比_每年思政课专任教师与在校生比-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_ssb%main-inssisch0377','dataset-bkshpg','bkshpg_szdw_ssb','main-inssisch0377',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_ssb%main-inssisch0377'
);

-- 本科审核评估_师资队伍_生师比_学校专职党务工作人员和思想政治工作人员总数与全校师生人数比-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_ssb%main-inssisch0378','dataset-bkshpg','bkshpg_szdw_ssb','main-inssisch0378',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_ssb%main-inssisch0378'
);

-- 本科审核评估_师资队伍_生师比_学校专职就业指导教师和专职就业工作人员与应届毕业生比-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_ssb%main-inssisch0379','dataset-bkshpg','bkshpg_szdw_ssb','main-inssisch0379',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_ssb%main-inssisch0379'
);

-- 本科审核评估_师资队伍_生师比_学校专职从事心理健康教育教师与在校生比-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_ssb%main-inssisch0380','dataset-bkshpg','bkshpg_szdw_ssb','main-inssisch0380',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_ssb%main-inssisch0380'
);

-- 本科审核评估_师资队伍_生师比_每年学院生师比-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_ssb%main-inssicol0188','dataset-bkshpg','bkshpg_szdw_ssb','main-inssicol0188',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_ssb%main-inssicol0188'
);

-- 本科审核评估_师资队伍_生师比_每年学院生师比均值-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_ssb%main-inssicol0279','dataset-bkshpg','bkshpg_szdw_ssb','main-inssicol0279',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_ssb%main-inssicol0279'
);

-- 本科审核评估_师资队伍_生师比_每年学院专职辅导员与在校生比-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_ssb%main-inssicol0189','dataset-bkshpg','bkshpg_szdw_ssb','main-inssicol0189',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_ssb%main-inssicol0189'
);

-- 本科审核评估_师资队伍_生师比_每年学院班导师与在校生比-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_ssb%main-inssicol0190','dataset-bkshpg','bkshpg_szdw_ssb','main-inssicol0190',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_ssb%main-inssicol0190'
);

-- 本科审核评估_师资队伍_生师比_每年学院思政课专任教师与在校生比-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_ssb%main-inssicol0271','dataset-bkshpg','bkshpg_szdw_ssb','main-inssicol0271',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_ssb%main-inssicol0271'
);

-- 本科审核评估_师资队伍_教学投入_每年各专业教授主讲本科课程人均学时数-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssimaj0016','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssimaj0016',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssimaj0016'
);

-- 本科审核评估_师资队伍_教学投入_每学年各学院及专业授课教授人数-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssimaj0035','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssimaj0035',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssimaj0035'
);

-- 本科审核评估_师资队伍_教学投入_每年教授主讲本科课程人均学时数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssisch0336','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssisch0336',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssisch0336'
);

-- 本科审核评估_师资队伍_教学投入_每学年主讲本科课程教师人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssisch0478','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssisch0478',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssisch0478'
);

-- 本科审核评估_师资队伍_教学投入_学校主讲本科课程教授占教授总数的比例-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssisch0337','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssisch0337',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssisch0337'
);

-- 本科审核评估_师资队伍_教学投入_每年高层次人才授课比例-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssisch0338','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssisch0338',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssisch0338'
);

-- 本科审核评估_师资队伍_教学投入_每年各学院教授主讲本科课程人均学时数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0160','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssicol0160',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0160'
);

-- 本科审核评估_师资队伍_教学投入_每年各学院教师授课总学时-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0280','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssicol0280',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0280'
);

-- 本科审核评估_师资队伍_教学投入_每年各学院不同课程分类的各类教师授课总学时-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0281','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssicol0281',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0281'
);

-- 本科审核评估_师资队伍_教学投入_每年各学院不同课程类别的各类教师授课总学时-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0282','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssicol0282',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0282'
);

-- 本科审核评估_师资队伍_教学投入_每年各学院不同课程分类的各职称教师人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0283','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssicol0283',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0283'
);

-- 本科审核评估_师资队伍_教学投入_每年各学院不同课程分类的各年龄段教师人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0284','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssicol0284',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0284'
);

-- 本科审核评估_师资队伍_教学投入_每年各学院不同课程类别的各职称教师人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0285','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssicol0285',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0285'
);

-- 本科审核评估_师资队伍_教学投入_每年各学院不同课程类别的各年龄段教师人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0286','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssicol0286',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0286'
);

-- 本科审核评估_师资队伍_教学投入_每年各学院不同课程分类教师人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0287','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssicol0287',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0287'
);

-- 本科审核评估_师资队伍_教学投入_每年各学院不同课程类别教师人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0288','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssicol0288',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0288'
);

-- 本科审核评估_师资队伍_教学投入_每年各学院不同课程分类的教师授课总学时-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0289','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssicol0289',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0289'
);

-- 本科审核评估_师资队伍_教学投入_每年各学院不同课程类别的教师授课总学时-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0290','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssicol0290',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0290'
);

-- 本科审核评估_师资队伍_教学投入_每学年各学院授课教师人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0278','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssicol0278',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0278'
);

-- 本科审核评估_师资队伍_教学投入_学校主讲本科课程教师人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0161','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssicol0161',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0161'
);

-- 本科审核评估_师资队伍_教学投入_学校学院及专业下主讲本科课程教授占教授总数的比例-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0162','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssicol0162',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0162'
);

-- 本科审核评估_师资队伍_教学投入_每年高层次人才授课人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0163','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssicol0163',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0163'
);

-- 本科审核评估_师资队伍_教学投入_每年各学院的高层次人才授课总学时-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0291','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssicol0291',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0291'
);

-- 本科审核评估_师资队伍_教学投入_每年各学院不同课程分类的高层次人才授课总学时-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0292','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssicol0292',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0292'
);

-- 本科审核评估_师资队伍_教学投入_每年各学院不同课程类别的高层次人才授课总学时-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0293','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssicol0293',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0293'
);

-- 本科审核评估_师资队伍_教学投入_每年各学院不同课程分类高层次人才数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0294','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssicol0294',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0294'
);

-- 本科审核评估_师资队伍_教学投入_每年各学院不同课程类别高层次人才数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0295','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssicol0295',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0295'
);

-- 本科审核评估_师资队伍_教学投入_每年各学院不同课程分类的各职称高层次人才授课总学时-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0296','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssicol0296',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0296'
);

-- 本科审核评估_师资队伍_教学投入_每年各学院不同课程类别的各职称高层次人才授课总学时-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0297','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssicol0297',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0297'
);

-- 本科审核评估_师资队伍_教学投入_每年各学院不同课程分类各职称高层次人才数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0298','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssicol0298',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0298'
);

-- 本科审核评估_师资队伍_教学投入_每年各学院不同课程类别各职称高层次人才数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0299','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssicol0299',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0299'
);

-- 本科审核评估_师资队伍_教学投入_每年各学院高层次人才授课比例-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0164','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssicol0164',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0164'
);

-- 本科审核评估_师资队伍_教学投入_每年教师授课门次-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0165','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssicol0165',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0165'
);

-- 本科审核评估_师资队伍_教学投入_每年教师授课门数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0166','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssicol0166',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0166'
);

-- 本科审核评估_师资队伍_教学投入_每年教师授课比例-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0167','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssicol0167',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0167'
);

-- 本科审核评估_师资队伍_教学投入_每年各职称教师授课门数及比例-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0315','dataset-bkshpg','bkshpg_szdw_jxtr','main-inssicol0315',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxtr%main-inssicol0315'
);

-- 本科审核评估_师资队伍_教师发展_每年各专业基层教学组织数量-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssimaj0025','dataset-bkshpg','bkshpg_szdw_jsfz','main-inssimaj0025',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssimaj0025'
);

-- 本科审核评估_师资队伍_教师发展_每年具有企业经验的专任教师数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssisch0339','dataset-bkshpg','bkshpg_szdw_jsfz','main-inssisch0339',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssisch0339'
);

-- 本科审核评估_师资队伍_教师发展_每年具有两年以上企业经验的专任教师数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssisch0340','dataset-bkshpg','bkshpg_szdw_jsfz','main-inssisch0340',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssisch0340'
);

-- 本科审核评估_师资队伍_教师发展_每年学校派去企业实践锻炼累计3个月及以上专任教师数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssisch0341','dataset-bkshpg','bkshpg_szdw_jsfz','main-inssisch0341',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssisch0341'
);

-- 本科审核评估_师资队伍_教师发展_每年具有企业经验的专任教师比例-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssisch0342','dataset-bkshpg','bkshpg_szdw_jsfz','main-inssisch0342',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssisch0342'
);

-- 本科审核评估_师资队伍_教师发展_每年开展活动数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssisch0343','dataset-bkshpg','bkshpg_szdw_jsfz','main-inssisch0343',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssisch0343'
);

-- 本科审核评估_师资队伍_教师发展_每年开展教学竞赛数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssisch0344','dataset-bkshpg','bkshpg_szdw_jsfz','main-inssisch0344',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssisch0344'
);

-- 本科审核评估_师资队伍_教师发展_每年参与教学竞赛教师人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssisch0353','dataset-bkshpg','bkshpg_szdw_jsfz','main-inssisch0353',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssisch0353'
);

-- 本科审核评估_师资队伍_教师发展_获得荣誉称号教师人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssisch0345','dataset-bkshpg','bkshpg_szdw_jsfz','main-inssisch0345',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssisch0345'
);

-- 本科审核评估_师资队伍_教师发展_获得不同类型荣誉称号教师人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssisch0381','dataset-bkshpg','bkshpg_szdw_jsfz','main-inssisch0381',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssisch0381'
);

-- 本科审核评估_师资队伍_教师发展_每年教学竞赛获奖人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssisch0545','dataset-bkshpg','bkshpg_szdw_jsfz','main-inssisch0545',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssisch0545'
);

-- 本科审核评估_师资队伍_教师发展_每年教学团队数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssicol0168','dataset-bkshpg','bkshpg_szdw_jsfz','main-inssicol0168',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssicol0168'
);

-- 本科审核评估_师资队伍_教师发展_每年各学院教学团队数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssicol0277','dataset-bkshpg','bkshpg_szdw_jsfz','main-inssicol0277',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssicol0277'
);

-- 本科审核评估_师资队伍_教师发展_每年新进教师培训人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssicol0169','dataset-bkshpg','bkshpg_szdw_jsfz','main-inssicol0169',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssicol0169'
);

-- 本科审核评估_师资队伍_教师发展_每年各学院参与教学竞赛教师人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssicol0310','dataset-bkshpg','bkshpg_szdw_jsfz','main-inssicol0310',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssicol0310'
);

-- 本科审核评估_师资队伍_教师发展_每年各学院获得荣誉称号教师人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssicol0313','dataset-bkshpg','bkshpg_szdw_jsfz','main-inssicol0313',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssicol0313'
);

-- 本科审核评估_师资队伍_教师发展_每年每位教师指导学生次数-TEA_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssitea0061','dataset-bkshpg','bkshpg_szdw_jsfz','main-inssitea0061',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssitea0061'
);

-- 本科审核评估_师资队伍_教师发展_每年每位教师的成果数量-TEA_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssitea0062','dataset-bkshpg','bkshpg_szdw_jsfz','main-inssitea0062',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jsfz%main-inssitea0062'
);

-- 本科审核评估_师资队伍_多教师授课情况-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw%main-insdjsskqk','dataset-bkshpg','bkshpg_szdw','main-insdjsskqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw%main-insdjsskqk'
);

-- 本科审核评估_师资队伍_相关教师情况-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw%main-insxgjsqk','dataset-bkshpg','bkshpg_szdw','main-insxgjsqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw%main-insxgjsqk'
);

-- 本科审核评估_师资队伍_教学能力_每年外籍专任教师数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxnl%main-inssisch0332','dataset-bkshpg','bkshpg_szdw_jxnl','main-inssisch0332',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxnl%main-inssisch0332'
);

-- 本科审核评估_师资队伍_教学能力_每年实验技术人员中专任教师占比-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxnl%main-inssisch0333','dataset-bkshpg','bkshpg_szdw_jxnl','main-inssisch0333',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxnl%main-inssisch0333'
);

-- 本科审核评估_师资队伍_教学能力_每年具有一年以上国(境)外经历专任教师比例-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxnl%main-inssisch0334','dataset-bkshpg','bkshpg_szdw_jxnl','main-inssisch0334',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxnl%main-inssisch0334'
);

-- 本科审核评估_师资队伍_教学能力_每年具有博士学位专任教师占比-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxnl%main-inssisch0335','dataset-bkshpg','bkshpg_szdw_jxnl','main-inssisch0335',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxnl%main-inssisch0335'
);

-- 本科审核评估_师资队伍_教学能力_每年双师双能型专任教师人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxnl%main-inssicol0157','dataset-bkshpg','bkshpg_szdw_jxnl','main-inssicol0157',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxnl%main-inssicol0157'
);

-- 本科审核评估_师资队伍_教学能力_每年各学院具有一年以上国(境)外经历专任教师比例-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxnl%main-inssicol0158','dataset-bkshpg','bkshpg_szdw_jxnl','main-inssicol0158',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxnl%main-inssicol0158'
);

-- 本科审核评估_师资队伍_教学能力_每年具有一年以上国(境)外经历专任教师人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw_jxnl%main-inssicol0159','dataset-bkshpg','bkshpg_szdw_jxnl','main-inssicol0159',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw_jxnl%main-inssicol0159'
);

-- 本科审核评估_师资队伍_高层次教学、研究团队-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw%main-insgccjxyjtd','dataset-bkshpg','bkshpg_szdw','main-insgccjxyjtd',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw%main-insgccjxyjtd'
);

-- 本科审核评估_师资队伍_高层次人才-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw%main-insgccrc','dataset-bkshpg','bkshpg_szdw','main-insgccrc',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw%main-insgccrc'
);

-- 本科审核评估_师资队伍_教师培训进修、交流情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw%main-insjspxjxjlqk','dataset-bkshpg','bkshpg_szdw','main-insjspxjxjlqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw%main-insjspxjxjlqk'
);

-- 本科审核评估_师资队伍_教职工基础信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw%main-insjzgjbxx','dataset-bkshpg','bkshpg_szdw','main-insjzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw%main-insjzgjbxx'
);

-- 本科审核评估_师资队伍_教职工其他信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw%main-insjzgqtxx','dataset-bkshpg','bkshpg_szdw','main-insjzgqtxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw%main-insjzgqtxx'
);

-- 本科审核评估_师资队伍_思政课教师情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw%main-insszkjsqk','dataset-bkshpg','bkshpg_szdw','main-insszkjsqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw%main-insszkjsqk'
);

-- 本科审核评估_师资队伍_外聘和兼职教师基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw%main-inswphjzjsjbxx','dataset-bkshpg','bkshpg_szdw','main-inswphjzjsjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw%main-inswphjzjsjbxx'
);

-- 本科审核评估_师资队伍_校领导基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw%main-insxldjbxx','dataset-bkshpg','bkshpg_szdw','main-insxldjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw%main-insxldjbxx'
);

-- 本科审核评估_师资队伍_教师活动情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw%main-abdteajshdqk','dataset-bkshpg','bkshpg_szdw','main-abdteajshdqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw%main-abdteajshdqk'
);

-- 本科审核评估_师资队伍_教师个人荣誉-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw%main-abdteajsgrry','dataset-bkshpg','bkshpg_szdw','main-abdteajsgrry',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw%main-abdteajsgrry'
);

-- 本科审核评估_师资队伍_班导师基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw%main-abdteabdsjbxx','dataset-bkshpg','bkshpg_szdw','main-abdteabdsjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw%main-abdteabdsjbxx'
);

-- 本科审核评估_师资队伍_教职工基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw%main-abdteajzgjbxx','dataset-bkshpg','bkshpg_szdw','main-abdteajzgjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw%main-abdteajzgjbxx'
);

-- 本科审核评估_师资队伍_教职工教学工作量-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw%main-abdteajzgjxgzl','dataset-bkshpg','bkshpg_szdw','main-abdteajzgjxgzl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw%main-abdteajzgjxgzl'
);

-- 本科审核评估_师资队伍_教职工发表论文-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw%main-abdteajzgfblw','dataset-bkshpg','bkshpg_szdw','main-abdteajzgfblw',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw%main-abdteajzgfblw'
);

-- 本科审核评估_师资队伍_教职工发明专利-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_szdw%main-abdteajzgfmzl','dataset-bkshpg','bkshpg_szdw','main-abdteajzgfmzl',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_szdw%main-abdteajzgfmzl'
);

-- 本科审核评估_招生情况_生源情况_每年学校招生计划人数-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zsqk_syqk%main-inssimaj0023','dataset-bkshpg','bkshpg_zsqk_syqk','main-inssimaj0023',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zsqk_syqk%main-inssimaj0023'
);

-- 本科审核评估_招生情况_生源情况_每年学校实际录取人数-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zsqk_syqk%main-inssimaj0024','dataset-bkshpg','bkshpg_zsqk_syqk','main-inssimaj0024',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zsqk_syqk%main-inssimaj0024'
);

-- 本科审核评估_招生情况_生源情况_每年生源来源省份数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zsqk_syqk%main-inssisch0428','dataset-bkshpg','bkshpg_zsqk_syqk','main-inssisch0428',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zsqk_syqk%main-inssisch0428'
);

-- 本科审核评估_招生情况_生源情况_每年各学院实际录取人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zsqk_syqk%main-inssicol0274','dataset-bkshpg','bkshpg_zsqk_syqk','main-inssicol0274',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zsqk_syqk%main-inssicol0274'
);

-- 本科审核评估_招生情况_生源情况_每年各学院录取人数前五名的中学分布-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zsqk_syqk%main-inssicol0275','dataset-bkshpg','bkshpg_zsqk_syqk','main-inssicol0275',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zsqk_syqk%main-inssicol0275'
);

-- 本科审核评估_招生情况_生源情况_每年招生中学录取人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zsqk_syqk%main-inssicol0276','dataset-bkshpg','bkshpg_zsqk_syqk','main-inssicol0276',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zsqk_syqk%main-inssicol0276'
);

-- 本科审核评估_招生情况_生源情况_每年优质生源录取人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zsqk_syqk%main-inssicol0198','dataset-bkshpg','bkshpg_zsqk_syqk','main-inssicol0198',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zsqk_syqk%main-inssicol0198'
);

-- 本科审核评估_招生情况_生源情况_每年优质生源中学数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zsqk_syqk%main-inssicol0199','dataset-bkshpg','bkshpg_zsqk_syqk','main-inssicol0199',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zsqk_syqk%main-inssicol0199'
);

-- 本科审核评估_招生情况_报到情况_每年报到人数-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zsqk_bdqk%main-inssimaj0008','dataset-bkshpg','bkshpg_zsqk_bdqk','main-inssimaj0008',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zsqk_bdqk%main-inssimaj0008'
);

-- 本科审核评估_招生情况_报到情况_每年专业（大类）报到率-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zsqk_bdqk%main-inssimaj0009','dataset-bkshpg','bkshpg_zsqk_bdqk','main-inssimaj0009',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zsqk_bdqk%main-inssimaj0009'
);

-- 本科审核评估_招生情况_报到情况_每年专业（大类）第一志愿录取率-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zsqk_bdqk%main-inssimaj0010','dataset-bkshpg','bkshpg_zsqk_bdqk','main-inssimaj0010',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zsqk_bdqk%main-inssimaj0010'
);

-- 本科审核评估_招生情况_报到情况_每年未报到学生人数-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zsqk_bdqk%main-inssimaj0033','dataset-bkshpg','bkshpg_zsqk_bdqk','main-inssimaj0033',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zsqk_bdqk%main-inssimaj0033'
);

-- 本科审核评估_招生情况_报到情况_每年报到率-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zsqk_bdqk%main-inssisch0429','dataset-bkshpg','bkshpg_zsqk_bdqk','main-inssisch0429',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zsqk_bdqk%main-inssisch0429'
);

-- 本科审核评估_招生情况_报到情况_每年第一志愿录取率-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zsqk_bdqk%main-inssisch0430','dataset-bkshpg','bkshpg_zsqk_bdqk','main-inssisch0430',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zsqk_bdqk%main-inssisch0430'
);

-- 本科审核评估_招生情况_近一级本科生录取标准及人数-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zsqk%main-insjyjbkslqbzjrs','dataset-bkshpg','bkshpg_zsqk','main-insjyjbkslqbzjrs',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zsqk%main-insjyjbkslqbzjrs'
);

-- 本科审核评估_招生情况_近一级本科生招生类别情况-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zsqk%main-insjyjbkszslbqk','dataset-bkshpg','bkshpg_zsqk','main-insjyjbkszslbqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zsqk%main-insjyjbkszslbqk'
);

-- 本科审核评估_招生情况_近一级各专业（大类）招生报到情况-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zsqk%main-insjyjgzyzsbdqk','dataset-bkshpg','bkshpg_zsqk','main-insjyjgzyzsbdqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zsqk%main-insjyjgzyzsbdqk'
);

-- 本科审核评估_招生情况_本科生基本情况-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zsqk%main-insbksjbqk','dataset-bkshpg','bkshpg_zsqk','main-insbksjbqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zsqk%main-insbksjbqk'
);

-- 本科审核评估_招生情况_优质生源-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zsqk%main-abdundyzsy','dataset-bkshpg','bkshpg_zsqk','main-abdundyzsy',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zsqk%main-abdundyzsy'
);

-- 本科审核评估_招生情况_本专科生基本信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zsqk%main-abdundbzksjbxx','dataset-bkshpg','bkshpg_zsqk','main-abdundbzksjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zsqk%main-abdundbzksjbxx'
);

-- 本科审核评估_招生情况_研究生基本信息-POS_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zsqk%main-abdposyjsjbxx','dataset-bkshpg','bkshpg_zsqk','main-abdposyjsjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zsqk%main-abdposyjsjbxx'
);

-- 本科审核评估_质量评估_学生评教_每年学校各专业课程测评均分-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_xspj%main-inssimaj0011','dataset-bkshpg','bkshpg_zlpg_xspj','main-inssimaj0011',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_xspj%main-inssimaj0011'
);

-- 本科审核评估_质量评估_学生评教_每年学校各专业班导师均分-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_xspj%main-inssimaj0017','dataset-bkshpg','bkshpg_zlpg_xspj','main-inssimaj0017',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_xspj%main-inssimaj0017'
);

-- 本科审核评估_质量评估_学生评教_本科生评教参评率-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_xspj%main-inssisch0468','dataset-bkshpg','bkshpg_zlpg_xspj','main-inssisch0468',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_xspj%main-inssisch0468'
);

-- 本科审核评估_质量评估_学生评教_每学期学校课程测评均分-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_xspj%main-inssisch0540','dataset-bkshpg','bkshpg_zlpg_xspj','main-inssisch0540',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_xspj%main-inssisch0540'
);

-- 本科审核评估_质量评估_学生评教_每年学校课程测评均分-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_xspj%main-inssisch0503','dataset-bkshpg','bkshpg_zlpg_xspj','main-inssisch0503',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_xspj%main-inssisch0503'
);

-- 本科审核评估_质量评估_学生评教_每年学校体育课测评均分-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_xspj%main-inssisch0510','dataset-bkshpg','bkshpg_zlpg_xspj','main-inssisch0510',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_xspj%main-inssisch0510'
);

-- 本科审核评估_质量评估_学生评教_每年学校班导师评教均分-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_xspj%main-inssisch0504','dataset-bkshpg','bkshpg_zlpg_xspj','main-inssisch0504',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_xspj%main-inssisch0504'
);

-- 本科审核评估_质量评估_学生评教_每年各学院课程均分-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_xspj%main-inssicol0300','dataset-bkshpg','bkshpg_zlpg_xspj','main-inssicol0300',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_xspj%main-inssicol0300'
);

-- 本科审核评估_质量评估_学生评教_每年学校各学院课程测评均分-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_xspj%main-inssicol0228','dataset-bkshpg','bkshpg_zlpg_xspj','main-inssicol0228',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_xspj%main-inssicol0228'
);

-- 本科审核评估_质量评估_学生评教_每年学校各学院班导师测评均分-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_xspj%main-inssicol0267','dataset-bkshpg','bkshpg_zlpg_xspj','main-inssicol0267',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_xspj%main-inssicol0267'
);

-- 本科审核评估_质量评估_学生评教_每年每位教师的评教分数-TEA_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_xspj%main-inssitea0063','dataset-bkshpg','bkshpg_zlpg_xspj','main-inssitea0063',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_xspj%main-inssitea0063'
);

-- 本科审核评估_质量评估_学生评教_每年体育课程测评均分-COU_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_xspj%main-inssicou0006','dataset-bkshpg','bkshpg_zlpg_xspj','main-inssicou0006',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_xspj%main-inssicou0006'
);

-- 本科审核评估_质量评估_随堂听课_每年各专业关注课程数量-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssimaj0012','dataset-bkshpg','bkshpg_zlpg_sttk','main-inssimaj0012',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssimaj0012'
);

-- 本科审核评估_质量评估_随堂听课_每年各专业听课均分-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssimaj0013','dataset-bkshpg','bkshpg_zlpg_sttk','main-inssimaj0013',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssimaj0013'
);

-- 本科审核评估_质量评估_随堂听课_每年听课门次数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssisch0469','dataset-bkshpg','bkshpg_zlpg_sttk','main-inssisch0469',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssisch0469'
);

-- 本科审核评估_质量评估_随堂听课_每年督导听课均分-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssisch0529','dataset-bkshpg','bkshpg_zlpg_sttk','main-inssisch0529',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssisch0529'
);

-- 本科审核评估_质量评估_随堂听课_每学期听课均分-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssisch0541','dataset-bkshpg','bkshpg_zlpg_sttk','main-inssisch0541',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssisch0541'
);

-- 本科审核评估_质量评估_随堂听课_每年听课均分-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssisch0470','dataset-bkshpg','bkshpg_zlpg_sttk','main-inssisch0470',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssisch0470'
);

-- 本科审核评估_质量评估_随堂听课_每年各类型课程听课均分-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssisch0538','dataset-bkshpg','bkshpg_zlpg_sttk','main-inssisch0538',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssisch0538'
);

-- 本科审核评估_质量评估_随堂听课_每年听课等第占比-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssisch0471','dataset-bkshpg','bkshpg_zlpg_sttk','main-inssisch0471',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssisch0471'
);

-- 本科审核评估_质量评估_随堂听课_每年各类型课程听课等第占比-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssisch0539','dataset-bkshpg','bkshpg_zlpg_sttk','main-inssisch0539',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssisch0539'
);

-- 本科审核评估_质量评估_随堂听课_每年优质课程数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssisch0472','dataset-bkshpg','bkshpg_zlpg_sttk','main-inssisch0472',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssisch0472'
);

-- 本科审核评估_质量评估_随堂听课_每年优质课程的上课学生数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssisch0473','dataset-bkshpg','bkshpg_zlpg_sttk','main-inssisch0473',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssisch0473'
);

-- 本科审核评估_质量评估_随堂听课_每年听课覆盖率-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssisch0474','dataset-bkshpg','bkshpg_zlpg_sttk','main-inssisch0474',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssisch0474'
);

-- 本科审核评估_质量评估_随堂听课_每年关注课程数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssicol0230','dataset-bkshpg','bkshpg_zlpg_sttk','main-inssicol0230',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssicol0230'
);

-- 本科审核评估_质量评估_随堂听课_每年各学院听课门次数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssicol0303','dataset-bkshpg','bkshpg_zlpg_sttk','main-inssicol0303',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssicol0303'
);

-- 本科审核评估_质量评估_随堂听课_每年各学院督导听课均分-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssicol0304','dataset-bkshpg','bkshpg_zlpg_sttk','main-inssicol0304',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssicol0304'
);

-- 本科审核评估_质量评估_随堂听课_每年学院各评教类型的听课均分-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssicol0301','dataset-bkshpg','bkshpg_zlpg_sttk','main-inssicol0301',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssicol0301'
);

-- 本科审核评估_质量评估_随堂听课_每年各学院听课均分-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssicol0231','dataset-bkshpg','bkshpg_zlpg_sttk','main-inssicol0231',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssicol0231'
);

-- 本科审核评估_质量评估_随堂听课_每年各学院课程听课覆盖率-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssicol0232','dataset-bkshpg','bkshpg_zlpg_sttk','main-inssicol0232',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssicol0232'
);

-- 本科审核评估_质量评估_随堂听课_每年每位教师的听课分数-TEA_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssitea0064','dataset-bkshpg','bkshpg_zlpg_sttk','main-inssitea0064',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_sttk%main-inssitea0064'
);

-- 本科审核评估_质量评估_专项检查_专业校内综合排名-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_zxjc%main-inssimaj0030','dataset-bkshpg','bkshpg_zlpg_zxjc','main-inssimaj0030',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_zxjc%main-inssimaj0030'
);

-- 本科审核评估_质量评估_专项检查_每年学校课程目标达成度-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_zxjc%main-inssisch0475','dataset-bkshpg','bkshpg_zlpg_zxjc','main-inssisch0475',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_zxjc%main-inssisch0475'
);

-- 本科审核评估_质量评估_专项检查_每年学校课程检查抽检率-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_zxjc%main-inssisch0476','dataset-bkshpg','bkshpg_zlpg_zxjc','main-inssisch0476',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_zxjc%main-inssisch0476'
);

-- 本科审核评估_质量评估_专项检查_每年学校各学院课程目标检查数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_zxjc%main-inssicol0233','dataset-bkshpg','bkshpg_zlpg_zxjc','main-inssicol0233',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_zxjc%main-inssicol0233'
);

-- 本科审核评估_质量评估_专项检查_每年学校各学院课程考核检查数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_zxjc%main-inssicol0234','dataset-bkshpg','bkshpg_zlpg_zxjc','main-inssicol0234',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_zxjc%main-inssicol0234'
);

-- 本科审核评估_质量评估_专项检查_每年学校各学院试卷检查数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_zxjc%main-inssicol0235','dataset-bkshpg','bkshpg_zlpg_zxjc','main-inssicol0235',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_zxjc%main-inssicol0235'
);

-- 本科审核评估_质量评估_专项检查_每年学校各学院毕业论文检查数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_zxjc%main-inssicol0236','dataset-bkshpg','bkshpg_zlpg_zxjc','main-inssicol0236',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_zxjc%main-inssicol0236'
);

-- 本科审核评估_质量评估_专项检查_每年学校各学院实验报告检查数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_zxjc%main-inssicol0237','dataset-bkshpg','bkshpg_zlpg_zxjc','main-inssicol0237',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_zxjc%main-inssicol0237'
);

-- 本科审核评估_质量评估_专项检查_每年学校各学院实习报告检查数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_zxjc%main-inssicol0238','dataset-bkshpg','bkshpg_zlpg_zxjc','main-inssicol0238',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_zxjc%main-inssicol0238'
);

-- 本科审核评估_质量评估_专项检查_每年学校各学院课程考核检查抽检率-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_zxjc%main-inssicol0239','dataset-bkshpg','bkshpg_zlpg_zxjc','main-inssicol0239',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_zxjc%main-inssicol0239'
);

-- 本科审核评估_质量评估_质量队伍_每年学校各学院教学质量管理人员数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_zldw%main-inssisch0480','dataset-bkshpg','bkshpg_zlpg_zldw','main-inssisch0480',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_zldw%main-inssisch0480'
);

-- 本科审核评估_质量评估_质量队伍_每年学校各学院管理人员数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_zldw%main-inssicol0240','dataset-bkshpg','bkshpg_zlpg_zldw','main-inssicol0240',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_zldw%main-inssicol0240'
);

-- 本科审核评估_质量评估_质量队伍_每年学校各学院管理人员比例-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_zldw%main-inssicol0255','dataset-bkshpg','bkshpg_zlpg_zldw','main-inssicol0255',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_zldw%main-inssicol0255'
);

-- 本科审核评估_质量评估_质量队伍_每年学校各学院班导师情况-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_zldw%main-inssicol0241','dataset-bkshpg','bkshpg_zlpg_zldw','main-inssicol0241',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_zldw%main-inssicol0241'
);

-- 本科审核评估_质量评估_质量队伍_每年学校各学院班导师比例-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_zldw%main-inssicol0256','dataset-bkshpg','bkshpg_zlpg_zldw','main-inssicol0256',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_zldw%main-inssicol0256'
);

-- 本科审核评估_质量评估_质量队伍_教师校内综合排名-TEA_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_zldw%main-inssitea0066','dataset-bkshpg','bkshpg_zlpg_zldw','main-inssitea0066',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_zldw%main-inssitea0066'
);

-- 本科审核评估_质量评估_质量队伍_教师院内综合排名-TEA_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_zldw%main-inssitea0067','dataset-bkshpg','bkshpg_zlpg_zldw','main-inssitea0067',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_zldw%main-inssitea0067'
);

-- 本科审核评估_质量评估_质量队伍_学生专业内综合排名-UND_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_zldw%main-inssiund0021','dataset-bkshpg','bkshpg_zlpg_zldw','main-inssiund0021',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_zldw%main-inssiund0021'
);

-- 本科审核评估_质量评估_质量队伍_课程校内综合排名-COU_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlpg_zldw%main-inssicou0014','dataset-bkshpg','bkshpg_zlpg_zldw','main-inssicou0014',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlpg_zldw%main-inssicou0014'
);

-- 本科审核评估_就业与发展_学生就业_每年学校各专业毕业生去向落实率-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssimaj0028','dataset-bkshpg','bkshpg_jyyfz_xsjy','main-inssimaj0028',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssimaj0028'
);

-- 本科审核评估_就业与发展_学生就业_每年学校毕业生去向落实率-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0460','dataset-bkshpg','bkshpg_jyyfz_xsjy','main-inssisch0460',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0460'
);

-- 本科审核评估_就业与发展_学生就业_每年学校学位授予率-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0484','dataset-bkshpg','bkshpg_jyyfz_xsjy','main-inssisch0484',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0484'
);

-- 本科审核评估_就业与发展_学生就业_每年学校毕业生升学或深造率-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0461','dataset-bkshpg','bkshpg_jyyfz_xsjy','main-inssisch0461',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0461'
);

-- 本科审核评估_就业与发展_学生就业_每年学校所在省份就业率-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0542','dataset-bkshpg','bkshpg_jyyfz_xsjy','main-inssisch0542',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0542'
);

-- 本科审核评估_就业与发展_学生就业_每年学校毕业生就业率-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0462','dataset-bkshpg','bkshpg_jyyfz_xsjy','main-inssisch0462',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0462'
);

-- 本科审核评估_就业与发展_学生就业_每年毕业生就业分类比例-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0543','dataset-bkshpg','bkshpg_jyyfz_xsjy','main-inssisch0543',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0543'
);

-- 本科审核评估_就业与发展_学生就业_每年学校毕业生签约就业占比-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0463','dataset-bkshpg','bkshpg_jyyfz_xsjy','main-inssisch0463',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0463'
);

-- 本科审核评估_就业与发展_学生就业_每年学校就业地域流向比例-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0531','dataset-bkshpg','bkshpg_jyyfz_xsjy','main-inssisch0531',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0531'
);

-- 本科审核评估_就业与发展_学生就业_每年各生源地的就业地域流向比例-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0532','dataset-bkshpg','bkshpg_jyyfz_xsjy','main-inssisch0532',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0532'
);

-- 本科审核评估_就业与发展_学生就业_每年学校升学深造类型比例-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0533','dataset-bkshpg','bkshpg_jyyfz_xsjy','main-inssisch0533',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0533'
);

-- 本科审核评估_就业与发展_学生就业_每年学校就业类型比例-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0534','dataset-bkshpg','bkshpg_jyyfz_xsjy','main-inssisch0534',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0534'
);

-- 本科审核评估_就业与发展_学生就业_每年学校毕业生去向分类比例-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0535','dataset-bkshpg','bkshpg_jyyfz_xsjy','main-inssisch0535',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0535'
);

-- 本科审核评估_就业与发展_学生就业_每年学校毕业生去向类型人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0464','dataset-bkshpg','bkshpg_jyyfz_xsjy','main-inssisch0464',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0464'
);

-- 本科审核评估_就业与发展_学生就业_每年学校毕业生单位性质人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0465','dataset-bkshpg','bkshpg_jyyfz_xsjy','main-inssisch0465',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0465'
);

-- 本科审核评估_就业与发展_学生就业_每年学校毕业生单位性质比例-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0536','dataset-bkshpg','bkshpg_jyyfz_xsjy','main-inssisch0536',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0536'
);

-- 本科审核评估_就业与发展_学生就业_每年学校毕业生单位行业人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0466','dataset-bkshpg','bkshpg_jyyfz_xsjy','main-inssisch0466',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0466'
);

-- 本科审核评估_就业与发展_学生就业_每年学校毕业生单位行业占比-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0537','dataset-bkshpg','bkshpg_jyyfz_xsjy','main-inssisch0537',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssisch0537'
);

-- 本科审核评估_就业与发展_学生就业_每年学校各学院毕业生去向落实率-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssicol0222','dataset-bkshpg','bkshpg_jyyfz_xsjy','main-inssicol0222',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssicol0222'
);

-- 本科审核评估_就业与发展_学生就业_每年学位授予率-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssicol0223','dataset-bkshpg','bkshpg_jyyfz_xsjy','main-inssicol0223',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssicol0223'
);

-- 本科审核评估_就业与发展_学生就业_每年学校各学院学位人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssicol0224','dataset-bkshpg','bkshpg_jyyfz_xsjy','main-inssicol0224',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsjy%main-inssicol0224'
);

-- 本科审核评估_就业与发展_学业预警_每年各专业平均绩点-MAJ_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xyyj%main-inssimaj0029','dataset-bkshpg','bkshpg_jyyfz_xyyj','main-inssimaj0029',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xyyj%main-inssimaj0029'
);

-- 本科审核评估_就业与发展_学业预警_每学年累计不及格学分达到专业培养方案总学分25%及以上的学生数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xyyj%main-inssisch0467','dataset-bkshpg','bkshpg_jyyfz_xyyj','main-inssisch0467',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xyyj%main-inssisch0467'
);

-- 本科审核评估_就业与发展_学业预警_每学年学业预警学生数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xyyj%main-inssicol0225','dataset-bkshpg','bkshpg_jyyfz_xyyj','main-inssicol0225',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xyyj%main-inssicol0225'
);

-- 本科审核评估_就业与发展_学业预警_每学年不及格学生数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xyyj%main-inssicol0226','dataset-bkshpg','bkshpg_jyyfz_xyyj','main-inssicol0226',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xyyj%main-inssicol0226'
);

-- 本科审核评估_就业与发展_学业预警_每学期不及格学生数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xyyj%main-inssicol0252','dataset-bkshpg','bkshpg_jyyfz_xyyj','main-inssicol0252',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xyyj%main-inssicol0252'
);

-- 本科审核评估_就业与发展_学业预警_每学年不同累计不及格学分数范围的学生数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xyyj%main-inssicol0227','dataset-bkshpg','bkshpg_jyyfz_xyyj','main-inssicol0227',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xyyj%main-inssicol0227'
);

-- 本科审核评估_就业与发展_学业预警_每年每位学生已选学分-UND_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xyyj%main-inssiund0017','dataset-bkshpg','bkshpg_jyyfz_xyyj','main-inssiund0017',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xyyj%main-inssiund0017'
);

-- 本科审核评估_就业与发展_学业预警_每年每位学生已获学分-UND_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xyyj%main-inssiund0018','dataset-bkshpg','bkshpg_jyyfz_xyyj','main-inssiund0018',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xyyj%main-inssiund0018'
);

-- 本科审核评估_就业与发展_学业预警_每学年学期每位学生绩点-UND_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xyyj%main-inssiund0019','dataset-bkshpg','bkshpg_jyyfz_xyyj','main-inssiund0019',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xyyj%main-inssiund0019'
);

-- 本科审核评估_就业与发展_学业预警_每年每位学生绩点-UND_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xyyj%main-inssiund0020','dataset-bkshpg','bkshpg_jyyfz_xyyj','main-inssiund0020',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xyyj%main-inssiund0020'
);

-- 本科审核评估_就业与发展_学业预警_每年每位学生思政成绩-UND_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xyyj%main-inssiund0022','dataset-bkshpg','bkshpg_jyyfz_xyyj','main-inssiund0022',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xyyj%main-inssiund0022'
);

-- 本科审核评估_就业与发展_学业预警_每年每位学生体育成绩-UND_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xyyj%main-inssiund0023','dataset-bkshpg','bkshpg_jyyfz_xyyj','main-inssiund0023',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xyyj%main-inssiund0023'
);

-- 本科审核评估_就业与发展_学生发展_每年学生参与科研项目数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsfz%main-inssisch0455','dataset-bkshpg','bkshpg_jyyfz_xsfz','main-inssisch0455',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsfz%main-inssisch0455'
);

-- 本科审核评估_就业与发展_学生发展_每年学生参与创作、表演作品数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsfz%main-inssisch0456','dataset-bkshpg','bkshpg_jyyfz_xsfz','main-inssisch0456',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsfz%main-inssisch0456'
);

-- 本科审核评估_就业与发展_学生发展_每年学科竞赛获奖数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsfz%main-inssisch0457','dataset-bkshpg','bkshpg_jyyfz_xsfz','main-inssisch0457',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsfz%main-inssisch0457'
);

-- 本科审核评估_就业与发展_学生发展_每年各类学科竞赛参与人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsfz%main-inssisch0512','dataset-bkshpg','bkshpg_jyyfz_xsfz','main-inssisch0512',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsfz%main-inssisch0512'
);

-- 本科审核评估_就业与发展_学生发展_每年各类学科竞赛参与比例-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsfz%main-inssisch0513','dataset-bkshpg','bkshpg_jyyfz_xsfz','main-inssisch0513',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsfz%main-inssisch0513'
);

-- 本科审核评估_就业与发展_学生发展_每年艺术类竞赛获奖数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsfz%main-inssisch0458','dataset-bkshpg','bkshpg_jyyfz_xsfz','main-inssisch0458',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsfz%main-inssisch0458'
);

-- 本科审核评估_就业与发展_学生发展_每年体育类竞赛获奖数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsfz%main-inssisch0459','dataset-bkshpg','bkshpg_jyyfz_xsfz','main-inssisch0459',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsfz%main-inssisch0459'
);

-- 本科审核评估_就业与发展_学生发展_每年学校体质健康合格率-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsfz%main-inssisch0483','dataset-bkshpg','bkshpg_jyyfz_xsfz','main-inssisch0483',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsfz%main-inssisch0483'
);

-- 本科审核评估_就业与发展_学生发展_每年发表学术论文人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsfz%main-inssicol0217','dataset-bkshpg','bkshpg_jyyfz_xsfz','main-inssicol0217',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsfz%main-inssicol0217'
);

-- 本科审核评估_就业与发展_学生发展_每年以第一作者获批专利人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsfz%main-inssicol0218','dataset-bkshpg','bkshpg_jyyfz_xsfz','main-inssicol0218',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsfz%main-inssicol0218'
);

-- 本科审核评估_就业与发展_学生发展_每年学科竞赛参与人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsfz%main-inssicol0219','dataset-bkshpg','bkshpg_jyyfz_xsfz','main-inssicol0219',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsfz%main-inssicol0219'
);

-- 本科审核评估_就业与发展_学生发展_每年各类学科竞赛各类赛道参与人数-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsfz%main-inssicol0272','dataset-bkshpg','bkshpg_jyyfz_xsfz','main-inssicol0272',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsfz%main-inssicol0272'
);

-- 本科审核评估_就业与发展_学生发展_每年学科竞赛参与比例-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsfz%main-inssicol0220','dataset-bkshpg','bkshpg_jyyfz_xsfz','main-inssicol0220',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsfz%main-inssicol0220'
);

-- 本科审核评估_就业与发展_学生发展_每年体质健康合格率-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jyyfz_xsfz%main-inssicol0221','dataset-bkshpg','bkshpg_jyyfz_xsfz','main-inssicol0221',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jyyfz_xsfz%main-inssicol0221'
);

-- 本科审核评估_学生工作_本科生奖贷补-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_xsgz%main-insbksjdb','dataset-bkshpg','bkshpg_xsgz','main-insbksjdb',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_xsgz%main-insbksjdb'
);

-- 本科审核评估_学生工作_学生社团-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_xsgz%main-insxsst','dataset-bkshpg','bkshpg_xsgz','main-insxsst',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_xsgz%main-insxsst'
);

-- 本科审核评估_学生工作_学生工作_每年奖贷补资助金额-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_xsgz_xsgz%main-inssisch0431','dataset-bkshpg','bkshpg_xsgz_xsgz','main-inssisch0431',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_xsgz_xsgz%main-inssisch0431'
);

-- 本科审核评估_学生工作_学生工作_每年学生社团数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_xsgz_xsgz%main-inssisch0432','dataset-bkshpg','bkshpg_xsgz_xsgz','main-inssisch0432',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_xsgz_xsgz%main-inssisch0432'
);

-- 本科审核评估_学生工作_学生工作_每年获得荣誉称号人数-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_xsgz_xsgz%main-inssisch0433','dataset-bkshpg','bkshpg_xsgz_xsgz','main-inssisch0433',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_xsgz_xsgz%main-inssisch0433'
);

-- 本科审核评估_学生工作_学生工作_每年毕业生英语等级通过率-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_xsgz_xsgz%main-inssisch0434','dataset-bkshpg','bkshpg_xsgz_xsgz','main-inssisch0434',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_xsgz_xsgz%main-inssisch0434'
);

-- 本科审核评估_学生工作_学生工作_每年国外港澳台学生数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_xsgz_xsgz%main-inssicol0200','dataset-bkshpg','bkshpg_xsgz_xsgz','main-inssicol0200',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_xsgz_xsgz%main-inssicol0200'
);

-- 本科审核评估_学生工作_学生工作_每年学院毕业生英语等级通过率-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_xsgz_xsgz%main-inssicol0201','dataset-bkshpg','bkshpg_xsgz_xsgz','main-inssicol0201',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_xsgz_xsgz%main-inssicol0201'
);

-- 本科审核评估_学生工作_学生个人荣誉-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_xsgz%main-abdundxsgrry','dataset-bkshpg','bkshpg_xsgz','main-abdundxsgrry',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_xsgz%main-abdundxsgrry'
);

-- 本科审核评估_学生发展_本科生交流情况-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_xsfz%main-insbksjlqk','dataset-bkshpg','bkshpg_xsfz','main-insbksjlqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_xsfz%main-insbksjlqk'
);

-- 本科审核评估_学生发展_学生数量基本情况-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_xsfz%main-insxxsljbqk','dataset-bkshpg','bkshpg_xsfz','main-insxxsljbqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_xsfz%main-insxxsljbqk'
);

-- 本科审核评估_学生发展_学生获专业比赛奖励情况（体育类专业用）-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_xsfz%main-instylxshzybsjlqk','dataset-bkshpg','bkshpg_xsfz','main-instylxshzybsjlqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_xsfz%main-instylxshzybsjlqk'
);

-- 本科审核评估_学生发展_学生参加大学生创新创业训练计划情况-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_xsfz%main-insxscjdxscxcyxljhqk','dataset-bkshpg','bkshpg_xsfz','main-insxscjdxscxcyxljhqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_xsfz%main-insxscjdxscxcyxljhqk'
);

-- 本科审核评估_学生发展_学生参与教师科研项目情况-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_xsfz%main-insxscyjskyxmqk','dataset-bkshpg','bkshpg_xsfz','main-insxscyjskyxmqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_xsfz%main-insxscyjskyxmqk'
);

-- 本科审核评估_学生发展_学生创作、表演的代表性作品-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_xsfz%main-insxsczbyddbxzp','dataset-bkshpg','bkshpg_xsfz','main-insxsczbyddbxzp',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_xsfz%main-insxsczbyddbxzp'
);

-- 本科审核评估_学生发展_学生发表学术论文情况-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_xsfz%main-insxsfbxslwqk','dataset-bkshpg','bkshpg_xsfz','main-insxsfbxslwqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_xsfz%main-insxsfbxslwqk'
);

-- 本科审核评估_学生发展_学生各类竞赛奖励情况-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_xsfz%main-insxshsjjysgljsjlqk','dataset-bkshpg','bkshpg_xsfz','main-insxshsjjysgljsjlqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_xsfz%main-insxshsjjysgljsjlqk'
);

-- 本科审核评估_学生发展_学生专利（著作权）授权情况-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_xsfz%main-insxszlsqqk','dataset-bkshpg','bkshpg_xsfz','main-insxszlsqqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_xsfz%main-insxszlsqqk'
);

-- 本科审核评估_学生发展_应届本科毕业生去向落实情况-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_xsfz%main-insyjbkbysqxlsqk','dataset-bkshpg','bkshpg_xsfz','main-insyjbkbysqxlsqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_xsfz%main-insyjbkbysqxlsqk'
);

-- 本科审核评估_学生发展_学生获专业比赛奖励情况（艺术类专业用）-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_xsfz%main-insyslxshzybsjlqk','dataset-bkshpg','bkshpg_xsfz','main-insyslxshzybsjlqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_xsfz%main-insyslxshzybsjlqk'
);

-- 本科审核评估_质量保障_听课记录表-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlbz%main-abdschtkjlb','dataset-bkshpg','bkshpg_zlbz','main-abdschtkjlb',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlbz%main-abdschtkjlb'
);

-- 本科审核评估_质量保障_专项检查信息-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlbz%main-abdschzxjcxx','dataset-bkshpg','bkshpg_zlbz','main-abdschzxjcxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlbz%main-abdschzxjcxx'
);

-- 本科审核评估_质量保障_品牌团队获奖情况-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlbz%main-abdschpptdhjqk','dataset-bkshpg','bkshpg_zlbz','main-abdschpptdhjqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlbz%main-abdschpptdhjqk'
);

-- 本科审核评估_质量保障_发展历程-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlbz%main-abdschfzlc','dataset-bkshpg','bkshpg_zlbz','main-abdschfzlc',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlbz%main-abdschfzlc'
);

-- 本科审核评估_质量保障_毕业生满意度评价-SCH_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlbz%main-abdschbysmydpj','dataset-bkshpg','bkshpg_zlbz','main-abdschbysmydpj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlbz%main-abdschbysmydpj'
);

-- 本科审核评估_质量保障_相关管理人员基本信息-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlbz%main-insxgglryjbxx','dataset-bkshpg','bkshpg_zlbz','main-insxgglryjbxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlbz%main-insxgglryjbxx'
);

-- 本科审核评估_质量保障_班导师评教结果-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlbz%main-abdteapjbds','dataset-bkshpg','bkshpg_zlbz','main-abdteapjbds',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlbz%main-abdteapjbds'
);

-- 本科审核评估_质量保障_教学班评教结果-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlbz%main-abdteapjjxb','dataset-bkshpg','bkshpg_zlbz','main-abdteapjjxb',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlbz%main-abdteapjjxb'
);

-- 本科审核评估_质量保障_教师评教情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlbz%main-abdteajspjqk','dataset-bkshpg','bkshpg_zlbz','main-abdteajspjqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlbz%main-abdteajspjqk'
);

-- 本科审核评估_质量保障_学生评教信息-UND_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_zlbz%main-abdundxspjxx','dataset-bkshpg','bkshpg_zlbz','main-abdundxspjxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_zlbz%main-abdundxspjxx'
);

-- 本科审核评估_成果特色_成果特色_每年学校教学成果奖数量-SCH_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_cgts_cgts%main-inssisch0489','dataset-bkshpg','bkshpg_cgts_cgts','main-inssisch0489',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_cgts_cgts%main-inssisch0489'
);

-- 本科审核评估_成果特色_成果特色_每年教学成果奖数量-COL_SI.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_cgts_cgts%main-inssicol0242','dataset-bkshpg','bkshpg_cgts_cgts','main-inssicol0242',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_cgts_cgts%main-inssicol0242'
);

-- 本科审核评估_教学建设_博士点、硕士点-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxjs%main-insbsdssd','dataset-bkshpg','bkshpg_jxjs','main-insbsdssd',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxjs%main-insbsdssd'
);

-- 本科审核评估_教学建设_学科信息-SUB_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxjs%main-abdsubxkxx','dataset-bkshpg','bkshpg_jxjs','main-abdsubxkxx',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxjs%main-abdsubxkxx'
);

-- 本科审核评估_教学成果_教师出版专著和主编教材情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxcg%main-insjscbzzhzbjcqk','dataset-bkshpg','bkshpg_jxcg','main-insjscbzzhzbjcqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxcg%main-insjscbzzhzbjcqk'
);

-- 本科审核评估_教学成果_教师科研成果转化情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxcg%main-insjskycgzhqk','dataset-bkshpg','bkshpg_jxcg','main-insjskycgzhqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxcg%main-insjskycgzhqk'
);

-- 本科审核评估_教学成果_教师专利（著作权）授权情况-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxcg%main-insjszlsqqk','dataset-bkshpg','bkshpg_jxcg','main-insjszlsqqk',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxcg%main-insjszlsqqk'
);

-- 本科审核评估_教学成果_教学成果奖-TEA_ABD.pdma.json
INSERT INTO t_da_dataset_model (id,sjjid,flid,mxid,
create_by,create_time,update_by,update_time,
model_table_copy,status,use_num)
SELECT 'dataset-bkshpg%bkshpg_jxcg%main-insjxcgj','dataset-bkshpg','bkshpg_jxcg','main-insjxcgj',
'dataapp', TIMESTAMP '2024-06-20 01:00:00', 
'dataapp', TIMESTAMP '2024-06-20 01:00:00',
'',1,0
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_da_dataset_model
    WHERE id = 'dataset-bkshpg%bkshpg_jxcg%main-insjxcgj'
);