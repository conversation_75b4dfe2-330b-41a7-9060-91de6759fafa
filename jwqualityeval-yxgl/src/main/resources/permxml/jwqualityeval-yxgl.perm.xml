<?xml version="1.0" encoding="UTF-8"?>
<app id="jwqualityeval" name="教务质量监测" groupId="jwqualityevalgroup">
    <!--菜单（目录）-->
    <menus id="jwqualityeval-yxgl" formRouterName="yxgl" name="运行管理" showOrder="2">
        <!--开课情况-菜单（页面）-->
        <menu bindType="default" formRouterName="kkqk" id="jwqualityeval-kkqk"
              menuPattern="pc"
              name="开课情况"
              showOrder="1">
            <routerParams>{ "appCode": "jwqualityeval", "pageCode": "kkqk"}</routerParams>
            <!-- 权限字-表单 -->
            <permCode name="开课情况" permCode="formkkqk" permCodeId="jwqualityeval-kkqk"
                      permCodeType="0"/>
            <!-- 权限字-片段 -->
            <permCode name="开课情况" permCode="formkkqk:fragmentkkqk"
                      permCodeId="fragmentjwqualityeval-kkqk"
                      permCodeType="1">
                <perm name="帆软代理服务接口" url="/toolpub/proxy/**"/>
                <perm name="获取res静态文件" url="/staticFile/getStaticFile"/>
                <perm name="刷选框下拉接口" url="/jwdatapub/select/**"/>
                <perm name="数据更新时间接口" url="/jwdatapub/dataUpdateTime/**"/>
            </permCode>
            <btn id="jwqualityeval-kkqk-show" menuPattern="pc" name="显示">
                <permCode name="显示" permCode="formkkqk:fragmentkkqk:show"
                          permCodeId="jwqualityeval-kkqk-show"/>
            </btn>
        </menu>

        <!--排课情况-菜单（页面）-->
        <menu bindType="default" formRouterName="pkqk" id="jwqualityeval-pkqk"
              menuPattern="pc"
              name="排课情况"
              showOrder="2">
            <routerParams>{ "appCode": "jwqualityeval", "pageCode": "pkqk"}</routerParams>
            <!-- 权限字-表单 -->
            <permCode name="排课情况" permCode="formpkqk" permCodeId="jwqualityeval-pkqk"
                      permCodeType="0"/>
            <!-- 权限字-片段 -->
            <permCode name="排课情况" permCode="formpkqk:fragmentpkqk"
                      permCodeId="fragmentjwqualityeval-pkqk"
                      permCodeType="1">
                <perm name="帆软代理服务接口" url="/toolpub/proxy/**"/>
                <perm name="获取res静态文件" url="/staticFile/getStaticFile"/>
                <perm name="刷选框下拉接口" url="/jwdatapub/select/**"/>
                <perm name="数据更新时间接口" url="/jwdatapub/dataUpdateTime/**"/>
            </permCode>
            <btn id="jwqualityeval-pkqk-show" menuPattern="pc" name="显示">
                <permCode name="显示" permCode="formpkqk:fragmentpkqk:show"
                          permCodeId="jwqualityeval-pkqk-show"/>
            </btn>
        </menu>

        <!--选课情况-菜单（页面）-->
        <menu bindType="default" formRouterName="xkqk" id="jwqualityeval-xkqk"
              menuPattern="pc"
              name="选课情况"
              showOrder="3">
            <routerParams>{ "appCode": "jwqualityeval", "pageCode": "xkqk"}</routerParams>
            <!-- 权限字-表单 -->
            <permCode name="选课情况" permCode="formxkqk" permCodeId="jwqualityeval-xkqk"
                      permCodeType="0"/>
            <!-- 权限字-片段 -->
            <permCode name="选课情况" permCode="formxkqk:fragmentxkqk"
                      permCodeId="fragmentjwqualityeval-xkqk"
                      permCodeType="1">
                <perm name="帆软代理服务接口" url="/toolpub/proxy/**"/>
                <perm name="获取res静态文件" url="/staticFile/getStaticFile"/>
                <perm name="刷选框下拉接口" url="/jwdatapub/select/**"/>
                <perm name="数据更新时间接口" url="/jwdatapub/dataUpdateTime/**"/>
            </permCode>
            <btn id="jwqualityeval-xkqk-show" menuPattern="pc" name="显示">
                <permCode name="显示" permCode="formxkqk:fragmentxkqk:show"
                          permCodeId="jwqualityeval-xkqk-show"/>
            </btn>
        </menu>

        <!--排考情况-菜单（页面）-->
        <menu bindType="default" formRouterName="pakqk" id="jwqualityeval-pakqk"
              menuPattern="pc"
              name="排考情况"
              showOrder="4">
            <routerParams>{ "appCode": "jwqualityeval", "pageCode": "pakqk"}</routerParams>
            <!-- 权限字-表单 -->
            <permCode name="排考情况" permCode="formpakqk" permCodeId="jwqualityeval-pakqk"
                      permCodeType="0"/>
            <!-- 权限字-片段 -->
            <permCode name="排考情况" permCode="formpakqk:fragmentpakqk"
                      permCodeId="fragmentjwqualityeval-pakqk"
                      permCodeType="1">
                <perm name="帆软代理服务接口" url="/toolpub/proxy/**"/>
                <perm name="获取res静态文件" url="/staticFile/getStaticFile"/>
                <perm name="刷选框下拉接口" url="/jwdatapub/select/**"/>
                <perm name="数据更新时间接口" url="/jwdatapub/dataUpdateTime/**"/>
            </permCode>
            <btn id="jwqualityeval-pakqk-show" menuPattern="pc" name="显示">
                <permCode name="显示" permCode="formpakqk:fragmentpakqk:show"
                          permCodeId="jwqualityeval-pakqk-show"/>
            </btn>
        </menu>

         <!--菜单（页面）-->
        <menu bindType="default" formRouterName="jxjhyfapcjhlb" id="jwqualityeval-jxjhyfapcjhlb"
              menuPattern="pc"
              name="教学计划与方案偏差计划列表"
              showOrder="5"
              menuHidden="1">
            <routerParams>{ "appCode": "jwqualityeval", "pageCode": "jxjhyfapcjhlb"}</routerParams>
            <!-- 权限字-表单 -->
            <permCode name="教学计划与方案偏差计划列表" permCode="formjxjhyfapcjhlb"
                      permCodeId="jwqualityeval-jxjhyfapcjhlb"
                      permCodeType="0"/>
            <!-- 权限字-片段 -->
            <permCode name="教学计划与方案偏差计划列表" permCode="formjxjhyfapcjhlb:fragmentjxjhyfapcjhlb"
                      permCodeId="fragmentjwqualityeval-jxjhyfapcjhlb"
                      permCodeType="1">
                <perm name="数据更新时间接口" url="/jwdatapub/dataUpdateTime/**"/>
                <perm name="字典集合" url="/admin/dict/list"/>
                <perm name="查询级联表格平铺接口" url="/eda/jwqualityeval/find/jxjhyfapcjhlbbo/jxjhyfapcjhlbbo"/>
                <perm name="页面数据展示" url="/eda/jwqualityeval/page/jxjhyfapcjhlbbo"/>
                <perm name="页面数据导出" url="/eda/jwqualityeval/export/jxjhyfapcjhlbbo"/>
                <perm name="页面导出文件下载" url="/admin/file/jwqualityeval/download"/>
            </permCode>
            <btn id="jwqualityeval-jxjhyfapcjhlb-show" menuPattern="pc" name="显示">
                <permCode name="显示" permCode="formjxjhyfapcjhlb:fragmentjxjhyfapcjhlb:show"
                          permCodeId="jwqualityeval-jxjhyfapcjhlb-show"/>
            </btn>
        </menu>

         <!--菜单（页面）-->
        <menu bindType="default" formRouterName="jhpcxxlb" id="jwqualityeval-jhpcxxlb"
              menuPattern="pc"
              name="计划偏差信息列表"
              showOrder="6"
              menuHidden="1">
            <routerParams>{ "appCode": "jwqualityeval", "pageCode": "jhpcxxlb"}</routerParams>
            <!-- 权限字-表单 -->
            <permCode name="计划偏差信息列表" permCode="formjhpcxxlb"
                      permCodeId="jwqualityeval-jhpcxxlb"
                      permCodeType="0"/>
            <!-- 权限字-片段 -->
            <permCode name="计划偏差信息列表" permCode="formjhpcxxlb:fragmentjhpcxxlb"
                      permCodeId="fragmentjwqualityeval-jhpcxxlb"
                      permCodeType="1">
                <perm name="数据更新时间接口" url="/jwdatapub/dataUpdateTime/**"/>
                <perm name="字典集合" url="/admin/dict/list"/>
                <perm name="查询级联表格平铺接口" url="/eda/jwqualityeval/find/jhpcxxlbbo/jhpcxxlbbo"/>
                <perm name="页面数据展示" url="/eda/jwqualityeval/page/jhpcxxlbbo"/>
                <perm name="页面数据导出" url="/eda/jwqualityeval/export/jhpcxxlbbo"/>
                <perm name="页面导出文件下载" url="/admin/file/jwqualityeval/download"/>
            </permCode>
            <btn id="jwqualityeval-jhpcxxlb-show" menuPattern="pc" name="显示">
                <permCode name="显示" permCode="formjhpcxxlb:fragmentjhpcxxlb:show"
                          permCodeId="jwqualityeval-jhpcxxlb-show"/>
            </btn>
        </menu>

         <!--菜单（页面）-->
        <menu bindType="default" formRouterName="xyyfapcjhslb" id="jwqualityeval-xyyfapcjhslb"
              menuPattern="pc"
              name="学院与方案偏差计划数列表"
              showOrder="7"
              menuHidden="1">
            <routerParams>{ "appCode": "jwqualityeval", "pageCode": "xyyfapcjhslb"}</routerParams>
            <!-- 权限字-表单 -->
            <permCode name="学院与方案偏差计划数列表" permCode="formxyyfapcjhslb"
                      permCodeId="jwqualityeval-xyyfapcjhslb"
                      permCodeType="0"/>
            <!-- 权限字-片段 -->
            <permCode name="学院与方案偏差计划数列表" permCode="formxyyfapcjhslb:fragmentxyyfapcjhslb"
                      permCodeId="fragmentjwqualityeval-xyyfapcjhslb"
                      permCodeType="1">
                <perm name="数据更新时间接口" url="/jwdatapub/dataUpdateTime/**"/>
                <perm name="字典集合" url="/admin/dict/list"/>
                <perm name="查询级联表格平铺接口" url="/eda/jwqualityeval/find/xyyfapcjhslbbo/xyyfapcjhslbbo"/>
                <perm name="页面数据展示" url="/eda/jwqualityeval/page/xyyfapcjhslbbo"/>
                <perm name="页面数据导出" url="/eda/jwqualityeval/export/xyyfapcjhslbbo"/>
                <perm name="页面导出文件下载" url="/admin/file/jwqualityeval/download"/>
            </permCode>
            <btn id="jwqualityeval-xyyfapcjhslb-show" menuPattern="pc" name="显示">
                <permCode name="显示" permCode="formxyyfapcjhslb:fragmentxyyfapcjhslb:show"
                          permCodeId="jwqualityeval-xyyfapcjhslb-show"/>
            </btn>
        </menu>

         <!--菜单（页面）-->
        <menu bindType="default" formRouterName="xyapjxblb" id="jwqualityeval-xyapjxblb"
              menuPattern="pc"
              name="学院安排教学班列表"
              showOrder="8"
              menuHidden="1">
            <routerParams>{ "appCode": "jwqualityeval", "pageCode": "xyapjxblb"}</routerParams>
            <!-- 权限字-表单 -->
            <permCode name="学院安排教学班列表" permCode="formxyapjxblb"
                      permCodeId="jwqualityeval-xyapjxblb"
                      permCodeType="0"/>
            <!-- 权限字-片段 -->
            <permCode name="学院安排教学班列表" permCode="formxyapjxblb:fragmentxyapjxblb"
                      permCodeId="fragmentjwqualityeval-xyapjxblb"
                      permCodeType="1">
                <perm name="数据更新时间接口" url="/jwdatapub/dataUpdateTime/**"/>
                <perm name="字典集合" url="/admin/dict/list"/>
                <perm name="查询级联表格平铺接口" url="/eda/jwqualityeval/find/xyapjxblbbo/xyapjxblbbo"/>
                <perm name="页面数据展示" url="/eda/jwqualityeval/page/xyapjxblbbo"/>
                <perm name="页面数据导出" url="/eda/jwqualityeval/export/xyapjxblbbo"/>
                <perm name="页面导出文件下载" url="/admin/file/jwqualityeval/download"/>
            </permCode>
            <btn id="jwqualityeval-xyapjxblb-show" menuPattern="pc" name="显示">
                <permCode name="显示" permCode="formxyapjxblb:fragmentxyapjxblb:show"
                          permCodeId="jwqualityeval-xyapjxblb-show"/>
            </btn>
        </menu>

    </menus>
</app>