###
POST http://127.0.0.1:8282/dataapp/admin/app/template/offline-pc-page-jwdata-jwzljcxzlb-ptxzlb
content-type: application/json;charset=UTF-8
Cookie: Authorization_lowcode=lowcodeadmin_77960390e9174270900d7b3678c07496; authType=DB

{
  "globalParam": {
    "appCode": "jwqualityeval",
    "appName": "教务质量监测"
  },
  "params": [
    {
      "moduleCode": "yxgl",
      "pageCode": "jhpcxxlb",
      "pageName": "计划偏差信息列表",
      "model": "jwqualityeval-jhpcxxlbbo",
      "enabledOperate": false,
      "operateName": "",
      "opJumpPage": "",
      "moduleName": "运行管理",
      "modelData": {
        "appName": "教务质量监测",
        "bizModel": 1,
        "cannotUpdFields": "clazzName,modelName,moduleCode,modelTable",
        "cascades": [],
        "clazz": "com.wisedu.lowcode4j.app.jwqualityeval.bo.JhpcxxlbBo",
        "clazzName": "JhpcxxlbBo",
        "createBy": "admin",
        "createTime": "2025-07-29 17:47:42",
        "defaultOrder": "",
        "extendFlag": 0,
        "id": "jwqualityeval-jhpcxxlbbo",
        "modelApp": "jwqualityeval",
        "modelClass": "com.wisedu.lowcode4j.app.jwqualityeval.bo.JhpcxxlbBo",
        "modelClassify": "",
        "modelId": "jwqualityeval-jhpcxxlbbo",
        "modelLabel": "计划偏差信息列表",
        "modelName": "jhpcxxlbbo",
        "orderIndex": 1,
        "permitExtFlag": 1,
        "permitUpdate": 1,
        "publishTime": "2025-07-29 17:47:42",
        "renderType": "table",
        "renderTypeName": "表格",
        "status": 1,
        "sysFlag": 0,
        "updateBy": "admin",
        "updateTime": "2025-07-29 17:47:42",
        "virtualModel": 0
      }
    }
  ]
}
