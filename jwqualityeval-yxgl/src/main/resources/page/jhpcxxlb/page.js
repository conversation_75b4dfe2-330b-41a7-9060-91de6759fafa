define(function (require) {
    return {
        /**
         * 监听指定对象的值发生变化，然后执行特定方法
         */
        pageWatch: {
            'globalVars.name': function (newVal, oldVal) {
                //console.log('值改变',newVal,oldVal)
            }
        },
        /**
         * 监听指定对象的值发生变化，然后执行特定方法
         */
        pageComputed: {
            getNewName: function () {
                return this.globalVars.name + '新的'
            }
        },
        var1: "变量",//响应式变量，该变量不能加到组件参数内,但是写到组件模板里
        /**
         * 响应式变量，当值变化时可以影响所有绑定的值,建议把需要绑定到组件参数里的变量申明到这里面
         */
        globalVars: {
            name: '一个变量', //响应式变量name,组件内使用时{{globalVars.name}}
            dicCaches: {},
            // url搜索参数缓存，目前重置搜索条件时使用
            urlParamCaches: {},

            // ********************配置项********************
            // url 传入的参数无需进行 name -> code 转换的字段
            urlParamNoNeedName2CodeFileds: [
                'xnxq', 'xdlx'
            ],
            // 搜索时的参数无需进行 code -> name 转换的字段
            searchParamNoNeedCode2nameFileds: [
                'xdlx', 'bglx', 'cddw'
            ]
            // *********************************************
        },
        /**
         * 页面被重新激活时调用
         */
        pageActivated: function () {
            //console.log('页面激活')
        },
        /**
         * 页面失去激活被缓存时调用
         */
        pageDeactivated: function () {
            //console.log('页面失活')
        },
        /**
         * 固定方法，页面js初始化完成后调用,当前能修改js变量，修改组件初始化属性或者设置组件默认值
         */
        pageCreated: function () {
            this.handleDataUpdateTime();
        },
        // 处理数据更新时间
        handleDataUpdateTime: function () {
            this.$request({
                method: 'post',
                url: '/jwdatapub/dataUpdateTime/getLatestTime',
                data: {"dataUpdateType": "jwqualityeval_jhpcxxlb"}
            }).then(res => {
                if (res.code === '0' && res.data && res.data.latestTime) {
                    window.postMessage({
                        messageType: 'dataUpdateTime',
                        dataUpdateTime: res.data.latestTime
                    }, '*');
                }
            });
        },
        /**
         * 固定方法，页面准备完成后调用，当前可以操作组件属性，调用未隐藏组件实例方法
         */
        pageReady: function () {
            //console.log('页面准备完成后调用')
        },
        /**
         * 固定方法，页面销毁前调用
         */
        pageDestroy: function () {
            //console.log('页面销毁前调用')
        },

        /**
         * 描述：mounted
         * @param{event}  内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组) return:
         */
        adv_search_utx4qjkq_mounted: async function (event) {
            if (!this.$pageRoute.merge.params) {
                return;
            }
            // 取得路由传递来的参数，带入搜索
            const params = JSON.parse(decodeURIComponent(this.$pageRoute.merge.params));

            const code2nameFiledDicMapping = {};
            const modelVals = {};
            const dicCodes = [];

            // 找到所有的需 name -> code 的下拉字段，请求其数据源
            for (let key in event.$com.formItemVm) {
                if (key !== 'formVm') {
                    const fieldName = key.split('.')[1] || key;// 可能是 xm 或者是 jzgjbxx.xm，提取 xm
                    if (params[fieldName] != undefined && params[fieldName] != null) {
                        if (event.$com.formItemVm[key].isSelect && !this.globalVars.urlParamNoNeedName2CodeFileds.includes(fieldName)) {
                            // 需要进行转换
                            const dicCode = event.$com.formItemVm[key]._props.dataOptions.remoteDict.dictCode;
                            code2nameFiledDicMapping[fieldName] = dicCode;
                            // 去重
                            if (!dicCodes.includes(dicCode)) {
                                dicCodes.push(dicCode);
                            }
                        } else {
                            modelVals[key] = params[fieldName];
                        }
                    }
                }
            }

            if (dicCodes.length > 0) {
                const dicsResult = await this.$request({
                    method: 'post',
                    url: `/admin/dict/list`,
                    data: {
                        cacheTypes: dicCodes.join(','),
                        needAllStatus: 1
                    }
                });
                // 缓存起来
                if (dicCodes.length === 1) {
                    this.globalVars.dicCaches = {
                        [dicCodes[0]]: dicsResult.data
                    }
                } else {
                    this.globalVars.dicCaches = dicsResult.data
                }

                for (let key in event.$com.formItemVm) {
                    if (key !== 'formVm') {
                        const fieldName = key.split('.')[1] || key;// 可能是 xm 或者是 jzgjbxx.xm，提取 xm
                        if (params[fieldName] !== undefined && params[fieldName] != null) {
                            if (!!code2nameFiledDicMapping[fieldName]) {
                                const dicArray = dicCodes.length === 1 ? dicsResult.data : dicsResult.data[code2nameFiledDicMapping[fieldName]];
                                var paramValues = params[fieldName].split(',');
                                var tempValues = [];
                                paramValues.forEach(value => {
                                    const trimValue = value.trim();
                                    for (let i = 0; i < dicArray.length; i++) {
                                        if (dicArray[i].itemName === trimValue) {
                                            tempValues.push(dicArray[i].itemId);
                                            break;
                                        }
                                    }
                                });
                                modelVals[key] = tempValues.join(',');
                            } else {
                                modelVals[key] = params[fieldName];
                            }
                        }
                    }
                }
            }

            this.globalVars.urlParamCaches = modelVals;

            this.$setModelVals('adv-search_utx4qjkq', modelVals);

            this.$nextTick(() => {
                setTimeout(() => {
                    this.$page('adv-search_utx4qjkq').doSearch();
                }, 0);
            });
        },

        /**
         * 描述：search
         * @param{event}  内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组) return:

         */
        adv_search_utx4qjkq_search: function (event) {
            const queryParams = event.evArgs[0];

            queryParams.forEach(item => {
                // 判断是否为下拉
                if (event.$com.formItemVm[item.name] && event.$com.formItemVm[item.name].isSelect) {
                    // 判断是否需要进行转换
                    const fieldName = item.name.split('.')[1] || item.name;
                    if (!this.globalVars.searchParamNoNeedCode2nameFileds.includes(fieldName)) {
                        const display = event.$com.getDictDisplay(item.name);
                        if (display) {
                            item.value = display.split('、').join(',');
                        } else {
                            // 此时下拉数据源还未就绪，从缓存中取
                            const dicCode = event.$com.formItemVm[item.name]._props.dataOptions.remoteDict.dictCode;
                            const dicData = this.globalVars.dicCaches[dicCode];

                            const values = item.value.split(',');
                            const convertValues = [];

                            values.forEach(value => {
                                for (let i = 0; i < dicData.length; i++) {
                                    const option = dicData[i];
                                    if (option.itemId === value) {
                                        convertValues.push(option.itemName);
                                        break;
                                    }
                                }
                            });

                            item.value = convertValues.join(',');
                        }
                    }
                }
            });
        },

        /**
         * 描述：model-inited
         * @param{event}  内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组) return:

         */
        adv_table_hd433oll_model_inited: function (event) {
            var self = this;

            const canClickFields = [];
            event.$com.columnDatas.forEach(column => {
                if (!!column.drilldownClickable) {
                    canClickFields.push(column.field);
                }
            })
            this.$setComProps("adv-table_hd433oll", {
                'cell-class-name': function (ev) {
                    const fieldName = ev.column.field;
                    if (canClickFields.includes(fieldName)) {
                        return 'field-clickable';
                    }
                }
            })
        },

        /**
         * 描述：cell-click
         * @param{event}  内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组) return:

         */
        adv_table_hd433oll_cell_click: function (event) {
            var self = this;

            // 当前点击的列
            const currentClickColumn = event.evArgs[0].column.field;
            let drilldownClickable = false;
            let drilldownParamName = null;
            let drilldownParamValue = null;
            event.$com.columnDatas.some(column => {
                if (column.field === currentClickColumn) {
                    if (!!column.drilldownClickable) {
                        drilldownClickable = true;
                        if (column.hasOwnProperty('drilldownParamName')) {
                            drilldownParamName = column.drilldownParamName;
                            drilldownParamValue = column.drilldownParamValue;
                        }
                    }
                    return true;
                } else {
                    return false;
                }
            });

            if (drilldownClickable) {
                // 当前列可以点击下钻
                const params = {
                    [drilldownParamName]: drilldownParamValue
                };

                /*****************下钻时带的参数*****************/
                // 获取“学年学期”值代入下钻页面
                params.xnxq = event.evArgs[0].row.xnxq;
                /*********************************************/

                this.drilldownPage(params);
            }
        },

        drilldownPage: function (params) {
            const urlParams = {
                ...params,
                timestamp: Date.now()
            };
            // 跳转下钻列表，只需修改页面code即可
            this.$router.push({
                name: '',
                query: {
                    parentMenuId: this.$route.query?.parentMenuId || this.$route.name,
                    params: encodeURIComponent(JSON.stringify(urlParams))
                }
            });
        },

        /**
         * 描述：跳转画像
         * @param{event}  {row}:对应的行数据;{col}:对应的列数据;{btn}:点击的按钮；{scope}:表格对象 return:

         */
        action_ev_ia10z40t: function (event) {
            var self = this;

            // 跳转至''
            const params = {
                xnxq: event.row.xnxq,
                timestamp: Date.now()
            };
            this.$router.push({
                name: '',
                query: {
                    parentMenuId: this.$route.query?.parentMenuId || this.$route.name,
                    params: encodeURIComponent(JSON.stringify(params))
                }
            });
        },

        /**
         * 描述：预处理字段显隐
         * @param {modelItem} 对应模型信息，可以修改模型内容；返回一个模型信息
         */
        before_render_w6hpl66w: function (modelItem) {
            var self = this;

            if (!modelItem['search.hidden'] && modelItem.xtype === "number-range") {
                const fieldName = modelItem.name.split('.')[1] || modelItem.name;
                const urlParams = this.$pageRoute.merge.params ? JSON.parse(decodeURIComponent(this.$pageRoute.merge.params)) : {};
                if (urlParams[fieldName] !== undefined && urlParams[fieldName] != null) {
                    modelItem['defaultVal'] = urlParams[fieldName].split(',');
                }
            }
        },


        /**
         * 描述：before-reset
         * @param{event}  内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组) return:

         */
        adv_search_utx4qjkq_before_reset: function (event) {
            var self = this;

            this.$setModelVals('adv-search_utx4qjkq', this.globalVars.urlParamCaches);
        },
    }
})