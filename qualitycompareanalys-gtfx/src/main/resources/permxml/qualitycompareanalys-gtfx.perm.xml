<?xml version="1.0" encoding="UTF-8"?>
<app id="qualitycompareanalys" name="学业表现" groupId="qualitycompareanalysgroup">
    <!--菜单（目录）-->
    <menus id="qualitycompareanalys-gtfx" formRouterName="gtfx" name="质量个体分析" showOrder="1">
        <!--菜单（页面）-->
        <menu bindType="default" formRouterName="studentlist" id="qualitycompareanalys-studentlist"
              menuPattern="pc"
              name="学生信息"
              showOrder="2">
            <routerParams>{ "appCode": "qualitycompareanalys", "pageCode": "studentlist"}</routerParams>
            <!-- 权限字-表单 -->
            <permCode name="学生信息" permCode="formstudentlist" permCodeId="qualitycompareanalys-studentlist"
                      permCodeType="0"/>
            <!-- 权限字-片段 -->
            <permCode name="学生信息" permCode="formstudentlist:fragmentstudentlist"
                      permCodeId="fragmentqualitycompareanalys-studentlist"
                      permCodeType="1">
                <perm name="查询级联表格平铺接口" url="/eda/qualitycompareanalys/find/bksxxlb/bksxxlb"/>
                <perm name="页面数据展示" url="/eda/qualitycompareanalys/page/bksxxlb"/>
                <perm name="页面数据导出" url="/eda/qualitycompareanalys/export/bksxxlb"/>
                <perm name="页面导出文件下载" url="/admin/file/qualitycompareanalys/download"/>
            </permCode>
            <btn id="qualitycompareanalys-studentlist-show" menuPattern="pc" name="显示">
                <permCode name="显示" permCode="formstudentlist:fragmentstudentlist:show"
                          permCodeId="qualitycompareanalys-studentlist-show"/>
            </btn>
        </menu>

        <!--菜单（页面）-->
        <menu bindType="default" formRouterName="xshx" id="qualitycompareanalys-xshx"
              menuPattern="pc"
              name="学生画像"
              showOrder="10"
              menuHidden="1">
            <routerParams>{ "appCode": "qualitycompareanalys", "pageCode": "xshx"}</routerParams>
            <!-- 权限字-表单 -->
            <permCode name="学生画像" permCode="formxshx" permCodeId="qualitycompareanalys-xshx"
                      permCodeType="0"/>
            <!-- 权限字-片段 -->
            <permCode name="学生画像" permCode="formxshx:fragmentxshx"
                      permCodeId="fragmentqualitycompareanalys-xshx"
                      permCodeType="1">
                <perm name="帆软代理服务接口" url="/toolpub/proxy/**"/>
                <perm name="获取res静态文件" url="/staticFile/getStaticFile"/>
            </permCode>
            <btn id="qualitycompareanalys-xshx-show" menuPattern="pc" name="显示">
                <permCode name="显示" permCode="formxshx:fragmentxshx:show"
                          permCodeId="qualitycompareanalys-xshx-show"/>
            </btn>
        </menu>
    </menus>
</app>