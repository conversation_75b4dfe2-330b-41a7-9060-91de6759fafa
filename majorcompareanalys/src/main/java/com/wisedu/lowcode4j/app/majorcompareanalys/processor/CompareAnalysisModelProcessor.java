package com.wisedu.lowcode4j.app.majorcompareanalys.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.gitee.starblues.bootstrap.annotation.AutowiredType;
import com.wisedu.lowcode4j.common.core.constant.ApplicationConstant;
import com.wisedu.lowcode4j.common.core.model.QueryModel;
import com.wisedu.lowcode4j.common.core.util.ApplicationContextHolder;
import com.wisedu.lowcode4j.common.db.BaseService;
import com.wisedu.lowcode4j.common.model.ModelProcessor;
import com.wisedu.lowcode4j.common.model.po.DataModel;
import com.wisedu.lowcode4j.common.model.po.DataModelColumn;
import com.wisedu.lowcode4j.common.model.vo.PageColVo;
import com.wisedu.lowcode4j.common.model.vo.PageSearchColVo;
import com.wisedu.lowcode4j.common.model.vo.PageTableColVo;
import com.wisedu.lowcode4j.main.modelpub.cqrs.ModelQueryService;
import com.wisedu.lowcode4j.main.modelpub.po.DaModelColumn;
import com.wisedu.lowcode4j.main.qcapub.service.QualityAnalysisPubService;
import com.wisedu.lowcode4j.main.qcapub.service.QualityModelProcessorService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.wisedu.lowcode4j.app.majorcompareanalys.constant.CompareAnalysisConstant.MODEL_CLASSIFY;

/**
 * 群体分析指标模型干预
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service("compareAnalysisModelProcessor")
public class CompareAnalysisModelProcessor implements ModelProcessor {
	private static final List<String> IGNORE_LIST = Arrays.asList("id", "createBy", "createTime", "updateBy", "updateTime", "zymc");

	private static final String ENTITY_NAME_FILED = "ZYMC";

	private static final Map<String, Integer> BIZ_FILED_ORDER_MAP;

	static {
		BIZ_FILED_ORDER_MAP = new HashMap<>();
		BIZ_FILED_ORDER_MAP.put(ENTITY_NAME_FILED, 2);
	}

	@Autowired
	@AutowiredType(AutowiredType.Type.MAIN)
	private ModelQueryService modelQueryService;

	@Autowired
	@AutowiredType(AutowiredType.Type.MAIN)
	private QualityAnalysisPubService qualityAnalysisPubService;

	@Autowired
	@AutowiredType(AutowiredType.Type.MAIN)
	private QualityModelProcessorService qualityModelProcessorService;


	@SuppressWarnings("unchecked")
	@Override
	public boolean afterModelConfig(List<? extends PageColVo> pageColVos, String actionType, Map params) throws Exception {
		log.info("群体分析指标模型干预：【pageColVos={},actionType={},params={}】", pageColVos, actionType, params);
		// 参数为空直接返回
		if (MapUtils.isEmpty(params)) {
			return true;
		}

		// 模型ID
		String modelId = MapUtils.getString(params, "modelId");
		if (StrUtil.isEmpty(modelId)) {
			log.info("传入模型ID为空 modelId={}", modelId);
			return true;
		}
		// 数据权限
		qualityAnalysisPubService.authModelData(modelId, MODEL_CLASSIFY);
		DataModel entityDataModel = modelQueryService.getDataModelById(modelId);
		if (entityDataModel == null) {
			log.info("获取指标结果模型失败 modelId={}", modelId);
			return true;
		}

		// 根据参数，获取不同的模型
		BaseService mainBaseService = ApplicationContextHolder.getBean(ApplicationConstant.MAIN, "baseService");
		// 转换列信息格式
		if (ApplicationConstant.ACTION_TYPE_SEARCH.equals(actionType)) {
			List<PageSearchColVo> entityColVos = mainBaseService.convertTypeList(entityDataModel.getColumns(), PageSearchColVo.class);
			entityColVos.removeIf(x -> IGNORE_LIST.contains(x.getColumnJavaname()));
			((List<PageColVo>) pageColVos).addAll(entityColVos);
		} else if (ApplicationConstant.ACTION_TYPE_TABLE.equals(actionType)) {
			// 获取实体模型字段信息
			List<DataModelColumn> entityColumns = entityDataModel.getColumns();
			// 移除不展示字段
			entityColumns.removeIf(x -> Convert.toInt(x.getColumnDisplay(), 1) == 0);
			// 转换为表格展示字段列表
			List<PageTableColVo> entityColVos = mainBaseService.convertTypeList(entityColumns, PageTableColVo.class);
			// 移除忽略字段
			entityColVos.removeIf(x -> IGNORE_LIST.contains(x.getColumnJavaname()));
			// 合并模型字段
			((List<PageColVo>) pageColVos).addAll(entityColVos);
			qualityModelProcessorService.sortPageColVos(pageColVos, entityDataModel, BIZ_FILED_ORDER_MAP);
		}
		return true;
	}
}
