package com.wisedu.lowcode4j.app.jwqualityeval.bo;

import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;
import com.wisedu.lowcode4j.common.model.constant.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;
import org.sagacity.sqltoy.model.SecureType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;
import com.alibaba.fastjson.annotation.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */


@ApiModel(value = "xyyfapcjhslbbo", description = "学院与方案偏差计划数列表")
@Comment("学院与方案偏差计划数列表")
@ModelDefine(renderType="table")
//end_dynamic_declare
@Data
public class XyyfapcjhslbBo extends BaseBizModel {

    private static final long serialVersionUID = 4431474721041951051L;

    //region start_dynamic_column
    @ApiModelProperty(name = "id", value = "ID" ,notes = "2025-07-30 14:23:55")
    @Comment("ID")
    @Column(value = "id")
    @ColumnDefine(columnHidden=true,columnXtype="text",tableHidden=true,formHidden=true,searchHidden=true)
    private String id;

    @ApiModelProperty(name = "xy", value = "学院" ,notes = "2025-07-30 14:23:55")
    @Comment("学院")
    @Column(value = "xy")
    @ColumnDefine(columnXtype="text")
    private String xy;

    @ApiModelProperty(name = "fapcjhzs", value = "方案偏差计划总数" ,notes = "2025-07-30 14:23:55")
    @Comment("方案偏差计划总数")
    @Column(value = "fapcjhzs")
    @ColumnDefine(columnXtype="text")
    private String fapcjhzs;

    @ApiModelProperty(name = "pcjhbl", value = "偏差计划比例" ,notes = "2025-07-30 14:23:55")
    @Comment("偏差计划比例")
    @Column(value = "pcjhbl")
    @ColumnDefine(columnXtype="text")
    private String pcjhbl;

    @ApiModelProperty(name = "xnxq", value = "学年学期" ,notes = "2025-07-30 14:23:55")
    @Comment("学年学期")
    @Column(value = "xnxq")
    @ColumnDefine(columnDict="biz_school_term",columnXtype="text")
    private String xnxq;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "xnxqName", value = "学年学期名称")
    @Translate(cacheName = "biz_school_term", keyField = "xnxq", cacheType = "", split=",")
    @JSONField(name = "xnxq"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String xnxqName;

	//endregion end_dynamic_dict_column

}
