package com.wisedu.lowcode4j.app.jwqualityeval.bo;

import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;
import com.wisedu.lowcode4j.common.model.constant.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;
import org.sagacity.sqltoy.model.SecureType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;
import com.alibaba.fastjson.annotation.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */


@ApiModel(value = "jhpcxxlbbo", description = "计划偏差信息列表")
@Comment("计划偏差信息列表")
@ModelDefine(renderType="table")
//end_dynamic_declare
@Data
public class JhpcxxlbBo extends BaseBizModel {

    private static final long serialVersionUID = 4431474721041951050L;

    //region start_dynamic_column
    @ApiModelProperty(name = "id", value = "ID" ,notes = "2025-07-30 14:23:55")
    @Comment("ID")
    @Column(value = "id", width = 128)
    @ColumnDefine(columnHidden=true,columnXtype="text",tableHidden=true,formHidden=true,searchHidden=true)
    private String id;

    @ApiModelProperty(name = "kch", value = "课程号" ,notes = "2025-07-30 14:23:55")
    @Comment("课程号")
    @Column(value = "kch", width = 128)
    @ColumnDefine(columnXtype="text")
    private String kch;

    @ApiModelProperty(name = "kcmc", value = "课程名称" ,notes = "2025-07-30 14:23:55")
    @Comment("课程名称")
    @Column(value = "kcmc", width = 128)
    @ColumnDefine(columnXtype="text")
    private String kcmc;

    @ApiModelProperty(name = "pyfamc", value = "培养方案名称" ,notes = "2025-07-30 14:23:55")
    @Comment("培养方案名称")
    @Column(value = "pyfamc", width = 128)
    @ColumnDefine(columnXtype="text")
    private String pyfamc;

    @ApiModelProperty(name = "bglx", value = "变更类型" ,notes = "2025-07-30 14:23:55")
    @Comment("变更类型")
    @Column(value = "bglx", width = 128)
    @ColumnDefine(columnDict="jwdatapub_bglx",columnXtype="text")
    private String bglx;

    @ApiModelProperty(name = "bgxx", value = "变更信息" ,notes = "2025-07-30 14:23:55")
    @Comment("变更信息")
    @Column(value = "bgxx", width = 128)
    @ColumnDefine(columnXtype="text")
    private String bgxx;

    @ApiModelProperty(name = "jys", value = "教研室" ,notes = "2025-07-30 14:23:55")
    @Comment("教研室")
    @Column(value = "jys", width = 128)
    @ColumnDefine(columnXtype="text")
    private String jys;

    @ApiModelProperty(name = "zxs", value = "总学时" ,notes = "2025-07-30 14:23:55")
    @Comment("总学时")
    @Column(value = "zxs", width = 128)
    @ColumnDefine(columnXtype="text")
    private String zxs;

    @ApiModelProperty(name = "xdlx", value = "修读类型" ,notes = "2025-07-30 14:23:55")
    @Comment("修读类型")
    @Column(value = "xdlx", width = 128)
    @ColumnDefine(columnDict="jwdatapub_xdlx",columnXtype="text")
    private String xdlx;

    @ApiModelProperty(name = "yjhxnxq", value = "原计划学年学期" ,notes = "2025-07-30 14:23:55")
    @Comment("原计划学年学期")
    @Column(value = "yjhxnxq", width = 128)
    @ColumnDefine(columnDict="biz_school_term",columnXtype="text")
    private String yjhxnxq;

    @ApiModelProperty(name = "tzzxnxq", value = "调整至学年学期" ,notes = "2025-07-30 14:23:55")
    @Comment("调整至学年学期")
    @Column(value = "tzzxnxq", width = 128)
    @ColumnDefine(columnDict="biz_school_term",columnXtype="text")
    private String tzzxnxq;

    @ApiModelProperty(name = "xnxq", value = "学年学期" ,notes = "2025-07-30 14:23:55")
    @Comment("学年学期")
    @Column(value = "xnxq", width = 128)
    @ColumnDefine(columnDict="biz_school_term",columnXtype="text")
    private String xnxq;

    @ApiModelProperty(name = "cddw", value = "承担单位" ,notes = "2025-07-30 14:23:55")
    @Comment("承担单位")
    @Column(value = "cddw", width = 128)
    @ColumnDefine(columnDict="department",columnXtype="select")
    private String cddw;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "bglxName", value = "变更类型名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "bglx", cacheType = "jwdatapub_bglx", split=",")
    @JSONField(name = "bglx"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String bglxName;

    @ApiModelProperty(name = "xdlxName", value = "修读类型名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "xdlx", cacheType = "jwdatapub_xdlx", split=",")
    @JSONField(name = "xdlx"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String xdlxName;

    @ApiModelProperty(name = "yjhxnxqName", value = "原计划学年学期名称")
    @Translate(cacheName = "biz_school_term", keyField = "yjhxnxq", cacheType = "", split=",")
    @JSONField(name = "yjhxnxq"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String yjhxnxqName;

    @ApiModelProperty(name = "tzzxnxqName", value = "调整至学年学期名称")
    @Translate(cacheName = "biz_school_term", keyField = "tzzxnxq", cacheType = "", split=",")
    @JSONField(name = "tzzxnxq"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String tzzxnxqName;

    @ApiModelProperty(name = "xnxqName", value = "学年学期名称")
    @Translate(cacheName = "biz_school_term", keyField = "xnxq", cacheType = "", split=",")
    @JSONField(name = "xnxq"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String xnxqName;

    @ApiModelProperty(name = "cddwName", value = "承担单位名称")
    @Translate(cacheName = "department", keyField = "cddw", cacheType = "", split=",")
    @JSONField(name = "cddw"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String cddwName;

	//endregion end_dynamic_dict_column

}
