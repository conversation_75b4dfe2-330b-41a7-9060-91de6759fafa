package com.wisedu.lowcode4j.app.jwqualityeval.bo;

import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;
import com.wisedu.lowcode4j.common.model.constant.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;
import org.sagacity.sqltoy.model.SecureType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;
import com.alibaba.fastjson.annotation.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */


@ApiModel(value = "jxjhyfapcjhlbbo", description = "教学计划与方案偏差计划列表")
@Comment("教学计划与方案偏差计划列表")
@ModelDefine(renderType="table")
//end_dynamic_declare
@Data
public class JxjhyfapcjhlbBo extends BaseBizModel {

    private static final long serialVersionUID = 4431474721041951049L;

    //region start_dynamic_column
    @ApiModelProperty(name = "id", value = "ID" ,notes = "2025-07-30 14:23:55")
    @Comment("ID")
    @Column(value = "id", width = 128)
    @ColumnDefine(columnHidden=true,columnXtype="text",tableHidden=true,formHidden=true,searchHidden=true)
    private String id;

    @ApiModelProperty(name = "xnxq", value = "学年学期" ,notes = "2025-07-30 14:23:55")
    @Comment("学年学期")
    @Column(value = "xnxq", width = 128)
    @ColumnDefine(columnDict="biz_school_term",columnXtype="text")
    private String xnxq;

    @ApiModelProperty(name = "zxpcjhs", value = "主修偏差计划数" ,notes = "2025-07-30 14:23:55")
    @Comment("主修偏差计划数")
    @Column(value = "zxpcjhs", width = 128)
    @ColumnDefine(columnXtype="text",tableJsonparam="{\"drilldownClickable\":true,\"drilldownParamValue\":\"1\",\"drilldownParamName\":\"xdlx\"}")
    private String zxpcjhs;

    @ApiModelProperty(name = "fxpcjhs", value = "辅修偏差计划数" ,notes = "2025-07-30 14:23:55")
    @Comment("辅修偏差计划数")
    @Column(value = "fxpcjhs", width = 128)
    @ColumnDefine(columnXtype="text",tableJsonparam="{\"drilldownClickable\":true,\"drilldownParamValue\":\"2\",\"drilldownParamName\":\"xdlx\"}")
    private String fxpcjhs;

    @ApiModelProperty(name = "fapcjhzs", value = "方案偏差计划总数" ,notes = "2025-07-30 14:23:55")
    @Comment("方案偏差计划总数")
    @Column(value = "fapcjhzs", width = 128)
    @ColumnDefine(columnXtype="text",tableJsonparam="{\"drilldownClickable\":true}")
    private String fapcjhzs;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "xnxqName", value = "学年学期名称")
    @Translate(cacheName = "biz_school_term", keyField = "xnxq", cacheType = "", split=",")
    @JSONField(name = "xnxq"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String xnxqName;

	//endregion end_dynamic_dict_column

}
