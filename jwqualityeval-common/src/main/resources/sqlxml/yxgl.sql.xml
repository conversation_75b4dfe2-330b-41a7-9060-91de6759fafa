<?xml version="1.0" encoding="utf-8"?>
<sqltoy xmlns="http://www.sagframe.com/schema/sqltoy"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.sagframe.com/schema/sqltoy http://www.sagframe.com/schema/sqltoy/sqltoy.xsd">
    <!--教学计划与方案偏差计划列表-->
    <sql id="page_jxjhyfapcjhlbbo_jxjhyfapcjhlbbo">
        <value>
            <![CDATA[
                SELECT * FROM (
                  WITH SJ_DATA AS (SELECT TJXQ, XQBM, XDLX, PCJHGS AS SZ
                                 FROM JWYXZLSJJ_ADS_XXMXQGXDLXPCJHGS),
                         ZX_DATA AS (SELECT TJXQ, XQBM, XDLX, SZ
                                     FROM SJ_DATA
                                     WHERE XDLX = '主修'),
                         FX_DATA AS (SELECT TJXQ, XQBM, XDLX, SZ
                                     FROM SJ_DATA
                                     WHERE XDLX = '辅修'),
                         XNXQ AS (SELECT DISTINCT TJXQ, XQBM, XNXQPX
                                  FROM JWYXZLSJJ_ADS_XXMXQGXDLXPCJHGS
                                           LEFT JOIN V_INS_XNXQ ON JWYXZLSJJ_ADS_XXMXQGXDLXPCJHGS.XQBM = V_INS_XNXQ.XNXQBM)
                    SELECT XNXQ.XNXQPX || '-' ||   XNXQ.XQBM       AS ID,
                           XNXQ.XQBM                               AS XNXQ,
                           NVL(ZX_DATA.SZ, 0)                      AS ZXPCJHS,
                           NVL(FX_DATA.SZ, 0)                      AS FXPCJHS,
                           NVL(ZX_DATA.SZ, 0) + NVL(FX_DATA.SZ, 0) AS FAPCJHZS
                    FROM XNXQ
                             LEFT JOIN ZX_DATA ON XNXQ.XQBM = ZX_DATA.XQBM
                             LEFT JOIN FX_DATA ON XNXQ.XQBM = FX_DATA.XQBM
                ) ABD_SCH_ZZJGXX
                WHERE 1 = 1
                #datascope#
                #[ AND @blank(:querySetting) (${querySetting})]
                ORDER BY #[@blank(:orderBy) @value(:orderBy), ] ID
            ]]>
        </value>
    </sql>

    <!--计划偏差信息列表-->
    <sql id="page_jhpcxxlbbo_jhpcxxlbbo" masterTableAlias="ABD_SCH_ZZJGXX">
        <value>
            <![CDATA[
                SELECT * FROM (
                  SELECT JWYXZLSJJ_ADS_JXJHKCXX.ID,
                           JWYXZLSJJ_ADS_JXJHKCXX.KCBH  AS KCH,
                           JWYXZLSJJ_ADS_KC.KCMC,
                           JWYXZLSJJ_ADS_PYFA.PYFABH,
                           JWYXZLSJJ_ADS_PYFA.PYFAMC,
                           JWYXZLSJJ_ADS_PYFA.XDLXDM    AS XDLX,
                           BGLXDM                       AS BGLX,
                           BGXX,
                           CDDWBH                       AS CDDW,
                           CDDWBH                       AS JGDM,
                           JWYXZLSJJ_ADS_JXJHKCXX.JYSBH AS JYS,
                           JWYXZLSJJ_ADS_JXJHKCXX.ZXS,
                           XNXQ,
                           YJHXNXQ,
                           TZZXNXQ
                    FROM JWYXZLSJJ_ADS_JXJHKCXX
                             LEFT JOIN JWYXZLSJJ_ADS_PYFA ON JWYXZLSJJ_ADS_JXJHKCXX.PYFABH = JWYXZLSJJ_ADS_PYFA.PYFABH AND
                                                             JWYXZLSJJ_ADS_PYFA.WISEDU_ZIP_ENDTIME > SYSDATE
                             LEFT JOIN JWYXZLSJJ_ADS_KC ON JWYXZLSJJ_ADS_JXJHKCXX.KCBH = JWYXZLSJJ_ADS_KC.KCH AND
                                                           JWYXZLSJJ_ADS_KC.WISEDU_ZIP_ENDTIME > SYSDATE
                ) ABD_SCH_ZZJGXX
                WHERE 1 = 1
                #datascope#
                #[ AND @blank(:querySetting) (${querySetting})]
                ORDER BY #[@blank(:orderBy) @value(:orderBy), ] PYFABH, CDDW, KCH, BGLX
            ]]>
        </value>
    </sql>

    <!--学院与方案偏差计划数列表-->
    <sql id="page_xyyfapcjhslbbo_xyyfapcjhslbbo" masterTableAlias="ABD_SCH_ZZJGXX">
        <value>
            <![CDATA[
                SELECT * FROM (
                  WITH FA_DATA AS (SELECT XYJC AS FL, XYBH, XQBM, SUM(JHGS) AS SZ
                                     FROM JWYXZLSJJ_ADS_XYMXQFAJHGS
                                     GROUP BY XYJC, XYBH, XQBM),
                         PC_DATA AS (SELECT XYJC AS FL, XYBH, XQBM, SUM(PCJHGS) AS SZ
                                     FROM JWYXZLSJJ_ADS_XYMXQGXDLXPCJHGS
                                     GROUP BY XYJC, XYBH, XQBM)
                    SELECT FA_DATA.XQBM || '-' || FA_DATA.XYBH                                      AS ID,
                           FA_DATA.XYBH                                                             AS XY,
                           FA_DATA.XYBH                                                             AS JGDM,
                           FA_DATA.XQBM                                                             AS XNXQ,
                           NVL(PC_DATA.SZ, 0)                                                       AS FAPCJHZS,
                           CASE WHEN FA_DATA.SZ > 0 THEN ROUND(NVL(PC_DATA.SZ, 0) / FA_DATA.SZ * 100, 2) ELSE 0 END || '%' AS FAPCJHBL
                    FROM FA_DATA
                             LEFT JOIN PC_DATA ON FA_DATA.XYBH = PC_DATA.XYBH
                    ORDER BY FA_DATA.XYBH
                ) ABD_SCH_ZZJGXX
                WHERE 1 = 1
                #datascope#
                #[ AND @blank(:querySetting) (${querySetting})]
                ORDER BY #[@blank(:orderBy) @value(:orderBy), ] ID
            ]]>
        </value>
    </sql>

</sqltoy>
