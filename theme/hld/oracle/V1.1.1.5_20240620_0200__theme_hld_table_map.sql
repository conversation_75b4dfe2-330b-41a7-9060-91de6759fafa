/*
 Description		: [高水平学科发展(HLD)]主题域报表表名对照表
 Author				: dataapp
 Date				: 2024-06-20 01:00:00
 Lowcode Version	: V1.1.1_Beta5
 Database           : oracle
*/

BEGIN
    EXECUTE IMMEDIATE 'CREATE TABLE t_table_map (
  id VARCHAR2(100 BYTE),
  table_name VARCHAR2(200 BYTE),
  comments VARCHAR2(500 BYTE),
  sql_str CLOB,
  create_by VARCHAR2(100),
  create_time DATE,
  update_by VARCHAR2(100),
  update_time DATE,PRIMARY KEY (id))';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

COMMENT ON TABLE t_table_map IS '报表模型表名映射';
COMMENT ON COLUMN t_table_map.id IS 'ID';
COMMENT ON COLUMN t_table_map.table_name IS '表名';
COMMENT ON COLUMN t_table_map.comments IS '表注释';
COMMENT ON COLUMN t_table_map.sql_str IS 'sql脚本';
COMMENT ON COLUMN t_table_map.create_by IS '创建人';
COMMENT ON COLUMN t_table_map.create_time IS '创建时间';
COMMENT ON COLUMN t_table_map.update_by IS '更新人';
COMMENT ON COLUMN t_table_map.update_time IS '更新时间';

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xxjbxx', 'abd_sch_xxjbxx', '学校基本信息', TO_CLOB('
select
学校基本信息.xxbsm as 学校标识码,
学校基本信息.xxmc as 学校名称,
学校基本信息.xxywmc as 学校英文名称,
学校基本信息.xxdz as 学校地址,
学校基本信息.xxyzbm as 学校邮政编码,
学校基本信息.xzqh as 行政区划,
学校基本信息.szdcxlx as 所在地城乡类型,
学校基本信息.jxny as 建校年月,
学校基本信息.xqr as 校庆日,
学校基本信息.xxbxlx as 学校办学类型,
学校基本信息.xxjbz as 学校举办者,
学校基本信息.xxzgbm as 学校主管部门,
学校基本信息.fddbrh as 法定代表人号,
学校基本信息.frzsh as 法人证书号,
学校基本信息.xzxm as 校长姓名,
学校基本信息.dwfzr as 党委负责人,
学校基本信息.zzjgm as 组织机构码,
学校基本信息.lxdh as 联系电话,
学校基本信息.czdh as 传真电话,
学校基本信息.dzxx as 电子信箱,
学校基本信息.xxbb as 学校办别,
学校基本信息.xxxz as 学校性质
from abd_sch_xxjbxx 学校基本信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xxjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xxjgxx', 'abd_sch_xxjgxx', '学校机构信息', TO_CLOB('
select
学校机构信息.xxdm as 学校代码,
学校机构信息.jgbm as 机构编码,
学校机构信息.jgmc as 机构名称,
学校机构信息.jglb as 机构类别,
学校机构信息.jlny as 建立年月,
学校机构信息.jgjb as 机构级别
from abd_sch_xxjgxx 学校机构信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xxjgxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xkpgpm', 'abd_sch_xkpgpm', '学科评估排名', TO_CLOB('
select
学科评估排名.yjxkdm as 一级学科代码,
学科评估排名.xkpgmc as 学科评估名称,
学科评估排名.pgjg as 评估结果,
学科评估排名.pgrq as 评估日期
from abd_sch_xkpgpm 学科评估排名'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xkpgpm'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_sdgjxkpm', 'abd_sch_sdgjxkpm', '四大国际学科排名', TO_CLOB('
select
四大国际学科排名.xxdm as 学校代码,
四大国际学科排名.xkmc as 学科名称,
四大国际学科排名.pmlx as 排名类型,
四大国际学科排名.pm as 排名,
四大国际学科排名.pgnd as 评估年度
from abd_sch_sdgjxkpm 四大国际学科排名'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_sdgjxkpm'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_syljsjftj', 'abd_sch_syljsjftj', '双一流建设经费统计', TO_CLOB('
select
双一流建设经费统计.xxdm as 学校代码,
双一流建设经费统计.tjxm as 统计项目,
双一流建设经费统计.hjys as 合计预算,
双一流建设经费统计.hjzc as 合计支出,
双一流建设经费统计.zyzxys as 中央专项预算,
双一流建设经费统计.zyzxzc as 中央专项支出,
双一流建设经费统计.dfzxys as 地方专项预算,
双一流建设经费统计.dfzxzc as 地方专项支出,
双一流建设经费统计.qtczys as 其他财政预算,
双一流建设经费统计.qtczzc as 其他财政实际,
双一流建设经费统计.shtrys as 社会投入预算,
双一流建设经费统计.shtrzc as 社会投入支出,
双一流建设经费统计.xxzyys as 学校自有预算,
双一流建设经费统计.xxzyzc as 学校自有支出,
双一流建设经费统计.tjnd as 统计年度
from abd_sch_syljsjftj 双一流建设经费统计'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_syljsjftj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_zkjsfzqktj', 'abd_sch_zkjsfzqktj', '智库建设发展情况统计', TO_CLOB('
select
智库建设发展情况统计.xxdm as 学校代码,
智库建设发展情况统计.tjnd as 统计年度,
智库建设发展情况统计.zkmc as 智库名称,
智库建设发展情况统计.zxzzbgs as 撰写资政报告数,
智库建设发展情况统计.zxndbgs as 撰写年度报告数,
智库建设发展情况统计.cbyjzzs as 出版研究专著数,
智库建设发展情况统计.fbqklws as 发表期刊论文数,
智库建设发展情况统计.zxnbcks as 撰写内部参考数
from abd_sch_zkjsfzqktj 智库建设发展情况统计'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_zkjsfzqktj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_cygjkxgcjh', 'abd_sch_cygjkxgcjh', '参与国际科学工程计划', TO_CLOB('
select
参与国际科学工程计划.xxdm as 学校代码,
参与国际科学工程计划.mc as 名称,
参与国际科学工程计划.zyhzzzmc as 主要合作组织名称,
参与国际科学工程计划.hzlx as 合作类型,
参与国际科学工程计划.cylx as 参与类型,
参与国际科学工程计划.hzjf as 合作经费,
参与国际科学工程计划.jfly as 经费来源,
参与国际科学工程计划.cysj as 参与时间,
参与国际科学工程计划.zycg as 主要成果
from abd_sch_cygjkxgcjh 参与国际科学工程计划'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_cygjkxgcjh'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_whccjdqktj', 'abd_sch_whccjdqktj', '文化传承基地情况统计', TO_CLOB('
select
文化传承基地情况统计.xxdm as 学校代码,
文化传承基地情况统计.jdmc as 基地名称,
文化传承基地情况统计.jscg as 建设成果,
文化传承基地情况统计.hprq as 获批日期
from abd_sch_whccjdqktj 文化传承基地情况统计'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_whccjdqktj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_bwgysgqktj', 'abd_sch_bwgysgqktj', '博物馆艺术馆情况统计', TO_CLOB('
select
博物馆艺术馆情况统计.xxdm as 学校代码,
博物馆艺术馆情况统计.mc as 名称,
博物馆艺术馆情况统计.lx as 类型,
博物馆艺术馆情况统计.kfxs as 开放形式,
博物馆艺术馆情况统计.mzkfxs as 每周开放小时,
博物馆艺术馆情况统计.mncfrc as 每年参访人次,
博物馆艺术馆情况统计.tjnd as 统计年度
from abd_sch_bwgysgqktj 博物馆艺术馆情况统计'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_bwgysgqktj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xtsjyrxmtj', 'abd_sch_xtsjyrxmtj', '协同实践育人项目统计', TO_CLOB('
select
协同实践育人项目统计.xxdm as 学校代码,
协同实践育人项目统计.xmmc as 项目名称,
协同实践育人项目统计.xmlx as 项目类型,
协同实践育人项目统计.hzdwmc as 合作单位名称,
协同实践育人项目统计.cyxsrs as 参与学生人数,
协同实践育人项目统计.cyzy as 参与专业,
协同实践育人项目统计.tjnd as 统计年度
from abd_sch_xtsjyrxmtj 协同实践育人项目统计'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xtsjyrxmtj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_gjjxrcpyxmtj', 'abd_sch_gjjxrcpyxmtj', '国家急需人才培养项目统计', TO_CLOB('
select
国家急需人才培养项目统计.xxdm as 学校代码,
国家急需人才培养项目统计.xmmc as 项目名称,
国家急需人才培养项目统计.xmlx as 项目类型,
国家急需人才培养项目统计.lxrq as 立项日期,
国家急需人才培养项目统计.fwlyjsscx as 服务领域及实施成效,
国家急需人才培养项目统计.bkzss as 本科生招生人数,
国家急需人才培养项目统计.sszss as 硕士生招生人数,
国家急需人才培养项目统计.bszss as 博士生招生人数,
国家急需人才培养项目统计.tjnd as 统计年度
from abd_sch_gjjxrcpyxmtj 国家急需人才培养项目统计'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_gjjxrcpyxmtj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_cjrhyfjg', 'abd_sch_cjrhyfjg', '产教融合研发机构', TO_CLOB('
select
产教融合研发机构.xxdm as 学校代码,
产教融合研发机构.jgmc as 机构名称,
产教融合研发机构.hzqy as 合作企业,
产教融合研发机构.qytrjf as 企业投入经费,
产教融合研发机构.clrq as 成立日期,
产教融合研发机构.cdxmqk as 承担项目情况,
产教融合研发机构.gjhzqk as 国际合作情况,
产教融合研发机构.yxqk as 运行情况,
产教融合研发机构.tjnd as 统计年度
from abd_sch_cjrhyfjg 产教融合研发机构'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_cjrhyfjg'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_cjxsqk', 'abd_sch_cjxsqk', '创建学术期刊', TO_CLOB('
select
创建学术期刊.xxdm as 学校代码,
创建学术期刊.qkmc as 期刊名称,
创建学术期刊.ih as ISSN号,
创建学术期刊.ch as CN号,
创建学术期刊.qkslqk as 期刊收录情况,
创建学术期刊.ckrq as 创刊日期,
创建学术期刊.yjxkdm as 一级学科代码
from abd_sch_cjxsqk 创建学术期刊'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_cjxsqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_fjwbxjgxm', 'abd_sch_fjwbxjgxm', '赴境外办学机构项目', TO_CLOB('
select
赴境外办学机构项目.xxdm as 学校代码,
赴境外办学机构项目.jgmc as 机构名称,
赴境外办学机构项目.bxlx as 办学类型,
赴境外办学机构项目.hzjgmc as 合作机构名称,
赴境外办学机构项目.trlx as 投入类型,
赴境外办学机构项目.xlcc as 学历层次,
赴境外办学机构项目.xwcc as 学位层次,
赴境外办学机构项目.dnzss as 当年招生人数,
赴境外办学机构项目.jwzb as 境外公民占比,
赴境外办学机构项目.tjnd as 统计年度
from abd_sch_fjwbxjgxm 赴境外办学机构项目'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_fjwbxjgxm'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_exkpm', 'abd_sch_exkpm', 'ESI学科排名', TO_CLOB('
select
ESI学科排名.xxdm as 学校代码,
ESI学科排名.xkmc as 学科名称,
ESI学科排名.pm as 排名,
ESI学科排名.pgny as 评估年月
from abd_sch_exkpm ESI学科排名'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_exkpm'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_xkxx', 'abd_sub_xkxx', '学科信息', TO_CLOB('
select
学科信息.yjxkdm as 一级学科代码,
学科信息.yjxkmc as 一级学科名称,
学科信息.xkmldm as 学科门类代码,
学科信息.xkmlmc as 学科门类名称,
学科信息.xwlx as 学位类型,
学科信息.sqjb as 授权级别,
学科信息.sqsjssd as 授权时间硕士点,
学科信息.sqsjbsd as 授权时间博士点,
学科信息.kszsrq as 开始招生日期,
学科信息.szrq as 设站日期,
学科信息.zt as 状态,
学科信息.gbdm as 国标代码,
学科信息.xkmlfl as 学科门类分类,
学科信息.sfssd as 是否硕士点,
学科信息.sfbsd as 是否博士点,
学科信息.xkjc as 学科简称,
学科信息.px as 排序
from abd_sub_xkxx 学科信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_xkxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_bkszyxx', 'abd_sub_bkszyxx', '本科生专业信息', TO_CLOB('
select
本科生专业信息.yjxkdm as 一级学科代码,
本科生专业信息.zybm as 专业编码,
本科生专业信息.zymc as 专业名称,
本科生专业信息.ssxy as 所属学院,
本科生专业信息.gjbzzydm as 国家标准专业代码,
本科生专业信息.gjbzzymc as 国家标准专业名称,
本科生专业信息.xz as 学制,
本科生专业信息.jlny as 建立年月,
本科生专业信息.sfsy as 是否使用
from abd_sub_bkszyxx 本科生专业信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_bkszyxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_kyjgxx', 'abd_sub_kyjgxx', '科研机构信息', TO_CLOB('
select
科研机构信息.yjxkdm as 一级学科代码,
科研机构信息.kyjgbm as 科研机构编码,
科研机构信息.kyjgmc as 科研机构名称,
科研机构信息.kyjglx as 科研机构类型,
科研机构信息.kyjgjb as 科研机构级别,
科研机构信息.fzr as 负责人,
科研机构信息.ytdw as 依托单位,
科研机构信息.zgdw as 主管单位,
科研机构信息.gjdw as 共建单位,
科研机构信息.lxdh as 联系电话,
科研机构信息.clrq as 成立日期,
科研机构信息.sfcx as 是否撤销,
科研机构信息.drcpydzczy as 对人才培养的支撑作用,
科研机构信息.kyjgjbfl as 科研机构级别分类,
科研机构信息.zlgn as 战略概念
from abd_sub_kyjgxx 科研机构信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_kyjgxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_zdxkjsqk', 'abd_sub_zdxkjsqk', '重点学科建设情况', TO_CLOB('
select
重点学科建设情况.yjxkdm as 一级学科代码,
重点学科建设情况.zdjslx as 重点建设类型,
重点学科建设情况.rxrq as 入选日期
from abd_sub_zdxkjsqk 重点学科建设情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_zdxkjsqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_xkjsjftj', 'abd_sub_xkjsjftj', '学科建设经费统计', TO_CLOB('
select
学科建设经费统计.tjxm as 统计项目,
学科建设经费统计.hjys as 合计预算,
学科建设经费统计.hjzc as 合计支出,
学科建设经费统计.zyzxys as 中央专项预算,
学科建设经费统计.zyzxzc as 中央专项支出,
学科建设经费统计.dfzxys as 地方专项预算,
学科建设经费统计.dfzxzc as 地方专项支出,
学科建设经费统计.qtczys as 其他财政预算,
学科建设经费统计.qtczzc as 其他财政实际,
学科建设经费统计.shtrys as 社会投入预算,
学科建设经费统计.shtrzc as 社会投入支出,
学科建设经费统计.xxzyys as 学校自有预算,
学科建设经费统计.xxzyzc as 学校自有支出,
学科建设经费统计.tjnd as 统计年度,
学科建设经费统计.yjxkdm as 一级学科代码
from abd_sub_xkjsjftj 学科建设经费统计'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_xkjsjftj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_jzgxx', 'abd_sub_jzgxx', '教职工信息', TO_CLOB('
select
教职工信息.zgh as 教职工号,
教职工信息.xm as 姓名,
教职工信息.gjdq as 国家地区,
教职工信息.zzmm as 政治面貌,
教职工信息.rzsj as 入职时间,
教职工信息.lxsj as 离校时间,
教职工信息.csrq as 出生日期,
教职工信息.ssjg as 所属机构,
教职工信息.jzglb as 教职工类别,
教职工信息.yjfx as 研究方向,
教职工信息.jzgdqzt as 教职工当前状态,
教职工信息.dslb as 导师类别,
教职工信息.zgxl as 最高学历,
教职工信息.zgxw as 最高学位,
教职工信息.zhbyyx as 最后毕业院校,
教职工信息.hwjl as 海外经历,
教职工信息.zyjszw as 专业技术职务,
教职工信息.zyjszwjb as 专业技术职务级别,
教职工信息.wjjsgzlx as 外籍教师工作类型,
教职工信息.yjxkdm as 一级学科代码
from abd_sub_jzgxx 教职工信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_jzgxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_gjrchx', 'abd_sub_gjrchx', '高级人才获选', TO_CLOB('
select
高级人才获选.zgh as 教职工号,
高级人才获选.rclb as 人才类别,
高级人才获选.pxdw as 评选单位,
高级人才获选.pxdwjb as 评选单位级别,
高级人才获选.pxrq as 获选日期,
高级人才获选.yjxkdm as 一级学科代码,
高级人才获选.wc as 位次
from abd_sub_gjrchx 高级人才获选'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_gjrchx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_jshjljry', 'abd_sub_jshjljry', '教师获奖励及荣誉', TO_CLOB('
select
教师获奖励及荣誉.zgh as 教职工号,
教师获奖励及荣誉.jlmc as 奖励名称,
教师获奖励及荣誉.rychmc as 荣誉称号名称,
教师获奖励及荣誉.jljb as 奖励级别,
教师获奖励及荣誉.jllb as 奖励类别,
教师获奖励及荣誉.bjdwlx as 颁奖单位类型,
教师获奖励及荣誉.bjdw as 颁奖单位,
教师获奖励及荣誉.jlfs as 奖励方式,
教师获奖励及荣誉.hjxm as 获奖项目,
教师获奖励及荣誉.hjrq as 获奖日期,
教师获奖励及荣誉.jldj as 奖励等级,
教师获奖励及荣誉.smsx as 署名顺序,
教师获奖励及荣誉.hjzsbh as 获奖证书编号,
教师获奖励及荣誉.bdwpm as 本单位排名,
教师获奖励及荣誉.yjxkdm as 一级学科代码
from abd_sub_jshjljry 教师获奖励及荣誉'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_jshjljry'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_jsxsqkrz', 'abd_sub_jsxsqkrz', '教师学术期刊任职', TO_CLOB('
select
教师学术期刊任职.zgh as 教职工号,
教师学术期刊任职.kwmc as 刊物名称,
教师学术期刊任职.gjdq as 国家地区,
教师学术期刊任职.qkslqk as 期刊收录情况,
教师学术期刊任职.ih as ISSN号,
教师学术期刊任职.ch as CN号,
教师学术期刊任职.rzzw as 任职职务,
教师学术期刊任职.rzqsrq as 任职起始日期,
教师学术期刊任职.rzjsrq as 任职结束日期,
教师学术期刊任职.yjxkdm as 一级学科代码
from abd_sub_jsxsqkrz 教师学术期刊任职'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_jsxsqkrz'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_jsxsttrz', 'abd_sub_jsxsttrz', '教师学术团体任职', TO_CLOB('
select
教师学术团体任职.zgh as 教职工号,
教师学术团体任职.xsttmc as 学术团体名称,
教师学术团体任职.xsttjb as 学术团体级别,
教师学术团体任职.rzzw as 任职职务,
教师学术团体任职.lshzgdw as 隶属或主管单位,
教师学术团体任职.rzksrq as 任职开始日期,
教师学术团体任职.rzjsrq as 任职结束日期,
教师学术团体任职.yjxkdm as 一级学科代码
from abd_sub_jsxsttrz 教师学术团体任职'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_jsxsttrz'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_jszdbsrz', 'abd_sub_jszdbsrz', '教师重大比赛任职', TO_CLOB('
select
教师重大比赛任职.zgh as 教职工号,
教师重大比赛任职.bsmc as 比赛名称,
教师重大比赛任职.bslx as 比赛类型,
教师重大比赛任职.bsjb as 比赛级别,
教师重大比赛任职.rzzw as 任职职务,
教师重大比赛任职.bsqsrq as 比赛起始日期,
教师重大比赛任职.bsjsrq as 比赛结束日期,
教师重大比赛任职.yjxkdm as 一级学科代码
from abd_sub_jszdbsrz 教师重大比赛任职'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_jszdbsrz'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_fdyrzjl', 'abd_sub_fdyrzjl', '辅导员任职记录', TO_CLOB('
select
辅导员任职记录.zgh as 教职工号,
辅导员任职记录.rzlx as 任职类型,
辅导员任职记录.rzksrq as 任职开始日期,
辅导员任职记录.rzjsrq as 任职结束日期,
辅导员任职记录.yjxkdm as 一级学科代码
from abd_sub_fdyrzjl 辅导员任职记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_fdyrzjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_bshrzjl', 'abd_sub_bshrzjl', '博士后任职记录', TO_CLOB('
select
博士后任职记录.zgh as 教职工号,
博士后任职记录.ldzmc as 流动站名称,
博士后任职记录.dqzt as 当前状态,
博士后任职记录.jzrq as 进站日期,
博士后任职记录.tzrq as 退站日期,
博士后任职记录.yjxkdm as 一级学科代码
from abd_sub_bshrzjl 博士后任职记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_bshrzjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_kyzlprjl', 'abd_sub_kyzlprjl', '科研助理聘任记录', TO_CLOB('
select
科研助理聘任记录.zgh as 教职工号,
科研助理聘任记录.prlx as 聘任类型,
科研助理聘任记录.rzksrq as 任职开始日期,
科研助理聘任记录.rzjsrq as 任职结束日期,
科研助理聘任记录.yjxkdm as 一级学科代码
from abd_sub_kyzlprjl 科研助理聘任记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_kyzlprjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_xkfxxx', 'abd_sub_xkfxxx', '学科方向信息', TO_CLOB('
select
学科方向信息.zgh as 教职工号,
学科方向信息.xkfx as 学科方向,
学科方向信息.ryxz as 人员性质,
学科方向信息.fxid as 方向ID,
学科方向信息.cgjj as 成果简介,
学科方向信息.yjxkdm as 一级学科代码,
学科方向信息.ksrq as 开设日期,
学科方向信息.sfyx as 是否有效
from abd_sub_xkfxxx 学科方向信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_xkfxxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_xsxx', 'abd_sub_xsxx', '学生信息', TO_CLOB('
select
学生信息.xh as 学号,
学生信息.xm as 姓名,
学生信息.xslb as 学生类别,
学生信息.gjdq as 国家地区,
学生信息.xybm as 学院编码,
学生信息.xymc as 学院名称,
学生信息.xnzybm as 校内专业编码,
学生信息.xnzymc as 校内专业名称,
学生信息.nj as 年级,
学生信息.bjmc as 班级名称,
学生信息.xwlb as 学位类别,
学生信息.pycc as 培养层次,
学生信息.pyfs as 培养方式,
学生信息.xsdqzt as 学生当前状态,
学生信息.yjxkdm as 一级学科代码,
学生信息.rxsj as 入学时间,
学生信息.swsj as 授位时间
from abd_sub_xsxx 学生信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_xsxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_tcgxz', 'abd_sub_tcgxz', '突出贡献者', TO_CLOB('
select
突出贡献者.xh as 学号,
突出贡献者.xm as 姓名,
突出贡献者.bynf as 毕业年份,
突出贡献者.tcgxxm as 突出贡献项目,
突出贡献者.yjxkdm as 一级学科代码
from abd_sub_tcgxz 突出贡献者'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_tcgxz'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_xsjyxx', 'abd_sub_xsjyxx', '学生就业信息', TO_CLOB('
select
学生就业信息.xh as 学号,
学生就业信息.xm as 姓名,
学生就业信息.byqx as 毕业去向,
学生就业信息.dwszd as 单位所在地,
学生就业信息.jyxy as 就业行业,
学生就业信息.dwmc as 单位名称,
学生就业信息.jydwxz as 就业单位性质,
学生就业信息.jyzw as 就业职位,
学生就业信息.byrq as 毕业日期,
学生就业信息.yjxkdm as 一级学科代码
from abd_sub_xsjyxx 学生就业信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_xsjyxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_jshjxcgj', 'abd_sub_jshjxcgj', '教师获教学成果奖', TO_CLOB('
select
教师获教学成果奖.cgmc as 成果名称,
教师获教学成果奖.jxmc as 奖项名称,
教师获教学成果奖.wcr as 完成人,
教师获教学成果奖.wcdw as 完成单位,
教师获教学成果奖.bdwpm as 本单位排名,
教师获教学成果奖.hjlx as 获奖类型,
教师获教学成果奖.hjdj as 获奖等级,
教师获教学成果奖.hjrq as 获奖日期,
教师获教学成果奖.fwh as 发文号,
教师获教学成果奖.yjxkdm as 一级学科代码,
教师获教学成果奖.gxd as 贡献度
from abd_sub_jshjxcgj 教师获教学成果奖'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_jshjxcgj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_xsjshj', 'abd_sub_xsjshj', '学生竞赛获奖', TO_CLOB('
select
学生竞赛获奖.jxmc as 奖项名称,
学生竞赛获奖.hjzp as 获奖作品,
学生竞赛获奖.hjdj as 获奖等级,
学生竞赛获奖.hjrq as 获奖日期,
学生竞赛获奖.zzdwmc as 组织单位名称,
学生竞赛获奖.zzdwlx as 组织单位类型,
学生竞赛获奖.xh as 学号,
学生竞赛获奖.hjxsxm as 获奖学生姓名,
学生竞赛获奖.hjxslx as 获奖学生类型,
学生竞赛获奖.yjxkdm as 一级学科代码
from abd_sub_xsjshj 学生竞赛获奖'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_xsjshj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_zyrzxx', 'abd_sub_zyrzxx', '专业认证信息', TO_CLOB('
select
专业认证信息.zymc as 专业名称,
专业认证信息.rzjg as 认证机构,
专业认证信息.rzlx as 认证类型,
专业认证信息.rzjb as 认证级别,
专业认证信息.sfsl as 是否受理,
专业认证信息.rzjl as 认证结论,
专业认证信息.rzrq as 认证日期,
专业认证信息.yxjzrq as 有效截至日期,
专业认证信息.yjxkdm as 一级学科代码
from abd_sub_zyrzxx 专业认证信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_zyrzxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_rxylzy', 'abd_sub_rxylzy', '入选一流专业', TO_CLOB('
select
入选一流专业.zymc as 专业名称,
入选一流专业.ylzyjb as 一流专业级别,
入选一流专业.fwh as 发文号,
入选一流专业.fwrq as 发文日期,
入选一流专业.yjxkdm as 一级学科代码,
入选一流专业.yjxkmc as 一级学科名称
from abd_sub_rxylzy 入选一流专业'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_rxylzy'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_rxylkc', 'abd_sub_rxylkc', '入选一流课程', TO_CLOB('
select
入选一流课程.kcmc as 课程名称,
入选一流课程.ylzylx as 一流课程类型,
入选一流课程.ylkcjb as 一流课程级别,
入选一流课程.fwh as 发文号,
入选一流课程.fwrq as 发文日期,
入选一流课程.yjxkdm as 一级学科代码,
入选一流课程.gxd as 贡献度
from abd_sub_rxylkc 入选一流课程'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_rxylkc'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_bksskjl', 'abd_sub_bksskjl', '本科生授课记录', TO_CLOB('
select
本科生授课记录.zgh as 教职工号,
本科生授课记录.sfzj as 是否主讲,
本科生授课记录.jxbbm as 教学班编码,
本科生授课记录.jxbmc as 教学班名称,
本科生授课记录.xnxq as 学年学期,
本科生授课记录.xs as 学时,
本科生授课记录.rs as 人数,
本科生授课记录.ksskrq as 开始授课日期,
本科生授课记录.yjxkdm as 一级学科代码
from abd_sub_bksskjl 本科生授课记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_bksskjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_jscbjc', 'abd_sub_jscbjc', '教师出版教材', TO_CLOB('
select
教师出版教材.zgh as 教职工号,
教师出版教材.cdjs as 承担角色,
教师出版教材.smsx as 署名顺序,
教师出版教材.jcmc as 教材名称,
教师出版教材.bdwpm as 本单位排名,
教师出版教材.cdgz as 承担工作,
教师出版教材.jclx as 教材类型,
教师出版教材.cbrq as 出版日期,
教师出版教材.cbsmc as 出版社名称,
教师出版教材.bc as 版次,
教师出版教材.fxl as 发行量,
教师出版教材.yjxkdm as 一级学科代码
from abd_sub_jscbjc 教师出版教材'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_jscbjc'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_jsfblw', 'abd_sub_jsfblw', '教师发表论文', TO_CLOB('
select
教师发表论文.zgh as 教职工号,
教师发表论文.fbrq as 发表日期,
教师发表论文.cdjs as 承担角色,
教师发表论文.smsx as 署名顺序,
教师发表论文.lwbm as 论文编码,
教师发表论文.lwmc as 论文名称,
教师发表论文.lwlx as 论文类型,
教师发表论文.kwmc as 刊物名称,
教师发表论文.yz as 语种,
教师发表论文.ih as ISSN号,
教师发表论文.ch as CN号,
教师发表论文.qkslqk as 期刊收录情况,
教师发表论文.hymc as 会议名称,
教师发表论文.hydd as 会议地点,
教师发表论文.yjxkdm as 一级学科代码,
教师发表论文.lwfq as 论文分区
from abd_sub_jsfblw 教师发表论文'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_jsfblw'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_jscbzz', 'abd_sub_jscbzz', '教师出版著作', TO_CLOB('
select
教师出版著作.zgh as 教职工号,
教师出版著作.cdjs as 承担角色,
教师出版著作.smsx as 署名顺序,
教师出版著作.cdzs as 承担字数,
教师出版著作.zzbm as 著作编码,
教师出版著作.zzmc as 著作名称,
教师出版著作.zzlb as 著作类别,
教师出版著作.bdwpm as 本单位排名,
教师出版著作.cbsmc as 出版社名称,
教师出版著作.cbsjb as 出版社级别,
教师出版著作.ih as ISBN号,
教师出版著作.yz as 语种,
教师出版著作.cip as CIP号,
教师出版著作.bc as 版次,
教师出版著作.zzzs as 著作字数,
教师出版著作.cbrq as 出版日期,
教师出版著作.yjxkdm as 一级学科代码
from abd_sub_jscbzz 教师出版著作'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_jscbzz'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_jscykyxm', 'abd_sub_jscykyxm', '教师参与科研项目', TO_CLOB('
select
教师参与科研项目.zgh as 教职工号,
教师参与科研项目.cdjs as 承担角色,
教师参与科研项目.smsx as 署名顺序,
教师参与科研项目.gxd as 贡献度,
教师参与科研项目.xmbm as 项目编码,
教师参与科研项目.xmmc as 项目名称,
教师参与科研项目.xmlb as 项目类别,
教师参与科研项目.xmjb as 项目级别,
教师参与科研项目.xmly as 项目来源,
教师参与科研项目.xmlydw as 项目来源单位,
教师参与科研项目.xmzt as 项目状态,
教师参与科研项目.lxrq as 立项日期,
教师参与科研项目.jxrq as 结项日期,
教师参与科研项目.pzjf as 批准经费,
教师参与科研项目.zcpt as 支撑平台,
教师参与科研项目.yjxkdm as 一级学科代码,
教师参与科研项目.xmlx as 项目类型,
教师参与科研项目.xmlxfl as 项目类型分类,
教师参与科研项目.zlgn as 战略概念
from abd_sub_jscykyxm 教师参与科研项目'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_jscykyxm'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_jskycghj', 'abd_sub_jskycghj', '教师科研成果获奖', TO_CLOB('
select
教师科研成果获奖.zgh as 教职工号,
教师科研成果获奖.cgmc as 成果名称,
教师科研成果获奖.cglx as 成果类型,
教师科研成果获奖.jxlb as 奖项类别,
教师科研成果获奖.jxmc as 奖项名称,
教师科研成果获奖.jxjb as 奖项级别,
教师科研成果获奖.hjdj as 获奖等级,
教师科研成果获奖.bjdw as 颁奖单位,
教师科研成果获奖.bdwpm as 本单位排名,
教师科研成果获奖.smsx as 署名顺序,
教师科研成果获奖.gxd as 贡献度,
教师科研成果获奖.hjrq as 获奖日期,
教师科研成果获奖.yjxkdm as 一级学科代码,
教师科研成果获奖.kyhjjbfl as 科研获奖级别分类
from abd_sub_jskycghj 教师科研成果获奖'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_jskycghj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_jsfmzl', 'abd_sub_jsfmzl', '教师发明专利', TO_CLOB('
select
教师发明专利.zgh as 教职工号,
教师发明专利.cdjs as 承担角色,
教师发明专利.gxd as 贡献度,
教师发明专利.zlbm as 专利编码,
教师发明专利.zlmc as 专利名称,
教师发明专利.zlsqh as 专利申请号,
教师发明专利.zllx as 专利类型,
教师发明专利.zlflzt as 专利法律状态,
教师发明专利.zlqr as 专利权人,
教师发明专利.zlsqrq as 专利申请日期,
教师发明专利.sqggrq as 授权公告日期,
教师发明专利.yjxkdm as 一级学科代码
from abd_sub_jsfmzl 教师发明专利'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_jsfmzl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_jszdbz', 'abd_sub_jszdbz', '教师制定标准', TO_CLOB('
select
教师制定标准.zdr as 制定人,
教师制定标准.cdjs as 承担角色,
教师制定标准.gxd as 贡献度,
教师制定标准.bzbm as 标准编码,
教师制定标准.bzmc as 标准名称,
教师制定标准.bzh as 标准号,
教师制定标准.bzlb as 标准类别,
教师制定标准.bzjb as 标准级别,
教师制定标准.bzzt as 标准状态,
教师制定标准.cylx as 参与类型,
教师制定标准.fbrq as 发布日期,
教师制定标准.fbdw as 发布单位,
教师制定标准.bdwpm as 本单位排名,
教师制定标准.yjxkdm as 一级学科代码
from abd_sub_jszdbz 教师制定标准'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_jszdbz'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_kyjfdz', 'abd_sub_kyjfdz', '科研经费到帐', TO_CLOB('
select
科研经费到帐.dzjf as 到账经费,
科研经费到帐.dzxm as 到账项目,
科研经费到帐.dzrq as 到账日期,
科研经费到帐.tjnd as 统计年度,
科研经费到帐.yjxkdm as 一级学科代码,
科研经费到帐.xmlb as 项目类别
from abd_sub_kyjfdz 科研经费到帐'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_kyjfdz'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_kycgzh', 'abd_sub_kycgzh', '科研成果转化', TO_CLOB('
select
科研成果转化.htje as 合同金额,
科研成果转化.cgmc as 成果名称,
科研成果转化.htmc as 合同名称,
科研成果转化.zhfs as 转化方式,
科研成果转化.srdw as 受让单位,
科研成果转化.htbh as 合同编号,
科研成果转化.qyrq as 签约日期,
科研成果转化.yjxkdm as 一级学科代码
from abd_sub_kycgzh 科研成果转化'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_kycgzh'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_cdgnwsjyzy', 'abd_sub_cdgnwsjyzy', '承担国内外设计与展演', TO_CLOB('
select
承担国内外设计与展演.yjxkdm as 一级学科代码,
承担国内外设计与展演.xm as 姓名,
承担国内外设计与展演.zymc as 国内外重大设计展演名称,
承担国内外设计与展演.cysj as 参与时间,
承担国内外设计与展演.cdrw as 承担任务
from abd_sub_cdgnwsjyzy 承担国内外设计与展演'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_cdgnwsjyzy'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_hshjztj', 'abd_sub_hshjztj', '获社会捐赠统计', TO_CLOB('
select
获社会捐赠统计.xxdm as 学校代码,
获社会捐赠统计.tjnd as 统计年度,
获社会捐赠统计.hjje as 获社会捐赠金额,
获社会捐赠统计.pbje as 财政配比资金金额
from abd_sub_hshjztj 获社会捐赠统计'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_hshjztj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_xslxjl', 'abd_sub_xslxjl', '学生留学记录', TO_CLOB('
select
学生留学记录.xh as 学号,
学生留学记录.xm as 姓名,
学生留学记录.lxxm as 留学项目,
学生留学记录.lxdw as 留学单位,
学生留学记录.lxsc as 留学时长,
学生留学记录.ksrq as 开始日期,
学生留学记录.jsrq as 结束日期,
学生留学记录.yjxkdm as 一级学科代码
from abd_sub_xslxjl 学生留学记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_xslxjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_jscjxshy', 'abd_sub_jscjxshy', '教师参加学术会议', TO_CLOB('
select
教师参加学术会议.zgh as 教职工号,
教师参加学术会议.hymc as 会议名称,
教师参加学术会议.bgtm as 报告题目,
教师参加学术会议.hydd as 会议地点,
教师参加学术会议.bgrq as 报告日期,
教师参加学术会议.yjxkdm as 一级学科代码
from abd_sub_jscjxshy 教师参加学术会议'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_jscjxshy'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_xscjxshy', 'abd_sub_xscjxshy', '学生参加学术会议', TO_CLOB('
select
学生参加学术会议.xh as 学号,
学生参加学术会议.pycc as 培养层次,
学生参加学术会议.hymc as 会议名称,
学生参加学术会议.bgtm as 报告题目,
学生参加学术会议.hydd as 会议地点,
学生参加学术会议.bgrq as 报告日期,
学生参加学术会议.yjxkdm as 一级学科代码
from abd_sub_xscjxshy 学生参加学术会议'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_xscjxshy'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_cygjxszzylm', 'abd_sub_cygjxszzylm', '参与国际学术组织与联盟', TO_CLOB('
select
参与国际学术组织与联盟.mc as 名称,
参与国际学术组织与联盟.cylx as 参与类型,
参与国际学术组织与联盟.cysj as 参与时间,
参与国际学术组织与联盟.yjxkdm as 一级学科代码
from abd_sub_cygjxszzylm 参与国际学术组织与联盟'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_cygjxszzylm'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0001', 'hld_si_sub_0001', '每年建设预算经费总数', TO_CLOB('
select
每年建设预算经费总数.xkbh as 学科编号,
每年建设预算经费总数.nd as 年度,
每年建设预算经费总数.jfzs as 经费总数
from hld_si_sub_0001 每年建设预算经费总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0001'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0002', 'hld_si_sub_0002', '每年建设经费支出总数', TO_CLOB('
select
每年建设经费支出总数.xkbh as 学科编号,
每年建设经费支出总数.nd as 年度,
每年建设经费支出总数.zczs as 支出总数
from hld_si_sub_0002 每年建设经费支出总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0002'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0003', 'hld_si_sub_0003', '每年不同项目类型预算经费', TO_CLOB('
select
每年不同项目类型预算经费.xkbh as 学科编号,
每年不同项目类型预算经费.nd as 年度,
每年不同项目类型预算经费.xmlx as 项目类型,
每年不同项目类型预算经费.ysjf as 预算经费
from hld_si_sub_0003 每年不同项目类型预算经费'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0003'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0004', 'hld_si_sub_0004', '每年不同项目类型实际经费投入', TO_CLOB('
select
每年不同项目类型实际经费投入.xkbh as 学科编号,
每年不同项目类型实际经费投入.nd as 年度,
每年不同项目类型实际经费投入.xmlx as 项目类型,
每年不同项目类型实际经费投入.trjf as 投入经费
from hld_si_sub_0004 每年不同项目类型实际经费投入'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0004'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0005', 'hld_si_sub_0005', '每年不同投入方对应预算经费总数', TO_CLOB('
select
每年不同投入方对应预算经费总数.xkbh as 学科编号,
每年不同投入方对应预算经费总数.nd as 年度,
每年不同投入方对应预算经费总数.trly as 投入来源,
每年不同投入方对应预算经费总数.ysjf as 预算经费
from hld_si_sub_0005 每年不同投入方对应预算经费总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0005'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0006', 'hld_si_sub_0006', '每年不同投入方对应实际支出总数', TO_CLOB('
select
每年不同投入方对应实际支出总数.xkbh as 学科编号,
每年不同投入方对应实际支出总数.nd as 年度,
每年不同投入方对应实际支出总数.trly as 投入来源,
每年不同投入方对应实际支出总数.trjf as 投入经费
from hld_si_sub_0006 每年不同投入方对应实际支出总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0006'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0007', 'hld_si_sub_0007', '每年招生人数', TO_CLOB('
select
每年招生人数.xkbh as 学科编号,
每年招生人数.nd as 年度,
每年招生人数.zss as 招生数
from hld_si_sub_0007 每年招生人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0007'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0008', 'hld_si_sub_0008', '每年授位人数', TO_CLOB('
select
每年授位人数.xkbh as 学科编号,
每年授位人数.nd as 年度,
每年授位人数.sws as 授位数
from hld_si_sub_0008 每年授位人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0008'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0009', 'hld_si_sub_0009', '每年国家级教学成果奖数', TO_CLOB('
select
每年国家级教学成果奖数.xkbh as 学科编号,
每年国家级教学成果奖数.nd as 年度,
每年国家级教学成果奖数.hjs as 获奖数
from hld_si_sub_0009 每年国家级教学成果奖数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0009'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0010', 'hld_si_sub_0010', '每年新增国家级教学成果奖特等奖数', TO_CLOB('
select
每年新增国家级教学成果奖特等奖数.xkbh as 学科编号,
每年新增国家级教学成果奖特等奖数.nd as 年度,
每年新增国家级教学成果奖特等奖数.hjs as 获奖数
from hld_si_sub_0010 每年新增国家级教学成果奖特等奖数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0010'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0011', 'hld_si_sub_0011', '每年新增国家级教学成果奖一等奖数', TO_CLOB('
select
每年新增国家级教学成果奖一等奖数.xkbh as 学科编号,
每年新增国家级教学成果奖一等奖数.nd as 年度,
每年新增国家级教学成果奖一等奖数.hjs as 获奖数
from hld_si_sub_0011 每年新增国家级教学成果奖一等奖数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0011'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0012', 'hld_si_sub_0012', '每学年硕士导师数', TO_CLOB('
select
每学年硕士导师数.xkbh as 学科编号,
每学年硕士导师数.xn as 学年,
每学年硕士导师数.dss as 导师数
from hld_si_sub_0012 每学年硕士导师数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0012'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0013', 'hld_si_sub_0013', '每学年博士导师数', TO_CLOB('
select
每学年博士导师数.xkbh as 学科编号,
每学年博士导师数.xn as 学年,
每学年博士导师数.dss as 导师数
from hld_si_sub_0013 每学年博士导师数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0013'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0014', 'hld_si_sub_0014', '每学年正教授总人数', TO_CLOB('
select
每学年正教授总人数.xkbh as 学科编号,
每学年正教授总人数.xn as 学年,
每学年正教授总人数.jss as 教授数
from hld_si_sub_0014 每学年正教授总人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0014'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0015', 'hld_si_sub_0015', '每学年给本科生上课的正教授总人数', TO_CLOB('
select
每学年给本科生上课的正教授总人数.xkbh as 学科编号,
每学年给本科生上课的正教授总人数.xn as 学年,
每学年给本科生上课的正教授总人数.skjss as 授课教授数
from hld_si_sub_0015 每学年给本科生上课的正教授总人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0015'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0016', 'hld_si_sub_0016', '每学年给本科生上课的正教授比例', TO_CLOB('
select
每学年给本科生上课的正教授比例.xkbh as 学科编号,
每学年给本科生上课的正教授比例.xn as 学年,
每学年给本科生上课的正教授比例.skbl as 授课比例
from hld_si_sub_0016 每学年给本科生上课的正教授比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0016'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0017', 'hld_si_sub_0017', '每年获得国内外竞赛获奖项目总数', TO_CLOB('
select
每年获得国内外竞赛获奖项目总数.xkbh as 学科编号,
每年获得国内外竞赛获奖项目总数.nd as 年度,
每年获得国内外竞赛获奖项目总数.hjxms as 获奖项目数
from hld_si_sub_0017 每年获得国内外竞赛获奖项目总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0017'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0018', 'hld_si_sub_0018', '每年获得全国互联网加获奖数', TO_CLOB('
select
每年获得全国互联网加获奖数.xkbh as 学科编号,
每年获得全国互联网加获奖数.nd as 年度,
每年获得全国互联网加获奖数.hjs as 获奖数
from hld_si_sub_0018 每年获得全国互联网加获奖数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0018'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0019', 'hld_si_sub_0019', '每年获得全国挑战杯获奖数', TO_CLOB('
select
每年获得全国挑战杯获奖数.xkbh as 学科编号,
每年获得全国挑战杯获奖数.nd as 年度,
每年获得全国挑战杯获奖数.hjs as 获奖数
from hld_si_sub_0019 每年获得全国挑战杯获奖数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0019'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0020', 'hld_si_sub_0020', '每年突出贡献者数量', TO_CLOB('
select
每年突出贡献者数量.xkbh as 学科编号,
每年突出贡献者数量.nd as 年度,
每年突出贡献者数量.rs as 人数
from hld_si_sub_0020 每年突出贡献者数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0020'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0021', 'hld_si_sub_0021', '每年学生就业率', TO_CLOB('
select
每年学生就业率.xkbh as 学科编号,
每年学生就业率.nd as 年度,
每年学生就业率.jyl as 就业率
from hld_si_sub_0021 每年学生就业率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0021'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0022', 'hld_si_sub_0022', '每年不同培养层次学生就业去向人数', TO_CLOB('
select
每年不同培养层次学生就业去向人数.xkbh as 学科编号,
每年不同培养层次学生就业去向人数.nd as 年度,
每年不同培养层次学生就业去向人数.pycc as 培养层次,
每年不同培养层次学生就业去向人数.jyrs as 就业人数
from hld_si_sub_0022 每年不同培养层次学生就业去向人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0022'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0023', 'hld_si_sub_0023', '每学年攻读不同学位层次的入学留学生数', TO_CLOB('
select
每学年攻读不同学位层次的入学留学生数.xkbh as 学科编号,
每学年攻读不同学位层次的入学留学生数.xn as 学年,
每学年攻读不同学位层次的入学留学生数.xwcc as 学位层次,
每学年攻读不同学位层次的入学留学生数.lxss as 留学生数
from hld_si_sub_0023 每学年攻读不同学位层次的入学留学生数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0023'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0024', 'hld_si_sub_0024', '每学年攻读不同学位层次的在校生留学生数', TO_CLOB('
select
每学年攻读不同学位层次的在校生留学生数.xkbh as 学科编号,
每学年攻读不同学位层次的在校生留学生数.xn as 学年,
每学年攻读不同学位层次的在校生留学生数.xwcc as 学位层次,
每学年攻读不同学位层次的在校生留学生数.lxss as 留学生数
from hld_si_sub_0024 每学年攻读不同学位层次的在校生留学生数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0024'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0025', 'hld_si_sub_0025', '每学年交流学者数', TO_CLOB('
select
每学年交流学者数.xkbh as 学科编号,
每学年交流学者数.xn as 学年,
每学年交流学者数.jlxzs as 交流学者数
from hld_si_sub_0025 每学年交流学者数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0025'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0026', 'hld_si_sub_0026', '每学年入学留学生占当年入学学生比例', TO_CLOB('
select
每学年入学留学生占当年入学学生比例.xkbh as 学科编号,
每学年入学留学生占当年入学学生比例.xn as 学年,
每学年入学留学生占当年入学学生比例.lxsbl as 留学生比例
from hld_si_sub_0026 每学年入学留学生占当年入学学生比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0026'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0027', 'hld_si_sub_0027', '每年不同学生层次参加不同国家重要学术会议并作报告总数', TO_CLOB('
select
每年不同学生层次参加不同国家重要学术会议并作报告总数.xkbh as 学科编号,
每年不同学生层次参加不同国家重要学术会议并作报告总数.nd as 年度,
每年不同学生层次参加不同国家重要学术会议并作报告总数.pycc as 培养层次,
每年不同学生层次参加不同国家重要学术会议并作报告总数.chs as 参会数
from hld_si_sub_0027 每年不同学生层次参加不同国家重要学术会议并作报告总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0027'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0028', 'hld_si_sub_0028', '每年杰出项目获得总数', TO_CLOB('
select
每年杰出项目获得总数.xkbh as 学科编号,
每年杰出项目获得总数.nd as 年度,
每年杰出项目获得总数.xms as 项目数
from hld_si_sub_0028 每年杰出项目获得总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0028'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0029', 'hld_si_sub_0029', '每学年专任教师总数', TO_CLOB('
select
每学年专任教师总数.xkbh as 学科编号,
每学年专任教师总数.xn as 学年,
每学年专任教师总数.jss as 教师数
from hld_si_sub_0029 每学年专任教师总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0029'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0030', 'hld_si_sub_0030', '每学年不同年龄段专任教师总数', TO_CLOB('
select
每学年不同年龄段专任教师总数.xkbh as 学科编号,
每学年不同年龄段专任教师总数.xn as 学年,
每学年不同年龄段专任教师总数.nld as 年龄段,
每学年不同年龄段专任教师总数.jss as 教师数
from hld_si_sub_0030 每学年不同年龄段专任教师总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0030'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0031', 'hld_si_sub_0031', '每学年不同职称层次专任教师总数', TO_CLOB('
select
每学年不同职称层次专任教师总数.xkbh as 学科编号,
每学年不同职称层次专任教师总数.xn as 学年,
每学年不同职称层次专任教师总数.zc as 职称,
每学年不同职称层次专任教师总数.jss as 教师数
from hld_si_sub_0031 每学年不同职称层次专任教师总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0031'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0032', 'hld_si_sub_0032', '每学年不同学位层次专任教师总数', TO_CLOB('
select
每学年不同学位层次专任教师总数.xkbh as 学科编号,
每学年不同学位层次专任教师总数.xn as 学年,
每学年不同学位层次专任教师总数.xwcc as 学位层次,
每学年不同学位层次专任教师总数.jss as 教师数
from hld_si_sub_0032 每学年不同学位层次专任教师总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0032'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0033', 'hld_si_sub_0033', '每学年具有海外经历专任教师比例', TO_CLOB('
select
每学年具有海外经历专任教师比例.xkbh as 学科编号,
每学年具有海外经历专任教师比例.xn as 学年,
每学年具有海外经历专任教师比例.jsbl as 教师比例
from hld_si_sub_0033 每学年具有海外经历专任教师比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0033'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0034', 'hld_si_sub_0034', '每学年博士学位专任教师比例', TO_CLOB('
select
每学年博士学位专任教师比例.xkbh as 学科编号,
每学年博士学位专任教师比例.xn as 学年,
每学年博士学位专任教师比例.jsbl as 教师比例
from hld_si_sub_0034 每学年博士学位专任教师比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0034'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0035', 'hld_si_sub_0035', '每年不同类型的博士后总数', TO_CLOB('
select
每年不同类型的博士后总数.xkbh as 学科编号,
每年不同类型的博士后总数.nd as 年度,
每年不同类型的博士后总数.hwjl as 海外经历,
每年不同类型的博士后总数.bshs as 博士后数
from hld_si_sub_0035 每年不同类型的博士后总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0035'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0036', 'hld_si_sub_0036', '每年不同聘任类型的科研助理总数', TO_CLOB('
select
每年不同聘任类型的科研助理总数.xkbh as 学科编号,
每年不同聘任类型的科研助理总数.nd as 年度,
每年不同聘任类型的科研助理总数.prlx as 聘任类型,
每年不同聘任类型的科研助理总数.kyzls as 科研助理数
from hld_si_sub_0036 每年不同聘任类型的科研助理总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0036'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0037', 'hld_si_sub_0037', '每学年外籍专任教师总数', TO_CLOB('
select
每学年外籍专任教师总数.xkbh as 学科编号,
每学年外籍专任教师总数.xn as 学年,
每学年外籍专任教师总数.jss as 教师数
from hld_si_sub_0037 每学年外籍专任教师总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0037'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0038', 'hld_si_sub_0038', '每学年不同职称层次下的外籍专任教师总数', TO_CLOB('
select
每学年不同职称层次下的外籍专任教师总数.xkbh as 学科编号,
每学年不同职称层次下的外籍专任教师总数.xn as 学年,
每学年不同职称层次下的外籍专任教师总数.zc as 职称,
每学年不同职称层次下的外籍专任教师总数.jss as 教师数
from hld_si_sub_0038 每学年不同职称层次下的外籍专任教师总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0038'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0039', 'hld_si_sub_0039', '每学年外籍专任教师比例', TO_CLOB('
select
每学年外籍专任教师比例.xkbh as 学科编号,
每学年外籍专任教师比例.xn as 学年,
每学年外籍专任教师比例.jsbl as 教师比例
from hld_si_sub_0039 每学年外籍专任教师比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0039'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0040', 'hld_si_sub_0040', '每学年语言类外籍专任教师总数', TO_CLOB('
select
每学年语言类外籍专任教师总数.xkbh as 学科编号,
每学年语言类外籍专任教师总数.xn as 学年,
每学年语言类外籍专任教师总数.jss as 教师数
from hld_si_sub_0040 每学年语言类外籍专任教师总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0040'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0041', 'hld_si_sub_0041', '每学年专业类外籍专任教师总数', TO_CLOB('
select
每学年专业类外籍专任教师总数.xkbh as 学科编号,
每学年专业类外籍专任教师总数.xn as 学年,
每学年专业类外籍专任教师总数.jss as 教师数
from hld_si_sub_0041 每学年专业类外籍专任教师总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0041'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0042', 'hld_si_sub_0042', '每学年不同学位层次下的外籍专任教师总数', TO_CLOB('
select
每学年不同学位层次下的外籍专任教师总数.xkbh as 学科编号,
每学年不同学位层次下的外籍专任教师总数.xn as 学年,
每学年不同学位层次下的外籍专任教师总数.zgxw as 最高学位,
每学年不同学位层次下的外籍专任教师总数.jss as 教师数
from hld_si_sub_0042 每学年不同学位层次下的外籍专任教师总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0042'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0043', 'hld_si_sub_0043', '每年教师担任不同职务国内外重要期刊负责人总数', TO_CLOB('
select
每年教师担任不同职务国内外重要期刊负责人总数.xkbh as 学科编号,
每年教师担任不同职务国内外重要期刊负责人总数.nd as 年度,
每年教师担任不同职务国内外重要期刊负责人总数.rzzw as 任职职务,
每年教师担任不同职务国内外重要期刊负责人总数.jss as 教师数
from hld_si_sub_0043 每年教师担任不同职务国内外重要期刊负责人总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0043'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0044', 'hld_si_sub_0044', '每年新增担任SCI期刊职务人数', TO_CLOB('
select
每年新增担任SCI期刊职务人数.xkbh as 学科编号,
每年新增担任SCI期刊职务人数.nd as 年度,
每年新增担任SCI期刊职务人数.jss as 教师数
from hld_si_sub_0044 每年新增担任SCI期刊职务人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0044'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0045', 'hld_si_sub_0045', '每年新增担任SSCI期刊职务人数', TO_CLOB('
select
每年新增担任SSCI期刊职务人数.xkbh as 学科编号,
每年新增担任SSCI期刊职务人数.nd as 年度,
每年新增担任SSCI期刊职务人数.jss as 教师数
from hld_si_sub_0045 每年新增担任SSCI期刊职务人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0045'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0046', 'hld_si_sub_0046', '每年新增担任EI期刊职务人数', TO_CLOB('
select
每年新增担任EI期刊职务人数.xkbh as 学科编号,
每年新增担任EI期刊职务人数.nd as 年度,
每年新增担任EI期刊职务人数.jss as 教师数
from hld_si_sub_0046 每年新增担任EI期刊职务人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0046'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0047', 'hld_si_sub_0047', '每年教师新增在重要学术组织任职数', TO_CLOB('
select
每年教师新增在重要学术组织任职数.xkbh as 学科编号,
每年教师新增在重要学术组织任职数.nd as 年度,
每年教师新增在重要学术组织任职数.jss as 教师数
from hld_si_sub_0047 每年教师新增在重要学术组织任职数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0047'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0048', 'hld_si_sub_0048', '每年教师参加本领域重要学术会议并作报告总数', TO_CLOB('
select
每年教师参加本领域重要学术会议并作报告总数.xkbh as 学科编号,
每年教师参加本领域重要学术会议并作报告总数.nd as 年度,
每年教师参加本领域重要学术会议并作报告总数.jss as 教师数
from hld_si_sub_0048 每年教师参加本领域重要学术会议并作报告总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0048'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0049', 'hld_si_sub_0049', '每年教师担任国际比赛评委、裁判人员总数', TO_CLOB('
select
每年教师担任国际比赛评委裁判人员总数.xkbh as 学科编号,
每年教师担任国际比赛评委裁判人员总数.nd as 年度,
每年教师担任国际比赛评委裁判人员总数.jss as 教师数
from hld_si_sub_0049 每年教师担任国际比赛评委裁判人员总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0049'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0050', 'hld_si_sub_0050', '每年教师获得的国家三大奖总数', TO_CLOB('
select
每年教师获得的国家三大奖总数.xkbh as 学科编号,
每年教师获得的国家三大奖总数.nd as 年度,
每年教师获得的国家三大奖总数.hjs as 获奖数
from hld_si_sub_0050 每年教师获得的国家三大奖总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0050'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0051', 'hld_si_sub_0051', '每年教师获得中国社会科学优秀成果奖数', TO_CLOB('
select
每年教师获得中国社会科学优秀成果奖数.xkbh as 学科编号,
每年教师获得中国社会科学优秀成果奖数.nd as 年度,
每年教师获得中国社会科学优秀成果奖数.hjs as 获奖数
from hld_si_sub_0051 每年教师获得中国社会科学优秀成果奖数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0051'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0052', 'hld_si_sub_0052', '每年教师获得中国医学科学奖数', TO_CLOB('
select
每年教师获得中国医学科学奖数.xkbh as 学科编号,
每年教师获得中国医学科学奖数.nd as 年度,
每年教师获得中国医学科学奖数.hjs as 获奖数
from hld_si_sub_0052 每年教师获得中国医学科学奖数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0052'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0053', 'hld_si_sub_0053', '每年教师获得不同组织单位的国内外重要奖项总数', TO_CLOB('
select
每年教师获得不同组织单位的国内外重要奖项总数.xkbh as 学科编号,
每年教师获得不同组织单位的国内外重要奖项总数.nd as 年度,
每年教师获得不同组织单位的国内外重要奖项总数.hjs as 获奖数
from hld_si_sub_0053 每年教师获得不同组织单位的国内外重要奖项总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0053'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0054', 'hld_si_sub_0054', '每年教师公开出版的专著总数', TO_CLOB('
select
每年教师公开出版的专著总数.xkbh as 学科编号,
每年教师公开出版的专著总数.nd as 年度,
每年教师公开出版的专著总数.cbs as 出版数
from hld_si_sub_0054 每年教师公开出版的专著总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0054'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0055', 'hld_si_sub_0055', '每年教师在国内外重要期刊发表的代表性论文总数', TO_CLOB('
select
每年教师在国内外重要期刊发表的代表性论文总数.xkbh as 学科编号,
每年教师在国内外重要期刊发表的代表性论文总数.nd as 年度,
每年教师在国内外重要期刊发表的代表性论文总数.fbs as 发表数
from hld_si_sub_0055 每年教师在国内外重要期刊发表的代表性论文总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0055'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0056', 'hld_si_sub_0056', '每年新增SCI收录期刊论文数', TO_CLOB('
select
每年新增SCI收录期刊论文数.xkbh as 学科编号,
每年新增SCI收录期刊论文数.nd as 年度,
每年新增SCI收录期刊论文数.fbs as 发表数
from hld_si_sub_0056 每年新增SCI收录期刊论文数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0056'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0057', 'hld_si_sub_0057', '每年新增SSCI收录期刊论文数', TO_CLOB('
select
每年新增SSCI收录期刊论文数.xkbh as 学科编号,
每年新增SSCI收录期刊论文数.nd as 年度,
每年新增SSCI收录期刊论文数.fbs as 发表数
from hld_si_sub_0057 每年新增SSCI收录期刊论文数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0057'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0058', 'hld_si_sub_0058', '每年新增EI收录期刊论文数', TO_CLOB('
select
每年新增EI收录期刊论文数.xkbh as 学科编号,
每年新增EI收录期刊论文数.nd as 年度,
每年新增EI收录期刊论文数.fbs as 发表数
from hld_si_sub_0058 每年新增EI收录期刊论文数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0058'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0059', 'hld_si_sub_0059', '每年新增Nature期刊论文数', TO_CLOB('
select
每年新增Nature期刊论文数.xkbh as 学科编号,
每年新增Nature期刊论文数.nd as 年度,
每年新增Nature期刊论文数.fbs as 发表数
from hld_si_sub_0059 每年新增Nature期刊论文数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0059'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0060', 'hld_si_sub_0060', '每年新增Science期刊论文数', TO_CLOB('
select
每年新增Science期刊论文数.xkbh as 学科编号,
每年新增Science期刊论文数.nd as 年度,
每年新增Science期刊论文数.fbs as 发表数
from hld_si_sub_0060 每年新增Science期刊论文数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0060'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0061', 'hld_si_sub_0061', '每年新增Cell期刊论文数', TO_CLOB('
select
每年新增Cell期刊论文数.xkbh as 学科编号,
每年新增Cell期刊论文数.nd as 年度,
每年新增Cell期刊论文数.fbs as 发表数
from hld_si_sub_0061 每年新增Cell期刊论文数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0061'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0062', 'hld_si_sub_0062', '每年教师在不同收录类别下发表的代表性论文总数', TO_CLOB('
select
每年教师在不同收录类别下发表的代表性论文总数.xkbh as 学科编号,
每年教师在不同收录类别下发表的代表性论文总数.nd as 年度,
每年教师在不同收录类别下发表的代表性论文总数.qkslqk as 期刊收录情况,
每年教师在不同收录类别下发表的代表性论文总数.fbs as 发表数
from hld_si_sub_0062 每年教师在不同收录类别下发表的代表性论文总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0062'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0063', 'hld_si_sub_0063', '每年新增授权发明专利数', TO_CLOB('
select
每年新增授权发明专利数.xkbh as 学科编号,
每年新增授权发明专利数.nd as 年度,
每年新增授权发明专利数.zls as 专利数
from hld_si_sub_0063 每年新增授权发明专利数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0063'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0064', 'hld_si_sub_0064', '每年新增国家自然科学基金数', TO_CLOB('
select
每年新增国家自然科学基金数.xkbh as 学科编号,
每年新增国家自然科学基金数.nd as 年度,
每年新增国家自然科学基金数.xms as 项目数
from hld_si_sub_0064 每年新增国家自然科学基金数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0064'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0065', 'hld_si_sub_0065', '每年新增国家自然科学基金重大项目数', TO_CLOB('
select
每年新增国家自然科学基金重大项目数.xkbh as 学科编号,
每年新增国家自然科学基金重大项目数.nd as 年度,
每年新增国家自然科学基金重大项目数.xms as 项目数
from hld_si_sub_0065 每年新增国家自然科学基金重大项目数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0065'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0066', 'hld_si_sub_0066', '每年新增国家自然科学基金重点项目数', TO_CLOB('
select
每年新增国家自然科学基金重点项目数.xkbh as 学科编号,
每年新增国家自然科学基金重点项目数.nd as 年度,
每年新增国家自然科学基金重点项目数.xms as 项目数
from hld_si_sub_0066 每年新增国家自然科学基金重点项目数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0066'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0067', 'hld_si_sub_0067', '每年新增国家社会科学基金项目数', TO_CLOB('
select
每年新增国家社会科学基金项目数.xkbh as 学科编号,
每年新增国家社会科学基金项目数.nd as 年度,
每年新增国家社会科学基金项目数.xms as 项目数
from hld_si_sub_0067 每年新增国家社会科学基金项目数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0067'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0068', 'hld_si_sub_0068', '每年承担不同任务的国内外重大设计与展演任务总数', TO_CLOB('
select
每年承担不同任务的国内外重大设计与展演任务总数.xkbh as 学科编号,
每年承担不同任务的国内外重大设计与展演任务总数.nd as 年度,
每年承担不同任务的国内外重大设计与展演任务总数.cdrw as 承担任务,
每年承担不同任务的国内外重大设计与展演任务总数.zys as 展演数
from hld_si_sub_0068 每年承担不同任务的国内外重大设计与展演任务总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0068'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0069', 'hld_si_sub_0069', '国家级重大平台总数', TO_CLOB('
select
国家级重大平台总数.xkbh as 学科编号,
国家级重大平台总数.nd as 年度,
国家级重大平台总数.pts as 平台数
from hld_si_sub_0069 国家级重大平台总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0069'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0070', 'hld_si_sub_0070', '每年国家级重大平台获得经费总数', TO_CLOB('
select
每年国家级重大平台获得经费总数.xkbh as 学科编号,
每年国家级重大平台获得经费总数.nd as 年度,
每年国家级重大平台获得经费总数.jfs as 经费数
from hld_si_sub_0070 每年国家级重大平台获得经费总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0070'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0071', 'hld_si_sub_0071', '每年国家级重大平台支持项目数', TO_CLOB('
select
每年国家级重大平台支持项目数.xkbh as 学科编号,
每年国家级重大平台支持项目数.nd as 年度,
每年国家级重大平台支持项目数.xms as 项目数
from hld_si_sub_0071 每年国家级重大平台支持项目数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0071'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0072', 'hld_si_sub_0072', '每年新增国家重点实验室数', TO_CLOB('
select
每年新增国家重点实验室数.xkbh as 学科编号,
每年新增国家重点实验室数.nd as 年度,
每年新增国家重点实验室数.pts as 平台数
from hld_si_sub_0072 每年新增国家重点实验室数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0072'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0073', 'hld_si_sub_0073', '每年新增国家工程研究中心数', TO_CLOB('
select
每年新增国家工程研究中心数.xkbh as 学科编号,
每年新增国家工程研究中心数.nd as 年度,
每年新增国家工程研究中心数.pts as 平台数
from hld_si_sub_0073 每年新增国家工程研究中心数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0073'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0074', 'hld_si_sub_0074', '每年新增国家临床医学中心数', TO_CLOB('
select
每年新增国家临床医学中心数.xkbh as 学科编号,
每年新增国家临床医学中心数.nd as 年度,
每年新增国家临床医学中心数.pts as 平台数
from hld_si_sub_0074 每年新增国家临床医学中心数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0074'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0075', 'hld_si_sub_0075', '每年新增教育部人文社科重点研究基地数', TO_CLOB('
select
每年新增教育部人文社科重点研究基地数.xkbh as 学科编号,
每年新增教育部人文社科重点研究基地数.nd as 年度,
每年新增教育部人文社科重点研究基地数.pts as 平台数
from hld_si_sub_0075 每年新增教育部人文社科重点研究基地数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0075'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0076', 'hld_si_sub_0076', '省部级重大平台总数', TO_CLOB('
select
省部级重大平台总数.xkbh as 学科编号,
省部级重大平台总数.nd as 年度,
省部级重大平台总数.pts as 平台数
from hld_si_sub_0076 省部级重大平台总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0076'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0077', 'hld_si_sub_0077', '每年省部级重大平台获得经费总数', TO_CLOB('
select
每年省部级重大平台获得经费总数.xkbh as 学科编号,
每年省部级重大平台获得经费总数.nd as 年度,
每年省部级重大平台获得经费总数.jfs as 经费数
from hld_si_sub_0077 每年省部级重大平台获得经费总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0077'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0078', 'hld_si_sub_0078', '每年省部级重大平台支持项目数', TO_CLOB('
select
每年省部级重大平台支持项目数.xkbh as 学科编号,
每年省部级重大平台支持项目数.nd as 年度,
每年省部级重大平台支持项目数.xms as 项目数
from hld_si_sub_0078 每年省部级重大平台支持项目数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0078'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0079', 'hld_si_sub_0079', '每月纵向科研项目经费到账数', TO_CLOB('
select
每月纵向科研项目经费到账数.xkbh as 学科编号,
每月纵向科研项目经费到账数.ny as 年月,
每月纵向科研项目经费到账数.jfs as 经费数
from hld_si_sub_0079 每月纵向科研项目经费到账数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0079'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0080', 'hld_si_sub_0080', '每月纵向科研项目立项数', TO_CLOB('
select
每月纵向科研项目立项数.xkbh as 学科编号,
每月纵向科研项目立项数.ny as 年月,
每月纵向科研项目立项数.xms as 项目数
from hld_si_sub_0080 每月纵向科研项目立项数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0080'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0081', 'hld_si_sub_0081', '每月横向科研经费到账总数', TO_CLOB('
select
每月横向科研经费到账总数.xkbh as 学科编号,
每月横向科研经费到账总数.ny as 年月,
每月横向科研经费到账总数.jfs as 经费数
from hld_si_sub_0081 每月横向科研经费到账总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0081'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0082', 'hld_si_sub_0082', '每月横向科研项目立项数', TO_CLOB('
select
每月横向科研项目立项数.xkbh as 学科编号,
每月横向科研项目立项数.ny as 年月,
每月横向科研项目立项数.xms as 项目数
from hld_si_sub_0082 每月横向科研项目立项数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0082'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0083', 'hld_si_sub_0083', '每年地方政府投入项目总数', TO_CLOB('
select
每年地方政府投入项目总数.xkbh as 学科编号,
每年地方政府投入项目总数.nd as 年度,
每年地方政府投入项目总数.xms as 项目数
from hld_si_sub_0083 每年地方政府投入项目总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0083'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0084', 'hld_si_sub_0084', '每年地方政府投入项目经费总数', TO_CLOB('
select
每年地方政府投入项目经费总数.xkbh as 学科编号,
每年地方政府投入项目经费总数.nd as 年度,
每年地方政府投入项目经费总数.jfs as 经费数
from hld_si_sub_0084 每年地方政府投入项目经费总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0084'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0085', 'hld_si_sub_0085', '每年人均到账科研经费', TO_CLOB('
select
每年人均到账科研经费.xkbh as 学科编号,
每年人均到账科研经费.nd as 年度,
每年人均到账科研经费.jfs as 经费数
from hld_si_sub_0085 每年人均到账科研经费'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0085'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0086', 'hld_si_sub_0086', '每年主办的国际学术期刊总数', TO_CLOB('
select
每年主办的国际学术期刊总数.xkbh as 学科编号,
每年主办的国际学术期刊总数.nd as 年度,
每年主办的国际学术期刊总数.zbs as 主办数
from hld_si_sub_0086 每年主办的国际学术期刊总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0086'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0087', 'hld_si_sub_0087', '每年参与制定的国内外标准总数', TO_CLOB('
select
每年参与制定的国内外标准总数.xkbh as 学科编号,
每年参与制定的国内外标准总数.nd as 年度,
每年参与制定的国内外标准总数.zds as 制定数
from hld_si_sub_0087 每年参与制定的国内外标准总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0087'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0088', 'hld_si_sub_0088', '每年成果转化总数', TO_CLOB('
select
每年成果转化总数.xkbh as 学科编号,
每年成果转化总数.nd as 年度,
每年成果转化总数.zhs as 转化数
from hld_si_sub_0088 每年成果转化总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0088'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0089', 'hld_si_sub_0089', '每年人均转化合同经费', TO_CLOB('
select
每年人均转化合同经费.xkbh as 学科编号,
每年人均转化合同经费.nd as 年度,
每年人均转化合同经费.jfs as 经费数
from hld_si_sub_0089 每年人均转化合同经费'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0089'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0090', 'hld_si_sub_0090', '每年人均横向项目合同经费', TO_CLOB('
select
每年人均横向项目合同经费.xkbh as 学科编号,
每年人均横向项目合同经费.nd as 年度,
每年人均横向项目合同经费.jfs as 经费数
from hld_si_sub_0090 每年人均横向项目合同经费'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0090'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'hld_si_sub_0091', 'hld_si_sub_0091', '每年咨询服务到校金额总数', TO_CLOB('
select
每年咨询服务到校金额总数.xkbh as 学科编号,
每年咨询服务到校金额总数.nd as 年度,
每年咨询服务到校金额总数.jfs as 经费数
from hld_si_sub_0091 每年咨询服务到校金额总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'hld_si_sub_0091'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'sch_label', 'sch_label', 'SCH标签宽表', TO_CLOB('
select
SCH标签宽表.kzrq as 快照日期,
SCH标签宽表.xxbsm as 学校标识码
from sch_label SCH标签宽表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'sch_label'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'sub_label', 'sub_label', 'SUB标签宽表', TO_CLOB('
select
SUB标签宽表.kzrq as 快照日期,
SUB标签宽表.yjxkdm as 一级学科代码
from sub_label SUB标签宽表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'sub_label'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 't_da_model_column_extend', 't_da_model_column_extend', '', TO_CLOB('
select
t_da_model_column_extend.primary_column_tag as 是否主键字段
from t_da_model_column_extend t_da_model_column_extend'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 't_da_model_column_extend'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 't_da_model_column_info', 't_da_model_column_info', '模型字段信息', TO_CLOB('
select
模型字段信息.model_id as 模型ID,
模型字段信息.column_id as 字段ID,
模型字段信息.syn_value as 同义词
from t_da_model_column_info 模型字段信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 't_da_model_column_info'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 't_da_model_info', 't_da_model_info', '模型信息', TO_CLOB('
select
模型信息.model_id as 模型ID,
模型信息.syn_value as 同义词
from t_da_model_info 模型信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 't_da_model_info'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 't_da_model_relation', 't_da_model_relation', '模型关系表', TO_CLOB('
select
模型关系表.model_id as 主模型ID,
模型关系表.fields as 主模型字段,
模型关系表.mapped_model_id as 关联模型,
模型关系表.mapped_fields as 关联模型字段
from t_da_model_relation 模型关系表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 't_da_model_relation'
);