-- 变更记录主表
BEGIN
EXECUTE IMMEDIATE 'CREATE TABLE "T_DA_MODEL_CHANGE" (
  "ID" VARCHAR2(100 BYTE) NOT NULL,
  "CREATE_BY" VARCHAR2(100 BYTE),
  "CREATE_TIME" DATE,
  "UPDATE_BY" VARCHAR2(100 BYTE),
  "UPDATE_TIME" DATE,
  PRIMARY KEY ("ID")
)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955) THEN
		RAISE;
	END IF;
END;
/

BEGIN
EXECUTE IMMEDIATE 'ALTER TABLE T_DA_MODEL_CHANGE ADD MODEL_ID VARCHAR2(255 BYTE)';
EXCEPTION
WHEN OTHERS THEN
 IF sqlcode != -1430 THEN RAISE; END IF;
END;
/

BEGIN
EXECUTE IMMEDIATE 'ALTER TABLE T_DA_MODEL_CHANGE ADD MODEL_VERSION VARCHAR2(50 BYTE)';
EXCEPTION
WHEN OTHERS THEN
 IF sqlcode != -1430 THEN RAISE; END IF;
END;
/

BEGIN
EXECUTE IMMEDIATE 'ALTER TABLE T_DA_MODEL_CHANGE ADD INTERNAL_VERSION VARCHAR2(50 BYTE)';
EXCEPTION
WHEN OTHERS THEN
 IF sqlcode != -1430 THEN RAISE; END IF;
END;
/

BEGIN
EXECUTE IMMEDIATE 'ALTER TABLE T_DA_MODEL_CHANGE ADD OPERATION_SOURCE VARCHAR2(50 BYTE)';
EXCEPTION
WHEN OTHERS THEN
 IF sqlcode != -1430 THEN RAISE; END IF;
END;
/

BEGIN
EXECUTE IMMEDIATE 'ALTER TABLE T_DA_MODEL_CHANGE ADD ACTION_TYPE VARCHAR2(50 BYTE)';
EXCEPTION
WHEN OTHERS THEN
 IF sqlcode != -1430 THEN RAISE; END IF;
END;
/

BEGIN
EXECUTE IMMEDIATE 'ALTER TABLE T_DA_MODEL_CHANGE ADD MODEL_LABEL VARCHAR2(255 BYTE)';
EXCEPTION
WHEN OTHERS THEN
 IF sqlcode != -1430 THEN RAISE; END IF;
END;
/

BEGIN
EXECUTE IMMEDIATE 'ALTER TABLE T_DA_MODEL_CHANGE ADD MODEL_TYPE VARCHAR2(255 BYTE)';
EXCEPTION
WHEN OTHERS THEN
 IF sqlcode != -1430 THEN RAISE; END IF;
END;
/

COMMENT ON COLUMN T_DA_MODEL_CHANGE.ID IS 'ID';
COMMENT ON COLUMN T_DA_MODEL_CHANGE.MODEL_ID IS '模型ID';
COMMENT ON COLUMN T_DA_MODEL_CHANGE.MODEL_VERSION IS '模型版本';
COMMENT ON COLUMN T_DA_MODEL_CHANGE.INTERNAL_VERSION IS '内部版本号';
COMMENT ON COLUMN T_DA_MODEL_CHANGE.OPERATION_SOURCE IS '操作来源: user-用户，upgrade-自动升级程序';
COMMENT ON COLUMN T_DA_MODEL_CHANGE.ACTION_TYPE IS '操作类型: add-新增，update-修改，delete-删除';
COMMENT ON COLUMN T_DA_MODEL_CHANGE.MODEL_LABEL IS '模型名称';
COMMENT ON COLUMN T_DA_MODEL_CHANGE.MODEL_TYPE IS '模型类型';
COMMENT ON COLUMN T_DA_MODEL_CHANGE.CREATE_BY IS '创建人';
COMMENT ON COLUMN T_DA_MODEL_CHANGE.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN T_DA_MODEL_CHANGE.UPDATE_BY IS '更新人';
COMMENT ON COLUMN T_DA_MODEL_CHANGE.UPDATE_TIME IS '更新时间';
COMMENT ON TABLE T_DA_MODEL_CHANGE IS '模型变更表';

-- 变更记录明细表
BEGIN
EXECUTE IMMEDIATE 'CREATE TABLE "T_DA_MODEL_CHANGE_DETAIL" (
  "ID" VARCHAR2(100 BYTE) NOT NULL,
  "CREATE_BY" VARCHAR2(100 BYTE),
  "CREATE_TIME" DATE,
  "UPDATE_BY" VARCHAR2(100 BYTE),
  "UPDATE_TIME" DATE,
  PRIMARY KEY ("ID")
)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955) THEN
		RAISE;
	END IF;
END;
/

BEGIN
EXECUTE IMMEDIATE 'ALTER TABLE T_DA_MODEL_CHANGE_DETAIL ADD CHANGE_ID VARCHAR2(255 BYTE)';
EXCEPTION
WHEN OTHERS THEN
 IF sqlcode != -1430 THEN RAISE; END IF;
END;
/

BEGIN
EXECUTE IMMEDIATE 'ALTER TABLE T_DA_MODEL_CHANGE_DETAIL ADD OPERATION_SOURCE VARCHAR2(50 BYTE)';
EXCEPTION
WHEN OTHERS THEN
 IF sqlcode != -1430 THEN RAISE; END IF;
END;
/

BEGIN
EXECUTE IMMEDIATE 'ALTER TABLE T_DA_MODEL_CHANGE_DETAIL ADD ACTION_TYPE VARCHAR2(50 BYTE)';
EXCEPTION
WHEN OTHERS THEN
 IF sqlcode != -1430 THEN RAISE; END IF;
END;
/

BEGIN
EXECUTE IMMEDIATE 'ALTER TABLE T_DA_MODEL_CHANGE_DETAIL ADD FIELD_TYPE VARCHAR2(50 BYTE)';
EXCEPTION
WHEN OTHERS THEN
 IF sqlcode != -1430 THEN RAISE; END IF;
END;
/

BEGIN
EXECUTE IMMEDIATE 'ALTER TABLE T_DA_MODEL_CHANGE_DETAIL ADD FIELD_ID VARCHAR2(255 BYTE)';
EXCEPTION
WHEN OTHERS THEN
 IF sqlcode != -1430 THEN RAISE; END IF;
END;
/

BEGIN
EXECUTE IMMEDIATE 'ALTER TABLE T_DA_MODEL_CHANGE_DETAIL ADD FIELD_NAME VARCHAR2(50 BYTE)';
EXCEPTION
WHEN OTHERS THEN
 IF sqlcode != -1430 THEN RAISE; END IF;
END;
/

BEGIN
EXECUTE IMMEDIATE 'ALTER TABLE T_DA_MODEL_CHANGE_DETAIL ADD CHANGE_DETAIL CLOB';
EXCEPTION
WHEN OTHERS THEN
 IF sqlcode != -1430 THEN RAISE; END IF;
END;
/

COMMENT ON COLUMN T_DA_MODEL_CHANGE_DETAIL.ID IS 'ID';
COMMENT ON COLUMN T_DA_MODEL_CHANGE_DETAIL.CHANGE_ID IS '变更ID，即t_da_model_change主键';
COMMENT ON COLUMN T_DA_MODEL_CHANGE_DETAIL.OPERATION_SOURCE IS '操作来源: user-用户，upgrade-自动升级程序';
COMMENT ON COLUMN T_DA_MODEL_CHANGE_DETAIL.ACTION_TYPE IS '操作类型: add-新增，update-修改，delete-删除';
COMMENT ON COLUMN T_DA_MODEL_CHANGE_DETAIL.FIELD_TYPE IS '变更类型: model-模型, column-字段';
COMMENT ON COLUMN T_DA_MODEL_CHANGE_DETAIL.FIELD_ID IS '模型ID或字段ID';
COMMENT ON COLUMN T_DA_MODEL_CHANGE_DETAIL.FIELD_NAME IS '属性名';
COMMENT ON COLUMN T_DA_MODEL_CHANGE_DETAIL.CHANGE_DETAIL IS '变更明细';
COMMENT ON COLUMN T_DA_MODEL_CHANGE_DETAIL.CREATE_BY IS '创建人';
COMMENT ON COLUMN T_DA_MODEL_CHANGE_DETAIL.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN T_DA_MODEL_CHANGE_DETAIL.UPDATE_BY IS '更新人';
COMMENT ON COLUMN T_DA_MODEL_CHANGE_DETAIL.UPDATE_TIME IS '更新时间';
COMMENT ON TABLE T_DA_MODEL_CHANGE_DETAIL IS '模型/字段变更表记录';
