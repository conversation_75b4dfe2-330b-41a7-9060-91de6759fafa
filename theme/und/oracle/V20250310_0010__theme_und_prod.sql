----------------------------------------------↓ 公共增量 ↓----------------------------------------------
-- 新增【模型分类扩展表】:1879435274158092288
insert into t_da_model_classify_extend (classify_id,classify_type,entity_id,id,create_by,create_time,update_by,update_time)
select '1879435274158092288','zb','SCHOOL','1879435274158092288','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
from dual
where not exists (
	select 1
	from t_da_model_classify_extend
	where id = '1879435274158092288');
-- 新增【模型分类】:1879435274158092288
insert into lowcode_model_classify (sys_flag,id,classify_code,classify_name,parent_id,status,create_by,create_time,update_by,update_time)
select 0,'1879435274158092288','extzb1879435274158092289','教务运行','SCHOOL',1,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
from dual
where not exists (
	select 1
	from lowcode_model_classify
	where id = '1879435274158092288');
----------------------------------------------↓ 模型 main-abdschbzksjxj ↓----------------------------------------------
--模型：本专科生奖学金 ABD_SCH_BZKSJXJ main-abdschbzksjxj
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdschbzksjxj'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdschbzksjxj版本号:'||v_version);
	IF v_version >= 'V1.0.2.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdschbzksjxj的本次增量版本号:V1.0.2,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1898972096462598145','main-abdschbzksjxj','V1.0.2','0','upgrade','update','本专科生奖学金','jcsj','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1898972096462598145');
		-- 新增【字段扩展】：main-abdschbzksjxj-xn
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdschbzksjxj',0,'main-abdschbzksjxj-xn','main-abdschbzksjxj-xn',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdschbzksjxj-xn');
		-- 新增【字段】：main-abdschbzksjxj-xn
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'year','main-abdschbzksjxj',1,0,'xn',0,'text','学年',50,0,'main-abdschbzksjxj-xn',0,'text',0,1,'java.lang.String',1,'XN',12,12,1,1,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdschbzksjxj-xn');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_SCH_BZKSJXJ ADD XN VARCHAR2(50)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_SCH_BZKSJXJ.XN is ''学年''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972101218938880','1898972096462598145','upgrade','add','column','main-abdschbzksjxj-xn',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"year","main-datamodelcolumn-columnJavaname":"xn","main-datamodelcolumn-columnLabel":"学年","main-datamodelcolumn-columnWidth":50,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XN","main-datamodelcolumn-orderIndex":12,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972101218938880');
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.2',internal_version='0' WHERE model_id ='main-abdschbzksjxj';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdschbzksjxj成功,模型版本号更新为:V1.0.2,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-abdundbzkshdjxjjl ↓----------------------------------------------
--模型：本专科生获得奖学金记录 ABD_UND_BZKSHDJXJJL main-abdundbzkshdjxjjl
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdundbzkshdjxjjl'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdundbzkshdjxjjl版本号:'||v_version);
	IF v_version >= 'V1.0.2.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdundbzkshdjxjjl的本次增量版本号:V1.0.2,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1898972096462598146','main-abdundbzkshdjxjjl','V1.0.2','0','upgrade','update','本专科生获得奖学金记录','jcsj','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1898972096462598146');
		-- 新增【字段扩展】：main-abdundbzkshdjxjjl-xn
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdundbzkshdjxjjl',0,'main-abdundbzkshdjxjjl-xn','main-abdundbzkshdjxjjl-xn',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdundbzkshdjxjjl-xn');
		-- 新增【字段】：main-abdundbzkshdjxjjl-xn
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'schoolYear','main-abdundbzkshdjxjjl',1,0,'xn',0,'text','学年',30,0,'main-abdundbzkshdjxjjl-xn',0,'text',0,1,'java.lang.String',1,'XN',12,12,1,1,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdundbzkshdjxjjl-xn');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_UND_BZKSHDJXJJL ADD XN VARCHAR2(30)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_UND_BZKSHDJXJJL.XN is ''学年''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972101277659136','1898972096462598146','upgrade','add','column','main-abdundbzkshdjxjjl-xn',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"schoolYear","main-datamodelcolumn-columnJavaname":"xn","main-datamodelcolumn-columnLabel":"学年","main-datamodelcolumn-columnWidth":30,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XN","main-datamodelcolumn-orderIndex":12,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972101277659136');
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.2',internal_version='0' WHERE model_id ='main-abdundbzkshdjxjjl';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdundbzkshdjxjjl成功,模型版本号更新为:V1.0.2,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-abdundbzksjbxx ↓----------------------------------------------
--模型：本专科生基本信息 ABD_UND_BZKSJBXX main-abdundbzksjbxx
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdundbzksjbxx'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdundbzksjbxx版本号:'||v_version);
	IF v_version >= 'V1.0.9.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdundbzksjbxx的本次增量版本号:V1.0.9,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1898972096462598147','main-abdundbzksjbxx','V1.0.9','0','upgrade','update','本专科生基本信息','jcsj','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1898972096462598147');
		-- 修改【字段】：main-abdundbzksjbxx-xy columnWidth
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-abdundbzksjbxx-xy' and FIELD_NAME='main-datamodelcolumn-columnWidth';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdundbzksjbxx-xy(main-datamodelcolumn-columnWidth 字段数据库长度)，跳过更新');
			ELSE
				BEGIN
				EXECUTE IMMEDIATE 'ALTER TABLE ABD_UND_BZKSJBXX MODIFY XY VARCHAR2(200)';
				EXCEPTION
				WHEN OTHERS THEN
					IF SQLCODE NOT IN (-942,-904) THEN
						RAISE;
					END IF;
				END;
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1898972101327990784','1898972096462598147','upgrade','update','column','main-abdundbzksjbxx-xy','main-datamodelcolumn-columnWidth','{"变更前":{"main-datamodelcolumn-columnJavaname":"xy","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XY","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":180,"main-datamodelcolumn-columnLabel":"学院","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":26,"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnDict":"biz_department_student","main-datamodelcolumn-columnXtype":"select-tree"},"变更后":{"main-datamodelcolumn-columnWidth":"200"}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1898972101327990784');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdundbzksjbxx-xy(main-datamodelcolumn-columnWidth 字段数据库长度),sql执行错误:' || SQLERRM);
		END;
		
		-- 修改【字段】：main-abdundbzksjbxx-xy searchXtype
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-abdundbzksjbxx-xy' and FIELD_NAME='main-datamodelcolumn-searchXtype';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdundbzksjbxx-xy(main-datamodelcolumn-searchXtype 搜索渲染类型)，跳过更新');
			ELSE
				update lowcode_model_column set search_xtype = 'multi-tree'
				where id = 'main-abdundbzksjbxx-xy';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1898972101336379392','1898972096462598147','upgrade','update','column','main-abdundbzksjbxx-xy','main-datamodelcolumn-searchXtype','{"变更前":{"main-datamodelcolumn-columnJavaname":"xy","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XY","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":180,"main-datamodelcolumn-columnLabel":"学院","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":26,"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnDict":"biz_department_student","main-datamodelcolumn-columnXtype":"select-tree"},"变更后":{"main-datamodelcolumn-searchXtype":"multi-tree"}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1898972101336379392');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdundbzksjbxx-xy(main-datamodelcolumn-searchXtype 搜索渲染类型),sql执行错误:' || SQLERRM);
		END;
		
		-- 修改【字段】：main-abdundbzksjbxx-xybm columnWidth
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-abdundbzksjbxx-xybm' and FIELD_NAME='main-datamodelcolumn-columnWidth';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdundbzksjbxx-xybm(main-datamodelcolumn-columnWidth 字段数据库长度)，跳过更新');
			ELSE
				BEGIN
				EXECUTE IMMEDIATE 'ALTER TABLE ABD_UND_BZKSJBXX MODIFY XYBM VARCHAR2(200)';
				EXCEPTION
				WHEN OTHERS THEN
					IF SQLCODE NOT IN (-942,-904) THEN
						RAISE;
					END IF;
				END;
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1898972101361545216','1898972096462598147','upgrade','update','column','main-abdundbzksjbxx-xybm','main-datamodelcolumn-columnWidth','{"变更前":{"main-datamodelcolumn-columnJavaname":"xybm","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XYBM","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":180,"main-datamodelcolumn-columnLabel":"学院编码","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":25,"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnDict":"dept_college","main-datamodelcolumn-columnXtype":"select-tree"},"变更后":{"main-datamodelcolumn-columnWidth":"200"}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1898972101361545216');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdundbzksjbxx-xybm(main-datamodelcolumn-columnWidth 字段数据库长度),sql执行错误:' || SQLERRM);
		END;
		
		-- 修改【字段】：main-abdundbzksjbxx-xybm searchXtype
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-abdundbzksjbxx-xybm' and FIELD_NAME='main-datamodelcolumn-searchXtype';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdundbzksjbxx-xybm(main-datamodelcolumn-searchXtype 搜索渲染类型)，跳过更新');
			ELSE
				update lowcode_model_column set search_xtype = 'multi-tree'
				where id = 'main-abdundbzksjbxx-xybm';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1898972101361545217','1898972096462598147','upgrade','update','column','main-abdundbzksjbxx-xybm','main-datamodelcolumn-searchXtype','{"变更前":{"main-datamodelcolumn-columnJavaname":"xybm","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XYBM","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":180,"main-datamodelcolumn-columnLabel":"学院编码","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":25,"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnDict":"dept_college","main-datamodelcolumn-columnXtype":"select-tree"},"变更后":{"main-datamodelcolumn-searchXtype":"multi-tree"}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1898972101361545217');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdundbzksjbxx-xybm(main-datamodelcolumn-searchXtype 搜索渲染类型),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.9',internal_version='0' WHERE model_id ='main-abdundbzksjbxx';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdundbzksjbxx成功,模型版本号更新为:V1.0.9,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-abdundbzkstccj ↓----------------------------------------------
--模型：本专科生体测成绩 ABD_UND_BZKSTCCJ main-abdundbzkstccj
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdundbzkstccj'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdundbzkstccj版本号:'||v_version);
	IF v_version >= 'V1.0.2.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdundbzkstccj的本次增量版本号:V1.0.2,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1898972096462598148','main-abdundbzkstccj','V1.0.2','0','upgrade','update','本专科生体测成绩','jcsj','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1898972096462598148');
		-- 新增【字段扩展】：main-abdundbzkstccj-xn
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdundbzkstccj',0,'main-abdundbzkstccj-xn','main-abdundbzkstccj-xn',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdundbzkstccj-xn');
		-- 新增【字段】：main-abdundbzkstccj-xn
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'schoolYear','main-abdundbzkstccj',1,0,'xn',0,'text','学年',30,0,'main-abdundbzkstccj-xn',0,'text',0,1,'java.lang.String',1,'XN',7,12,1,1,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdundbzkstccj-xn');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_UND_BZKSTCCJ ADD XN VARCHAR2(30)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_UND_BZKSTCCJ.XN is ''学年''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972101420265472','1898972096462598148','upgrade','add','column','main-abdundbzkstccj-xn',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"schoolYear","main-datamodelcolumn-columnJavaname":"xn","main-datamodelcolumn-columnLabel":"学年","main-datamodelcolumn-columnWidth":30,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XN","main-datamodelcolumn-orderIndex":7,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972101420265472');
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.2',internal_version='0' WHERE model_id ='main-abdundbzkstccj';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdundbzkstccj成功,模型版本号更新为:V1.0.2,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-abdundbzksxycj ↓----------------------------------------------
--模型：本专科生学业成绩 ABD_UND_BZKSXYCJ main-abdundbzksxycj
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdundbzksxycj'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdundbzksxycj版本号:'||v_version);
	IF v_version >= 'V1.0.2.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdundbzksxycj的本次增量版本号:V1.0.2,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1898972096462598149','main-abdundbzksxycj','V1.0.2','0','upgrade','update','本专科生学业成绩','jcsj','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1898972096462598149');
		-- 新增【字段扩展】：main-abdundbzksxycj-xn
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdundbzksxycj',0,'main-abdundbzksxycj-xn','main-abdundbzksxycj-xn',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdundbzksxycj-xn');
		-- 新增【字段】：main-abdundbzksxycj-xn
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'year','main-abdundbzksxycj',1,0,'xn',0,'text','学年',30,0,'main-abdundbzksxycj-xn',0,'text',0,1,'java.lang.String',1,'XN',14,12,1,1,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdundbzksxycj-xn');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_UND_BZKSXYCJ ADD XN VARCHAR2(30)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_UND_BZKSXYCJ.XN is ''学年''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972101466402816','1898972096462598149','upgrade','add','column','main-abdundbzksxycj-xn',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"year","main-datamodelcolumn-columnJavaname":"xn","main-datamodelcolumn-columnLabel":"学年","main-datamodelcolumn-columnWidth":30,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XN","main-datamodelcolumn-orderIndex":14,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972101466402816');
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.2',internal_version='0' WHERE model_id ='main-abdundbzksxycj';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdundbzksxycj成功,模型版本号更新为:V1.0.2,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-undsisch0520 ↓----------------------------------------------
--模型：每年学校困难生奖学金获奖情况 UND_SI_SCH_0520 main-undsisch0520
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-undsisch0520'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-undsisch0520版本号:'||v_version);
	IF v_version >= 'V1.0.2.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-undsisch0520的本次增量版本号:V1.0.2,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1898972096462598150','main-undsisch0520','V1.0.2','0','upgrade','update','每年学校困难生奖学金获奖情况','zb','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1898972096462598150');
		-- 新增【字段扩展】：main-undsisch0520-xn
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsisch0520',0,'main-undsisch0520-xn','main-undsisch0520-xn',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsisch0520-xn');
		-- 新增【字段】：main-undsisch0520-xn
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'schoolYear','main-undsisch0520',1,0,'xn',0,'text','学年',30,0,'main-undsisch0520-xn',0,'text',0,1,'java.lang.String',1,'XN',11,12,1,1,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsisch0520-xn');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE UND_SI_SCH_0520 ADD XN VARCHAR2(30)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_SCH_0520.XN is ''学年''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972101512540160','1898972096462598150','upgrade','add','column','main-undsisch0520-xn',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"schoolYear","main-datamodelcolumn-columnJavaname":"xn","main-datamodelcolumn-columnLabel":"学年","main-datamodelcolumn-columnWidth":30,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XN","main-datamodelcolumn-orderIndex":11,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972101512540160');
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.2',internal_version='0' WHERE model_id ='main-undsisch0520';
		DBMS_OUTPUT.PUT_LINE('升级模型main-undsisch0520成功,模型版本号更新为:V1.0.2,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-undsischmnbjyjsxfwcqk ↓----------------------------------------------
--模型：每年班级应届生学分完成情况 UND_SI_SCH_MNBJYJSXFWCQK main-undsischmnbjyjsxfwcqk
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-undsischmnbjyjsxfwcqk'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-undsischmnbjyjsxfwcqk版本号:'||v_version);
	IF v_version >= 'V1.0.0.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-undsischmnbjyjsxfwcqk的本次增量版本号:V1.0.0,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1898972096462598151','main-undsischmnbjyjsxfwcqk','V1.0.0','0','upgrade','add','每年班级应届生学分完成情况','zb','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1898972096462598151');
		-- 新增【模型】：main-undsischmnbjyjsxfwcqk
		insert into lowcode_model (model_app,publish_time,sys_flag,model_class,permit_ext_flag,extend_flag,model_label,model_name,model_table,model_classify,virtual_model,biz_model,order_index,id,status,create_by,create_time,update_by,update_time)
		select 'main',TIMESTAMP '2025-01-16 16:23:30',0,'com.wisedu.lowcode4j.main.po.Undsischmnbjyjsxfwcqk',1,1,'每年班级应届生学分完成情况','undsischmnbjyjsxfwcqk','UND_SI_SCH_MNBJYJSXFWCQK','SCHOOL_XSFZ_ZB',1,0,788,'main-undsischmnbjyjsxfwcqk',1,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model
			where id = 'main-undsischmnbjyjsxfwcqk');
		BEGIN
		EXECUTE IMMEDIATE 'CREATE TABLE UND_SI_SCH_MNBJYJSXFWCQK(ID VARCHAR2(100) DEFAULT SYS_GUID() primary key ,CREATE_BY VARCHAR2(100),CREATE_TIME DATE,UPDATE_BY VARCHAR2(100),UPDATE_TIME DATE)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-955) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on table UND_SI_SCH_MNBJYJSXFWCQK is ''每年班级应届生学分完成情况''';
		END;
		-- 新增【模型扩展】：main-undsischmnbjyjsxfwcqk
		insert into t_da_model_extend (model_encoding,access_level,model_id,model_version,model_enable,entity_id,model_type,metric_id,use_num,theme_code,internal_version,id,create_by,create_time,update_by,update_time)
		select 'UND_SI_SCH_0614','1','main-undsischmnbjyjsxfwcqk','V1.0.0','1','SCHOOL','zb',NULL,0,'UND','0','main-undsischmnbjyjsxfwcqk','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_extend
			where id = 'main-undsischmnbjyjsxfwcqk');
		-- 新增【模型使用范围】:1737013784121001243
		insert into t_da_model_theme (model_id,theme_code,id,create_by,create_time,update_by,update_time)
		select 'main-undsischmnbjyjsxfwcqk','UND','1737013784121001243','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_theme
			where id = '1737013784121001243');
		-- 新增【字段扩展】：main-undsischmnbjyjsxfwcqk-bjbh
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsischmnbjyjsxfwcqk',0,'main-undsischmnbjyjsxfwcqk-bjbh','main-undsischmnbjyjsxfwcqk-bjbh',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsischmnbjyjsxfwcqk-bjbh');
		-- 新增【字段扩展】：main-undsischmnbjyjsxfwcqk-createBy
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsischmnbjyjsxfwcqk',0,'main-undsischmnbjyjsxfwcqk-createBy','main-undsischmnbjyjsxfwcqk-createBy',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsischmnbjyjsxfwcqk-createBy');
		-- 新增【字段扩展】：main-undsischmnbjyjsxfwcqk-createTime
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsischmnbjyjsxfwcqk',0,'main-undsischmnbjyjsxfwcqk-createTime','main-undsischmnbjyjsxfwcqk-createTime',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsischmnbjyjsxfwcqk-createTime');
		-- 新增【字段扩展】：main-undsischmnbjyjsxfwcqk-ddbyyqxsrs
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsischmnbjyjsxfwcqk',0,'main-undsischmnbjyjsxfwcqk-ddbyyqxsrs','main-undsischmnbjyjsxfwcqk-ddbyyqxsrs',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsischmnbjyjsxfwcqk-ddbyyqxsrs');
		-- 新增【字段扩展】：main-undsischmnbjyjsxfwcqk-id
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,primary_column_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsischmnbjyjsxfwcqk',0,'main-undsischmnbjyjsxfwcqk-id','main-undsischmnbjyjsxfwcqk-id',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsischmnbjyjsxfwcqk-id');
		-- 新增【字段扩展】：main-undsischmnbjyjsxfwcqk-updateBy
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsischmnbjyjsxfwcqk',0,'main-undsischmnbjyjsxfwcqk-updateBy','main-undsischmnbjyjsxfwcqk-updateBy',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsischmnbjyjsxfwcqk-updateBy');
		-- 新增【字段扩展】：main-undsischmnbjyjsxfwcqk-updateTime
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsischmnbjyjsxfwcqk',0,'main-undsischmnbjyjsxfwcqk-updateTime','main-undsischmnbjyjsxfwcqk-updateTime',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsischmnbjyjsxfwcqk-updateTime');
		-- 新增【字段扩展】：main-undsischmnbjyjsxfwcqk-wdbyyqxfrs
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsischmnbjyjsxfwcqk',0,'main-undsischmnbjyjsxfwcqk-wdbyyqxfrs','main-undsischmnbjyjsxfwcqk-wdbyyqxfrs',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsischmnbjyjsxfwcqk-wdbyyqxfrs');
		-- 新增【字段扩展】：main-undsischmnbjyjsxfwcqk-xnxq
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsischmnbjyjsxfwcqk',0,'main-undsischmnbjyjsxfwcqk-xnxq','main-undsischmnbjyjsxfwcqk-xnxq',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsischmnbjyjsxfwcqk-xnxq');
		-- 新增【字段扩展】：main-undsischmnbjyjsxfwcqk-xybm
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsischmnbjyjsxfwcqk',0,'main-undsischmnbjyjsxfwcqk-xybm','main-undsischmnbjyjsxfwcqk-xybm',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsischmnbjyjsxfwcqk-xybm');
		-- 新增【字段】：main-undsischmnbjyjsxfwcqk-bjbh
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-undsischmnbjyjsxfwcqk',1,0,'bjbh',0,'班级编号',50,0,'main-undsischmnbjyjsxfwcqk-bjbh',0,'text',0,1,'java.lang.String',1,'BJBH',1,12,1,1,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsischmnbjyjsxfwcqk-bjbh');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE UND_SI_SCH_MNBJYJSXFWCQK ADD BJBH VARCHAR2(50)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_SCH_MNBJYJSXFWCQK.BJBH is ''班级编号''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972101571260416','1898972096462598151','upgrade','add','column','main-undsischmnbjyjsxfwcqk-bjbh',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"bjbh","main-datamodelcolumn-columnLabel":"班级编号","main-datamodelcolumn-columnWidth":50,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"BJBH","main-datamodelcolumn-orderIndex":1,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972101571260416');
		-- 新增【字段】：main-undsischmnbjyjsxfwcqk-createBy
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-undsischmnbjyjsxfwcqk',1,'createBy',0,'创建人',100,0,'main-undsischmnbjyjsxfwcqk-createBy',1,0,1,'java.lang.String',0,0,1,'CREATE_BY',0,301,12,1,0,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsischmnbjyjsxfwcqk-createBy');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_SCH_MNBJYJSXFWCQK.CREATE_BY is ''创建人''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972101579649024','1898972096462598151','upgrade','add','column','main-undsischmnbjyjsxfwcqk-createBy',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"createBy","main-datamodelcolumn-columnLabel":"创建人","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"CREATE_BY","main-datamodelcolumn-orderIndex":301,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972101579649024');
		-- 新增【字段】：main-undsischmnbjyjsxfwcqk-createTime
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-undsischmnbjyjsxfwcqk',1,'createTime',0,'创建时间',0,0,'main-undsischmnbjyjsxfwcqk-createTime',1,'date-full',0,1,'java.util.Date',0,0,1,'CREATE_TIME',0,302,93,1,0,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsischmnbjyjsxfwcqk-createTime');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_SCH_MNBJYJSXFWCQK.CREATE_TIME is ''创建时间''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972101592231936','1898972096462598151','upgrade','add','column','main-undsischmnbjyjsxfwcqk-createTime',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"createTime","main-datamodelcolumn-columnLabel":"创建时间","main-datamodelcolumn-columnWidth":0,"main-datamodelcolumn-columnXtype":"date-full","main-datamodelcolumn-columnJavatype":"java.util.Date","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"CREATE_TIME","main-datamodelcolumn-orderIndex":302,"main-datamodelcolumn-columnDbtype":93}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972101592231936');
		-- 新增【字段】：main-undsischmnbjyjsxfwcqk-ddbyyqxsrs
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'number','main-undsischmnbjyjsxfwcqk',1,0,'ddbyyqxsrs',0,'达到毕业要求学分人数',10,0,'main-undsischmnbjyjsxfwcqk-ddbyyqxsrs',0,'number',0,1,'java.lang.Integer',1,'DDBYYQXSRS',4,4,1,1,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsischmnbjyjsxfwcqk-ddbyyqxsrs');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE UND_SI_SCH_MNBJYJSXFWCQK ADD DDBYYQXSRS NUMBER(10)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_SCH_MNBJYJSXFWCQK.DDBYYQXSRS is ''达到毕业要求学分人数''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972101600620544','1898972096462598151','upgrade','add','column','main-undsischmnbjyjsxfwcqk-ddbyyqxsrs',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"number","main-datamodelcolumn-columnJavaname":"ddbyyqxsrs","main-datamodelcolumn-columnLabel":"达到毕业要求学分人数","main-datamodelcolumn-columnWidth":10,"main-datamodelcolumn-columnXtype":"number","main-datamodelcolumn-columnJavatype":"java.lang.Integer","main-datamodelcolumn-columnDbname":"DDBYYQXSRS","main-datamodelcolumn-orderIndex":4,"main-datamodelcolumn-columnDbtype":4}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972101600620544');
		-- 新增【字段】：main-undsischmnbjyjsxfwcqk-id
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-undsischmnbjyjsxfwcqk',1,'id',0,'ID',100,0,'main-undsischmnbjyjsxfwcqk-id',1,0,1,'java.lang.String',0,0,1,'ID',0,300,12,1,0,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsischmnbjyjsxfwcqk-id');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_SCH_MNBJYJSXFWCQK.ID is ''ID''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972101663535104','1898972096462598151','upgrade','add','column','main-undsischmnbjyjsxfwcqk-id',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"id","main-datamodelcolumn-columnLabel":"ID","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"ID","main-datamodelcolumn-orderIndex":300,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972101663535104');
		-- 新增【字段】：main-undsischmnbjyjsxfwcqk-updateBy
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-undsischmnbjyjsxfwcqk',1,'updateBy',0,'更新人',100,0,'main-undsischmnbjyjsxfwcqk-updateBy',1,0,1,'java.lang.String',0,0,1,'UPDATE_BY',0,303,12,1,0,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsischmnbjyjsxfwcqk-updateBy');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_SCH_MNBJYJSXFWCQK.UPDATE_BY is ''更新人''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972101680312320','1898972096462598151','upgrade','add','column','main-undsischmnbjyjsxfwcqk-updateBy',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"updateBy","main-datamodelcolumn-columnLabel":"更新人","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"UPDATE_BY","main-datamodelcolumn-orderIndex":303,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972101680312320');
		-- 新增【字段】：main-undsischmnbjyjsxfwcqk-updateTime
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-undsischmnbjyjsxfwcqk',1,'updateTime',0,'更新时间',0,0,'main-undsischmnbjyjsxfwcqk-updateTime',1,'date-full',0,1,'java.util.Date',0,0,1,'UPDATE_TIME',0,304,93,1,0,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsischmnbjyjsxfwcqk-updateTime');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_SCH_MNBJYJSXFWCQK.UPDATE_TIME is ''更新时间''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972101697089536','1898972096462598151','upgrade','add','column','main-undsischmnbjyjsxfwcqk-updateTime',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"updateTime","main-datamodelcolumn-columnLabel":"更新时间","main-datamodelcolumn-columnWidth":0,"main-datamodelcolumn-columnXtype":"date-full","main-datamodelcolumn-columnJavatype":"java.util.Date","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"UPDATE_TIME","main-datamodelcolumn-orderIndex":304,"main-datamodelcolumn-columnDbtype":93}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972101697089536');
		-- 新增【字段】：main-undsischmnbjyjsxfwcqk-wdbyyqxfrs
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'number','main-undsischmnbjyjsxfwcqk',1,0,'wdbyyqxfrs',0,'未达毕业要求学分人数',10,0,'main-undsischmnbjyjsxfwcqk-wdbyyqxfrs',0,'number',0,1,'java.lang.Integer',1,'WDBYYQXFRS',5,4,1,1,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsischmnbjyjsxfwcqk-wdbyyqxfrs');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE UND_SI_SCH_MNBJYJSXFWCQK ADD WDBYYQXFRS NUMBER(10)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_SCH_MNBJYJSXFWCQK.WDBYYQXFRS is ''未达毕业要求学分人数''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972101713866752','1898972096462598151','upgrade','add','column','main-undsischmnbjyjsxfwcqk-wdbyyqxfrs',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"number","main-datamodelcolumn-columnJavaname":"wdbyyqxfrs","main-datamodelcolumn-columnLabel":"未达毕业要求学分人数","main-datamodelcolumn-columnWidth":10,"main-datamodelcolumn-columnXtype":"number","main-datamodelcolumn-columnJavatype":"java.lang.Integer","main-datamodelcolumn-columnDbname":"WDBYYQXFRS","main-datamodelcolumn-orderIndex":5,"main-datamodelcolumn-columnDbtype":4}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972101713866752');
		-- 新增【字段】：main-undsischmnbjyjsxfwcqk-xnxq
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'schoolYearTerm','main-undsischmnbjyjsxfwcqk',1,0,'xnxq',0,'学年学期',50,0,'main-undsischmnbjyjsxfwcqk-xnxq',0,'text',0,1,'java.lang.String',1,'XNXQ',3,12,1,1,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsischmnbjyjsxfwcqk-xnxq');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE UND_SI_SCH_MNBJYJSXFWCQK ADD XNXQ VARCHAR2(50)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_SCH_MNBJYJSXFWCQK.XNXQ is ''学年学期''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972101722255360','1898972096462598151','upgrade','add','column','main-undsischmnbjyjsxfwcqk-xnxq',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"schoolYearTerm","main-datamodelcolumn-columnJavaname":"xnxq","main-datamodelcolumn-columnLabel":"学年学期","main-datamodelcolumn-columnWidth":50,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XNXQ","main-datamodelcolumn-orderIndex":3,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972101722255360');
		-- 新增【字段】：main-undsischmnbjyjsxfwcqk-xybm
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-undsischmnbjyjsxfwcqk',1,0,'xybm',0,'学院编码',50,0,'main-undsischmnbjyjsxfwcqk-xybm',0,'text',0,1,'java.lang.String',1,'XYBM',2,12,1,1,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsischmnbjyjsxfwcqk-xybm');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE UND_SI_SCH_MNBJYJSXFWCQK ADD XYBM VARCHAR2(50)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_SCH_MNBJYJSXFWCQK.XYBM is ''学院编码''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972101739032576','1898972096462598151','upgrade','add','column','main-undsischmnbjyjsxfwcqk-xybm',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"xybm","main-datamodelcolumn-columnLabel":"学院编码","main-datamodelcolumn-columnWidth":50,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XYBM","main-datamodelcolumn-orderIndex":2,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972101739032576');
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-undsischmnbjyjsxfwcqk' and FIELD_NAME='main-damodelcolumn-entityTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-undsischmnbjyjsxfwcqk(main-damodelcolumn-entityTag 是否实体标识)，跳过更新');
			ELSE
				update t_da_model_column_extend set entity_tag = '0'
				where model_id = 'main-undsischmnbjyjsxfwcqk';
				update t_da_model_column_extend set entity_tag = '1'
				where id in ('main-undsischmnbjyjsxfwcqk-bjbh');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1898972101793558528','1898972096462598151','upgrade','update','model','main-undsischmnbjyjsxfwcqk','main-damodelcolumn-entityTag','{"变更前":{"main-damodelcolumn-entityTag":""},"变更后":{"main-damodelcolumn-entityTag":"班级编号"}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1898972101793558528');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-undsischmnbjyjsxfwcqk(main-damodelcolumn-entityTag 是否实体标识),sql执行错误:' || SQLERRM);
		END;
		
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-undsischmnbjyjsxfwcqk' and FIELD_NAME='main-damodelcolumn-timeTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-undsischmnbjyjsxfwcqk(main-damodelcolumn-timeTag 是否时点标识字段)，跳过更新');
			ELSE
				update t_da_model_column_extend set time_tag = '0'
				where model_id = 'main-undsischmnbjyjsxfwcqk';
				update t_da_model_column_extend set time_tag = '1'
				where id in ('main-undsischmnbjyjsxfwcqk-xnxq');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1898972101797752832','1898972096462598151','upgrade','update','model','main-undsischmnbjyjsxfwcqk','main-damodelcolumn-timeTag','{"变更前":{"main-damodelcolumn-timeTag":""},"变更后":{"main-damodelcolumn-timeTag":"学年学期"}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1898972101797752832');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-undsischmnbjyjsxfwcqk(main-damodelcolumn-timeTag 是否时点标识字段),sql执行错误:' || SQLERRM);
		END;
		
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-undsischmnbjyjsxfwcqk' and FIELD_NAME='main-damodelcolumn-valueTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-undsischmnbjyjsxfwcqk(main-damodelcolumn-valueTag 是否指标值字段)，跳过更新');
			ELSE
				update t_da_model_column_extend set value_tag = '0'
				where model_id = 'main-undsischmnbjyjsxfwcqk';
				update t_da_model_column_extend set value_tag = '1'
				where id in ('main-undsischmnbjyjsxfwcqk-ddbyyqxsrs','main-undsischmnbjyjsxfwcqk-wdbyyqxfrs');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1898972101801947136','1898972096462598151','upgrade','update','model','main-undsischmnbjyjsxfwcqk','main-damodelcolumn-valueTag','{"变更前":{"main-damodelcolumn-valueTag":""},"变更后":{"main-damodelcolumn-valueTag":"达到毕业要求学分人数,未达毕业要求学分人数"}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1898972101801947136');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-undsischmnbjyjsxfwcqk(main-damodelcolumn-valueTag 是否指标值字段),sql执行错误:' || SQLERRM);
		END;
		
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-undsischmnbjyjsxfwcqk' and FIELD_NAME='main-damodelcolumn-primaryColumnTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-undsischmnbjyjsxfwcqk(main-damodelcolumn-primaryColumnTag 是否主键字段)，跳过更新');
			ELSE
				update t_da_model_column_extend set primary_column_tag = '0'
				where model_id = 'main-undsischmnbjyjsxfwcqk';
				update t_da_model_column_extend set primary_column_tag = '1'
				where id in ('main-undsischmnbjyjsxfwcqk-id');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1898972101806141440','1898972096462598151','upgrade','update','model','main-undsischmnbjyjsxfwcqk','main-damodelcolumn-primaryColumnTag','{"变更前":{"main-damodelcolumn-primaryColumnTag":""},"变更后":{"main-damodelcolumn-primaryColumnTag":"ID"}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1898972101806141440');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-undsischmnbjyjsxfwcqk(main-damodelcolumn-primaryColumnTag 是否主键字段),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.0',internal_version='0' WHERE model_id ='main-undsischmnbjyjsxfwcqk';
		DBMS_OUTPUT.PUT_LINE('升级模型main-undsischmnbjyjsxfwcqk成功,模型版本号更新为:V1.0.0,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-undsischmxqbjxsgkqk ↓----------------------------------------------
--模型：每学期班级学生挂科情况 UND_SI_SCH_MXQBJXSGKQK main-undsischmxqbjxsgkqk
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-undsischmxqbjxsgkqk'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-undsischmxqbjxsgkqk版本号:'||v_version);
	IF v_version >= 'V1.0.0.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-undsischmxqbjxsgkqk的本次增量版本号:V1.0.0,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1898972096462598152','main-undsischmxqbjxsgkqk','V1.0.0','0','upgrade','add','每学期班级学生挂科情况','zb','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1898972096462598152');
		-- 新增【模型】：main-undsischmxqbjxsgkqk
		insert into lowcode_model (model_app,publish_time,sys_flag,model_class,permit_ext_flag,extend_flag,model_label,model_name,model_table,model_classify,virtual_model,biz_model,order_index,id,status,create_by,create_time,update_by,update_time)
		select 'main',TIMESTAMP '2025-01-17 09:17:54',0,'com.wisedu.lowcode4j.main.po.Undsischmxqbjxsgkqk',1,1,'每学期班级学生挂科情况','undsischmxqbjxsgkqk','UND_SI_SCH_MXQBJXSGKQK','SCHOOL_XSFZ_ZB',1,0,790,'main-undsischmxqbjxsgkqk',1,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model
			where id = 'main-undsischmxqbjxsgkqk');
		BEGIN
		EXECUTE IMMEDIATE 'CREATE TABLE UND_SI_SCH_MXQBJXSGKQK(ID VARCHAR2(100) DEFAULT SYS_GUID() primary key ,CREATE_BY VARCHAR2(100),CREATE_TIME DATE,UPDATE_BY VARCHAR2(100),UPDATE_TIME DATE)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-955) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on table UND_SI_SCH_MXQBJXSGKQK is ''每学期班级学生挂科情况''';
		END;
		-- 新增【模型扩展】：main-undsischmxqbjxsgkqk
		insert into t_da_model_extend (model_encoding,access_level,model_id,model_version,model_enable,entity_id,model_type,metric_id,use_num,theme_code,internal_version,id,create_by,create_time,update_by,update_time)
		select 'UND_SI_SCH_0615','1','main-undsischmxqbjxsgkqk','V1.0.0','1','SCHOOL','zb',NULL,0,'UND','0','main-undsischmxqbjxsgkqk','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_extend
			where id = 'main-undsischmxqbjxsgkqk');
		-- 新增【模型使用范围】:1737076436492001243
		insert into t_da_model_theme (model_id,theme_code,id,create_by,create_time,update_by,update_time)
		select 'main-undsischmxqbjxsgkqk','UND','1737076436492001243','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_theme
			where id = '1737076436492001243');
		-- 新增【字段扩展】：main-undsischmxqbjxsgkqk-bjbh
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsischmxqbjxsgkqk',0,'main-undsischmxqbjxsgkqk-bjbh','main-undsischmxqbjxsgkqk-bjbh',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsischmxqbjxsgkqk-bjbh');
		-- 新增【字段扩展】：main-undsischmxqbjxsgkqk-bxkgkrs
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsischmxqbjxsgkqk',0,'main-undsischmxqbjxsgkqk-bxkgkrs','main-undsischmxqbjxsgkqk-bxkgkrs',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsischmxqbjxsgkqk-bxkgkrs');
		-- 新增【字段扩展】：main-undsischmxqbjxsgkqk-createBy
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsischmxqbjxsgkqk',0,'main-undsischmxqbjxsgkqk-createBy','main-undsischmxqbjxsgkqk-createBy',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsischmxqbjxsgkqk-createBy');
		-- 新增【字段扩展】：main-undsischmxqbjxsgkqk-createTime
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsischmxqbjxsgkqk',0,'main-undsischmxqbjxsgkqk-createTime','main-undsischmxqbjxsgkqk-createTime',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsischmxqbjxsgkqk-createTime');
		-- 新增【字段扩展】：main-undsischmxqbjxsgkqk-id
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,primary_column_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsischmxqbjxsgkqk',0,'main-undsischmxqbjxsgkqk-id','main-undsischmxqbjxsgkqk-id',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsischmxqbjxsgkqk-id');
		-- 新增【字段扩展】：main-undsischmxqbjxsgkqk-updateBy
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsischmxqbjxsgkqk',0,'main-undsischmxqbjxsgkqk-updateBy','main-undsischmxqbjxsgkqk-updateBy',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsischmxqbjxsgkqk-updateBy');
		-- 新增【字段扩展】：main-undsischmxqbjxsgkqk-updateTime
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsischmxqbjxsgkqk',0,'main-undsischmxqbjxsgkqk-updateTime','main-undsischmxqbjxsgkqk-updateTime',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsischmxqbjxsgkqk-updateTime');
		-- 新增【字段扩展】：main-undsischmxqbjxsgkqk-xnxq
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsischmxqbjxsgkqk',0,'main-undsischmxqbjxsgkqk-xnxq','main-undsischmxqbjxsgkqk-xnxq',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsischmxqbjxsgkqk-xnxq');
		-- 新增【字段扩展】：main-undsischmxqbjxsgkqk-xybm
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsischmxqbjxsgkqk',0,'main-undsischmxqbjxsgkqk-xybm','main-undsischmxqbjxsgkqk-xybm',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsischmxqbjxsgkqk-xybm');
		-- 新增【字段】：main-undsischmxqbjxsgkqk-bjbh
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-undsischmxqbjxsgkqk',1,0,'bjbh',0,'班级编号',50,0,'main-undsischmxqbjxsgkqk-bjbh',0,'text',0,1,'java.lang.String',1,'BJBH',1,12,1,1,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsischmxqbjxsgkqk-bjbh');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE UND_SI_SCH_MXQBJXSGKQK ADD BJBH VARCHAR2(50)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_SCH_MXQBJXSGKQK.BJBH is ''班级编号''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972101839695872','1898972096462598152','upgrade','add','column','main-undsischmxqbjxsgkqk-bjbh',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"bjbh","main-datamodelcolumn-columnLabel":"班级编号","main-datamodelcolumn-columnWidth":50,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"BJBH","main-datamodelcolumn-orderIndex":1,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972101839695872');
		-- 新增【字段】：main-undsischmxqbjxsgkqk-bxkgkrs
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'number','main-undsischmxqbjxsgkqk',1,0,'bxkgkrs',0,'必修课挂科人数',10,0,'main-undsischmxqbjxsgkqk-bxkgkrs',0,'number',0,1,'java.lang.Integer',1,'BXKGKRS',4,4,1,1,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsischmxqbjxsgkqk-bxkgkrs');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE UND_SI_SCH_MXQBJXSGKQK ADD BXKGKRS NUMBER(10)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_SCH_MXQBJXSGKQK.BXKGKRS is ''必修课挂科人数''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972101848084480','1898972096462598152','upgrade','add','column','main-undsischmxqbjxsgkqk-bxkgkrs',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"number","main-datamodelcolumn-columnJavaname":"bxkgkrs","main-datamodelcolumn-columnLabel":"必修课挂科人数","main-datamodelcolumn-columnWidth":10,"main-datamodelcolumn-columnXtype":"number","main-datamodelcolumn-columnJavatype":"java.lang.Integer","main-datamodelcolumn-columnDbname":"BXKGKRS","main-datamodelcolumn-orderIndex":4,"main-datamodelcolumn-columnDbtype":4}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972101848084480');
		-- 新增【字段】：main-undsischmxqbjxsgkqk-createBy
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-undsischmxqbjxsgkqk',1,'createBy',0,'创建人',100,0,'main-undsischmxqbjxsgkqk-createBy',1,0,1,'java.lang.String',0,0,1,'CREATE_BY',0,301,12,1,0,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsischmxqbjxsgkqk-createBy');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_SCH_MXQBJXSGKQK.CREATE_BY is ''创建人''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972101856473088','1898972096462598152','upgrade','add','column','main-undsischmxqbjxsgkqk-createBy',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"createBy","main-datamodelcolumn-columnLabel":"创建人","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"CREATE_BY","main-datamodelcolumn-orderIndex":301,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972101856473088');
		-- 新增【字段】：main-undsischmxqbjxsgkqk-createTime
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-undsischmxqbjxsgkqk',1,'createTime',0,'创建时间',0,0,'main-undsischmxqbjxsgkqk-createTime',1,'date-full',0,1,'java.util.Date',0,0,1,'CREATE_TIME',0,302,93,1,0,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsischmxqbjxsgkqk-createTime');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_SCH_MXQBJXSGKQK.CREATE_TIME is ''创建时间''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972101864861696','1898972096462598152','upgrade','add','column','main-undsischmxqbjxsgkqk-createTime',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"createTime","main-datamodelcolumn-columnLabel":"创建时间","main-datamodelcolumn-columnWidth":0,"main-datamodelcolumn-columnXtype":"date-full","main-datamodelcolumn-columnJavatype":"java.util.Date","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"CREATE_TIME","main-datamodelcolumn-orderIndex":302,"main-datamodelcolumn-columnDbtype":93}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972101864861696');
		-- 新增【字段】：main-undsischmxqbjxsgkqk-id
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-undsischmxqbjxsgkqk',1,'id',0,'ID',100,0,'main-undsischmxqbjxsgkqk-id',1,0,1,'java.lang.String',0,0,1,'ID',0,300,12,1,0,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsischmxqbjxsgkqk-id');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_SCH_MXQBJXSGKQK.ID is ''ID''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972101877444608','1898972096462598152','upgrade','add','column','main-undsischmxqbjxsgkqk-id',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"id","main-datamodelcolumn-columnLabel":"ID","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"ID","main-datamodelcolumn-orderIndex":300,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972101877444608');
		-- 新增【字段】：main-undsischmxqbjxsgkqk-updateBy
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-undsischmxqbjxsgkqk',1,'updateBy',0,'更新人',100,0,'main-undsischmxqbjxsgkqk-updateBy',1,0,1,'java.lang.String',0,0,1,'UPDATE_BY',0,303,12,1,0,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsischmxqbjxsgkqk-updateBy');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_SCH_MXQBJXSGKQK.UPDATE_BY is ''更新人''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972101885833216','1898972096462598152','upgrade','add','column','main-undsischmxqbjxsgkqk-updateBy',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"updateBy","main-datamodelcolumn-columnLabel":"更新人","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"UPDATE_BY","main-datamodelcolumn-orderIndex":303,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972101885833216');
		-- 新增【字段】：main-undsischmxqbjxsgkqk-updateTime
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-undsischmxqbjxsgkqk',1,'updateTime',0,'更新时间',0,0,'main-undsischmxqbjxsgkqk-updateTime',1,'date-full',0,1,'java.util.Date',0,0,1,'UPDATE_TIME',0,304,93,1,0,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsischmxqbjxsgkqk-updateTime');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_SCH_MXQBJXSGKQK.UPDATE_TIME is ''更新时间''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972101898416128','1898972096462598152','upgrade','add','column','main-undsischmxqbjxsgkqk-updateTime',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"updateTime","main-datamodelcolumn-columnLabel":"更新时间","main-datamodelcolumn-columnWidth":0,"main-datamodelcolumn-columnXtype":"date-full","main-datamodelcolumn-columnJavatype":"java.util.Date","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"UPDATE_TIME","main-datamodelcolumn-orderIndex":304,"main-datamodelcolumn-columnDbtype":93}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972101898416128');
		-- 新增【字段】：main-undsischmxqbjxsgkqk-xnxq
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'schoolYearTerm','main-undsischmxqbjxsgkqk',1,0,'xnxq',0,'学年学期',20,0,'main-undsischmxqbjxsgkqk-xnxq',0,'text',0,1,'java.lang.String',1,'XNXQ',3,12,1,1,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsischmxqbjxsgkqk-xnxq');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE UND_SI_SCH_MXQBJXSGKQK ADD XNXQ VARCHAR2(20)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_SCH_MXQBJXSGKQK.XNXQ is ''学年学期''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972101910999040','1898972096462598152','upgrade','add','column','main-undsischmxqbjxsgkqk-xnxq',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"schoolYearTerm","main-datamodelcolumn-columnJavaname":"xnxq","main-datamodelcolumn-columnLabel":"学年学期","main-datamodelcolumn-columnWidth":20,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XNXQ","main-datamodelcolumn-orderIndex":3,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972101910999040');
		-- 新增【字段】：main-undsischmxqbjxsgkqk-xybm
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-undsischmxqbjxsgkqk',1,0,'xybm',0,'学院编码',50,0,'main-undsischmxqbjxsgkqk-xybm',0,'text',0,1,'java.lang.String',1,'XYBM',2,12,1,1,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsischmxqbjxsgkqk-xybm');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE UND_SI_SCH_MXQBJXSGKQK ADD XYBM VARCHAR2(50)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_SCH_MXQBJXSGKQK.XYBM is ''学院编码''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972101919387648','1898972096462598152','upgrade','add','column','main-undsischmxqbjxsgkqk-xybm',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"xybm","main-datamodelcolumn-columnLabel":"学院编码","main-datamodelcolumn-columnWidth":50,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XYBM","main-datamodelcolumn-orderIndex":2,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972101919387648');
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-undsischmxqbjxsgkqk' and FIELD_NAME='main-damodelcolumn-entityTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-undsischmxqbjxsgkqk(main-damodelcolumn-entityTag 是否实体标识)，跳过更新');
			ELSE
				update t_da_model_column_extend set entity_tag = '0'
				where model_id = 'main-undsischmxqbjxsgkqk';
				update t_da_model_column_extend set entity_tag = '1'
				where id in ('main-undsischmxqbjxsgkqk-bjbh');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1898972101948747776','1898972096462598152','upgrade','update','model','main-undsischmxqbjxsgkqk','main-damodelcolumn-entityTag','{"变更前":{"main-damodelcolumn-entityTag":""},"变更后":{"main-damodelcolumn-entityTag":"班级编号"}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1898972101948747776');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-undsischmxqbjxsgkqk(main-damodelcolumn-entityTag 是否实体标识),sql执行错误:' || SQLERRM);
		END;
		
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-undsischmxqbjxsgkqk' and FIELD_NAME='main-damodelcolumn-timeTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-undsischmxqbjxsgkqk(main-damodelcolumn-timeTag 是否时点标识字段)，跳过更新');
			ELSE
				update t_da_model_column_extend set time_tag = '0'
				where model_id = 'main-undsischmxqbjxsgkqk';
				update t_da_model_column_extend set time_tag = '1'
				where id in ('main-undsischmxqbjxsgkqk-xnxq');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1898972101957136384','1898972096462598152','upgrade','update','model','main-undsischmxqbjxsgkqk','main-damodelcolumn-timeTag','{"变更前":{"main-damodelcolumn-timeTag":""},"变更后":{"main-damodelcolumn-timeTag":"学年学期"}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1898972101957136384');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-undsischmxqbjxsgkqk(main-damodelcolumn-timeTag 是否时点标识字段),sql执行错误:' || SQLERRM);
		END;
		
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-undsischmxqbjxsgkqk' and FIELD_NAME='main-damodelcolumn-valueTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-undsischmxqbjxsgkqk(main-damodelcolumn-valueTag 是否指标值字段)，跳过更新');
			ELSE
				update t_da_model_column_extend set value_tag = '0'
				where model_id = 'main-undsischmxqbjxsgkqk';
				update t_da_model_column_extend set value_tag = '1'
				where id in ('main-undsischmxqbjxsgkqk-bxkgkrs');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1898972101957136385','1898972096462598152','upgrade','update','model','main-undsischmxqbjxsgkqk','main-damodelcolumn-valueTag','{"变更前":{"main-damodelcolumn-valueTag":""},"变更后":{"main-damodelcolumn-valueTag":"必修课挂科人数"}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1898972101957136385');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-undsischmxqbjxsgkqk(main-damodelcolumn-valueTag 是否指标值字段),sql执行错误:' || SQLERRM);
		END;
		
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-undsischmxqbjxsgkqk' and FIELD_NAME='main-damodelcolumn-primaryColumnTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-undsischmxqbjxsgkqk(main-damodelcolumn-primaryColumnTag 是否主键字段)，跳过更新');
			ELSE
				update t_da_model_column_extend set primary_column_tag = '0'
				where model_id = 'main-undsischmxqbjxsgkqk';
				update t_da_model_column_extend set primary_column_tag = '1'
				where id in ('main-undsischmxqbjxsgkqk-id');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1898972101961330688','1898972096462598152','upgrade','update','model','main-undsischmxqbjxsgkqk','main-damodelcolumn-primaryColumnTag','{"变更前":{"main-damodelcolumn-primaryColumnTag":""},"变更后":{"main-damodelcolumn-primaryColumnTag":"ID"}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1898972101961330688');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-undsischmxqbjxsgkqk(main-damodelcolumn-primaryColumnTag 是否主键字段),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.0',internal_version='0' WHERE model_id ='main-undsischmxqbjxsgkqk';
		DBMS_OUTPUT.PUT_LINE('升级模型main-undsischmxqbjxsgkqk成功,模型版本号更新为:V1.0.0,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-undsischmxqbjxswjcfqk ↓----------------------------------------------
--模型：每学期班级学生违纪处分情况 UND_SI_SCH_MXQBJXSWJCFQK main-undsischmxqbjxswjcfqk
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-undsischmxqbjxswjcfqk'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-undsischmxqbjxswjcfqk版本号:'||v_version);
	IF v_version >= 'V1.0.0.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-undsischmxqbjxswjcfqk的本次增量版本号:V1.0.0,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1898972096462598153','main-undsischmxqbjxswjcfqk','V1.0.0','0','upgrade','add','每学期班级学生违纪处分情况','zb','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1898972096462598153');
		-- 新增【模型】：main-undsischmxqbjxswjcfqk
		insert into lowcode_model (model_app,publish_time,sys_flag,model_class,permit_ext_flag,extend_flag,model_label,model_name,model_table,model_classify,virtual_model,biz_model,order_index,id,status,create_by,create_time,update_by,update_time)
		select 'main',TIMESTAMP '2025-01-17 10:35:17',0,'com.wisedu.lowcode4j.main.po.Undsischmxqbjxswjcfqk',1,1,'每学期班级学生违纪处分情况','undsischmxqbjxswjcfqk','UND_SI_SCH_MXQBJXSWJCFQK','SCHOOL_JCXX_ZB',1,0,791,'main-undsischmxqbjxswjcfqk',1,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model
			where id = 'main-undsischmxqbjxswjcfqk');
		BEGIN
		EXECUTE IMMEDIATE 'CREATE TABLE UND_SI_SCH_MXQBJXSWJCFQK(ID VARCHAR2(100) DEFAULT SYS_GUID() primary key ,CREATE_BY VARCHAR2(100),CREATE_TIME DATE,UPDATE_BY VARCHAR2(100),UPDATE_TIME DATE)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-955) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on table UND_SI_SCH_MXQBJXSWJCFQK is ''每学期班级学生违纪处分情况''';
		END;
		-- 新增【模型扩展】：main-undsischmxqbjxswjcfqk
		insert into t_da_model_extend (model_encoding,access_level,model_id,model_version,model_enable,entity_id,model_type,metric_id,use_num,theme_code,internal_version,id,create_by,create_time,update_by,update_time)
		select 'UND_SI_SCH_0616','1','main-undsischmxqbjxswjcfqk','V1.0.0','1','SCHOOL','zb',NULL,0,'UND','0','main-undsischmxqbjxswjcfqk','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_extend
			where id = 'main-undsischmxqbjxswjcfqk');
		-- 新增【模型使用范围】:1737081197006001243
		insert into t_da_model_theme (model_id,theme_code,id,create_by,create_time,update_by,update_time)
		select 'main-undsischmxqbjxswjcfqk','UND','1737081197006001243','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_theme
			where id = '1737081197006001243');
		-- 新增【字段扩展】：main-undsischmxqbjxswjcfqk-bjbh
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsischmxqbjxswjcfqk',0,'main-undsischmxqbjxswjcfqk-bjbh','main-undsischmxqbjxswjcfqk-bjbh',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsischmxqbjxswjcfqk-bjbh');
		-- 新增【字段扩展】：main-undsischmxqbjxswjcfqk-cfrc
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsischmxqbjxswjcfqk',0,'main-undsischmxqbjxswjcfqk-cfrc','main-undsischmxqbjxswjcfqk-cfrc',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsischmxqbjxswjcfqk-cfrc');
		-- 新增【字段扩展】：main-undsischmxqbjxswjcfqk-createBy
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsischmxqbjxswjcfqk',0,'main-undsischmxqbjxswjcfqk-createBy','main-undsischmxqbjxswjcfqk-createBy',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsischmxqbjxswjcfqk-createBy');
		-- 新增【字段扩展】：main-undsischmxqbjxswjcfqk-createTime
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsischmxqbjxswjcfqk',0,'main-undsischmxqbjxswjcfqk-createTime','main-undsischmxqbjxswjcfqk-createTime',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsischmxqbjxswjcfqk-createTime');
		-- 新增【字段扩展】：main-undsischmxqbjxswjcfqk-id
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,primary_column_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsischmxqbjxswjcfqk',0,'main-undsischmxqbjxswjcfqk-id','main-undsischmxqbjxswjcfqk-id',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsischmxqbjxswjcfqk-id');
		-- 新增【字段扩展】：main-undsischmxqbjxswjcfqk-updateBy
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsischmxqbjxswjcfqk',0,'main-undsischmxqbjxswjcfqk-updateBy','main-undsischmxqbjxswjcfqk-updateBy',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsischmxqbjxswjcfqk-updateBy');
		-- 新增【字段扩展】：main-undsischmxqbjxswjcfqk-updateTime
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsischmxqbjxswjcfqk',0,'main-undsischmxqbjxswjcfqk-updateTime','main-undsischmxqbjxswjcfqk-updateTime',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsischmxqbjxswjcfqk-updateTime');
		-- 新增【字段扩展】：main-undsischmxqbjxswjcfqk-xnxq
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsischmxqbjxswjcfqk',0,'main-undsischmxqbjxswjcfqk-xnxq','main-undsischmxqbjxswjcfqk-xnxq',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsischmxqbjxswjcfqk-xnxq');
		-- 新增【字段扩展】：main-undsischmxqbjxswjcfqk-xybm
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsischmxqbjxswjcfqk',0,'main-undsischmxqbjxswjcfqk-xybm','main-undsischmxqbjxswjcfqk-xybm',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsischmxqbjxswjcfqk-xybm');
		-- 新增【字段】：main-undsischmxqbjxswjcfqk-bjbh
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-undsischmxqbjxswjcfqk',1,0,'bjbh',0,'班级编号',50,0,'main-undsischmxqbjxswjcfqk-bjbh',0,'text',0,1,'java.lang.String',1,'BJBH',1,12,1,1,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsischmxqbjxswjcfqk-bjbh');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE UND_SI_SCH_MXQBJXSWJCFQK ADD BJBH VARCHAR2(50)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_SCH_MXQBJXSWJCFQK.BJBH is ''班级编号''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972101994885120','1898972096462598153','upgrade','add','column','main-undsischmxqbjxswjcfqk-bjbh',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"bjbh","main-datamodelcolumn-columnLabel":"班级编号","main-datamodelcolumn-columnWidth":50,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"BJBH","main-datamodelcolumn-orderIndex":1,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972101994885120');
		-- 新增【字段】：main-undsischmxqbjxswjcfqk-cfrc
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'number','main-undsischmxqbjxswjcfqk',1,0,'cfrc',0,'处分人次',10,0,'main-undsischmxqbjxswjcfqk-cfrc',0,'number',0,1,'java.lang.Integer',1,'CFRC',4,4,1,1,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsischmxqbjxswjcfqk-cfrc');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE UND_SI_SCH_MXQBJXSWJCFQK ADD CFRC NUMBER(10)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_SCH_MXQBJXSWJCFQK.CFRC is ''处分人次''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972102007468032','1898972096462598153','upgrade','add','column','main-undsischmxqbjxswjcfqk-cfrc',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"number","main-datamodelcolumn-columnJavaname":"cfrc","main-datamodelcolumn-columnLabel":"处分人次","main-datamodelcolumn-columnWidth":10,"main-datamodelcolumn-columnXtype":"number","main-datamodelcolumn-columnJavatype":"java.lang.Integer","main-datamodelcolumn-columnDbname":"CFRC","main-datamodelcolumn-orderIndex":4,"main-datamodelcolumn-columnDbtype":4}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972102007468032');
		-- 新增【字段】：main-undsischmxqbjxswjcfqk-createBy
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-undsischmxqbjxswjcfqk',1,'createBy',0,'创建人',100,0,'main-undsischmxqbjxswjcfqk-createBy',1,0,1,'java.lang.String',0,0,1,'CREATE_BY',0,301,12,1,0,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsischmxqbjxswjcfqk-createBy');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_SCH_MXQBJXSWJCFQK.CREATE_BY is ''创建人''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972102015856640','1898972096462598153','upgrade','add','column','main-undsischmxqbjxswjcfqk-createBy',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"createBy","main-datamodelcolumn-columnLabel":"创建人","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"CREATE_BY","main-datamodelcolumn-orderIndex":301,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972102015856640');
		-- 新增【字段】：main-undsischmxqbjxswjcfqk-createTime
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-undsischmxqbjxswjcfqk',1,'createTime',0,'创建时间',0,0,'main-undsischmxqbjxswjcfqk-createTime',1,'date-full',0,1,'java.util.Date',0,0,1,'CREATE_TIME',0,302,93,1,0,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsischmxqbjxswjcfqk-createTime');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_SCH_MXQBJXSWJCFQK.CREATE_TIME is ''创建时间''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972102024245248','1898972096462598153','upgrade','add','column','main-undsischmxqbjxswjcfqk-createTime',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"createTime","main-datamodelcolumn-columnLabel":"创建时间","main-datamodelcolumn-columnWidth":0,"main-datamodelcolumn-columnXtype":"date-full","main-datamodelcolumn-columnJavatype":"java.util.Date","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"CREATE_TIME","main-datamodelcolumn-orderIndex":302,"main-datamodelcolumn-columnDbtype":93}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972102024245248');
		-- 新增【字段】：main-undsischmxqbjxswjcfqk-id
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-undsischmxqbjxswjcfqk',1,'id',0,'ID',100,0,'main-undsischmxqbjxswjcfqk-id',1,0,1,'java.lang.String',0,0,1,'ID',0,300,12,1,0,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsischmxqbjxswjcfqk-id');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_SCH_MXQBJXSWJCFQK.ID is ''ID''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972102036828160','1898972096462598153','upgrade','add','column','main-undsischmxqbjxswjcfqk-id',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"id","main-datamodelcolumn-columnLabel":"ID","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"ID","main-datamodelcolumn-orderIndex":300,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972102036828160');
		-- 新增【字段】：main-undsischmxqbjxswjcfqk-updateBy
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-undsischmxqbjxswjcfqk',1,'updateBy',0,'更新人',100,0,'main-undsischmxqbjxswjcfqk-updateBy',1,0,1,'java.lang.String',0,0,1,'UPDATE_BY',0,303,12,1,0,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsischmxqbjxswjcfqk-updateBy');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_SCH_MXQBJXSWJCFQK.UPDATE_BY is ''更新人''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972102045216768','1898972096462598153','upgrade','add','column','main-undsischmxqbjxswjcfqk-updateBy',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"updateBy","main-datamodelcolumn-columnLabel":"更新人","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"UPDATE_BY","main-datamodelcolumn-orderIndex":303,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972102045216768');
		-- 新增【字段】：main-undsischmxqbjxswjcfqk-updateTime
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-undsischmxqbjxswjcfqk',1,'updateTime',0,'更新时间',0,0,'main-undsischmxqbjxswjcfqk-updateTime',1,'date-full',0,1,'java.util.Date',0,0,1,'UPDATE_TIME',0,304,93,1,0,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsischmxqbjxswjcfqk-updateTime');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_SCH_MXQBJXSWJCFQK.UPDATE_TIME is ''更新时间''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972102053605376','1898972096462598153','upgrade','add','column','main-undsischmxqbjxswjcfqk-updateTime',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"updateTime","main-datamodelcolumn-columnLabel":"更新时间","main-datamodelcolumn-columnWidth":0,"main-datamodelcolumn-columnXtype":"date-full","main-datamodelcolumn-columnJavatype":"java.util.Date","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"UPDATE_TIME","main-datamodelcolumn-orderIndex":304,"main-datamodelcolumn-columnDbtype":93}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972102053605376');
		-- 新增【字段】：main-undsischmxqbjxswjcfqk-xnxq
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'schoolYearTerm','main-undsischmxqbjxswjcfqk',1,0,'xnxq',0,'学年学期',20,0,'main-undsischmxqbjxswjcfqk-xnxq',0,'text',0,1,'java.lang.String',1,'XNXQ',3,12,1,1,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsischmxqbjxswjcfqk-xnxq');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE UND_SI_SCH_MXQBJXSWJCFQK ADD XNXQ VARCHAR2(20)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_SCH_MXQBJXSWJCFQK.XNXQ is ''学年学期''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972102066188288','1898972096462598153','upgrade','add','column','main-undsischmxqbjxswjcfqk-xnxq',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"schoolYearTerm","main-datamodelcolumn-columnJavaname":"xnxq","main-datamodelcolumn-columnLabel":"学年学期","main-datamodelcolumn-columnWidth":20,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XNXQ","main-datamodelcolumn-orderIndex":3,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972102066188288');
		-- 新增【字段】：main-undsischmxqbjxswjcfqk-xybm
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-undsischmxqbjxswjcfqk',1,0,'xybm',0,'学院编码',50,0,'main-undsischmxqbjxswjcfqk-xybm',0,'text',0,1,'java.lang.String',1,'XYBM',2,12,1,1,'dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsischmxqbjxswjcfqk-xybm');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE UND_SI_SCH_MXQBJXSWJCFQK ADD XYBM VARCHAR2(50)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_SCH_MXQBJXSWJCFQK.XYBM is ''学院编码''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972102074576896','1898972096462598153','upgrade','add','column','main-undsischmxqbjxswjcfqk-xybm',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"xybm","main-datamodelcolumn-columnLabel":"学院编码","main-datamodelcolumn-columnWidth":50,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XYBM","main-datamodelcolumn-orderIndex":2,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972102074576896');
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-undsischmxqbjxswjcfqk' and FIELD_NAME='main-damodelcolumn-entityTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-undsischmxqbjxswjcfqk(main-damodelcolumn-entityTag 是否实体标识)，跳过更新');
			ELSE
				update t_da_model_column_extend set entity_tag = '0'
				where model_id = 'main-undsischmxqbjxswjcfqk';
				update t_da_model_column_extend set entity_tag = '1'
				where id in ('main-undsischmxqbjxswjcfqk-bjbh');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1898972102103937024','1898972096462598153','upgrade','update','model','main-undsischmxqbjxswjcfqk','main-damodelcolumn-entityTag','{"变更前":{"main-damodelcolumn-entityTag":""},"变更后":{"main-damodelcolumn-entityTag":"班级编号"}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1898972102103937024');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-undsischmxqbjxswjcfqk(main-damodelcolumn-entityTag 是否实体标识),sql执行错误:' || SQLERRM);
		END;
		
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-undsischmxqbjxswjcfqk' and FIELD_NAME='main-damodelcolumn-timeTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-undsischmxqbjxswjcfqk(main-damodelcolumn-timeTag 是否时点标识字段)，跳过更新');
			ELSE
				update t_da_model_column_extend set time_tag = '0'
				where model_id = 'main-undsischmxqbjxswjcfqk';
				update t_da_model_column_extend set time_tag = '1'
				where id in ('main-undsischmxqbjxswjcfqk-xnxq');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1898972102108131328','1898972096462598153','upgrade','update','model','main-undsischmxqbjxswjcfqk','main-damodelcolumn-timeTag','{"变更前":{"main-damodelcolumn-timeTag":""},"变更后":{"main-damodelcolumn-timeTag":"学年学期"}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1898972102108131328');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-undsischmxqbjxswjcfqk(main-damodelcolumn-timeTag 是否时点标识字段),sql执行错误:' || SQLERRM);
		END;
		
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-undsischmxqbjxswjcfqk' and FIELD_NAME='main-damodelcolumn-valueTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-undsischmxqbjxswjcfqk(main-damodelcolumn-valueTag 是否指标值字段)，跳过更新');
			ELSE
				update t_da_model_column_extend set value_tag = '0'
				where model_id = 'main-undsischmxqbjxswjcfqk';
				update t_da_model_column_extend set value_tag = '1'
				where id in ('main-undsischmxqbjxswjcfqk-cfrc');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1898972102120714240','1898972096462598153','upgrade','update','model','main-undsischmxqbjxswjcfqk','main-damodelcolumn-valueTag','{"变更前":{"main-damodelcolumn-valueTag":""},"变更后":{"main-damodelcolumn-valueTag":"处分人次"}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1898972102120714240');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-undsischmxqbjxswjcfqk(main-damodelcolumn-valueTag 是否指标值字段),sql执行错误:' || SQLERRM);
		END;
		
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-undsischmxqbjxswjcfqk' and FIELD_NAME='main-damodelcolumn-primaryColumnTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-undsischmxqbjxswjcfqk(main-damodelcolumn-primaryColumnTag 是否主键字段)，跳过更新');
			ELSE
				update t_da_model_column_extend set primary_column_tag = '0'
				where model_id = 'main-undsischmxqbjxswjcfqk';
				update t_da_model_column_extend set primary_column_tag = '1'
				where id in ('main-undsischmxqbjxswjcfqk-id');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1898972102124908544','1898972096462598153','upgrade','update','model','main-undsischmxqbjxswjcfqk','main-damodelcolumn-primaryColumnTag','{"变更前":{"main-damodelcolumn-primaryColumnTag":""},"变更后":{"main-damodelcolumn-primaryColumnTag":"ID"}}','dataapp',TIMESTAMP '2025-03-10 13:40:04','dataapp',TIMESTAMP '2025-03-10 13:40:04'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1898972102124908544');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-undsischmxqbjxswjcfqk(main-damodelcolumn-primaryColumnTag 是否主键字段),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.0',internal_version='0' WHERE model_id ='main-undsischmxqbjxswjcfqk';
		DBMS_OUTPUT.PUT_LINE('升级模型main-undsischmxqbjxswjcfqk成功,模型版本号更新为:V1.0.0,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-undsiundbzsmnhdzzqk ↓----------------------------------------------
--模型：本专生每年获得资助情况 UND_SI_UND_BZSMNHDZZQK main-undsiundbzsmnhdzzqk
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-undsiundbzsmnhdzzqk'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-undsiundbzsmnhdzzqk版本号:'||v_version);
	IF v_version >= 'V1.0.0.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-undsiundbzsmnhdzzqk的本次增量版本号:V1.0.0,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1898972096462598154','main-undsiundbzsmnhdzzqk','V1.0.0','0','upgrade','add','本专生每年获得资助情况','zb','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1898972096462598154');
		-- 新增【模型】：main-undsiundbzsmnhdzzqk
		insert into lowcode_model (model_app,publish_time,sys_flag,model_class,permit_ext_flag,extend_flag,model_label,model_name,model_table,model_classify,virtual_model,biz_model,order_index,id,status,create_by,create_time,update_by,update_time)
		select 'main',TIMESTAMP '2025-01-17 15:58:25',0,'com.wisedu.lowcode4j.main.po.Undsiundbzsmnhdzzqk',1,1,'本专生每年获得资助情况','undsiundbzsmnhdzzqk','UND_SI_UND_BZSMNHDZZQK','UNDERGRADUATE_ZZXX_ZB',1,0,793,'main-undsiundbzsmnhdzzqk',1,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from lowcode_model
			where id = 'main-undsiundbzsmnhdzzqk');
		BEGIN
		EXECUTE IMMEDIATE 'CREATE TABLE UND_SI_UND_BZSMNHDZZQK(ID VARCHAR2(100) DEFAULT SYS_GUID() primary key ,CREATE_BY VARCHAR2(100),CREATE_TIME DATE,UPDATE_BY VARCHAR2(100),UPDATE_TIME DATE)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-955) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on table UND_SI_UND_BZSMNHDZZQK is ''本专生每年获得资助情况''';
		END;
		-- 新增【模型扩展】：main-undsiundbzsmnhdzzqk
		insert into t_da_model_extend (model_encoding,access_level,model_id,model_version,model_enable,entity_id,model_type,metric_id,use_num,theme_code,internal_version,id,create_by,create_time,update_by,update_time)
		select 'UND_SI_UND_0032','1','main-undsiundbzsmnhdzzqk','V1.0.0','1','UNDERGRADUATE','zb',NULL,0,'UND','0','main-undsiundbzsmnhdzzqk','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_extend
			where id = 'main-undsiundbzsmnhdzzqk');
		-- 新增【模型使用范围】:1737100470302001243
		insert into t_da_model_theme (model_id,theme_code,id,create_by,create_time,update_by,update_time)
		select 'main-undsiundbzsmnhdzzqk','UND','1737100470302001243','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_theme
			where id = '1737100470302001243');
		-- 新增【字段扩展】：main-undsiundbzsmnhdzzqk-createBy
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsiundbzsmnhdzzqk',0,'main-undsiundbzsmnhdzzqk-createBy','main-undsiundbzsmnhdzzqk-createBy',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsiundbzsmnhdzzqk-createBy');
		-- 新增【字段扩展】：main-undsiundbzsmnhdzzqk-createTime
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsiundbzsmnhdzzqk',0,'main-undsiundbzsmnhdzzqk-createTime','main-undsiundbzsmnhdzzqk-createTime',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsiundbzsmnhdzzqk-createTime');
		-- 新增【字段扩展】：main-undsiundbzsmnhdzzqk-id
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,primary_column_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsiundbzsmnhdzzqk',0,'main-undsiundbzsmnhdzzqk-id','main-undsiundbzsmnhdzzqk-id',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsiundbzsmnhdzzqk-id');
		-- 新增【字段扩展】：main-undsiundbzsmnhdzzqk-knbzze
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsiundbzsmnhdzzqk',0,'main-undsiundbzsmnhdzzqk-knbzze','main-undsiundbzsmnhdzzqk-knbzze',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsiundbzsmnhdzzqk-knbzze');
		-- 新增【字段扩展】：main-undsiundbzsmnhdzzqk-qgzxze
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsiundbzsmnhdzzqk',0,'main-undsiundbzsmnhdzzqk-qgzxze','main-undsiundbzsmnhdzzqk-qgzxze',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsiundbzsmnhdzzqk-qgzxze');
		-- 新增【字段扩展】：main-undsiundbzsmnhdzzqk-tjxn
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsiundbzsmnhdzzqk',0,'main-undsiundbzsmnhdzzqk-tjxn','main-undsiundbzsmnhdzzqk-tjxn',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsiundbzsmnhdzzqk-tjxn');
		-- 新增【字段扩展】：main-undsiundbzsmnhdzzqk-updateBy
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsiundbzsmnhdzzqk',0,'main-undsiundbzsmnhdzzqk-updateBy','main-undsiundbzsmnhdzzqk-updateBy',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsiundbzsmnhdzzqk-updateBy');
		-- 新增【字段扩展】：main-undsiundbzsmnhdzzqk-updateTime
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsiundbzsmnhdzzqk',0,'main-undsiundbzsmnhdzzqk-updateTime','main-undsiundbzsmnhdzzqk-updateTime',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsiundbzsmnhdzzqk-updateTime');
		-- 新增【字段扩展】：main-undsiundbzsmnhdzzqk-xh
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsiundbzsmnhdzzqk',0,'main-undsiundbzsmnhdzzqk-xh','main-undsiundbzsmnhdzzqk-xh',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsiundbzsmnhdzzqk-xh');
		-- 新增【字段扩展】：main-undsiundbzsmnhdzzqk-zxdkze
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsiundbzsmnhdzzqk',0,'main-undsiundbzsmnhdzzqk-zxdkze','main-undsiundbzsmnhdzzqk-zxdkze',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsiundbzsmnhdzzqk-zxdkze');
		-- 新增【字段扩展】：main-undsiundbzsmnhdzzqk-zxjze
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsiundbzsmnhdzzqk',0,'main-undsiundbzsmnhdzzqk-zxjze','main-undsiundbzsmnhdzzqk-zxjze',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsiundbzsmnhdzzqk-zxjze');
		-- 新增【字段扩展】：main-undsiundbzsmnhdzzqk-zzze
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsiundbzsmnhdzzqk',0,'main-undsiundbzsmnhdzzqk-zzze','main-undsiundbzsmnhdzzqk-zzze',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsiundbzsmnhdzzqk-zzze');
		-- 新增【字段】：main-undsiundbzsmnhdzzqk-createBy
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-undsiundbzsmnhdzzqk',1,'createBy',0,'创建人',100,0,'main-undsiundbzsmnhdzzqk-createBy',1,0,1,'java.lang.String',0,0,1,'CREATE_BY',0,301,12,1,0,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsiundbzsmnhdzzqk-createBy');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_UND_BZSMNHDZZQK.CREATE_BY is ''创建人''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972102166851584','1898972096462598154','upgrade','add','column','main-undsiundbzsmnhdzzqk-createBy',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"createBy","main-datamodelcolumn-columnLabel":"创建人","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"CREATE_BY","main-datamodelcolumn-orderIndex":301,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972102166851584');
		-- 新增【字段】：main-undsiundbzsmnhdzzqk-createTime
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-undsiundbzsmnhdzzqk',1,'createTime',0,'创建时间',0,0,'main-undsiundbzsmnhdzzqk-createTime',1,'date-full',0,1,'java.util.Date',0,0,1,'CREATE_TIME',0,302,93,1,0,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsiundbzsmnhdzzqk-createTime');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_UND_BZSMNHDZZQK.CREATE_TIME is ''创建时间''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972102175240192','1898972096462598154','upgrade','add','column','main-undsiundbzsmnhdzzqk-createTime',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"createTime","main-datamodelcolumn-columnLabel":"创建时间","main-datamodelcolumn-columnWidth":0,"main-datamodelcolumn-columnXtype":"date-full","main-datamodelcolumn-columnJavatype":"java.util.Date","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"CREATE_TIME","main-datamodelcolumn-orderIndex":302,"main-datamodelcolumn-columnDbtype":93}}','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972102175240192');
		-- 新增【字段】：main-undsiundbzsmnhdzzqk-id
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-undsiundbzsmnhdzzqk',1,'id',0,'ID',100,0,'main-undsiundbzsmnhdzzqk-id',1,0,1,'java.lang.String',0,0,1,'ID',0,300,12,1,0,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsiundbzsmnhdzzqk-id');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_UND_BZSMNHDZZQK.ID is ''ID''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972102187823104','1898972096462598154','upgrade','add','column','main-undsiundbzsmnhdzzqk-id',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"id","main-datamodelcolumn-columnLabel":"ID","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"ID","main-datamodelcolumn-orderIndex":300,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972102187823104');
		-- 新增【字段】：main-undsiundbzsmnhdzzqk-knbzze
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'currency','main-undsiundbzsmnhdzzqk',1,0,'knbzze',0,'困难补助总额',10,0,'main-undsiundbzsmnhdzzqk-knbzze',0,'number',0,1,'java.lang.Integer',1,'KNBZZE',6,4,1,1,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsiundbzsmnhdzzqk-knbzze');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE UND_SI_UND_BZSMNHDZZQK ADD KNBZZE NUMBER(10)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_UND_BZSMNHDZZQK.KNBZZE is ''困难补助总额''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972102200406016','1898972096462598154','upgrade','add','column','main-undsiundbzsmnhdzzqk-knbzze',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"currency","main-datamodelcolumn-columnJavaname":"knbzze","main-datamodelcolumn-columnLabel":"困难补助总额","main-datamodelcolumn-columnWidth":10,"main-datamodelcolumn-columnXtype":"number","main-datamodelcolumn-columnJavatype":"java.lang.Integer","main-datamodelcolumn-columnDbname":"KNBZZE","main-datamodelcolumn-orderIndex":6,"main-datamodelcolumn-columnDbtype":4}}','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972102200406016');
		-- 新增【字段】：main-undsiundbzsmnhdzzqk-qgzxze
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'currency','main-undsiundbzsmnhdzzqk',1,0,'qgzxze',0,'勤工助学总额',10,0,'main-undsiundbzsmnhdzzqk-qgzxze',0,'number',0,1,'java.lang.Integer',1,'QGZXZE',5,4,1,1,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsiundbzsmnhdzzqk-qgzxze');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE UND_SI_UND_BZSMNHDZZQK ADD QGZXZE NUMBER(10)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_UND_BZSMNHDZZQK.QGZXZE is ''勤工助学总额''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972102208794624','1898972096462598154','upgrade','add','column','main-undsiundbzsmnhdzzqk-qgzxze',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"currency","main-datamodelcolumn-columnJavaname":"qgzxze","main-datamodelcolumn-columnLabel":"勤工助学总额","main-datamodelcolumn-columnWidth":10,"main-datamodelcolumn-columnXtype":"number","main-datamodelcolumn-columnJavatype":"java.lang.Integer","main-datamodelcolumn-columnDbname":"QGZXZE","main-datamodelcolumn-orderIndex":5,"main-datamodelcolumn-columnDbtype":4}}','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972102208794624');
		-- 新增【字段】：main-undsiundbzsmnhdzzqk-tjxn
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'schoolYear','main-undsiundbzsmnhdzzqk',1,0,'tjxn',0,'统计学年',20,0,'main-undsiundbzsmnhdzzqk-tjxn',0,'text',0,1,'java.lang.String',1,'TJXN',2,12,1,1,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsiundbzsmnhdzzqk-tjxn');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE UND_SI_UND_BZSMNHDZZQK ADD TJXN VARCHAR2(20)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_UND_BZSMNHDZZQK.TJXN is ''统计学年''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972102221377536','1898972096462598154','upgrade','add','column','main-undsiundbzsmnhdzzqk-tjxn',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"schoolYear","main-datamodelcolumn-columnJavaname":"tjxn","main-datamodelcolumn-columnLabel":"统计学年","main-datamodelcolumn-columnWidth":20,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"TJXN","main-datamodelcolumn-orderIndex":2,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972102221377536');
		-- 新增【字段】：main-undsiundbzsmnhdzzqk-updateBy
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-undsiundbzsmnhdzzqk',1,'updateBy',0,'更新人',100,0,'main-undsiundbzsmnhdzzqk-updateBy',1,0,1,'java.lang.String',0,0,1,'UPDATE_BY',0,303,12,1,0,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsiundbzsmnhdzzqk-updateBy');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_UND_BZSMNHDZZQK.UPDATE_BY is ''更新人''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972102229766144','1898972096462598154','upgrade','add','column','main-undsiundbzsmnhdzzqk-updateBy',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"updateBy","main-datamodelcolumn-columnLabel":"更新人","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"UPDATE_BY","main-datamodelcolumn-orderIndex":303,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972102229766144');
		-- 新增【字段】：main-undsiundbzsmnhdzzqk-updateTime
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-undsiundbzsmnhdzzqk',1,'updateTime',0,'更新时间',0,0,'main-undsiundbzsmnhdzzqk-updateTime',1,'date-full',0,1,'java.util.Date',0,0,1,'UPDATE_TIME',0,304,93,1,0,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsiundbzsmnhdzzqk-updateTime');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_UND_BZSMNHDZZQK.UPDATE_TIME is ''更新时间''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972102238154752','1898972096462598154','upgrade','add','column','main-undsiundbzsmnhdzzqk-updateTime',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"updateTime","main-datamodelcolumn-columnLabel":"更新时间","main-datamodelcolumn-columnWidth":0,"main-datamodelcolumn-columnXtype":"date-full","main-datamodelcolumn-columnJavatype":"java.util.Date","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"UPDATE_TIME","main-datamodelcolumn-orderIndex":304,"main-datamodelcolumn-columnDbtype":93}}','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972102238154752');
		-- 新增【字段】：main-undsiundbzsmnhdzzqk-xh
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-undsiundbzsmnhdzzqk',1,0,'xh',0,'学号',20,0,'main-undsiundbzsmnhdzzqk-xh',0,'text',0,1,'java.lang.String',1,'XH',1,12,1,1,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsiundbzsmnhdzzqk-xh');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE UND_SI_UND_BZSMNHDZZQK ADD XH VARCHAR2(20)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_UND_BZSMNHDZZQK.XH is ''学号''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972102246543360','1898972096462598154','upgrade','add','column','main-undsiundbzsmnhdzzqk-xh',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"xh","main-datamodelcolumn-columnLabel":"学号","main-datamodelcolumn-columnWidth":20,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XH","main-datamodelcolumn-orderIndex":1,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972102246543360');
		-- 新增【字段】：main-undsiundbzsmnhdzzqk-zxdkze
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'currency','main-undsiundbzsmnhdzzqk',1,0,'zxdkze',0,'助学贷款总额',10,0,'main-undsiundbzsmnhdzzqk-zxdkze',0,'number',0,1,'java.lang.Integer',1,'ZXDKZE',7,4,1,1,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsiundbzsmnhdzzqk-zxdkze');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE UND_SI_UND_BZSMNHDZZQK ADD ZXDKZE NUMBER(10)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_UND_BZSMNHDZZQK.ZXDKZE is ''助学贷款总额''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972102254931968','1898972096462598154','upgrade','add','column','main-undsiundbzsmnhdzzqk-zxdkze',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"currency","main-datamodelcolumn-columnJavaname":"zxdkze","main-datamodelcolumn-columnLabel":"助学贷款总额","main-datamodelcolumn-columnWidth":10,"main-datamodelcolumn-columnXtype":"number","main-datamodelcolumn-columnJavatype":"java.lang.Integer","main-datamodelcolumn-columnDbname":"ZXDKZE","main-datamodelcolumn-orderIndex":7,"main-datamodelcolumn-columnDbtype":4}}','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972102254931968');
		-- 新增【字段】：main-undsiundbzsmnhdzzqk-zxjze
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'currency','main-undsiundbzsmnhdzzqk',1,0,'zxjze',0,'助学金总额',10,0,'main-undsiundbzsmnhdzzqk-zxjze',0,'number',0,1,'java.lang.Integer',1,'ZXJZE',4,4,1,1,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsiundbzsmnhdzzqk-zxjze');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE UND_SI_UND_BZSMNHDZZQK ADD ZXJZE NUMBER(10)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_UND_BZSMNHDZZQK.ZXJZE is ''助学金总额''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972102263320576','1898972096462598154','upgrade','add','column','main-undsiundbzsmnhdzzqk-zxjze',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"currency","main-datamodelcolumn-columnJavaname":"zxjze","main-datamodelcolumn-columnLabel":"助学金总额","main-datamodelcolumn-columnWidth":10,"main-datamodelcolumn-columnXtype":"number","main-datamodelcolumn-columnJavatype":"java.lang.Integer","main-datamodelcolumn-columnDbname":"ZXJZE","main-datamodelcolumn-orderIndex":4,"main-datamodelcolumn-columnDbtype":4}}','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972102263320576');
		-- 新增【字段】：main-undsiundbzsmnhdzzqk-zzze
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'currency','main-undsiundbzsmnhdzzqk',1,0,'zzze',0,'资助总额',10,0,'main-undsiundbzsmnhdzzqk-zzze',0,'number',0,1,'java.lang.Integer',1,'ZZZE',3,4,1,1,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsiundbzsmnhdzzqk-zzze');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE UND_SI_UND_BZSMNHDZZQK ADD ZZZE NUMBER(10)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_UND_BZSMNHDZZQK.ZZZE is ''资助总额''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972102271709184','1898972096462598154','upgrade','add','column','main-undsiundbzsmnhdzzqk-zzze',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"currency","main-datamodelcolumn-columnJavaname":"zzze","main-datamodelcolumn-columnLabel":"资助总额","main-datamodelcolumn-columnWidth":10,"main-datamodelcolumn-columnXtype":"number","main-datamodelcolumn-columnJavatype":"java.lang.Integer","main-datamodelcolumn-columnDbname":"ZZZE","main-datamodelcolumn-orderIndex":3,"main-datamodelcolumn-columnDbtype":4}}','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972102271709184');
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-undsiundbzsmnhdzzqk' and FIELD_NAME='main-damodelcolumn-entityTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-undsiundbzsmnhdzzqk(main-damodelcolumn-entityTag 是否实体标识)，跳过更新');
			ELSE
				update t_da_model_column_extend set entity_tag = '0'
				where model_id = 'main-undsiundbzsmnhdzzqk';
				update t_da_model_column_extend set entity_tag = '1'
				where id in ('main-undsiundbzsmnhdzzqk-xh');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1898972102301069312','1898972096462598154','upgrade','update','model','main-undsiundbzsmnhdzzqk','main-damodelcolumn-entityTag','{"变更前":{"main-damodelcolumn-entityTag":""},"变更后":{"main-damodelcolumn-entityTag":"学号"}}','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1898972102301069312');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-undsiundbzsmnhdzzqk(main-damodelcolumn-entityTag 是否实体标识),sql执行错误:' || SQLERRM);
		END;
		
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-undsiundbzsmnhdzzqk' and FIELD_NAME='main-damodelcolumn-timeTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-undsiundbzsmnhdzzqk(main-damodelcolumn-timeTag 是否时点标识字段)，跳过更新');
			ELSE
				update t_da_model_column_extend set time_tag = '0'
				where model_id = 'main-undsiundbzsmnhdzzqk';
				update t_da_model_column_extend set time_tag = '1'
				where id in ('main-undsiundbzsmnhdzzqk-tjxn');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1898972102305263616','1898972096462598154','upgrade','update','model','main-undsiundbzsmnhdzzqk','main-damodelcolumn-timeTag','{"变更前":{"main-damodelcolumn-timeTag":""},"变更后":{"main-damodelcolumn-timeTag":"统计学年"}}','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1898972102305263616');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-undsiundbzsmnhdzzqk(main-damodelcolumn-timeTag 是否时点标识字段),sql执行错误:' || SQLERRM);
		END;
		
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-undsiundbzsmnhdzzqk' and FIELD_NAME='main-damodelcolumn-valueTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-undsiundbzsmnhdzzqk(main-damodelcolumn-valueTag 是否指标值字段)，跳过更新');
			ELSE
				update t_da_model_column_extend set value_tag = '0'
				where model_id = 'main-undsiundbzsmnhdzzqk';
				update t_da_model_column_extend set value_tag = '1'
				where id in ('main-undsiundbzsmnhdzzqk-knbzze','main-undsiundbzsmnhdzzqk-qgzxze','main-undsiundbzsmnhdzzqk-zxdkze','main-undsiundbzsmnhdzzqk-zxjze','main-undsiundbzsmnhdzzqk-zzze');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1898972102305263617','1898972096462598154','upgrade','update','model','main-undsiundbzsmnhdzzqk','main-damodelcolumn-valueTag','{"变更前":{"main-damodelcolumn-valueTag":""},"变更后":{"main-damodelcolumn-valueTag":"困难补助总额,勤工助学总额,助学贷款总额,助学金总额,资助总额"}}','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1898972102305263617');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-undsiundbzsmnhdzzqk(main-damodelcolumn-valueTag 是否指标值字段),sql执行错误:' || SQLERRM);
		END;
		
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-undsiundbzsmnhdzzqk' and FIELD_NAME='main-damodelcolumn-primaryColumnTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-undsiundbzsmnhdzzqk(main-damodelcolumn-primaryColumnTag 是否主键字段)，跳过更新');
			ELSE
				update t_da_model_column_extend set primary_column_tag = '0'
				where model_id = 'main-undsiundbzsmnhdzzqk';
				update t_da_model_column_extend set primary_column_tag = '1'
				where id in ('main-undsiundbzsmnhdzzqk-id');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1898972102309457920','1898972096462598154','upgrade','update','model','main-undsiundbzsmnhdzzqk','main-damodelcolumn-primaryColumnTag','{"变更前":{"main-damodelcolumn-primaryColumnTag":""},"变更后":{"main-damodelcolumn-primaryColumnTag":"ID"}}','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1898972102309457920');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-undsiundbzsmnhdzzqk(main-damodelcolumn-primaryColumnTag 是否主键字段),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.0',internal_version='0' WHERE model_id ='main-undsiundbzsmnhdzzqk';
		DBMS_OUTPUT.PUT_LINE('升级模型main-undsiundbzsmnhdzzqk成功,模型版本号更新为:V1.0.0,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-undsiundknsyktmrxfqk ↓----------------------------------------------
--模型：困难生一卡通每日消费情况 UND_SI_UND_KNSYKTMRXFQK main-undsiundknsyktmrxfqk
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-undsiundknsyktmrxfqk'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-undsiundknsyktmrxfqk版本号:'||v_version);
	IF v_version >= 'V1.0.0.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-undsiundknsyktmrxfqk的本次增量版本号:V1.0.0,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1898972096462598155','main-undsiundknsyktmrxfqk','V1.0.0','0','upgrade','add','困难生一卡通每日消费情况','zb','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1898972096462598155');
		-- 新增【模型】：main-undsiundknsyktmrxfqk
		insert into lowcode_model (model_app,publish_time,sys_flag,model_class,permit_ext_flag,extend_flag,model_label,model_name,model_table,model_classify,virtual_model,biz_model,order_index,id,status,create_by,create_time,update_by,update_time)
		select 'main',TIMESTAMP '2025-01-17 16:10:42',0,'com.wisedu.lowcode4j.main.po.Undsiundknsyktmrxfqk',1,1,'困难生一卡通每日消费情况','undsiundknsyktmrxfqk','UND_SI_UND_KNSYKTMRXFQK','UNDERGRADUATE_RCSH_ZB',1,0,794,'main-undsiundknsyktmrxfqk',1,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from lowcode_model
			where id = 'main-undsiundknsyktmrxfqk');
		BEGIN
		EXECUTE IMMEDIATE 'CREATE TABLE UND_SI_UND_KNSYKTMRXFQK(ID VARCHAR2(100) DEFAULT SYS_GUID() primary key ,CREATE_BY VARCHAR2(100),CREATE_TIME DATE,UPDATE_BY VARCHAR2(100),UPDATE_TIME DATE)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-955) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on table UND_SI_UND_KNSYKTMRXFQK is ''困难生一卡通每日消费情况''';
		END;
		-- 新增【模型扩展】：main-undsiundknsyktmrxfqk
		insert into t_da_model_extend (model_encoding,access_level,model_id,model_version,model_enable,entity_id,model_type,metric_id,use_num,theme_code,internal_version,id,create_by,create_time,update_by,update_time)
		select 'UND_SI_UND_0033','1','main-undsiundknsyktmrxfqk','V1.0.0','1','UNDERGRADUATE','zb',NULL,0,'UND','0','main-undsiundknsyktmrxfqk','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_extend
			where id = 'main-undsiundknsyktmrxfqk');
		-- 新增【模型使用范围】:1737101078253001243
		insert into t_da_model_theme (model_id,theme_code,id,create_by,create_time,update_by,update_time)
		select 'main-undsiundknsyktmrxfqk','UND','1737101078253001243','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_theme
			where id = '1737101078253001243');
		-- 新增【字段扩展】：main-undsiundknsyktmrxfqk-createBy
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsiundknsyktmrxfqk',0,'main-undsiundknsyktmrxfqk-createBy','main-undsiundknsyktmrxfqk-createBy',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsiundknsyktmrxfqk-createBy');
		-- 新增【字段扩展】：main-undsiundknsyktmrxfqk-createTime
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsiundknsyktmrxfqk',0,'main-undsiundknsyktmrxfqk-createTime','main-undsiundknsyktmrxfqk-createTime',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsiundknsyktmrxfqk-createTime');
		-- 新增【字段扩展】：main-undsiundknsyktmrxfqk-id
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,primary_column_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsiundknsyktmrxfqk',0,'main-undsiundknsyktmrxfqk-id','main-undsiundknsyktmrxfqk-id',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsiundknsyktmrxfqk-id');
		-- 新增【字段扩展】：main-undsiundknsyktmrxfqk-tjrq
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsiundknsyktmrxfqk',0,'main-undsiundknsyktmrxfqk-tjrq','main-undsiundknsyktmrxfqk-tjrq',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsiundknsyktmrxfqk-tjrq');
		-- 新增【字段扩展】：main-undsiundknsyktmrxfqk-updateBy
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsiundknsyktmrxfqk',0,'main-undsiundknsyktmrxfqk-updateBy','main-undsiundknsyktmrxfqk-updateBy',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsiundknsyktmrxfqk-updateBy');
		-- 新增【字段扩展】：main-undsiundknsyktmrxfqk-updateTime
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsiundknsyktmrxfqk',0,'main-undsiundknsyktmrxfqk-updateTime','main-undsiundknsyktmrxfqk-updateTime',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsiundknsyktmrxfqk-updateTime');
		-- 新增【字段扩展】：main-undsiundknsyktmrxfqk-xfje
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsiundknsyktmrxfqk',0,'main-undsiundknsyktmrxfqk-xfje','main-undsiundknsyktmrxfqk-xfje',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsiundknsyktmrxfqk-xfje');
		-- 新增【字段扩展】：main-undsiundknsyktmrxfqk-xh
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-undsiundknsyktmrxfqk',0,'main-undsiundknsyktmrxfqk-xh','main-undsiundknsyktmrxfqk-xh',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-undsiundknsyktmrxfqk-xh');
		-- 新增【字段】：main-undsiundknsyktmrxfqk-createBy
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-undsiundknsyktmrxfqk',1,'createBy',0,'创建人',100,0,'main-undsiundknsyktmrxfqk-createBy',1,0,1,'java.lang.String',0,0,1,'CREATE_BY',0,301,12,1,0,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsiundknsyktmrxfqk-createBy');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_UND_KNSYKTMRXFQK.CREATE_BY is ''创建人''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972102347206656','1898972096462598155','upgrade','add','column','main-undsiundknsyktmrxfqk-createBy',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"createBy","main-datamodelcolumn-columnLabel":"创建人","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"CREATE_BY","main-datamodelcolumn-orderIndex":301,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972102347206656');
		-- 新增【字段】：main-undsiundknsyktmrxfqk-createTime
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-undsiundknsyktmrxfqk',1,'createTime',0,'创建时间',0,0,'main-undsiundknsyktmrxfqk-createTime',1,'date-full',0,1,'java.util.Date',0,0,1,'CREATE_TIME',0,302,93,1,0,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsiundknsyktmrxfqk-createTime');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_UND_KNSYKTMRXFQK.CREATE_TIME is ''创建时间''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972102359789568','1898972096462598155','upgrade','add','column','main-undsiundknsyktmrxfqk-createTime',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"createTime","main-datamodelcolumn-columnLabel":"创建时间","main-datamodelcolumn-columnWidth":0,"main-datamodelcolumn-columnXtype":"date-full","main-datamodelcolumn-columnJavatype":"java.util.Date","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"CREATE_TIME","main-datamodelcolumn-orderIndex":302,"main-datamodelcolumn-columnDbtype":93}}','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972102359789568');
		-- 新增【字段】：main-undsiundknsyktmrxfqk-id
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-undsiundknsyktmrxfqk',1,'id',0,'ID',100,0,'main-undsiundknsyktmrxfqk-id',1,0,1,'java.lang.String',0,0,1,'ID',0,300,12,1,0,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsiundknsyktmrxfqk-id');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_UND_KNSYKTMRXFQK.ID is ''ID''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972102368178176','1898972096462598155','upgrade','add','column','main-undsiundknsyktmrxfqk-id',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"id","main-datamodelcolumn-columnLabel":"ID","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"ID","main-datamodelcolumn-orderIndex":300,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972102368178176');
		-- 新增【字段】：main-undsiundknsyktmrxfqk-tjrq
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'date','main-undsiundknsyktmrxfqk',1,0,'tjrq',0,'统计日期',20,0,'main-undsiundknsyktmrxfqk-tjrq',0,'date-local',0,1,'java.lang.String',1,'TJRQ',2,12,1,1,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsiundknsyktmrxfqk-tjrq');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE UND_SI_UND_KNSYKTMRXFQK ADD TJRQ VARCHAR2(20)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_UND_KNSYKTMRXFQK.TJRQ is ''统计日期''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972102376566784','1898972096462598155','upgrade','add','column','main-undsiundknsyktmrxfqk-tjrq',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"date","main-datamodelcolumn-columnJavaname":"tjrq","main-datamodelcolumn-columnLabel":"统计日期","main-datamodelcolumn-columnWidth":20,"main-datamodelcolumn-columnXtype":"date-local","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"TJRQ","main-datamodelcolumn-orderIndex":2,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972102376566784');
		-- 新增【字段】：main-undsiundknsyktmrxfqk-updateBy
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-undsiundknsyktmrxfqk',1,'updateBy',0,'更新人',100,0,'main-undsiundknsyktmrxfqk-updateBy',1,0,1,'java.lang.String',0,0,1,'UPDATE_BY',0,303,12,1,0,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsiundknsyktmrxfqk-updateBy');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_UND_KNSYKTMRXFQK.UPDATE_BY is ''更新人''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972102384955392','1898972096462598155','upgrade','add','column','main-undsiundknsyktmrxfqk-updateBy',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"updateBy","main-datamodelcolumn-columnLabel":"更新人","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"UPDATE_BY","main-datamodelcolumn-orderIndex":303,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972102384955392');
		-- 新增【字段】：main-undsiundknsyktmrxfqk-updateTime
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-undsiundknsyktmrxfqk',1,'updateTime',0,'更新时间',0,0,'main-undsiundknsyktmrxfqk-updateTime',1,'date-full',0,1,'java.util.Date',0,0,1,'UPDATE_TIME',0,304,93,1,0,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsiundknsyktmrxfqk-updateTime');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_UND_KNSYKTMRXFQK.UPDATE_TIME is ''更新时间''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972102393344000','1898972096462598155','upgrade','add','column','main-undsiundknsyktmrxfqk-updateTime',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"updateTime","main-datamodelcolumn-columnLabel":"更新时间","main-datamodelcolumn-columnWidth":0,"main-datamodelcolumn-columnXtype":"date-full","main-datamodelcolumn-columnJavatype":"java.util.Date","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"UPDATE_TIME","main-datamodelcolumn-orderIndex":304,"main-datamodelcolumn-columnDbtype":93}}','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972102393344000');
		-- 新增【字段】：main-undsiundknsyktmrxfqk-xfje
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'currency','main-undsiundknsyktmrxfqk',1,0,'xfje',0,'消费金额',10,0,'main-undsiundknsyktmrxfqk-xfje',0,'number',0,1,'java.lang.Integer',1,'XFJE',3,4,1,1,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsiundknsyktmrxfqk-xfje');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE UND_SI_UND_KNSYKTMRXFQK ADD XFJE NUMBER(10)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_UND_KNSYKTMRXFQK.XFJE is ''消费金额''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972102397538304','1898972096462598155','upgrade','add','column','main-undsiundknsyktmrxfqk-xfje',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"currency","main-datamodelcolumn-columnJavaname":"xfje","main-datamodelcolumn-columnLabel":"消费金额","main-datamodelcolumn-columnWidth":10,"main-datamodelcolumn-columnXtype":"number","main-datamodelcolumn-columnJavatype":"java.lang.Integer","main-datamodelcolumn-columnDbname":"XFJE","main-datamodelcolumn-orderIndex":3,"main-datamodelcolumn-columnDbtype":4}}','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972102397538304');
		-- 新增【字段】：main-undsiundknsyktmrxfqk-xh
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-undsiundknsyktmrxfqk',1,0,'xh',0,'学号',20,0,'main-undsiundknsyktmrxfqk-xh',0,'text',0,1,'java.lang.String',1,'XH',1,12,1,1,'dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-undsiundknsyktmrxfqk-xh');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE UND_SI_UND_KNSYKTMRXFQK ADD XH VARCHAR2(20)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column UND_SI_UND_KNSYKTMRXFQK.XH is ''学号''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1898972102405926912','1898972096462598155','upgrade','add','column','main-undsiundknsyktmrxfqk-xh',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"xh","main-datamodelcolumn-columnLabel":"学号","main-datamodelcolumn-columnWidth":20,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XH","main-datamodelcolumn-orderIndex":1,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1898972102405926912');
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-undsiundknsyktmrxfqk' and FIELD_NAME='main-damodelcolumn-entityTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-undsiundknsyktmrxfqk(main-damodelcolumn-entityTag 是否实体标识)，跳过更新');
			ELSE
				update t_da_model_column_extend set entity_tag = '0'
				where model_id = 'main-undsiundknsyktmrxfqk';
				update t_da_model_column_extend set entity_tag = '1'
				where id in ('main-undsiundknsyktmrxfqk-xh');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1898972102435287040','1898972096462598155','upgrade','update','model','main-undsiundknsyktmrxfqk','main-damodelcolumn-entityTag','{"变更前":{"main-damodelcolumn-entityTag":""},"变更后":{"main-damodelcolumn-entityTag":"学号"}}','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1898972102435287040');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-undsiundknsyktmrxfqk(main-damodelcolumn-entityTag 是否实体标识),sql执行错误:' || SQLERRM);
		END;
		
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-undsiundknsyktmrxfqk' and FIELD_NAME='main-damodelcolumn-timeTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-undsiundknsyktmrxfqk(main-damodelcolumn-timeTag 是否时点标识字段)，跳过更新');
			ELSE
				update t_da_model_column_extend set time_tag = '0'
				where model_id = 'main-undsiundknsyktmrxfqk';
				update t_da_model_column_extend set time_tag = '1'
				where id in ('main-undsiundknsyktmrxfqk-tjrq');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1898972102435287041','1898972096462598155','upgrade','update','model','main-undsiundknsyktmrxfqk','main-damodelcolumn-timeTag','{"变更前":{"main-damodelcolumn-timeTag":""},"变更后":{"main-damodelcolumn-timeTag":"统计日期"}}','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1898972102435287041');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-undsiundknsyktmrxfqk(main-damodelcolumn-timeTag 是否时点标识字段),sql执行错误:' || SQLERRM);
		END;
		
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-undsiundknsyktmrxfqk' and FIELD_NAME='main-damodelcolumn-valueTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-undsiundknsyktmrxfqk(main-damodelcolumn-valueTag 是否指标值字段)，跳过更新');
			ELSE
				update t_da_model_column_extend set value_tag = '0'
				where model_id = 'main-undsiundknsyktmrxfqk';
				update t_da_model_column_extend set value_tag = '1'
				where id in ('main-undsiundknsyktmrxfqk-xfje');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1898972102439481344','1898972096462598155','upgrade','update','model','main-undsiundknsyktmrxfqk','main-damodelcolumn-valueTag','{"变更前":{"main-damodelcolumn-valueTag":""},"变更后":{"main-damodelcolumn-valueTag":"消费金额"}}','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1898972102439481344');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-undsiundknsyktmrxfqk(main-damodelcolumn-valueTag 是否指标值字段),sql执行错误:' || SQLERRM);
		END;
		
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-undsiundknsyktmrxfqk' and FIELD_NAME='main-damodelcolumn-primaryColumnTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-undsiundknsyktmrxfqk(main-damodelcolumn-primaryColumnTag 是否主键字段)，跳过更新');
			ELSE
				update t_da_model_column_extend set primary_column_tag = '0'
				where model_id = 'main-undsiundknsyktmrxfqk';
				update t_da_model_column_extend set primary_column_tag = '1'
				where id in ('main-undsiundknsyktmrxfqk-id');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1898972102443675648','1898972096462598155','upgrade','update','model','main-undsiundknsyktmrxfqk','main-damodelcolumn-primaryColumnTag','{"变更前":{"main-damodelcolumn-primaryColumnTag":""},"变更后":{"main-damodelcolumn-primaryColumnTag":"ID"}}','dataapp',TIMESTAMP '2025-03-10 13:40:05','dataapp',TIMESTAMP '2025-03-10 13:40:05'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1898972102443675648');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-undsiundknsyktmrxfqk(main-damodelcolumn-primaryColumnTag 是否主键字段),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.0',internal_version='0' WHERE model_id ='main-undsiundknsyktmrxfqk';
		DBMS_OUTPUT.PUT_LINE('升级模型main-undsiundknsyktmrxfqk成功,模型版本号更新为:V1.0.0,内部版本号更新为：0');
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE OR REPLACE VIEW V_ABD_UND_BZKSJBXX AS SELECT * FROM ABD_UND_BZKSJBXX WHERE kzrq = TO_CHAR(SYSDATE, ''yyyy-MM-dd'')';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904) THEN
		RAISE;
	END IF;
END;