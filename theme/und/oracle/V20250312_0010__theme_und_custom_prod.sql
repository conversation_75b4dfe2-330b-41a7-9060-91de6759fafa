BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzksyktxx_xh ON abd_und_bzksyktxx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzksxlpcxx_xh ON abd_und_bzksxlpcxx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzkst_xh ON abd_und_bzkst (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzkssswjxx_xh ON abd_und_bzkssswjxx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_und_bzksmyxfqk_xh ON und_si_und_bzksmyxfqk (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0134_xxbsm ON und_si_sch_0134 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0135_xxbsm ON und_si_sch_0135 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_col_0068_xybh ON und_si_col_0068 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_col_0069_xybh ON und_si_col_0069 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzkshdrychjl_xh ON abd_und_bzkshdrychjl (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0098_xxbsm ON und_si_sch_0098 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0099_xxbsm ON und_si_sch_0099 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0136_xxbsm ON und_si_sch_0136 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0137_xxbsm ON und_si_sch_0137 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0138_xxbsm ON und_si_sch_0138 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_col_0070_xybh ON und_si_col_0070 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_col_0071_xybh ON und_si_col_0071 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzkswjcf_xh ON abd_und_bzkswjcf (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0100_xxbsm ON und_si_sch_0100 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0101_xxbsm ON und_si_sch_0101 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0102_xxbsm ON und_si_sch_0102 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0139_xxbsm ON und_si_sch_0139 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0140_xxbsm ON und_si_sch_0140 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_col_0072_xybh ON und_si_col_0072 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_col_0073_xybh ON und_si_col_0073 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_col_0074_xybh ON und_si_col_0074 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzkshjjl_xh ON abd_und_bzkshjjl (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzksknsxx_xh ON abd_und_bzksknsxx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0103_xxbsm ON und_si_sch_0103 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0104_xxbsm ON und_si_sch_0104 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0141_xxbsm ON und_si_sch_0141 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0142_xxbsm ON und_si_sch_0142 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0143_xxbsm ON und_si_sch_0143 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_col_0075_xybh ON und_si_col_0075 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_col_0076_xybh ON und_si_col_0076 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzkshdzxjjl_xh ON abd_und_bzkshdzxjjl (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzkszxjffjl_xh ON abd_und_bzkszxjffjl (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0105_xxbsm ON und_si_sch_0105 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0106_xxbsm ON und_si_sch_0106 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0144_xxbsm ON und_si_sch_0144 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0145_xxbsm ON und_si_sch_0145 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_col_0077_xybh ON und_si_col_0077 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_col_0078_xybh ON und_si_col_0078 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_col_0079_xybh ON und_si_col_0079 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzksqgzxsgjl_xh ON abd_und_bzksqgzxsgjl (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0107_xxbsm ON und_si_sch_0107 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0108_xxbsm ON und_si_sch_0108 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0109_xxbsm ON und_si_sch_0109 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0146_xxbsm ON und_si_sch_0146 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0147_xxbsm ON und_si_sch_0147 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_col_0080_xybh ON und_si_col_0080 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_col_0081_xybh ON und_si_col_0081 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_sch_bzksjxj_xxbsm ON abd_sch_bzksjxj (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_sch_bzksrych_xxbsm ON abd_sch_bzksrych (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0110_xxbsm ON und_si_sch_0110 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0111_xxbsm ON und_si_sch_0111 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0148_xxbsm ON und_si_sch_0148 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0524_xxbsm ON und_si_sch_0524 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_col_0082_xybh ON und_si_col_0082 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_col_0083_xybh ON und_si_col_0083 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzksjbxx_xh ON abd_und_bzksjbxx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_sch_bzkshd_xxbsm ON abd_sch_bzkshd (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_sch_bzksshsj_xxbsm ON abd_sch_bzksshsj (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0112_xxbsm ON und_si_sch_0112 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0113_xxbsm ON und_si_sch_0113 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0517_xxbsm ON und_si_sch_0517 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_sch_bzkszyfw_xxbsm ON abd_sch_bzkszyfw (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_sch_bkspyfaxx_xxbsm ON abd_sch_bkspyfaxx (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0114_xxbsm ON und_si_sch_0114 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0115_xxbsm ON und_si_sch_0115 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0116_xxbsm ON und_si_sch_0116 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0518_xxbsm ON und_si_sch_0518 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0519_xxbsm ON und_si_sch_0519 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzksjyjl_xh ON abd_und_bzksjyjl (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0083_xxbsm ON und_si_sch_0083 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0117_xxbsm ON und_si_sch_0117 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0118_xxbsm ON und_si_sch_0118 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0520_xxbsm ON und_si_sch_0520 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0521_xxbsm ON und_si_sch_0521 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzksxsxx_xh ON abd_und_bzksxsxx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0084_xxbsm ON und_si_sch_0084 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0085_xxbsm ON und_si_sch_0085 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0086_xxbsm ON und_si_sch_0086 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0119_xxbsm ON und_si_sch_0119 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0120_xxbsm ON und_si_sch_0120 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0121_xxbsm ON und_si_sch_0121 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0522_xxbsm ON und_si_sch_0522 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_col_0055_xybh ON und_si_col_0055 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzklhlxsxx_xh ON abd_und_bzklhlxsxx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0087_xxbsm ON und_si_sch_0087 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0088_xxbsm ON und_si_sch_0088 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0122_xxbsm ON und_si_sch_0122 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0123_xxbsm ON und_si_sch_0123 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0124_xxbsm ON und_si_sch_0124 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_col_0056_xybh ON und_si_col_0056 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_col_0057_xybh ON und_si_col_0057 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0089_xxbsm ON und_si_sch_0089 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0090_xxbsm ON und_si_sch_0090 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0125_xxbsm ON und_si_sch_0125 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0126_xxbsm ON und_si_sch_0126 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_col_0058_xybh ON und_si_col_0058 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_col_0059_xybh ON und_si_col_0059 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_col_0060_xybh ON und_si_col_0060 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzksxjydxx_xh ON abd_und_bzksxjydxx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0091_xxbsm ON und_si_sch_0091 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0092_xxbsm ON und_si_sch_0092 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0523_xxbsm ON und_si_sch_0523 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0127_xxbsm ON und_si_sch_0127 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0128_xxbsm ON und_si_sch_0128 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_col_0061_xybh ON und_si_col_0061 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_col_0062_xybh ON und_si_col_0062 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzksjtqk_xh ON abd_und_bzksjtqk (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0093_xxbsm ON und_si_sch_0093 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0094_xxbsm ON und_si_sch_0094 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0095_xxbsm ON und_si_sch_0095 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0129_xxbsm ON und_si_sch_0129 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0130_xxbsm ON und_si_sch_0130 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_col_0063_xybh ON und_si_col_0063 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_col_0064_xybh ON und_si_col_0064 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzksjtcy_xh ON abd_und_bzksjtcy (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzkszhcpjg_xh ON abd_und_bzkszhcpjg (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0096_xxbsm ON und_si_sch_0096 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0097_xxbsm ON und_si_sch_0097 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0131_xxbsm ON und_si_sch_0131 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0132_xxbsm ON und_si_sch_0132 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0133_xxbsm ON und_si_sch_0133 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_col_0065_xybh ON und_si_col_0065 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_col_0066_xybh ON und_si_col_0066 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_col_0067_xybh ON und_si_col_0067 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzkszhcpcjmx_xh ON abd_und_bzkszhcpcjmx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzkshdjxjjl_xh ON abd_und_bzkshdjxjjl (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzksqgzxbcffjl_xh ON abd_und_bzksqgzxbcffjl (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzkszxdkhkjl_xh ON abd_und_bzkszxdkhkjl (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzksxszbbxx_xh ON abd_und_bzksxszbbxx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzkshdknbzjl_xh ON abd_und_bzkshdknbzjl (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzksknbzffjl_xh ON abd_und_bzksknbzffjl (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzkshdzxdkjl_xh ON abd_und_bzkshdzxdkjl (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzkszxdkffjl_xh ON abd_und_bzkszxdkffjl (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzksyktxfxx_xh ON abd_und_bzksyktxfxx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0547_xxbsm ON und_si_sch_0547 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzksswxx_xh ON abd_und_bzksswxx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzksrwjl_xh ON abd_und_bzksrwjl (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzkscgjlxjl_xh ON abd_und_bzkscgjlxjl (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0550_xxbsm ON und_si_sch_0550 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_und_0024_xh ON und_si_und_0024 (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzkscgjxm_xh ON abd_und_bzkscgjxm (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_und_0025_xh ON und_si_und_0025 (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzkszsxx_xh ON abd_und_bzkszsxx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzkssscqxx_xh ON abd_und_bzkssscqxx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzksxkjsxx_xh ON abd_und_bzksxkjsxx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzksbyxx_xh ON abd_und_bzksbyxx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzksjyxx_xh ON abd_und_bzksjyxx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzksxycj_xh ON abd_und_bzksxycj (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzkstccj_xh ON abd_und_bzkstccj (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_und_0001_xh ON und_si_und_0001 (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_und_0002_xh ON und_si_und_0002 (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_und_0003_xh ON und_si_und_0003 (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_und_0010_xh ON und_si_und_0010 (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_und_0011_xh ON und_si_und_0011 (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_und_0004_xh ON und_si_und_0004 (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_und_0013_xh ON und_si_und_0013 (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_und_0005_xh ON und_si_und_0005 (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_und_0006_xh ON und_si_und_0006 (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_und_0007_xh ON und_si_und_0007 (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_und_0008_xh ON und_si_und_0008 (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzksqjxx_xh ON abd_und_bzksqjxx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzksjjrlfxxx_xh ON abd_und_bzksjjrlfxxx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_und_0009_xh ON und_si_und_0009 (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_und_0014_xh ON und_si_und_0014 (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_und_bzkstsjyxx_xh ON abd_und_bzkstsjyxx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_und_0012_xh ON und_si_und_0012 (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_label_xh ON und_label (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_0546_xxbsm ON und_si_sch_0546 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_mxqbjxsgkq_bjbh ON und_si_sch_mxqbjxsgkq (bjbh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_und_bzsmnhdzzqk_xh ON und_si_und_bzsmnhdzzqk (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_mxqbjxswjc_bjbh ON und_si_sch_mxqbjxswjc (bjbh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_und_knsyktmrxfqk_xh ON und_si_und_knsyktmrxfqk (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_und_si_sch_mnbjyjsxfw_bjbh ON und_si_sch_mnbjyjsxfw (bjbh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/