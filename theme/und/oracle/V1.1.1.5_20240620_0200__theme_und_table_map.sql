/*
 Description		: [本专科生(UND)]主题域报表表名对照表
 Author				: dataapp
 Date				: 2024-06-20 01:00:00
 Lowcode Version	: V1.1.1_Beta5
 Database           : oracle
*/

BEGIN
    EXECUTE IMMEDIATE 'CREATE TABLE t_table_map (
  id VARCHAR2(100 BYTE),
  table_name VARCHAR2(200 BYTE),
  comments VARCHAR2(500 BYTE),
  sql_str CLOB,
  create_by VARCHAR2(100),
  create_time DATE,
  update_by VARCHAR2(100),
  update_time DATE,PRIMARY KEY (id))';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

COMMENT ON TABLE t_table_map IS '报表模型表名映射';
COMMENT ON COLUMN t_table_map.id IS 'ID';
COMMENT ON COLUMN t_table_map.table_name IS '表名';
COMMENT ON COLUMN t_table_map.comments IS '表注释';
COMMENT ON COLUMN t_table_map.sql_str IS 'sql脚本';
COMMENT ON COLUMN t_table_map.create_by IS '创建人';
COMMENT ON COLUMN t_table_map.create_time IS '创建时间';
COMMENT ON COLUMN t_table_map.update_by IS '更新人';
COMMENT ON COLUMN t_table_map.update_time IS '更新时间';

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_col_xyjbxx', 'abd_col_xyjbxx', '学院基本信息', TO_CLOB('
select
学院基本信息.xybh as 学院编号,
学院基本信息.xymc as 学院名称
from abd_col_xyjbxx 学院基本信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_col_xyjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xxjbxx', 'abd_sch_xxjbxx', '学校基本信息', TO_CLOB('
select
学校基本信息.xxbsm as 学校标识码,
学校基本信息.xxmc as 学校名称,
学校基本信息.xxywmc as 学校英文名称,
学校基本信息.xxdz as 学校地址,
学校基本信息.xxyzbm as 学校邮政编码,
学校基本信息.xzqh as 行政区划,
学校基本信息.szdcxlx as 所在地城乡类型,
学校基本信息.jxny as 建校年月,
学校基本信息.xqr as 校庆日,
学校基本信息.xxbxlx as 学校办学类型,
学校基本信息.xxjbz as 学校举办者,
学校基本信息.xxzgbm as 学校主管部门,
学校基本信息.fddbrh as 法定代表人号,
学校基本信息.frzsh as 法人证书号,
学校基本信息.xzxm as 校长姓名,
学校基本信息.dwfzr as 党委负责人,
学校基本信息.zzjgm as 组织机构码,
学校基本信息.lxdh as 联系电话,
学校基本信息.czdh as 传真电话,
学校基本信息.dzxx as 电子信箱,
学校基本信息.xxbb as 学校办别,
学校基本信息.xxxz as 学校性质
from abd_sch_xxjbxx 学校基本信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xxjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_bzksjxj', 'abd_sch_bzksjxj', '本专科生奖学金', TO_CLOB('
select
本专科生奖学金.xxbsm as 学校标识码,
本专科生奖学金.jxjbm as 奖学金编码,
本专科生奖学金.xnxq as 学年学期,
本专科生奖学金.jxjmc as 奖学金名称,
本专科生奖学金.jxjdj as 奖学金等级,
本专科生奖学金.jxjlx as 奖学金类型,
本专科生奖学金.jljb as 奖励级别,
本专科生奖学金.sldwhgr as 设立单位或个人,
本专科生奖学金.zjly as 资金来源,
本专科生奖学金.jlje as 奖励金额
from abd_sch_bzksjxj 本专科生奖学金'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_bzksjxj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_bzksrych', 'abd_sch_bzksrych', '本专科生荣誉称号', TO_CLOB('
select
本专科生荣誉称号.xxbsm as 学校标识码,
本专科生荣誉称号.rychbm as 荣誉称号编码,
本专科生荣誉称号.rychmc as 荣誉称号名称,
本专科生荣誉称号.sldw as 设立单位,
本专科生荣誉称号.rychlx as 荣誉称号类型,
本专科生荣誉称号.jljb as 奖励级别,
本专科生荣誉称号.xnxq as 学年学期
from abd_sch_bzksrych 本专科生荣誉称号'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_bzksrych'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_bzkshd', 'abd_sch_bzkshd', '本专科生活动', TO_CLOB('
select
本专科生活动.xxbsm as 学校标识码,
本专科生活动.hdbm as 活动编码,
本专科生活动.hdmc as 活动名称,
本专科生活动.xnxq as 学年学期,
本专科生活动.hdrq as 活动日期,
本专科生活动.hdjb as 活动级别,
本专科生活动.hdlx as 活动类型,
本专科生活动.hdbq as 活动标签,
本专科生活动.hdpf as 活动评分
from abd_sch_bzkshd 本专科生活动'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_bzkshd'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_bzksshsj', 'abd_sch_bzksshsj', '本专科生社会实践', TO_CLOB('
select
本专科生社会实践.xxbsm as 学校标识码,
本专科生社会实践.shsjbm as 社会实践编码,
本专科生社会实践.xnxq as 学年学期,
本专科生社会实践.ztmc as 主题名称,
本专科生社会实践.sjlx as 实践类型
from abd_sch_bzksshsj 本专科生社会实践'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_bzksshsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_bzkszyfw', 'abd_sch_bzkszyfw', '本专科生志愿服务', TO_CLOB('
select
本专科生志愿服务.xxbsm as 学校标识码,
本专科生志愿服务.zyfwbm as 志愿服务编码,
本专科生志愿服务.xnxq as 学年学期,
本专科生志愿服务.hdmc as 活动名称,
本专科生志愿服务.hdjb as 活动级别
from abd_sch_bzkszyfw 本专科生志愿服务'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_bzkszyfw'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_bkspyfaxx', 'abd_sch_bkspyfaxx', '本科生培养方案信息', TO_CLOB('
select
本科生培养方案信息.xxbsm as 学校标识码,
本科生培养方案信息.pyfabm as 培养方案编码,
本科生培养方案信息.pyfamc as 培养方案名称,
本科生培养方案信息.mxnj as 面向年级,
本科生培养方案信息.zy as 专业,
本科生培养方案信息.zyfx as 专业方向,
本科生培养方案信息.xz as 学制,
本科生培养方案信息.xdlx as 修读类型,
本科生培养方案信息.xw as 学位,
本科生培养方案信息.pycc as 培养层次,
本科生培养方案信息.zsyqxf as 最少要求学分,
本科生培养方案信息.pymb as 培养目标,
本科生培养方案信息.xdyq as 修读要求,
本科生培养方案信息.fats as 方案特色,
本科生培养方案信息.zgxk as 主干学科,
本科生培养方案信息.byyq as 毕业要求
from abd_sch_bkspyfaxx 本科生培养方案信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_bkspyfaxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzksjbxx', 'abd_und_bzksjbxx', '本专科生基本信息', TO_CLOB('
select
本专科生基本信息.xh as 学号,
本专科生基本信息.xm as 姓名,
本专科生基本信息.xmpy as 姓名拼音,
本专科生基本信息.cym as 曾用名,
本专科生基本信息.xb as 性别,
本专科生基本信息.nl as 年龄,
本专科生基本信息.xx as 血型,
本专科生基本信息.csrq as 出生日期,
本专科生基本信息.csd as 出生地,
本专科生基本信息.jg as 籍贯,
本专科生基本信息.gjdq as 国家地区,
本专科生基本信息.mz as 民族,
本专科生基本信息.sfzjlx as 身份证件类型,
本专科生基本信息.hyzk as 婚姻状况,
本专科生基本信息.jkzk as 健康状况,
本专科生基本信息.gatqw as 港澳台侨外,
本专科生基本信息.zjxy as 宗教信仰,
本专科生基本信息.zzmm as 政治面貌,
本专科生基本信息.sstzz as 所属团组织,
本专科生基本信息.ssdzz as 所属党组织,
本专科生基本信息.yktkh as 一卡通卡号,
本专科生基本信息.wlzh as 网络账号,
本专科生基本信息.yxzh as 邮箱账号,
本专科生基本信息.xqh as 校区号,
本专科生基本信息.xq as 校区,
本专科生基本信息.xybm as 学院编码,
本专科生基本信息.xy as 学院,
本专科生基本信息.bh as 班号,
本专科生基本信息.bj as 班级,
本专科生基本信息.zybm as 专业编码,
本专科生基本信息.zy as 专业,
本专科生基本信息.nj as 年级,
本专科生基本信息.ssq as 宿舍区,
本专科生基本信息.ssl as 宿舍楼,
本专科生基本信息.s')||TO_CLOB('sdz as 宿舍地址,
本专科生基本信息.rxzp as 入学照片,
本专科生基本信息.xslb as 学生类别,
本专科生基本信息.pyfs as 培养方式,
本专科生基本信息.pycc as 培养层次,
本专科生基本信息.pyfa as 培养方案,
本专科生基本信息.xz as 学制,
本专科生基本信息.yjbyrq as 预计毕业日期,
本专科生基本信息.xjzt as 学籍状态,
本专科生基本信息.xsdqzt as 学生当前状态,
本专科生基本信息.dszgh as 导师职工号,
本专科生基本信息.kzrq as 快照日期
from abd_und_bzksjbxx 本专科生基本信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzksjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzklhlxsxx', 'abd_und_bzklhlxsxx', '本专科来华留学生信息', TO_CLOB('
select
本专科来华留学生信息.xh as 学号,
本专科来华留学生信息.xm as 姓名,
本专科来华留学生信息.ywxm as 英文姓名,
本专科来华留学生信息.hzx as 护照姓,
本专科来华留学生信息.hzm as 护照名,
本专科来华留学生信息.xb as 性别,
本专科来华留学生信息.xy as 学院,
本专科来华留学生信息.gj as 国籍,
本专科来华留学生信息.lygwgx as 来源国外高校,
本专科来华留学生信息.tlqx as 停留期限,
本专科来华留学生信息.csrq as 出生日期,
本专科来华留学生信息.csd as 出生地,
本专科来华留学生信息.zgxl as 最高学历,
本专科来华留学生信息.hyzk as 婚姻状况,
本专科来华留学生信息.xyzj as 信仰宗教,
本专科来华留学生信息.my as 母语,
本专科来华留学生信息.qzzlx as 签证注类型,
本专科来华留学生信息.tjdw as 推荐单位,
本专科来华留学生信息.zhswdbr as 在华事务担保人,
本专科来华留学生信息.zhswdbrdh as 在华事务担保人电话,
本专科来华留学生信息.jfly as 经费来源,
本专科来华留学生信息.lxslb as 留学生类别,
本专科来华留学生信息.zcrq as 注册日期,
本专科来华留学生信息.lxqx as 留学期限,
本专科来华留学生信息.lxrq as 来校日期,
本专科来华留学生信息.yzymc as 原专业名称,
本专科来华留学生信息.pzlxqx as 批准留学期限,
本专科来华留学生信息.hynl as 汉语能力,
本专科来华留学生信息.yynl as 英语能力,
本专科来华留学生信息.pycc a')||TO_CLOB('s 培养层次,
本专科来华留学生信息.skyy as 授课语言,
本专科来华留学生信息.sqrq as 申请日期,
本专科来华留学生信息.lqrq as 录取日期,
本专科来华留学生信息.lhrq as 来华日期,
本专科来华留学生信息.byrq as 毕业日期
from abd_und_bzklhlxsxx 本专科来华留学生信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzklhlxsxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzksxsxx', 'abd_und_bzksxsxx', '本专科生新生信息', TO_CLOB('
select
本专科生新生信息.xh as 学号,
本专科生新生信息.ksh as 考生号,
本专科生新生信息.kslb as 考生类别,
本专科生新生信息.lqlb as 录取类别,
本专科生新生信息.tzsh as 通知书号,
本专科生新生信息.rxny as 入学年月,
本专科生新生信息.rxjj as 入学季节,
本专科生新生信息.rxfs as 入学方式,
本专科生新生信息.rxnl as 入学年龄,
本专科生新生信息.sydsf as 生源地省份,
本专科生新生信息.gkzf as 高考总分,
本专科生新生信息.hkszd as 户口所在地,
本专科生新生信息.rxqdw as 入学前单位,
本专科生新生信息.dxhwpdw as 定向或委培单位,
本专科生新生信息.gkzp as 高考照片,
本专科生新生信息.syd as 生源地
from abd_und_bzksxsxx 本专科生新生信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzksxsxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzksxjydxx', 'abd_und_bzksxjydxx', '本专科生学籍异动信息', TO_CLOB('
select
本专科生学籍异动信息.xh as 学号,
本专科生学籍异动信息.ydlb as 异动类别,
本专科生学籍异动信息.ydyy as 异动原因,
本专科生学籍异动信息.xnxq as 学年学期,
本专科生学籍异动信息.ydrq as 异动日期,
本专科生学籍异动信息.ydqxy as 异动前学院,
本专科生学籍异动信息.ydqzy as 异动前专业,
本专科生学籍异动信息.ydqnj as 异动前年级,
本专科生学籍异动信息.ydqbj as 异动前班级,
本专科生学籍异动信息.ydqxz as 异动前学制,
本专科生学籍异动信息.ydqzyfx as 异动前专业方向,
本专科生学籍异动信息.yyjbyrq as 原预计毕业日期,
本专科生学籍异动信息.ydqdqzt as 异动前当前状态,
本专科生学籍异动信息.ydqzxzt as 异动前在校状态,
本专科生学籍异动信息.ydhxy as 异动后学院,
本专科生学籍异动信息.ydhzy as 异动后专业,
本专科生学籍异动信息.ydhnj as 异动后年级,
本专科生学籍异动信息.ydhbj as 异动后班级,
本专科生学籍异动信息.ydhxz as 异动后学制,
本专科生学籍异动信息.ydhxx as 异动后学校,
本专科生学籍异动信息.ydhzyfx as 异动后专业方向,
本专科生学籍异动信息.ydhdqzt as 异动后当前状态,
本专科生学籍异动信息.ydhzxzt as 异动后在校状态,
本专科生学籍异动信息.ydhbyrq as 异动后毕业日期,
本专科生学籍异动信息.spwh as 审批文号
from abd_und_bzksxjydxx 本专科生学籍异动信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzksxjydxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzksjtqk', 'abd_und_bzksjtqk', '本专科生家庭情况', TO_CLOB('
select
本专科生家庭情况.xh as 学号,
本专科生家庭情况.jtrks as 家庭人口数,
本专科生家庭情况.jtnsr as 家庭年收入,
本专科生家庭情况.jtlb as 家庭类别,
本专科生家庭情况.kncd as 困难程度,
本专科生家庭情况.sfdb as 是否低保,
本专科生家庭情况.rxqhkxz as 入学前户口性质
from abd_und_bzksjtqk 本专科生家庭情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzksjtqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzksjtcy', 'abd_und_bzksjtcy', '本专科生家庭成员', TO_CLOB('
select
本专科生家庭成员.xh as 学号,
本专科生家庭成员.cyxm as 成员姓名,
本专科生家庭成员.cygx as 成员关系,
本专科生家庭成员.jkzk as 健康状况,
本专科生家庭成员.gzdw as 工作单位,
本专科生家庭成员.dh as 电话,
本专科生家庭成员.dzxx as 电子信箱
from abd_und_bzksjtcy 本专科生家庭成员'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzksjtcy'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzkszhcpjg', 'abd_und_bzkszhcpjg', '本专科生综合测评结果', TO_CLOB('
select
本专科生综合测评结果.xh as 学号,
本专科生综合测评结果.xnxq as 学年学期,
本专科生综合测评结果.cprq as 测评日期,
本专科生综合测评结果.cpzcj as 测评总成绩,
本专科生综合测评结果.dj as 等级,
本专科生综合测评结果.cpzcjpm as 测评总成绩排名,
本专科生综合测评结果.cpzcjbjpm as 测评总成绩班级排名,
本专科生综合测评结果.cpzcjzypm as 测评总成绩专业排名
from abd_und_bzkszhcpjg 本专科生综合测评结果'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzkszhcpjg'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzkszhcpcjmx', 'abd_und_bzkszhcpcjmx', '本专科生综合测评成绩明细', TO_CLOB('
select
本专科生综合测评成绩明细.xh as 学号,
本专科生综合测评成绩明细.xnxq as 学年学期,
本专科生综合测评成绩明细.cprq as 测评日期,
本专科生综合测评成绩明细.cpxm as 测评项目,
本专科生综合测评成绩明细.cpxmcj as 测评项目成绩,
本专科生综合测评成绩明细.cpxmcjpm as 测评项目成绩排名,
本专科生综合测评成绩明细.cpxmcjbjpm as 测评项目成绩班级排名,
本专科生综合测评成绩明细.cpxmcjzypm as 测评项目成绩专业排名
from abd_und_bzkszhcpcjmx 本专科生综合测评成绩明细'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzkszhcpcjmx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzkshdjxjjl', 'abd_und_bzkshdjxjjl', '本专科生获得奖学金记录', TO_CLOB('
select
本专科生获得奖学金记录.xh as 学号,
本专科生获得奖学金记录.jxjbm as 奖学金编码,
本专科生获得奖学金记录.jxjmc as 奖学金名称,
本专科生获得奖学金记录.jxjlx as 奖学金类型,
本专科生获得奖学金记录.jljb as 奖励级别,
本专科生获得奖学金记录.jxjdj as 奖学金等级,
本专科生获得奖学金记录.xnxq as 学年学期,
本专科生获得奖学金记录.hjje as 获奖金额,
本专科生获得奖学金记录.ffrq as 发放日期,
本专科生获得奖学金记录.ffje as 发放金额
from abd_und_bzkshdjxjjl 本专科生获得奖学金记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzkshdjxjjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzkshdrychjl', 'abd_und_bzkshdrychjl', '本专科生获得荣誉称号记录', TO_CLOB('
select
本专科生获得荣誉称号记录.xh as 学号,
本专科生获得荣誉称号记录.rychbm as 荣誉称号编码,
本专科生获得荣誉称号记录.rychmc as 荣誉称号名称,
本专科生获得荣誉称号记录.sldw as 设立单位,
本专科生获得荣誉称号记录.rychlx as 荣誉称号类型,
本专科生获得荣誉称号记录.jljb as 奖励级别,
本专科生获得荣誉称号记录.xnxq as 学年学期,
本专科生获得荣誉称号记录.xy as 学院,
本专科生获得荣誉称号记录.hjje as 获奖金额
from abd_und_bzkshdrychjl 本专科生获得荣誉称号记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzkshdrychjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzkswjcf', 'abd_und_bzkswjcf', '本专科生违纪处分', TO_CLOB('
select
本专科生违纪处分.xh as 学号,
本专科生违纪处分.wjlb as 违纪类别,
本专科生违纪处分.xnxq as 学年学期,
本专科生违纪处分.wjrq as 违纪日期,
本专科生违纪处分.wjqkms as 违纪情况描述,
本专科生违纪处分.cfmc as 处分名称,
本专科生违纪处分.cfyy as 处分原因,
本专科生违纪处分.cfwh as 处分文号,
本专科生违纪处分.ckqy as 察看期月,
本专科生违纪处分.ckjzrq as 察看截止日期,
本专科生违纪处分.ssrq as 申诉日期,
本专科生违纪处分.ssjg as 申诉结果,
本专科生违纪处分.cfsfcx as 处分是否撤销,
本专科生违纪处分.cfcxrq as 处分撤消日期,
本专科生违纪处分.cfcxwh as 处分撤消文号,
本专科生违纪处分.cfcxyy as 处分撤消原因,
本专科生违纪处分.cfsfjc as 处分是否解除,
本专科生违纪处分.cfjcrq as 处分解除日期,
本专科生违纪处分.cfjcwh as 处分解除文号,
本专科生违纪处分.cfjcyy as 处分解除原因
from abd_und_bzkswjcf 本专科生违纪处分'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzkswjcf'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzkshjjl', 'abd_und_bzkshjjl', '本专科生获奖记录', TO_CLOB('
select
本专科生获奖记录.xh as 学号,
本专科生获奖记录.jlmc as 奖励名称,
本专科生获奖记录.bjdw as 颁奖单位,
本专科生获奖记录.jljb as 奖励级别,
本专科生获奖记录.hjxn as 获奖学年,
本专科生获奖记录.hjrq as 获奖日期,
本专科生获奖记录.jlje as 奖励金额
from abd_und_bzkshjjl 本专科生获奖记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzkshjjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzksknsxx', 'abd_und_bzksknsxx', '本专科生困难生信息', TO_CLOB('
select
本专科生困难生信息.xh as 学号,
本专科生困难生信息.rdxn as 认定学年,
本专科生困难生信息.rdrq as 认定日期,
本专科生困难生信息.rdknslx as 认定困难生类型
from abd_und_bzksknsxx 本专科生困难生信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzksknsxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzkshdzxjjl', 'abd_und_bzkshdzxjjl', '本专科生获得助学金记录', TO_CLOB('
select
本专科生获得助学金记录.xh as 学号,
本专科生获得助学金记录.zxjbm as 助学金编码,
本专科生获得助学金记录.zxjmc as 助学金名称,
本专科生获得助学金记录.jljb as 奖励级别,
本专科生获得助学金记录.zxjdj as 助学金等级,
本专科生获得助学金记录.zxje as 助学金额,
本专科生获得助学金记录.zzdwhgr as 资助单位或个人,
本专科生获得助学金记录.zjly as 资金来源,
本专科生获得助学金记录.xnxq as 学年学期
from abd_und_bzkshdzxjjl 本专科生获得助学金记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzkshdzxjjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzkszxjffjl', 'abd_und_bzkszxjffjl', '本专科生助学金发放记录', TO_CLOB('
select
本专科生助学金发放记录.xh as 学号,
本专科生助学金发放记录.zxjbm as 助学金编码,
本专科生助学金发放记录.zxjmc as 助学金名称,
本专科生助学金发放记录.zzdwhgr as 资助单位或个人,
本专科生助学金发放记录.zjly as 资金来源,
本专科生助学金发放记录.jljb as 奖励级别,
本专科生助学金发放记录.zxjdj as 助学金等级,
本专科生助学金发放记录.xnxq as 学年学期,
本专科生助学金发放记录.ffrq as 发放日期,
本专科生助学金发放记录.ffje as 发放金额
from abd_und_bzkszxjffjl 本专科生助学金发放记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzkszxjffjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzkshdknbzjl', 'abd_und_bzkshdknbzjl', '本专科生获得困难补助记录', TO_CLOB('
select
本专科生获得困难补助记录.xh as 学号,
本专科生获得困难补助记录.knbzbm as 困难补助编码,
本专科生获得困难补助记录.knbzmc as 困难补助名称,
本专科生获得困难补助记录.knbzdj as 困难补助等级,
本专科生获得困难补助记录.zjly as 资金来源,
本专科生获得困难补助记录.xnxq as 学年学期,
本专科生获得困难补助记录.bzje as 补助金额
from abd_und_bzkshdknbzjl 本专科生获得困难补助记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzkshdknbzjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzkshdzxdkjl', 'abd_und_bzkshdzxdkjl', '本专科生获得助学贷款记录', TO_CLOB('
select
本专科生获得助学贷款记录.xh as 学号,
本专科生获得助学贷款记录.jbxn as 经办学年,
本专科生获得助学贷款记录.dklx as 贷款类型,
本专科生获得助学贷款记录.dkhth as 贷款合同号,
本专科生获得助学贷款记录.dkbm as 贷款编码,
本专科生获得助学贷款记录.dkze as 贷款总额,
本专科生获得助学贷款记录.dknx as 贷款年限,
本专科生获得助学贷款记录.fkzt as 放款状态
from abd_und_bzkshdzxdkjl 本专科生获得助学贷款记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzkshdzxdkjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzksqjxx', 'abd_und_bzksqjxx', '本专科生请假信息', TO_CLOB('
select
本专科生请假信息.xh as 学号,
本专科生请假信息.xnxq as 学年学期,
本专科生请假信息.qjxz as 请假性质,
本专科生请假信息.qjlx as 请假类型,
本专科生请假信息.qjts as 请假天数,
本专科生请假信息.sflx as 是否离校,
本专科生请假信息.lxqx as 离校去向,
本专科生请假信息.sfxj as 是否销假
from abd_und_bzksqjxx 本专科生请假信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzksqjxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzksjjrlfxxx', 'abd_und_bzksjjrlfxxx', '本专科生节假日离返校信息', TO_CLOB('
select
本专科生节假日离返校信息.xh as 学号,
本专科生节假日离返校信息.xnxq as 学年学期,
本专科生节假日离返校信息.jjr as 节假日,
本专科生节假日离返校信息.lxqx as 离校去向,
本专科生节假日离返校信息.sflx as 是否离校,
本专科生节假日离返校信息.fxqk as 返校情况,
本专科生节假日离返校信息.fxrq as 返校日期,
本专科生节假日离返校信息.fxyqyy as 返校延期原因
from abd_und_bzksjjrlfxxx 本专科生节假日离返校信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzksjjrlfxxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzkscgjlxjl', 'abd_und_bzkscgjlxjl', '本专科生出国(境)留学记录', TO_CLOB('
select
本专科生出国境留学记录.xh as 学号,
本专科生出国境留学记录.xmbh as 项目编号,
本专科生出国境留学记录.lxdw as 留学单位,
本专科生出国境留学记录.ddrq as 抵达日期,
本专科生出国境留学记录.lxqx as 留学期限,
本专科生出国境留学记录.lxqxksrq as 留学期限开始日期,
本专科生出国境留学记录.lxqxjsrq as 留学期限结束日期,
本专科生出国境留学记录.ggrq as 归国日期,
本专科生出国境留学记录.hzzt as 护照状态
from abd_und_bzkscgjlxjl 本专科生出国境留学记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzkscgjlxjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzkscgjxm', 'abd_und_bzkscgjxm', '本专科生出国(境)项目', TO_CLOB('
select
本专科生出国境项目.xh as 学号,
本专科生出国境项目.xmbh as 项目编号,
本专科生出国境项目.xmmc as 项目名称,
本专科生出国境项目.xmlx as 项目类型,
本专科生出国境项目.xmzq as 项目周期,
本专科生出国境项目.qwgjdq as 前往国家地区,
本专科生出国境项目.fzdw as 负责单位,
本专科生出国境项目.lxdw as 留学单位,
本专科生出国境项目.pcqx as 派出期限,
本专科生出国境项目.xmme as 项目名额,
本专科生出国境项目.bmkssj as 报名开始时间,
本专科生出国境项目.bmjssj as 报名结束时间,
本专科生出国境项目.xmksrq as 项目开始日期,
本专科生出国境项目.xmjsrq as 项目结束日期,
本专科生出国境项目.xmjs as 项目介绍,
本专科生出国境项目.lxdwjj as 留学单位简介
from abd_und_bzkscgjxm 本专科生出国境项目'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzkscgjxm'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzkszsxx', 'abd_und_bzkszsxx', '本专科生住宿信息', TO_CLOB('
select
本专科生住宿信息.xh as 学号,
本专科生住宿信息.ssq as 宿舍区,
本专科生住宿信息.ssl as 宿舍楼,
本专科生住宿信息.szss as 所在宿舍,
本专科生住宿信息.cwh as 床位号
from abd_und_bzkszsxx 本专科生住宿信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzkszsxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzkssscqxx', 'abd_und_bzkssscqxx', '本专科生宿舍查寝信息', TO_CLOB('
select
本专科生宿舍查寝信息.xh as 学号,
本专科生宿舍查寝信息.xn as 学年,
本专科生宿舍查寝信息.kqrq as 考勤日期,
本专科生宿舍查寝信息.gqlx as 归寝类型,
本专科生宿舍查寝信息.gqsj as 归寝时间,
本专科生宿舍查寝信息.wgyy as 晚归原因
from abd_und_bzkssscqxx 本专科生宿舍查寝信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzkssscqxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzkstsjyxx', 'abd_und_bzkstsjyxx', '本专科生图书借阅信息', TO_CLOB('
select
本专科生图书借阅信息.xh as 学号,
本专科生图书借阅信息.xm as 姓名,
本专科生图书借阅信息.rylx as 人员类型,
本专科生图书借阅信息.dzzt as 读者状态,
本专科生图书借阅信息.tsbh as 图书编号,
本专科生图书借阅信息.tsm as 图书名,
本专科生图书借阅信息.tslx as 图书类型,
本专科生图书借阅信息.jyrq as 借阅日期,
本专科生图书借阅信息.ghrq as 归还日期,
本专科生图书借阅信息.yhrq as 应还日期,
本专科生图书借阅信息.sfxj as 是否续借,
本专科生图书借阅信息.xjrq as 续借日期
from abd_und_bzkstsjyxx 本专科生图书借阅信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzkstsjyxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzksyktxfxx', 'abd_und_bzksyktxfxx', '本专科生一卡通消费信息', TO_CLOB('
select
本专科生一卡通消费信息.xh as 学号,
本专科生一卡通消费信息.kh as 卡号,
本专科生一卡通消费信息.zhye as 账户余额,
本专科生一卡通消费信息.kzt as 卡状态,
本专科生一卡通消费信息.lsh as 流水号,
本专科生一卡通消费信息.shmc as 商户名称,
本专科生一卡通消费信息.shlx as 商户类型,
本专科生一卡通消费信息.sbbh as 设备编号,
本专科生一卡通消费信息.jysj as 交易时间,
本专科生一卡通消费信息.jylx as 交易类型,
本专科生一卡通消费信息.xfje as 消费金额,
本专科生一卡通消费信息.ljsycs as 累计使用次数
from abd_und_bzksyktxfxx 本专科生一卡通消费信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzksyktxfxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzksswxx', 'abd_und_bzksswxx', '本专科生上网信息', TO_CLOB('
select
本专科生上网信息.xh as 学号,
本专科生上网信息.zh as 账号,
本专科生上网信息.sxsj as 上线时间,
本专科生上网信息.xxsj as 下线时间,
本专科生上网信息.ll as 流量,
本专科生上网信息.sz as 时长,
本专科生上网信息.ktsj as 开通时间,
本专科生上网信息.tysj as 停用时间,
本专科生上网信息.zhzt as 账号状态
from abd_und_bzksswxx 本专科生上网信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzksswxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzksxkjsxx', 'abd_und_bzksxkjsxx', '本专科生学科竞赛信息', TO_CLOB('
select
本专科生学科竞赛信息.xh as 学号,
本专科生学科竞赛信息.jsbh as 竞赛编号,
本专科生学科竞赛信息.xnxq as 学年学期,
本专科生学科竞赛信息.jsmc as 竞赛名称,
本专科生学科竞赛信息.jsjb as 竞赛级别,
本专科生学科竞赛信息.jsxk as 竞赛学科,
本专科生学科竞赛信息.jsksrq as 竞赛开始日期,
本专科生学科竞赛信息.jsjsrq as 竞赛结束日期,
本专科生学科竞赛信息.tdbh as 团队编号,
本专科生学科竞赛信息.tdmc as 团队名称,
本专科生学科竞赛信息.zpbh as 作品编号,
本专科生学科竞赛信息.zpmc as 作品名称,
本专科生学科竞赛信息.sftdfzr as 是否团队负责人,
本专科生学科竞赛信息.brpx as 本人排序,
本专科生学科竞赛信息.jscj as 竞赛成绩,
本专科生学科竞赛信息.jljb as 奖励级别,
本专科生学科竞赛信息.jldj as 奖励等级,
本专科生学科竞赛信息.hjmc as 获奖名次,
本专科生学科竞赛信息.hjrq as 获奖日期,
本专科生学科竞赛信息.hjzsbh as 获奖证书编号
from abd_und_bzksxkjsxx 本专科生学科竞赛信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzksxkjsxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzksjyxx', 'abd_und_bzksjyxx', '本专科生就业信息', TO_CLOB('
select
本专科生就业信息.xh as 学号,
本专科生就业信息.xysbh as 协议书编号,
本专科生就业信息.xyqdrq as 协议签订日期,
本专科生就业信息.xynx as 协议年限,
本专科生就业信息.jylsfs as 就业落实方式,
本专科生就业信息.byqx as 毕业去向,
本专科生就业信息.jydwhsxxx as 就业单位或升学学校,
本专科生就业信息.dwhxxszgjhdq as 单位或学校所在国家或地区,
本专科生就业信息.dwhxxszsf as 单位或学校所在省份,
本专科生就业信息.dwhxxszcs as 单位或学校所在城市,
本专科生就业信息.dwhxxzgbm as 单位或学校主管部门,
本专科生就业信息.shdwxz as 社会单位性质,
本专科生就业信息.dwjjxz as 单位经济性质,
本专科生就业信息.dwtgddy as 单位提供的待遇,
本专科生就业信息.dajsdz as 档案接收地址,
本专科生就业信息.jsdyzbm as 接收地邮政编码,
本专科生就业信息.gzgwxz as 工作岗位性质,
本专科生就业信息.dwxy as 单位行业,
本专科生就业信息.bdzh as 报到证号,
本专科生就业信息.yrdwyrxs as 用人单位用人形式
from abd_und_bzksjyxx 本专科生就业信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzksjyxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzksxycj', 'abd_und_bzksxycj', '本专科生学业成绩', TO_CLOB('
select
本专科生学业成绩.xh as 学号,
本专科生学业成绩.xnxq as 学年学期,
本专科生学业成绩.kcmc as 课程名称,
本专科生学业成绩.kcsx as 课程属性,
本专科生学业成绩.ksrq as 考试日期,
本专科生学业成绩.ksxz as 考试性质,
本专科生学业成绩.xdfs as 修读方式,
本专科生学业成绩.bfzzcj as 百分制总成绩,
本专科生学业成绩.kcdjcj as 课程等级成绩,
本专科生学业成绩.xf as 学分,
本专科生学业成绩.jdf as 绩点分,
本专科生学业成绩.sfjg as 是否及格,
本专科生学业成绩.sfcyxfjjs as 是否参与学分绩计算
from abd_und_bzksxycj 本专科生学业成绩'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzksxycj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzkstccj', 'abd_und_bzkstccj', '本专科生体测成绩', TO_CLOB('
select
本专科生体测成绩.xh as 学号,
本专科生体测成绩.tcrq as 体测日期,
本专科生体测成绩.cjzf as 成绩总分,
本专科生体测成绩.sfjg as 是否及格,
本专科生体测成绩.zcjdj as 总成绩等级
from abd_und_bzkstccj 本专科生体测成绩'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzkstccj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzksxszbbxx', 'abd_und_bzksxszbbxx', '本专科生学生证补办信息', TO_CLOB('
select
本专科生学生证补办信息.xh as 学号,
本专科生学生证补办信息.xnxq as 学年学期,
本专科生学生证补办信息.bbrq as 补办日期
from abd_und_bzksxszbbxx 本专科生学生证补办信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzksxszbbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzksjyjl', 'abd_und_bzksjyjl', '本专科生教育经历', TO_CLOB('
select
本专科生教育经历.xh as 学号,
本专科生教育经历.rxny as 入学年月,
本专科生教育经历.byny as 毕业年月,
本专科生教育经历.byyx as 毕业院校,
本专科生教育经历.sxzy as 所学专业,
本专科生教育经历.shxl as 所获学历
from abd_und_bzksjyjl 本专科生教育经历'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzksjyjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzksqgzxsgjl', 'abd_und_bzksqgzxsgjl', '本专科生勤工助学上岗记录', TO_CLOB('
select
本专科生勤工助学上岗记录.xh as 学号,
本专科生勤工助学上岗记录.xnxq as 学年学期,
本专科生勤工助学上岗记录.gwbm as 岗位编码,
本专科生勤工助学上岗记录.sfxndw as 是否校内单位,
本专科生勤工助学上岗记录.gwmc as 岗位名称,
本专科生勤工助学上岗记录.gwlx as 岗位类型,
本专科生勤工助学上岗记录.ygdw as 用工单位,
本专科生勤工助学上岗记录.xqxslx as 需求学生类型,
本专科生勤工助学上岗记录.gwyq as 岗位要求,
本专科生勤工助学上岗记录.gzdd as 工作地点,
本专科生勤工助学上岗记录.bcbz as 报酬标准,
本专科生勤工助学上岗记录.bcjsdw as 报酬计算单位,
本专科生勤工助学上岗记录.gzksrq as 工作开始日期,
本专科生勤工助学上岗记录.gzjsrq as 工作结束日期,
本专科生勤工助学上岗记录.gs as 工时,
本专科生勤工助学上岗记录.zgzt as 在岗状态
from abd_und_bzksqgzxsgjl 本专科生勤工助学上岗记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzksqgzxsgjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzksqgzxbcffjl', 'abd_und_bzksqgzxbcffjl', '本专科生勤工助学报酬发放记录', TO_CLOB('
select
本专科生勤工助学报酬发放记录.xh as 学号,
本专科生勤工助学报酬发放记录.xnxq as 学年学期,
本专科生勤工助学报酬发放记录.gwmc as 岗位名称,
本专科生勤工助学报酬发放记录.sfxndw as 是否校内单位,
本专科生勤工助学报酬发放记录.gwlx as 岗位类型,
本专科生勤工助学报酬发放记录.gwbm as 岗位编码,
本专科生勤工助学报酬发放记录.sfje as 实发金额,
本专科生勤工助学报酬发放记录.ffrq as 发放日期
from abd_und_bzksqgzxbcffjl 本专科生勤工助学报酬发放记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzksqgzxbcffjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzksknbzffjl', 'abd_und_bzksknbzffjl', '本专科生困难补助发放记录', TO_CLOB('
select
本专科生困难补助发放记录.xh as 学号,
本专科生困难补助发放记录.knbzbm as 困难补助编码,
本专科生困难补助发放记录.knbzmc as 困难补助名称,
本专科生困难补助发放记录.knbzdj as 困难补助等级,
本专科生困难补助发放记录.zjly as 资金来源,
本专科生困难补助发放记录.xnxq as 学年学期,
本专科生困难补助发放记录.ffrq as 发放日期,
本专科生困难补助发放记录.ffje as 发放金额
from abd_und_bzksknbzffjl 本专科生困难补助发放记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzksknbzffjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzkszxdkffjl', 'abd_und_bzkszxdkffjl', '本专科生助学贷款发放记录', TO_CLOB('
select
本专科生助学贷款发放记录.xh as 学号,
本专科生助学贷款发放记录.dklx as 贷款类型,
本专科生助学贷款发放记录.dkhth as 贷款合同号,
本专科生助学贷款发放记录.jbxn as 经办学年,
本专科生助学贷款发放记录.fkxn as 放款学年,
本专科生助学贷款发放记录.ffje as 发放金额,
本专科生助学贷款发放记录.ffrq as 发放日期
from abd_und_bzkszxdkffjl 本专科生助学贷款发放记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzkszxdkffjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzkszxdkhkjl', 'abd_und_bzkszxdkhkjl', '本专科生助学贷款还款记录', TO_CLOB('
select
本专科生助学贷款还款记录.xh as 学号,
本专科生助学贷款还款记录.dklx as 贷款类型,
本专科生助学贷款还款记录.dkhth as 贷款合同号,
本专科生助学贷款还款记录.jbxn as 经办学年,
本专科生助学贷款还款记录.hkrq as 还款日期,
本专科生助学贷款还款记录.hkfs as 还款方式,
本专科生助学贷款还款记录.hkje as 还款金额
from abd_und_bzkszxdkhkjl 本专科生助学贷款还款记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzkszxdkhkjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzksrwjl', 'abd_und_bzksrwjl', '本专科生入伍记录', TO_CLOB('
select
本专科生入伍记录.xh as 学号,
本专科生入伍记录.rwtzsbh as 入伍通知书编号,
本专科生入伍记录.rwrq as 入伍日期,
本专科生入伍记录.rwlb as 入伍类别,
本专科生入伍记录.rwddlb as 入伍地点类别,
本专科生入伍记录.zbbgs as 征兵办公室,
本专科生入伍记录.bylb as 兵役类别,
本专科生入伍记录.tyrq as 退役日期,
本专科生入伍记录.fxrq as 复学日期,
本专科生入伍记录.fxjdnx as 复学就读年限
from abd_und_bzksrwjl 本专科生入伍记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzksrwjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzksbyxx', 'abd_und_bzksbyxx', '本专科生毕业信息', TO_CLOB('
select
本专科生毕业信息.xh as 学号,
本专科生毕业信息.xb as 性别,
本专科生毕业信息.byzsh as 毕业证书号,
本专科生毕业信息.xwzsh as 学位证书号,
本专科生毕业信息.xwlb as 学位类别,
本专科生毕业信息.byzy as 毕业专业,
本专科生毕业信息.shxw as 所获学位,
本专科生毕业信息.syxwrq as 授予学位日期,
本专科生毕业信息.xdlx as 修读类型,
本专科生毕业信息.xwjl as 学位结论,
本专科生毕业信息.sjbyrq as 实际毕业日期
from abd_und_bzksbyxx 本专科生毕业信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzksbyxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_col_0055', 'und_si_col_0055', '每年各学院本专科生获得奖学金情况', TO_CLOB('
select
每年各学院本专科生获得奖学金情况.xybh as 学院编号,
每年各学院本专科生获得奖学金情况.tjnf as 统计年份,
每年各学院本专科生获得奖学金情况.hjje as 获奖金额,
每年各学院本专科生获得奖学金情况.hjrc as 获奖人次
from und_si_col_0055 每年各学院本专科生获得奖学金情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_col_0055'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_col_0056', 'und_si_col_0056', '每年各学院本专科生获得各级别奖学金情况', TO_CLOB('
select
每年各学院本专科生获得各级别奖学金情况.xybh as 学院编号,
每年各学院本专科生获得各级别奖学金情况.jljb as 奖励级别,
每年各学院本专科生获得各级别奖学金情况.tjnf as 统计年份,
每年各学院本专科生获得各级别奖学金情况.hjje as 获奖金额,
每年各学院本专科生获得各级别奖学金情况.hjrc as 获奖人次
from und_si_col_0056 每年各学院本专科生获得各级别奖学金情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_col_0056'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_col_0057', 'und_si_col_0057', '每年学院本专科生获得奖励情况', TO_CLOB('
select
每年学院本专科生获得奖励情况.xybh as 学院编号,
每年学院本专科生获得奖励情况.tjnf as 统计年份,
每年学院本专科生获得奖励情况.jlje as 奖励金额,
每年学院本专科生获得奖励情况.hjrc as 获奖人次
from und_si_col_0057 每年学院本专科生获得奖励情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_col_0057'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_col_0058', 'und_si_col_0058', '每年学院本专科生获得各级别奖励情况', TO_CLOB('
select
每年学院本专科生获得各级别奖励情况.xybh as 学院编号,
每年学院本专科生获得各级别奖励情况.jljb as 奖励级别,
每年学院本专科生获得各级别奖励情况.tjnf as 统计年份,
每年学院本专科生获得各级别奖励情况.jlje as 奖励金额,
每年学院本专科生获得各级别奖励情况.jlrc as 奖励人次
from und_si_col_0058 每年学院本专科生获得各级别奖励情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_col_0058'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_col_0059', 'und_si_col_0059', '每年学院本专科生获荣誉称号情况', TO_CLOB('
select
每年学院本专科生获荣誉称号情况.xybh as 学院编号,
每年学院本专科生获荣誉称号情况.tjnf as 统计年份,
每年学院本专科生获荣誉称号情况.hjje as 获奖金额,
每年学院本专科生获荣誉称号情况.hjrc as 获奖人次
from und_si_col_0059 每年学院本专科生获荣誉称号情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_col_0059'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_col_0060', 'und_si_col_0060', '每年学院本专科生获各级别荣誉称号情况', TO_CLOB('
select
每年学院本专科生获各级别荣誉称号情况.xybh as 学院编号,
每年学院本专科生获各级别荣誉称号情况.jljb as 奖励级别,
每年学院本专科生获各级别荣誉称号情况.tjnf as 统计年份,
每年学院本专科生获各级别荣誉称号情况.hjje as 获奖金额,
每年学院本专科生获各级别荣誉称号情况.hjrc as 获奖人次
from und_si_col_0060 每年学院本专科生获各级别荣誉称号情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_col_0060'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_col_0061', 'und_si_col_0061', '每年学院本专科生获各类荣誉称号情况', TO_CLOB('
select
每年学院本专科生获各类荣誉称号情况.xybh as 学院编号,
每年学院本专科生获各类荣誉称号情况.jljb as 奖励级别,
每年学院本专科生获各类荣誉称号情况.tjnf as 统计年份,
每年学院本专科生获各类荣誉称号情况.hjje as 获奖金额,
每年学院本专科生获各类荣誉称号情况.hjrc as 获奖人次
from und_si_col_0061 每年学院本专科生获各类荣誉称号情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_col_0061'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_col_0062', 'und_si_col_0062', '每年各学院本专科生违纪处分人次', TO_CLOB('
select
每年各学院本专科生违纪处分人次.xybh as 学院编号,
每年各学院本专科生违纪处分人次.tjnf as 统计年份,
每年各学院本专科生违纪处分人次.cfrc as 处分人次
from und_si_col_0062 每年各学院本专科生违纪处分人次'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_col_0062'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_col_0063', 'und_si_col_0063', '每年各学院本专科生各类违纪处分人次', TO_CLOB('
select
每年各学院本专科生各类违纪处分人次.xybh as 学院编号,
每年各学院本专科生各类违纪处分人次.wjlb as 违纪类别,
每年各学院本专科生各类违纪处分人次.tjnf as 统计年份,
每年各学院本专科生各类违纪处分人次.cfrc as 处分人次
from und_si_col_0063 每年各学院本专科生各类违纪处分人次'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_col_0063'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_col_0064', 'und_si_col_0064', '每年各学院本专科生各处分名称人次', TO_CLOB('
select
每年各学院本专科生各处分名称人次.xybh as 学院编号,
每年各学院本专科生各处分名称人次.cfmc as 处分名称,
每年各学院本专科生各处分名称人次.tjnf as 统计年份,
每年各学院本专科生各处分名称人次.cfrc as 处分人次
from und_si_col_0064 每年各学院本专科生各处分名称人次'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_col_0064'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_col_0065', 'und_si_col_0065', '每年学院本专科生勤工助学情况', TO_CLOB('
select
每年学院本专科生勤工助学情况.xybh as 学院编号,
每年学院本专科生勤工助学情况.tjnf as 统计年份,
每年学院本专科生勤工助学情况.qgzxrc as 勤工助学人次,
每年学院本专科生勤工助学情况.qgzxje as 勤工助学金额
from und_si_col_0065 每年学院本专科生勤工助学情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_col_0065'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_col_0066', 'und_si_col_0066', '每年学院本专科生各岗位类型勤工助学情况', TO_CLOB('
select
每年学院本专科生各岗位类型勤工助学情况.xybh as 学院编号,
每年学院本专科生各岗位类型勤工助学情况.tjnf as 统计年份,
每年学院本专科生各岗位类型勤工助学情况.qgzxrc as 勤工助学人次,
每年学院本专科生各岗位类型勤工助学情况.qgzxje as 勤工助学金额
from und_si_col_0066 每年学院本专科生各岗位类型勤工助学情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_col_0066'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_col_0067', 'und_si_col_0067', '每年学院校内外岗位勤工助学情况', TO_CLOB('
select
每年学院校内外岗位勤工助学情况.xybh as 学院编号,
每年学院校内外岗位勤工助学情况.tjnf as 统计年份,
每年学院校内外岗位勤工助学情况.xnqgzxrc as 校内勤工助学人次,
每年学院校内外岗位勤工助学情况.xnqgzxje as 校内勤工助学金额,
每年学院校内外岗位勤工助学情况.xwqgzxrc as 校外勤工助学人次,
每年学院校内外岗位勤工助学情况.xwqgzxje as 校外勤工助学金额
from und_si_col_0067 每年学院校内外岗位勤工助学情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_col_0067'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_col_0068', 'und_si_col_0068', '每月学院本专科生勤工助学平均工作时长', TO_CLOB('
select
每月学院本专科生勤工助学平均工作时长.xybh as 学院编号,
每月学院本专科生勤工助学平均工作时长.tjyf as 统计月份,
每月学院本专科生勤工助学平均工作时长.qgzxsz as 勤工助学时长
from und_si_col_0068 每月学院本专科生勤工助学平均工作时长'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_col_0068'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_col_0069', 'und_si_col_0069', '学院本专科生校内岗位勤工助学每小时平均酬金', TO_CLOB('
select
学院本专科生校内岗位勤工助学每小时平均酬金.xybh as 学院编号,
学院本专科生校内岗位勤工助学每小时平均酬金.qgzxje as 勤工助学金额,
学院本专科生校内岗位勤工助学每小时平均酬金.tjxq as 统计学期
from und_si_col_0069 学院本专科生校内岗位勤工助学每小时平均酬金'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_col_0069'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_col_0070', 'und_si_col_0070', '各学院本专科生政治面貌占比', TO_CLOB('
select
各学院本专科生政治面貌占比.xybh as 学院编号,
各学院本专科生政治面貌占比.zzmm as 政治面貌,
各学院本专科生政治面貌占比.tjrq as 统计日期,
各学院本专科生政治面貌占比.zgdybl as 中共党员比例,
各学院本专科生政治面貌占比.gqtybl as 共青团员比例,
各学院本专科生政治面貌占比.qzbl as 群众比例
from und_si_col_0070 各学院本专科生政治面貌占比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_col_0070'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_col_0071', 'und_si_col_0071', '每年各学院本专科生学业成绩挂科率', TO_CLOB('
select
每年各学院本专科生学业成绩挂科率.xybh as 学院编号,
每年各学院本专科生学业成绩挂科率.tjnf as 统计年份,
每年各学院本专科生学业成绩挂科率.gkl as 挂科率
from und_si_col_0071 每年各学院本专科生学业成绩挂科率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_col_0071'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_col_0072', 'und_si_col_0072', '每年各学院本专科生四六级通过率', TO_CLOB('
select
每年各学院本专科生四六级通过率.xybh as 学院编号,
每年各学院本专科生四六级通过率.tjnf as 统计年份,
每年各学院本专科生四六级通过率.sjtgl as 四级通过率,
每年各学院本专科生四六级通过率.ljtgl as 六级通过率
from und_si_col_0072 每年各学院本专科生四六级通过率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_col_0072'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_col_0073', 'und_si_col_0073', '每年各专业本专科生专业平均分', TO_CLOB('
select
每年各专业本专科生专业平均分.xybh as 学院编号,
每年各专业本专科生专业平均分.tjnf as 统计年份,
每年各专业本专科生专业平均分.zypjf as 专业平均分
from und_si_col_0073 每年各专业本专科生专业平均分'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_col_0073'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_col_0074', 'und_si_col_0074', '每年学院本专科生获智育竞赛奖项数量', TO_CLOB('
select
每年学院本专科生获智育竞赛奖项数量.xybh as 学院编号,
每年学院本专科生获智育竞赛奖项数量.tjnf as 统计年份,
每年学院本专科生获智育竞赛奖项数量.jxsl as 奖项数量
from und_si_col_0074 每年学院本专科生获智育竞赛奖项数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_col_0074'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_col_0075', 'und_si_col_0075', '每年学院本专科生获智育竞赛各级别奖项数量', TO_CLOB('
select
每年学院本专科生获智育竞赛各级别奖项数量.xybh as 学院编号,
每年学院本专科生获智育竞赛各级别奖项数量.tjnf as 统计年份,
每年学院本专科生获智育竞赛各级别奖项数量.jxsl as 奖项数量
from und_si_col_0075 每年学院本专科生获智育竞赛各级别奖项数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_col_0075'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_col_0076', 'und_si_col_0076', '每学期各学院本专科生体育成绩挂科率', TO_CLOB('
select
每学期各学院本专科生体育成绩挂科率.xybh as 学院编号,
每学期各学院本专科生体育成绩挂科率.tjxq as 统计学期,
每学期各学院本专科生体育成绩挂科率.gkl as 挂科率
from und_si_col_0076 每学期各学院本专科生体育成绩挂科率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_col_0076'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_col_0077', 'und_si_col_0077', '每学期各学院体育成绩均分', TO_CLOB('
select
每学期各学院体育成绩均分.xybh as 学院编号,
每学期各学院体育成绩均分.tjxq as 统计学期,
每学期各学院体育成绩均分.typjf as 体育平均分
from und_si_col_0077 每学期各学院体育成绩均分'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_col_0077'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_col_0078', 'und_si_col_0078', '每学年各学院本专科生体测成绩及格率', TO_CLOB('
select
每学年各学院本专科生体测成绩及格率.xybh as 学院编号,
每学年各学院本专科生体测成绩及格率.tjnf as 统计年份,
每学年各学院本专科生体测成绩及格率.jgl as 及格率
from und_si_col_0078 每学年各学院本专科生体测成绩及格率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_col_0078'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_col_0079', 'und_si_col_0079', '每学年各学院体测成绩均分', TO_CLOB('
select
每学年各学院体测成绩均分.xybh as 学院编号,
每学年各学院体测成绩均分.tjnf as 统计年份,
每学年各学院体测成绩均分.tcpjf as 体测平均分
from und_si_col_0079 每学年各学院体测成绩均分'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_col_0079'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_col_0080', 'und_si_col_0080', '每年各学院本专科生参与志愿活动人数比例', TO_CLOB('
select
每年各学院本专科生参与志愿活动人数比例.xybh as 学院编号,
每年各学院本专科生参与志愿活动人数比例.tjnf as 统计年份,
每年各学院本专科生参与志愿活动人数比例.rszb as 人数占比
from und_si_col_0080 每年各学院本专科生参与志愿活动人数比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_col_0080'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_col_0081', 'und_si_col_0081', '每年各学院本专科生人均志愿服务时长', TO_CLOB('
select
每年各学院本专科生人均志愿服务时长.xybh as 学院编号,
每年各学院本专科生人均志愿服务时长.tjnf as 统计年份,
每年各学院本专科生人均志愿服务时长.pjzysz as 平均志愿时长
from und_si_col_0081 每年各学院本专科生人均志愿服务时长'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_col_0081'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_col_0082', 'und_si_col_0082', '每年各学院本专科生参与社会实践人数比例', TO_CLOB('
select
每年各学院本专科生参与社会实践人数比例.xybh as 学院编号,
每年各学院本专科生参与社会实践人数比例.tjnf as 统计年份,
每年各学院本专科生参与社会实践人数比例.rszb as 人数占比
from und_si_col_0082 每年各学院本专科生参与社会实践人数比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_col_0082'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_col_0083', 'und_si_col_0083', '每年各学院平均创新创业数量', TO_CLOB('
select
每年各学院平均创新创业数量.xybh as 学院编号,
每年各学院平均创新创业数量.tjnf as 统计年份,
每年各学院平均创新创业数量.xmsl as 项目数量
from und_si_col_0083 每年各学院平均创新创业数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_col_0083'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0083', 'und_si_sch_0083', '学校本专科生请假人数', TO_CLOB('
select
学校本专科生请假人数.xxbsm as 学校标识码,
学校本专科生请假人数.tjrq as 统计日期,
学校本专科生请假人数.qjrs as 请假人数
from und_si_sch_0083 学校本专科生请假人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0083'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0084', 'und_si_sch_0084', '学校本专科生平均请假天数', TO_CLOB('
select
学校本专科生平均请假天数.xxbsm as 学校标识码,
学校本专科生平均请假天数.tjnf as 统计年份,
学校本专科生平均请假天数.pjqjts as 平均请假天数
from und_si_sch_0084 学校本专科生平均请假天数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0084'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0085', 'und_si_sch_0085', '每年学校本专科生获得奖学金情况', TO_CLOB('
select
每年学校本专科生获得奖学金情况.xxbsm as 学校标识码,
每年学校本专科生获得奖学金情况.tjnf as 统计年份,
每年学校本专科生获得奖学金情况.hjje as 获奖金额,
每年学校本专科生获得奖学金情况.hjrc as 获奖人次
from und_si_sch_0085 每年学校本专科生获得奖学金情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0085'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0086', 'und_si_sch_0086', '每年学校本专科生获得各级别奖学金情况', TO_CLOB('
select
每年学校本专科生获得各级别奖学金情况.xxbsm as 学校标识码,
每年学校本专科生获得各级别奖学金情况.jljb as 奖励级别,
每年学校本专科生获得各级别奖学金情况.tjnf as 统计年份,
每年学校本专科生获得各级别奖学金情况.hjje as 获奖金额,
每年学校本专科生获得各级别奖学金情况.hjrc as 获奖人次
from und_si_sch_0086 每年学校本专科生获得各级别奖学金情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0086'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0087', 'und_si_sch_0087', '每年学校本专科生获得奖励情况', TO_CLOB('
select
每年学校本专科生获得奖励情况.xxbsm as 学校标识码,
每年学校本专科生获得奖励情况.tjnf as 统计年份,
每年学校本专科生获得奖励情况.jlje as 奖励金额,
每年学校本专科生获得奖励情况.hjrc as 获奖人次
from und_si_sch_0087 每年学校本专科生获得奖励情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0087'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0088', 'und_si_sch_0088', '每年学校本专科生获得各级别奖励情况', TO_CLOB('
select
每年学校本专科生获得各级别奖励情况.xxbsm as 学校标识码,
每年学校本专科生获得各级别奖励情况.jljb as 奖励级别,
每年学校本专科生获得各级别奖励情况.tjnf as 统计年份,
每年学校本专科生获得各级别奖励情况.jlje as 奖励金额,
每年学校本专科生获得各级别奖励情况.jlrc as 奖励人次
from und_si_sch_0088 每年学校本专科生获得各级别奖励情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0088'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0089', 'und_si_sch_0089', '每年学校本专科生获荣誉称号情况', TO_CLOB('
select
每年学校本专科生获荣誉称号情况.xxbsm as 学校标识码,
每年学校本专科生获荣誉称号情况.tjnf as 统计年份,
每年学校本专科生获荣誉称号情况.hjje as 获奖金额,
每年学校本专科生获荣誉称号情况.hjrc as 获奖人次
from und_si_sch_0089 每年学校本专科生获荣誉称号情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0089'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0090', 'und_si_sch_0090', '每年学校本专科生获各类荣誉称号情况', TO_CLOB('
select
每年学校本专科生获各类荣誉称号情况.xxbsm as 学校标识码,
每年学校本专科生获各类荣誉称号情况.tjnf as 统计年份,
每年学校本专科生获各类荣誉称号情况.hjje as 获奖金额,
每年学校本专科生获各类荣誉称号情况.hjrc as 获奖人次
from und_si_sch_0090 每年学校本专科生获各类荣誉称号情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0090'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0091', 'und_si_sch_0091', '每年学校本专科生获各级别荣誉称号情况', TO_CLOB('
select
每年学校本专科生获各级别荣誉称号情况.xxbsm as 学校标识码,
每年学校本专科生获各级别荣誉称号情况.jljb as 奖励级别,
每年学校本专科生获各级别荣誉称号情况.tjnf as 统计年份,
每年学校本专科生获各级别荣誉称号情况.hjje as 获奖金额,
每年学校本专科生获各级别荣誉称号情况.hjrc as 获奖人次
from und_si_sch_0091 每年学校本专科生获各级别荣誉称号情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0091'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0092', 'und_si_sch_0092', '每年学校本专科生违纪处分人次', TO_CLOB('
select
每年学校本专科生违纪处分人次.xxbsm as 学校标识码,
每年学校本专科生违纪处分人次.tjnf as 统计年份,
每年学校本专科生违纪处分人次.cfrc as 处分人次
from und_si_sch_0092 每年学校本专科生违纪处分人次'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0092'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0093', 'und_si_sch_0093', '每年学校本专科生各类违纪处分人次', TO_CLOB('
select
每年学校本专科生各类违纪处分人次.xxbsm as 学校标识码,
每年学校本专科生各类违纪处分人次.wjlb as 违纪类别,
每年学校本专科生各类违纪处分人次.tjnf as 统计年份,
每年学校本专科生各类违纪处分人次.cfrc as 处分人次
from und_si_sch_0093 每年学校本专科生各类违纪处分人次'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0093'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0094', 'und_si_sch_0094', '每年学校本专科生各处分名称人次', TO_CLOB('
select
每年学校本专科生各处分名称人次.xxbsm as 学校标识码,
每年学校本专科生各处分名称人次.cfmc as 处分名称,
每年学校本专科生各处分名称人次.tjnf as 统计年份,
每年学校本专科生各处分名称人次.cfrc as 处分人次
from und_si_sch_0094 每年学校本专科生各处分名称人次'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0094'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0095', 'und_si_sch_0095', '每年学校本专科生勤工助学情况', TO_CLOB('
select
每年学校本专科生勤工助学情况.xxbsm as 学校标识码,
每年学校本专科生勤工助学情况.tjnf as 统计年份,
每年学校本专科生勤工助学情况.qgzxrc as 勤工助学人次,
每年学校本专科生勤工助学情况.qgzxje as 勤工助学金额
from und_si_sch_0095 每年学校本专科生勤工助学情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0095'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0096', 'und_si_sch_0096', '每年学校校内外岗位勤工助学情况', TO_CLOB('
select
每年学校校内外岗位勤工助学情况.xxbsm as 学校标识码,
每年学校校内外岗位勤工助学情况.tjnf as 统计年份,
每年学校校内外岗位勤工助学情况.xnqgzxrc as 校内勤工助学人次,
每年学校校内外岗位勤工助学情况.xnqgzxje as 校内勤工助学金额,
每年学校校内外岗位勤工助学情况.xwqgzxrc as 校外勤工助学人次,
每年学校校内外岗位勤工助学情况.xwqgzxje as 校外勤工助学金额
from und_si_sch_0096 每年学校校内外岗位勤工助学情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0096'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0097', 'und_si_sch_0097', '每年学校本专科生各岗位类型勤工助学情况', TO_CLOB('
select
每年学校本专科生各岗位类型勤工助学情况.xxbsm as 学校标识码,
每年学校本专科生各岗位类型勤工助学情况.tjnf as 统计年份,
每年学校本专科生各岗位类型勤工助学情况.qgzxrc as 勤工助学人次,
每年学校本专科生各岗位类型勤工助学情况.qgzxje as 勤工助学金额
from und_si_sch_0097 每年学校本专科生各岗位类型勤工助学情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0097'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0098', 'und_si_sch_0098', '每月学校本专科生勤工助学平均工作时长', TO_CLOB('
select
每月学校本专科生勤工助学平均工作时长.xxbsm as 学校标识码,
每月学校本专科生勤工助学平均工作时长.tjyf as 统计月份,
每月学校本专科生勤工助学平均工作时长.qgzxsz as 勤工助学时长
from und_si_sch_0098 每月学校本专科生勤工助学平均工作时长'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0098'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0099', 'und_si_sch_0099', '学校本专科生校内岗位勤工助学每小时平均酬金', TO_CLOB('
select
学校本专科生校内岗位勤工助学每小时平均酬金.xxbsm as 学校标识码,
学校本专科生校内岗位勤工助学每小时平均酬金.qgzxje as 勤工助学金额,
学校本专科生校内岗位勤工助学每小时平均酬金.tjxq as 统计学期
from und_si_sch_0099 学校本专科生校内岗位勤工助学每小时平均酬金'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0099'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0100', 'und_si_sch_0100', '每年学校勤工助学困难生比例', TO_CLOB('
select
每年学校勤工助学困难生比例.xxbsm as 学校标识码,
每年学校勤工助学困难生比例.tjnf as 统计年份,
每年学校勤工助学困难生比例.knsbl as 困难生比例
from und_si_sch_0100 每年学校勤工助学困难生比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0100'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0101', 'und_si_sch_0101', '每年学校本专科生困难补助情况', TO_CLOB('
select
每年学校本专科生困难补助情况.xxbsm as 学校标识码,
每年学校本专科生困难补助情况.tjnf as 统计年份,
每年学校本专科生困难补助情况.knbzrc as 困难补助人次,
每年学校本专科生困难补助情况.knbzje as 困难补助金额
from und_si_sch_0101 每年学校本专科生困难补助情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0101'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0102', 'und_si_sch_0102', '每年学校本专科生困难补助资金来源情况', TO_CLOB('
select
每年学校本专科生困难补助资金来源情况.xxbsm as 学校标识码,
每年学校本专科生困难补助资金来源情况.zjly as 资金来源,
每年学校本专科生困难补助资金来源情况.tjnf as 统计年份,
每年学校本专科生困难补助资金来源情况.knbzje as 困难补助金额
from und_si_sch_0102 每年学校本专科生困难补助资金来源情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0102'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0103', 'und_si_sch_0103', '每年学校困难生获困难补助覆盖率', TO_CLOB('
select
每年学校困难生获困难补助覆盖率.xxbsm as 学校标识码,
每年学校困难生获困难补助覆盖率.tjnf as 统计年份,
每年学校困难生获困难补助覆盖率.knbzfgl as 困难补助覆盖率
from und_si_sch_0103 每年学校困难生获困难补助覆盖率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0103'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0104', 'und_si_sch_0104', '每年学校本专科生获助学金情况', TO_CLOB('
select
每年学校本专科生获助学金情况.xxbsm as 学校标识码,
每年学校本专科生获助学金情况.tjnf as 统计年份,
每年学校本专科生获助学金情况.zxjrc as 助学金人次,
每年学校本专科生获助学金情况.zxjje as 助学金金额
from und_si_sch_0104 每年学校本专科生获助学金情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0104'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0105', 'und_si_sch_0105', '每年学校本专科生获各奖励级别助学金情况', TO_CLOB('
select
每年学校本专科生获各奖励级别助学金情况.xxbsm as 学校标识码,
每年学校本专科生获各奖励级别助学金情况.jljb as 奖励级别,
每年学校本专科生获各奖励级别助学金情况.tjnf as 统计年份,
每年学校本专科生获各奖励级别助学金情况.zxjrc as 助学金人次,
每年学校本专科生获各奖励级别助学金情况.zxjje as 助学金金额
from und_si_sch_0105 每年学校本专科生获各奖励级别助学金情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0105'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0106', 'und_si_sch_0106', '每年学校本专科助学贷款情况', TO_CLOB('
select
每年学校本专科助学贷款情况.xxbsm as 学校标识码,
每年学校本专科助学贷款情况.tjnf as 统计年份,
每年学校本专科助学贷款情况.dkrs as 贷款人数,
每年学校本专科助学贷款情况.dkje as 贷款金额
from und_si_sch_0106 每年学校本专科助学贷款情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0106'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0107', 'und_si_sch_0107', '每年学校本专科生各类助学贷款情况', TO_CLOB('
select
每年学校本专科生各类助学贷款情况.xxbsm as 学校标识码,
每年学校本专科生各类助学贷款情况.tjnf as 统计年份,
每年学校本专科生各类助学贷款情况.dkrs as 贷款人数,
每年学校本专科生各类助学贷款情况.dkje as 贷款金额
from und_si_sch_0107 每年学校本专科生各类助学贷款情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0107'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0108', 'und_si_sch_0108', '学校本专科生政治面貌占比', TO_CLOB('
select
学校本专科生政治面貌占比.xxbsm as 学校标识码,
学校本专科生政治面貌占比.zzmm as 政治面貌,
学校本专科生政治面貌占比.tjrq as 统计日期,
学校本专科生政治面貌占比.zgdybl as 中共党员比例,
学校本专科生政治面貌占比.gqtybl as 共青团员比例,
学校本专科生政治面貌占比.qzbl as 群众比例
from und_si_sch_0108 学校本专科生政治面貌占比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0108'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0109', 'und_si_sch_0109', '每年学校党建培训情况', TO_CLOB('
select
每年学校党建培训情况.xxbsm as 学校标识码,
每年学校党建培训情况.tjnf as 统计年份,
每年学校党建培训情况.cyrc as 参与人次,
每年学校党建培训情况.hdsl as 活动数量
from und_si_sch_0109 每年学校党建培训情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0109'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0110', 'und_si_sch_0110', '每年学校社团活动情况', TO_CLOB('
select
每年学校社团活动情况.xxbsm as 学校标识码,
每年学校社团活动情况.tjnf as 统计年份,
每年学校社团活动情况.cyrc as 参与人次,
每年学校社团活动情况.stsl as 社团数量
from und_si_sch_0110 每年学校社团活动情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0110'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0111', 'und_si_sch_0111', '每年学校德育活动情况', TO_CLOB('
select
每年学校德育活动情况.xxbsm as 学校标识码,
每年学校德育活动情况.tjnf as 统计年份,
每年学校德育活动情况.cyrc as 参与人次,
每年学校德育活动情况.hdsl as 活动数量
from und_si_sch_0111 每年学校德育活动情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0111'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0112', 'und_si_sch_0112', '每年学校本专科生人均参与德育活动次数', TO_CLOB('
select
每年学校本专科生人均参与德育活动次数.xxbsm as 学校标识码,
每年学校本专科生人均参与德育活动次数.tjnf as 统计年份,
每年学校本专科生人均参与德育活动次数.hdcs as 活动次数
from und_si_sch_0112 每年学校本专科生人均参与德育活动次数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0112'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0113', 'und_si_sch_0113', '每年学校本专科生参与德育活动比例', TO_CLOB('
select
每年学校本专科生参与德育活动比例.xxbsm as 学校标识码,
每年学校本专科生参与德育活动比例.tjnf as 统计年份,
每年学校本专科生参与德育活动比例.rszb as 人数占比
from und_si_sch_0113 每年学校本专科生参与德育活动比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0113'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0114', 'und_si_sch_0114', '每年学校本专科生学业成绩挂科率', TO_CLOB('
select
每年学校本专科生学业成绩挂科率.xxbsm as 学校标识码,
每年学校本专科生学业成绩挂科率.tjnf as 统计年份,
每年学校本专科生学业成绩挂科率.gkl as 挂科率
from und_si_sch_0114 每年学校本专科生学业成绩挂科率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0114'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0115', 'und_si_sch_0115', '每年学校本专科生四六级通过率', TO_CLOB('
select
每年学校本专科生四六级通过率.xxbsm as 学校标识码,
每年学校本专科生四六级通过率.tjnf as 统计年份,
每年学校本专科生四六级通过率.sjtgl as 四级通过率,
每年学校本专科生四六级通过率.ljtgl as 六级通过率
from und_si_sch_0115 每年学校本专科生四六级通过率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0115'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0116', 'und_si_sch_0116', '每年各学院本专科生专业平均分', TO_CLOB('
select
每年各学院本专科生专业平均分.xxbsm as 学校标识码,
每年各学院本专科生专业平均分.tjnf as 统计年份,
每年各学院本专科生专业平均分.zypjf as 专业平均分
from und_si_sch_0116 每年各学院本专科生专业平均分'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0116'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0117', 'und_si_sch_0117', '每年学校本专科生科研成果总数', TO_CLOB('
select
每年学校本专科生科研成果总数.xxbsm as 学校标识码,
每年学校本专科生科研成果总数.tjnf as 统计年份,
每年学校本专科生科研成果总数.cgsl as 成果数量
from und_si_sch_0117 每年学校本专科生科研成果总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0117'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0118', 'und_si_sch_0118', '每年学校本专科生发表论文数量', TO_CLOB('
select
每年学校本专科生发表论文数量.xxbsm as 学校标识码,
每年学校本专科生发表论文数量.tjnf as 统计年份,
每年学校本专科生发表论文数量.lwsl as 论文数量
from und_si_sch_0118 每年学校本专科生发表论文数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0118'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0119', 'und_si_sch_0119', '每年学校本专科生各刊物级别论文发表数量', TO_CLOB('
select
每年学校本专科生各刊物级别论文发表数量.xxbsm as 学校标识码,
每年学校本专科生各刊物级别论文发表数量.tjnf as 统计年份,
每年学校本专科生各刊物级别论文发表数量.lwsl as 论文数量
from und_si_sch_0119 每年学校本专科生各刊物级别论文发表数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0119'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0120', 'und_si_sch_0120', '每年学校本专科生发明专利数量', TO_CLOB('
select
每年学校本专科生发明专利数量.xxbsm as 学校标识码,
每年学校本专科生发明专利数量.tjnf as 统计年份,
每年学校本专科生发明专利数量.zlsl as 专利数量
from und_si_sch_0120 每年学校本专科生发明专利数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0120'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0121', 'und_si_sch_0121', '每年学校本专科生各发明专利类别数量', TO_CLOB('
select
每年学校本专科生各发明专利类别数量.xxbsm as 学校标识码,
每年学校本专科生各发明专利类别数量.tjnf as 统计年份,
每年学校本专科生各发明专利类别数量.zlsl as 专利数量
from und_si_sch_0121 每年学校本专科生各发明专利类别数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0121'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0122', 'und_si_sch_0122', '每年学校本专科生科研项目数量', TO_CLOB('
select
每年学校本专科生科研项目数量.xxbsm as 学校标识码,
每年学校本专科生科研项目数量.tjnf as 统计年份,
每年学校本专科生科研项目数量.xmsl as 项目数量
from und_si_sch_0122 每年学校本专科生科研项目数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0122'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0123', 'und_si_sch_0123', '每年学校本专科生各科研项目级别数量', TO_CLOB('
select
每年学校本专科生各科研项目级别数量.xxbsm as 学校标识码,
每年学校本专科生各科研项目级别数量.tjnf as 统计年份,
每年学校本专科生各科研项目级别数量.xmsl as 项目数量
from und_si_sch_0123 每年学校本专科生各科研项目级别数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0123'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0124', 'und_si_sch_0124', '每年学校举办智育活动情况', TO_CLOB('
select
每年学校举办智育活动情况.xxbsm as 学校标识码,
每年学校举办智育活动情况.tjnf as 统计年份,
每年学校举办智育活动情况.cyrc as 参与人次,
每年学校举办智育活动情况.hdsl as 活动数量
from und_si_sch_0124 每年学校举办智育活动情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0124'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0125', 'und_si_sch_0125', '每年学校本专科生人均参与智育活动数量', TO_CLOB('
select
每年学校本专科生人均参与智育活动数量.xxbsm as 学校标识码,
每年学校本专科生人均参与智育活动数量.tjnf as 统计年份,
每年学校本专科生人均参与智育活动数量.rjcysl as 人均参与数量
from und_si_sch_0125 每年学校本专科生人均参与智育活动数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0125'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0126', 'und_si_sch_0126', '每年学校智育活动本专科生参加比例', TO_CLOB('
select
每年学校智育活动本专科生参加比例.xxbsm as 学校标识码,
每年学校智育活动本专科生参加比例.tjnf as 统计年份,
每年学校智育活动本专科生参加比例.cjbl as 参加比例
from und_si_sch_0126 每年学校智育活动本专科生参加比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0126'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0127', 'und_si_sch_0127', '每学期学校本专科生体育成绩挂科率', TO_CLOB('
select
每学期学校本专科生体育成绩挂科率.xxbsm as 学校标识码,
每学期学校本专科生体育成绩挂科率.tjxq as 统计学期,
每学期学校本专科生体育成绩挂科率.gkl as 挂科率
from und_si_sch_0127 每学期学校本专科生体育成绩挂科率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0127'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0128', 'und_si_sch_0128', '每学年学校本专科生体测成绩及格率', TO_CLOB('
select
每学年学校本专科生体测成绩及格率.xxbsm as 学校标识码,
每学年学校本专科生体测成绩及格率.tjnf as 统计年份,
每学年学校本专科生体测成绩及格率.jgl as 及格率
from und_si_sch_0128 每学年学校本专科生体测成绩及格率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0128'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0129', 'und_si_sch_0129', '每学年各年级体测成绩均分', TO_CLOB('
select
每学年各年级体测成绩均分.xxbsm as 学校标识码,
每学年各年级体测成绩均分.tjnf as 统计年份,
每学年各年级体测成绩均分.tcpjf as 体测平均分
from und_si_sch_0129 每学年各年级体测成绩均分'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0129'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0130', 'und_si_sch_0130', '每年学校举办体育活动情况', TO_CLOB('
select
每年学校举办体育活动情况.xxbsm as 学校标识码,
每年学校举办体育活动情况.tjnf as 统计年份,
每年学校举办体育活动情况.cyrc as 参与人次,
每年学校举办体育活动情况.hdsl as 活动数量
from und_si_sch_0130 每年学校举办体育活动情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0130'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0131', 'und_si_sch_0131', '每年学校本专科生人均参与体育活动次数', TO_CLOB('
select
每年学校本专科生人均参与体育活动次数.xxbsm as 学校标识码,
每年学校本专科生人均参与体育活动次数.tjnf as 统计年份,
每年学校本专科生人均参与体育活动次数.rjcysl as 人均参与数量
from und_si_sch_0131 每年学校本专科生人均参与体育活动次数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0131'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0132', 'und_si_sch_0132', '每年学校体育活动本专科生参与比例', TO_CLOB('
select
每年学校体育活动本专科生参与比例.xxbsm as 学校标识码,
每年学校体育活动本专科生参与比例.tjnf as 统计年份,
每年学校体育活动本专科生参与比例.cjbl as 参加比例
from und_si_sch_0132 每年学校体育活动本专科生参与比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0132'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0133', 'und_si_sch_0133', '每年学校美育活动情况', TO_CLOB('
select
每年学校美育活动情况.xxbsm as 学校标识码,
每年学校美育活动情况.tjnf as 统计年份,
每年学校美育活动情况.cyrc as 参与人次,
每年学校美育活动情况.hdsl as 活动数量
from und_si_sch_0133 每年学校美育活动情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0133'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0134', 'und_si_sch_0134', '每年学校本专科生人均参与美育活动次数', TO_CLOB('
select
每年学校本专科生人均参与美育活动次数.xxbsm as 学校标识码,
每年学校本专科生人均参与美育活动次数.tjnf as 统计年份,
每年学校本专科生人均参与美育活动次数.rjcysl as 人均参与数量
from und_si_sch_0134 每年学校本专科生人均参与美育活动次数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0134'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0135', 'und_si_sch_0135', '每年学校本专科生参与美育活动比例', TO_CLOB('
select
每年学校本专科生参与美育活动比例.xxbsm as 学校标识码,
每年学校本专科生参与美育活动比例.tjnf as 统计年份,
每年学校本专科生参与美育活动比例.rszb as 人数占比
from und_si_sch_0135 每年学校本专科生参与美育活动比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0135'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0136', 'und_si_sch_0136', '每年学校劳育活动情况', TO_CLOB('
select
每年学校劳育活动情况.xxbsm as 学校标识码,
每年学校劳育活动情况.tjnf as 统计年份,
每年学校劳育活动情况.cyrc as 参与人次,
每年学校劳育活动情况.hdsl as 活动数量
from und_si_sch_0136 每年学校劳育活动情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0136'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0137', 'und_si_sch_0137', '每年学校本专科生人均参与劳育活动次数', TO_CLOB('
select
每年学校本专科生人均参与劳育活动次数.xxbsm as 学校标识码,
每年学校本专科生人均参与劳育活动次数.tjnf as 统计年份,
每年学校本专科生人均参与劳育活动次数.hdcs as 活动次数
from und_si_sch_0137 每年学校本专科生人均参与劳育活动次数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0137'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0138', 'und_si_sch_0138', '每年学校本专科生参与劳育活动比例', TO_CLOB('
select
每年学校本专科生参与劳育活动比例.xxbsm as 学校标识码,
每年学校本专科生参与劳育活动比例.tjnf as 统计年份,
每年学校本专科生参与劳育活动比例.rszb as 人数占比
from und_si_sch_0138 每年学校本专科生参与劳育活动比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0138'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0139', 'und_si_sch_0139', '每年学校本专科生参与志愿服务情况', TO_CLOB('
select
每年学校本专科生参与志愿服务情况.xxbsm as 学校标识码,
每年学校本专科生参与志愿服务情况.tjnf as 统计年份,
每年学校本专科生参与志愿服务情况.zyrc as 志愿人次,
每年学校本专科生参与志愿服务情况.zysl as 志愿数量
from und_si_sch_0139 每年学校本专科生参与志愿服务情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0139'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0140', 'und_si_sch_0140', '学校志愿团队数量', TO_CLOB('
select
学校志愿团队数量.xxbsm as 学校标识码,
学校志愿团队数量.tjrq as 统计日期,
学校志愿团队数量.zytdsl as 志愿团队数量
from und_si_sch_0140 学校志愿团队数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0140'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0141', 'und_si_sch_0141', '每年学校本专科生人均志愿服务时长', TO_CLOB('
select
每年学校本专科生人均志愿服务时长.xxbsm as 学校标识码,
每年学校本专科生人均志愿服务时长.tjnf as 统计年份,
每年学校本专科生人均志愿服务时长.pjzysz as 平均志愿时长
from und_si_sch_0141 每年学校本专科生人均志愿服务时长'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0141'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0142', 'und_si_sch_0142', '每年学校本专科生参与社会实践情况', TO_CLOB('
select
每年学校本专科生参与社会实践情况.xxbsm as 学校标识码,
每年学校本专科生参与社会实践情况.tjnf as 统计年份,
每年学校本专科生参与社会实践情况.sjsl as 实践数量,
每年学校本专科生参与社会实践情况.sjrc as 实践人次
from und_si_sch_0142 每年学校本专科生参与社会实践情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0142'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0143', 'und_si_sch_0143', '每年学校本专科生创新创业情况', TO_CLOB('
select
每年学校本专科生创新创业情况.xxbsm as 学校标识码,
每年学校本专科生创新创业情况.tjnf as 统计年份,
每年学校本专科生创新创业情况.xmsl as 项目数量,
每年学校本专科生创新创业情况.cyrc as 参与人次,
每年学校本专科生创新创业情况.jtxmsl as 结题项目数量
from und_si_sch_0143 每年学校本专科生创新创业情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0143'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0144', 'und_si_sch_0144', '每年学校各项目级别创新创业数量', TO_CLOB('
select
每年学校各项目级别创新创业数量.xxbsm as 学校标识码,
每年学校各项目级别创新创业数量.tjnf as 统计年份,
每年学校各项目级别创新创业数量.xmsl as 项目数量
from und_si_sch_0144 每年学校各项目级别创新创业数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0144'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0145', 'und_si_sch_0145', '每年学校本专科生毕业去向情况', TO_CLOB('
select
每年学校本专科生毕业去向情况.xxbsm as 学校标识码,
每年学校本专科生毕业去向情况.tjnf as 统计年份,
每年学校本专科生毕业去向情况.gnsxl as 国内升学率,
每年学校本专科生毕业去向情况.cgcjlxl as 出国出境留学率,
每年学校本专科生毕业去向情况.jyl as 就业率,
每年学校本专科生毕业去向情况.gnsxrs as 国内升学人数,
每年学校本专科生毕业去向情况.cgcjlxrs as 出国出境留学人数,
每年学校本专科生毕业去向情况.jyrs as 就业人数
from und_si_sch_0145 每年学校本专科生毕业去向情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0145'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0146', 'und_si_sch_0146', '每年学校本专科生就业单位行业分布情况', TO_CLOB('
select
每年学校本专科生就业单位行业分布情况.xxbsm as 学校标识码,
每年学校本专科生就业单位行业分布情况.tjnf as 统计年份,
每年学校本专科生就业单位行业分布情况.jyrs as 就业人数
from und_si_sch_0146 每年学校本专科生就业单位行业分布情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0146'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0147', 'und_si_sch_0147', '每年学校本专科生就业单位性质分布情况', TO_CLOB('
select
每年学校本专科生就业单位性质分布情况.xxbsm as 学校标识码,
每年学校本专科生就业单位性质分布情况.tjnf as 统计年份,
每年学校本专科生就业单位性质分布情况.jyrs as 就业人数
from und_si_sch_0147 每年学校本专科生就业单位性质分布情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0147'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0148', 'und_si_sch_0148', '每年学校本专科生就业区域分布情况', TO_CLOB('
select
每年学校本专科生就业区域分布情况.xxbsm as 学校标识码,
每年学校本专科生就业区域分布情况.tjnf as 统计年份,
每年学校本专科生就业区域分布情况.jyrs as 就业人数
from und_si_sch_0148 每年学校本专科生就业区域分布情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0148'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0515', 'und_si_sch_0515', '每年学校资助学生情况', TO_CLOB('
select
每年学校资助学生情况.xxbsm as 学校标识码,
每年学校资助学生情况.tjnf as 统计年份,
每年学校资助学生情况.zzknsrs as 资助困难生人数,
每年学校资助学生情况.knszzje as 困难生资助金额,
每年学校资助学生情况.zzfknsrs as 资助非困难生人数,
每年学校资助学生情况.fknszzje as 非困难生资助金额
from und_si_sch_0515 每年学校资助学生情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0515'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0516', 'und_si_sch_0516', '每年学生资助各困难程度学生情况', TO_CLOB('
select
每年学生资助各困难程度学生情况.xxbsm as 学校标识码,
每年学生资助各困难程度学生情况.knlx as 困难类型,
每年学生资助各困难程度学生情况.tjnf as 统计年份,
每年学生资助各困难程度学生情况.rjzzje as 人均资助金额
from und_si_sch_0516 每年学生资助各困难程度学生情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0516'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0517', 'und_si_sch_0517', '学校各民族困难生情况', TO_CLOB('
select
学校各民族困难生情况.xxbsm as 学校标识码,
学校各民族困难生情况.mz as 民族,
学校各民族困难生情况.tjnf as 统计年份,
学校各民族困难生情况.knrs as 困难人数
from und_si_sch_0517 学校各民族困难生情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0517'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0518', 'und_si_sch_0518', '学校各省份困难生情况', TO_CLOB('
select
学校各省份困难生情况.xxbsm as 学校标识码,
学校各省份困难生情况.sf as 省份,
学校各省份困难生情况.tjnf as 统计年份,
学校各省份困难生情况.knrs as 困难人数
from und_si_sch_0518 学校各省份困难生情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0518'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0519', 'und_si_sch_0519', '每年学校奖学金性别分布情况', TO_CLOB('
select
每年学校奖学金性别分布情况.xxbsm as 学校标识码,
每年学校奖学金性别分布情况.xb as 性别,
每年学校奖学金性别分布情况.tjnf as 统计年份,
每年学校奖学金性别分布情况.hjrc as 获奖人次,
每年学校奖学金性别分布情况.hjje as 获奖金额
from und_si_sch_0519 每年学校奖学金性别分布情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0519'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0520', 'und_si_sch_0520', '每年学校困难生奖学金获奖情况', TO_CLOB('
select
每年学校困难生奖学金获奖情况.xxbsm as 学校标识码,
每年学校困难生奖学金获奖情况.tjnf as 统计年份,
每年学校困难生奖学金获奖情况.hjrc as 获奖人次,
每年学校困难生奖学金获奖情况.hjje as 获奖金额,
每年学校困难生奖学金获奖情况.rczb as 人次占比
from und_si_sch_0520 每年学校困难生奖学金获奖情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0520'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0521', 'und_si_sch_0521', '每年学校非困难生奖学金获奖情况', TO_CLOB('
select
每年学校非困难生奖学金获奖情况.xxbsm as 学校标识码,
每年学校非困难生奖学金获奖情况.tjnf as 统计年份,
每年学校非困难生奖学金获奖情况.hjrc as 获奖人次,
每年学校非困难生奖学金获奖情况.hjje as 获奖金额,
每年学校非困难生奖学金获奖情况.rczb as 人次占比
from und_si_sch_0521 每年学校非困难生奖学金获奖情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0521'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0522', 'und_si_sch_0522', '每年学校学生获各荣誉称号类型情况', TO_CLOB('
select
每年学校学生获各荣誉称号类型情况.xxbsm as 学校标识码,
每年学校学生获各荣誉称号类型情况.rychlx as 荣誉称号类型,
每年学校学生获各荣誉称号类型情况.tjnf as 统计年份,
每年学校学生获各荣誉称号类型情况.hjrc as 获奖人次,
每年学校学生获各荣誉称号类型情况.hjje as 获奖金额,
每年学校学生获各荣誉称号类型情况.rczb as 人次占比
from und_si_sch_0522 每年学校学生获各荣誉称号类型情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0522'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0523', 'und_si_sch_0523', '每年学校应届生学分完成情况', TO_CLOB('
select
每年学校应届生学分完成情况.xxbsm as 学校标识码,
每年学校应届生学分完成情况.tjnf as 统计年份,
每年学校应届生学分完成情况.ddbyyqxfrs as 达到毕业要求学分人数,
每年学校应届生学分完成情况.wdbyyqxfrs as 未达毕业要求学分人数
from und_si_sch_0523 每年学校应届生学分完成情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0523'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_sch_0524', 'und_si_sch_0524', '每年学校本专科生综合测评平均分', TO_CLOB('
select
每年学校本专科生综合测评平均分.xxbsm as 学校标识码,
每年学校本专科生综合测评平均分.tjnf as 统计年份,
每年学校本专科生综合测评平均分.pjf as 平均分
from und_si_sch_0524 每年学校本专科生综合测评平均分'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_sch_0524'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_und_0001', 'und_si_und_0001', '每月本专科生图书借阅情况', TO_CLOB('
select
每月本专科生图书借阅情况.xh as 学号,
每月本专科生图书借阅情况.tjrq as 统计日期,
每月本专科生图书借阅情况.jybs as 借阅本数,
每月本专科生图书借阅情况.yqtsbs as 逾期图书本数
from und_si_und_0001 每月本专科生图书借阅情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_und_0001'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_und_0002', 'und_si_und_0002', '本专科生每日上网时长', TO_CLOB('
select
本专科生每日上网时长.xh as 学号,
本专科生每日上网时长.tjrq as 统计日期,
本专科生每日上网时长.swsz as 上网时长
from und_si_und_0002 本专科生每日上网时长'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_und_0002'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_und_0003', 'und_si_und_0003', '每月本专科生请假情况', TO_CLOB('
select
每月本专科生请假情况.xh as 学号,
每月本专科生请假情况.tjrq as 统计日期,
每月本专科生请假情况.qjts as 请假天数,
每月本专科生请假情况.qjcs as 请假次数
from und_si_und_0003 每月本专科生请假情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_und_0003'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_und_0004', 'und_si_und_0004', '每年本专科生获得奖学金情况', TO_CLOB('
select
每年本专科生获得奖学金情况.xh as 学号,
每年本专科生获得奖学金情况.tjnf as 统计年份,
每年本专科生获得奖学金情况.hjje as 获奖金额,
每年本专科生获得奖学金情况.hjcs as 获奖次数
from und_si_und_0004 每年本专科生获得奖学金情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_und_0004'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_und_0005', 'und_si_und_0005', '每学期本专科生获各级荣誉称号情况', TO_CLOB('
select
每学期本专科生获各级荣誉称号情况.xh as 学号,
每学期本专科生获各级荣誉称号情况.jljb as 奖励级别,
每学期本专科生获各级荣誉称号情况.tjxq as 统计学期,
每学期本专科生获各级荣誉称号情况.hjje as 获奖金额,
每学期本专科生获各级荣誉称号情况.hjcs as 获奖次数
from und_si_und_0005 每学期本专科生获各级荣誉称号情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_und_0005'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_und_0006', 'und_si_und_0006', '每学期本专科生违纪处分次数', TO_CLOB('
select
每学期本专科生违纪处分次数.xh as 学号,
每学期本专科生违纪处分次数.tjxq as 统计学期,
每学期本专科生违纪处分次数.cfcs as 处分次数
from und_si_und_0006 每学期本专科生违纪处分次数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_und_0006'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_und_0007', 'und_si_und_0007', '每学期本专科生课程平均分', TO_CLOB('
select
每学期本专科生课程平均分.xh as 学号,
每学期本专科生课程平均分.tjxq as 统计学期,
每学期本专科生课程平均分.bxkpjf as 必修课平均分,
每学期本专科生课程平均分.xxkpjf as 选修课平均分
from und_si_und_0007 每学期本专科生课程平均分'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_und_0007'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_und_0008', 'und_si_und_0008', '每学期本专科生课程平均分排名', TO_CLOB('
select
每学期本专科生课程平均分排名.xh as 学号,
每学期本专科生课程平均分排名.tjxq as 统计学期,
每学期本专科生课程平均分排名.bjpm as 班级排名,
每学期本专科生课程平均分排名.zypm as 专业排名
from und_si_und_0008 每学期本专科生课程平均分排名'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_und_0008'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_und_0009', 'und_si_und_0009', '每学期本专科生不及格课程数量', TO_CLOB('
select
每学期本专科生不及格课程数量.xh as 学号,
每学期本专科生不及格课程数量.tjxq as 统计学期,
每学期本专科生不及格课程数量.gkms as 挂科门数
from und_si_und_0009 每学期本专科生不及格课程数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_und_0009'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_und_0010', 'und_si_und_0010', '本专科生一卡通每日消费情况', TO_CLOB('
select
本专科生一卡通每日消费情况.xh as 学号,
本专科生一卡通每日消费情况.tjrq as 统计日期,
本专科生一卡通每日消费情况.pjxfe as 平均消费额,
本专科生一卡通每日消费情况.xfje as 消费金额,
本专科生一卡通每日消费情况.xfcs as 消费次数
from und_si_und_0010 本专科生一卡通每日消费情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_und_0010'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_und_0011', 'und_si_und_0011', '本专科生食堂每日消费情况', TO_CLOB('
select
本专科生食堂每日消费情况.xh as 学号,
本专科生食堂每日消费情况.tjrq as 统计日期,
本专科生食堂每日消费情况.xfje as 消费金额,
本专科生食堂每日消费情况.xfcs as 消费次数
from und_si_und_0011 本专科生食堂每日消费情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_und_0011'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_und_0012', 'und_si_und_0012', '本专生获得资助情况', TO_CLOB('
select
本专生获得资助情况.xh as 学号,
本专生获得资助情况.tjrq as 统计日期,
本专生获得资助情况.zzze as 资助总额
from und_si_und_0012 本专生获得资助情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_und_0012'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_und_0013', 'und_si_und_0013', '每年本专科生获得各级别奖学金情况', TO_CLOB('
select
每年本专科生获得各级别奖学金情况.xh as 学号,
每年本专科生获得各级别奖学金情况.jljb as 奖励级别,
每年本专科生获得各级别奖学金情况.tjnf as 统计年份,
每年本专科生获得各级别奖学金情况.hjje as 获奖金额,
每年本专科生获得各级别奖学金情况.hjcs as 获奖次数
from und_si_und_0013 每年本专科生获得各级别奖学金情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_und_0013'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_si_und_0014', 'und_si_und_0014', '本专科生平均学分绩点', TO_CLOB('
select
本专科生平均学分绩点.xh as 学号,
本专科生平均学分绩点.tjnf as 统计年份,
本专科生平均学分绩点.pjxfjd as 平均学分绩点,
本专科生平均学分绩点.bjpm as 班级排名,
本专科生平均学分绩点.zypm as 专业排名
from und_si_und_0014 本专科生平均学分绩点'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_si_und_0014'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'col_label', 'col_label', 'COL标签宽表', TO_CLOB('
select
COL标签宽表.kzrq as 快照日期,
COL标签宽表.xybh as 学院编号
from col_label COL标签宽表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'col_label'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'sch_label', 'sch_label', 'SCH标签宽表', TO_CLOB('
select
SCH标签宽表.kzrq as 快照日期,
SCH标签宽表.xxbsm as 学校标识码
from sch_label SCH标签宽表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'sch_label'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 't_da_model_column_extend', 't_da_model_column_extend', '', TO_CLOB('
select
t_da_model_column_extend.primary_column_tag as 是否主键字段
from t_da_model_column_extend t_da_model_column_extend'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 't_da_model_column_extend'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 't_da_model_column_info', 't_da_model_column_info', '模型字段信息', TO_CLOB('
select
模型字段信息.model_id as 模型ID,
模型字段信息.column_id as 字段ID,
模型字段信息.syn_value as 同义词
from t_da_model_column_info 模型字段信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 't_da_model_column_info'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 't_da_model_info', 't_da_model_info', '模型信息', TO_CLOB('
select
模型信息.model_id as 模型ID,
模型信息.syn_value as 同义词
from t_da_model_info 模型信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 't_da_model_info'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 't_da_model_relation', 't_da_model_relation', '模型关系表', TO_CLOB('
select
模型关系表.model_id as 主模型ID,
模型关系表.fields as 主模型字段,
模型关系表.mapped_model_id as 关联模型,
模型关系表.mapped_fields as 关联模型字段
from t_da_model_relation 模型关系表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 't_da_model_relation'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_label', 'und_label', 'UND标签宽表', TO_CLOB('
select
UND标签宽表.kzrq as 快照日期,
UND标签宽表.xh as 学号
from und_label UND标签宽表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_label'
);