-- T_DA_THEME 主题域表
BEGIN
EXECUTE IMMEDIATE 'CREATE TABLE "T_DA_THEME"
   (	"ID" VARCHAR2(100),
	"THEME_CODE" VARCHAR2(255),
	"THEME_NAME" VARCHAR2(255),
	"CREATE_BY" VARCHAR2(100),
	"CREATE_TIME" DATE,
	"UPDATE_BY" VARCHAR2(100),
	"UPDATE_TIME" DATE,
	 PRIMARY KEY ("ID")
   )';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX "SYS_C0011844" ON "T_DA_THEME" ("ID")';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-1408) THEN
		RAISE;
	END IF;
END;
/
COMMENT ON TABLE T_DA_THEME IS '主题域表';
COMMENT ON COLUMN T_DA_THEME.ID IS 'ID';
COMMENT ON COLUMN T_DA_THEME.THEME_CODE IS '主题域编码';
COMMENT ON COLUMN T_DA_THEME.THEME_NAME IS '主题域名称';
COMMENT ON COLUMN T_DA_THEME.CREATE_BY IS '创建人';
COMMENT ON COLUMN T_DA_THEME.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN T_DA_THEME.UPDATE_BY IS '更新人';
COMMENT ON COLUMN T_DA_THEME.UPDATE_TIME IS '更新时间';

-- T_DA_THEME_APP 主题域应用
BEGIN
EXECUTE IMMEDIATE 'CREATE TABLE "T_DA_THEME_APP"
   (	"ID" VARCHAR2(100),
	"THEME_CODE" VARCHAR2(255),
	"APPID" VARCHAR2(255),
	"CREATE_BY" VARCHAR2(100),
	"CREATE_TIME" DATE,
	"UPDATE_BY" VARCHAR2(100),
	"UPDATE_TIME" DATE,
	 PRIMARY KEY ("ID")
   )';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX "SYS_C0011845" ON "T_DA_THEME_APP" ("ID")';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-1408) THEN
		RAISE;
	END IF;
END;
/
COMMENT ON TABLE T_DA_THEME_APP IS '主题域应用';
COMMENT ON COLUMN T_DA_THEME_APP.ID IS 'ID';
COMMENT ON COLUMN T_DA_THEME_APP.THEME_CODE IS '主题域编码';
COMMENT ON COLUMN T_DA_THEME_APP.APPID IS '应用ID';
COMMENT ON COLUMN T_DA_THEME_APP.CREATE_BY IS '创建人';
COMMENT ON COLUMN T_DA_THEME_APP.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN T_DA_THEME_APP.UPDATE_BY IS '更新人';
COMMENT ON COLUMN T_DA_THEME_APP.UPDATE_TIME IS '更新时间';

-- T_DA_ENTITY 主题域对象
BEGIN
EXECUTE IMMEDIATE 'CREATE TABLE "T_DA_ENTITY"
   (	"ID" VARCHAR2(100),
	"ZTY" VARCHAR2(255),
	"STBM" VARCHAR2(30),
	"STMC" VARCHAR2(90),
	"STBMJC" VARCHAR2(255),
	"STBSZD" VARCHAR2(100),
	"MXID" VARCHAR2(100),
	"LABEL_MODEL" VARCHAR2(100),
	"CLASSIFY_CODE" VARCHAR2(100),
	"DXMS" VARCHAR2(600),
	"PARENT_CODE" VARCHAR2(50),
	"DATA_SCOPE" VARCHAR2(2000),
	"CREATE_BY" VARCHAR2(100),
	"CREATE_TIME" DATE,
	"UPDATE_BY" VARCHAR2(100),
	"UPDATE_TIME" DATE,
	 PRIMARY KEY ("ID"))';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX "SYS_C0011847" ON "T_DA_ENTITY" ("ID")';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-1408) THEN
		RAISE;
	END IF;
END;
/
COMMENT ON TABLE T_DA_ENTITY IS '主题域对象';
COMMENT ON COLUMN T_DA_ENTITY.ID IS 'ID';
COMMENT ON COLUMN T_DA_ENTITY.ZTY IS '主题域对象';
COMMENT ON COLUMN T_DA_ENTITY.STBM IS '实体编码(一级分类编码)';
COMMENT ON COLUMN T_DA_ENTITY.STMC IS '实体名称';
COMMENT ON COLUMN T_DA_ENTITY.STBMJC IS '实体编码简称';
COMMENT ON COLUMN T_DA_ENTITY.STBSZD IS '实体标识字段';
COMMENT ON COLUMN T_DA_ENTITY.MXID IS '模型ID';
COMMENT ON COLUMN T_DA_ENTITY.LABEL_MODEL IS '对象标签模型ID';
COMMENT ON COLUMN T_DA_ENTITY.CLASSIFY_CODE IS '对象主表所在分类';
COMMENT ON COLUMN T_DA_ENTITY.DXMS IS '对象描述';
COMMENT ON COLUMN T_DA_ENTITY.PARENT_CODE IS '父对象code';
COMMENT ON COLUMN T_DA_ENTITY.DATA_SCOPE IS '对象范围';
COMMENT ON COLUMN T_DA_ENTITY.CREATE_BY IS '创建人';
COMMENT ON COLUMN T_DA_ENTITY.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN T_DA_ENTITY.UPDATE_BY IS '更新人';
COMMENT ON COLUMN T_DA_ENTITY.UPDATE_TIME IS '更新时间';

-- T_DA_THEME_ENTITY 主题域对象关联模型
BEGIN
EXECUTE IMMEDIATE 'CREATE TABLE "T_DA_THEME_ENTITY"
   (	"ID" VARCHAR2(100),
	"THEME_ID" VARCHAR2(255),
	"ENTITY_ID" VARCHAR2(255),
	"CREATE_BY" VARCHAR2(100),
	"CREATE_TIME" DATE,
	"UPDATE_BY" VARCHAR2(100),
	"UPDATE_TIME" DATE,
	 PRIMARY KEY ("ID"))';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX "SYS_C0011852" ON "T_DA_THEME_ENTITY" ("ID") ';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-1408) THEN
		RAISE;
	END IF;
END;
/
COMMENT ON TABLE T_DA_THEME_ENTITY IS '主题域对象关联模型';
COMMENT ON COLUMN T_DA_THEME_ENTITY.ID IS 'ID';
COMMENT ON COLUMN T_DA_THEME_ENTITY.THEME_ID IS '主题域';
COMMENT ON COLUMN T_DA_THEME_ENTITY.ENTITY_ID IS '实体ID';
COMMENT ON COLUMN T_DA_THEME_ENTITY.CREATE_BY IS '创建人';
COMMENT ON COLUMN T_DA_THEME_ENTITY.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN T_DA_THEME_ENTITY.UPDATE_BY IS '更新人';
COMMENT ON COLUMN T_DA_THEME_ENTITY.UPDATE_TIME IS '更新时间';

-- LOWCODE_MODEL 模型配置
BEGIN
EXECUTE IMMEDIATE 'CREATE TABLE "LOWCODE_MODEL"
   (	"ID" VARCHAR2(100),
	"MODEL_NAME" VARCHAR2(255),
	"MODEL_LABEL" VARCHAR2(255) NOT NULL ENABLE,
	"MODEL_LABEL_UT" DATE,
	"MODEL_CLASS" VARCHAR2(255),
	"MODEL_TABLE" VARCHAR2(255),
	"VIRTUAL_MODEL" NUMBER(10,0) DEFAULT 0,
	"BIZ_MODEL" NUMBER(10,0) DEFAULT 1,
	"MODEL_APP" VARCHAR2(128),
	"MODULE_CODE" VARCHAR2(128),
	"MODEL_PROCESSOR" VARCHAR2(255),
	"DATAFILTER_PROCESSOR" VARCHAR2(255),
	"DEFAULT_ORDER" VARCHAR2(512),
	"DEFAULT_ORDER_UT" DATE,
	"PERMIT_EXT_FLAG" NUMBER DEFAULT 1 NOT NULL ENABLE,
	"EXTEND_FLAG" NUMBER(10,0) DEFAULT 0 NOT NULL ENABLE,
	"SYS_FLAG" NUMBER(10,0) DEFAULT 0 NOT NULL ENABLE,
	"STATUS" NUMBER(10,0) DEFAULT 0,
	"PUBLISH_TIME" DATE,
	"MODEL_HIS_TYPE" VARCHAR2(225),
	"MODEL_HIS_TYPE_UT" DATE,
	"MODEL_CLASSIFY" VARCHAR2(100),
	"MODEL_CLASSIFY_UT" DATE,
	"RENDER_TYPE" VARCHAR2(100),
	"RENDER_TYPE_UT" DATE,
	"ORDER_INDEX" NUMBER(10,0),
	"ORDER_INDEX_UT" DATE,
	"CHECKSUM" VARCHAR2(1000),
	"TABLE_META" CLOB,
	"TABLE_META_UT" DATE,
	"CREATE_BY" VARCHAR2(100),
	"CREATE_TIME" DATE,
	"UPDATE_BY" VARCHAR2(100),
	"UPDATE_TIME" DATE,
	 PRIMARY KEY ("ID"))';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX "SYS_C0011181" ON "LOWCODE_MODEL" ("ID")';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX "IDX_MODELNAME" ON "LOWCODE_MODEL" ("MODEL_NAME", "MODEL_APP")';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-1408) THEN
		RAISE;
	END IF;
END;
/
COMMENT ON TABLE LOWCODE_MODEL IS '模型配置';
COMMENT ON COLUMN LOWCODE_MODEL.ID IS 'ID';
COMMENT ON COLUMN LOWCODE_MODEL.MODEL_NAME IS '模型全小写名称（模型编码）';
COMMENT ON COLUMN LOWCODE_MODEL.MODEL_LABEL IS '模型中文名称';
COMMENT ON COLUMN LOWCODE_MODEL.MODEL_LABEL_UT IS '模型中文名称更新时间';
COMMENT ON COLUMN LOWCODE_MODEL.MODEL_CLASS IS '模型java类含包名';
COMMENT ON COLUMN LOWCODE_MODEL.MODEL_TABLE IS '模型对应表名';
COMMENT ON COLUMN LOWCODE_MODEL.VIRTUAL_MODEL IS '是否虚拟对象';
COMMENT ON COLUMN LOWCODE_MODEL.BIZ_MODEL IS '是否业务对象';
COMMENT ON COLUMN LOWCODE_MODEL.MODEL_APP IS '所属应用';
COMMENT ON COLUMN LOWCODE_MODEL.MODULE_CODE IS '模型归属应用模块';
COMMENT ON COLUMN LOWCODE_MODEL.MODEL_PROCESSOR IS '模型处理器beanId';
COMMENT ON COLUMN LOWCODE_MODEL.DATAFILTER_PROCESSOR IS '数据过滤处理器beanId';
COMMENT ON COLUMN LOWCODE_MODEL.DEFAULT_ORDER IS '模型默认排序(xxx asc,xxx desc)';
COMMENT ON COLUMN LOWCODE_MODEL.DEFAULT_ORDER_UT IS '模型默认排序更新时间';
COMMENT ON COLUMN LOWCODE_MODEL.PERMIT_EXT_FLAG IS '是否允许二开';
COMMENT ON COLUMN LOWCODE_MODEL.EXTEND_FLAG IS '是否二开';
COMMENT ON COLUMN LOWCODE_MODEL.SYS_FLAG IS '是否内置';
COMMENT ON COLUMN LOWCODE_MODEL.STATUS IS '是否发布状态';
COMMENT ON COLUMN LOWCODE_MODEL.PUBLISH_TIME IS '发布时间';
COMMENT ON COLUMN LOWCODE_MODEL.MODEL_HIS_TYPE IS '拉链表记录类型，多个之间用逗号隔开';
COMMENT ON COLUMN LOWCODE_MODEL.MODEL_HIS_TYPE_UT IS '拉链表记录类型更新时间';
COMMENT ON COLUMN LOWCODE_MODEL.MODEL_CLASSIFY IS '模型分类';
COMMENT ON COLUMN LOWCODE_MODEL.MODEL_CLASSIFY_UT IS '模型分类更新时间';
COMMENT ON COLUMN LOWCODE_MODEL.RENDER_TYPE IS '渲染类型';
COMMENT ON COLUMN LOWCODE_MODEL.RENDER_TYPE_UT IS '渲染类型更新时间';
COMMENT ON COLUMN LOWCODE_MODEL.ORDER_INDEX IS '模型展示排序';
COMMENT ON COLUMN LOWCODE_MODEL.ORDER_INDEX_UT IS '模型展示排序更新时间';
COMMENT ON COLUMN LOWCODE_MODEL.CHECKSUM IS '文件大小';
COMMENT ON COLUMN LOWCODE_MODEL.TABLE_META IS '表分区信息';
COMMENT ON COLUMN LOWCODE_MODEL.TABLE_META_UT IS '表分区信息更新时间';
COMMENT ON COLUMN LOWCODE_MODEL.CREATE_BY IS '创建人';
COMMENT ON COLUMN LOWCODE_MODEL.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN LOWCODE_MODEL.UPDATE_BY IS '更新人';
COMMENT ON COLUMN LOWCODE_MODEL.UPDATE_TIME IS '更新时间';

-- T_DA_MODEL_EXTEND 模型扩展表
BEGIN
EXECUTE IMMEDIATE 'CREATE TABLE "T_DA_MODEL_EXTEND"
   (	"ID" VARCHAR2(100),
	"MODEL_ID" VARCHAR2(255),
	"MODEL_TYPE" VARCHAR2(255),
	"METRIC_ID" VARCHAR2(255),
	"PRIMARY_MODEL" VARCHAR2(128),
	"MODEL_ENCODING" VARCHAR2(100),
	"COLUMN_CLASSIFY" VARCHAR2(100),
	"MODEL_ENABLE" VARCHAR2(100),
	"DATA_SOURCE" VARCHAR2(100),
	"ENTITY_ID" VARCHAR2(100),
	"MODEL_DESC" VARCHAR2(3000),
	"MARK_OBJECT_ID" VARCHAR2(100),
	"LABEL_CREATE_ID" VARCHAR2(100),
	"THEME_CODE" VARCHAR2(50),
	"ACCESS_LEVEL" VARCHAR2(128),
	"USE_NUM" NUMBER(8,0) DEFAULT 0 NOT NULL ENABLE,
	"UPDATE_MODE" VARCHAR2(128),
	"UPDATE_FREQUENCY" VARCHAR2(128),
	"STATISTICAL_PERIOD" VARCHAR2(128),
	"MODEL_VERSION" VARCHAR2(50),
	"INTERNAL_VERSION" VARCHAR2(50),
	"MODEL_SNAPSHOT" CLOB,
	"CREATE_BY" VARCHAR2(100),
	"CREATE_TIME" DATE,
	"UPDATE_BY" VARCHAR2(100),
	"UPDATE_TIME" DATE,
	 PRIMARY KEY ("ID")
   )';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX "SYS_C0011829" ON "T_DA_MODEL_EXTEND" ("ID")';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-1408) THEN
		RAISE;
	END IF;
END;
/
COMMENT ON TABLE T_DA_MODEL_EXTEND IS '模型扩展表';
COMMENT ON COLUMN T_DA_MODEL_EXTEND.ID IS 'ID';
COMMENT ON COLUMN T_DA_MODEL_EXTEND.MODEL_ID IS '模型ID';
COMMENT ON COLUMN T_DA_MODEL_EXTEND.MODEL_TYPE IS '模型类型';
COMMENT ON COLUMN T_DA_MODEL_EXTEND.METRIC_ID IS '指标ID(指标模型)';
COMMENT ON COLUMN T_DA_MODEL_EXTEND.PRIMARY_MODEL IS '是否主表标识';
COMMENT ON COLUMN T_DA_MODEL_EXTEND.MODEL_ENCODING IS '模型编码';
COMMENT ON COLUMN T_DA_MODEL_EXTEND.COLUMN_CLASSIFY IS '标签模型分类(标签模型)';
COMMENT ON COLUMN T_DA_MODEL_EXTEND.MODEL_ENABLE IS '模型是否启用';
COMMENT ON COLUMN T_DA_MODEL_EXTEND.DATA_SOURCE IS '数据来源(常模)';
COMMENT ON COLUMN T_DA_MODEL_EXTEND.ENTITY_ID IS '对象id';
COMMENT ON COLUMN T_DA_MODEL_EXTEND.MODEL_DESC IS '模型说明';
COMMENT ON COLUMN T_DA_MODEL_EXTEND.MARK_OBJECT_ID IS '打标对象id';
COMMENT ON COLUMN T_DA_MODEL_EXTEND.LABEL_CREATE_ID IS '标签创建返回ID';
COMMENT ON COLUMN T_DA_MODEL_EXTEND.THEME_CODE IS '主题域编码';
COMMENT ON COLUMN T_DA_MODEL_EXTEND.ACCESS_LEVEL IS '模型可见性';
COMMENT ON COLUMN T_DA_MODEL_EXTEND.USE_NUM IS '引用次数';
COMMENT ON COLUMN T_DA_MODEL_EXTEND.UPDATE_MODE IS '更新方式';
COMMENT ON COLUMN T_DA_MODEL_EXTEND.UPDATE_FREQUENCY IS '更新频率';
COMMENT ON COLUMN T_DA_MODEL_EXTEND.STATISTICAL_PERIOD IS '统计周期';
COMMENT ON COLUMN T_DA_MODEL_EXTEND.MODEL_VERSION IS '模型版本号';
COMMENT ON COLUMN T_DA_MODEL_EXTEND.INTERNAL_VERSION IS '内部版本号';
COMMENT ON COLUMN T_DA_MODEL_EXTEND.MODEL_SNAPSHOT IS '模型快照';
COMMENT ON COLUMN T_DA_MODEL_EXTEND.CREATE_BY IS '创建人';
COMMENT ON COLUMN T_DA_MODEL_EXTEND.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN T_DA_MODEL_EXTEND.UPDATE_BY IS '更新人';
COMMENT ON COLUMN T_DA_MODEL_EXTEND.UPDATE_TIME IS '更新时间';

-- T_DA_MODEL_THEME 模型使用范围
BEGIN
EXECUTE IMMEDIATE 'CREATE TABLE "T_DA_MODEL_THEME"
   (	"ID" VARCHAR2(100),
	"THEME_CODE" VARCHAR2(255),
	"MODEL_ID" VARCHAR2(255),
	"CREATE_BY" VARCHAR2(100),
	"CREATE_TIME" DATE,
	"UPDATE_BY" VARCHAR2(100),
	"UPDATE_TIME" DATE,
	 PRIMARY KEY ("ID")
   )';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX "SYS_C0011840" ON "T_DA_MODEL_THEME" ("ID")';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-1408) THEN
		RAISE;
	END IF;
END;
/
COMMENT ON TABLE T_DA_MODEL_THEME IS '模型使用范围';
COMMENT ON COLUMN T_DA_MODEL_THEME.ID IS 'ID';
COMMENT ON COLUMN T_DA_MODEL_THEME.THEME_CODE IS '主题域code';
COMMENT ON COLUMN T_DA_MODEL_THEME.MODEL_ID IS '模型ID';
COMMENT ON COLUMN T_DA_MODEL_THEME.CREATE_BY IS '创建人';
COMMENT ON COLUMN T_DA_MODEL_THEME.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN T_DA_MODEL_THEME.UPDATE_BY IS '更新人';
COMMENT ON COLUMN T_DA_MODEL_THEME.UPDATE_TIME IS '更新时间';

-- LOWCODE_MODEL_CLASSIFY 模型分类
BEGIN
EXECUTE IMMEDIATE 'CREATE TABLE "LOWCODE_MODEL_CLASSIFY"
   (	"ID" VARCHAR2(100),
	"CLASSIFY_NAME" VARCHAR2(90) NOT NULL ENABLE,
	"CLASSIFY_CODE" VARCHAR2(100) NOT NULL ENABLE,
	"PARENT_ID" VARCHAR2(128),
	"STATUS" NUMBER(8,0) DEFAULT 1,
	"SHOW_ORDER" NUMBER(8,0),
	"SYS_FLAG" NUMBER(10,0) DEFAULT 0 NOT NULL ENABLE,
	"CREATE_BY" VARCHAR2(100),
	"CREATE_TIME" DATE,
	"UPDATE_BY" VARCHAR2(100),
	"UPDATE_TIME" DATE,
	 PRIMARY KEY ("ID"))';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX "SYS_C0011259" ON "LOWCODE_MODEL_CLASSIFY" ("ID")';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-1408) THEN
		RAISE;
	END IF;
END;
/
COMMENT ON TABLE LOWCODE_MODEL_CLASSIFY IS '模型分类';
COMMENT ON COLUMN LOWCODE_MODEL_CLASSIFY.ID IS 'ID';
COMMENT ON COLUMN LOWCODE_MODEL_CLASSIFY.CLASSIFY_NAME IS '分类名称';
COMMENT ON COLUMN LOWCODE_MODEL_CLASSIFY.CLASSIFY_CODE IS '分类编码';
COMMENT ON COLUMN LOWCODE_MODEL_CLASSIFY.PARENT_ID IS '父分类Id';
COMMENT ON COLUMN LOWCODE_MODEL_CLASSIFY.STATUS IS '状态';
COMMENT ON COLUMN LOWCODE_MODEL_CLASSIFY.SHOW_ORDER IS '排序';
COMMENT ON COLUMN LOWCODE_MODEL_CLASSIFY.SYS_FLAG IS '是否内置';
COMMENT ON COLUMN LOWCODE_MODEL_CLASSIFY.CREATE_BY IS '创建人';
COMMENT ON COLUMN LOWCODE_MODEL_CLASSIFY.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN LOWCODE_MODEL_CLASSIFY.UPDATE_BY IS '更新人';
COMMENT ON COLUMN LOWCODE_MODEL_CLASSIFY.UPDATE_TIME IS '更新时间';

-- T_DA_MODEL_CLASSIFY_EXTEND 模型分类扩展表
BEGIN
EXECUTE IMMEDIATE 'CREATE TABLE "T_DA_MODEL_CLASSIFY_EXTEND"
   (	"ID" VARCHAR2(100),
	"CLASSIFY_ID" VARCHAR2(255),
	"CLASSIFY_TYPE" VARCHAR2(255),
	"ENTITY_ID" VARCHAR2(255),
	"CLASSIFY_DESC" VARCHAR2(600),
	"CREATE_BY" VARCHAR2(100),
	"CREATE_TIME" DATE,
	"UPDATE_BY" VARCHAR2(100),
	"UPDATE_TIME" DATE,
	 PRIMARY KEY ("ID")
   )';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX "SYS_C0011832" ON "T_DA_MODEL_CLASSIFY_EXTEND" ("ID")';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-1408) THEN
		RAISE;
	END IF;
END;
/
COMMENT ON TABLE T_DA_MODEL_CLASSIFY_EXTEND IS '模型分类扩展表';
COMMENT ON COLUMN T_DA_MODEL_CLASSIFY_EXTEND.ID IS 'ID';
COMMENT ON COLUMN T_DA_MODEL_CLASSIFY_EXTEND.CLASSIFY_ID IS '分类ID';
COMMENT ON COLUMN T_DA_MODEL_CLASSIFY_EXTEND.CLASSIFY_TYPE IS '分类模型类型';
COMMENT ON COLUMN T_DA_MODEL_CLASSIFY_EXTEND.ENTITY_ID IS '主题域对象ID';
COMMENT ON COLUMN T_DA_MODEL_CLASSIFY_EXTEND.CLASSIFY_DESC IS '分类描述';
COMMENT ON COLUMN T_DA_MODEL_CLASSIFY_EXTEND.CREATE_BY IS '创建人';
COMMENT ON COLUMN T_DA_MODEL_CLASSIFY_EXTEND.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN T_DA_MODEL_CLASSIFY_EXTEND.UPDATE_BY IS '更新人';
COMMENT ON COLUMN T_DA_MODEL_CLASSIFY_EXTEND.UPDATE_TIME IS '更新时间';

-- LOWCODE_MODEL_COLUMN 模型字段配置
BEGIN
EXECUTE IMMEDIATE 'CREATE TABLE "LOWCODE_MODEL_COLUMN"
   (	"ID" VARCHAR2(100),
	"COLUMN_JAVANAME" VARCHAR2(255) NOT NULL ENABLE,
	"MODEL_ID" VARCHAR2(128) NOT NULL ENABLE,
	"COLUMN_JAVATYPE" VARCHAR2(500),
	"COLUMN_DBNAME" VARCHAR2(255),
	"COLUMN_DBTYPE" NUMBER(10,0),
	"COLUMN_WIDTH" NUMBER(10,0),
	"COLUMN_WIDTH_UT" DATE,
	"COLUMN_PRECISION" NUMBER(10,0),
	"COLUMN_PRECISION_UT" DATE,
	"COLUMN_LABEL" VARCHAR2(255),
	"COLUMN_LABEL_UT" DATE,
	"COLUMN_NOTNULL" NUMBER(10,0),
	"COLUMN_NOTNULL_UT" DATE,
	"ORDER_INDEX" NUMBER(8,0),
	"ORDER_INDEX_UT" DATE,
	"DBF_SIZE" NUMBER(8,0),
	"DBF_SIZE_UT" DATE,
	"COLUMN_DEFAULT" VARCHAR2(90),
	"COLUMN_DEFAULT_UT" DATE,
	"COLUMN_ADD" NUMBER(10,0) DEFAULT 1,
	"COLUMN_ADD_UT" DATE,
	"COLUMN_LINKSELECT" NUMBER(10,0) DEFAULT 1,
	"COLUMN_LINKSELECT_UT" DATE,
	"COLUMN_UPDATE" NUMBER(10,0) DEFAULT 1,
	"COLUMN_UPDATE_UT" DATE,
	"COLUMN_DISPLAY" NUMBER(10,0) DEFAULT 1,
	"COLUMN_DISPLAY_UT" DATE,
	"COLUMN_FILTER" NUMBER(10,0),
	"COLUMN_FILTER_UT" DATE,
	"COLUMN_TYPE" NUMBER(10,0),
	"COLUMN_TYPE_UT" DATE,
	"COLUMN_PREVINSERT" VARCHAR2(255),
	"COLUMN_PREVINSERT_UT" DATE,
	"COLUMN_PREVUPDATE" VARCHAR2(255),
	"COLUMN_PREVUPDATE_UT" DATE,
	"COLUMN_ENCRYPT" NUMBER(10,0) DEFAULT 0 NOT NULL ENABLE,
	"COLUMNSECURE" VARCHAR2(32),
	"COLUMN_SECURE_UT" DATE,
	"COLUMN_EL" VARCHAR2(255),
	"COLUMN_EL_UT" DATE,
	"VIRTUAL_COLUMN" NUMBER(10,0) DEFAULT 0 NOT NULL ENABLE,
	"EXTEND_FLAG" NUMBER(10,0) DEFAULT 0 NOT NULL ENABLE,
	"EXTEND_JAVANAME" VARCHAR2(255),
	"ABBR_JAVANAME" VARCHAR2(255),
	"ABBR_JAVANAME_UT" DATE,
	"EVENT_TIME_POINT" NUMBER(10,0) DEFAULT 0,
	"EVENT_TIME_POINT_UT" DATE,
	"LOGICAL_TYPE" VARCHAR2(255),
	"LOGICAL_TYPE_UT" DATE,
	"VIRTUAL_COLUMN_STATUS" NUMBER(2,0),
	"COLUMN_DESCRIPTION" VARCHAR2(4000),
	"COLUMN_DESCRIPTION_UT" DATE,
	"COLUMN_DICT" VARCHAR2(255),
	"COLUMN_DICT_UT" DATE,
	"COLUMN_DICT_SPLIT" VARCHAR2(16),
	"COLUMN_DICT_SPLIT_UT" DATE,
	"COLUMN_GROUP" VARCHAR2(90),
	"COLUMN_GROUP_UT" DATE,
	"COLUMN_READONLY" NUMBER(10,0) DEFAULT 0,
	"COLUMN_READONLY_UT" DATE,
	"QUICKSEARCH_FLAG" NUMBER(10,0),
	"QUICKSEARCH_FLAG_UT" DATE,
	"FUZZYSEARCH_FLAG" NUMBER(10,0),
	"FUZZYSEARCH_FLAG_UT" DATE,
	"COLUMN_PATTERN" VARCHAR2(255),
	"COLUMN_PATTERN_UT" DATE,
	"COLUMN_HIDDEN" NUMBER(10,0) DEFAULT 0,
	"COLUMN_HIDDEN_UT" DATE,
	"COLUMN_FORMAT" VARCHAR2(255),
	"COLUMN_FORMAT_UT" DATE,
	"COLUMN_JSONPARAM" VARCHAR2(4000),
	"COLUMN_JSONPARAM_UT" DATE,
	"COLUMN_HTMLEDITPARAM" VARCHAR2(4000),
	"COLUMN_HTMLEDITPARAM_UT" DATE,
	"COLUMN_HTMLSHOWPARAM" VARCHAR2(4000),
	"COLUMN_HTMLSHOWPARAM_UT" DATE,
	"COLUMN_XTYPE" VARCHAR2(255),
	"COLUMN_XTYPE_UT" DATE,
	"BUILDER_DEFAULT" VARCHAR2(255),
	"BUILDER_DEFAULT_UT" DATE,
	"TABLE_READONLY" NUMBER(10,0),
	"TABLE_READONLY_UT" DATE,
	"TABLE_HIDDEN" NUMBER(10,0),
	"TABLE_HIDDEN_UT" DATE,
	"TABLE_XTYPE" VARCHAR2(255),
	"TABLE_XTYPE_UT" DATE,
	"TABLE_REQUIRED" NUMBER(10,0),
	"TABLE_REQUIRED_UT" DATE,
	"TABLE_JSONPARAM" VARCHAR2(255),
	"TABLE_JSONPARAM_UT" DATE,
	"TABLE_HTMLEDITPARAM" VARCHAR2(4000),
	"TABLE_HTMLEDITPARAM_UT" DATE,
	"TABLE_HTMLSHOWPARAM" VARCHAR2(4000),
	"TABLE_HTMLSHOWPARAM_UT" DATE,
	"TABLE_FIXED" VARCHAR2(32),
	"TABLE_FIXED_UT" DATE,
	"TABLE_COLUMN_GROUP" VARCHAR2(300),
	"TABLE_COLUMN_GROUP_UT" DATE,
	"FORM_READONLY" NUMBER(10,0),
	"FORM_READONLY_UT" DATE,
	"FORM_HIDDEN" NUMBER(10,0),
	"FORM_HIDDEN_UT" DATE,
	"FORM_XTYPE" VARCHAR2(255),
	"FORM_XTYPE_UT" DATE,
	"FORM_REQUIRED" NUMBER(10,0),
	"FORM_REQUIRED_UT" DATE,
	"FORM_JSONPARAM" VARCHAR2(255),
	"FORM_JSONPARAM_UT" DATE,
	"FORM_HTMLEDITPARAM" VARCHAR2(4000),
	"FORM_HTMLEDITPARAM_UT" DATE,
	"FORM_HTMLSHOWPARAM" VARCHAR2(4000),
	"FORM_HTMLSHOWPARAM_UT" DATE,
	"SEARCH_READONLY" NUMBER(10,0),
	"SEARCH_READONLY_UT" DATE,
	"SEARCH_HIDDEN" NUMBER(10,0),
	"SEARCH_HIDDEN_UT" DATE,
	"SEARCH_XTYPE" VARCHAR2(255),
	"SEARCH_XTYPE_UT" DATE,
	"SEARCH_REQUIRED" NUMBER(10,0),
	"SEARCH_REQUIRED_UT" DATE,
	"SEARCH_JSONPARAM" VARCHAR2(255),
	"SEARCH_JSONPARAM_UT" DATE,
	"SEARCH_HTMLSHOWPARAM" VARCHAR2(4000),
	"SEARCH_HTMLSHOWPARAM_UT" DATE,
	"CREATE_BY" VARCHAR2(100),
	"CREATE_TIME" DATE,
	"UPDATE_BY" VARCHAR2(100),
	"UPDATE_TIME" DATE,
	"COLUMN_DATAFILTER" NUMBER(10,0) DEFAULT 0,
	"COLUMN_DATAFILTER_UT" DATE,
	"COLUMN_STORECOUNT" NUMBER(10,0),
	"COLUMN_STORECOUNT_UT" DATE,
	"COLUMN_STORETYPE" VARCHAR2(400),
	"COLUMN_STORETYPE_UT" DATE,
	"SEARCH_HTMLEDITPARAM" VARCHAR2(4000),
	"SEARCH_HTMLEDITPARAM_UT" DATE,
	"COLUMN_ENCRYPT_UT" DATE,
	 PRIMARY KEY ("ID")
   )';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'ALTER TABLE LOWCODE_MODEL_COLUMN ADD COLUMN_DATAFILTER NUMBER(10,0) DEFAULT 0';
EXCEPTION
WHEN OTHERS THEN
 IF sqlcode != -1430 THEN RAISE; END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'ALTER TABLE LOWCODE_MODEL_COLUMN ADD COLUMN_DATAFILTER_UT DATE';
EXCEPTION
WHEN OTHERS THEN
 IF sqlcode != -1430 THEN RAISE; END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'ALTER TABLE LOWCODE_MODEL_COLUMN ADD COLUMN_STORECOUNT NUMBER(10,0)';
EXCEPTION
WHEN OTHERS THEN
 IF sqlcode != -1430 THEN RAISE; END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'ALTER TABLE LOWCODE_MODEL_COLUMN ADD COLUMN_STORECOUNT_UT DATE';
EXCEPTION
WHEN OTHERS THEN
 IF sqlcode != -1430 THEN RAISE; END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'ALTER TABLE LOWCODE_MODEL_COLUMN ADD COLUMN_STORETYPE VARCHAR2(400)';
EXCEPTION
WHEN OTHERS THEN
 IF sqlcode != -1430 THEN RAISE; END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'ALTER TABLE LOWCODE_MODEL_COLUMN ADD COLUMN_STORETYPE_UT DATE';
EXCEPTION
WHEN OTHERS THEN
 IF sqlcode != -1430 THEN RAISE; END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'ALTER TABLE LOWCODE_MODEL_COLUMN ADD SEARCH_HTMLEDITPARAM VARCHAR2(4000)';
EXCEPTION
WHEN OTHERS THEN
 IF sqlcode != -1430 THEN RAISE; END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'ALTER TABLE LOWCODE_MODEL_COLUMN ADD SEARCH_HTMLEDITPARAM_UT DATE';
EXCEPTION
WHEN OTHERS THEN
 IF sqlcode != -1430 THEN RAISE; END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'ALTER TABLE LOWCODE_MODEL_COLUMN ADD COLUMN_ENCRYPT NUMBER(10,0) DEFAULT 0 NOT NULL ENABLE';
EXCEPTION
WHEN OTHERS THEN
 IF sqlcode != -1430 THEN RAISE; END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'ALTER TABLE LOWCODE_MODEL_COLUMN ADD COLUMN_ENCRYPT_UT DATE';
EXCEPTION
WHEN OTHERS THEN
 IF sqlcode != -1430 THEN RAISE; END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX "SYS_C0011195" ON "LOWCODE_MODEL_COLUMN" ("ID")';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX "IDX_MODELID_COLUMNJAVANAME" ON "LOWCODE_MODEL_COLUMN" ("MODEL_ID", "COLUMN_JAVANAME")';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-1408) THEN
		RAISE;
	END IF;
END;
/
COMMENT ON TABLE LOWCODE_MODEL_COLUMN IS '模型字段配置';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.ID IS 'ID';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_JAVANAME IS '字段java名称';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.MODEL_ID IS '模型';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_JAVATYPE IS '字段java类型';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_DBNAME IS '字段数据库名称';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_DBTYPE IS '字段数据库类型';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_WIDTH IS '字段数据库长度';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_WIDTH_UT IS '字段数据库长度更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_PRECISION IS '小数位数';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_PRECISION_UT IS '小数位数更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_LABEL IS '字段显示名称';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_LABEL_UT IS '字段显示名称更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_NOTNULL IS '是否必填';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_NOTNULL_UT IS '是否必填更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.ORDER_INDEX IS '排序';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.ORDER_INDEX_UT IS '排序更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.DBF_SIZE IS 'dbf导出字段长度';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.DBF_SIZE_UT IS 'dbf导出字段长度更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_DEFAULT IS '字段默认值';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_DEFAULT_UT IS '字段默认值更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_ADD IS '是否可插入';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_ADD_UT IS '是否可插入更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_LINKSELECT IS '是否可关联查询';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_LINKSELECT_UT IS '是否可关联查询更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_UPDATE IS '是否可更新';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_UPDATE_UT IS '是否可更新更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_DISPLAY IS '是否返回给前端';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_DISPLAY_UT IS '是否返回给前端更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_FILTER IS '字段过滤方式';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_FILTER_UT IS '字段过滤方式更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_TYPE IS '字段类别';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_TYPE_UT IS '字段类别更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_PREVINSERT IS '新增前操作字段表达式';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_PREVINSERT_UT IS '新增前操作字段表达式更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_PREVUPDATE IS '更新前操作字段表达式';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_PREVUPDATE_UT IS '更新前操作字段表达式更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_ENCRYPT IS '是否加密存储';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMNSECURE IS '字段脱敏方式';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_SECURE_UT IS '字段脱敏方式更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_EL IS '字段显示表达式';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_EL_UT IS '字段显示表达式更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.VIRTUAL_COLUMN IS '是否虚拟字段';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.EXTEND_FLAG IS '是否二开';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.EXTEND_JAVANAME IS '定制前字段名称';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.ABBR_JAVANAME IS '字段缩写';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.ABBR_JAVANAME_UT IS '字段缩写更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.EVENT_TIME_POINT IS '事件时点';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.EVENT_TIME_POINT_UT IS '事件时点更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.LOGICAL_TYPE IS '逻辑类型';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.LOGICAL_TYPE_UT IS '逻辑类型更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.VIRTUAL_COLUMN_STATUS IS '虚拟字段状态';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_DESCRIPTION IS '字段描述';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_DESCRIPTION_UT IS '字段描述更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_DICT IS '字段字典';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_DICT_UT IS '字段字典更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_DICT_SPLIT IS '字段字典分隔符';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_DICT_SPLIT_UT IS '字段字典分隔符更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_GROUP IS '分组名称';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_GROUP_UT IS '分组名称更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_READONLY IS '默认是否只读';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_READONLY_UT IS '默认是否只读更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.QUICKSEARCH_FLAG IS '是否用于快速搜索';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.QUICKSEARCH_FLAG_UT IS '是否用于快速搜索更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.FUZZYSEARCH_FLAG IS '是否用于模糊搜索';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.FUZZYSEARCH_FLAG_UT IS '是否用于快速搜索更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_PATTERN IS '处理格式例如：yyyy-MM-dd';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_PATTERN_UT IS '处理格式更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_HIDDEN IS '是否隐藏';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_HIDDEN_UT IS '默认是否隐藏更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_FORMAT IS '显示格式';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_FORMAT_UT IS '显示格式更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_JSONPARAM IS '额外json参数';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_JSONPARAM_UT IS '额外json参数更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_HTMLEDITPARAM IS '额外Html编辑参数';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_HTMLEDITPARAM_UT IS '额外Html编辑参数更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_HTMLSHOWPARAM IS '额外Html展示参数';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_HTMLSHOWPARAM_UT IS '额外Html展示参数更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_XTYPE IS '渲染类型';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_XTYPE_UT IS '默认渲染类型更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.BUILDER_DEFAULT IS '默认条件构造器';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.BUILDER_DEFAULT_UT IS '默认条件构造器更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.TABLE_READONLY IS '表格是否只读';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.TABLE_READONLY_UT IS '表格是否只读更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.TABLE_HIDDEN IS '表格是否显示';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.TABLE_HIDDEN_UT IS '表格是否显示更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.TABLE_XTYPE IS '表格渲染类型';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.TABLE_XTYPE_UT IS '表格渲染类型更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.TABLE_REQUIRED IS '表格是否必填';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.TABLE_REQUIRED_UT IS '表格是否必填更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.TABLE_JSONPARAM IS '表格额外json参数';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.TABLE_JSONPARAM_UT IS '表格额外json参数更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.TABLE_HTMLEDITPARAM IS '表格额外Html编辑参数';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.TABLE_HTMLEDITPARAM_UT IS '表格额外Html编辑参数更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.TABLE_HTMLSHOWPARAM IS '表格额外Html展示参数';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.TABLE_HTMLSHOWPARAM_UT IS '表格额外Html展示参数更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.TABLE_FIXED IS '表格固定位置';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.TABLE_FIXED_UT IS '表格固定位置更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.TABLE_COLUMN_GROUP IS '表格分组名称';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.TABLE_COLUMN_GROUP_UT IS '表格分组名称更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.FORM_READONLY IS '表单是否只读';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.FORM_READONLY_UT IS '表单是否只读更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.FORM_HIDDEN IS '表单是否显示';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.FORM_HIDDEN_UT IS '表单是否显示更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.FORM_XTYPE IS '表单渲染类型';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.FORM_XTYPE_UT IS '表单渲染类型更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.FORM_REQUIRED IS '表单是否必填';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.FORM_REQUIRED_UT IS '表单是否必填更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.FORM_JSONPARAM IS '表单额外json参数';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.FORM_JSONPARAM_UT IS '表单额外json参数更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.FORM_HTMLEDITPARAM IS '表单额外Html编辑参数';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.FORM_HTMLEDITPARAM_UT IS '表单额外Html编辑参数更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.FORM_HTMLSHOWPARAM IS '表单额外Html展示参数';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.FORM_HTMLSHOWPARAM_UT IS '表单额外Html展示参数更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.SEARCH_READONLY IS '搜索是否只读';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.SEARCH_READONLY_UT IS '搜索是否只读更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.SEARCH_HIDDEN IS '搜索是否显示';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.SEARCH_HIDDEN_UT IS '搜索是否显示更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.SEARCH_XTYPE IS '搜索渲染类型';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.SEARCH_XTYPE_UT IS '搜索渲染类型更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.SEARCH_REQUIRED IS '搜索是否必填';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.SEARCH_REQUIRED_UT IS '搜索是否必填更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.SEARCH_JSONPARAM IS '搜索额外json参数';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.SEARCH_JSONPARAM_UT IS '搜索额外json参数更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.SEARCH_HTMLSHOWPARAM IS '搜索额外Html展示参数';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.SEARCH_HTMLSHOWPARAM_UT IS '搜索额外Html展示参数更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.CREATE_BY IS '创建人';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.UPDATE_BY IS '更新人';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_DATAFILTER IS '数据权限过滤类型';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_DATAFILTER_UT IS '数据权限过滤类型更新时间更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_STORECOUNT IS '上传文件数量';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_STORECOUNT_UT IS '上传文件数量更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_STORETYPE IS '上传文件类型';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_STORETYPE_UT IS '上传文件类型更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.SEARCH_HTMLEDITPARAM IS '搜索额外Html编辑参数';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.SEARCH_HTMLEDITPARAM_UT IS '搜索额外Html编辑参数更新时间';
COMMENT ON COLUMN LOWCODE_MODEL_COLUMN.COLUMN_ENCRYPT_UT IS '是否加密存储更新时间';

-- T_DA_MODEL_COLUMN_EXTEND 模型字段扩展表
BEGIN
EXECUTE IMMEDIATE 'CREATE TABLE "T_DA_MODEL_COLUMN_EXTEND"
   (	"ID" VARCHAR2(100),
	"MODEL_ID" VARCHAR2(255),
	"COLUMN_ID" VARCHAR2(255),
	"ENTITY_TAG" VARCHAR2(10),
	"ENTITY_NAME_TAG" VARCHAR2(255),
	"COND_TAG" VARCHAR2(10),
	"TIME_TAG" VARCHAR2(10),
	"VALUE_TAG" VARCHAR2(10),
	"LABEL_ID" VARCHAR2(255),
	"LABEL_TYPE" VARCHAR2(255),
	"LABEL_STATUS" VARCHAR2(10),
	"LABEL_BASE" VARCHAR2(255),
	"USE_NUM" NUMBER(8,0) DEFAULT 0 NOT NULL ENABLE,
	"LABEL_MULTIPLE" VARCHAR2(100),
	"LABEL_CODE" VARCHAR2(255),
	"CLASSIFY_CODE" VARCHAR2(100),
	"LABEL_DESC" VARCHAR2(3000),
	"PRIMARY_COLUMN_TAG" VARCHAR2(10),
	"COLUMN_UNIT" VARCHAR2(255),
	"MODEL_VERSION" VARCHAR2(50),
	"DELETE_FLAG" VARCHAR2(10),
	"CREATE_BY" VARCHAR2(100),
	"CREATE_TIME" DATE,
	"UPDATE_BY" VARCHAR2(100),
	"UPDATE_TIME" DATE,
	 PRIMARY KEY ("ID")
   )';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX "SYS_C0011834" ON "T_DA_MODEL_COLUMN_EXTEND" ("ID")';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-1408) THEN
		RAISE;
	END IF;
END;
/
COMMENT ON TABLE T_DA_MODEL_COLUMN_EXTEND IS '模型字段扩展表';
COMMENT ON COLUMN T_DA_MODEL_COLUMN_EXTEND.ID IS 'ID';
COMMENT ON COLUMN T_DA_MODEL_COLUMN_EXTEND.MODEL_ID IS '模型ID';
COMMENT ON COLUMN T_DA_MODEL_COLUMN_EXTEND.COLUMN_ID IS '字段ID';
COMMENT ON COLUMN T_DA_MODEL_COLUMN_EXTEND.ENTITY_TAG IS '是否实体标识';
COMMENT ON COLUMN T_DA_MODEL_COLUMN_EXTEND.ENTITY_NAME_TAG IS '是否实体名称标识';
COMMENT ON COLUMN T_DA_MODEL_COLUMN_EXTEND.COND_TAG IS '是否筛选条件字段';
COMMENT ON COLUMN T_DA_MODEL_COLUMN_EXTEND.TIME_TAG IS '是否时点标识字段';
COMMENT ON COLUMN T_DA_MODEL_COLUMN_EXTEND.VALUE_TAG IS '是否指标值字段';
COMMENT ON COLUMN T_DA_MODEL_COLUMN_EXTEND.LABEL_ID IS '标签ID';
COMMENT ON COLUMN T_DA_MODEL_COLUMN_EXTEND.LABEL_TYPE IS '标签类型';
COMMENT ON COLUMN T_DA_MODEL_COLUMN_EXTEND.LABEL_STATUS IS '标签生产状态';
COMMENT ON COLUMN T_DA_MODEL_COLUMN_EXTEND.LABEL_BASE IS '是否基础标签';
COMMENT ON COLUMN T_DA_MODEL_COLUMN_EXTEND.USE_NUM IS '引用次数';
COMMENT ON COLUMN T_DA_MODEL_COLUMN_EXTEND.LABEL_MULTIPLE IS '是否支持多值';
COMMENT ON COLUMN T_DA_MODEL_COLUMN_EXTEND.LABEL_CODE IS '标签编号';
COMMENT ON COLUMN T_DA_MODEL_COLUMN_EXTEND.CLASSIFY_CODE IS '标签模型分类';
COMMENT ON COLUMN T_DA_MODEL_COLUMN_EXTEND.LABEL_DESC IS '标签说明';
COMMENT ON COLUMN T_DA_MODEL_COLUMN_EXTEND.PRIMARY_COLUMN_TAG IS '是否主键字段';
COMMENT ON COLUMN T_DA_MODEL_COLUMN_EXTEND.COLUMN_UNIT IS '单位';
COMMENT ON COLUMN T_DA_MODEL_COLUMN_EXTEND.MODEL_VERSION IS '模型版本号';
COMMENT ON COLUMN T_DA_MODEL_COLUMN_EXTEND.DELETE_FLAG IS '0：删除 1：已删除';
COMMENT ON COLUMN T_DA_MODEL_COLUMN_EXTEND.CREATE_BY IS '创建人';
COMMENT ON COLUMN T_DA_MODEL_COLUMN_EXTEND.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN T_DA_MODEL_COLUMN_EXTEND.UPDATE_BY IS '更新人';
COMMENT ON COLUMN T_DA_MODEL_COLUMN_EXTEND.UPDATE_TIME IS '更新时间';
