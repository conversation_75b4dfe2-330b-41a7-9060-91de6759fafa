/*
 Description		: [职教一体化(IVE)]主题域报表表名对照表
 Author				: dataapp
 Date				: 2024-06-20 01:00:00
 Lowcode Version	: V1.1.1_Beta5
 Database           : oracle
*/

BEGIN
    EXECUTE IMMEDIATE 'CREATE TABLE t_table_map (
  id VARCHAR2(100 BYTE),
  table_name VARCHAR2(200 BYTE),
  comments VARCHAR2(500 BYTE),
  sql_str CLOB,
  create_by VARCHAR2(100),
  create_time DATE,
  update_by VARCHAR2(100),
  update_time DATE,PRIMARY KEY (id))';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

COMMENT ON TABLE t_table_map IS '报表模型表名映射';
COMMENT ON COLUMN t_table_map.id IS 'ID';
COMMENT ON COLUMN t_table_map.table_name IS '表名';
COMMENT ON COLUMN t_table_map.comments IS '表注释';
COMMENT ON COLUMN t_table_map.sql_str IS 'sql脚本';
COMMENT ON COLUMN t_table_map.create_by IS '创建人';
COMMENT ON COLUMN t_table_map.create_time IS '创建时间';
COMMENT ON COLUMN t_table_map.update_by IS '更新人';
COMMENT ON COLUMN t_table_map.update_time IS '更新时间';

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xxjbxx', 'abd_sch_xxjbxx', '学校基本信息', TO_CLOB('
select
学校基本信息.xxbsm as 学校标识码,
学校基本信息.xxmc as 学校名称,
学校基本信息.xxywmc as 学校英文名称,
学校基本信息.xxdz as 学校地址,
学校基本信息.xxyzbm as 学校邮政编码,
学校基本信息.xzqh as 行政区划,
学校基本信息.szdcxlx as 所在地城乡类型,
学校基本信息.jxny as 建校年月,
学校基本信息.xqr as 校庆日,
学校基本信息.xxbxlx as 学校办学类型,
学校基本信息.xxjbz as 学校举办者,
学校基本信息.xxzgbm as 学校主管部门,
学校基本信息.fddbrh as 法定代表人号,
学校基本信息.frzsh as 法人证书号,
学校基本信息.xzxm as 校长姓名,
学校基本信息.dwfzr as 党委负责人,
学校基本信息.zzjgm as 组织机构码,
学校基本信息.lxdh as 联系电话,
学校基本信息.czdh as 传真电话,
学校基本信息.dzxx as 电子信箱,
学校基本信息.xxbb as 学校办别,
学校基本信息.xxxz as 学校性质
from abd_sch_xxjbxx 学校基本信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xxjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xxjbsj', 'abd_sch_xxjbsj', '学校基本数据', TO_CLOB('
select
学校基本数据.zjsjwyxbs as 主键数据唯一性标识,
学校基本数据.sjgbm as 省机构编码,
学校基本数据.sjgmc as 省机构名称,
学校基本数据.shjgbm as 市机构编码,
学校基本数据.shjgmc as 市机构名称,
学校基本数据.qxjgbm as 区县机构编码,
学校基本数据.qxjgmc as 区县机构名称,
学校基本数据.xxjgdm as 学校机构代码,
学校基本数据.xxdm as 学校代码,
学校基本数据.xxmc as 学校名称,
学校基本数据.jxny as 建校年月,
学校基本数据.xxjbzmc as 学校举办者名称,
学校基本数据.xxjbzxzm as 学校举办者性质码,
学校基本数据.xxjbzjbm as 学校举办者级别码,
学校基本数据.xxxzm as 学校性质码,
学校基本数据.gspxxxzm as 高水平学校性质码,
学校基本数据.gspxxjbm as 高水平学校专业群级别码,
学校基本数据.gspxxlxbmmc as 高水平学校专业群立项部门名称全称,
学校基本数据.gspxxpzny as 高水平学校专业群批准年月,
学校基本数据.zyqsl as 专业群数量,
学校基本数据.xxbxlxm as 学校办学类型码,
学校基本数据.xyjzgzs as 现有教职工总数,
学校基本数据.zjgrs as 专技岗人数,
学校基本数据.glgrs as 管理岗人数,
学校基本数据.gqgrs as 工勤岗人数,
学校基本数据.xyxss as 现有学生数,
学校基本数据.xxtsxxhxts as 学校特色信息化系统数,
学校基本数据.xyckdk as 校园出口带宽,
学校基本数据.xy')||TO_CLOB('zgwdk as 校园主干网带宽,
学校基本数据.yxwljrs as 有线网络接入数,
学校基本数据.wxwljrs as 无线网络接入数,
学校基本数据.dmtjss as 多媒体教室数,
学校基本数据.sjcjsj as 数据采集时间
from abd_sch_xxjbsj 学校基本数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xxjbsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xxhxtjssj', 'abd_sch_xxhxtjssj', '信息化系统建设数据', TO_CLOB('
select
信息化系统建设数据.zjsjwyxbs as 主键数据唯一性标识,
信息化系统建设数据.xxdm as 学校代码,
信息化系统建设数据.xxhxtbh as 信息化系统编号,
信息化系统建设数据.xxhxtflm as 信息化系统分类码,
信息化系统建设数据.xxhxtjc as 信息化系统简称,
信息化系统建设数据.xxhxtqc as 信息化系统全称,
信息化系统建设数据.xxhxtfwdz as 信息化系统访问地址,
信息化系统建设数据.sftsxxhxt as 是否特色信息化系统,
信息化系统建设数据.xxhxtjslxm as 信息化系统建设类型码,
信息化系统建设数据.sjcjsj as 数据采集时间
from abd_sch_xxhxtjssj 信息化系统建设数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xxhxtjssj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xxhxtfwjlsj', 'abd_sch_xxhxtfwjlsj', '信息化系统访问记录数据', TO_CLOB('
select
信息化系统访问记录数据.zjsjwyxbs as 主键数据唯一性标识,
信息化系统访问记录数据.xxdm as 学校代码,
信息化系统访问记录数据.xxhxtbh as 信息化系统编号,
信息化系统访问记录数据.xxhxtqc as 信息化系统全称,
信息化系统访问记录数据.fwfsm as 访问方式码,
信息化系统访问记录数据.jsfwcs as 教师访问次数,
信息化系统访问记录数据.xsfwcs as 学生访问次数,
信息化系统访问记录数据.xxhxtfwrq as 信息化系统访问日期,
信息化系统访问记录数据.sjcjsj as 数据采集时间
from abd_sch_xxhxtfwjlsj 信息化系统访问记录数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xxhxtfwjlsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xxtsxxhxtyysj', 'abd_sch_xxtsxxhxtyysj', '学校特色信息化系统应用数据', TO_CLOB('
select
学校特色信息化系统应用数据.zjsjwyxbs as 主键数据唯一性标识,
学校特色信息化系统应用数据.xxdm as 学校代码,
学校特色信息化系统应用数据.tsxxhxtbh as 特色信息化系统编号,
学校特色信息化系统应用数据.tsxxhxtqc as 特色信息化系统全称,
学校特色信息化系统应用数据.nr as 内容,
学校特色信息化系统应用数据.csrq as 产生日期,
学校特色信息化系统应用数据.sjcjsj as 数据采集时间
from abd_sch_xxtsxxhxtyysj 学校特色信息化系统应用数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xxtsxxhxtyysj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_kcjbsj', 'abd_sch_kcjbsj', '课程基本数据', TO_CLOB('
select
课程基本数据.zjsjwyxbs as 主键数据唯一性标识,
课程基本数据.xxdm as 学校代码,
课程基本数据.kcmc as 课程名称,
课程基本数据.kch as 课程号,
课程基本数据.kclxm as 课程类型码,
课程基本数据.kcsxm as 课程属性码,
课程基本数据.ggklbm as 公共课类别码,
课程基本数据.kcxzm as 课程性质码,
课程基本数据.sfxqhzkfkc as 是否校企合作开发课程,
课程基本数据.jpkcjbm as 精品课程级别码,
课程基本数据.jxjhgdkss as 教学计划规定课时数,
课程基本数据.sjjxkss as 实践教学课时数,
课程基本数据.sfyxskc as 是否有线上课程,
课程基本数据.xskcwz as 线上课程网址,
课程基本数据.sfkcszsfkc as 是否课程思政示范课程,
课程基本数据.ktjxsjzyxykczym as 课堂教学设计主要选用课程资源码,
课程基本数据.xnfzsxkss as 虚拟仿真实训课时数,
课程基本数据.zyksxnfzsxxmmc as 主要开设虚拟仿真实训项目名称,
课程基本数据.zykssyxmmc as 主要开设实验项目名称,
课程基本数据.zykssxxmmc as 主要开设实训项目名称,
课程基本数据.zyksshxxmmc as 主要开设实习项目名称,
课程基本数据.sjcjsj as 数据采集时间
from abd_sch_kcjbsj 课程基本数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_kcjbsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_kcjssj', 'abd_sch_kcjssj', '课程建设数据', TO_CLOB('
select
课程建设数据.zjsjwyxbs as 主键数据唯一性标识,
课程建设数据.xxdm as 学校代码,
课程建设数据.kch as 课程号,
课程建设数据.kcmc as 课程名称,
课程建设数据.fzrh as 负责人号,
课程建设数据.sqrq as 申请日期,
课程建设数据.lxnd as 立项年度,
课程建设数据.ysrq as 验收日期,
课程建设数据.jflym as 经费来源码,
课程建设数据.lxjf as 立项经费,
课程建设数据.ssdwh as 所属单位号,
课程建设数据.kcjslbm as 课程建设类别码,
课程建设数据.sjcjsj as 数据采集时间
from abd_sch_kcjssj 课程建设数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_kcjssj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_gjptzydjsj', 'abd_sch_gjptzydjsj', '国家平台资源对接数据', TO_CLOB('
select
国家平台资源对接数据.zjsjwyxbs as 主键数据唯一性标识,
国家平台资源对接数据.xxdm as 学校代码,
国家平台资源对接数据.zybh as 资源编号,
国家平台资源对接数据.zymc as 资源名称,
国家平台资源对接数据.zyjbm as 资源级别码,
国家平台资源对接数据.gjzhjxptzylxm as 国家智慧教学平台资源类型码,
国家平台资源对接数据.zyfwlj as 资源访问链接,
国家平台资源对接数据.djrq as 对接日期,
国家平台资源对接数据.sjcjsj as 数据采集时间
from abd_sch_gjptzydjsj 国家平台资源对接数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_gjptzydjsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_zyjsqksj', 'abd_sch_zyjsqksj', '专业建设情况数据', TO_CLOB('
select
专业建设情况数据.zjsjwyxbs as 主键数据唯一性标识,
专业建设情况数据.xxdm as 学校代码,
专业建设情况数据.zyh as 专业号,
专业建设情况数据.zdzyjbm as 重点专业级别码,
专业建设情况数据.zdzylxm as 重点专业类型码,
专业建设情况数据.syrq as 授予日期,
专业建设情况数据.bz as 备注,
专业建设情况数据.sjcjsj as 数据采集时间
from abd_sch_zyjsqksj 专业建设情况数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_zyjsqksj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_zyszsj', 'abd_sch_zyszsj', '专业设置数据', TO_CLOB('
select
专业设置数据.zjsjwyxbs as 主键数据唯一性标识,
专业设置数据.xxdm as 学校代码,
专业设置数据.zyssyxbmc as 专业所属院系部名称,
专业设置数据.zyssyxbdwh as 专业所属院系部单位号,
专业设置数据.zymc as 专业名称,
专业设置数据.zyh as 专业号,
专业设置数据.sszyqmc as 所属专业群名称,
专业设置数据.sszyqh as 所属专业群号,
专业设置数据.pzszny as 批准设置年月,
专业设置数据.sczsny as 首次招生年月,
专业设置数据.zyccm as 专业层次码,
专业设置数据.xzm as 学制码,
专业设置数据.syxwlbm as 授予学位类别码,
专业设置数据.sfzdzy as 是否重点专业,
专业设置数据.zdzyjbm as 重点专业级别码,
专业设置数据.zdzylxm as 重点专业类型码,
专业设置数据.sjcjsj as 数据采集时间
from abd_sch_zyszsj 专业设置数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_zyszsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_sxjdsj', 'abd_sch_sxjdsj', '实训基地数据', TO_CLOB('
select
实训基地数据.zjsjwyxbs as 主键数据唯一性标识,
实训基地数据.xxdm as 学校代码,
实训基地数据.sxjdbh as 实训基地编号,
实训基地数据.sxjdmc as 实训基地名称,
实训基地数据.ssgldwh as 所属管理单位号,
实训基地数据.sxjdjlrq as 实训基地建立日期,
实训基地数据.ytdw as 依托单位,
实训基地数据.syzymc as 适应专业名称,
实训基地数据.jdhzqy as 基地合作企业,
实训基地数据.sxjdlbm as 实训基地类别码,
实训基地数据.sjcjsj as 数据采集时间
from abd_sch_sxjdsj 实训基地数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_sxjdsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_sxsjbsj', 'abd_sch_sxsjbsj', '实训室基本数据', TO_CLOB('
select
实训室基本数据.zjsjwyxbs as 主键数据唯一性标识,
实训室基本数据.xxdm as 学校代码,
实训室基本数据.sxsh as 实训室号,
实训室基本数据.sxsmc as 实训室名称,
实训室基本数据.ssgldwh as 所属管理单位号,
实训室基本数据.ssgldwmc as 所属管理单位名称,
实训室基本数据.sssxjdbh as 所属实训基地编号,
实训室基本数据.sxsjlrq as 实训室建立日期,
实训室基本数据.fzrjgh as 负责人教工号,
实训室基本数据.gws as 工位数,
实训室基本数据.fjh as 房间号,
实训室基本数据.fjmc as 房间名称,
实训室基本数据.syshzqy as 实验室合作企业,
实训室基本数据.sjcjsj as 数据采集时间
from abd_sch_sxsjbsj 实训室基本数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_sxsjbsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_sxxmsj', 'abd_sch_sxxmsj', '实训项目数据', TO_CLOB('
select
实训项目数据.zjsjwyxbs as 主键数据唯一性标识,
实训项目数据.xxdm as 学校代码,
实训项目数据.sxxmbh as 实训项目编号,
实训项目数据.sxxmmc as 实训项目名称,
实训项目数据.sfxnfzsxxm as 是否虚拟仿真实训项目,
实训项目数据.gkzyh as 归口专业号,
实训项目数据.gkzymc as 归口专业名称,
实训项目数据.ssjhkch as 所属计划课程号,
实训项目数据.ptsxzymc as 配套实训资源名称,
实训项目数据.dxrwmc as 典型任务名称,
实训项目数据.jnyq as 技能要求,
实训项目数据.khfs as 考核方式,
实训项目数据.mksl as 模块数量,
实训项目数据.xssxkss as 学生实训课时数,
实训项目数据.sfdwfw as 是否对外服务,
实训项目数据.xn as 学年度,
实训项目数据.xq as 学期码,
实训项目数据.qtsxdd as 其他实训地点,
实训项目数据.xnsxsh as 校内实训室号,
实训项目数据.sjcjsj as 数据采集时间
from abd_sch_sxxmsj 实训项目数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_sxxmsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_sxjxgcsj', 'abd_sch_sxjxgcsj', '实训教学过程数据', TO_CLOB('
select
实训教学过程数据.zjsjwyxbs as 主键数据唯一性标识,
实训教学过程数据.xxdm as 学校代码,
实训教学过程数据.sxxmbh as 实训项目编号,
实训教学过程数据.sszyh as 所属专业号,
实训教学过程数据.xh as 学号,
实训教学过程数据.xsxm as 学生姓名,
实训教学过程数据.xssxkss as 学生实训课时数,
实训教学过程数据.sxksrq as 实训开始日期,
实训教学过程数据.sxjsrq as 实训结束日期,
实训教学过程数据.sxjgzhpj as 实训结果综合评价,
实训教学过程数据.sjcjsj as 数据采集时间
from abd_sch_sxjxgcsj 实训教学过程数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_sxjxgcsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xnfzjddwfwsj', 'abd_sch_xnfzjddwfwsj', '虚拟仿真基地对外服务数据', TO_CLOB('
select
虚拟仿真基地对外服务数据.zjsjwyxbs as 主键数据唯一性标识,
虚拟仿真基地对外服务数据.xxdm as 学校代码,
虚拟仿真基地对外服务数据.dwfwbh as 对外服务编号,
虚拟仿真基地对外服务数据.dwfwlxm as 对外服务类型码,
虚拟仿真基地对外服务数据.xnfzsxxmbh as 虚拟仿真实训项目编号,
虚拟仿真基地对外服务数据.dwfwrc as 对外服务人次,
虚拟仿真基地对外服务数据.dwfwdx as 对外服务对象,
虚拟仿真基地对外服务数据.fwkss as 服务课时数,
虚拟仿真基地对外服务数据.fwrq as 服务日期,
虚拟仿真基地对外服务数据.fwdwmc as 服务单位名称,
虚拟仿真基地对外服务数据.sjcjsj as 数据采集时间
from abd_sch_xnfzjddwfwsj 虚拟仿真基地对外服务数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xnfzjddwfwsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_pksj', 'abd_sch_pksj', '排课数据', TO_CLOB('
select
排课数据.zjsjwyxbs as 主键数据唯一性标识,
排课数据.xxdm as 学校代码,
排课数据.jhkch as 计划课程号,
排课数据.pkh as 排课号,
排课数据.kch as 课程号,
排课数据.kcmc as 课程名称,
排课数据.zyssyxbmc as 专业所属院系部名称,
排课数据.zymc as 专业名称,
排课数据.zyh as 专业号,
排课数据.nj as 年级,
排课数据.bh as 班号,
排课数据.bjmc as 班级名称,
排课数据.kkxnd as 开课学年度,
排课数据.kkxqm as 开课学期码,
排课数据.zc as 周次,
排课数据.xqj as 星期几,
排课数据.skjc as 上课节次,
排课数据.skrq as 上课日期,
排课数据.sksj as 上课时间,
排课数据.jgh as 教工号,
排课数据.jxbrs as 教学班人数,
排课数据.skdd as 上课地点,
排课数据.jsh as 教室号,
排课数据.skddlbm as 授课地点类别码,
排课数据.xyjccbh as 选用教材出版号,
排课数据.sjcjsj as 数据采集时间
from abd_sch_pksj 排课数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_pksj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_fwjxzyqksj', 'abd_sch_fwjxzyqksj', '访问教学资源情况数据', TO_CLOB('
select
访问教学资源情况数据.zjsjwyxbs as 主键数据唯一性标识,
访问教学资源情况数据.xxdm as 学校代码,
访问教学资源情况数据.fwrybh as 访问人员编号,
访问教学资源情况数据.fwryxm as 访问人员姓名,
访问教学资源情况数据.jxzybh as 教学资源编号,
访问教学资源情况数据.jxzymc as 教学资源名称,
访问教学资源情况数据.fwjxzykssj as 访问教学资源开始时间,
访问教学资源情况数据.fwjxzyjssj as 访问教学资源结束时间,
访问教学资源情况数据.sjcjsj as 数据采集时间
from abd_sch_fwjxzyqksj 访问教学资源情况数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_fwjxzyqksj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_wlkczxxxjlsj', 'abd_sch_wlkczxxxjlsj', '网络课程在线学习记录数据', TO_CLOB('
select
网络课程在线学习记录数据.zjsjwyxbs as 主键数据唯一性标识,
网络课程在线学习记录数据.xxdm as 学校代码,
网络课程在线学习记录数据.wlkcbh as 网络课程编号,
网络课程在线学习记录数据.wlkcmc as 网络课程名称,
网络课程在线学习记录数据.wlkclym as 网络课程来源码,
网络课程在线学习记录数据.wlkcssptmc as 网络课程所属平台名称,
网络课程在线学习记录数据.zjjsgh as 主讲教师工号,
网络课程在线学习记录数据.zjjsxm as 主讲教师姓名,
网络课程在线学习记录数据.xsxxrq as 学生学习日期,
网络课程在线学习记录数据.xsxxsc as 学生学习时长,
网络课程在线学习记录数据.xsxxrc as 学生学习人次,
网络课程在线学习记录数据.xstjzys as 学生提交作业数,
网络课程在线学习记录数据.dytls as 答疑讨论数,
网络课程在线学习记录数据.lrrq as 录入日期,
网络课程在线学习记录数据.sjcjsj as 数据采集时间
from abd_sch_wlkczxxxjlsj 网络课程在线学习记录数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_wlkczxxxjlsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_kthdsj', 'abd_sch_kthdsj', '课堂互动数据', TO_CLOB('
select
课堂互动数据.zjsjwyxbs as 主键数据唯一性标识,
课堂互动数据.xxdm as 学校代码,
课堂互动数据.pkh as 排课号,
课堂互动数据.kch as 课程号,
课堂互动数据.kcmc as 课程名称,
课堂互动数据.zc as 周次,
课堂互动数据.xqj as 星期几,
课堂互动数据.skjc as 上课节次,
课堂互动数据.skrq as 上课日期,
课堂互动数据.sksj as 上课时间,
课堂互动数据.kthdfqzcs as 课堂互动发起总次数,
课堂互动数据.xscyzcs as 学生参与总次数,
课堂互动数据.xscyl as 学生参与率,
课堂互动数据.cql as 出勤率,
课堂互动数据.sjcjsj as 数据采集时间
from abd_sch_kthdsj 课堂互动数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_kthdsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_fwsztsgzysj', 'abd_sch_fwsztsgzysj', '访问数字图书馆资源数据', TO_CLOB('
select
访问数字图书馆资源数据.zjsjwyxbs as 主键数据唯一性标识,
访问数字图书馆资源数据.xxdm as 学校代码,
访问数字图书馆资源数据.dzqkfwcs as 电子期刊访问次数,
访问数字图书馆资源数据.dztsfwcs as 电子图书访问次数,
访问数字图书馆资源数据.fwrq as 访问日期,
访问数字图书馆资源数据.sjcjsj as 数据采集时间
from abd_sch_fwsztsgzysj 访问数字图书馆资源数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_fwsztsgzysj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_jxzlypjsj', 'abd_sch_jxzlypjsj', '教学质量与评价数据', TO_CLOB('
select
教学质量与评价数据.zjsjwyxbs as 主键数据唯一性标识,
教学质量与评价数据.xxdm as 学校代码,
教学质量与评价数据.xn as 学年度,
教学质量与评价数据.xqm as 学期码,
教学质量与评价数据.jhkch as 计划课程号,
教学质量与评价数据.kch as 课程号,
教学质量与评价数据.kcmc as 课程名称,
教学质量与评价数据.gh as 工号,
教学质量与评价数据.kczlpj as 课程质量评价,
教学质量与评价数据.cpxss as 参评学生数,
教学质量与评价数据.pjwcrq as 评价完成日期,
教学质量与评价数据.sjcjsj as 数据采集时间
from abd_sch_jxzlypjsj 教学质量与评价数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_jxzlypjsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xksj', 'abd_sch_xksj', '巡课数据', TO_CLOB('
select
巡课数据.zjsjwyxbs as 主键数据唯一性标识,
巡课数据.xxdm as 学校代码,
巡课数据.pkh as 排课号,
巡课数据.zc as 周次,
巡课数据.xqj as 星期几,
巡课数据.skjc as 上课节次,
巡课数据.ydxsrs as 应到学生人数,
巡课数据.sdxsrs as 实到学生人数,
巡课数据.jsdkqkm as 教师到课情况码,
巡课数据.kthdqkm as 课堂互动情况码,
巡课数据.xkr as 巡课人,
巡课数据.jxtjm as 教学条件码,
巡课数据.ycsm as 异常说明,
巡课数据.xksj as 巡课时间,
巡课数据.sjcjsj as 数据采集时间
from abd_sch_xksj 巡课数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xksj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_szzyjbsj', 'abd_sch_szzyjbsj', '数字资源基本数据', TO_CLOB('
select
数字资源基本数据.zjsjwyxbs as 主键数据唯一性标识,
数字资源基本数据.xxdm as 学校代码,
数字资源基本数据.zybh as 资源编号,
数字资源基本数据.zymc as 资源名称,
数字资源基本数据.sszyh as 所属专业号,
数字资源基本数据.zyfzrh as 资源负责人号,
数字资源基本数据.zyjbm as 资源级别码,
数字资源基本数据.zylxm as 资源类型码,
数字资源基本数据.gjzhjxptzylxm as 国家智慧教学平台资源类型码,
数字资源基本数据.zycjsj as 资源创建时间,
数字资源基本数据.zygxsj as 资源更新时间,
数字资源基本数据.glkch as 关联课程号,
数字资源基本数据.sjcjsj as 数据采集时间
from abd_sch_szzyjbsj 数字资源基本数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_szzyjbsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xnfzszzyjbsj', 'abd_sch_xnfzszzyjbsj', '虚拟仿真数字资源基本数据', TO_CLOB('
select
虚拟仿真数字资源基本数据.zjsjwyxbs as 主键数据唯一性标识,
虚拟仿真数字资源基本数据.xxdm as 学校代码,
虚拟仿真数字资源基本数据.zybh as 资源编号,
虚拟仿真数字资源基本数据.zymc as 资源名称,
虚拟仿真数字资源基本数据.sszyh as 所属专业号,
虚拟仿真数字资源基本数据.zyfzrh as 资源负责人号,
虚拟仿真数字资源基本数据.zyjbm as 资源级别码,
虚拟仿真数字资源基本数据.zylxm as 虚拟仿真资源类型码,
虚拟仿真数字资源基本数据.zycjsj as 资源创建时间,
虚拟仿真数字资源基本数据.zygxsj as 资源更新时间,
虚拟仿真数字资源基本数据.glkch as 关联课程号,
虚拟仿真数字资源基本数据.sssxjdbh as 所属实训基地编号,
虚拟仿真数字资源基本数据.sjcjsj as 数据采集时间
from abd_sch_xnfzszzyjbsj 虚拟仿真数字资源基本数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xnfzszzyjbsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_cxhzsj', 'abd_sch_cxhzsj', '产学合作数据', TO_CLOB('
select
产学合作数据.zjsjwyxbs as 主键数据唯一性标识,
产学合作数据.xxdm as 学校代码,
产学合作数据.zyh as 专业号,
产学合作数据.hzqymc as 合作企业名称,
产学合作数据.hzksny as 合作开始年月,
产学合作数据.ddpyrs as 订单培养人数,
产学合作数据.gtkfkcms as 共同开发课程门数,
产学合作数据.gtkfjcs as 共同开发教材数,
产学合作数据.zcxxjzjss as 支持学校兼职教师数,
产学合作数据.jsdgsxxss as 接受顶岗实习学生数,
产学合作数据.jsbysjys as 接受毕业生就业数,
产学合作数据.xxwqyjsfwnsr as 学校为企业技术服务年收入,
产学合作数据.sjcjsj as 数据采集时间
from abd_sch_cxhzsj 产学合作数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_cxhzsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_gzxzssj', 'abd_sch_gzxzssj', '1+X 证书数据', TO_CLOB('
select
1X证书数据.zjsjwyxbs as 主键数据唯一性标识,
1X证书数据.xxdm as 学校代码,
1X证书数据.zsh as 证书号,
1X证书数据.zyh as 专业号,
1X证书数据.zsbfdwmc as 证书颁发单位名称,
1X证书数据.zsmc as 证书名称,
1X证书数据.kzrq as 考证日期,
1X证书数据.cyrs as 参与人数,
1X证书数据.qzrs as 取证人数,
1X证书数据.sjcjsj as 数据采集时间
from abd_sch_gzxzssj 1X证书数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_gzxzssj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_zyjnjdjgsj', 'abd_sch_zyjnjdjgsj', '职业技能鉴定机构数据', TO_CLOB('
select
职业技能鉴定机构数据.zjsjwyxbs as 主键数据唯一性标识,
职业技能鉴定机构数据.xxdm as 学校代码,
职业技能鉴定机构数据.jdjgmc as 鉴定机构名称,
职业技能鉴定机构数据.jdzsmc as 鉴定证书名称,
职业技能鉴定机构数据.jddjm as 鉴定等级码,
职业技能鉴定机构数据.jldwjbm as 建立单位级别码,
职业技能鉴定机构数据.jldwbmm as 建立单位部门码,
职业技能鉴定机构数据.jdrs as 鉴定人数,
职业技能鉴定机构数据.sjcjsj as 数据采集时间
from abd_sch_zyjnjdjgsj 职业技能鉴定机构数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_zyjnjdjgsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_ztjhsj', 'abd_sch_ztjhsj', '总体计划数据', TO_CLOB('
select
总体计划数据.zjsjwyxbs as 主键数据唯一性标识,
总体计划数据.xxdm as 学校代码,
总体计划数据.jhnj as 计划年级,
总体计划数据.zyh as 专业号,
总体计划数据.zxfyq as 总学分要求,
总体计划数据.zxsyq as 总学时要求,
总体计划数据.jlny as 建立年月,
总体计划数据.pymb as 培养目标,
总体计划数据.yqdzsmc as 应取得证书名称,
总体计划数据.bxxf as 必修学分,
总体计划数据.bxxs as 必修学时,
总体计划数据.xxxf as 限选学分,
总体计划数据.xxxs as 限选学时,
总体计划数据.sjcjsj as 数据采集时间
from abd_sch_ztjhsj 总体计划数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_ztjhsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_jhkcsj', 'abd_sch_jhkcsj', '计划课程数据', TO_CLOB('
select
计划课程数据.zjsjwyxbs as 主键数据唯一性标识,
计划课程数据.xxdm as 学校代码,
计划课程数据.jhkch as 计划课程号,
计划课程数据.kch as 课程号,
计划课程数据.kkxnd as 开课学年度,
计划课程数据.kkxqm as 开课学期码,
计划课程数据.ksxsm as 考试形式码,
计划课程数据.sfzyhxkc as 是否专业核心课程,
计划课程数据.sfkzrtkc as 是否课证融通课程,
计划课程数据.ktjxsjzyskfsm as 课堂教学设计主要授课方式码,
计划课程数据.xyjccbh as 选用教材出版号,
计划课程数据.zsh as 证书号,
计划课程数据.zjjsgh as 主讲教师工号,
计划课程数据.xgjsmc as 相关竞赛名称,
计划课程数据.sjcjsj as 数据采集时间
from abd_sch_jhkcsj 计划课程数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_jhkcsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_jcjbsj', 'abd_sch_jcjbsj', '教材基本数据', TO_CLOB('
select
教材基本数据.zjsjwyxbs as 主键数据唯一性标识,
教材基本数据.xxdm as 学校代码,
教材基本数据.cbh as 出版号,
教材基本数据.jcmc as 教材名称,
教材基本数据.jclxm as 教材类型码,
教材基本数据.bc as 版次,
教材基本数据.cbs as 出版社,
教材基本数据.bzzzs as 编著者总数,
教材基本数据.cbrq as 出版日期,
教材基本数据.zz as 作者,
教材基本数据.flh as 分类号,
教材基本数据.dj as 定价,
教材基本数据.sfgjghjc as 是否国家规划教材,
教材基本数据.ghjcpc as 规划教材批次,
教材基本数据.sfxqhzkfjc as 是否校企合作开发教材,
教材基本数据.sjcjsj as 数据采集时间
from abd_sch_jcjbsj 教材基本数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_jcjbsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_hjjcsj', 'abd_sch_hjjcsj', '获奖教材数据', TO_CLOB('
select
获奖教材数据.zjsjwyxbs as 主键数据唯一性标识,
获奖教材数据.xxdm as 学校代码,
获奖教材数据.hjjcbh as 获奖教材编号,
获奖教材数据.hjxm as 获奖项目,
获奖教材数据.hjjc as 获奖届次,
获奖教材数据.hjrq as 获奖日期,
获奖教材数据.jlmc as 奖励名称,
获奖教材数据.jljbm as 奖励级别码,
获奖教材数据.jldjm as 奖励等级码,
获奖教材数据.bjdw as 颁奖单位,
获奖教材数据.cbh as 出版号,
获奖教材数据.sjcjsj as 数据采集时间
from abd_sch_hjjcsj 获奖教材数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_hjjcsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_jsjbsj', 'abd_sch_jsjbsj', '教室基本数据', TO_CLOB('
select
教室基本数据.zjsjwyxbs as 主键数据唯一性标识,
教室基本数据.xxdm as 学校代码,
教室基本数据.jsh as 教室号,
教室基本数据.jsmc as 教室名称,
教室基本数据.zws as 座位数,
教室基本数据.jslxm as 教室类型码,
教室基本数据.jsms as 教室描述,
教室基本数据.sjcjsj as 数据采集时间
from abd_sch_jsjbsj 教室基本数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_jsjbsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_jssplsj', 'abd_sch_jssplsj', '教室视频流数据', TO_CLOB('
select
教室视频流数据.zjsjwyxbs as 主键数据唯一性标识,
教室视频流数据.xxdm as 学校代码,
教室视频流数据.jsh as 教室号,
教室视频流数据.spsbid as 视频设备ID,
教室视频流数据.spsbdwmc as 视频设备点位名称,
教室视频流数据.spldz as 视频流地址,
教室视频流数据.sjcjsj as 数据采集时间
from abd_sch_jssplsj 教室视频流数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_jssplsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_stxhjbsj', 'abd_sch_stxhjbsj', '社团（协会）基本数据', TO_CLOB('
select
社团协会基本数据.zjsjwyxbs as 主键数据唯一性标识,
社团协会基本数据.xxdm as 学校代码,
社团协会基本数据.stbh as 社团编号,
社团协会基本数据.stmc as 社团名称,
社团协会基本数据.stlxm as 社团类型码,
社团协会基本数据.stjj as 社团简介,
社团协会基本数据.clrq as 成立日期,
社团协会基本数据.gkdwh as 挂靠单位号,
社团协会基本数据.gkdwmc as 挂靠单位名称,
社团协会基本数据.stfzrh as 社团负责人号,
社团协会基本数据.stfzrxm as 社团负责人姓名,
社团协会基本数据.zdjsh as 指导教师号,
社团协会基本数据.zdjsxm as 指导教师姓名,
社团协会基本数据.jsrq as 解散日期,
社团协会基本数据.sjcjsj as 数据采集时间
from abd_sch_stxhjbsj 社团协会基本数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_stxhjbsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_jbsthdsj', 'abd_sch_jbsthdsj', '举办社团活动数据', TO_CLOB('
select
举办社团活动数据.zjsjwyxbs as 主键数据唯一性标识,
举办社团活动数据.xxdm as 学校代码,
举办社团活动数据.sthdbh as 社团活动编号,
举办社团活动数据.sthdmc as 社团活动名称,
举办社团活动数据.stbh as 社团编号,
举办社团活动数据.stmc as 社团名称,
举办社团活动数据.hdfbsj as 活动发布时间,
举办社团活动数据.hdkssj as 活动开始时间,
举办社团活动数据.hdjssj as 活动结束时间,
举办社团活动数据.hdlqfsm as 活动录取方式码,
举办社团活动数据.hdzdjsgh as 活动指导教师工号,
举办社团活动数据.hdzdjsxm as 活动指导教师姓名,
举办社团活动数据.hdlxm as 活动类型码,
举办社团活动数据.hdjbm as 活动级别码,
举办社团活动数据.zdbmrs as 最大报名人数,
举办社团活动数据.hdbmrs as 活动报名人数,
举办社团活动数据.hdcyfsm as 活动参与方式码,
举办社团活动数据.sjcjsj as 数据采集时间
from abd_sch_jbsthdsj 举办社团活动数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_jbsthdsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_dzzqkjcsj', 'abd_sch_dzzqkjcsj', '党组织情况基础数据', TO_CLOB('
select
党组织情况基础数据.zjsjwyxbs as 主键数据唯一性标识,
党组织情况基础数据.xxdm as 学校代码,
党组织情况基础数据.dzzlxm as 党组织类型码,
党组织情况基础数据.dzzmc as 党组织名称,
党组织情况基础数据.dzzbh as 党组织编号,
党组织情况基础数据.lssjdzzmc as 隶属上级党组织名称,
党组织情况基础数据.dnldxm as 党内领导姓名,
党组织情况基础数据.dnldjgh as 党内领导教工号,
党组织情况基础数据.dnldzwm as 党内领导职务码,
党组织情况基础数据.dzzdyrs as 党组织党员人数,
党组织情况基础数据.sjcjsj as 数据采集时间
from abd_sch_dzzqkjcsj 党组织情况基础数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_dzzqkjcsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_dyfzsj', 'abd_sch_dyfzsj', '党员发展数据', TO_CLOB('
select
党员发展数据.zjsjwyxbs as 主键数据唯一性标识,
党员发展数据.xxdm as 学校代码,
党员发展数据.dzzlxm as 党组织类型码,
党员发展数据.dzzmc as 党组织名称,
党员发展数据.dzzbh as 党组织编号,
党员发展数据.xdylxm as 新党员类型码,
党员发展数据.xm as 姓名,
党员发展数据.rybh as 人员编号,
党员发展数据.dyfzztm as 党员发展状态码,
党员发展数据.cwjjfzrq as 成为积极分子日期,
党员发展数据.cwybdyrq as 成为预备党员日期,
党员发展数据.cwzsdyrq as 成为正式党员日期,
党员发展数据.sjcjsj as 数据采集时间
from abd_sch_dyfzsj 党员发展数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_dyfzsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_dygbxxsj', 'abd_sch_dygbxxsj', '党员干部学习数据', TO_CLOB('
select
党员干部学习数据.zjsjwyxbs as 主键数据唯一性标识,
党员干部学习数据.xxdm as 学校代码,
党员干部学习数据.dzzmc as 党组织名称,
党员干部学习数据.dzzbh as 党组织编号,
党员干部学习数据.dygbxxpxzytjhztm as 党员干部学习培训主要途径和载体码,
党员干部学习数据.dygbxxpxnrm as 党员干部学习培训内容码,
党员干部学习数据.hdksrq as 活动开始日期,
党员干部学习数据.hdjsrq as 活动结束日期,
党员干部学习数据.cyxss as 参与学生数,
党员干部学习数据.cyjss as 参与教师数,
党员干部学习数据.sjcjsj as 数据采集时间
from abd_sch_dygbxxsj 党员干部学习数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_dygbxxsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_dksj', 'abd_sch_dksj', '党课数据', TO_CLOB('
select
党课数据.zjsjwyxbs as 主键数据唯一性标识,
党课数据.xxdm as 学校代码,
党课数据.dzzmc as 党组织名称,
党课数据.dzzbh as 党组织编号,
党课数据.hdxsm as 活动形式码,
党课数据.dkzjr as 党课主讲人,
党课数据.hdnr as 活动内容,
党课数据.hdksrq as 活动开始日期,
党课数据.hdjsrq as 活动结束日期,
党课数据.cyxss as 参与学生数,
党课数据.cyjss as 参与教师数,
党课数据.sjcjsj as 数据采集时间
from abd_sch_dksj 党课数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_dksj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_dyrchdsj', 'abd_sch_dyrchdsj', '党员日常活动数据', TO_CLOB('
select
党员日常活动数据.zjsjwyxbs as 主键数据唯一性标识,
党员日常活动数据.xxdm as 学校代码,
党员日常活动数据.dzzmc as 党组织名称,
党员日常活动数据.dzzbh as 党组织编号,
党员日常活动数据.hddd as 活动地点,
党员日常活动数据.hdnr as 活动内容,
党员日常活动数据.hdksrq as 活动开始日期,
党员日常活动数据.hdjsrq as 活动结束日期,
党员日常活动数据.cyxss as 参与学生数,
党员日常活动数据.cyjss as 参与教师数,
党员日常活动数据.sjcjsj as 数据采集时间
from abd_sch_dyrchdsj 党员日常活动数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_dyrchdsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_shsj', 'abd_sch_shsj', '三会数据', TO_CLOB('
select
三会数据.zjsjwyxbs as 主键数据唯一性标识,
三会数据.xxdm as 学校代码,
三会数据.dzzmc as 党组织名称,
三会数据.dzzbh as 党组织编号,
三会数据.hddd as 活动地点,
三会数据.hdnr as 活动内容,
三会数据.hdksrq as 活动开始日期,
三会数据.hdjsrq as 活动结束日期,
三会数据.cyxss as 参与学生数,
三会数据.cyjss as 参与教师数,
三会数据.sjcjsj as 数据采集时间
from abd_sch_shsj 三会数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_shsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_dyztdrsj', 'abd_sch_dyztdrsj', '党员主题党日数据', TO_CLOB('
select
党员主题党日数据.zjsjwyxbs as 主键数据唯一性标识,
党员主题党日数据.xxdm as 学校代码,
党员主题党日数据.dzzmc as 党组织名称,
党员主题党日数据.dzzbh as 党组织编号,
党员主题党日数据.hddd as 活动地点,
党员主题党日数据.hdnrm as 活动内容码,
党员主题党日数据.hdksrq as 活动开始日期,
党员主题党日数据.hdjsrq as 活动结束日期,
党员主题党日数据.cyxss as 参与学生数,
党员主题党日数据.cyjss as 参与教师数,
党员主题党日数据.sjcjsj as 数据采集时间
from abd_sch_dyztdrsj 党员主题党日数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_dyztdrsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_szhdsj', 'abd_sch_szhdsj', '思政活动数据', TO_CLOB('
select
思政活动数据.zjsjwyxbs as 主键数据唯一性标识,
思政活动数据.xxdm as 学校代码,
思政活动数据.hdbh as 活动编号,
思政活动数据.hdmc as 活动名称,
思政活动数据.sszt as 所属专题,
思政活动数据.hdbk as 活动板块,
思政活动数据.hdzt as 活动主题,
思政活动数据.hdlxm as 活动类型码,
思政活动数据.hdnr as 活动内容,
思政活动数据.zbdw as 主办单位,
思政活动数据.zbdwjbm as 主办单位级别码,
思政活动数据.hdksrq as 活动开始日期,
思政活动数据.hdjsrq as 活动结束日期,
思政活动数据.xxfzr as 学校负责人,
思政活动数据.fzrlxfs as 负责人联系方式,
思政活动数据.bxjscyrs as 本校教师参与人数,
思政活动数据.wxjscyrs as 外校教师参与人数,
思政活动数据.bxxscyrs as 本校学生参与人数,
思政活动数据.xwxscyrs as 校外学生参与人数,
思政活动数据.sjcjsj as 数据采集时间
from abd_sch_szhdsj 思政活动数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_szhdsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_szjzgsj', 'abd_sch_szjzgsj', '思政教职工数据', TO_CLOB('
select
思政教职工数据.zjsjwyxbs as 主键数据唯一性标识,
思政教职工数据.xxdm as 学校代码,
思政教职工数据.jgh as 教工号,
思政教职工数据.jsxm as 教师姓名,
思政教职工数据.rylxm as 人员类型码,
思政教职工数据.jl as 教龄,
思政教职工数据.zksxzymc as 专科所学专业名称,
思政教职工数据.bksxzymc as 本科所学专业名称,
思政教职工数据.sssxzymc as 硕士所学专业名称,
思政教职工数据.bssxzymc as 博士所学专业名称,
思政教职工数据.bzrfdynxszknx as 班主任辅导员年限思政课年限,
思政教职工数据.sfxljkkjs as 是否心理健康课教师,
思政教职工数据.sfxlzxs as 是否心理咨询师,
思政教职工数据.sfcyxlzxszgzs as 是否持有心理咨询师资格证书,
思政教职工数据.xlzxszgzsbh as 心理咨询师资格证书编号,
思政教职工数据.sjcjsj as 数据采集时间
from abd_sch_szjzgsj 思政教职工数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_szjzgsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_yqsbjbsj', 'abd_sch_yqsbjbsj', '仪器设备基本数据', TO_CLOB('
select
仪器设备基本数据.zjsjwyxbs as 主键数据唯一性标识,
仪器设备基本数据.xxdm as 学校代码,
仪器设备基本数据.yqh as 仪器号,
仪器设备基本数据.yqmc as 仪器名称,
仪器设备基本数据.yqywmc as 仪器英文名称,
仪器设备基本数据.xxdwccm as 学校单位层次码,
仪器设备基本数据.cqm as 产权码,
仪器设备基本数据.syzkm as 使用状况码,
仪器设备基本数据.gxzcflm as 高校资产分类码,
仪器设备基本数据.yqxh as 仪器型号,
仪器设备基本数据.ccrq as 出厂日期,
仪器设备基本数据.scgbdqm as 生产国别地区码,
仪器设备基本数据.jfkmm as 经费科目码,
仪器设备基本数据.gzrq as 购置日期,
仪器设备基本数据.sccj as 生产厂家,
仪器设备基本数据.cch as 出厂号,
仪器设备基本数据.djh as 单据号,
仪器设备基本数据.tp as 图片,
仪器设备基本数据.fjh as 房间号,
仪器设备基本数据.fjmc as 房间名称,
仪器设备基本数据.sbfzrh as 设备负责人号,
仪器设备基本数据.sbfzrxm as 设备负责人姓名,
仪器设备基本数据.jszb as 技术指标,
仪器设备基本数据.yqpz as 仪器配置,
仪器设备基本数据.jgbz as 价格币种,
仪器设备基本数据.yqjg as 仪器价格,
仪器设备基本数据.yqsm as 仪器说明,
仪器设备基本数据.cwzh as 财务账号,
仪器设备基本数据.cwglrxm as 财务管理人姓名,
仪器设备基本数据.ghs as 供货商,
仪器设备基本数据.bxjzrq ')||TO_CLOB('as 保修截止日期,
仪器设备基本数据.sblym as 设备来源码,
仪器设备基本数据.sfjmgzyq as 是否精密贵重仪器,
仪器设备基本数据.zcjcflm as 资产基础分类码,
仪器设备基本数据.sjcjsj as 数据采集时间
from abd_sch_yqsbjbsj 仪器设备基本数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_yqsbjbsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_zhjssbyxsj', 'abd_sch_zhjssbyxsj', '智慧教室设备运行', TO_CLOB('
select
智慧教室设备运行.zjsjwyxbs as 主键数据唯一性标识,
智慧教室设备运行.xxdm as 学校代码,
智慧教室设备运行.sbbm as 设备编码,
智慧教室设备运行.sbmc as 设备名称,
智慧教室设备运行.jsh as 教室号,
智慧教室设备运行.ksyxsj as 开始运行时间,
智慧教室设备运行.jsyxsj as 结束运行时间,
智慧教室设备运行.xtjlsj as 系统记录时间,
智慧教室设备运行.sjcjsj as 数据采集时间
from abd_sch_zhjssbyxsj 智慧教室设备运行'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_zhjssbyxsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_kyxmjbsj', 'abd_sch_kyxmjbsj', '科研项目基本数据', TO_CLOB('
select
科研项目基本数据.zjsjwyxbs as 主键数据唯一性标识,
科研项目基本数据.xxdm as 学校代码,
科研项目基本数据.kyxmbh as 科研项目编号,
科研项目基本数据.kyxmmc as 科研项目名称,
科研项目基本数据.kyxmlbm as 科研项目类别码,
科研项目基本数据.kyxmzt as 科研项目主题,
科研项目基本数据.kyxmnrjj as 科研项目内容简介,
科研项目基本数据.ktrq as 开题日期,
科研项目基本数据.sbrq as 申报日期,
科研项目基本数据.lxrq as 立项日期,
科研项目基本数据.jxrq as 结项日期,
科研项目基本数据.kyxmfzr as 科研项目负责人,
科研项目基本数据.xmlxjf as 项目立项经费,
科研项目基本数据.cyxss as 参与学生数,
科研项目基本数据.cyjss as 参与教师数,
科研项目基本数据.sjcjsj as 数据采集时间
from abd_sch_kyxmjbsj 科研项目基本数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_kyxmjbsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_kyxmyjhdsj', 'abd_sch_kyxmyjhdsj', '科研项目研究活动数据', TO_CLOB('
select
科研项目研究活动数据.zjsjwyxbs as 主键数据唯一性标识,
科研项目研究活动数据.xxdm as 学校代码,
科研项目研究活动数据.kyxmbh as 科研项目编号,
科研项目研究活动数据.kyxmmc as 科研项目名称,
科研项目研究活动数据.kyxmyjhdzt as 科研项目研究活动主题,
科研项目研究活动数据.kyxmyjhdnrjj as 科研项目研究活动内容简介,
科研项目研究活动数据.kyxmyjhdrq as 科研项目研究活动日期,
科研项目研究活动数据.sjcjsj as 数据采集时间
from abd_sch_kyxmyjhdsj 科研项目研究活动数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_kyxmyjhdsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_kyjfzcsj', 'abd_sch_kyjfzcsj', '科研经费支出数据', TO_CLOB('
select
科研经费支出数据.zjsjwyxbs as 主键数据唯一性标识,
科研经费支出数据.xxdm as 学校代码,
科研经费支出数据.kyxmbh as 科研项目编号,
科研经费支出数据.jfzckyxmmc as 经费支出科研项目名称,
科研经费支出数据.jfzckyxmlbm as 经费支出科研项目类别码,
科研经费支出数据.zcje as 支出金额,
科研经费支出数据.zcsj as 支出时间,
科研经费支出数据.zcyt as 支出用途,
科研经费支出数据.sjcjsj as 数据采集时间
from abd_sch_kyjfzcsj 科研经费支出数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_kyjfzcsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_lwfbsj', 'abd_sch_lwfbsj', '论文发表数据', TO_CLOB('
select
论文发表数据.zjsjwyxbs as 主键数据唯一性标识,
论文发表数据.xxdm as 学校代码,
论文发表数据.lwbh as 论文编号,
论文发表数据.lwzwmc as 论文中文名称,
论文发表数据.lwjj as 论文简介,
论文发表数据.fbrq as 发表日期,
论文发表数据.fbkwmc as 发表刊物名称,
论文发表数据.dywcrgh as 第一完成人工号,
论文发表数据.sjcjsj as 数据采集时间
from abd_sch_lwfbsj 论文发表数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_lwfbsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_zlfbsj', 'abd_sch_zlfbsj', '专利发布数据', TO_CLOB('
select
专利发布数据.zjsjwyxbs as 主键数据唯一性标识,
专利发布数据.xxdm as 学校代码,
专利发布数据.fbzlbh as 发布专利编号,
专利发布数据.fbzlmc as 发布专利名称,
专利发布数据.fbzljj as 发布专利简介,
专利发布数据.sqggrq as 授权公告日期,
专利发布数据.dywcrgh as 第一完成人工号,
专利发布数据.sjcjsj as 数据采集时间
from abd_sch_zlfbsj 专利发布数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_zlfbsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_zzfbsj', 'abd_sch_zzfbsj', '专著发表数据', TO_CLOB('
select
专著发表数据.zjsjwyxbs as 主键数据唯一性标识,
专著发表数据.xxdm as 学校代码,
专著发表数据.zzbh as 专著编号,
专著发表数据.zzzwmc as 专著中文名称,
专著发表数据.zzjj as 专著简介,
专著发表数据.cbrq as 出版日期,
专著发表数据.dywcrgh as 第一完成人工号,
专著发表数据.sjcjsj as 数据采集时间
from abd_sch_zzfbsj 专著发表数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_zzfbsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xsjzsj', 'abd_sch_xsjzsj', '学术讲座数据', TO_CLOB('
select
学术讲座数据.zjsjwyxbs as 主键数据唯一性标识,
学术讲座数据.xxdm as 学校代码,
学术讲座数据.xsjzbh as 学术讲座编号,
学术讲座数据.xsjzzt as 学术讲座主题,
学术讲座数据.xsjznrjj as 学术讲座内容简介,
学术讲座数据.xsjzrq as 学术讲座日期,
学术讲座数据.zjrxm as 主讲人姓名,
学术讲座数据.cyxss as 参与学生数,
学术讲座数据.cyjss as 参与教师数,
学术讲座数据.sjcjsj as 数据采集时间
from abd_sch_xsjzsj 学术讲座数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xsjzsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xsxfsj', 'abd_sch_xsxfsj', '学生消费数据', TO_CLOB('
select
学生消费数据.zjsjwyxbs as 主键数据唯一性标识,
学生消费数据.xxdm as 学校代码,
学生消费数据.xfze as 消费总额,
学生消费数据.xfrc as 消费人次,
学生消费数据.xflbm as 消费类别码,
学生消费数据.xfrq as 消费日期,
学生消费数据.sjcjsj as 数据采集时间
from abd_sch_xsxfsj 学生消费数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xsxfsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_yktrzsj', 'abd_sch_yktrzsj', '一卡通认证数据', TO_CLOB('
select
一卡通认证数据.zjsjwyxbs as 主键数据唯一性标识,
一卡通认证数据.xxdm as 学校代码,
一卡通认证数据.cslxm as 场所类型码,
一卡通认证数据.rzlxm as 认证类型码,
一卡通认证数据.rzrq as 认证日期,
一卡通认证数据.rzrc as 认证人次,
一卡通认证数据.sjcjsj as 数据采集时间
from abd_sch_yktrzsj 一卡通认证数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_yktrzsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_zxswblsj', 'abd_sch_zxswblsj', '在线事务办理数据', TO_CLOB('
select
在线事务办理数据.zjsjwyxbs as 主键数据唯一性标识,
在线事务办理数据.xxdm as 学校代码,
在线事务办理数据.bsswmc as 办事事务名称,
在线事务办理数据.bsswlxm as 办事事务类型码,
在线事务办理数据.rylxm as 人员类型码,
在线事务办理数据.sqsj as 申请时间,
在线事务办理数据.swblr as 事务办理人,
在线事务办理数据.blsj as 办理时间,
在线事务办理数据.sjcjsj as 数据采集时间
from abd_sch_zxswblsj 在线事务办理数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_zxswblsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xydtxxsj', 'abd_sch_xydtxxsj', '校园动态信息数据', TO_CLOB('
select
校园动态信息数据.zjsjwyxbs as 主键数据唯一性标识,
校园动态信息数据.xxdm as 学校代码,
校园动态信息数据.xydtxxbt as 校园动态信息标题,
校园动态信息数据.xydtxxwbfwlj as 校园动态信息外部访问链接,
校园动态信息数据.dtfbrq as 动态发布日期,
校园动态信息数据.sjcjsj as 数据采集时间
from abd_sch_xydtxxsj 校园动态信息数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xydtxxsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_tsjbsj', 'abd_sch_tsjbsj', '图书基本数据', TO_CLOB('
select
图书基本数据.zjsjwyxbs as 主键数据唯一性标识,
图书基本数据.xxdm as 学校代码,
图书基本数据.tsbh as 图书编号,
图书基本数据.tstxm as 图书条形码,
图书基本数据.zbt as 正标题,
图书基本数据.blbt as 并列标题,
图书基本数据.fbt as 副标题,
图书基本数据.jsgjz as 检索关键字,
图书基本数据.tscbh as 图书出版号,
图书基本数据.dyzz as 第一作者,
图书基本数据.qtzz as 其他作者,
图书基本数据.jg as 价格,
图书基本数据.wxlxm as 文献类型码,
图书基本数据.zdm as 装订码,
图书基本数据.flh as 分类号,
图书基本数据.zgyzm as 中国语种码,
图书基本数据.yzm as 语种码,
图书基本数据.kb as 开本,
图书基本数据.ys as 页数,
图书基本数据.bc as 版次,
图书基本数据.tsfjmc as 图书附件名称,
图书基本数据.csmc as 丛书名称,
图书基本数据.csbz as 丛书编者,
图书基本数据.cbs as 出版社,
图书基本数据.cbsjbm as 出版社级别码,
图书基本数据.cbd as 出版地,
图书基本数据.cbrq as 出版日期,
图书基本数据.fxdw as 发行单位,
图书基本数据.bz as 备注,
图书基本数据.tsztm as 图书状态码,
图书基本数据.sjcjsj as 数据采集时间
from abd_sch_tsjbsj 图书基本数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_tsjbsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_tsjysj', 'abd_sch_tsjysj', '图书借阅数据', TO_CLOB('
select
图书借阅数据.zjsjwyxbs as 主键数据唯一性标识,
图书借阅数据.xxdm as 学校代码,
图书借阅数据.jyzbh as 借阅者编号,
图书借阅数据.jyzxm as 借阅者姓名,
图书借阅数据.jyrylbm as 借阅人员类别码,
图书借阅数据.tsbh as 图书编号,
图书借阅数据.jyrq as 借阅日期,
图书借阅数据.yjghrq as 预计归还日期,
图书借阅数据.sjghrq as 实际归还日期,
图书借阅数据.sjcjsj as 数据采集时间
from abd_sch_tsjysj 图书借阅数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_tsjysj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_qkjbsj', 'abd_sch_qkjbsj', '期刊基本数据', TO_CLOB('
select
期刊基本数据.zjsjwyxbs as 主键数据唯一性标识,
期刊基本数据.xxdm as 学校代码,
期刊基本数据.qkbh as 期刊编号,
期刊基本数据.qktxm as 期刊条形码,
期刊基本数据.qkzwmc as 期刊中文名称,
期刊基本数据.qkywmc as 期刊英文名称,
期刊基本数据.qkcbh as 期刊出版号,
期刊基本数据.bjb as 编辑部,
期刊基本数据.zb as 主编,
期刊基本数据.mqdj as 每期单价,
期刊基本数据.ckny as 创刊年月,
期刊基本数据.nh as 年号,
期刊基本数据.jh as 卷号,
期刊基本数据.qh as 期号,
期刊基本数据.zqh as 总期号,
期刊基本数据.flh as 分类号,
期刊基本数据.zgyzm as 中国语种码,
期刊基本数据.sjcjsj as 数据采集时间
from abd_sch_qkjbsj 期刊基本数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_qkjbsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xnxssshdsj', 'abd_sch_xnxssshdsj', '校内学生赛事活动数据', TO_CLOB('
select
校内学生赛事活动数据.zjsjwyxbs as 主键数据唯一性标识,
校内学生赛事活动数据.xxdm as 学校代码,
校内学生赛事活动数据.sshdbh as 赛事活动编号,
校内学生赛事活动数据.sshdmc as 赛事活动名称,
校内学生赛事活动数据.sshdlxm as 赛事活动类型码,
校内学生赛事活动数据.hdksrq as 活动开始日期,
校内学生赛事活动数据.hdjsrq as 活动结束日期,
校内学生赛事活动数据.cyjss as 参与教师数,
校内学生赛事活动数据.cyxss as 参与学生数,
校内学生赛事活动数据.sjcjsj as 数据采集时间
from abd_sch_xnxssshdsj 校内学生赛事活动数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xnxssshdsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_wlaqglsj', 'abd_sch_wlaqglsj', '网络安全管理数据', TO_CLOB('
select
网络安全管理数据.zjsjwyxbs as 主键数据唯一性标识,
网络安全管理数据.xxdm as 学校代码,
网络安全管理数据.wlsyjls as 网络使用记录数,
网络安全管理数据.wlsyll as 网络使用流量,
网络安全管理数据.wlgjsbcs as 网络攻击识别次数,
网络安全管理数据.wldksysxsl as 网络带宽使用上行速率,
网络安全管理数据.wldksyxxsl as 网络带宽使用下行速率,
网络安全管理数据.jlrq as 记录日期,
网络安全管理数据.sjcjsj as 数据采集时间
from abd_sch_wlaqglsj 网络安全管理数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_wlaqglsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xbsjzxsjjcqksj', 'abd_sch_xbsjzxsjjcqksj', '校本数据中心数据集成情况数据', TO_CLOB('
select
校本数据中心数据集成情况数据.zjsjwyxbs as 主键数据唯一性标识,
校本数据中心数据集成情况数据.xxdm as 学校代码,
校本数据中心数据集成情况数据.sjkmc as 数据库名称,
校本数据中心数据集成情况数据.sjbmc as 数据表名称,
校本数据中心数据集成情况数据.sjbsm as 数据表说明,
校本数据中心数据集成情况数据.sjbgxplm as 数据表更新频率码,
校本数据中心数据集成情况数据.xxhxtbh as 信息化系统编号,
校本数据中心数据集成情况数据.sjhjl as 数据汇聚量,
校本数据中心数据集成情况数据.sjjkfwdyl as 数据接口服务调用量,
校本数据中心数据集成情况数据.sjbgxfsm as 数据表更新方式码,
校本数据中心数据集成情况数据.sjbgxrq as 数据表更新日期,
校本数据中心数据集成情况数据.sjcjsj as 数据采集时间
from abd_sch_xbsjzxsjjcqksj 校本数据中心数据集成情况数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xbsjzxsjjcqksj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_sjbzyxxxtysgxsj', 'abd_sch_sjbzyxxxtysgxsj', '数据标准与信息系统映射关系数据', TO_CLOB('
select
数据标准与信息系统映射关系数据.zjsjwyxbs as 主键数据唯一性标识,
数据标准与信息系统映射关系数据.xxdm as 学校代码,
数据标准与信息系统映射关系数据.sjzlywmc as 数据子类英文名称,
数据标准与信息系统映射关系数据.sjzlzwmc as 数据子类中文名称,
数据标准与信息系统映射关系数据.xxhxtbh as 信息化系统编号,
数据标准与信息系统映射关系数据.xxhxtqc as 信息化系统全称,
数据标准与信息系统映射关系数据.sjcjsj as 数据采集时间
from abd_sch_sjbzyxxxtysgxsj 数据标准与信息系统映射关系数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_sjbzyxxxtysgxsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xxhxtjcqksj', 'abd_sch_xxhxtjcqksj', '信息化系统集成情况数据', TO_CLOB('
select
信息化系统集成情况数据.zjsjwyxbs as 主键数据唯一性标识,
信息化系统集成情况数据.xxdm as 学校代码,
信息化系统集成情况数据.xxhxtbh as 信息化系统编号,
信息化系统集成情况数据.xxhxtjc as 信息化系统简称,
信息化系统集成情况数据.xxhxtqc as 信息化系统全称,
信息化系统集成情况数据.jcfxm as 集成方向码,
信息化系统集成情况数据.jcbb as 集成版本,
信息化系统集成情况数据.jcfsm as 集成方式码,
信息化系统集成情况数据.jkxym as 接口协议码,
信息化系统集成情况数据.jkbb as 接口版本,
信息化系统集成情况数据.rzfsm as 认证方式码,
信息化系统集成情况数据.jcwcrq as 集成完成日期,
信息化系统集成情况数据.sjcjsj as 数据采集时间
from abd_sch_xxhxtjcqksj 信息化系统集成情况数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xxhxtjcqksj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_tyyhrzdlsj', 'abd_sch_tyyhrzdlsj', '统一用户认证登录数据', TO_CLOB('
select
统一用户认证登录数据.zjsjwyxbs as 主键数据唯一性标识,
统一用户认证登录数据.xxdm as 学校代码,
统一用户认证登录数据.dlzh as 登录账号,
统一用户认证登录数据.js as 角色,
统一用户认证登录数据.szbmmc as 所在部门名称,
统一用户认证登录数据.dlsj as 登录时间,
统一用户认证登录数据.dlip as 登录IP,
统一用户认证登录数据.zhztm as 账号状态码,
统一用户认证登录数据.sjcjsj as 数据采集时间
from abd_sch_tyyhrzdlsj 统一用户认证登录数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_tyyhrzdlsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_sxjlfxhdjl', 'abd_sch_sxjlfxhdjl', '实习交流分享活动记录', TO_CLOB('
select
实习交流分享活动记录.xxbsm as 学校标识码,
实习交流分享活动记录.hdmc as 活动名称,
实习交流分享活动记录.hdjbrq as 活动举办日期,
实习交流分享活动记录.cjxsrs as 参加学生人数
from abd_sch_sxjlfxhdjl 实习交流分享活动记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_sxjlfxhdjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xwwzfwjl', 'abd_sch_xwwzfwjl', '校外网站访问记录', TO_CLOB('
select
校外网站访问记录.xh as 学号,
校外网站访问记录.xm as 姓名,
校外网站访问记录.fwsj as 访问时间
from abd_sch_xwwzfwjl 校外网站访问记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xwwzfwjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_sbsjmxqqjl', 'abd_sch_sbsjmxqqjl', '上报数据模型确权记录', TO_CLOB('
select
上报数据模型确权记录.xxbsm as 学校标识码,
上报数据模型确权记录.sjbzw as 数据表中文,
上报数据模型确权记录.zyly as 主要来源,
上报数据模型确权记录.zrdw as 责任单位,
上报数据模型确权记录.qqrq as 确权日期
from abd_sch_sbsjmxqqjl 上报数据模型确权记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_sbsjmxqqjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xxcyxy', 'abd_sch_xxcyxy', '学校产业学院', TO_CLOB('
select
学校产业学院.xxbsm as 学校标识码,
学校产业学院.cyxybm as 产业学院编码,
学校产业学院.cyxymc as 产业学院名称,
学校产业学院.jb as 级别,
学校产业学院.pzbm as 批准部门,
学校产业学院.pzrq as 批准日期,
学校产业学院.bxwc as 本校位次,
学校产业学院.xnssdw as 校内所属单位
from abd_sch_xxcyxy 学校产业学院'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xxcyxy'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xxjxcg', 'abd_sch_xxjxcg', '学校教学成果', TO_CLOB('
select
学校教学成果.xxbsm as 学校标识码,
学校教学成果.jxcgbm as 教学成果编码,
学校教学成果.jxcgmc as 教学成果名称,
学校教学成果.xkml as 学科门类,
学校教学成果.yjxk as 一级学科,
学校教学成果.zcr as 主持人,
学校教学成果.zcdw as 主持单位,
学校教学成果.bxfzrgh as 本校负责人工号,
学校教学成果.bxfzdw as 本校负责单位,
学校教学成果.cgjj as 成果简介,
学校教学成果.cgcxd as 成果创新点,
学校教学成果.xxsm as 学校署名
from abd_sch_xxjxcg 学校教学成果'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xxjxcg'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xxjgxm', 'abd_sch_xxjgxm', '学校教改项目', TO_CLOB('
select
学校教改项目.xxbsm as 学校标识码,
学校教改项目.xmbh as 项目编号,
学校教改项目.xmmc as 项目名称,
学校教改项目.xmjb as 项目级别,
学校教改项目.xmlx as 项目类型,
学校教改项目.qtdw as 牵头单位,
学校教改项目.ssdw as 所属单位,
学校教改项目.bxfzrgh as 本校负责人工号,
学校教改项目.xmfzrxm as 项目负责人姓名,
学校教改项目.lxrq as 立项日期,
学校教改项目.ksrq as 开始日期,
学校教改项目.jxrq as 结项日期,
学校教改项目.xmjf as 项目经费,
学校教改项目.xmzt as 项目状态,
学校教改项目.xmly as 项目来源,
学校教改项目.bxcyqk as 本校参与情况
from abd_sch_xxjgxm 学校教改项目'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xxjgxm'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xxjsjrjzzq', 'abd_sch_xxjsjrjzzq', '学校计算机软件著作权', TO_CLOB('
select
学校计算机软件著作权.xxbsm as 学校标识码,
学校计算机软件著作权.rjzzqbm as 软件著作权编码,
学校计算机软件著作权.kycgbm as 科研成果编码,
学校计算机软件著作权.kycglx as 科研成果类型,
学校计算机软件著作权.rjzzmc as 软件著作名称,
学校计算机软件著作权.zsh as 证书号,
学校计算机软件著作权.djh as 登记号,
学校计算机软件著作权.zzqr as 著作权人,
学校计算机软件著作权.scfbrq as 首次发表日期,
学校计算机软件著作权.zsqdrq as 证书取得日期,
学校计算机软件著作权.zsbfjg as 证书颁发机构,
学校计算机软件著作权.qlqdfs as 权利取得方式
from abd_sch_xxjsjrjzzq 学校计算机软件著作权'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xxjsjrjzzq'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xxkyxm', 'abd_sch_xxkyxm', '学校科研项目', TO_CLOB('
select
学校科研项目.xxbsm as 学校标识码,
学校科研项目.xmbm as 项目编码,
学校科研项目.xmmc as 项目名称,
学校科研项目.xmlb as 项目类别,
学校科研项目.xmlx as 项目类型,
学校科研项目.kjxmlb as 科技项目类别,
学校科研项目.xmjb as 项目级别,
学校科研项目.bmjb as 保密级别,
学校科研项目.sskyjg as 所属科研机构,
学校科研项目.ssdw as 所属单位,
学校科研项目.xmfzr as 项目负责人,
学校科研项目.bwtdw as 被委托单位,
学校科研项目.bwtr as 被委托人,
学校科研项目.xmpzh as 项目批准号,
学校科研项目.xkml as 学科门类,
学校科研项目.yjxk as 一级学科,
学校科研项目.cgxs as 成果形式,
学校科研项目.hdlx as 活动类型,
学校科研项目.kydl as 科研大类,
学校科研项目.lxrq as 立项日期,
学校科研项目.ksrq as 开始日期,
学校科研项目.jhwcrq as 计划完成日期,
学校科研项目.jxrq as 结项日期,
学校科研项目.xmly as 项目来源,
学校科研项目.xmlydw as 项目来源单位,
学校科研项目.hzgj as 合作国家,
学校科研项目.hzxs as 合作形式,
学校科研项目.ssxy as 所属行业,
学校科研项目.shjjxy as 社会经济效益,
学校科研项目.zgbm as 主管部门,
学校科研项目.xmzxzt as 项目执行状态,
学校科研项目.xxsm as 学校署名
from abd_sch_xxkyxm 学校科研项目'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xxkyxm'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xxzscq', 'abd_sch_xxzscq', '学校知识产权', TO_CLOB('
select
学校知识产权.xxbsm as 学校标识码,
学校知识产权.zscqbm as 知识产权编码,
学校知识产权.kycg as 科研成果,
学校知识产权.zscqmc as 知识产权名称,
学校知识产权.zscqxs as 知识产权形式,
学校知识产权.qdrq as 取得日期
from abd_sch_xxzscq 学校知识产权'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xxzscq'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xxzl', 'abd_sch_xxzl', '学校专利', TO_CLOB('
select
学校专利.xxbsm as 学校标识码,
学校专利.zlbm as 专利编码,
学校专利.zlmc as 专利名称,
学校专利.kycg as 科研成果,
学校专利.kycglx as 科研成果类型,
学校专利.zlsqbh as 专利申请编号,
学校专利.zllx as 专利类型,
学校专利.zlzt as 专利状态,
学校专利.sqr as 申请人,
学校专利.sqrq as 申请日期,
学校专利.dyfmr as 第一发明人,
学校专利.zlqr as 专利权人,
学校专利.zlsqggh as 专利授权公告号,
学校专利.zlsqggrq as 专利授权公告日期,
学校专利.sfbxzl as 是否本校专利,
学校专利.sfpzl as 是否PCT专利,
学校专利.zlgj as 专利国家,
学校专利.flh as 分类号
from abd_sch_xxzl 学校专利'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xxzl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xxhzxm', 'abd_sch_xxhzxm', '学校合作项目', TO_CLOB('
select
学校合作项目.xxbsm as 学校标识码,
学校合作项目.zwxmm as 中文项目名,
学校合作项目.ywxmm as 英文项目名,
学校合作项目.xmlx as 项目类型,
学校合作项目.sqdw as 申请单位,
学校合作项目.hzdw as 合作单位,
学校合作项目.wffzr as 我方负责人,
学校合作项目.wfllr as 我方联络人,
学校合作项目.xmksrq as 项目开始日期,
学校合作项目.xmjsrq as 项目结束日期
from abd_sch_xxhzxm 学校合作项目'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xxhzxm'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_spe_zyqjbxx', 'abd_spe_zyqjbxx', '专业群基本信息', TO_CLOB('
select
专业群基本信息.zyqbm as 专业群编码,
专业群基本信息.zyqmc as 专业群名称
from abd_spe_zyqjbxx 专业群基本信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_spe_zyqjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgjbxx', 'abd_tea_jzgjbxx', '教职工基本信息', TO_CLOB('
select
教职工基本信息.zgh as 教职工号,
教职工基本信息.xm as 姓名,
教职工基本信息.xb as 性别,
教职工基本信息.mz as 民族,
教职工基本信息.sfzjlx as 身份证件类型,
教职工基本信息.gatqw as 港澳台侨外,
教职工基本信息.zjxy as 宗教信仰,
教职工基本信息.zzmm as 政治面貌,
教职工基本信息.jg as 籍贯,
教职工基本信息.hkszs as 户口所在省,
教职工基本信息.hkszds as 户口所在地市,
教职工基本信息.hkszd as 户口所在地,
教职工基本信息.xx as 血型,
教职工基本信息.csrq as 出生日期,
教职工基本信息.csd as 出生地,
教职工基本信息.gjdq as 国家地区,
教职工基本信息.hyzk as 婚姻状况,
教职工基本信息.zgxl as 最高学历,
教职工基本信息.zgxw as 最高学位,
教职工基本信息.dyxl as 第一学历,
教职工基本信息.dyxlbyyx as 第一学历毕业院校,
教职工基本信息.dyxlbyyxlx as 第一学历毕业院校类型,
教职工基本信息.zgxlbyyx as 最高学历毕业院校,
教职工基本信息.zgxlbyyxlx as 最高学历毕业院校类型,
教职工基本信息.zhbyyx as 最后毕业院校,
教职工基本信息.zhbyyxlx as 最后毕业院校类型,
教职工基本信息.sfbxby as 是否本校毕业,
教职工基本信息.sjhm as 手机号码,
教职工基本信息.dzyx as 电子邮箱,
教职工基本信息.jtdz as 家庭地址,
教职工基本信息.ssjgdm as 所属机构代码,
教职工基本信息.s')||TO_CLOB('sjg as 所属机构,
教职工基本信息.ssxdm as 所属系代码,
教职工基本信息.ssx as 所属系,
教职工基本信息.jzglb as 教职工类别,
教职工基本信息.jzgly as 教职工来源,
教职工基本信息.bzlb as 编制类别,
教职工基本信息.yrfs as 用人方式,
教职工基本信息.cjny as 从教年月,
教职工基本信息.lxny as 来校年月,
教职工基本信息.jzjsprlb as 兼职教师聘任类别,
教职工基本信息.dslb as 导师类别,
教职工基本信息.fdylb as 辅导员类别,
教职工基本信息.sfssxjs as 是否双师型教师,
教职工基本信息.sfsjt as 是否双肩挑,
教职工基本信息.xkml as 学科门类,
教职工基本信息.yjxk as 一级学科,
教职工基本信息.ejxk as 二级学科,
教职工基本信息.yjfx as 研究方向,
教职工基本信息.jzgdqzt as 教职工当前状态,
教职工基本信息.lxrq as 离校日期,
教职工基本信息.yjtxrq as 预计退休日期,
教职工基本信息.zyjszw as 专业技术职务,
教职工基本信息.zyjszwjb as 专业技术职务级别,
教职工基本信息.zyjsgwdj as 专业技术岗位等级,
教职工基本信息.glgwdj as 管理岗位等级,
教职工基本信息.gqgwdj as 工勤岗位等级,
教职工基本信息.zygwlx as 主要岗位类型,
教职工基本信息.gwmc as 岗位名称,
教职工基本信息.gbzw as 干部职务,
教职工基本信息.gbzwjb as 干部职务级别,
教职工基本信息.nl as 年龄,
教职工基本信息.rjlx ')||TO_CLOB('as 任教类型,
教职工基本信息.rjzymc as 任教专业名称,
教职工基本信息.rjzydm as 任教专业代码,
教职工基本信息.zyrjsj as 专业任教时间,
教职工基本信息.sfsyjsry as 是否实验技术人员,
教职工基本信息.sfwp as 是否外聘,
教职工基本信息.glrylb as 管理人员类别,
教职工基本信息.sfbds as 是否班导师,
教职工基本信息.sfskjs as 是否授课教师,
教职工基本信息.sfszkjs as 是否思政课教师,
教职工基本信息.kzrq as 快照日期,
教职工基本信息.jkzk as 健康状况,
教职工基本信息.yjjgdm as 一级机构代码,
教职工基本信息.yjjg as 一级机构,
教职工基本信息.cjgzny as 参加工作年月,
教职工基本信息.sffdy as 是否辅导员,
教职工基本信息.zyjsgwlb as 专业技术岗位类别,
教职工基本信息.zrjslx as 专任教师类型,
教职工基本信息.zgxwcc as 最高学位层次,
教职工基本信息.zyjszwdm as 专业技术职务代码,
教职工基本信息.zyjszwjbdm as 专业技术职务级别代码
from abd_tea_jzgjbxx 教职工基本信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgjyjl', 'abd_tea_jzgjyjl', '教职工教育经历', TO_CLOB('
select
教职工教育经历.zgh as 教职工号,
教职工教育经历.rxny as 入学年月,
教职工教育经历.byny as 毕业年月,
教职工教育经历.byyxxhdw as 毕肄业学校或单位,
教职工教育经历.xl as 学历,
教职工教育经历.xw as 学位,
教职工教育经历.sxzy as 所学专业,
教职工教育经历.xwsygj as 学位授予国家,
教职工教育经历.gxlb as 高校类别,
教职工教育经历.xz as 学制
from abd_tea_jzgjyjl 教职工教育经历'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgjyjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgzzxx', 'abd_tea_jzgzzxx', '教职工资质信息', TO_CLOB('
select
教职工资质信息.zgh as 教职工号,
教职工资质信息.zzmc as 资质名称,
教职工资质信息.zzlb as 资质类别,
教职工资质信息.zzdj as 资质等级,
教职工资质信息.hdrq as 获得日期
from abd_tea_jzgzzxx 教职工资质信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgzzxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzggzjl', 'abd_tea_jzggzjl', '教职工工作经历', TO_CLOB('
select
教职工工作经历.zgh as 教职工号,
教职工工作经历.szdwmc as 所在单位名称,
教职工工作经历.gjdq as 国家地区,
教职工工作经历.crdzzw as 曾任党政职务,
教职工工作经历.crzyjszw as 曾任专业技术职务,
教职工工作经历.sfhwjl as 是否海外经历,
教职工工作经历.qsny as 起始年月,
教职工工作经历.jzny as 截止年月
from abd_tea_jzggzjl 教职工工作经历'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzggzjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_gbzwxx', 'abd_tea_gbzwxx', '干部职务信息', TO_CLOB('
select
干部职务信息.zgh as 教职工号,
干部职务信息.zwmc as 职务名称,
干部职务信息.zwlb as 职务类别,
干部职务信息.zwjb as 职务级别,
干部职务信息.rzdw as 任职单位,
干部职务信息.rzny as 任职年月,
干部职务信息.rzfs as 任职方式,
干部职务信息.rzqx as 任职期限,
干部职务信息.sfzr as 是否在任,
干部职务信息.mzrq as 免职日期,
干部职务信息.rzdwbm as 任职单位编码
from abd_tea_gbzwxx 干部职务信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_gbzwxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgjljryxx', 'abd_tea_jzgjljryxx', '教职工奖励及荣誉信息', TO_CLOB('
select
教职工奖励及荣誉信息.zgh as 教职工号,
教职工奖励及荣誉信息.jlmc as 奖励名称,
教职工奖励及荣誉信息.rych as 荣誉称号,
教职工奖励及荣誉信息.hjrq as 获奖日期,
教职工奖励及荣誉信息.jljb as 奖励级别,
教职工奖励及荣誉信息.jldj as 奖励等级,
教职工奖励及荣誉信息.jllb as 奖励类别,
教职工奖励及荣誉信息.bjdw as 颁奖单位,
教职工奖励及荣誉信息.hjxm as 获奖项目,
教职工奖励及荣誉信息.brpm as 本人排名
from abd_tea_jzgjljryxx 教职工奖励及荣誉信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgjljryxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgjxcghjxx', 'abd_tea_jzgjxcghjxx', '教职工教学成果获奖信息', TO_CLOB('
select
教职工教学成果获奖信息.zgh as 教职工号,
教职工教学成果获奖信息.jxcgbh as 教学成果编号,
教职工教学成果获奖信息.jxcgmc as 教学成果名称,
教职工教学成果获奖信息.brwc as 本人位次,
教职工教学成果获奖信息.jxmc as 奖项名称,
教职工教学成果获奖信息.jljb as 奖励级别,
教职工教学成果获奖信息.jldj as 奖励等级,
教职工教学成果获奖信息.hjrq as 获奖日期,
教职工教学成果获奖信息.xkml as 学科门类,
教职工教学成果获奖信息.yjxk as 一级学科,
教职工教学成果获奖信息.xxsm as 学校署名,
教职工教学成果获奖信息.jljbdm as 奖励级别代码
from abd_tea_jzgjxcghjxx 教职工教学成果获奖信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgjxcghjxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgkyhj', 'abd_tea_jzgkyhj', '教职工科研获奖', TO_CLOB('
select
教职工科研获奖.zgh as 教职工号,
教职工科研获奖.hjcglx as 获奖成果类型,
教职工科研获奖.hjcgbh as 获奖成果编号,
教职工科研获奖.hjcgmc as 获奖成果名称,
教职工科研获奖.jlmc as 奖励名称,
教职工科研获奖.hjlb as 获奖类别,
教职工科研获奖.hjjb as 获奖级别,
教职工科研获奖.hjdj as 获奖等级,
教职工科研获奖.hjrq as 获奖日期,
教职工科研获奖.hjje as 获奖金额,
教职工科研获奖.dwpm as 单位排名,
教职工科研获奖.smsx as 署名顺序,
教职工科研获奖.hjjbdm as 获奖级别代码
from abd_tea_jzgkyhj 教职工科研获奖'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgkyhj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jshxsj', 'abd_tea_jshxsj', '教师画像数据', TO_CLOB('
select
教师画像数据.zjsjwyxbs as 主键数据唯一性标识,
教师画像数据.xxdm as 学校代码,
教师画像数据.jgh as 教工号,
教师画像数据.jsxm as 教师姓名,
教师画像数据.sjkbfwcs as 数据看板访问次数,
教师画像数据.pjwdjjg as 评价维度及结果,
教师画像数据.sjscrq as 数据生成日期,
教师画像数据.sjkbzxfwrq as 数据看板最新访问日期,
教师画像数据.sjcjsj as 数据采集时间
from abd_tea_jshxsj 教师画像数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jshxsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jsqyjzsjsj', 'abd_tea_jsqyjzsjsj', '教师企业兼职/实践数据', TO_CLOB('
select
教师企业兼职实践数据.zjsjwyxbs as 主键数据唯一性标识,
教师企业兼职实践数据.xxdm as 学校代码,
教师企业兼职实践数据.jgh as 教工号,
教师企业兼职实践数据.jsxm as 教师姓名,
教师企业兼职实践数据.qydllxm as 企业锻炼类型码,
教师企业兼职实践数据.qyjgdm as 企业机构代码,
教师企业兼职实践数据.qymc as 企业名称,
教师企业兼职实践数据.qyxzm as 企业性质码,
教师企业兼职实践数据.zqysjjzksrq as 在企业实践兼职开始日期,
教师企业兼职实践数据.zqysjjzzzrq as 在企业实践兼职终止日期,
教师企业兼职实践数据.qysjjzgw as 企业实践兼职岗位,
教师企业兼职实践数据.qysshym as 企业所属行业码,
教师企业兼职实践数据.sjcjsj as 数据采集时间
from abd_tea_jsqyjzsjsj 教师企业兼职实践数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jsqyjzsjsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_xxjxsj', 'abd_tea_xxjxsj', '学习进修数据', TO_CLOB('
select
学习进修数据.zjsjwyxbs as 主键数据唯一性标识,
学习进修数据.xxdm as 学校代码,
学习进修数据.jgh as 教工号,
学习进修数据.jsxm as 教师姓名,
学习进修数据.jxxxddm as 进修学习地点码,
学习进修数据.jxpxbh as 进修培训编号,
学习进修数据.jxpxmc as 进修培训名称,
学习进修数据.jxpxzt as 进修培训主题,
学习进修数据.jxpxnrjj as 进修培训内容简介,
学习进修数据.jxpxzjrxm as 进修培训主讲人姓名,
学习进修数据.jxpxksrq as 进修培训开始日期,
学习进修数据.jxpxjzrq as 进修培训截止日期,
学习进修数据.cgrq as 出国境日期,
学习进修数据.cgmdm as 出国境目的码,
学习进修数据.pcdw as 派出单位,
学习进修数据.spdw as 审批单位,
学习进修数据.sprq as 审批日期,
学习进修数据.yhgrq as 应回国日期,
学习进修数据.sjcjsj as 数据采集时间
from abd_tea_xxjxsj 学习进修数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_xxjxsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jskhsj', 'abd_tea_jskhsj', '教师考核数据', TO_CLOB('
select
教师考核数据.zjsjwyxbs as 主键数据唯一性标识,
教师考核数据.xxdm as 学校代码,
教师考核数据.jgh as 教工号,
教师考核数据.jzgxm as 教职工姓名,
教师考核数据.jzgkhlbm as 教职工考核类别码,
教师考核数据.jzgkhrq as 教职工考核日期,
教师考核数据.jzgkhnr as 教职工考核内容,
教师考核数据.jzgkhdwh as 教职工考核单位号,
教师考核数据.khdwmc as 考核单位名称,
教师考核数据.dwkhjgm as 单位考核结果码,
教师考核数据.dwkhfzrh as 单位考核负责人号,
教师考核数据.fzrxm as 负责人姓名,
教师考核数据.xxkhjgm as 学校考核结果码,
教师考核数据.sjcjsj as 数据采集时间
from abd_tea_jskhsj 教师考核数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jskhsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jscygkszrtdjl', 'abd_tea_jscygkszrtdjl', '教师参与岗课赛证融通的记录', TO_CLOB('
select
教师参与岗课赛证融通的记录.gh as 工号,
教师参与岗课赛证融通的记录.xm as 姓名,
教师参与岗课赛证融通的记录.cjsj as 参加时间,
教师参与岗课赛证融通的记录.gkszrtmc as 岗课赛证融通名称
from abd_tea_jscygkszrtdjl 教师参与岗课赛证融通的记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jscygkszrtdjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzksjbxx', 'abd_und_bzksjbxx', '本专科生基本信息', TO_CLOB('
select
本专科生基本信息.xh as 学号,
本专科生基本信息.xm as 姓名,
本专科生基本信息.xmpy as 姓名拼音,
本专科生基本信息.cym as 曾用名,
本专科生基本信息.xb as 性别,
本专科生基本信息.nl as 年龄,
本专科生基本信息.xx as 血型,
本专科生基本信息.csrq as 出生日期,
本专科生基本信息.csd as 出生地,
本专科生基本信息.jg as 籍贯,
本专科生基本信息.gjdq as 国家地区,
本专科生基本信息.mz as 民族,
本专科生基本信息.sfzjlx as 身份证件类型,
本专科生基本信息.hyzk as 婚姻状况,
本专科生基本信息.jkzk as 健康状况,
本专科生基本信息.gatqw as 港澳台侨外,
本专科生基本信息.zjxy as 宗教信仰,
本专科生基本信息.zzmm as 政治面貌,
本专科生基本信息.sstzz as 所属团组织,
本专科生基本信息.ssdzz as 所属党组织,
本专科生基本信息.yktkh as 一卡通卡号,
本专科生基本信息.wlzh as 网络账号,
本专科生基本信息.yxzh as 邮箱账号,
本专科生基本信息.xqh as 校区号,
本专科生基本信息.xq as 校区,
本专科生基本信息.xybm as 学院编码,
本专科生基本信息.xy as 学院,
本专科生基本信息.bh as 班号,
本专科生基本信息.bj as 班级,
本专科生基本信息.zybm as 专业编码,
本专科生基本信息.zy as 专业,
本专科生基本信息.nj as 年级,
本专科生基本信息.ssq as 宿舍区,
本专科生基本信息.ssl as 宿舍楼,
本专科生基本信息.s')||TO_CLOB('sdz as 宿舍地址,
本专科生基本信息.rxzp as 入学照片,
本专科生基本信息.xslb as 学生类别,
本专科生基本信息.pyfs as 培养方式,
本专科生基本信息.pycc as 培养层次,
本专科生基本信息.pyfa as 培养方案,
本专科生基本信息.xz as 学制,
本专科生基本信息.yjbyrq as 预计毕业日期,
本专科生基本信息.xjzt as 学籍状态,
本专科生基本信息.xsdqzt as 学生当前状态,
本专科生基本信息.dszgh as 导师职工号,
本专科生基本信息.kzrq as 快照日期
from abd_und_bzksjbxx 本专科生基本信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzksjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzklhlxsxx', 'abd_und_bzklhlxsxx', '本专科来华留学生信息', TO_CLOB('
select
本专科来华留学生信息.xh as 学号,
本专科来华留学生信息.xm as 姓名,
本专科来华留学生信息.ywxm as 英文姓名,
本专科来华留学生信息.hzx as 护照姓,
本专科来华留学生信息.hzm as 护照名,
本专科来华留学生信息.xb as 性别,
本专科来华留学生信息.xy as 学院,
本专科来华留学生信息.gj as 国籍,
本专科来华留学生信息.lygwgx as 来源国外高校,
本专科来华留学生信息.tlqx as 停留期限,
本专科来华留学生信息.csrq as 出生日期,
本专科来华留学生信息.csd as 出生地,
本专科来华留学生信息.zgxl as 最高学历,
本专科来华留学生信息.hyzk as 婚姻状况,
本专科来华留学生信息.xyzj as 信仰宗教,
本专科来华留学生信息.my as 母语,
本专科来华留学生信息.qzzlx as 签证注类型,
本专科来华留学生信息.tjdw as 推荐单位,
本专科来华留学生信息.zhswdbr as 在华事务担保人,
本专科来华留学生信息.zhswdbrdh as 在华事务担保人电话,
本专科来华留学生信息.jfly as 经费来源,
本专科来华留学生信息.lxslb as 留学生类别,
本专科来华留学生信息.zcrq as 注册日期,
本专科来华留学生信息.lxqx as 留学期限,
本专科来华留学生信息.lxrq as 来校日期,
本专科来华留学生信息.yzymc as 原专业名称,
本专科来华留学生信息.pzlxqx as 批准留学期限,
本专科来华留学生信息.hynl as 汉语能力,
本专科来华留学生信息.yynl as 英语能力,
本专科来华留学生信息.pycc a')||TO_CLOB('s 培养层次,
本专科来华留学生信息.skyy as 授课语言,
本专科来华留学生信息.sqrq as 申请日期,
本专科来华留学生信息.lqrq as 录取日期,
本专科来华留学生信息.lhrq as 来华日期,
本专科来华留学生信息.byrq as 毕业日期
from abd_und_bzklhlxsxx 本专科来华留学生信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzklhlxsxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzkscgjlxjl', 'abd_und_bzkscgjlxjl', '本专科生出国(境)留学记录', TO_CLOB('
select
本专科生出国境留学记录.xh as 学号,
本专科生出国境留学记录.xmbh as 项目编号,
本专科生出国境留学记录.lxdw as 留学单位,
本专科生出国境留学记录.ddrq as 抵达日期,
本专科生出国境留学记录.lxqx as 留学期限,
本专科生出国境留学记录.lxqxksrq as 留学期限开始日期,
本专科生出国境留学记录.lxqxjsrq as 留学期限结束日期,
本专科生出国境留学记录.ggrq as 归国日期,
本专科生出国境留学记录.hzzt as 护照状态
from abd_und_bzkscgjlxjl 本专科生出国境留学记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzkscgjlxjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzkscgjxm', 'abd_und_bzkscgjxm', '本专科生出国(境)项目', TO_CLOB('
select
本专科生出国境项目.xh as 学号,
本专科生出国境项目.xmbh as 项目编号,
本专科生出国境项目.xmmc as 项目名称,
本专科生出国境项目.xmlx as 项目类型,
本专科生出国境项目.xmzq as 项目周期,
本专科生出国境项目.qwgjdq as 前往国家地区,
本专科生出国境项目.fzdw as 负责单位,
本专科生出国境项目.lxdw as 留学单位,
本专科生出国境项目.pcqx as 派出期限,
本专科生出国境项目.xmme as 项目名额,
本专科生出国境项目.bmkssj as 报名开始时间,
本专科生出国境项目.bmjssj as 报名结束时间,
本专科生出国境项目.xmksrq as 项目开始日期,
本专科生出国境项目.xmjsrq as 项目结束日期,
本专科生出国境项目.xmjs as 项目介绍,
本专科生出国境项目.lxdwjj as 留学单位简介
from abd_und_bzkscgjxm 本专科生出国境项目'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzkscgjxm'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzksxkjsxx', 'abd_und_bzksxkjsxx', '本专科生学科竞赛信息', TO_CLOB('
select
本专科生学科竞赛信息.xh as 学号,
本专科生学科竞赛信息.jsbh as 竞赛编号,
本专科生学科竞赛信息.xnxq as 学年学期,
本专科生学科竞赛信息.jsmc as 竞赛名称,
本专科生学科竞赛信息.jsjb as 竞赛级别,
本专科生学科竞赛信息.jsxk as 竞赛学科,
本专科生学科竞赛信息.jsksrq as 竞赛开始日期,
本专科生学科竞赛信息.jsjsrq as 竞赛结束日期,
本专科生学科竞赛信息.tdbh as 团队编号,
本专科生学科竞赛信息.tdmc as 团队名称,
本专科生学科竞赛信息.zpbh as 作品编号,
本专科生学科竞赛信息.zpmc as 作品名称,
本专科生学科竞赛信息.sftdfzr as 是否团队负责人,
本专科生学科竞赛信息.brpx as 本人排序,
本专科生学科竞赛信息.jscj as 竞赛成绩,
本专科生学科竞赛信息.jljb as 奖励级别,
本专科生学科竞赛信息.jldj as 奖励等级,
本专科生学科竞赛信息.hjmc as 获奖名次,
本专科生学科竞赛信息.hjrq as 获奖日期,
本专科生学科竞赛信息.hjzsbh as 获奖证书编号
from abd_und_bzksxkjsxx 本专科生学科竞赛信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzksxkjsxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_sxjcsj', 'abd_und_sxjcsj', '实习基础数据', TO_CLOB('
select
实习基础数据.zjsjwyxbs as 主键数据唯一性标识,
实习基础数据.xxdm as 学校代码,
实习基础数据.xh as 学号,
实习基础数据.xsxm as 学生姓名,
实习基础数据.zyh as 专业号,
实习基础数据.zymc as 专业名称,
实习基础数据.bh as 班号,
实习基础数据.bjmc as 班级名称,
实习基础数据.xzm as 学制码,
实习基础数据.xqm as 学期码,
实习基础数据.sxsfks as 实习是否开始,
实习基础数据.sxsfjs as 实习是否结束,
实习基础数据.sxqyhylbm as 实习企业行业类别码,
实习基础数据.sxnr as 实习内容,
实习基础数据.sfdk as 是否对口,
实习基础数据.sxapm as 实习安排码,
实习基础数据.sxxsm as 实习形式码,
实习基础数据.sxsc as 实习时长,
实习基础数据.sxcj as 实习成绩,
实习基础数据.sxksrq as 实习开始日期,
实习基础数据.sxjsrq as 实习结束日期,
实习基础数据.sxqymc as 实习企业名称,
实习基础数据.sxddszsjgbm as 实习地点所在省机构编码,
实习基础数据.sxddszsjgmc as 实习地点所在省机构名称,
实习基础数据.sxddszshjgbm as 实习地点所在市机构编码,
实习基础数据.sxddszshjgmc as 实习地点所在市机构名称,
实习基础数据.xnzdjsgh as 校内指导教师工号,
实习基础数据.xnzdjsxm as 校内指导教师姓名,
实习基础数据.xnzdjsdh as 校内指导教师电话,
实习基础数据.qyzmzdryxm as ')||TO_CLOB('企业专门指导人员姓名,
实习基础数据.qyzmzdrydh as 企业专门指导人员电话,
实习基础数据.zfsxxssjbc as 支付实习学生生均报酬,
实习基础数据.xjjdzxdh as 校级监督咨询电话,
实习基础数据.sfqdsfxy as 是否签订三方协议,
实习基础数据.sfkssx as 是否跨省实习,
实习基础数据.sffgwsx as 是否赴国外实习,
实习基础数据.rxnf as 入学年份,
实习基础数据.sjcjsj as 数据采集时间
from abd_und_sxjcsj 实习基础数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_sxjcsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_sxbxgmqksj', 'abd_und_sxbxgmqksj', '实习保险购买情况数据', TO_CLOB('
select
实习保险购买情况数据.zjsjwyxbs as 主键数据唯一性标识,
实习保险购买情况数据.xxdm as 学校代码,
实习保险购买情况数据.xh as 学号,
实习保险购买情况数据.xsxm as 学生姓名,
实习保险购买情况数据.bxxzmc as 保险险种名称,
实习保险购买情况数据.bdh as 保单号,
实习保险购买情况数据.bxfczfmc as 保险费出资方名称,
实习保险购买情况数据.bxgmrq as 保险购买日期,
实习保险购买情况数据.bxgmfm as 保险购买方码,
实习保险购买情况数据.gmbxzlm as 购买保险种类码,
实习保险购买情况数据.sjcjsj as 数据采集时间
from abd_und_sxbxgmqksj 实习保险购买情况数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_sxbxgmqksj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_sxwgxwqksj', 'abd_und_sxwgxwqksj', '实习违规行为情况数据', TO_CLOB('
select
实习违规行为情况数据.zjsjwyxbs as 主键数据唯一性标识,
实习违规行为情况数据.xxdm as 学校代码,
实习违规行为情况数据.xh as 学号,
实习违规行为情况数据.xsxm as 学生姓名,
实习违规行为情况数据.sxksrq as 实习开始日期,
实习违规行为情况数据.sxjsrq as 实习结束日期,
实习违规行为情况数据.sxddszsjgbm as 实习地点所在省机构编码,
实习违规行为情况数据.sxddszsjgmc as 实习地点所在省机构名称,
实习违规行为情况数据.sxddszshjgbm as 实习地点所在市机构编码,
实习违规行为情况数据.sxddszshjgmc as 实习地点所在市机构名称,
实习违规行为情况数据.sxqymc as 实习企业名称,
实习违规行为情况数据.qywgxwm as 企业违规行为码,
实习违规行为情况数据.sjcjsj as 数据采集时间
from abd_und_sxwgxwqksj 实习违规行为情况数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_sxwgxwqksj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_sxbgjlsj', 'abd_und_sxbgjlsj', '实习报告记录数据', TO_CLOB('
select
实习报告记录数据.zjsjwyxbs as 主键数据唯一性标识,
实习报告记录数据.xxdm as 学校代码,
实习报告记录数据.xh as 学号,
实习报告记录数据.xsxm as 学生姓名,
实习报告记录数据.sxqymc as 实习企业名称,
实习报告记录数据.sxgwmc as 实习岗位名称,
实习报告记录数据.sxzdjs as 实习指导教师,
实习报告记录数据.sxbglbm as 实习报告类别码,
实习报告记录数据.sxbgnr as 实习报告内容,
实习报告记录数据.sxbgtjrq as 实习报告提交日期,
实习报告记录数据.jspyztm as 教师批阅状态码,
实习报告记录数据.sjcjsj as 数据采集时间
from abd_und_sxbgjlsj 实习报告记录数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_sxbgjlsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_xsxksj', 'abd_und_xsxksj', '学生选课数据', TO_CLOB('
select
学生选课数据.zjsjwyxbs as 主键数据唯一性标识,
学生选课数据.xxdm as 学校代码,
学生选课数据.xh as 学号,
学生选课数据.jhkch as 计划课程号,
学生选课数据.kch as 课程号,
学生选课数据.kcmc as 课程名称,
学生选课数据.kkxnd as 开课学年度,
学生选课数据.kkxqm as 开课学期码,
学生选课数据.xksj as 选课时间,
学生选课数据.xkxzm as 选课性质码,
学生选课数据.sjcjsj as 数据采集时间
from abd_und_xsxksj 学生选课数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_xsxksj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_xshxsj', 'abd_und_xshxsj', '学生画像数据', TO_CLOB('
select
学生画像数据.zjsjwyxbs as 主键数据唯一性标识,
学生画像数据.xxdm as 学校代码,
学生画像数据.xh as 学号,
学生画像数据.xsxm as 学生姓名,
学生画像数据.sjkbfwcs as 数据看板访问次数,
学生画像数据.pjwdjjg as 评价维度及结果,
学生画像数据.sjscrq as 数据生成日期,
学生画像数据.sjkbzxfwrq as 数据看板最新访问日期,
学生画像数据.sjcjsj as 数据采集时间
from abd_und_xshxsj 学生画像数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_xshxsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_xsjnzssj', 'abd_und_xsjnzssj', '学生技能证书数据', TO_CLOB('
select
学生技能证书数据.zjsjwyxbs as 主键数据唯一性标识,
学生技能证书数据.xxdm as 学校代码,
学生技能证书数据.xh as 学号,
学生技能证书数据.xsxm as 学生姓名,
学生技能证书数据.fzjg as 发证机构,
学生技能证书数据.zsdjm as 证书等级码,
学生技能证书数据.zsh as 证书号,
学生技能证书数据.zsmc as 证书名称,
学生技能证书数据.fzrq as 发证日期,
学生技能证书数据.sfzcdzzs as 是否支持电子证书,
学生技能证书数据.xzdycs as 下载打印次数,
学生技能证书数据.sjcjsj as 数据采集时间
from abd_und_xsjnzssj 学生技能证书数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_xsjnzssj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_zhcjypjsj', 'abd_und_zhcjypjsj', '综合成绩与评价数据', TO_CLOB('
select
综合成绩与评价数据.zjsjwyxbs as 主键数据唯一性标识,
综合成绩与评价数据.xxdm as 学校代码,
综合成绩与评价数据.xn as 学年度,
综合成绩与评价数据.xqm as 学期码,
综合成绩与评价数据.zyh as 专业号,
综合成绩与评价数据.zymc as 专业名称,
综合成绩与评价数据.nj as 年级,
综合成绩与评价数据.bh as 班号,
综合成绩与评价数据.bjmc as 班级名称,
综合成绩与评价数据.xh as 学号,
综合成绩与评价数据.xsxm as 学生姓名,
综合成绩与评价数据.ggjckyhxf as 公共基础课已获学分,
综合成绩与评价数据.zyjckyhxf as 专业基础课已获学分,
综合成绩与评价数据.zyhxkyhxf as 专业核心课已获学分,
综合成绩与评价数据.zytzkyhxf as 专业拓展课已获学分,
综合成绩与评价数据.ggjckcj as 公共基础课成绩,
综合成绩与评价数据.zyjckcj as 专业基础课成绩,
综合成绩与评价数据.zyhxkcj as 专业核心课成绩,
综合成绩与评价数据.zytzkcj as 专业拓展课成绩,
综合成绩与评价数据.gwsxcj as 岗位实习成绩,
综合成绩与评价数据.lrrq as 录入日期,
综合成绩与评价数据.sjcjsj as 数据采集时间
from abd_und_zhcjypjsj 综合成绩与评价数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_zhcjypjsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_jzdsqsj', 'abd_und_jzdsqsj', '奖助贷申请数据', TO_CLOB('
select
奖助贷申请数据.zjsjwyxbs as 主键数据唯一性标识,
奖助贷申请数据.xxdm as 学校代码,
奖助贷申请数据.xh as 学号,
奖助贷申请数据.xsxm as 学生姓名,
奖助贷申请数据.jzxmmc as 奖助项目名称,
奖助贷申请数据.jzxmzlm as 奖助项目种类码,
奖助贷申请数据.jzbz as 奖助标准,
奖助贷申请数据.zzdwgr as 资助单位个人,
奖助贷申请数据.zjlym as 资金来源码,
奖助贷申请数据.sqrq as 申请日期,
奖助贷申请数据.spsj as 审批时间,
奖助贷申请数据.spztm as 审批状态码,
奖助贷申请数据.sjcjsj as 数据采集时间
from abd_und_jzdsqsj 奖助贷申请数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_jzdsqsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_xscystsj', 'abd_und_xscystsj', '学生参与社团数据', TO_CLOB('
select
学生参与社团数据.zjsjwyxbs as 主键数据唯一性标识,
学生参与社团数据.xxdm as 学校代码,
学生参与社团数据.stbh as 社团编号,
学生参与社团数据.stmc as 社团名称,
学生参与社团数据.xh as 学号,
学生参与社团数据.xsxm as 学生姓名,
学生参与社团数据.cjrq as 参加日期,
学生参与社团数据.tcrq as 退出日期,
学生参与社团数据.stzw as 社团职务,
学生参与社团数据.sjcjsj as 数据采集时间
from abd_und_xscystsj 学生参与社团数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_xscystsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_cjsthdsj', 'abd_und_cjsthdsj', '参加社团活动数据', TO_CLOB('
select
参加社团活动数据.zjsjwyxbs as 主键数据唯一性标识,
参加社团活动数据.xxdm as 学校代码,
参加社团活动数据.xh as 学号,
参加社团活动数据.xsxm as 学生姓名,
参加社团活动数据.sthdbh as 社团活动编号,
参加社团活动数据.sthdmc as 社团活动名称,
参加社团活动数据.sthdlxm as 社团活动类型码,
参加社团活动数据.cjsthdsj as 参加社团活动时间,
参加社团活动数据.hjjbm as 获奖级别码,
参加社团活动数据.hjrq as 获奖日期,
参加社团活动数据.bjdw as 颁奖单位,
参加社团活动数据.zdjsxm as 指导教师姓名,
参加社团活动数据.sjcjsj as 数据采集时间
from abd_und_cjsthdsj 参加社团活动数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_cjsthdsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_xlzxjlsj', 'abd_und_xlzxjlsj', '心理咨询记录数据', TO_CLOB('
select
心理咨询记录数据.zjsjwyxbs as 主键数据唯一性标识,
心理咨询记录数据.xxdm as 学校代码,
心理咨询记录数据.zxrq as 咨询日期,
心理咨询记录数据.zxrc as 咨询人次,
心理咨询记录数据.gjc as 关键词,
心理咨询记录数据.yyrc as 预约人次,
心理咨询记录数据.sjcjsj as 数据采集时间
from abd_und_xlzxjlsj 心理咨询记录数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_xlzxjlsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_lxsxblsj', 'abd_und_lxsxblsj', '离校手续办理数据', TO_CLOB('
select
离校手续办理数据.zjsjwyxbs as 主键数据唯一性标识,
离校手续办理数据.xxdm as 学校代码,
离校手续办理数据.xh as 学号,
离校手续办理数据.xsxm as 学生姓名,
离校手续办理数据.jfqkqrrq as 结费情况确认日期,
离校手续办理数据.xfqrrq as 学分确认日期,
离校手续办理数据.tsghqrrq as 图书归还确认日期,
离校手续办理数据.sstsqrrq as 宿舍退宿确认日期,
离校手续办理数据.byzlqrq as 毕业证领取日期,
离校手续办理数据.sjcjsj as 数据采集时间
from abd_und_lxsxblsj 离校手续办理数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_lxsxblsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_byqxsxsj', 'abd_und_byqxsxsj', '毕业去向【升学】数据', TO_CLOB('
select
毕业去向升学数据.zjsjwyxbs as 主键数据唯一性标识,
毕业去向升学数据.xxdm as 学校代码,
毕业去向升学数据.xh as 学号,
毕业去向升学数据.xm as 姓名,
毕业去向升学数据.xslxm as 学生类型码,
毕业去向升学数据.zyh as 专业号,
毕业去向升学数据.zymc as 专业名称,
毕业去向升学数据.bh as 班号,
毕业去向升学数据.bjmc as 班级名称,
毕业去向升学数据.sfzjh as 身份证件号,
毕业去向升学数据.sxqdm as 升学渠道码,
毕业去向升学数据.lqxxmc as 录取学校名称,
毕业去向升学数据.lqzymc as 录取专业名称,
毕业去向升学数据.fs as 分数,
毕业去向升学数据.sxccm as 升学层次码,
毕业去向升学数据.sslxm as 硕士类型码,
毕业去向升学数据.sjcjsj as 数据采集时间
from abd_und_byqxsxsj 毕业去向升学数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_byqxsxsj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_byqxjysj', 'abd_und_byqxjysj', '毕业去向【就业】数据', TO_CLOB('
select
毕业去向就业数据.zjsjwyxbs as 主键数据唯一性标识,
毕业去向就业数据.xxdm as 学校代码,
毕业去向就业数据.xh as 学号,
毕业去向就业数据.xm as 姓名,
毕业去向就业数据.zyh as 专业号,
毕业去向就业数据.zymc as 专业名称,
毕业去向就业数据.bh as 班号,
毕业去向就业数据.bjmc as 班级名称,
毕业去向就业数据.sfzjh as 身份证件号,
毕业去向就业数据.jydwsfxqhzdw as 就业单位是否校企合作单位,
毕业去向就业数据.jydwmc as 就业单位名称,
毕业去向就业数据.jydwhym as 就业单位行业码,
毕业去向就业数据.jydwxzm as 就业单位性质码,
毕业去向就业数据.jydwgmm as 就业单位规模码,
毕业去向就业数据.qxx as 起薪线,
毕业去向就业数据.sfdk as 是否对口,
毕业去向就业数据.jyqysjgbm as 就业区域省机构编码,
毕业去向就业数据.jyqysjgmc as 就业区域省机构名称,
毕业去向就业数据.jyqyshjgbm as 就业区域市机构编码,
毕业去向就业数据.jyqyshjgmc as 就业区域市机构名称,
毕业去向就业数据.jyrq as 就业日期,
毕业去向就业数据.sfzzcy as 是否自主创业,
毕业去向就业数据.cyxmmc as 创业项目名称,
毕业去向就业数据.sflhjy as 是否灵活就业,
毕业去向就业数据.gzgwmc as 工作岗位名称,
毕业去向就业数据.sjcjsj as 数据采集时间
from abd_und_byqxjysj 毕业去向就业数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_byqxjysj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_byqxwjysj', 'abd_und_byqxwjysj', '毕业去向【未就业】数据', TO_CLOB('
select
毕业去向未就业数据.zjsjwyxbs as 主键数据唯一性标识,
毕业去向未就业数据.xxdm as 学校代码,
毕业去向未就业数据.xh as 学号,
毕业去向未就业数据.xm as 姓名,
毕业去向未就业数据.zyh as 专业号,
毕业去向未就业数据.zymc as 专业名称,
毕业去向未就业数据.bh as 班号,
毕业去向未就业数据.bjmc as 班级名称,
毕业去向未就业数据.sfzjh as 身份证件号,
毕业去向未就业数据.wjylxm as 未就业类型码,
毕业去向未就业数据.sjcjsj as 数据采集时间
from abd_und_byqxwjysj 毕业去向未就业数据'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_byqxwjysj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_xsckkbjl', 'abd_und_xsckkbjl', '学生查看课表记录', TO_CLOB('
select
学生查看课表记录.xh as 学号,
学生查看课表记录.xm as 姓名,
学生查看课表记录.fwsj as 访问时间
from abd_und_xsckkbjl 学生查看课表记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_xsckkbjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_xsdycjdjl', 'abd_und_xsdycjdjl', '学生打印成绩单记录', TO_CLOB('
select
学生打印成绩单记录.xh as 学号,
学生打印成绩单记录.xm as 姓名,
学生打印成绩单记录.dysj as 打印时间
from abd_und_xsdycjdjl 学生打印成绩单记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_xsdycjdjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0221', 'ive_si_sch_0221', '本学年拟制教学总体计划的专业数量', TO_CLOB('
select
本学年拟制教学总体计划的专业数量.xxbsm as 学校标识码,
本学年拟制教学总体计划的专业数量.xn as 学年,
本学年拟制教学总体计划的专业数量.zys as 专业数,
本学年拟制教学总体计划的专业数量.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本学年拟制教学总体计划的专业数量.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0221 本学年拟制教学总体计划的专业数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0221'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0222', 'ive_si_sch_0222', '本学期综合成绩与评价的总记录数', TO_CLOB('
select
本学期综合成绩与评价的总记录数.xxbsm as 学校标识码,
本学期综合成绩与评价的总记录数.xnxq as 学年学期,
本学期综合成绩与评价的总记录数.jls as 记录数,
本学期综合成绩与评价的总记录数.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本学期综合成绩与评价的总记录数.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0222 本学期综合成绩与评价的总记录数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0222'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0223', 'ive_si_sch_0223', '本学年产学合作企业接受毕业生人数', TO_CLOB('
select
本学年产学合作企业接受毕业生人数.xxbsm as 学校标识码,
本学年产学合作企业接受毕业生人数.xn as 学年,
本学年产学合作企业接受毕业生人数.xss as 学生数,
本学年产学合作企业接受毕业生人数.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本学年产学合作企业接受毕业生人数.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0223 本学年产学合作企业接受毕业生人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0223'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0224', 'ive_si_sch_0224', '本月学生画像访问次数', TO_CLOB('
select
本月学生画像访问次数.xxbsm as 学校标识码,
本月学生画像访问次数.tjny as 统计年月,
本月学生画像访问次数.fwcs as 访问次数,
本月学生画像访问次数.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月学生画像访问次数.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0224 本月学生画像访问次数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0224'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0225', 'ive_si_sch_0225', '本月参加党建活动的教师人数', TO_CLOB('
select
本月参加党建活动的教师人数.xxbsm as 学校标识码,
本月参加党建活动的教师人数.tjny as 统计年月,
本月参加党建活动的教师人数.jss as 教师数,
本月参加党建活动的教师人数.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月参加党建活动的教师人数.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0225 本月参加党建活动的教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0225'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0226', 'ive_si_sch_0226', '本月参加学习进修的教师人次', TO_CLOB('
select
本月参加学习进修的教师人次.xxbsm as 学校标识码,
本月参加学习进修的教师人次.tjny as 统计年月,
本月参加学习进修的教师人次.jss as 教师数,
本月参加学习进修的教师人次.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月参加学习进修的教师人次.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0226 本月参加学习进修的教师人次'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0226'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0227', 'ive_si_sch_0227', '本学期参加“科研和教研类别”项目的教师人数', TO_CLOB('
select
本学期参加科研和教研类别项目的教师人数.xxbsm as 学校标识码,
本学期参加科研和教研类别项目的教师人数.xnxq as 学年学期,
本学期参加科研和教研类别项目的教师人数.jss as 教师数,
本学期参加科研和教研类别项目的教师人数.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本学期参加科研和教研类别项目的教师人数.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0227 本学期参加科研和教研类别项目的教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0227'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0228', 'ive_si_sch_0228', '本学期参与学术讲座的教师人数', TO_CLOB('
select
本学期参与学术讲座的教师人数.xxbsm as 学校标识码,
本学期参与学术讲座的教师人数.xnxq as 学年学期,
本学期参与学术讲座的教师人数.jss as 教师数,
本学期参与学术讲座的教师人数.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本学期参与学术讲座的教师人数.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0228 本学期参与学术讲座的教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0228'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0229', 'ive_si_sch_0229', '本学期企业兼职教师数', TO_CLOB('
select
本学期企业兼职教师数.xxbsm as 学校标识码,
本学期企业兼职教师数.xnxq as 学年学期,
本学期企业兼职教师数.jss as 教师数,
本学期企业兼职教师数.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本学期企业兼职教师数.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0229 本学期企业兼职教师数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0229'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0230', 'ive_si_sch_0230', '本学期参加“校企合作类别”项目的教师人数', TO_CLOB('
select
本学期参加校企合作类别项目的教师人数.xxbsm as 学校标识码,
本学期参加校企合作类别项目的教师人数.xnxq as 学年学期,
本学期参加校企合作类别项目的教师人数.jss as 教师数,
本学期参加校企合作类别项目的教师人数.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本学期参加校企合作类别项目的教师人数.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0230 本学期参加校企合作类别项目的教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0230'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0231', 'ive_si_sch_0231', '本学期参加企业实践的教师人数', TO_CLOB('
select
本学期参加企业实践的教师人数.xxbsm as 学校标识码,
本学期参加企业实践的教师人数.xnxq as 学年学期,
本学期参加企业实践的教师人数.jss as 教师数,
本学期参加企业实践的教师人数.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本学期参加企业实践的教师人数.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0231 本学期参加企业实践的教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0231'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0232', 'ive_si_sch_0232', '本月教师画像访问次数', TO_CLOB('
select
本月教师画像访问次数.xxbsm as 学校标识码,
本月教师画像访问次数.tjny as 统计年月,
本月教师画像访问次数.fwcs as 访问次数,
本月教师画像访问次数.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月教师画像访问次数.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0232 本月教师画像访问次数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0232'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0233', 'ive_si_sch_0233', '本学年总体计划内容更新总次数', TO_CLOB('
select
本学年总体计划内容更新总次数.xxbsm as 学校标识码,
本学年总体计划内容更新总次数.xn as 学年,
本学年总体计划内容更新总次数.gxcs as 更新次数,
本学年总体计划内容更新总次数.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本学年总体计划内容更新总次数.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0233 本学年总体计划内容更新总次数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0233'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0234', 'ive_si_sch_0234', '本学年专业设置变化记录数', TO_CLOB('
select
本学年专业设置变化记录数.xxbsm as 学校标识码,
本学年专业设置变化记录数.xn as 学年,
本学年专业设置变化记录数.dzcs as 调整次数,
本学年专业设置变化记录数.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本学年专业设置变化记录数.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0234 本学年专业设置变化记录数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0234'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0235', 'ive_si_sch_0235', '本月参加思政活动的学生人次', TO_CLOB('
select
本月参加思政活动的学生人次.xxbsm as 学校标识码,
本月参加思政活动的学生人次.tjny as 统计年月,
本月参加思政活动的学生人次.xss as 学生数,
本月参加思政活动的学生人次.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月参加思政活动的学生人次.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0235 本月参加思政活动的学生人次'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0235'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0236', 'ive_si_sch_0236', '本月通过网络课程在线学习的学生人次', TO_CLOB('
select
本月通过网络课程在线学习的学生人次.xxbsm as 学校标识码,
本月通过网络课程在线学习的学生人次.tjny as 统计年月,
本月通过网络课程在线学习的学生人次.xss as 学生数,
本月通过网络课程在线学习的学生人次.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月通过网络课程在线学习的学生人次.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0236 本月通过网络课程在线学习的学生人次'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0236'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0237', 'ive_si_sch_0237', '本月在智慧教室上课的学生人数', TO_CLOB('
select
本月在智慧教室上课的学生人数.xxbsm as 学校标识码,
本月在智慧教室上课的学生人数.tjny as 统计年月,
本月在智慧教室上课的学生人数.xss as 学生数,
本月在智慧教室上课的学生人数.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月在智慧教室上课的学生人数.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0237 本月在智慧教室上课的学生人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0237'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0238', 'ive_si_sch_0238', '本月借阅图书的学生人次', TO_CLOB('
select
本月借阅图书的学生人次.xxbsm as 学校标识码,
本月借阅图书的学生人次.tjny as 统计年月,
本月借阅图书的学生人次.jycs as 借阅次数,
本月借阅图书的学生人次.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月借阅图书的学生人次.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0238 本月借阅图书的学生人次'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0238'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0239', 'ive_si_sch_0239', '本月访问数字图书馆人次', TO_CLOB('
select
本月访问数字图书馆人次.xxbsm as 学校标识码,
本月访问数字图书馆人次.tjny as 统计年月,
本月访问数字图书馆人次.fwcs as 访问次数,
本月访问数字图书馆人次.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月访问数字图书馆人次.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0239 本月访问数字图书馆人次'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0239'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0240', 'ive_si_sch_0240', '本学期学生虚拟仿真实训课时', TO_CLOB('
select
本学期学生虚拟仿真实训课时.xxbsm as 学校标识码,
本学期学生虚拟仿真实训课时.xnxq as 学年学期,
本学期学生虚拟仿真实训课时.kss as 课时数,
本学期学生虚拟仿真实训课时.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本学期学生虚拟仿真实训课时.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0240 本学期学生虚拟仿真实训课时'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0240'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0241', 'ive_si_sch_0241', '本月实习学生人数', TO_CLOB('
select
本月实习学生人数.xxbsm as 学校标识码,
本月实习学生人数.tjny as 统计年月,
本月实习学生人数.xss as 学生数,
本月实习学生人数.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月实习学生人数.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0241 本月实习学生人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0241'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0242', 'ive_si_sch_0242', '本学期举办实习交流分享会活动次数', TO_CLOB('
select
本学期举办实习交流分享会活动次数.xxbsm as 学校标识码,
本学期举办实习交流分享会活动次数.xnxq as 学年学期,
本学期举办实习交流分享会活动次数.hdcs as 活动次数,
本学期举办实习交流分享会活动次数.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本学期举办实习交流分享会活动次数.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0242 本学期举办实习交流分享会活动次数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0242'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0243', 'ive_si_sch_0243', '本月实习报告提交数量', TO_CLOB('
select
本月实习报告提交数量.xxbsm as 学校标识码,
本月实习报告提交数量.tjny as 统计年月,
本月实习报告提交数量.tjbgs as 提交报告数,
本月实习报告提交数量.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月实习报告提交数量.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0243 本月实习报告提交数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0243'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0244', 'ive_si_sch_0244', '本月参加赛事活动的学生人次', TO_CLOB('
select
本月参加赛事活动的学生人次.xxbsm as 学校标识码,
本月参加赛事活动的学生人次.tjny as 统计年月,
本月参加赛事活动的学生人次.xss as 学生数,
本月参加赛事活动的学生人次.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月参加赛事活动的学生人次.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0244 本月参加赛事活动的学生人次'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0244'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0245', 'ive_si_sch_0245', '本学期获得技能证书的学生人数', TO_CLOB('
select
本学期获得技能证书的学生人数.xxbsm as 学校标识码,
本学期获得技能证书的学生人数.xnxq as 学年学期,
本学期获得技能证书的学生人数.xss as 学生数,
本学期获得技能证书的学生人数.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本学期获得技能证书的学生人数.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0245 本学期获得技能证书的学生人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0245'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0246', 'ive_si_sch_0246', '本月查看课表的学生人次', TO_CLOB('
select
本月查看课表的学生人次.xxbsm as 学校标识码,
本月查看课表的学生人次.tjny as 统计年月,
本月查看课表的学生人次.ckrc as 查看人次,
本月查看课表的学生人次.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月查看课表的学生人次.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0246 本月查看课表的学生人次'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0246'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0247', 'ive_si_sch_0247', '本学期参与选课的学生人数', TO_CLOB('
select
本学期参与选课的学生人数.xxbsm as 学校标识码,
本学期参与选课的学生人数.xnxq as 学年学期,
本学期参与选课的学生人数.xss as 学生数,
本学期参与选课的学生人数.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本学期参与选课的学生人数.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0247 本学期参与选课的学生人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0247'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0248', 'ive_si_sch_0248', '本学期自助打印成绩单的学生人次', TO_CLOB('
select
本学期自助打印成绩单的学生人次.xxbsm as 学校标识码,
本学期自助打印成绩单的学生人次.xnxq as 学年学期,
本学期自助打印成绩单的学生人次.dycs as 打印次数,
本学期自助打印成绩单的学生人次.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本学期自助打印成绩单的学生人次.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0248 本学期自助打印成绩单的学生人次'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0248'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0249', 'ive_si_sch_0249', '本月思政示范课程的授课教师人数', TO_CLOB('
select
本月思政示范课程的授课教师人数.xxbsm as 学校标识码,
本月思政示范课程的授课教师人数.tjny as 统计年月,
本月思政示范课程的授课教师人数.jss as 教师数,
本月思政示范课程的授课教师人数.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月思政示范课程的授课教师人数.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0249 本月思政示范课程的授课教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0249'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0250', 'ive_si_sch_0250', '本月网络课程的授课教师人数', TO_CLOB('
select
本月网络课程的授课教师人数.xxbsm as 学校标识码,
本月网络课程的授课教师人数.tjny as 统计年月,
本月网络课程的授课教师人数.jss as 教师数,
本月网络课程的授课教师人数.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月网络课程的授课教师人数.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0250 本月网络课程的授课教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0250'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0251', 'ive_si_sch_0251', '本月在智慧教室授课的教师人数', TO_CLOB('
select
本月在智慧教室授课的教师人数.xxbsm as 学校标识码,
本月在智慧教室授课的教师人数.tjny as 统计年月,
本月在智慧教室授课的教师人数.jss as 教师数,
本月在智慧教室授课的教师人数.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月在智慧教室授课的教师人数.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0251 本月在智慧教室授课的教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0251'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0252', 'ive_si_sch_0252', '本学期参与岗课赛证融通教学的教师人数', TO_CLOB('
select
本学期参与岗课赛证融通教学的教师人数.xxbsm as 学校标识码,
本学期参与岗课赛证融通教学的教师人数.xnxq as 学年学期,
本学期参与岗课赛证融通教学的教师人数.jss as 教师数,
本学期参与岗课赛证融通教学的教师人数.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本学期参与岗课赛证融通教学的教师人数.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0252 本学期参与岗课赛证融通教学的教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0252'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0253', 'ive_si_sch_0253', '本学期虚拟仿真实训项目数', TO_CLOB('
select
本学期虚拟仿真实训项目数.xxbsm as 学校标识码,
本学期虚拟仿真实训项目数.xnxq as 学年学期,
本学期虚拟仿真实训项目数.xms as 项目数,
本学期虚拟仿真实训项目数.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本学期虚拟仿真实训项目数.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0253 本学期虚拟仿真实训项目数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0253'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0254', 'ive_si_sch_0254', '本学期开发数字教材或虚拟仿真实训资源数量', TO_CLOB('
select
本学期开发数字教材或虚拟仿真实训资源数量.xxbsm as 学校标识码,
本学期开发数字教材或虚拟仿真实训资源数量.xnxq as 学年学期,
本学期开发数字教材或虚拟仿真实训资源数量.zysl as 资源数量,
本学期开发数字教材或虚拟仿真实训资源数量.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本学期开发数字教材或虚拟仿真实训资源数量.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0254 本学期开发数字教材或虚拟仿真实训资源数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0254'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0255', 'ive_si_sch_0255', '本学期应用数字教材或虚拟仿真数字资源的课程数量', TO_CLOB('
select
本学期应用数字教材或虚拟仿真数字资源的课程数量.xxbsm as 学校标识码,
本学期应用数字教材或虚拟仿真数字资源的课程数量.xnxq as 学年学期,
本学期应用数字教材或虚拟仿真数字资源的课程数量.kcs as 课程数,
本学期应用数字教材或虚拟仿真数字资源的课程数量.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本学期应用数字教材或虚拟仿真数字资源的课程数量.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0255 本学期应用数字教材或虚拟仿真数字资源的课程数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0255'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0256', 'ive_si_sch_0256', '本学期对接国家平台的资源数量', TO_CLOB('
select
本学期对接国家平台的资源数量.xxbsm as 学校标识码,
本学期对接国家平台的资源数量.xnxq as 学年学期,
本学期对接国家平台的资源数量.zys as 资源数,
本学期对接国家平台的资源数量.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本学期对接国家平台的资源数量.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0256 本学期对接国家平台的资源数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0256'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0257', 'ive_si_sch_0257', '本学期参与教学质量评价的课程数量', TO_CLOB('
select
本学期参与教学质量评价的课程数量.xxbsm as 学校标识码,
本学期参与教学质量评价的课程数量.xnxq as 学年学期,
本学期参与教学质量评价的课程数量.kcs as 课程数,
本学期参与教学质量评价的课程数量.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本学期参与教学质量评价的课程数量.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0257 本学期参与教学质量评价的课程数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0257'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0258', 'ive_si_sch_0258', '本学年办理离校手续的学生人数', TO_CLOB('
select
本学年办理离校手续的学生人数.xxbsm as 学校标识码,
本学年办理离校手续的学生人数.xn as 学年,
本学年办理离校手续的学生人数.xss as 学生数,
本学年办理离校手续的学生人数.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本学年办理离校手续的学生人数.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0258 本学年办理离校手续的学生人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0258'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0259', 'ive_si_sch_0259', '本月心理咨询人次', TO_CLOB('
select
本月心理咨询人次.xxbsm as 学校标识码,
本月心理咨询人次.tjny as 统计年月,
本月心理咨询人次.zxrc as 咨询人次,
本月心理咨询人次.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月心理咨询人次.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0259 本月心理咨询人次'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0259'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0260', 'ive_si_sch_0260', '本学期申请奖助贷的学生人数', TO_CLOB('
select
本学期申请奖助贷的学生人数.xxbsm as 学校标识码,
本学期申请奖助贷的学生人数.xnxq as 学年学期,
本学期申请奖助贷的学生人数.xss as 学生数,
本学期申请奖助贷的学生人数.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本学期申请奖助贷的学生人数.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0260 本学期申请奖助贷的学生人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0260'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0261', 'ive_si_sch_0261', '本学期参与教师考核人数', TO_CLOB('
select
本学期参与教师考核人数.xxbsm as 学校标识码,
本学期参与教师考核人数.xnxq as 学年学期,
本学期参与教师考核人数.jss as 教师数,
本学期参与教师考核人数.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本学期参与教师考核人数.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0261 本学期参与教师考核人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0261'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0262', 'ive_si_sch_0262', '本学期支出科研经费的项目数量', TO_CLOB('
select
本学期支出科研经费的项目数量.xxbsm as 学校标识码,
本学期支出科研经费的项目数量.xnxq as 学年学期,
本学期支出科研经费的项目数量.xms as 项目数,
本学期支出科研经费的项目数量.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本学期支出科研经费的项目数量.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0262 本学期支出科研经费的项目数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0262'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0263', 'ive_si_sch_0263', '本月仪器设备信息维护次数', TO_CLOB('
select
本月仪器设备信息维护次数.xxbsm as 学校标识码,
本月仪器设备信息维护次数.tjny as 统计年月,
本月仪器设备信息维护次数.whcs as 维护次数,
本月仪器设备信息维护次数.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月仪器设备信息维护次数.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0263 本月仪器设备信息维护次数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0263'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0264', 'ive_si_sch_0264', '本月统一身份认证登录人次', TO_CLOB('
select
本月统一身份认证登录人次.xxbsm as 学校标识码,
本月统一身份认证登录人次.tjny as 统计年月,
本月统一身份认证登录人次.dlcs as 登录次数,
本月统一身份认证登录人次.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月统一身份认证登录人次.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0264 本月统一身份认证登录人次'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0264'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0265', 'ive_si_sch_0265', '本月校本数据中心集成接口调用次数', TO_CLOB('
select
本月校本数据中心集成接口调用次数.xxbsm as 学校标识码,
本月校本数据中心集成接口调用次数.tjny as 统计年月,
本月校本数据中心集成接口调用次数.dycs as 调用次数,
本月校本数据中心集成接口调用次数.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月校本数据中心集成接口调用次数.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0265 本月校本数据中心集成接口调用次数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0265'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0266', 'ive_si_sch_0266', '本月视频巡课次数', TO_CLOB('
select
本月视频巡课次数.xxbsm as 学校标识码,
本月视频巡课次数.tjny as 统计年月,
本月视频巡课次数.xkcs as 巡课次数,
本月视频巡课次数.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月视频巡课次数.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0266 本月视频巡课次数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0266'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0267', 'ive_si_sch_0267', '本月巡课次数', TO_CLOB('
select
本月巡课次数.xxbsm as 学校标识码,
本月巡课次数.tjny as 统计年月,
本月巡课次数.xkcs as 巡课次数,
本月巡课次数.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月巡课次数.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0267 本月巡课次数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0267'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0268', 'ive_si_sch_0268', '本月信息化系统访问师生人次', TO_CLOB('
select
本月信息化系统访问师生人次.xxbsm as 学校标识码,
本月信息化系统访问师生人次.tjny as 统计年月,
本月信息化系统访问师生人次.fwcs as 访问次数,
本月信息化系统访问师生人次.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月信息化系统访问师生人次.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0268 本月信息化系统访问师生人次'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0268'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0269', 'ive_si_sch_0269', '本月一卡通人均消费金额', TO_CLOB('
select
本月一卡通人均消费金额.xxbsm as 学校标识码,
本月一卡通人均消费金额.tjny as 统计年月,
本月一卡通人均消费金额.xfje as 消费金额,
本月一卡通人均消费金额.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月一卡通人均消费金额.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0269 本月一卡通人均消费金额'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0269'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0270', 'ive_si_sch_0270', '本月“开具证明”在线事务办理数量', TO_CLOB('
select
本月开具证明在线事务办理数量.xxbsm as 学校标识码,
本月开具证明在线事务办理数量.tjny as 统计年月,
本月开具证明在线事务办理数量.blsl as 办理数量,
本月开具证明在线事务办理数量.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月开具证明在线事务办理数量.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0270 本月开具证明在线事务办理数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0270'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0271', 'ive_si_sch_0271', '本月“报销发票”在线事务办理数量', TO_CLOB('
select
本月报销发票在线事务办理数量.xxbsm as 学校标识码,
本月报销发票在线事务办理数量.tjny as 统计年月,
本月报销发票在线事务办理数量.blsl as 办理数量,
本月报销发票在线事务办理数量.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月报销发票在线事务办理数量.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0271 本月报销发票在线事务办理数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0271'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0272', 'ive_si_sch_0272', '本月校园动态发布数量', TO_CLOB('
select
本月校园动态发布数量.xxbsm as 学校标识码,
本月校园动态发布数量.tjny as 统计年月,
本月校园动态发布数量.fbsl as 发布数量,
本月校园动态发布数量.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月校园动态发布数量.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0272 本月校园动态发布数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0272'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0273', 'ive_si_sch_0273', '本月“后勤报修”在线事务办理数量', TO_CLOB('
select
本月后勤报修在线事务办理数量.xxbsm as 学校标识码,
本月后勤报修在线事务办理数量.tjny as 统计年月,
本月后勤报修在线事务办理数量.blsl as 办理数量,
本月后勤报修在线事务办理数量.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月后勤报修在线事务办理数量.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0273 本月后勤报修在线事务办理数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0273'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0274', 'ive_si_sch_0274', '本月“场地预约”在线事务办理数量', TO_CLOB('
select
本月场地预约在线事务办理数量.xxbsm as 学校标识码,
本月场地预约在线事务办理数量.tjny as 统计年月,
本月场地预约在线事务办理数量.blsl as 办理数量,
本月场地预约在线事务办理数量.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月场地预约在线事务办理数量.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0274 本月场地预约在线事务办理数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0274'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0275', 'ive_si_sch_0275', '本月校外网站访问人次', TO_CLOB('
select
本月校外网站访问人次.xxbsm as 学校标识码,
本月校外网站访问人次.tjny as 统计年月,
本月校外网站访问人次.fwcs as 访问次数,
本月校外网站访问人次.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月校外网站访问人次.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0275 本月校外网站访问人次'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0275'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0276', 'ive_si_sch_0276', '本月网络使用记录数', TO_CLOB('
select
本月网络使用记录数.xxbsm as 学校标识码,
本月网络使用记录数.tjny as 统计年月,
本月网络使用记录数.sycs as 使用次数,
本月网络使用记录数.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月网络使用记录数.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0276 本月网络使用记录数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0276'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0277', 'ive_si_sch_0277', '本月网络攻击识别次数', TO_CLOB('
select
本月网络攻击识别次数.xxbsm as 学校标识码,
本月网络攻击识别次数.tjny as 统计年月,
本月网络攻击识别次数.gjsbcs as 攻击识别次数,
本月网络攻击识别次数.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月网络攻击识别次数.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0277 本月网络攻击识别次数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0277'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0278', 'ive_si_sch_0278', '多媒体教室建设数量', TO_CLOB('
select
多媒体教室建设数量.xxbsm as 学校标识码,
多媒体教室建设数量.xn as 学年,
多媒体教室建设数量.jssl as 教室数量,
多媒体教室建设数量.zbjglxwdzqsl as 指标结果连续为0的周期数量,
多媒体教室建设数量.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0278 多媒体教室建设数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0278'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0279', 'ive_si_sch_0279', '实训室建设数量', TO_CLOB('
select
实训室建设数量.xxbsm as 学校标识码,
实训室建设数量.xn as 学年,
实训室建设数量.sxssl as 实训室数量,
实训室建设数量.zbjglxwdzqsl as 指标结果连续为0的周期数量,
实训室建设数量.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0279 实训室建设数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0279'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0280', 'ive_si_sch_0280', '本月智慧教室设备运行时长', TO_CLOB('
select
本月智慧教室设备运行时长.xxbsm as 学校标识码,
本月智慧教室设备运行时长.tjny as 统计年月,
本月智慧教室设备运行时长.yxsz as 运行时长,
本月智慧教室设备运行时长.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月智慧教室设备运行时长.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0280 本月智慧教室设备运行时长'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0280'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0281', 'ive_si_sch_0281', '本月进出校门或宿舍的刷卡人次', TO_CLOB('
select
本月进出校门或宿舍的刷卡人次.xxbsm as 学校标识码,
本月进出校门或宿舍的刷卡人次.tjny as 统计年月,
本月进出校门或宿舍的刷卡人次.skcs as 刷卡次数,
本月进出校门或宿舍的刷卡人次.zbjglxwdzqsl as 指标结果连续为0的周期数量,
本月进出校门或宿舍的刷卡人次.jsgzqbjbhfd as 较上个周期比较变化幅度
from ive_si_sch_0281 本月进出校门或宿舍的刷卡人次'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0281'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0283', 'ive_si_sch_0283', '本学年毕业生人数', TO_CLOB('
select
本学年毕业生人数.xxbsm as 学校标识码,
本学年毕业生人数.xn as 学年,
本学年毕业生人数.bysrs as 毕业生人数
from ive_si_sch_0283 本学年毕业生人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0283'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0284', 'ive_si_sch_0284', '本月在校教师人数', TO_CLOB('
select
本月在校教师人数.xxbsm as 学校标识码,
本月在校教师人数.tjny as 统计年月,
本月在校教师人数.jsrs as 教师人数,
本月在校教师人数.my as 每月
from ive_si_sch_0284 本月在校教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0284'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0285', 'ive_si_sch_0285', '本月学习进修活动数量', TO_CLOB('
select
本月学习进修活动数量.xxbsm as 学校标识码,
本月学习进修活动数量.tjny as 统计年月,
本月学习进修活动数量.hdsl as 活动数量
from ive_si_sch_0285 本月学习进修活动数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0285'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0286', 'ive_si_sch_0286', '本学期“科研和教研类别”项目数量', TO_CLOB('
select
本学期科研和教研类别项目数量.xxbsm as 学校标识码,
本学期科研和教研类别项目数量.xnxq as 学年学期,
本学期科研和教研类别项目数量.xmsl as 项目数量
from ive_si_sch_0286 本学期科研和教研类别项目数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0286'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0287', 'ive_si_sch_0287', '本学期学术讲座举办数量', TO_CLOB('
select
本学期学术讲座举办数量.xxbsm as 学校标识码,
本学期学术讲座举办数量.xnxq as 学年学期,
本学期学术讲座举办数量.jzsl as 讲座数量
from ive_si_sch_0287 本学期学术讲座举办数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0287'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0288', 'ive_si_sch_0288', '本学期“校企合作类别”项目数量', TO_CLOB('
select
本学期校企合作类别项目数量.xxbsm as 学校标识码,
本学期校企合作类别项目数量.xnxq as 学年学期,
本学期校企合作类别项目数量.xmsl as 项目数量
from ive_si_sch_0288 本学期校企合作类别项目数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0288'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0289', 'ive_si_sch_0289', '本学年教学总体计划拟制数量', TO_CLOB('
select
本学年教学总体计划拟制数量.xxbsm as 学校标识码,
本学年教学总体计划拟制数量.xn as 学年,
本学年教学总体计划拟制数量.jhnzsl as 计划拟制数量
from ive_si_sch_0289 本学年教学总体计划拟制数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0289'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0290', 'ive_si_sch_0290', '本学年专业设置数量', TO_CLOB('
select
本学年专业设置数量.xxbsm as 学校标识码,
本学年专业设置数量.xn as 学年,
本学年专业设置数量.zysl as 专业数量
from ive_si_sch_0290 本学年专业设置数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0290'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0291', 'ive_si_sch_0291', '本月在校学生人数', TO_CLOB('
select
本月在校学生人数.xxbsm as 学校标识码,
本月在校学生人数.tjny as 统计年月,
本月在校学生人数.xsrs as 学生人数,
本月在校学生人数.my as 每月
from ive_si_sch_0291 本月在校学生人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0291'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0292', 'ive_si_sch_0292', '本学期虚拟仿真实训项目数量', TO_CLOB('
select
本学期虚拟仿真实训项目数量.xxbsm as 学校标识码,
本学期虚拟仿真实训项目数量.xnxq as 学年学期,
本学期虚拟仿真实训项目数量.xmsl as 项目数量
from ive_si_sch_0292 本学期虚拟仿真实训项目数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0292'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0293', 'ive_si_sch_0293', '本月实习生人数', TO_CLOB('
select
本月实习生人数.xxbsm as 学校标识码,
本月实习生人数.tjny as 统计年月,
本月实习生人数.sxsrs as 实习生人数
from ive_si_sch_0293 本月实习生人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0293'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0294', 'ive_si_sch_0294', '本月赛事活动举办次数', TO_CLOB('
select
本月赛事活动举办次数.xxbsm as 学校标识码,
本月赛事活动举办次数.tjny as 统计年月,
本月赛事活动举办次数.hdsl as 活动数量
from ive_si_sch_0294 本月赛事活动举办次数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0294'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0295', 'ive_si_sch_0295', '本月思政示范课排课数量', TO_CLOB('
select
本月思政示范课排课数量.xxbsm as 学校标识码,
本月思政示范课排课数量.tjny as 统计年月,
本月思政示范课排课数量.pksl as 排课数量
from ive_si_sch_0295 本月思政示范课排课数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0295'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0296', 'ive_si_sch_0296', '本月网络课程课排课数量', TO_CLOB('
select
本月网络课程课排课数量.xxbsm as 学校标识码,
本月网络课程课排课数量.tjny as 统计年月,
本月网络课程课排课数量.pksl as 排课数量
from ive_si_sch_0296 本月网络课程课排课数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0296'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0297', 'ive_si_sch_0297', '本月在智慧教室上课的课程数量', TO_CLOB('
select
本月在智慧教室上课的课程数量.xxbsm as 学校标识码,
本月在智慧教室上课的课程数量.tjny as 统计年月,
本月在智慧教室上课的课程数量.kcsl as 课程数量
from ive_si_sch_0297 本月在智慧教室上课的课程数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0297'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0298', 'ive_si_sch_0298', '本学期课程数量', TO_CLOB('
select
本学期课程数量.xxbsm as 学校标识码,
本学期课程数量.xnxq as 学年学期,
本学期课程数量.kcsl as 课程数量
from ive_si_sch_0298 本学期课程数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0298'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0299', 'ive_si_sch_0299', '本学期科研项目数量', TO_CLOB('
select
本学期科研项目数量.xxbsm as 学校标识码,
本学期科研项目数量.xnxq as 学年学期,
本学期科研项目数量.xmsl as 项目数量
from ive_si_sch_0299 本学期科研项目数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0299'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0300', 'ive_si_sch_0300', '本月仪器设备总数', TO_CLOB('
select
本月仪器设备总数.xxbsm as 学校标识码,
本月仪器设备总数.tjny as 统计年月,
本月仪器设备总数.sbsl as 设备数量
from ive_si_sch_0300 本月仪器设备总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0300'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0301', 'ive_si_sch_0301', '在校师生人数', TO_CLOB('
select
在校师生人数.xxbsm as 学校标识码,
在校师生人数.tjny as 统计年月,
在校师生人数.ssrs as 师生人数
from ive_si_sch_0301 在校师生人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0301'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0302', 'ive_si_sch_0302', '本月集成接口总数', TO_CLOB('
select
本月集成接口总数.xxbsm as 学校标识码,
本月集成接口总数.tjny as 统计年月,
本月集成接口总数.jkzs as 接口总数
from ive_si_sch_0302 本月集成接口总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0302'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0303', 'ive_si_sch_0303', '本月在安装视频设备教室的上课数量', TO_CLOB('
select
本月在安装视频设备教室的上课数量.xxbsm as 学校标识码,
本月在安装视频设备教室的上课数量.tjny as 统计年月,
本月在安装视频设备教室的上课数量.kcsl as 课程数量
from ive_si_sch_0303 本月在安装视频设备教室的上课数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0303'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0304', 'ive_si_sch_0304', '本月排课数量', TO_CLOB('
select
本月排课数量.xxbsm as 学校标识码,
本月排课数量.tjny as 统计年月,
本月排课数量.pksl as 排课数量
from ive_si_sch_0304 本月排课数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0304'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0305', 'ive_si_sch_0305', '本学期在校教师人数', TO_CLOB('
select
本学期在校教师人数.xxbsm as 学校标识码,
本学期在校教师人数.xnxq as 学年学期,
本学期在校教师人数.jsrs as 教师人数
from ive_si_sch_0305 本学期在校教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0305'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ive_si_sch_0306', 'ive_si_sch_0306', '本学期在校学生人数', TO_CLOB('
select
本学期在校学生人数.xxbsm as 学校标识码,
本学期在校学生人数.xnxq as 学年学期,
本学期在校学生人数.xsrs as 学生人数
from ive_si_sch_0306 本学期在校学生人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ive_si_sch_0306'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'sch_label', 'sch_label', 'SCH标签宽表', TO_CLOB('
select
SCH标签宽表.hj as 合计,
SCH标签宽表.zycztrzj as 中央财政投入资金,
SCH标签宽表.dfgjcztrzj as 地方各级财政投入资金,
SCH标签宽表.jbftrzj as 举办方投入资金,
SCH标签宽表.xyqyzczj as 行业企业支持资金,
SCH标签宽表.xxzczj as 学校自筹资金,
SCH标签宽表.kcszsfkslxx as 课程思政示范课数量学校,
SCH标签宽表.jssxjdslxx as 建设实训基地数量学校,
SCH标签宽表.cyxzszdsdsl as 参与1X证书制度试点数量,
SCH标签宽表.cyxzsksrs as 参与1X证书考试人数,
SCH标签宽表.sygjghjcslxx as 使用国家规划教材数量学校,
SCH标签宽表.jxcgslxx as 教学成果数量学校,
SCH标签宽表.sjsjjxdkcsl as 涉及实践教学的课程数量,
SCH标签宽表.xscjjshjszyq as 学生参加竞赛获奖数专业群,
SCH标签宽表.dkjybyssl as 对口就业毕业生数量,
SCH标签宽表.jskjcxtd as 建设科技创新团队,
SCH标签宽表.jscjrhptsl as 建设产教融合平台数量,
SCH标签宽表.jscxcyptsl as 建设创新创业平台数量,
SCH标签宽表.rwyshkxxmsl as 人文与社会科学项目数量,
SCH标签宽表.zrkxxmsl as 自然科学项目数量,
SCH标签宽表.gcyjsxmsl as 工程与技术项目数量,
SCH标签宽表.sqfmzlsl as 申请发明专利数量,
SCH标签宽表.sqzscqsl as 申请知识产权数量,
SCH标签宽')||TO_CLOB('表.kycghjsl as 科研成果获奖数量,
SCH标签宽表.zrjssxx as 专任教师数学校,
SCH标签宽表.ssszzrjsslxx as 双师素质专任教师数量学校,
SCH标签宽表.yjsysxwdzrjsslxx as 研究生以上学位的专任教师数量学校,
SCH标签宽表.gjzwzrjsslxx as 高级职务专任教师数量学校,
SCH标签宽表.jzjssl as 兼职教师数量,
SCH标签宽表.yqygzjldzrjsrsxx as 有企业工作经历的专任教师人数学校,
SCH标签宽表.cjjxpxdjsrs as 参加进修培训的教师人数,
SCH标签宽表.pyxnzyqdtrsl as 培养校内专业群带头人数量,
SCH标签宽表.pqxyqyljrcsl as 聘请行业企业领军人才数量,
SCH标签宽表.jsssxjspypxjd as 建设双师型教师培养培训基地,
SCH标签宽表.jyzyjndjzsdjssl as 具有职业技能等级证书的教师数量,
SCH标签宽表.jscjjxnlbshjsxx as 教师参加教学能力比赛获奖数学校,
SCH标签宽表.hprychdjssxx as 获评荣誉称号的教师数学校,
SCH标签宽表.cyxysl as 产业学院数量,
SCH标签宽表.yxxjmhzdxyqys as 与学校紧密合作的行业企业数,
SCH标签宽表.kfxnfzsxjxxm as 开发虚拟仿真实训教学项目,
SCH标签宽表.xqhzkfkcsl as 校企合作开发课程数量,
SCH标签宽表.xqhzkfjcslxx as 校企合作开发教材数量学校,
SCH标签宽表.zdzyjxbzsl as 制定专业教学标准数量,
SCH标签宽表.ytqyjsxw')||TO_CLOB('sjjdsl as 依托企业建设校外实践基地数量,
SCH标签宽表.kzxdxtzdzyqsl as 开展现代学徒制的专业群数量,
SCH标签宽表.xxwqyjsfwnsr as 学校为企业技术服务年收入,
SCH标签宽表.kdwtgfwdxnfzjdsl as 可对外提供服务的虚拟仿真基地数量,
SCH标签宽表.xnfzjddwfwrs as 虚拟仿真基地对外服务人数,
SCH标签宽表.xnfzjddwfwkss as 虚拟仿真基地对外服务课时数,
SCH标签宽表.szzyjnjdjgsl as 设置职业技能鉴定机构数量,
SCH标签宽表.cjxxzlbfzsk as 创建学校治理办法知识库,
SCH标签宽表.dzzlbztx as 打造质量保障体系,
SCH标签宽表.kzzyjxzggzzcs as 开展专业教学诊改工作总次数,
SCH标签宽表.kzxsfwzgggzcs as 开展学生服务诊改给工作次数,
SCH标签宽表.kzjsfwzgggzcs as 开展教师服务诊改给工作次数,
SCH标签宽表.mrwlpjsyll as 每日网络平均使用流量,
SCH标签宽表.xbsjzxjcsjksl as 校本数据中心集成数据库数量,
SCH标签宽表.tysfrzjcywxtsl as 统一身份认证集成业务系统数量,
SCH标签宽表.jsyjf as 建设云机房,
SCH标签宽表.jsdmtjs as 建设多媒体教室,
SCH标签宽表.jwjxpxjssl as 境外进修培训教师数量,
SCH标签宽表.gjjlxmcyxsrs as 国际交流项目参与学生人数,
SCH标签宽表.ljpycglxsrs as 累计培养出国留学生人数,
SCH标签宽表.ljpylhlxsrs as')||TO_CLOB(' 累计培养来华留学生人数,
SCH标签宽表.swhzxm as 涉外合作项目,
SCH标签宽表.zjzwzjlhgzs as 组建中外专家联合工作室,
SCH标签宽表.xsgjjshjs as 学生国际竞赛获奖数,
SCH标签宽表.kcszsfkblxx as 课程思政示范课比例学校,
SCH标签宽表.jsxnscxsxjdsl as 建设校内生产性实训基地数量,
SCH标签宽表.xzspjhql as 1X证书平均获取率,
SCH标签宽表.jxcgjhjsxx as 教学成果奖获奖数学校,
SCH标签宽表.sjjxdkczb as 实践教学的课程占比,
SCH标签宽表.gjwzyjndshjs as 国境外职业技能大赛获奖数,
SCH标签宽表.jndshjsxx as 技能大赛获奖数学校,
SCH标签宽表.sjzyyxjndshjs as 省级职业院校技能大赛获奖数,
SCH标签宽表.yjbysccjyl as 应届毕业生初次就业率,
SCH标签宽表.sjyscxcyptsl as 省级以上创新创业平台数量,
SCH标签宽表.sjdxscxcyxlxmsl as 省级大学生创新创业训练项目数量,
SCH标签宽表.sjjyscxcydshjsl as 省级及以上创新创业大赛获奖数量,
SCH标签宽表.cdgjjkyxmsl as 承担国家级科研项目数量,
SCH标签宽表.cdsjkyxmsl as 承担省级科研项目数量,
SCH标签宽表.sqfmzlslxx as 授权发明专利数量学校,
SCH标签宽表.hpgjjyxcgjsl as 获评国家级优秀成果奖数量,
SCH标签宽表.hpsjyxcgjsl as 获评省级优秀成果奖数量,
SCH标签宽表.zrjsssbxx as ')||TO_CLOB('专任教师生师比学校,
SCH标签宽表.ssszzrjsblxx as 双师素质专任教师比例学校,
SCH标签宽表.yjsysxwzrjsblxx as 研究生以上学位专任教师比例学校,
SCH标签宽表.gjzwzrjsblxx as 高级职务专任教师比例学校,
SCH标签宽表.qyjzjszykkszb as 企业兼职教师专业课课时占比,
SCH标签宽表.yqysjdzrjsblxx as 有企业实践的专任教师比例学校,
SCH标签宽表.ygjwgzjldzrjsrs as 有国境外工作经历的专任教师人数,
SCH标签宽表.cjjxpxdjsbl as 参加进修培训的教师比例,
SCH标签宽表.jyzyjnzsdjssl as 具有职业技能证书的教师数量,
SCH标签宽表.gjjjxnlbshjsxx as 国家级教学能力比赛获奖数学校,
SCH标签宽表.sjjxnlbshjsxx as 省级教学能力比赛获奖数学校,
SCH标签宽表.hsjysrydjssxx as 获省级以上荣誉的教师数学校,
SCH标签宽表.yhzqydzysbl as 有合作企业的专业数比例,
SCH标签宽表.xqhzqysl as 校企合作企业数量,
SCH标签宽表.zhzscq as 转化知识产权,
SCH标签宽表.gjszchcpsjzd as 供技术支持和产品升级指导,
SCH标签宽表.cyjsxypxjd as 参与建设行业培训基地,
SCH标签宽表.kzzyjxzggzfgl as 开展专业教学诊改工作覆盖率,
SCH标签宽表.kzxsfwzggzfgl as 开展学生服务诊改工作覆盖率,
SCH标签宽表.kzjsfwzggzfgl as 开展教师服务诊改工作覆盖率,
SCH标签宽表.jst')||TO_CLOB('sxxhxtsl as 建设特色信息化系统数量,
SCH标签宽表.zxkfkczb as 在线开放课程占比,
SCH标签宽表.rwzqwcd as 任务终期完成度,
SCH标签宽表.sryszxl as 收入预算执行率,
SCH标签宽表.zcyszxl as 支出预算执行率,
SCH标签宽表.rwndwcl as 任务年度完成率,
SCH标签宽表.jszyrcpyms as 建设专业人才培养模式,
SCH标签宽表.jsrcpyfasl as 建设人才培养方案数量,
SCH标签宽表.jsshfwpxjdsl as 建设社会服务培训基地数量,
SCH标签宽表.zyxljxjyhgwjnpxrs as 专业学历继续教育和岗位技能培训人数,
SCH标签宽表.gszjsxsyrcsl as 高素质技术型适用人才数量,
SCH标签宽表.cjdfcyjsxmsl as 承建地方产业建设项目数量,
SCH标签宽表.xqgjpxrs as 校企国际培训人数,
SCH标签宽表.cyzyjxbztgcs as 参与专业教学标准推广次数,
SCH标签宽表.cyzyjyggyjhdcs as 参与职业教育改革研究活动次数,
SCH标签宽表.cyxdxtzsdzysl as 参与现代学徒制试点专业数量,
SCH标签宽表.xxzsjhwcl as 学校招生计划完成率,
SCH标签宽表.qtssdbxwhdrtd as 全体师生对本校文化的认同度,
SCH标签宽表.zxsmyd as 在校生满意度,
SCH标签宽表.bysmyd as 毕业生满意度,
SCH标签宽表.jzgmyd as 教职工满意度,
SCH标签宽表.yrdwmyd as 用人单位满意度,
SCH标签宽表.jzmyd as 家长满意度,
S')||TO_CLOB('CH标签宽表.kzrq as 快照日期,
SCH标签宽表.xxbsm as 学校标识码
from sch_label SCH标签宽表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'sch_label'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'spe_label', 'spe_label', 'SPE标签宽表', TO_CLOB('
select
SPE标签宽表.zrjsszyq as 专任教师数专业群,
SPE标签宽表.ssszzrjsslzyq as 双师素质专任教师数量专业群,
SPE标签宽表.yjsysxwdzrjsslzyq as 研究生以上学位的专任教师数量专业群,
SPE标签宽表.gjzwzrjsslzyq as 高级职务专任教师数量专业群,
SPE标签宽表.yqygzjldzrjsrszyq as 有企业工作经历的专任教师人数专业群,
SPE标签宽表.jscjjxnlbshjszyq as 教师参加教学能力比赛获奖数专业群,
SPE标签宽表.hprychdjsszyq as 获评荣誉称号的教师数专业群,
SPE标签宽表.jxcgslzyq as 教学成果数量专业群,
SPE标签宽表.jszxkfkc as 建设在线开放课程,
SPE标签宽表.kcszsfkslzyq as 课程思政示范课数量专业群,
SPE标签宽表.pyzyqdtrsl as 培养专业群带头人数量,
SPE标签宽表.sygjghjcslzyq as 使用国家规划教材数量专业群,
SPE标签宽表.xqhzkfjcslzyq as 校企合作开发教材数量专业群,
SPE标签宽表.xscjjshjsxx as 学生参加竞赛获奖数学校,
SPE标签宽表.jssxjdslzyq as 建设实训基地数量专业群,
SPE标签宽表.ljlxdjgxmsl as 累计立项的教改项目数量,
SPE标签宽表.ljlxdkyxmsl as 累计立项的科研项目数量,
SPE标签宽表.ljsqzlsl as 累计申请专利数量,
SPE标签宽表.ljsqrjzzqsl as 累计申请软件著作权数量,
SPE标签宽表.zrjsssbzyq as 专任教师')||TO_CLOB('生师比专业群,
SPE标签宽表.ssszzrjsblzyq as 双师素质专任教师比例专业群,
SPE标签宽表.yjsysxwzrjsblzyq as 研究生以上学位专任教师比例专业群,
SPE标签宽表.gjzwzrjsblzyq as 高级职务专任教师比例专业群,
SPE标签宽表.yqysjdzrjsblzyq as 有企业实践的专任教师比例专业群,
SPE标签宽表.gjjjxnlbshjszyq as 国家级教学能力比赛获奖数专业群,
SPE标签宽表.sjjxnlbshjszyq as 省级教学能力比赛获奖数专业群,
SPE标签宽表.hsjysrydjsszyq as 获省级以上荣誉的教师数专业群,
SPE标签宽表.jxcgjhjszyq as 教学成果奖获奖数专业群,
SPE标签宽表.kcszsfkblzyq as 课程思政示范课比例专业群,
SPE标签宽表.jndshjszyq as 技能大赛获奖数专业群,
SPE标签宽表.sjjysjyxmsl as 省级及以上教研项目数量,
SPE标签宽表.sjyskyxmsl as 省级以上科研项目数量,
SPE标签宽表.sqfmzlslzyq as 授权发明专利数量专业群,
SPE标签宽表.kzrq as 快照日期,
SPE标签宽表.zyqbm as 专业群编码
from spe_label SPE标签宽表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'spe_label'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 't_da_model_column_extend', 't_da_model_column_extend', '', TO_CLOB('
select
t_da_model_column_extend.primary_column_tag as 是否主键字段
from t_da_model_column_extend t_da_model_column_extend'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 't_da_model_column_extend'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 't_da_model_column_info', 't_da_model_column_info', '模型字段信息', TO_CLOB('
select
模型字段信息.model_id as 模型ID,
模型字段信息.column_id as 字段ID,
模型字段信息.syn_value as 同义词
from t_da_model_column_info 模型字段信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 't_da_model_column_info'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 't_da_model_info', 't_da_model_info', '模型信息', TO_CLOB('
select
模型信息.model_id as 模型ID,
模型信息.syn_value as 同义词
from t_da_model_info 模型信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 't_da_model_info'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 't_da_model_relation', 't_da_model_relation', '模型关系表', TO_CLOB('
select
模型关系表.model_id as 主模型ID,
模型关系表.fields as 主模型字段,
模型关系表.mapped_model_id as 关联模型,
模型关系表.mapped_fields as 关联模型字段
from t_da_model_relation 模型关系表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 't_da_model_relation'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_label', 'tea_label', 'TEA标签宽表', TO_CLOB('
select
TEA标签宽表.kzrq as 快照日期,
TEA标签宽表.zgh as 教职工号
from tea_label TEA标签宽表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_label'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_label', 'und_label', 'UND标签宽表', TO_CLOB('
select
UND标签宽表.kzrq as 快照日期,
UND标签宽表.xh as 学号
from und_label UND标签宽表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_label'
);