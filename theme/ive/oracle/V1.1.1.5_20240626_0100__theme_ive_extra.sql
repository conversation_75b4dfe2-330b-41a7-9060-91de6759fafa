/*
 Description		: [职教一体化(IVE)]主题域上报数据模型确权记录
 Author				: 王平
 Date				: 2024-05-26 01:00:00
 Lowcode Version	: V1.1.1_Beta3
 Database           : oracle
*/

--上报数据模型确权记录

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244244696003851', '1', '学校基本数据', '学校办公室', '校办',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244244696003851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244244696004851', '1', '信息化系统建设数据', '学校基础数据平台', '信息中心',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244244696004851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244244696005851', '1', '信息化系统访问记录数据', '教务、科研、学工等系统', '信息中心',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244244696005851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244244696006851', '1', '学校特色信息化系统应用数据', '学校办公室', '信息中心',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244244696006851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244244696007851', '1', '课程基本数据', '教务管理系统', '教务处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244244696007851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244244696008851', '1', '课程建设数据', '教务管理系统', '教务处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244244696008851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244244696009851', '1', '国家平台资源对接数据', '数据中台', '信息中心',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244244696009851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244246631001851', '1', '实习基础数据', '实习管理系统', '招就办',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244246631001851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244246631002851', '1', '实习保险购买情况数据', '实习管理系统', '招就办',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244246631002851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244246631003851', '1', '实习违规行为情况数据', '实习管理系统', '招就办',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244246631003851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244246631004851', '1', '实习报告记录数据', '实习管理系统', '招就办',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244246631004851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244246631005851', '1', '专业建设情况数据', '教务管理系统', '教务处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244246631005851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244253659001851', '1', '专业设置数据', '教务管理系统', '教务处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244253659001851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708245301002001851', '1', '实训基地数据', '教务管理系统', '教务处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708245301002001851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708245855966001472', '1', '实训室基本数据', '教务管理系统', '教务处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708245855966001472'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708245855967001472', '1', '实训项目数据', '教务管理系统', '教务处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708245855967001472'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708245855967002472', '1', '实训教学过程数据', '教务管理系统', '教务处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708245855967002472'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708245861091001472', '1', '虚拟仿真基地对外服务数据', '虚拟仿真实训教学管理及资源共享平台', '教务处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708245861091001472'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708245864196001472', '1', '排课数据', '教务管理系统', '教务处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708245864196001472'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708246441044001472', '1', '学生选课数据', '教务管理系统', '教务处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708246441044001472'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708246441361001472', '1', '访问教学资源情况数据', '资源管理系统', '教务处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708246441361001472'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708247583056001119', '1', '网络课程在线学习记录数据', '网络教学平台', '教务处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708247583056001119'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708247583057001119', '1', '课堂互动数据', '网络教学平台、智慧教室管理平台等', '教务处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708247583057001119'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708247583057002119', '1', '访问数字图书馆资源数据', '图书管理系统、网络电子资源平台', '教务处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708247583057002119'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708247583057003119', '1', '教学质量与评价数据', '教学质量管理平台', '教务处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708247583057003119'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708247583057004119', '1', '巡课数据', '教务管理系统', '教务处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708247583057004119'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708308602161001885', '1', '数字资源基本数据', '教学资源管理系统', '教务处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708308602161001885'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708308602161002885', '1', '虚拟仿真数字资源基本数据', '虚拟仿真实训教学管理及资源共享平台', '教务处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708308602161002885'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708308603247001885', '1', '产学合作数据', '校企合作处', '校企合作处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708308603247001885'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708311237486001885', '1', '1+X证书数据', '教务管理系统', '教务处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708311237486001885'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708311237487001885', '1', '职业技能鉴定机构数据', '校企合作处', '校企合作处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708311237487001885'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708311239164001885', '1', '总体计划数据', '教务管理系统', '教务处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708311239164001885'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708311239164002885', '1', '计划课程数据', '教务管理系统', '教务处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708311239164002885'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708311239164003885', '1', '教材基本数据', '教务管理系统', '教务处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708311239164003885'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708311239164004885', '1', '获奖教材数据', '教学管理系统或科研管理系统', '教务处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708311239164004885'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708326045489001245', '1', '教室基本数据', '教务管理系统', '教务处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708326045489001245'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708326045490001245', '1', '教室视频流数据', '教务管理系统', '教务处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708326045490001245'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708326045490002245', '1', '教师画像数据', '大数据中心', '信息中心',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708326045490002245'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708326045490003245', '1', '教师企业兼职/实践数据', '人事系统', '人事处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708326045490003245'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708326045490004245', '1', '学习进修数据', '人事系统', '人事处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708326045490004245'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708326045490005245', '1', '教师考核数据', '人事系统', '人事处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708326045490005245'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708326045490006245', '1', '学生画像数据', '大数据中心', '信息中心',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708326045490006245'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708326045490007245', '1', '学生技能证书数据', '教务管理系统', '教务处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708326045490007245'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708326045490008245', '1', '综合成绩与评价数据', '学工系统', '学工处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708326045490008245'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708326045491001245', '1', '奖助贷申请数据', '学工系统', '学工处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708326045491001245'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708326045491002245', '1', '社团（协会）基本数据', '团委', '团委',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708326045491002245'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708326045491003245', '1', '学生参与社团数据', '团委', '团委',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708326045491003245'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708326045491004245', '1', '举办社团活动数据', '团委', '团委',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708326045491004245'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708326045491005245', '1', '参加社团活动数据', '团委', '团委',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708326045491005245'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708326045491006245', '1', '心理咨询记录数据', '心理系统', '学工处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708326045491006245'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708326045491007245', '1', '离校手续办理数据', '离校系统', '招就办',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708326045491007245'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708326045491008245', '1', '毕业去向【升学】数据', '离校系统', '招就办',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708326045491008245'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708326045491009245', '1', '毕业去向【就业】数据', '离校系统', '招就办',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708326045491009245'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708326045491010245', '1', '毕业去向【未就业】数据', '离校系统', '招就办',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708326045491010245'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219918001851', '1', '党组织情况基础数据', '党建系统', '党政办',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219918001851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219919001851', '1', '党员发展数据', '党建系统', '党政办',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219919001851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219919002851', '1', '党员干部学习数据', '党建系统', '党政办',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219919002851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219919003851', '1', '党课数据', '党建系统', '党政办',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219919003851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219919004851', '1', '党员日常活动数据', '党建系统', '党政办',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219919004851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219919005851', '1', '三会数据', '党建系统', '党政办',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219919005851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219919006851', '1', '党员主题党日数据', '党建系统', '党政办',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219919006851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219919007851', '1', '思政活动数据', '党建系统', '党政办',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219919007851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219919008851', '1', '思政教职工数据', '党建系统', '党政办',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219919008851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219919009851', '1', '仪器设备基本数据', '资产管理系统', '资产处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219919009851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219919010851', '1', '智慧教室设备运行', '设备监控系统', '教务处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219919010851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219919011851', '1', '科研项目基本数据', '科研系统', '科研处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219919011851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219919012851', '1', '科研项目研究活动数据', '科研系统', '科研处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219919012851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219919013851', '1', '科研经费支出数据', '科研系统', '科研处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219919013851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219919014851', '1', '论文发表数据', '科研系统', '科研处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219919014851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219919015851', '1', '专利发布数据', '科研系统', '科研处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219919015851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219919016851', '1', '专著发表数据', '科研系统', '科研处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219919016851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219919017851', '1', '学术讲座数据', '科研系统', '科研处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219919017851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219919018851', '1', '学生消费数据', '一卡通', '信息中心',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219919018851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219919019851', '1', '一卡通认证数据', '一卡通', '信息中心',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219919019851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219919020851', '1', '在线事务办理数据', '一网通办平台', '信息中心',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219919020851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219919021851', '1', '校园动态信息数据', '网站群', '组织部',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219919021851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219919022851', '1', '图书基本数据', '图书管理系统', '信息中心',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219919022851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219919023851', '1', '图书借阅数据', '图书管理系统', '信息中心',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219919023851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219919024851', '1', '期刊基本数据', '图书管理系统', '信息中心',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219919024851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219919025851', '1', '校内学生赛事活动数据', '学工系统', '学工处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219919025851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219919026851', '1', '网络安全管理数据', '网络安全监控系统', '信息中心',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219919026851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219919027851', '1', '校本数据中心数据集成情况数据', '校本数据中心', '信息中心',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219919027851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219919028851', '1', '数据标准与信息系统映射关系数据', '手动填报', '信息中心',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219919028851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219919029851', '1', '信息化系统集成情况数据', '统一身份认证平台', '信息中心',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219919029851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219920001851', '1', '统一用户认证登录数据', '统一身份认证平台', '信息中心',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219920001851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219920002851', '1', '实习交流分享活动记录', null, '就业办',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219920002851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219920003851', '1', '学生查看课表记录', '教务系统', '教务处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219920003851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219920004851', '1', '学生打印成绩单记录', '教务系统', '教务处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219920004851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219920005851', '1', '教师参与岗课赛证融通的记录', null, '人事处',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219920005851'
);

INSERT INTO abd_sch_sbsjmxqqjl (id, xxbsm, sjbzw, zyly, zrdw, qqrq, create_by, create_time, update_by, update_time) 
SELECT '1708244219920006851', '1', '校外网站访问记录', '统一身份认证平台', '信息中心',  '2022-09-01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01', 'lowcodeadmin', TIMESTAMP '2024-05-28 00:00:01'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM abd_sch_sbsjmxqqjl
    WHERE id = '1708244219920006851'
);
