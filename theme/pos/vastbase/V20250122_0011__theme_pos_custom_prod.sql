
CREATE OR R<PERSON>LACE FORCE NONEDITIONABLE VIEW "V_ABD_MEN_DSJBXX"  AS 
  SELECT * 
  FROM ABD_MEN_DSJBXX 
  WHERE KZRQ = TO_CHAR(SYSDATE, 'yyyy-MM-dd');
  
 

CREATE OR REPLACE FORCE NONEDITIONABLE VIEW "V_ABD_POS_YJSJBXX"  AS 
  SELECT * FROM ABD_POS_YJSJBXX 
  WHERE KZRQ = TO_CHAR(SYSDATE, 'yyyy-MM-dd');
  
 

CREATE OR REPLACE FORCE NONEDITIONABLE VIEW "V_ABD_TEA_JZGJBXX" AS 
  SELECT *
  FROM ABD_TEA_JZGJBXX 
  WHERE KZRQ = TO_CHAR(SYSDATE, 'yyyy-MM-dd');
  
 

CREATE OR REPLACE FORCE NONEDITIONABLE VIEW "V_ABD_UND_BZKSJBXX"  AS 
  SELECT * 
  FROM ABD_UND_BZKSJBXX 
  WHERE KZRQ = TO_CHAR(SYSDATE, 'yyyy-MM-dd');
  
 

CREATE OR REPLACE FORCE NONEDITIONABLE VIEW "V_COL_LABEL" AS 
  SELECT *  
  FROM COL_LABEL 
  WHERE kzrq = TO_CHAR(SYSDATE, 'yyyy-MM-dd');
  
 

CREATE OR REPLACE FORCE NONEDITIONABLE VIEW "V_COU_LABEL" AS 
  SELECT *  
  FROM COU_LABEL 
  WHERE kzrq = TO_CHAR(SYSDATE, 'yyyy-MM-dd'); 
  

CREATE OR REPLACE FORCE NONEDITIONABLE VIEW "V_MEN_LABEL" AS 
  SELECT * FROM MEN_LABEL 
  WHERE kzrq = TO_CHAR(SYSDATE, 'yyyy-MM-dd'); 
  
 

CREATE OR REPLACE FORCE NONEDITIONABLE VIEW "V_POS_LABEL"  AS 
  SELECT *
  FROM POS_LABEL 
  WHERE kzrq = TO_CHAR(SYSDATE, 'yyyy-MM-dd');
  
 

CREATE OR REPLACE FORCE NONEDITIONABLE VIEW "V_SCH_LABEL" AS 
  SELECT * 
 FROM SCH_LABEL 
WHERE kzrq = TO_CHAR(SYSDATE, 'yyyy-MM-dd');



CREATE OR REPLACE FORCE NONEDITIONABLE VIEW "V_SUB_LABEL" AS 
  SELECT * 
 FROM SUB_LABEL 
WHERE kzrq = TO_CHAR(SYSDATE, 'yyyy-MM-dd');



CREATE OR REPLACE FORCE NONEDITIONABLE VIEW "V_TEA_LABEL"  AS 
  SELECT *
 FROM TEA_LABEL 
 WHERE kzrq = TO_CHAR(SYSDATE, 'yyyy-MM-dd');



CREATE OR REPLACE FORCE NONEDITIONABLE VIEW "V_TRA_LABEL" AS 
  SELECT * 
 FROM TRA_LABEL 
WHERE kzrq = TO_CHAR(SYSDATE, 'yyyy-MM-dd');



CREATE OR REPLACE FORCE NONEDITIONABLE VIEW "V_UND_LABEL" AS 
  SELECT * 
 FROM UND_LABEL 
 WHERE kzrq = TO_CHAR(SYSDATE, 'yyyy-MM-dd');
 
 
 
 