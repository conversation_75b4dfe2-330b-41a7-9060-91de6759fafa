----------------------------------------------↓ 公共增量 ↓----------------------------------------------
-- 新增【模型分类】:1856966711765905408
insert into lowcode_model_classify (sys_flag,id,classify_code,classify_name,status,create_by,create_time,update_by,update_time)
select 0,'1856966711765905408','extcm1856966711765905409','学术论文',1,'dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
from dual
where not exists (
	select 1
	from lowcode_model_classify
	where id = '1856966711765905408');
-- 新增【模型分类扩展表】:1856966711765905408
insert into t_da_model_classify_extend (classify_id,classify_type,id,create_by,create_time,update_by,update_time)
select '1856966711765905408','cm','1856966711765905408','dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
from dual
where not exists (
	select 1
	from t_da_model_classify_extend
	where id = '1856966711765905408');
----------------------------------------------↓ 模型 main-abdnrgjzyqkqd ↓----------------------------------------------
--模型：国际重要期刊清单 ABD_NR_GJZYQKQD main-abdnrgjzyqkqd
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdnrgjzyqkqd'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdnrgjzyqkqd版本号:'||v_version);
	IF v_version >= 'V1.0.0.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdnrgjzyqkqd的本次增量版本号:V1.0.0,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1857230556027695105','main-abdnrgjzyqkqd','V1.0.0','0','upgrade','add','国际重要期刊清单','cm','dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1857230556027695105');
		-- 新增【模型使用范围】:1731578849245001243
		insert into t_da_model_theme (model_id,theme_code,id,create_by,create_time,update_by,update_time)
		select 'main-abdnrgjzyqkqd','POS','1731578849245001243','dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
		from dual
		where not exists (
			select 1
			from t_da_model_theme
			where id = '1731578849245001243');
		-- 新增【模型】：main-abdnrgjzyqkqd
		insert into lowcode_model (model_app,publish_time,sys_flag,model_class,permit_ext_flag,extend_flag,model_label,model_name,model_table,model_classify,virtual_model,biz_model,order_index,id,status,create_by,create_time,update_by,update_time)
		select 'main',TIMESTAMP '2024-11-14 16:40:03',0,'com.wisedu.lowcode4j.main.po.Abdnrgjzyqkqd',1,1,'国际重要期刊清单','abdnrgjzyqkqd','ABD_NR_GJZYQKQD','extcm1856966711765905409',1,0,627,'main-abdnrgjzyqkqd',1,'dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
		from dual
		where not exists (
			select 1
			from lowcode_model
			where id = 'main-abdnrgjzyqkqd');
		BEGIN
		EXECUTE IMMEDIATE 'CREATE TABLE ABD_NR_GJZYQKQD(ID VARCHAR2(100) DEFAULT SYS_GUID() primary key ,CREATE_BY VARCHAR2(100),CREATE_TIME DATE,UPDATE_BY VARCHAR2(100),UPDATE_TIME DATE)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-955) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on table ABD_NR_GJZYQKQD is ''国际重要期刊清单''';
		END;
		-- 新增【模型扩展】：main-abdnrgjzyqkqd
		insert into t_da_model_extend (model_id,use_num,model_version,model_enable,entity_id,internal_version,id,model_type,data_source,create_by,create_time,update_by,update_time)
		select 'main-abdnrgjzyqkqd',0,'V1.0.0','1','SCHOOL','0','main-abdnrgjzyqkqd','cm','软科','dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
		from dual
		where not exists (
			select 1
			from t_da_model_extend
			where id = 'main-abdnrgjzyqkqd');
		-- 新增【字段扩展】：main-abdnrgjzyqkqd-createBy
		insert into t_da_model_column_extend (time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdnrgjzyqkqd',0,'main-abdnrgjzyqkqd-createBy','main-abdnrgjzyqkqd-createBy',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdnrgjzyqkqd-createBy');
		-- 新增【字段扩展】：main-abdnrgjzyqkqd-createTime
		insert into t_da_model_column_extend (time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdnrgjzyqkqd',0,'main-abdnrgjzyqkqd-createTime','main-abdnrgjzyqkqd-createTime',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdnrgjzyqkqd-createTime');
		-- 新增【字段扩展】：main-abdnrgjzyqkqd-id
		insert into t_da_model_column_extend (time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,primary_column_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdnrgjzyqkqd',0,'main-abdnrgjzyqkqd-id','main-abdnrgjzyqkqd-id',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdnrgjzyqkqd-id');
		-- 新增【字段扩展】：main-abdnrgjzyqkqd-kwmc
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdnrgjzyqkqd',0,'main-abdnrgjzyqkqd-kwmc','main-abdnrgjzyqkqd-kwmc',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdnrgjzyqkqd-kwmc');
		-- 新增【字段扩展】：main-abdnrgjzyqkqd-updateBy
		insert into t_da_model_column_extend (time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdnrgjzyqkqd',0,'main-abdnrgjzyqkqd-updateBy','main-abdnrgjzyqkqd-updateBy',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdnrgjzyqkqd-updateBy');
		-- 新增【字段扩展】：main-abdnrgjzyqkqd-updateTime
		insert into t_da_model_column_extend (time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdnrgjzyqkqd',0,'main-abdnrgjzyqkqd-updateTime','main-abdnrgjzyqkqd-updateTime',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdnrgjzyqkqd-updateTime');
		-- 新增【字段】：main-abdnrgjzyqkqd-createBy
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-abdnrgjzyqkqd',1,'createBy',0,'创建人',100,0,'main-abdnrgjzyqkqd-createBy',1,0,1,'java.lang.String',0,0,1,'CREATE_BY',0,301,12,1,0,'dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdnrgjzyqkqd-createBy');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_NR_GJZYQKQD.CREATE_BY is ''创建人''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1857230558955319296','1857230556027695105','upgrade','add','column','main-abdnrgjzyqkqd-createBy',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"createBy","main-datamodelcolumn-columnLabel":"创建人","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"CREATE_BY","main-datamodelcolumn-orderIndex":301,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1857230558955319296');
		-- 新增【字段】：main-abdnrgjzyqkqd-createTime
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-abdnrgjzyqkqd',1,'createTime',0,'创建时间',0,0,'main-abdnrgjzyqkqd-createTime',1,'date-full',0,1,'java.util.Date',0,0,1,'CREATE_TIME',0,302,93,1,0,'dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdnrgjzyqkqd-createTime');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_NR_GJZYQKQD.CREATE_TIME is ''创建时间''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1857230558963707904','1857230556027695105','upgrade','add','column','main-abdnrgjzyqkqd-createTime',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"createTime","main-datamodelcolumn-columnLabel":"创建时间","main-datamodelcolumn-columnWidth":0,"main-datamodelcolumn-columnXtype":"date-full","main-datamodelcolumn-columnJavatype":"java.util.Date","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"CREATE_TIME","main-datamodelcolumn-orderIndex":302,"main-datamodelcolumn-columnDbtype":93}}','dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1857230558963707904');
		-- 新增【字段】：main-abdnrgjzyqkqd-id
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-abdnrgjzyqkqd',1,'id',0,'ID',100,0,'main-abdnrgjzyqkqd-id',1,0,1,'java.lang.String',0,0,1,'ID',0,300,12,1,0,'dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdnrgjzyqkqd-id');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_NR_GJZYQKQD.ID is ''ID''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1857230558967902208','1857230556027695105','upgrade','add','column','main-abdnrgjzyqkqd-id',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"id","main-datamodelcolumn-columnLabel":"ID","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"ID","main-datamodelcolumn-orderIndex":300,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1857230558967902208');
		-- 新增【字段】：main-abdnrgjzyqkqd-kwmc
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdnrgjzyqkqd',1,0,'kwmc',0,'刊物名称',200,0,'main-abdnrgjzyqkqd-kwmc',0,'text',0,1,'java.lang.String',1,'KWMC',1,12,1,1,'dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdnrgjzyqkqd-kwmc');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_NR_GJZYQKQD ADD KWMC VARCHAR2(200)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_NR_GJZYQKQD.KWMC is ''刊物名称''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1857230558972096512','1857230556027695105','upgrade','add','column','main-abdnrgjzyqkqd-kwmc',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"kwmc","main-datamodelcolumn-columnLabel":"刊物名称","main-datamodelcolumn-columnWidth":200,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"KWMC","main-datamodelcolumn-orderIndex":1,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1857230558972096512');
		-- 新增【字段】：main-abdnrgjzyqkqd-updateBy
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-abdnrgjzyqkqd',1,'updateBy',0,'更新人',100,0,'main-abdnrgjzyqkqd-updateBy',1,0,1,'java.lang.String',0,0,1,'UPDATE_BY',0,303,12,1,0,'dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdnrgjzyqkqd-updateBy');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_NR_GJZYQKQD.UPDATE_BY is ''更新人''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1857230558976290816','1857230556027695105','upgrade','add','column','main-abdnrgjzyqkqd-updateBy',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"updateBy","main-datamodelcolumn-columnLabel":"更新人","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"UPDATE_BY","main-datamodelcolumn-orderIndex":303,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1857230558976290816');
		-- 新增【字段】：main-abdnrgjzyqkqd-updateTime
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-abdnrgjzyqkqd',1,'updateTime',0,'更新时间',0,0,'main-abdnrgjzyqkqd-updateTime',1,'date-full',0,1,'java.util.Date',0,0,1,'UPDATE_TIME',0,304,93,1,0,'dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdnrgjzyqkqd-updateTime');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_NR_GJZYQKQD.UPDATE_TIME is ''更新时间''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1857230558980485120','1857230556027695105','upgrade','add','column','main-abdnrgjzyqkqd-updateTime',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"updateTime","main-datamodelcolumn-columnLabel":"更新时间","main-datamodelcolumn-columnWidth":0,"main-datamodelcolumn-columnXtype":"date-full","main-datamodelcolumn-columnJavatype":"java.util.Date","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"UPDATE_TIME","main-datamodelcolumn-orderIndex":304,"main-datamodelcolumn-columnDbtype":93}}','dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1857230558980485120');
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-abdnrgjzyqkqd' and FIELD_NAME='main-damodelcolumn-primaryColumnTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdnrgjzyqkqd(main-damodelcolumn-primaryColumnTag 是否主键字段)，跳过更新');
			ELSE
				update t_da_model_column_extend set primary_column_tag = '0'
				where model_id = 'main-abdnrgjzyqkqd';
				update t_da_model_column_extend set primary_column_tag = '1'
				where id in ('main-abdnrgjzyqkqd-id');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1857230559055982592','1857230556027695105','upgrade','update','model','main-abdnrgjzyqkqd','main-damodelcolumn-primaryColumnTag','{"变更前":{"main-damodelcolumn-primaryColumnTag":""},"变更后":{"main-damodelcolumn-primaryColumnTag":"ID"}}','dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1857230559055982592');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdnrgjzyqkqd(main-damodelcolumn-primaryColumnTag 是否主键字段),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.0',internal_version='0' WHERE model_id ='main-abdnrgjzyqkqd';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdnrgjzyqkqd成功,模型版本号更新为:V1.0.0,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-abdsubjsxsqkrz ↓----------------------------------------------
--模型：教师学术期刊任职 ABD_SUB_JSXSQKRZ main-abdsubjsxsqkrz
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdsubjsxsqkrz'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdsubjsxsqkrz版本号:'||v_version);
	IF v_version >= 'V1.0.1.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdsubjsxsqkrz的本次增量版本号:V1.0.1,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1857230556027695106','main-abdsubjsxsqkrz','V1.0.1','0','upgrade','update','教师学术期刊任职','jcsj','dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1857230556027695106');
		-- 新增【字段扩展】：main-abdsubjsxsqkrz-sfgjzyqk
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdsubjsxsqkrz',0,'main-abdsubjsxsqkrz-sfgjzyqk','main-abdsubjsxsqkrz-sfgjzyqk',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdsubjsxsqkrz-sfgjzyqk');
		-- 新增【字段】：main-abdsubjsxsqkrz-sfgjzyqk
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_dict,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'quoteCode','main-abdsubjsxsqkrz',1,0,'sfgjzyqk',0,'是否国际重要期刊','SFBZ',100,0,'main-abdsubjsxsqkrz-sfgjzyqk',0,'text',0,1,'java.lang.String',1,'SFGJZYQK',11,12,1,1,'dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdsubjsxsqkrz-sfgjzyqk');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_SUB_JSXSQKRZ ADD SFGJZYQK VARCHAR2(100)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_SUB_JSXSQKRZ.SFGJZYQK is ''是否国际重要期刊''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1857230559072759808','1857230556027695106','upgrade','add','column','main-abdsubjsxsqkrz-sfgjzyqk',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"quoteCode","main-datamodelcolumn-columnJavaname":"sfgjzyqk","main-datamodelcolumn-columnLabel":"是否国际重要期刊","main-datamodelcolumn-columnDict":"SFBZ","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"SFGJZYQK","main-datamodelcolumn-orderIndex":11,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1857230559072759808');
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.1',internal_version='0' WHERE model_id ='main-abdsubjsxsqkrz';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdsubjsxsqkrz成功,模型版本号更新为:V1.0.1,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-insxshsjjysgljsjlqk ↓----------------------------------------------
--模型：学生各类竞赛奖励情况 INS_XSHSJJYSGLJSJLQK main-insxshsjjysgljsjlqk
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-insxshsjjysgljsjlqk'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-insxshsjjysgljsjlqk版本号:'||v_version);
	IF v_version >= 'V1.0.1.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-insxshsjjysgljsjlqk的本次增量版本号:V1.0.1,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1857230556027695107','main-insxshsjjysgljsjlqk','V1.0.1','0','upgrade','update','学生各类竞赛奖励情况','jcsj','dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1857230556027695107');
		-- 新增【字段扩展】：main-insxshsjjysgljsjlqk-xmjb
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-insxshsjjysgljsjlqk',0,'main-insxshsjjysgljsjlqk-xmjb','main-insxshsjjysgljsjlqk-xmjb',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-insxshsjjysgljsjlqk-xmjb');
		-- 新增【字段】：main-insxshsjjysgljsjlqk-xmjb
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_dict,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'quoteCode','main-insxshsjjysgljsjlqk',1,0,'xmjb',0,'项目级别','XMJB',40,0,'main-insxshsjjysgljsjlqk-xmjb',0,'text',0,1,'java.lang.String',1,'XMJB',17,12,1,1,'dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-insxshsjjysgljsjlqk-xmjb');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE INS_XSHSJJYSGLJSJLQK ADD XMJB VARCHAR2(40)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column INS_XSHSJJYSGLJSJLQK.XMJB is ''项目级别''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1857230559152451584','1857230556027695107','upgrade','add','column','main-insxshsjjysgljsjlqk-xmjb',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"quoteCode","main-datamodelcolumn-columnJavaname":"xmjb","main-datamodelcolumn-columnLabel":"项目级别","main-datamodelcolumn-columnDict":"XMJB","main-datamodelcolumn-columnWidth":40,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XMJB","main-datamodelcolumn-orderIndex":17,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-11-15 09:14:05','dataapp',TIMESTAMP '2024-11-15 09:14:05'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1857230559152451584');
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.1',internal_version='0' WHERE model_id ='main-insxshsjjysgljsjlqk';
		DBMS_OUTPUT.PUT_LINE('升级模型main-insxshsjjysgljsjlqk成功,模型版本号更新为:V1.0.1,内部版本号更新为：0');
	END IF;
END;
/
