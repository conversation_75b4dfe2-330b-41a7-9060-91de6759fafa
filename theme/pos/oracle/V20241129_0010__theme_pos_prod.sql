----------------------------------------------↓ 模型 main-abdmendsjbxx ↓----------------------------------------------
--模型：导师基本信息 ABD_MEN_DSJBXX main-abdmendsjbxx
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdmendsjbxx'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdmendsjbxx版本号:'||v_version);
	IF v_version >= 'V1.0.1.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdmendsjbxx的本次增量版本号:V1.0.1,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1862405979149709312','main-abdmendsjbxx','V1.0.1','0','upgrade','update','导师基本信息','jcsj','dataapp',TIMESTAMP '2024-11-29 15:59:22','dataapp',TIMESTAMP '2024-11-29 15:59:22'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1862405979149709312');
		-- 新增【字段扩展】：main-abdmendsjbxx-nl
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdmendsjbxx',0,'main-abdmendsjbxx-nl','main-abdmendsjbxx-nl',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-11-29 15:59:22','dataapp',TIMESTAMP '2024-11-29 15:59:22'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdmendsjbxx-nl');
		-- 新增【字段】：main-abdmendsjbxx-nl
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdmendsjbxx',1,0,'nl',0,'年龄',3,0,'main-abdmendsjbxx-nl',0,'text',0,1,'java.lang.String',1,'NL',36,12,1,1,'dataapp',TIMESTAMP '2024-11-29 15:59:22','dataapp',TIMESTAMP '2024-11-29 15:59:22'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdmendsjbxx-nl');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_MEN_DSJBXX ADD NL VARCHAR2(3)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_MEN_DSJBXX.NL is ''年龄''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1862405980886151168','1862405979149709312','upgrade','add','column','main-abdmendsjbxx-nl',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"nl","main-datamodelcolumn-columnLabel":"年龄","main-datamodelcolumn-columnWidth":3,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"NL","main-datamodelcolumn-orderIndex":36,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-11-29 15:59:22','dataapp',TIMESTAMP '2024-11-29 15:59:22'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1862405980886151168');
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.1',internal_version='0' WHERE model_id ='main-abdmendsjbxx';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdmendsjbxx成功,模型版本号更新为:V1.0.1,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-abdsubjshjxcgj ↓----------------------------------------------
--模型：教师获教学成果奖 ABD_SUB_JSHJXCGJ main-abdsubjshjxcgj
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdsubjshjxcgj'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdsubjshjxcgj版本号:'||v_version);
	IF v_version >= 'V1.0.1.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdsubjshjxcgj的本次增量版本号:V1.0.1,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1862405979149709313','main-abdsubjshjxcgj','V1.0.1','0','upgrade','update','教师获教学成果奖','jcsj','dataapp',TIMESTAMP '2024-11-29 15:59:22','dataapp',TIMESTAMP '2024-11-29 15:59:22'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1862405979149709313');
		-- 新增【字段扩展】：main-abdsubjshjxcgj-cgjj
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdsubjshjxcgj',0,'main-abdsubjshjxcgj-cgjj','main-abdsubjshjxcgj-cgjj',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-11-29 15:59:22','dataapp',TIMESTAMP '2024-11-29 15:59:22'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdsubjshjxcgj-cgjj');
		-- 新增【字段扩展】：main-abdsubjshjxcgj-tp
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdsubjshjxcgj',0,'main-abdsubjshjxcgj-tp','main-abdsubjshjxcgj-tp',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-11-29 15:59:22','dataapp',TIMESTAMP '2024-11-29 15:59:22'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdsubjshjxcgj-tp');
		-- 新增【字段】：main-abdsubjshjxcgj-cgjj
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdsubjshjxcgj',1,0,'cgjj',0,'成果简介',5000,0,'main-abdsubjshjxcgj-cgjj',0,'text',0,1,'java.sql.Clob',1,'CGJJ',13,2005,1,1,'dataapp',TIMESTAMP '2024-11-29 15:59:22','dataapp',TIMESTAMP '2024-11-29 15:59:22'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdsubjshjxcgj-cgjj');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_SUB_JSHJXCGJ ADD CGJJ CLOB';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_SUB_JSHJXCGJ.CGJJ is ''成果简介''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1862405980944871424','1862405979149709313','upgrade','add','column','main-abdsubjshjxcgj-cgjj',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"cgjj","main-datamodelcolumn-columnLabel":"成果简介","main-datamodelcolumn-columnWidth":5000,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.sql.Clob","main-datamodelcolumn-columnDbname":"CGJJ","main-datamodelcolumn-orderIndex":13,"main-datamodelcolumn-columnDbtype":2005}}','dataapp',TIMESTAMP '2024-11-29 15:59:22','dataapp',TIMESTAMP '2024-11-29 15:59:22'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1862405980944871424');
		-- 新增【字段】：main-abdsubjshjxcgj-tp
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'picture','main-abdsubjshjxcgj',1,0,'tp',0,'图片',2000,0,'main-abdsubjshjxcgj-tp',0,'uploadfile',0,1,'java.sql.Blob',1,'TP',12,2004,1,1,'dataapp',TIMESTAMP '2024-11-29 15:59:22','dataapp',TIMESTAMP '2024-11-29 15:59:22'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdsubjshjxcgj-tp');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_SUB_JSHJXCGJ ADD TP BLOB';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_SUB_JSHJXCGJ.TP is ''图片''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1862405980949065728','1862405979149709313','upgrade','add','column','main-abdsubjshjxcgj-tp',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"picture","main-datamodelcolumn-columnJavaname":"tp","main-datamodelcolumn-columnLabel":"图片","main-datamodelcolumn-columnWidth":2000,"main-datamodelcolumn-columnXtype":"uploadfile","main-datamodelcolumn-columnJavatype":"java.sql.Blob","main-datamodelcolumn-columnDbname":"TP","main-datamodelcolumn-orderIndex":12,"main-datamodelcolumn-columnDbtype":2004}}','dataapp',TIMESTAMP '2024-11-29 15:59:22','dataapp',TIMESTAMP '2024-11-29 15:59:22'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1862405980949065728');
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.1',internal_version='0' WHERE model_id ='main-abdsubjshjxcgj';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdsubjshjxcgj成功,模型版本号更新为:V1.0.1,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-abdundbzksjbxx ↓----------------------------------------------
--模型：本专科生基本信息 ABD_UND_BZKSJBXX main-abdundbzksjbxx
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdundbzksjbxx'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdundbzksjbxx版本号:'||v_version);
	IF v_version >= 'V1.0.2.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdundbzksjbxx的本次增量版本号:V1.0.2,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1862405979149709314','main-abdundbzksjbxx','V1.0.2','0','upgrade','update','本专科生基本信息','jcsj','dataapp',TIMESTAMP '2024-11-29 15:59:22','dataapp',TIMESTAMP '2024-11-29 15:59:22'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1862405979149709314');
		-- 修改【字段】：main-abdundbzksjbxx-bj columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-abdundbzksjbxx-bj' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdundbzksjbxx-bj(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'class'
				where id = 'main-abdundbzksjbxx-bj';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1862405980999397376','1862405979149709314','upgrade','update','column','main-abdundbzksjbxx-bj','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"bj","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"BJ","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":180,"main-datamodelcolumn-columnLabel":"班级","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":29,"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"class"}}','dataapp',TIMESTAMP '2024-11-29 15:59:22','dataapp',TIMESTAMP '2024-11-29 15:59:22'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1862405980999397376');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdundbzksjbxx-bj(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		-- 修改【字段】：main-abdundbzksjbxx-nj columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-abdundbzksjbxx-nj' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdundbzksjbxx-nj(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'biz_student_grade'
				where id = 'main-abdundbzksjbxx-nj';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1862405981003591680','1862405979149709314','upgrade','update','column','main-abdundbzksjbxx-nj','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"nj","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"NJ","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":180,"main-datamodelcolumn-columnLabel":"年级","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":32,"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"biz_student_grade"}}','dataapp',TIMESTAMP '2024-11-29 15:59:22','dataapp',TIMESTAMP '2024-11-29 15:59:22'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1862405981003591680');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdundbzksjbxx-nj(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		-- 修改【字段】：main-abdundbzksjbxx-xy columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-abdundbzksjbxx-xy' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdundbzksjbxx-xy(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'dept_college'
				where id = 'main-abdundbzksjbxx-xy';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1862405981011980288','1862405979149709314','upgrade','update','column','main-abdundbzksjbxx-xy','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"xy","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XY","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":180,"main-datamodelcolumn-columnLabel":"学院","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":27,"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"dept_college"}}','dataapp',TIMESTAMP '2024-11-29 15:59:22','dataapp',TIMESTAMP '2024-11-29 15:59:22'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1862405981011980288');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdundbzksjbxx-xy(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		-- 修改【字段】：main-abdundbzksjbxx-xybm columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-abdundbzksjbxx-xybm' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdundbzksjbxx-xybm(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'dept_college'
				where id = 'main-abdundbzksjbxx-xybm';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1862405981020368896','1862405979149709314','upgrade','update','column','main-abdundbzksjbxx-xybm','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"xybm","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XYBM","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":180,"main-datamodelcolumn-columnLabel":"学院编码","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":26,"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"dept_college"}}','dataapp',TIMESTAMP '2024-11-29 15:59:22','dataapp',TIMESTAMP '2024-11-29 15:59:22'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1862405981020368896');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdundbzksjbxx-xybm(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		-- 修改【字段】：main-abdundbzksjbxx-zy columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-abdundbzksjbxx-zy' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdundbzksjbxx-zy(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'major'
				where id = 'main-abdundbzksjbxx-zy';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1862405981024563200','1862405979149709314','upgrade','update','column','main-abdundbzksjbxx-zy','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"zy","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"ZY","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":180,"main-datamodelcolumn-columnLabel":"专业","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":31,"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"major"}}','dataapp',TIMESTAMP '2024-11-29 15:59:22','dataapp',TIMESTAMP '2024-11-29 15:59:22'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1862405981024563200');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdundbzksjbxx-zy(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		-- 修改【字段】：main-abdundbzksjbxx-zybm columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-abdundbzksjbxx-zybm' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdundbzksjbxx-zybm(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'major'
				where id = 'main-abdundbzksjbxx-zybm';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1862405981032951808','1862405979149709314','upgrade','update','column','main-abdundbzksjbxx-zybm','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"zybm","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"ZYBM","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":180,"main-datamodelcolumn-columnLabel":"专业编码","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":30,"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"major"}}','dataapp',TIMESTAMP '2024-11-29 15:59:22','dataapp',TIMESTAMP '2024-11-29 15:59:22'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1862405981032951808');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdundbzksjbxx-zybm(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.2',internal_version='0' WHERE model_id ='main-abdundbzksjbxx';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdundbzksjbxx成功,模型版本号更新为:V1.0.2,内部版本号更新为：0');
	END IF;
END;
/
