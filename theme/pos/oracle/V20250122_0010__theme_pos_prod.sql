----------------------------------------------↓ 模型 main-abdsubzjxsrc ↓----------------------------------------------
--模型：造就学术人才 ABD_SUB_ZJXSRC main-abdsubzjxsrc
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdsubzjxsrc'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdsubzjxsrc版本号:'||v_version);
	IF v_version >= 'V1.0.1.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdsubzjxsrc的本次增量版本号:V1.0.1,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1881898458844508160','main-abdsubzjxsrc','V1.0.1','0','upgrade','update','造就学术人才','jcsj','dataapp',TIMESTAMP '2025-01-22 10:55:31','dataapp',TIMESTAMP '2025-01-22 10:55:31'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1881898458844508160');
		-- 新增【字段扩展】：main-abdsubzjxsrc-hxxsrcrq
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdsubzjxsrc',0,'main-abdsubzjxsrc-hxxsrcrq','main-abdsubzjxsrc-hxxsrcrq',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-22 10:55:31','dataapp',TIMESTAMP '2025-01-22 10:55:31'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdsubzjxsrc-hxxsrcrq');
		-- 新增【字段】：main-abdsubzjxsrc-hxxsrcrq
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_format,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'date','main-abdsubzjxsrc',1,1,'hxxsrcrq',0,'date-range','获选学术人才日期',10,0,'main-abdsubzjxsrc-hxxsrcrq','yyyy-MM-dd',0,'date-local',0,1,'java.lang.String',1,'HXXSRCRQ',7,12,1,1,'dataapp',TIMESTAMP '2025-01-22 10:55:31','dataapp',TIMESTAMP '2025-01-22 10:55:31'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdsubzjxsrc-hxxsrcrq');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_SUB_ZJXSRC ADD HXXSRCRQ VARCHAR2(10)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_SUB_ZJXSRC.HXXSRCRQ is ''获选学术人才日期''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1881898460450926592','1881898458844508160','upgrade','add','column','main-abdsubzjxsrc-hxxsrcrq',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"date","main-datamodelcolumn-columnJavaname":"hxxsrcrq","main-datamodelcolumn-columnLabel":"获选学术人才日期","main-datamodelcolumn-columnWidth":10,"main-datamodelcolumn-columnFormat":"yyyy-MM-dd","main-datamodelcolumn-columnXtype":"date-local","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"HXXSRCRQ","main-datamodelcolumn-orderIndex":7,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-22 10:55:31','dataapp',TIMESTAMP '2025-01-22 10:55:31'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1881898460450926592');
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.1',internal_version='0' WHERE model_id ='main-abdsubzjxsrc';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdsubzjxsrc成功,模型版本号更新为:V1.0.1,内部版本号更新为：0');
	END IF;
END;
/
