BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0335_xybh ON pos_si_col_0335 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0325_xybh ON pos_si_col_0325 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_pos_yjszcxx_xh ON abd_pos_yjszcxx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_sub_label_yjxkdm ON sub_label (yjxkdm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_pos_yjsjbxx_xh ON abd_pos_yjsjbxx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_label_xh ON pos_label (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_pos_yjscjxx_xh ON abd_pos_yjscjxx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_pos_yjsgjjlxx_xh ON abd_pos_yjsgjjlxx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_pos_yjskyjshjxx_xh ON abd_pos_yjskyjshjxx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0096_xybh ON pos_si_col_0096 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0097_xybh ON pos_si_col_0097 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0248_xybh ON pos_si_col_0248 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0187_xybh ON pos_si_col_0187 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0307_xybh ON pos_si_col_0307 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_pos_yjsfblwxx_xh ON abd_pos_yjsfblwxx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sub_0105_xkbh ON pos_si_sub_0105 (xkbh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sub_0106_xkbh ON pos_si_sub_0106 (xkbh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0098_xybh ON pos_si_col_0098 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0099_xybh ON pos_si_col_0099 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0308_xybh ON pos_si_col_0308 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0309_xybh ON pos_si_col_0309 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_pos_yjsfbzlxx_xh ON abd_pos_yjsfbzlxx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sub_0107_xkbh ON pos_si_sub_0107 (xkbh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sub_0108_xkbh ON pos_si_sub_0108 (xkbh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0100_xybh ON pos_si_col_0100 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0101_xybh ON pos_si_col_0101 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0311_xybh ON pos_si_col_0311 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_pos_yjszfljc_xh ON abd_pos_yjszfljc (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sub_0109_xkbh ON pos_si_sub_0109 (xkbh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sub_0110_xkbh ON pos_si_sub_0110 (xkbh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sub_0111_xkbh ON pos_si_sub_0111 (xkbh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0183_xybh ON pos_si_col_0183 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0127_xybh ON pos_si_col_0127 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0128_xybh ON pos_si_col_0128 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0312_xybh ON pos_si_col_0312 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_men_dsjbxx_zgh ON abd_men_dsjbxx (zgh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_pos_yjscjjg_xh ON abd_pos_yjscjjg (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_pos_yjslwpy_xh ON abd_pos_yjslwpy (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sub_0112_xkbh ON pos_si_sub_0112 (xkbh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sub_0113_xkbh ON pos_si_sub_0113 (xkbh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0129_xybh ON pos_si_col_0129 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0109_xybh ON pos_si_col_0109 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_pos_yjslwdb_xh ON abd_pos_yjslwdb (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_pos_yjsxwsq_xh ON abd_pos_yjsxwsq (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sub_0114_xkbh ON pos_si_sub_0114 (xkbh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sub_0117_xkbh ON pos_si_sub_0117 (xkbh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0110_xybh ON pos_si_col_0110 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0111_xybh ON pos_si_col_0111 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_pos_yjsyxlwhdqk_xh ON abd_pos_yjsyxlwhdqk (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_pos_yjsshtljg_xh ON abd_pos_yjsshtljg (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_col_kcjxbksxx_kkdwdm ON abd_col_kcjxbksxx (kkdwdm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0112_xybh ON pos_si_col_0112 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0113_xybh ON pos_si_col_0113 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0114_xybh ON pos_si_col_0114 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_pos_yjsbjyjl_xh ON abd_pos_yjsbjyjl (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_pos_yjsbjygcjl_xh ON abd_pos_yjsbjygcjl (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_col_xyylkcjalxx_yxdm ON abd_col_xyylkcjalxx (yxdm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0115_xybh ON pos_si_col_0115 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0116_xybh ON pos_si_col_0116 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_pos_yjsyxbysqk_xh ON abd_pos_yjsyxbysqk (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_col_xyjcjsycb_yxdm ON abd_col_xyjcjsycb (yxdm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0117_xybh ON pos_si_col_0117 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0118_xybh ON pos_si_col_0118 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_col_xysjjdxx_ytyxdm ON abd_col_xysjjdxx (ytyxdm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0119_xybh ON pos_si_col_0119 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0120_xybh ON pos_si_col_0120 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0121_xybh ON pos_si_col_0121 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_tea_rkjspjjgxx_zgh ON abd_tea_rkjspjjgxx (zgh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_men_label_zgh ON men_label (zgh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_col_xydspxxx_zbdwdm ON abd_col_xydspxxx (zbdwdm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0122_xybh ON pos_si_col_0122 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0123_xybh ON pos_si_col_0123 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_tea_0059_zgh ON pos_si_tea_0059 (zgh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_tea_0065_zgh ON pos_si_tea_0065 (zgh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0102_xybh ON pos_si_col_0102 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0103_xybh ON pos_si_col_0103 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0124_xybh ON pos_si_col_0124 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0125_xybh ON pos_si_col_0125 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_tea_0060_zgh ON pos_si_tea_0060 (zgh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_pos_yjsxjydxx_xh ON abd_pos_yjsxjydxx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0104_xybh ON pos_si_col_0104 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0105_xybh ON pos_si_col_0105 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0126_xybh ON pos_si_col_0126 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0184_xybh ON pos_si_col_0184 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_pos_yjsjxjxx_xh ON abd_pos_yjsjxjxx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_pos_yjszxjffxx_xh ON abd_pos_yjszxjffxx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0106_xybh ON pos_si_col_0106 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0107_xybh ON pos_si_col_0107 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0185_xybh ON pos_si_col_0185 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0186_xybh ON pos_si_col_0186 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0269_xybh ON pos_si_col_0269 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_pos_yjsszsqxx_xh ON abd_pos_yjsszsqxx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_pos_yjsszffxx_xh ON abd_pos_yjsszffxx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0108_xybh ON pos_si_col_0108 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0094_xybh ON pos_si_col_0094 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0270_xybh ON pos_si_col_0270 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0314_xybh ON pos_si_col_0314 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_pos_yjszxdkxx_xh ON abd_pos_yjszxdkxx (xh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_col_0095_xybh ON pos_si_col_0095 (xybh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0314_xxbsm ON pos_si_sch_0314 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0315_xxbsm ON pos_si_sch_0315 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0316_xxbsm ON pos_si_sch_0316 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0317_xxbsm ON pos_si_sch_0317 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0322_xxbsm ON pos_si_sch_0322 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0359_xxbsm ON pos_si_sch_0359 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0360_xxbsm ON pos_si_sch_0360 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0481_xxbsm ON pos_si_sch_0481 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0361_xxbsm ON pos_si_sch_0361 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sub_0115_xkbh ON pos_si_sub_0115 (xkbh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0505_xxbsm ON pos_si_sch_0505 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0506_xxbsm ON pos_si_sch_0506 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sub_0097_xkbh ON pos_si_sub_0097 (xkbh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sub_0098_xkbh ON pos_si_sub_0098 (xkbh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sub_0099_xkbh ON pos_si_sub_0099 (xkbh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0507_xxbsm ON pos_si_sch_0507 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0362_xxbsm ON pos_si_sch_0362 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sub_0116_xkbh ON pos_si_sub_0116 (xkbh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sub_0103_xkbh ON pos_si_sub_0103 (xkbh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sub_0104_xkbh ON pos_si_sub_0104 (xkbh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0363_xxbsm ON pos_si_sch_0363 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0364_xxbsm ON pos_si_sch_0364 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0365_xxbsm ON pos_si_sch_0365 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sub_0095_xkbh ON pos_si_sub_0095 (xkbh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sub_0096_xkbh ON pos_si_sub_0096 (xkbh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0366_xxbsm ON pos_si_sch_0366 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0367_xxbsm ON pos_si_sch_0367 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sub_0100_xkbh ON pos_si_sub_0100 (xkbh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sub_0101_xkbh ON pos_si_sub_0101 (xkbh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_sch_xwpc_xxdm ON abd_sch_xwpc (xxdm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0369_xxbsm ON pos_si_sch_0369 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0370_xxbsm ON pos_si_sch_0370 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0371_xxbsm ON pos_si_sch_0371 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sub_0093_xkbh ON pos_si_sub_0093 (xkbh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sub_0094_xkbh ON pos_si_sub_0094 (xkbh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_sch_jxcgxx_xxdm ON abd_sch_jxcgxx (xxdm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_sch_ssbmxx_ksbh ON abd_sch_ssbmxx (ksbh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0372_xxbsm ON pos_si_sch_0372 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0373_xxbsm ON pos_si_sch_0373 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_sch_bsbmxx_ksbh ON abd_sch_bsbmxx (ksbh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_sch_zsjh_xxdm ON abd_sch_zsjh (xxdm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0498_xxbsm ON pos_si_sch_0498 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0499_xxbsm ON pos_si_sch_0499 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_sch_sslqxx_ksbh ON abd_sch_sslqxx (ksbh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_abd_sch_bslqxx_ksbh ON abd_sch_bslqxx (ksbh)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0500_xxbsm ON pos_si_sch_0500 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0501_xxbsm ON pos_si_sch_0501 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0514_xxbsm ON pos_si_sch_0514 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0309_xxbsm ON pos_si_sch_0309 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0551_xxbsm ON pos_si_sch_0551 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0552_xxbsm ON pos_si_sch_0552 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0310_xxbsm ON pos_si_sch_0310 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0311_xxbsm ON pos_si_sch_0311 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0312_xxbsm ON pos_si_sch_0312 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0553_xxbsm ON pos_si_sch_0553 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0554_xxbsm ON pos_si_sch_0554 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0313_xxbsm ON pos_si_sch_0313 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0307_xxbsm ON pos_si_sch_0307 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0555_xxbsm ON pos_si_sch_0555 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0556_xxbsm ON pos_si_sch_0556 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0557_xxbsm ON pos_si_sch_0557 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0308_xxbsm ON pos_si_sch_0308 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0358_xxbsm ON pos_si_sch_0358 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0320_xxbsm ON pos_si_sch_0320 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/
BEGIN
EXECUTE IMMEDIATE 'CREATE INDEX idx_pos_si_sch_0321_xxbsm ON pos_si_sch_0321 (xxbsm)';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/