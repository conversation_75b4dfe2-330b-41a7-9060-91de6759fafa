----------------------------------------------↓ 模型 main-possisub0115 ↓----------------------------------------------
--模型：各学科导师累计人数 POS_SI_SUB_0115 main-possisub0115
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-possisub0115'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-possisub0115版本号:'||v_version);
	IF v_version >= 'V1.0.1.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-possisub0115的本次增量版本号:V1.0.1,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1857321091929223168','main-possisub0115','V1.0.1','0','upgrade','update','各学科导师累计人数','zb','dataapp',TIMESTAMP '2024-11-15 15:13:50','dataapp',TIMESTAMP '2024-11-15 15:13:50'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1857321091929223168');
		-- 新增【字段扩展】：main-possisub0115-dspylx
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,label_desc,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-possisub0115',0,'main-possisub0115-dspylx','专职、兼职','main-possisub0115-dspylx',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-11-15 15:13:50','dataapp',TIMESTAMP '2024-11-15 15:13:50'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-possisub0115-dspylx');
		-- 新增【字段扩展】：main-possisub0115-dsxz
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,label_desc,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-possisub0115',0,'main-possisub0115-dsxz','指导教师、行业导师','main-possisub0115-dsxz',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-11-15 15:13:50','dataapp',TIMESTAMP '2024-11-15 15:13:50'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-possisub0115-dsxz');
		-- 新增【字段】：main-possisub0115-dspylx
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-possisub0115',1,0,'dspylx',0,'导师聘用类型',100,0,'main-possisub0115-dspylx',0,'text',0,1,'java.lang.String',1,'DSPYLX',10,12,1,1,'dataapp',TIMESTAMP '2024-11-15 15:13:50','dataapp',TIMESTAMP '2024-11-15 15:13:50'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-possisub0115-dspylx');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE POS_SI_SUB_0115 ADD DSPYLX VARCHAR2(100)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column POS_SI_SUB_0115.DSPYLX is ''导师聘用类型''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1857321092340264960','1857321091929223168','upgrade','add','column','main-possisub0115-dspylx',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"dspylx","main-datamodelcolumn-columnLabel":"导师聘用类型","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"DSPYLX","main-datamodelcolumn-orderIndex":10,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-11-15 15:13:50','dataapp',TIMESTAMP '2024-11-15 15:13:50'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1857321092340264960');
		-- 新增【字段】：main-possisub0115-dsxz
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-possisub0115',1,0,'dsxz',0,'导师性质',100,0,'main-possisub0115-dsxz',0,'text',0,1,'java.lang.String',1,'DSXZ',11,12,1,1,'dataapp',TIMESTAMP '2024-11-15 15:13:50','dataapp',TIMESTAMP '2024-11-15 15:13:50'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-possisub0115-dsxz');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE POS_SI_SUB_0115 ADD DSXZ VARCHAR2(100)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column POS_SI_SUB_0115.DSXZ is ''导师性质''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1857321092344459264','1857321091929223168','upgrade','add','column','main-possisub0115-dsxz',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"dsxz","main-datamodelcolumn-columnLabel":"导师性质","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"DSXZ","main-datamodelcolumn-orderIndex":11,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-11-15 15:13:50','dataapp',TIMESTAMP '2024-11-15 15:13:50'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1857321092344459264');
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.1',internal_version='0' WHERE model_id ='main-possisub0115';
		DBMS_OUTPUT.PUT_LINE('升级模型main-possisub0115成功,模型版本号更新为:V1.0.1,内部版本号更新为：0');
	END IF;
END;
/
