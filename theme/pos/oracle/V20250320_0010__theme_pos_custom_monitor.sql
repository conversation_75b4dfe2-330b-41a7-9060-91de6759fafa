BEGIN
EXECUTE IMMEDIATE 'create or replace view V_ABD_SCH_XXJBXX as SELECT * FROM ABD_SCH_XXJBXX';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/

BEGIN
EXECUTE IMMEDIATE 'create or replace view V_ABD_COL_XYJBXX as SELECT * FROM ABD_COL_XYJBXX';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/

BEGIN
EXECUTE IMMEDIATE 'create or replace view V_ABD_SUB_XKXX as SELECT * FROM ABD_SUB_XKXX';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/

BEGIN
EXECUTE IMMEDIATE 'create or replace view V_ABD_COU_KCJBXX as SELECT * FROM ABD_COU_KCJBXX';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/

BEGIN
EXECUTE IMMEDIATE 'create or replace view V_ABD_TRA_PYFA as SELECT * FROM ABD_TRA_PYFA';
EXCEPTION
WHEN OTHERS THEN
	IF SQLCODE NOT IN (-955,-942,-904,1408) THEN
		RAISE;
	END IF;
END;
/