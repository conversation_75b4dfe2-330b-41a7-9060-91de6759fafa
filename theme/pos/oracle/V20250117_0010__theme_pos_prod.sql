----------------------------------------------↓ 公共增量 ↓----------------------------------------------
-- 新增【主题域对象】:TRAINNING PLAN
insert into t_da_entity (stmc,stbszd,mxid,dxms,stbm,id,stbmjc,label_model,classify_code,create_by,create_time,update_by,update_time)
select '方案','pyfadm','main-abdtrapyfa','自动生成 对象 2024-06-20 01:00:00','TRAINNING PLAN','TRAINNING PLAN','TRA','main-tralabel','TRAINNING PLAN_JXXGXX_JCSJ','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
from dual
where not exists (
	select 1
	from t_da_entity
	where id = 'TRAINNING PLAN');
-- 新增【模型分类扩展表】:1879435274158092288
insert into t_da_model_classify_extend (classify_id,classify_type,entity_id,id,create_by,create_time,update_by,update_time)
select '1879435274158092288','zb','SCHOOL','1879435274158092288','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
from dual
where not exists (
	select 1
	from t_da_model_classify_extend
	where id = '1879435274158092288');
-- 新增【模型分类扩展表】:TRAINNING PLAN_JXXGXX_JCSJ
insert into t_da_model_classify_extend (classify_id,classify_desc,classify_type,entity_id,id,create_by,create_time,update_by,update_time)
select 'TRAINNING PLAN_JXXGXX_JCSJ','自动生成 分类 2024-06-20 01:00:00','jcsj','TRAINNING PLAN','TRAINNING PLAN_JXXGXX_JCSJ','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
from dual
where not exists (
	select 1
	from t_da_model_classify_extend
	where id = 'TRAINNING PLAN_JXXGXX_JCSJ');
-- 新增【模型分类】:1879435274158092288
insert into lowcode_model_classify (sys_flag,id,classify_code,classify_name,parent_id,status,create_by,create_time,update_by,update_time)
select 0,'1879435274158092288','extzb1879435274158092289','教务运行','SCHOOL',1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
from dual
where not exists (
	select 1
	from lowcode_model_classify
	where id = '1879435274158092288');
-- 新增【模型分类】:TRAINNING PLAN
insert into lowcode_model_classify (sys_flag,show_order,id,classify_code,classify_name,status,create_by,create_time,update_by,update_time)
select 0,0,'TRAINNING PLAN','TRAINNING PLAN','方案',1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
from dual
where not exists (
	select 1
	from lowcode_model_classify
	where id = 'TRAINNING PLAN');
-- 新增【模型分类】:TRAINNING PLAN_JXXGXX_JCSJ
insert into lowcode_model_classify (sys_flag,show_order,id,classify_code,classify_name,parent_id,status,create_by,create_time,update_by,update_time)
select 0,2,'TRAINNING PLAN_JXXGXX_JCSJ','TRAINNING PLAN_JXXGXX_JCSJ','教学相关信息','TRAINNING PLAN',1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
from dual
where not exists (
	select 1
	from lowcode_model_classify
	where id = 'TRAINNING PLAN_JXXGXX_JCSJ');
----------------------------------------------↓ 模型 main-abdmendsjbxx ↓----------------------------------------------
--模型：导师基本信息 ABD_MEN_DSJBXX main-abdmendsjbxx
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdmendsjbxx'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdmendsjbxx版本号:'||v_version);
	IF v_version >= 'V1.0.2.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdmendsjbxx的本次增量版本号:V1.0.2,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1880186819095048193','main-abdmendsjbxx','V1.0.2','0','upgrade','update','导师基本信息','jcsj','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1880186819095048193');
		-- 新增【字段扩展】：main-abdmendsjbxx-zydm1
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdmendsjbxx',0,'main-abdmendsjbxx-zydm1','main-abdmendsjbxx-zydm1',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdmendsjbxx-zydm1');
		-- 新增【字段扩展】：main-abdmendsjbxx-zydm2
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdmendsjbxx',0,'main-abdmendsjbxx-zydm2','main-abdmendsjbxx-zydm2',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdmendsjbxx-zydm2');
		-- 新增【字段扩展】：main-abdmendsjbxx-zydm3
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdmendsjbxx',0,'main-abdmendsjbxx-zydm3','main-abdmendsjbxx-zydm3',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdmendsjbxx-zydm3');
		-- 新增【字段扩展】：main-abdmendsjbxx-zyxwm1
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdmendsjbxx',0,'main-abdmendsjbxx-zyxwm1','main-abdmendsjbxx-zyxwm1',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdmendsjbxx-zyxwm1');
		-- 新增【字段扩展】：main-abdmendsjbxx-zyxwm2
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdmendsjbxx',0,'main-abdmendsjbxx-zyxwm2','main-abdmendsjbxx-zyxwm2',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdmendsjbxx-zyxwm2');
		-- 新增【字段】：main-abdmendsjbxx-zydm1
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdmendsjbxx',1,0,'zydm1',0,'专业代码1',4,0,'main-abdmendsjbxx-zydm1',0,'text',0,1,'java.lang.String',1,'ZYDM1',37,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdmendsjbxx-zydm1');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_MEN_DSJBXX ADD ZYDM1 VARCHAR2(4)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_MEN_DSJBXX.ZYDM1 is ''专业代码1''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186831485022208','1880186819095048193','upgrade','add','column','main-abdmendsjbxx-zydm1',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"zydm1","main-datamodelcolumn-columnLabel":"专业代码1","main-datamodelcolumn-columnWidth":4,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"ZYDM1","main-datamodelcolumn-orderIndex":37,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186831485022208');
		-- 新增【字段】：main-abdmendsjbxx-zydm2
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdmendsjbxx',1,0,'zydm2',0,'专业代码2',4,0,'main-abdmendsjbxx-zydm2',0,'text',0,1,'java.lang.String',1,'ZYDM2',38,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdmendsjbxx-zydm2');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_MEN_DSJBXX ADD ZYDM2 VARCHAR2(4)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_MEN_DSJBXX.ZYDM2 is ''专业代码2''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186831493410816','1880186819095048193','upgrade','add','column','main-abdmendsjbxx-zydm2',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"zydm2","main-datamodelcolumn-columnLabel":"专业代码2","main-datamodelcolumn-columnWidth":4,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"ZYDM2","main-datamodelcolumn-orderIndex":38,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186831493410816');
		-- 新增【字段】：main-abdmendsjbxx-zydm3
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdmendsjbxx',1,0,'zydm3',0,'专业代码3',4,0,'main-abdmendsjbxx-zydm3',0,'text',0,1,'java.lang.String',1,'ZYDM3',39,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdmendsjbxx-zydm3');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_MEN_DSJBXX ADD ZYDM3 VARCHAR2(4)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_MEN_DSJBXX.ZYDM3 is ''专业代码3''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186831497605120','1880186819095048193','upgrade','add','column','main-abdmendsjbxx-zydm3',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"zydm3","main-datamodelcolumn-columnLabel":"专业代码3","main-datamodelcolumn-columnWidth":4,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"ZYDM3","main-datamodelcolumn-orderIndex":39,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186831497605120');
		-- 新增【字段】：main-abdmendsjbxx-zyxwm1
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdmendsjbxx',1,0,'zyxwm1',0,'专业学位码1',4,0,'main-abdmendsjbxx-zyxwm1',0,'text',0,1,'java.lang.String',1,'ZYXWM1',40,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdmendsjbxx-zyxwm1');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_MEN_DSJBXX ADD ZYXWM1 VARCHAR2(4)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_MEN_DSJBXX.ZYXWM1 is ''专业学位码1''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186831505993728','1880186819095048193','upgrade','add','column','main-abdmendsjbxx-zyxwm1',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"zyxwm1","main-datamodelcolumn-columnLabel":"专业学位码1","main-datamodelcolumn-columnWidth":4,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"ZYXWM1","main-datamodelcolumn-orderIndex":40,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186831505993728');
		-- 新增【字段】：main-abdmendsjbxx-zyxwm2
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdmendsjbxx',1,0,'zyxwm2',0,'专业学位码2',4,0,'main-abdmendsjbxx-zyxwm2',0,'text',0,1,'java.lang.String',1,'ZYXWM2',41,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdmendsjbxx-zyxwm2');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_MEN_DSJBXX ADD ZYXWM2 VARCHAR2(4)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_MEN_DSJBXX.ZYXWM2 is ''专业学位码2''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186831510188032','1880186819095048193','upgrade','add','column','main-abdmendsjbxx-zyxwm2',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"zyxwm2","main-datamodelcolumn-columnLabel":"专业学位码2","main-datamodelcolumn-columnWidth":4,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"ZYXWM2","main-datamodelcolumn-orderIndex":41,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186831510188032');
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.2',internal_version='0' WHERE model_id ='main-abdmendsjbxx';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdmendsjbxx成功,模型版本号更新为:V1.0.2,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-abdposyjscjxx ↓----------------------------------------------
--模型：研究生成绩信息 ABD_POS_YJSCJXX main-abdposyjscjxx
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdposyjscjxx'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdposyjscjxx版本号:'||v_version);
	IF v_version >= 'V1.0.0.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdposyjscjxx的本次增量版本号:V1.0.0,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1880186819095048194','main-abdposyjscjxx','V1.0.0','0','upgrade','add','研究生成绩信息','jcsj','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1880186819095048194');
		-- 新增【模型扩展】：main-abdposyjscjxx
		insert into t_da_model_extend (model_encoding,access_level,model_id,model_version,model_enable,entity_id,model_type,use_num,theme_code,model_desc,internal_version,id,create_by,create_time,update_by,update_time)
		select 'ABD_POS_0022','1','main-abdposyjscjxx','V1.0.0','1','POSTGRADUATE','jcsj',0,'POS','研究生最终成绩，课程代码+学号    唯一','0','main-abdposyjscjxx','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_extend
			where id = 'main-abdposyjscjxx');
		-- 新增【模型使用范围】:1736487992285001243
		insert into t_da_model_theme (model_id,theme_code,id,create_by,create_time,update_by,update_time)
		select 'main-abdposyjscjxx','POS','1736487992285001243','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_theme
			where id = '1736487992285001243');
		-- 新增【模型】：main-abdposyjscjxx
		insert into lowcode_model (model_app,publish_time,sys_flag,model_class,permit_ext_flag,extend_flag,model_label,model_name,model_table,model_classify,virtual_model,biz_model,order_index,id,status,create_by,create_time,update_by,update_time)
		select 'main',TIMESTAMP '2025-01-10 13:46:35',0,'com.wisedu.lowcode4j.main.po.Abdposyjscjxx',1,1,'研究生成绩信息','abdposyjscjxx','ABD_POS_YJSCJXX','POSTGRADUATE_PYGC_JCSJ',1,0,723,'main-abdposyjscjxx',1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model
			where id = 'main-abdposyjscjxx');
		BEGIN
		EXECUTE IMMEDIATE 'CREATE TABLE ABD_POS_YJSCJXX(ID VARCHAR2(100) DEFAULT SYS_GUID() primary key ,CREATE_BY VARCHAR2(100),CREATE_TIME DATE,UPDATE_BY VARCHAR2(100),UPDATE_TIME DATE)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-955) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on table ABD_POS_YJSCJXX is ''研究生成绩信息''';
		END;
		-- 新增【字段扩展】：main-abdposyjscjxx-cj
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdposyjscjxx',0,'main-abdposyjscjxx-cj','main-abdposyjscjxx-cj',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdposyjscjxx-cj');
		-- 新增【字段扩展】：main-abdposyjscjxx-createBy
		insert into t_da_model_column_extend (time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdposyjscjxx',0,'main-abdposyjscjxx-createBy','main-abdposyjscjxx-createBy',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdposyjscjxx-createBy');
		-- 新增【字段扩展】：main-abdposyjscjxx-createTime
		insert into t_da_model_column_extend (time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdposyjscjxx',0,'main-abdposyjscjxx-createTime','main-abdposyjscjxx-createTime',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdposyjscjxx-createTime');
		-- 新增【字段扩展】：main-abdposyjscjxx-id
		insert into t_da_model_column_extend (time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,primary_column_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdposyjscjxx',0,'main-abdposyjscjxx-id','main-abdposyjscjxx-id',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdposyjscjxx-id');
		-- 新增【字段扩展】：main-abdposyjscjxx-kcdm
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdposyjscjxx',0,'main-abdposyjscjxx-kcdm','main-abdposyjscjxx-kcdm',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdposyjscjxx-kcdm');
		-- 新增【字段扩展】：main-abdposyjscjxx-kcmc
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdposyjscjxx',0,'main-abdposyjscjxx-kcmc','main-abdposyjscjxx-kcmc',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdposyjscjxx-kcmc');
		-- 新增【字段扩展】：main-abdposyjscjxx-lrsj
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdposyjscjxx',0,'main-abdposyjscjxx-lrsj','main-abdposyjscjxx-lrsj',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdposyjscjxx-lrsj');
		-- 新增【字段扩展】：main-abdposyjscjxx-updateBy
		insert into t_da_model_column_extend (time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdposyjscjxx',0,'main-abdposyjscjxx-updateBy','main-abdposyjscjxx-updateBy',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdposyjscjxx-updateBy');
		-- 新增【字段扩展】：main-abdposyjscjxx-updateTime
		insert into t_da_model_column_extend (time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdposyjscjxx',0,'main-abdposyjscjxx-updateTime','main-abdposyjscjxx-updateTime',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdposyjscjxx-updateTime');
		-- 新增【字段扩展】：main-abdposyjscjxx-xf
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdposyjscjxx',0,'main-abdposyjscjxx-xf','main-abdposyjscjxx-xf',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdposyjscjxx-xf');
		-- 新增【字段扩展】：main-abdposyjscjxx-xh
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdposyjscjxx',0,'main-abdposyjscjxx-xh','main-abdposyjscjxx-xh',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdposyjscjxx-xh');
		-- 新增【字段扩展】：main-abdposyjscjxx-xm
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdposyjscjxx',0,'main-abdposyjscjxx-xm','main-abdposyjscjxx-xm',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdposyjscjxx-xm');
		-- 新增【字段扩展】：main-abdposyjscjxx-xnxq
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdposyjscjxx',0,'main-abdposyjscjxx-xnxq','main-abdposyjscjxx-xnxq',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdposyjscjxx-xnxq');
		-- 新增【字段】：main-abdposyjscjxx-cj
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'number','main-abdposyjscjxx',1,0,'cj',0,'number-range','成绩',5,0,'main-abdposyjscjxx-cj',0,'number',0,1,'java.lang.Integer',1,'CJ',5,4,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdposyjscjxx-cj');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_POS_YJSCJXX ADD CJ NUMBER(5)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_POS_YJSCJXX.CJ is ''成绩''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186831606657024','1880186819095048194','upgrade','add','column','main-abdposyjscjxx-cj',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"number","main-datamodelcolumn-columnJavaname":"cj","main-datamodelcolumn-columnLabel":"成绩","main-datamodelcolumn-columnWidth":5,"main-datamodelcolumn-columnXtype":"number","main-datamodelcolumn-columnJavatype":"java.lang.Integer","main-datamodelcolumn-columnDbname":"CJ","main-datamodelcolumn-orderIndex":5,"main-datamodelcolumn-columnDbtype":4}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186831606657024');
		-- 新增【字段】：main-abdposyjscjxx-createBy
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-abdposyjscjxx',1,'createBy',0,'创建人',100,0,'main-abdposyjscjxx-createBy',1,0,1,'java.lang.String',0,0,1,'CREATE_BY',0,301,12,1,0,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdposyjscjxx-createBy');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_POS_YJSCJXX.CREATE_BY is ''创建人''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186831615045632','1880186819095048194','upgrade','add','column','main-abdposyjscjxx-createBy',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"createBy","main-datamodelcolumn-columnLabel":"创建人","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"CREATE_BY","main-datamodelcolumn-orderIndex":301,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186831615045632');
		-- 新增【字段】：main-abdposyjscjxx-createTime
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-abdposyjscjxx',1,'createTime',0,'datetime-range','创建时间',0,0,'main-abdposyjscjxx-createTime',1,'date-full',0,1,'java.util.Date',0,0,1,'CREATE_TIME',0,302,93,1,0,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdposyjscjxx-createTime');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_POS_YJSCJXX.CREATE_TIME is ''创建时间''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186831623434240','1880186819095048194','upgrade','add','column','main-abdposyjscjxx-createTime',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"createTime","main-datamodelcolumn-columnLabel":"创建时间","main-datamodelcolumn-columnWidth":0,"main-datamodelcolumn-columnXtype":"date-full","main-datamodelcolumn-columnJavatype":"java.util.Date","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"CREATE_TIME","main-datamodelcolumn-orderIndex":302,"main-datamodelcolumn-columnDbtype":93}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186831623434240');
		-- 新增【字段】：main-abdposyjscjxx-id
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-abdposyjscjxx',1,'id',0,'ID',100,0,'main-abdposyjscjxx-id',1,0,1,'java.lang.String',0,0,1,'ID',0,300,12,1,0,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdposyjscjxx-id');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_POS_YJSCJXX.ID is ''ID''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186831627628544','1880186819095048194','upgrade','add','column','main-abdposyjscjxx-id',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"id","main-datamodelcolumn-columnLabel":"ID","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"ID","main-datamodelcolumn-orderIndex":300,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186831627628544');
		-- 新增【字段】：main-abdposyjscjxx-kcdm
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdposyjscjxx',1,0,'kcdm',0,'text','课程代码',100,0,'main-abdposyjscjxx-kcdm',0,'text',0,1,'java.lang.String',1,'KCDM',3,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdposyjscjxx-kcdm');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_POS_YJSCJXX ADD KCDM VARCHAR2(100)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_POS_YJSCJXX.KCDM is ''课程代码''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186831636017152','1880186819095048194','upgrade','add','column','main-abdposyjscjxx-kcdm',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"kcdm","main-datamodelcolumn-columnLabel":"课程代码","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"KCDM","main-datamodelcolumn-orderIndex":3,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186831636017152');
		-- 新增【字段】：main-abdposyjscjxx-kcmc
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdposyjscjxx',1,0,'kcmc',0,'text','课程名称',100,0,'main-abdposyjscjxx-kcmc',0,'text',0,1,'java.lang.String',1,'KCMC',4,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdposyjscjxx-kcmc');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_POS_YJSCJXX ADD KCMC VARCHAR2(100)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_POS_YJSCJXX.KCMC is ''课程名称''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186831640211456','1880186819095048194','upgrade','add','column','main-abdposyjscjxx-kcmc',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"kcmc","main-datamodelcolumn-columnLabel":"课程名称","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"KCMC","main-datamodelcolumn-orderIndex":4,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186831640211456');
		-- 新增【字段】：main-abdposyjscjxx-lrsj
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_format,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'date','main-abdposyjscjxx',1,0,'lrsj',0,'date-range','录入时间',10,0,'main-abdposyjscjxx-lrsj','yyyy-MM-dd',0,'date-local',0,1,'java.lang.String',1,'LRSJ',7,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdposyjscjxx-lrsj');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_POS_YJSCJXX ADD LRSJ VARCHAR2(10)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_POS_YJSCJXX.LRSJ is ''录入时间''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186831648600064','1880186819095048194','upgrade','add','column','main-abdposyjscjxx-lrsj',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"date","main-datamodelcolumn-columnJavaname":"lrsj","main-datamodelcolumn-columnLabel":"录入时间","main-datamodelcolumn-columnWidth":10,"main-datamodelcolumn-columnFormat":"yyyy-MM-dd","main-datamodelcolumn-columnXtype":"date-local","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"LRSJ","main-datamodelcolumn-orderIndex":7,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186831648600064');
		-- 新增【字段】：main-abdposyjscjxx-updateBy
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-abdposyjscjxx',1,'updateBy',0,'更新人',100,0,'main-abdposyjscjxx-updateBy',1,0,1,'java.lang.String',0,0,1,'UPDATE_BY',0,303,12,1,0,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdposyjscjxx-updateBy');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_POS_YJSCJXX.UPDATE_BY is ''更新人''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186831652794368','1880186819095048194','upgrade','add','column','main-abdposyjscjxx-updateBy',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"updateBy","main-datamodelcolumn-columnLabel":"更新人","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"UPDATE_BY","main-datamodelcolumn-orderIndex":303,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186831652794368');
		-- 新增【字段】：main-abdposyjscjxx-updateTime
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-abdposyjscjxx',1,'updateTime',0,'datetime-range','更新时间',0,0,'main-abdposyjscjxx-updateTime',1,'date-full',0,1,'java.util.Date',0,0,1,'UPDATE_TIME',0,304,93,1,0,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdposyjscjxx-updateTime');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_POS_YJSCJXX.UPDATE_TIME is ''更新时间''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186831656988672','1880186819095048194','upgrade','add','column','main-abdposyjscjxx-updateTime',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"updateTime","main-datamodelcolumn-columnLabel":"更新时间","main-datamodelcolumn-columnWidth":0,"main-datamodelcolumn-columnXtype":"date-full","main-datamodelcolumn-columnJavatype":"java.util.Date","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"UPDATE_TIME","main-datamodelcolumn-orderIndex":304,"main-datamodelcolumn-columnDbtype":93}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186831656988672');
		-- 新增【字段】：main-abdposyjscjxx-xf
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'number','main-abdposyjscjxx',1,0,'xf',0,'number-range','学分',5,0,'main-abdposyjscjxx-xf',2,0,'number',0,1,'java.lang.Double',1,'XF',6,8,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdposyjscjxx-xf');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_POS_YJSCJXX ADD XF NUMBER(5,2)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_POS_YJSCJXX.XF is ''学分''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186831665377280','1880186819095048194','upgrade','add','column','main-abdposyjscjxx-xf',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"number","main-datamodelcolumn-columnJavaname":"xf","main-datamodelcolumn-columnLabel":"学分","main-datamodelcolumn-columnWidth":5,"main-datamodelcolumn-columnXtype":"number","main-datamodelcolumn-columnJavatype":"java.lang.Double","main-datamodelcolumn-columnDbname":"XF","main-datamodelcolumn-orderIndex":6,"main-datamodelcolumn-columnDbtype":8}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186831665377280');
		-- 新增【字段】：main-abdposyjscjxx-xh
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdposyjscjxx',1,0,'xh',0,'text','学号',100,0,'main-abdposyjscjxx-xh',0,'text',0,1,'java.lang.String',1,'XH',1,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdposyjscjxx-xh');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_POS_YJSCJXX ADD XH VARCHAR2(100)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_POS_YJSCJXX.XH is ''学号''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186831669571584','1880186819095048194','upgrade','add','column','main-abdposyjscjxx-xh',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"xh","main-datamodelcolumn-columnLabel":"学号","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XH","main-datamodelcolumn-orderIndex":1,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186831669571584');
		-- 新增【字段】：main-abdposyjscjxx-xm
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdposyjscjxx',1,0,'xm',0,'text','姓名',100,0,'main-abdposyjscjxx-xm',0,'text',0,1,'java.lang.String',1,'XM',2,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdposyjscjxx-xm');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_POS_YJSCJXX ADD XM VARCHAR2(100)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_POS_YJSCJXX.XM is ''姓名''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186831677960192','1880186819095048194','upgrade','add','column','main-abdposyjscjxx-xm',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"xm","main-datamodelcolumn-columnLabel":"姓名","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XM","main-datamodelcolumn-orderIndex":2,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186831677960192');
		-- 新增【字段】：main-abdposyjscjxx-xnxq
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdposyjscjxx',1,0,'xnxq',0,'text','学年学期',100,0,'main-abdposyjscjxx-xnxq',0,'text',0,1,'java.lang.String',1,'XNXQ',8,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdposyjscjxx-xnxq');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_POS_YJSCJXX ADD XNXQ VARCHAR2(100)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_POS_YJSCJXX.XNXQ is ''学年学期''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186831682154496','1880186819095048194','upgrade','add','column','main-abdposyjscjxx-xnxq',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"xnxq","main-datamodelcolumn-columnLabel":"学年学期","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XNXQ","main-datamodelcolumn-orderIndex":8,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186831682154496');
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-abdposyjscjxx' and FIELD_NAME='main-damodelcolumn-entityTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdposyjscjxx(main-damodelcolumn-entityTag 是否实体标识)，跳过更新');
			ELSE
				update t_da_model_column_extend set entity_tag = '0'
				where model_id = 'main-abdposyjscjxx';
				update t_da_model_column_extend set entity_tag = '1'
				where id in ('main-abdposyjscjxx-xh');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1880186831753457664','1880186819095048194','upgrade','update','model','main-abdposyjscjxx','main-damodelcolumn-entityTag','{"变更前":{"main-damodelcolumn-entityTag":""},"变更后":{"main-damodelcolumn-entityTag":"学号"}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1880186831753457664');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdposyjscjxx(main-damodelcolumn-entityTag 是否实体标识),sql执行错误:' || SQLERRM);
		END;
		
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-abdposyjscjxx' and FIELD_NAME='main-damodelcolumn-timeTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdposyjscjxx(main-damodelcolumn-timeTag 是否时点标识字段)，跳过更新');
			ELSE
				update t_da_model_column_extend set time_tag = '0'
				where model_id = 'main-abdposyjscjxx';
				update t_da_model_column_extend set time_tag = '1'
				where id in ('main-abdposyjscjxx-lrsj');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1880186831761846272','1880186819095048194','upgrade','update','model','main-abdposyjscjxx','main-damodelcolumn-timeTag','{"变更前":{"main-damodelcolumn-timeTag":""},"变更后":{"main-damodelcolumn-timeTag":"录入时间"}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1880186831761846272');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdposyjscjxx(main-damodelcolumn-timeTag 是否时点标识字段),sql执行错误:' || SQLERRM);
		END;
		
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-abdposyjscjxx' and FIELD_NAME='main-damodelcolumn-primaryColumnTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdposyjscjxx(main-damodelcolumn-primaryColumnTag 是否主键字段)，跳过更新');
			ELSE
				update t_da_model_column_extend set primary_column_tag = '0'
				where model_id = 'main-abdposyjscjxx';
				update t_da_model_column_extend set primary_column_tag = '1'
				where id in ('main-abdposyjscjxx-id');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1880186831766040576','1880186819095048194','upgrade','update','model','main-abdposyjscjxx','main-damodelcolumn-primaryColumnTag','{"变更前":{"main-damodelcolumn-primaryColumnTag":""},"变更后":{"main-damodelcolumn-primaryColumnTag":"ID"}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1880186831766040576');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdposyjscjxx(main-damodelcolumn-primaryColumnTag 是否主键字段),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.0',internal_version='0' WHERE model_id ='main-abdposyjscjxx';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdposyjscjxx成功,模型版本号更新为:V1.0.0,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-abdposyjsjbxx ↓----------------------------------------------
--模型：研究生基本信息 ABD_POS_YJSJBXX main-abdposyjsjbxx
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdposyjsjbxx'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdposyjsjbxx版本号:'||v_version);
	IF v_version >= 'V1.0.3.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdposyjsjbxx的本次增量版本号:V1.0.3,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1880186819095048195','main-abdposyjsjbxx','V1.0.3','0','upgrade','update','研究生基本信息','jcsj','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1880186819095048195');
		-- 新增【字段扩展】：main-abdposyjsjbxx-kygjc
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdposyjsjbxx',0,'main-abdposyjsjbxx-kygjc','main-abdposyjsjbxx-kygjc',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdposyjsjbxx-kygjc');
		-- 新增【字段扩展】：main-abdposyjsjbxx-xp
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdposyjsjbxx',0,'main-abdposyjsjbxx-xp','main-abdposyjsjbxx-xp',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdposyjsjbxx-xp');
		-- 新增【字段】：main-abdposyjsjbxx-kygjc
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdposyjsjbxx',1,0,'kygjc',0,'text','科研关键词',1000,0,'main-abdposyjsjbxx-kygjc',0,'text',0,1,'java.lang.String',1,'KYGJC',44,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdposyjsjbxx-kygjc');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_POS_YJSJBXX ADD KYGJC VARCHAR2(1000)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_POS_YJSJBXX.KYGJC is ''科研关键词''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186831833149440','1880186819095048195','upgrade','add','column','main-abdposyjsjbxx-kygjc',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"kygjc","main-datamodelcolumn-columnLabel":"科研关键词","main-datamodelcolumn-columnWidth":1000,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"KYGJC","main-datamodelcolumn-orderIndex":44,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186831833149440');
		-- 新增【字段】：main-abdposyjsjbxx-xp
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'picture','main-abdposyjsjbxx',1,0,'xp',0,'照片',1000,0,'main-abdposyjsjbxx-xp',0,'uploadfile',0,1,'java.sql.Blob',1,'XP',43,2004,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdposyjsjbxx-xp');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_POS_YJSJBXX ADD XP BLOB';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_POS_YJSJBXX.XP is ''照片''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186831854120960','1880186819095048195','upgrade','add','column','main-abdposyjsjbxx-xp',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"picture","main-datamodelcolumn-columnJavaname":"xp","main-datamodelcolumn-columnLabel":"照片","main-datamodelcolumn-columnWidth":1000,"main-datamodelcolumn-columnXtype":"uploadfile","main-datamodelcolumn-columnJavatype":"java.sql.Blob","main-datamodelcolumn-columnDbname":"XP","main-datamodelcolumn-orderIndex":43,"main-datamodelcolumn-columnDbtype":2004}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186831854120960');
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.3',internal_version='0' WHERE model_id ='main-abdposyjsjbxx';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdposyjsjbxx成功,模型版本号更新为:V1.0.3,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-abdposyjszcxx ↓----------------------------------------------
--模型：研究生综测信息 ABD_POS_YJSZCXX main-abdposyjszcxx
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdposyjszcxx'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdposyjszcxx版本号:'||v_version);
	IF v_version >= 'V1.0.0.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdposyjszcxx的本次增量版本号:V1.0.0,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1880186819095048196','main-abdposyjszcxx','V1.0.0','0','upgrade','add','研究生综测信息','jcsj','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1880186819095048196');
		-- 新增【模型扩展】：main-abdposyjszcxx
		insert into t_da_model_extend (model_encoding,access_level,model_id,model_version,model_enable,entity_id,model_type,use_num,theme_code,internal_version,id,create_by,create_time,update_by,update_time)
		select 'ABD_POS_0023','1','main-abdposyjszcxx','V1.0.0','1','POSTGRADUATE','jcsj',0,'POS','0','main-abdposyjszcxx','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_extend
			where id = 'main-abdposyjszcxx');
		-- 新增【模型使用范围】:1736411781406001243
		insert into t_da_model_theme (model_id,theme_code,id,create_by,create_time,update_by,update_time)
		select 'main-abdposyjszcxx','POS','1736411781406001243','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_theme
			where id = '1736411781406001243');
		-- 新增【模型】：main-abdposyjszcxx
		insert into lowcode_model (model_app,publish_time,sys_flag,model_class,permit_ext_flag,extend_flag,model_label,model_name,model_table,model_classify,virtual_model,biz_model,order_index,id,status,create_by,create_time,update_by,update_time)
		select 'main',TIMESTAMP '2025-01-09 16:37:41',0,'com.wisedu.lowcode4j.main.po.Abdposyjszcxx',1,1,'研究生综测信息','abdposyjszcxx','ABD_POS_YJSZCXX','POSTGRADUATE_PYGC_JCSJ',1,0,724,'main-abdposyjszcxx',1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model
			where id = 'main-abdposyjszcxx');
		BEGIN
		EXECUTE IMMEDIATE 'CREATE TABLE ABD_POS_YJSZCXX(ID VARCHAR2(100) DEFAULT SYS_GUID() primary key ,CREATE_BY VARCHAR2(100),CREATE_TIME DATE,UPDATE_BY VARCHAR2(100),UPDATE_TIME DATE)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-955) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on table ABD_POS_YJSZCXX is ''研究生综测信息''';
		END;
		-- 新增【字段扩展】：main-abdposyjszcxx-createBy
		insert into t_da_model_column_extend (time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdposyjszcxx',0,'main-abdposyjszcxx-createBy','main-abdposyjszcxx-createBy',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdposyjszcxx-createBy');
		-- 新增【字段扩展】：main-abdposyjszcxx-createTime
		insert into t_da_model_column_extend (time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdposyjszcxx',0,'main-abdposyjszcxx-createTime','main-abdposyjszcxx-createTime',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdposyjszcxx-createTime');
		-- 新增【字段扩展】：main-abdposyjszcxx-id
		insert into t_da_model_column_extend (time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,primary_column_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdposyjszcxx',0,'main-abdposyjszcxx-id','main-abdposyjszcxx-id',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdposyjszcxx-id');
		-- 新增【字段扩展】：main-abdposyjszcxx-updateBy
		insert into t_da_model_column_extend (time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdposyjszcxx',0,'main-abdposyjszcxx-updateBy','main-abdposyjszcxx-updateBy',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdposyjszcxx-updateBy');
		-- 新增【字段扩展】：main-abdposyjszcxx-updateTime
		insert into t_da_model_column_extend (time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdposyjszcxx',0,'main-abdposyjszcxx-updateTime','main-abdposyjszcxx-updateTime',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdposyjszcxx-updateTime');
		-- 新增【字段扩展】：main-abdposyjszcxx-xh
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdposyjszcxx',0,'main-abdposyjszcxx-xh','main-abdposyjszcxx-xh',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdposyjszcxx-xh');
		-- 新增【字段扩展】：main-abdposyjszcxx-xnxq
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdposyjszcxx',0,'main-abdposyjszcxx-xnxq','main-abdposyjszcxx-xnxq',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdposyjszcxx-xnxq');
		-- 新增【字段扩展】：main-abdposyjszcxx-zcfs
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdposyjszcxx',0,'main-abdposyjszcxx-zcfs','main-abdposyjszcxx-zcfs',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdposyjszcxx-zcfs');
		-- 新增【字段】：main-abdposyjszcxx-createBy
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-abdposyjszcxx',1,'createBy',0,'创建人',100,0,'main-abdposyjszcxx-createBy',1,0,1,'java.lang.String',0,0,1,'CREATE_BY',0,301,12,1,0,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdposyjszcxx-createBy');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_POS_YJSZCXX.CREATE_BY is ''创建人''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186831946395648','1880186819095048196','upgrade','add','column','main-abdposyjszcxx-createBy',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"createBy","main-datamodelcolumn-columnLabel":"创建人","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"CREATE_BY","main-datamodelcolumn-orderIndex":301,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186831946395648');
		-- 新增【字段】：main-abdposyjszcxx-createTime
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-abdposyjszcxx',1,'createTime',0,'datetime-range','创建时间',0,0,'main-abdposyjszcxx-createTime',1,'date-full',0,1,'java.util.Date',0,0,1,'CREATE_TIME',0,302,93,1,0,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdposyjszcxx-createTime');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_POS_YJSZCXX.CREATE_TIME is ''创建时间''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186831954784256','1880186819095048196','upgrade','add','column','main-abdposyjszcxx-createTime',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"createTime","main-datamodelcolumn-columnLabel":"创建时间","main-datamodelcolumn-columnWidth":0,"main-datamodelcolumn-columnXtype":"date-full","main-datamodelcolumn-columnJavatype":"java.util.Date","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"CREATE_TIME","main-datamodelcolumn-orderIndex":302,"main-datamodelcolumn-columnDbtype":93}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186831954784256');
		-- 新增【字段】：main-abdposyjszcxx-id
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-abdposyjszcxx',1,'id',0,'ID',100,0,'main-abdposyjszcxx-id',1,0,1,'java.lang.String',0,0,1,'ID',0,300,12,1,0,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdposyjszcxx-id');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_POS_YJSZCXX.ID is ''ID''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186831958978560','1880186819095048196','upgrade','add','column','main-abdposyjszcxx-id',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"id","main-datamodelcolumn-columnLabel":"ID","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"ID","main-datamodelcolumn-orderIndex":300,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186831958978560');
		-- 新增【字段】：main-abdposyjszcxx-updateBy
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-abdposyjszcxx',1,'updateBy',0,'更新人',100,0,'main-abdposyjszcxx-updateBy',1,0,1,'java.lang.String',0,0,1,'UPDATE_BY',0,303,12,1,0,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdposyjszcxx-updateBy');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_POS_YJSZCXX.UPDATE_BY is ''更新人''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186831963172864','1880186819095048196','upgrade','add','column','main-abdposyjszcxx-updateBy',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"updateBy","main-datamodelcolumn-columnLabel":"更新人","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"UPDATE_BY","main-datamodelcolumn-orderIndex":303,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186831963172864');
		-- 新增【字段】：main-abdposyjszcxx-updateTime
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-abdposyjszcxx',1,'updateTime',0,'datetime-range','更新时间',0,0,'main-abdposyjszcxx-updateTime',1,'date-full',0,1,'java.util.Date',0,0,1,'UPDATE_TIME',0,304,93,1,0,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdposyjszcxx-updateTime');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_POS_YJSZCXX.UPDATE_TIME is ''更新时间''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186831971561472','1880186819095048196','upgrade','add','column','main-abdposyjszcxx-updateTime',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"updateTime","main-datamodelcolumn-columnLabel":"更新时间","main-datamodelcolumn-columnWidth":0,"main-datamodelcolumn-columnXtype":"date-full","main-datamodelcolumn-columnJavatype":"java.util.Date","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"UPDATE_TIME","main-datamodelcolumn-orderIndex":304,"main-datamodelcolumn-columnDbtype":93}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186831971561472');
		-- 新增【字段】：main-abdposyjszcxx-xh
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdposyjszcxx',1,0,'xh',0,'text','学号',100,0,'main-abdposyjszcxx-xh',0,'text',0,1,'java.lang.String',1,'XH',1,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdposyjszcxx-xh');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_POS_YJSZCXX ADD XH VARCHAR2(100)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_POS_YJSZCXX.XH is ''学号''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186831975755776','1880186819095048196','upgrade','add','column','main-abdposyjszcxx-xh',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"xh","main-datamodelcolumn-columnLabel":"学号","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XH","main-datamodelcolumn-orderIndex":1,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186831975755776');
		-- 新增【字段】：main-abdposyjszcxx-xnxq
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdposyjszcxx',1,0,'xnxq',0,'text','学年学期',100,0,'main-abdposyjszcxx-xnxq',0,'text',0,1,'java.lang.String',1,'XNXQ',3,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdposyjszcxx-xnxq');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_POS_YJSZCXX ADD XNXQ VARCHAR2(100)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_POS_YJSZCXX.XNXQ is ''学年学期''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186831984144384','1880186819095048196','upgrade','add','column','main-abdposyjszcxx-xnxq',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"xnxq","main-datamodelcolumn-columnLabel":"学年学期","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XNXQ","main-datamodelcolumn-orderIndex":3,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186831984144384');
		-- 新增【字段】：main-abdposyjszcxx-zcfs
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'number','main-abdposyjszcxx',1,0,'zcfs',0,'number-range','综测分数',5,0,'main-abdposyjszcxx-zcfs',2,0,'number',0,1,'java.lang.Double',1,'ZCFS',2,8,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdposyjszcxx-zcfs');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_POS_YJSZCXX ADD ZCFS NUMBER(5,2)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_POS_YJSZCXX.ZCFS is ''综测分数''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186831988338688','1880186819095048196','upgrade','add','column','main-abdposyjszcxx-zcfs',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"number","main-datamodelcolumn-columnJavaname":"zcfs","main-datamodelcolumn-columnLabel":"综测分数","main-datamodelcolumn-columnWidth":5,"main-datamodelcolumn-columnXtype":"number","main-datamodelcolumn-columnJavatype":"java.lang.Double","main-datamodelcolumn-columnDbname":"ZCFS","main-datamodelcolumn-orderIndex":2,"main-datamodelcolumn-columnDbtype":8}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186831988338688');
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-abdposyjszcxx' and FIELD_NAME='main-damodelcolumn-entityTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdposyjszcxx(main-damodelcolumn-entityTag 是否实体标识)，跳过更新');
			ELSE
				update t_da_model_column_extend set entity_tag = '0'
				where model_id = 'main-abdposyjszcxx';
				update t_da_model_column_extend set entity_tag = '1'
				where id in ('main-abdposyjszcxx-xh');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1880186832055447552','1880186819095048196','upgrade','update','model','main-abdposyjszcxx','main-damodelcolumn-entityTag','{"变更前":{"main-damodelcolumn-entityTag":""},"变更后":{"main-damodelcolumn-entityTag":"学号"}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1880186832055447552');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdposyjszcxx(main-damodelcolumn-entityTag 是否实体标识),sql执行错误:' || SQLERRM);
		END;
		
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-abdposyjszcxx' and FIELD_NAME='main-damodelcolumn-primaryColumnTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdposyjszcxx(main-damodelcolumn-primaryColumnTag 是否主键字段)，跳过更新');
			ELSE
				update t_da_model_column_extend set primary_column_tag = '0'
				where model_id = 'main-abdposyjszcxx';
				update t_da_model_column_extend set primary_column_tag = '1'
				where id in ('main-abdposyjszcxx-id');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1880186832059641856','1880186819095048196','upgrade','update','model','main-abdposyjszcxx','main-damodelcolumn-primaryColumnTag','{"变更前":{"main-damodelcolumn-primaryColumnTag":""},"变更后":{"main-damodelcolumn-primaryColumnTag":"ID"}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1880186832059641856');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdposyjszcxx(main-damodelcolumn-primaryColumnTag 是否主键字段),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.0',internal_version='0' WHERE model_id ='main-abdposyjszcxx';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdposyjszcxx成功,模型版本号更新为:V1.0.0,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-abdschxxjbxx ↓----------------------------------------------
--模型：学校基本信息 ABD_SCH_XXJBXX main-abdschxxjbxx
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdschxxjbxx'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdschxxjbxx版本号:'||v_version);
	IF v_version >= 'V1.0.8.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdschxxjbxx的本次增量版本号:V1.0.8,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1880186819095048197','main-abdschxxjbxx','V1.0.8','0','upgrade','update','学校基本信息','jcsj','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1880186819095048197');
		-- 修改【字段】：main-abdschxxjbxx-xxbxlx columnWidth
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-abdschxxjbxx-xxbxlx' and FIELD_NAME='main-datamodelcolumn-columnWidth';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdschxxjbxx-xxbxlx(main-datamodelcolumn-columnWidth 字段数据库长度)，跳过更新');
			ELSE
				BEGIN
				EXECUTE IMMEDIATE 'ALTER TABLE ABD_SCH_XXJBXX MODIFY XXBXLX VARCHAR2(30)';
				EXCEPTION
				WHEN OTHERS THEN
					IF SQLCODE NOT IN (-942,-904) THEN
						RAISE;
					END IF;
				END;
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1880186832093196288','1880186819095048197','upgrade','update','column','main-abdschxxjbxx-xxbxlx','main-datamodelcolumn-columnWidth','{"变更前":{"main-datamodelcolumn-columnJavaname":"xxbxlx","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XXBXLX","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":9,"main-datamodelcolumn-columnLabel":"学校办学类型","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":9,"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnDict":"XXBXLX","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnWidth":"30"}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1880186832093196288');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdschxxjbxx-xxbxlx(main-datamodelcolumn-columnWidth 字段数据库长度),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.8',internal_version='0' WHERE model_id ='main-abdschxxjbxx';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdschxxjbxx成功,模型版本号更新为:V1.0.8,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-abdsubjscykyxm ↓----------------------------------------------
--模型：教师参与科研项目 ABD_SUB_JSCYKYXM main-abdsubjscykyxm
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdsubjscykyxm'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdsubjscykyxm版本号:'||v_version);
	IF v_version >= 'V1.0.1.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdsubjscykyxm的本次增量版本号:V1.0.1,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1880186819095048198','main-abdsubjscykyxm','V1.0.1','0','upgrade','update','教师参与科研项目','jcsj','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1880186819095048198');
		-- 新增【字段扩展】：main-abdsubjscykyxm-xmlxbz
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdsubjscykyxm',0,'main-abdsubjscykyxm-xmlxbz','main-abdsubjscykyxm-xmlxbz',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdsubjscykyxm-xmlxbz');
		-- 新增【字段】：main-abdsubjscykyxm-xmlxbz
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdsubjscykyxm',1,0,'xmlxbz',0,'text','项目类型（标准）',100,0,'main-abdsubjscykyxm-xmlxbz',0,'text',0,1,'java.lang.String',1,'XMLXBZ',21,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdsubjscykyxm-xmlxbz');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_SUB_JSCYKYXM ADD XMLXBZ VARCHAR2(100)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_SUB_JSCYKYXM.XMLXBZ is ''项目类型（标准）''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832185470976','1880186819095048198','upgrade','add','column','main-abdsubjscykyxm-xmlxbz',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"xmlxbz","main-datamodelcolumn-columnLabel":"项目类型（标准）","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XMLXBZ","main-datamodelcolumn-orderIndex":21,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832185470976');
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.1',internal_version='0' WHERE model_id ='main-abdsubjscykyxm';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdsubjscykyxm成功,模型版本号更新为:V1.0.1,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-abdsubjshjxcgj ↓----------------------------------------------
--模型：教师获教学成果奖 ABD_SUB_JSHJXCGJ main-abdsubjshjxcgj
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdsubjshjxcgj'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdsubjshjxcgj版本号:'||v_version);
	IF v_version >= 'V1.0.2.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdsubjshjxcgj的本次增量版本号:V1.0.2,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1880186819095048199','main-abdsubjshjxcgj','V1.0.2','0','upgrade','update','教师获教学成果奖','jcsj','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1880186819095048199');
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-abdsubjshjxcgj-hjlx' and FIELD_NAME='main-damodelcolumn-labelDesc';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdsubjshjxcgj-hjlx(main-damodelcolumn-labelDesc 标签说明)，跳过更新');
			ELSE
				-- 修改【字段扩展】：main-abdsubjshjxcgj-hjlx labelDesc
				update t_da_model_column_extend set label_desc = '获奖类型：国家级、省部级、地市级'
				where id = 'main-abdsubjshjxcgj-hjlx';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1880186832252579840','1880186819095048199','upgrade','update','column','main-abdsubjshjxcgj-hjlx','main-damodelcolumn-labelDesc','{"变更前":{"main-damodelcolumn-labelDesc":"获奖类型"},"变更后":{"main-damodelcolumn-labelDesc":"获奖类型：国家级、省部级、地市级"}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1880186832252579840');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdsubjshjxcgj-hjlx(main-damodelcolumn-labelDesc 标签说明),sql执行错误:' || SQLERRM);
		END;
		
		-- 修改【字段】：main-abdsubjshjxcgj-hjlx columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-abdsubjshjxcgj-hjlx' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdsubjshjxcgj-hjlx(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'XMJB'
				where id = 'main-abdsubjshjxcgj-hjlx';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1880186832277745664','1880186819095048199','upgrade','update','column','main-abdsubjshjxcgj-hjlx','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"hjlx","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"HJLX","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":300,"main-datamodelcolumn-columnLabel":"获奖类型","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":6,"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"XMJB"}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1880186832277745664');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdsubjshjxcgj-hjlx(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.2',internal_version='0' WHERE model_id ='main-abdsubjshjxcgj';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdsubjshjxcgj成功,模型版本号更新为:V1.0.2,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-abdsubjskycghj ↓----------------------------------------------
--模型：教师科研成果获奖 ABD_SUB_JSKYCGHJ main-abdsubjskycghj
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdsubjskycghj'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdsubjskycghj版本号:'||v_version);
	IF v_version >= 'V1.0.1.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdsubjskycghj的本次增量版本号:V1.0.1,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1880186819095048200','main-abdsubjskycghj','V1.0.1','0','upgrade','update','教师科研成果获奖','jcsj','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1880186819095048200');
		-- 修改【字段】：main-abdsubjskycghj-kyhjjbfl columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-abdsubjskycghj-kyhjjbfl' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdsubjskycghj-kyhjjbfl(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'XMJB'
				where id = 'main-abdsubjskycghj-kyhjjbfl';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1880186832370020352','1880186819095048200','upgrade','update','column','main-abdsubjskycghj-kyhjjbfl','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"kyhjjbfl","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"KYHJJBFL","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":40,"main-datamodelcolumn-columnLabel":"科研获奖级别分类","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":14,"main-datamodelcolumn-logicalType":"quoteCode","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"XMJB"}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1880186832370020352');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdsubjskycghj-kyhjjbfl(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.1',internal_version='0' WHERE model_id ='main-abdsubjskycghj';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdsubjskycghj成功,模型版本号更新为:V1.0.1,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-abdsubrxylkc ↓----------------------------------------------
--模型：入选一流课程 ABD_SUB_RXYLKC main-abdsubrxylkc
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdsubrxylkc'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdsubrxylkc版本号:'||v_version);
	IF v_version >= 'V1.0.1.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdsubrxylkc的本次增量版本号:V1.0.1,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1880186819095048201','main-abdsubrxylkc','V1.0.1','0','upgrade','update','入选一流课程','jcsj','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1880186819095048201');
		-- 新增【字段扩展】：main-abdsubrxylkc-zgh
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdsubrxylkc',0,'main-abdsubrxylkc-zgh','main-abdsubrxylkc-zgh',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdsubrxylkc-zgh');
		-- 新增【字段】：main-abdsubrxylkc-zgh
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdsubrxylkc',1,0,'zgh',0,'text','职工号',10,0,'main-abdsubrxylkc-zgh',0,'text',0,1,'java.lang.String',1,'ZGH',8,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdsubrxylkc-zgh');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_SUB_RXYLKC ADD ZGH VARCHAR2(10)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_SUB_RXYLKC.ZGH is ''职工号''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832453906432','1880186819095048201','upgrade','add','column','main-abdsubrxylkc-zgh',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"zgh","main-datamodelcolumn-columnLabel":"职工号","main-datamodelcolumn-columnWidth":10,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"ZGH","main-datamodelcolumn-orderIndex":8,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832453906432');
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.1',internal_version='0' WHERE model_id ='main-abdsubrxylkc';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdsubrxylkc成功,模型版本号更新为:V1.0.1,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-abdtrapyfa ↓----------------------------------------------
--模型：培养方案 ABD_TRA_PYFA main-abdtrapyfa
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdtrapyfa'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdtrapyfa版本号:'||v_version);
	IF v_version >= 'V1.0.3.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdtrapyfa的本次增量版本号:V1.0.3,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1880186819095048202','main-abdtrapyfa','V1.0.3','0','upgrade','add','培养方案','jcsj','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1880186819095048202');
		-- 新增【模型扩展】：main-abdtrapyfa
		insert into t_da_model_extend (model_encoding,access_level,model_id,model_version,model_enable,entity_id,model_type,primary_model,use_num,update_mode,theme_code,model_desc,internal_version,id,mark_object_id,create_by,create_time,update_by,update_time)
		select 'ABD_TRA_0003','1','main-abdtrapyfa','V1.0.3','1','TRAINNING PLAN','jcsj','1',0,'直接更新','INS','用于存储和管理学校或院系的培养方案信息，包括培养方案的名称、目标、课程设置、学分要求等。','0','main-abdtrapyfa',NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_extend
			where id = 'main-abdtrapyfa');
		-- 新增【模型使用范围】:1737011243025001243
		insert into t_da_model_theme (model_id,theme_code,id,create_by,create_time,update_by,update_time)
		select 'main-abdtrapyfa','POS','1737011243025001243','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_theme
			where id = '1737011243025001243');
		-- 新增【模型】：main-abdtrapyfa
		insert into lowcode_model (model_app,publish_time,sys_flag,model_class,permit_ext_flag,extend_flag,model_label,model_name,model_table,model_classify,virtual_model,biz_model,order_index,id,status,create_by,create_time,update_by,update_time)
		select 'main',TIMESTAMP '2024-06-20 01:00:00',0,'com.wisedu.lowcode4j.main.po.Abdtrapyfa',1,1,'培养方案','abdtrapyfa','ABD_TRA_PYFA','TRAINNING PLAN_JXXGXX_JCSJ',1,0,541,'main-abdtrapyfa',1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model
			where id = 'main-abdtrapyfa');
		BEGIN
		EXECUTE IMMEDIATE 'CREATE TABLE ABD_TRA_PYFA(ID VARCHAR2(100) DEFAULT SYS_GUID() primary key ,CREATE_BY VARCHAR2(100),CREATE_TIME DATE,UPDATE_BY VARCHAR2(100),UPDATE_TIME DATE)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-955) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on table ABD_TRA_PYFA is ''培养方案''';
		END;
		-- 新增【字段扩展】：main-abdtrapyfa-createBy
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdtrapyfa','main-abdtrapyfa-createBy','V1.0.2',NULL,NULL,'0',0,'创建人','main-abdtrapyfa-createBy',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdtrapyfa-createBy');
		-- 新增【字段扩展】：main-abdtrapyfa-createTime
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdtrapyfa','main-abdtrapyfa-createTime','V1.0.2',NULL,NULL,'0',0,'创建时间','main-abdtrapyfa-createTime',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdtrapyfa-createTime');
		-- 新增【字段扩展】：main-abdtrapyfa-dlxq
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdtrapyfa','main-abdtrapyfa-dlxq','V1.0.2',NULL,NULL,'0',0,'大类学期','main-abdtrapyfa-dlxq',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdtrapyfa-dlxq');
		-- 新增【字段扩展】：main-abdtrapyfa-dwdm
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdtrapyfa','main-abdtrapyfa-dwdm','V1.0.2',NULL,NULL,'0',0,'单位','main-abdtrapyfa-dwdm',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdtrapyfa-dwdm');
		-- 新增【字段扩展】：main-abdtrapyfa-faccdm
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdtrapyfa','main-abdtrapyfa-faccdm','V1.0.2',NULL,NULL,'0',0,'方案层次','main-abdtrapyfa-faccdm',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdtrapyfa-faccdm');
		-- 新增【字段扩展】：main-abdtrapyfa-falxdm
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdtrapyfa','main-abdtrapyfa-falxdm','V1.0.2',NULL,NULL,'0',0,'方案类型','main-abdtrapyfa-falxdm',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdtrapyfa-falxdm');
		-- 新增【字段扩展】：main-abdtrapyfa-faztdm
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdtrapyfa','main-abdtrapyfa-faztdm','V1.0.2',NULL,NULL,'0',0,'方案状态','main-abdtrapyfa-faztdm',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdtrapyfa-faztdm');
		-- 新增【字段扩展】：main-abdtrapyfa-id
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdtrapyfa','main-abdtrapyfa-id','V1.0.2',NULL,NULL,'0',0,'ID','main-abdtrapyfa-id',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdtrapyfa-id');
		-- 新增【字段扩展】：main-abdtrapyfa-jcdlfadm
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdtrapyfa','main-abdtrapyfa-jcdlfadm','V1.0.2',NULL,NULL,'0',0,'继承大类方案代码','main-abdtrapyfa-jcdlfadm',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdtrapyfa-jcdlfadm');
		-- 新增【字段扩展】：main-abdtrapyfa-ksxndm
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdtrapyfa','main-abdtrapyfa-ksxndm','V1.0.2',NULL,NULL,'0',0,'开始学年','main-abdtrapyfa-ksxndm',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdtrapyfa-ksxndm');
		-- 新增【字段扩展】：main-abdtrapyfa-ksxqdm
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdtrapyfa','main-abdtrapyfa-ksxqdm','V1.0.2',NULL,NULL,'0',0,'开始学期','main-abdtrapyfa-ksxqdm',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdtrapyfa-ksxqdm');
		-- 新增【字段扩展】：main-abdtrapyfa-njdm
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdtrapyfa','main-abdtrapyfa-njdm','V1.0.2',NULL,NULL,'0',0,'年级','main-abdtrapyfa-njdm',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdtrapyfa-njdm');
		-- 新增【字段扩展】：main-abdtrapyfa-pyccdm
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdtrapyfa','main-abdtrapyfa-pyccdm','V1.0.2',NULL,NULL,'0',0,'培养层次','main-abdtrapyfa-pyccdm',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdtrapyfa-pyccdm');
		-- 新增【字段扩展】：main-abdtrapyfa-pyfadm
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdtrapyfa','main-abdtrapyfa-pyfadm','V1.0.2',NULL,NULL,'0',0,'培养方案代码','main-abdtrapyfa-pyfadm',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdtrapyfa-pyfadm');
		-- 新增【字段扩展】：main-abdtrapyfa-pyfamc
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdtrapyfa','main-abdtrapyfa-pyfamc','V1.0.2',NULL,NULL,'0',0,'培养方案名称','main-abdtrapyfa-pyfamc',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdtrapyfa-pyfamc');
		-- 新增【字段扩展】：main-abdtrapyfa-sfjcdlfa
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdtrapyfa','main-abdtrapyfa-sfjcdlfa','V1.0.2',NULL,NULL,'0',0,'是否继承大类方案','main-abdtrapyfa-sfjcdlfa',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdtrapyfa-sfjcdlfa');
		-- 新增【字段扩展】：main-abdtrapyfa-sfyc
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdtrapyfa','main-abdtrapyfa-sfyc','V1.0.2',NULL,NULL,'0',0,'是否异常','main-abdtrapyfa-sfyc',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdtrapyfa-sfyc');
		-- 新增【字段扩展】：main-abdtrapyfa-updateBy
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdtrapyfa','main-abdtrapyfa-updateBy','V1.0.2',NULL,NULL,'0',0,'更新人','main-abdtrapyfa-updateBy',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdtrapyfa-updateBy');
		-- 新增【字段扩展】：main-abdtrapyfa-updateTime
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdtrapyfa','main-abdtrapyfa-updateTime','V1.0.2',NULL,NULL,'0',0,'更新时间','main-abdtrapyfa-updateTime',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdtrapyfa-updateTime');
		-- 新增【字段扩展】：main-abdtrapyfa-xdlxdm
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdtrapyfa','main-abdtrapyfa-xdlxdm','V1.0.2',NULL,NULL,'0',0,'修读类型','main-abdtrapyfa-xdlxdm',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdtrapyfa-xdlxdm');
		-- 新增【字段扩展】：main-abdtrapyfa-xlccdm
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdtrapyfa','main-abdtrapyfa-xlccdm','V1.0.2',NULL,NULL,'0',0,'学历层次','main-abdtrapyfa-xlccdm',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdtrapyfa-xlccdm');
		-- 新增【字段扩展】：main-abdtrapyfa-xqlxdm
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdtrapyfa','main-abdtrapyfa-xqlxdm','V1.0.2',NULL,NULL,'0',0,'学期类型','main-abdtrapyfa-xqlxdm',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdtrapyfa-xqlxdm');
		-- 新增【字段扩展】：main-abdtrapyfa-xwdm
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdtrapyfa','main-abdtrapyfa-xwdm','V1.0.2',NULL,NULL,'0',0,'学位','main-abdtrapyfa-xwdm',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdtrapyfa-xwdm');
		-- 新增【字段扩展】：main-abdtrapyfa-xznx
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdtrapyfa','main-abdtrapyfa-xznx','V1.0.2',NULL,NULL,'0',0,'学制年限','main-abdtrapyfa-xznx',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdtrapyfa-xznx');
		-- 新增【字段扩展】：main-abdtrapyfa-ypyfadm
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdtrapyfa','main-abdtrapyfa-ypyfadm','V1.0.2',NULL,NULL,'0',0,'原培养方案代码','main-abdtrapyfa-ypyfadm',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdtrapyfa-ypyfadm');
		-- 新增【字段扩展】：main-abdtrapyfa-zsyqxf
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdtrapyfa','main-abdtrapyfa-zsyqxf','V1.0.2',NULL,NULL,'0',0,'最少要求学分','main-abdtrapyfa-zsyqxf',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdtrapyfa-zsyqxf');
		-- 新增【字段扩展】：main-abdtrapyfa-zydm
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdtrapyfa','main-abdtrapyfa-zydm','V1.0.2',NULL,NULL,'0',0,'专业','main-abdtrapyfa-zydm',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdtrapyfa-zydm');
		-- 新增【字段扩展】：main-abdtrapyfa-zyfxdm
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdtrapyfa','main-abdtrapyfa-zyfxdm','V1.0.2',NULL,NULL,'0',0,'专业方向','main-abdtrapyfa-zyfxdm',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdtrapyfa-zyfxdm');
		-- 新增【字段】：main-abdtrapyfa-createBy
		insert into lowcode_model_column (logical_type,model_id,column_display,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdtrapyfa',1,'createBy',0,'text','创建人',300,0,'main-abdtrapyfa-createBy',0,1,'text',0,0,'java.lang.String',0,1,'CREATE_BY',0,24,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdtrapyfa-createBy');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_TRA_PYFA.CREATE_BY is ''创建人''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832567152640','1880186819095048202','upgrade','add','column','main-abdtrapyfa-createBy',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"createBy","main-datamodelcolumn-columnLabel":"创建人","main-datamodelcolumn-columnWidth":300,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"CREATE_BY","main-datamodelcolumn-orderIndex":24,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832567152640');
		-- 新增【字段】：main-abdtrapyfa-createTime
		insert into lowcode_model_column (logical_type,model_id,column_display,column_javaname,column_readonly,search_xtype,column_label,event_time_point,id,column_format,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_pattern,column_dbname,search_jsonparam,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'datetime','main-abdtrapyfa',1,'createTime',0,'datetime-range','创建时间',0,'main-abdtrapyfa-createTime','yyyy-MM-dd HH:mm:ss',0,1,'date-full',0,0,'java.util.Date',0,1,'yyyy-MM-dd HH:mm:ss','CREATE_TIME','{"type": "datetimerange","format": "yyyy-MM-dd HH:mm:ss","value-format": "yyyy-MM-dd HH:mm:ss","pattern": "yyyy-MM-dd HH:mm:ss"}',0,25,93,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdtrapyfa-createTime');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_TRA_PYFA.CREATE_TIME is ''创建时间''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832571346944','1880186819095048202','upgrade','add','column','main-abdtrapyfa-createTime',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"datetime","main-datamodelcolumn-columnJavaname":"createTime","main-datamodelcolumn-columnLabel":"创建时间","main-datamodelcolumn-columnFormat":"yyyy-MM-dd HH:mm:ss","main-datamodelcolumn-columnXtype":"date-full","main-datamodelcolumn-columnJavatype":"java.util.Date","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnPattern":"yyyy-MM-dd HH:mm:ss","main-datamodelcolumn-columnDbname":"CREATE_TIME","main-datamodelcolumn-orderIndex":25,"main-datamodelcolumn-columnDbtype":93}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832571346944');
		-- 新增【字段】：main-abdtrapyfa-dlxq
		insert into lowcode_model_column (logical_type,model_id,column_display,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdtrapyfa',1,'dlxq',0,'text','大类学期',120,0,'main-abdtrapyfa-dlxq',0,0,'text',0,0,'java.lang.String',0,1,'DLXQ',0,22,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdtrapyfa-dlxq');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_TRA_PYFA ADD DLXQ VARCHAR2(120)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_TRA_PYFA.DLXQ is ''大类学期''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832579735552','1880186819095048202','upgrade','add','column','main-abdtrapyfa-dlxq',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"dlxq","main-datamodelcolumn-columnLabel":"大类学期","main-datamodelcolumn-columnWidth":120,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"DLXQ","main-datamodelcolumn-orderIndex":22,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832579735552');
		-- 新增【字段】：main-abdtrapyfa-dwdm
		insert into lowcode_model_column (logical_type,model_id,column_display,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdtrapyfa',1,'dwdm',0,'text','单位',60,0,'main-abdtrapyfa-dwdm',0,0,'text',0,0,'java.lang.String',0,1,'DWDM',0,5,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdtrapyfa-dwdm');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_TRA_PYFA ADD DWDM VARCHAR2(60)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_TRA_PYFA.DWDM is ''单位''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832588124160','1880186819095048202','upgrade','add','column','main-abdtrapyfa-dwdm',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"dwdm","main-datamodelcolumn-columnLabel":"单位","main-datamodelcolumn-columnWidth":60,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"DWDM","main-datamodelcolumn-orderIndex":5,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832588124160');
		-- 新增【字段】：main-abdtrapyfa-faccdm
		insert into lowcode_model_column (logical_type,model_id,column_display,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdtrapyfa',1,'faccdm',0,'text','方案层次',30,0,'main-abdtrapyfa-faccdm',0,0,'text',0,0,'java.lang.String',0,1,'FACCDM',0,3,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdtrapyfa-faccdm');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_TRA_PYFA ADD FACCDM VARCHAR2(30)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_TRA_PYFA.FACCDM is ''方案层次''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832592318464','1880186819095048202','upgrade','add','column','main-abdtrapyfa-faccdm',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"faccdm","main-datamodelcolumn-columnLabel":"方案层次","main-datamodelcolumn-columnWidth":30,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"FACCDM","main-datamodelcolumn-orderIndex":3,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832592318464');
		-- 新增【字段】：main-abdtrapyfa-falxdm
		insert into lowcode_model_column (logical_type,model_id,column_display,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdtrapyfa',1,'falxdm',0,'text','方案类型',30,0,'main-abdtrapyfa-falxdm',0,0,'text',0,0,'java.lang.String',0,1,'FALXDM',0,17,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdtrapyfa-falxdm');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_TRA_PYFA ADD FALXDM VARCHAR2(30)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_TRA_PYFA.FALXDM is ''方案类型''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832596512768','1880186819095048202','upgrade','add','column','main-abdtrapyfa-falxdm',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"falxdm","main-datamodelcolumn-columnLabel":"方案类型","main-datamodelcolumn-columnWidth":30,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"FALXDM","main-datamodelcolumn-orderIndex":17,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832596512768');
		-- 新增【字段】：main-abdtrapyfa-faztdm
		insert into lowcode_model_column (logical_type,model_id,column_display,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdtrapyfa',1,'faztdm',0,'text','方案状态',30,0,'main-abdtrapyfa-faztdm',0,0,'text',0,0,'java.lang.String',0,1,'FAZTDM',0,16,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdtrapyfa-faztdm');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_TRA_PYFA ADD FAZTDM VARCHAR2(30)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_TRA_PYFA.FAZTDM is ''方案状态''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832604901376','1880186819095048202','upgrade','add','column','main-abdtrapyfa-faztdm',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"faztdm","main-datamodelcolumn-columnLabel":"方案状态","main-datamodelcolumn-columnWidth":30,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"FAZTDM","main-datamodelcolumn-orderIndex":16,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832604901376');
		-- 新增【字段】：main-abdtrapyfa-id
		insert into lowcode_model_column (logical_type,model_id,column_display,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdtrapyfa',1,'id',0,'text','ID',300,0,'main-abdtrapyfa-id',0,1,'text',0,0,'java.lang.String',0,1,'ID',0,0,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdtrapyfa-id');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_TRA_PYFA.ID is ''ID''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832609095680','1880186819095048202','upgrade','add','column','main-abdtrapyfa-id',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"id","main-datamodelcolumn-columnLabel":"ID","main-datamodelcolumn-columnWidth":300,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"ID","main-datamodelcolumn-orderIndex":0,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832609095680');
		-- 新增【字段】：main-abdtrapyfa-jcdlfadm
		insert into lowcode_model_column (logical_type,model_id,column_display,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdtrapyfa',1,'jcdlfadm',0,'text','继承大类方案代码',300,0,'main-abdtrapyfa-jcdlfadm',0,0,'text',0,0,'java.lang.String',0,1,'JCDLFADM',0,19,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdtrapyfa-jcdlfadm');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_TRA_PYFA ADD JCDLFADM VARCHAR2(300)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_TRA_PYFA.JCDLFADM is ''继承大类方案代码''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832617484288','1880186819095048202','upgrade','add','column','main-abdtrapyfa-jcdlfadm',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"jcdlfadm","main-datamodelcolumn-columnLabel":"继承大类方案代码","main-datamodelcolumn-columnWidth":300,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"JCDLFADM","main-datamodelcolumn-orderIndex":19,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832617484288');
		-- 新增【字段】：main-abdtrapyfa-ksxndm
		insert into lowcode_model_column (logical_type,model_id,column_display,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdtrapyfa',1,'ksxndm',0,'text','开始学年',60,0,'main-abdtrapyfa-ksxndm',0,0,'text',0,0,'java.lang.String',0,1,'KSXNDM',0,10,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdtrapyfa-ksxndm');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_TRA_PYFA ADD KSXNDM VARCHAR2(60)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_TRA_PYFA.KSXNDM is ''开始学年''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832625872896','1880186819095048202','upgrade','add','column','main-abdtrapyfa-ksxndm',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"ksxndm","main-datamodelcolumn-columnLabel":"开始学年","main-datamodelcolumn-columnWidth":60,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"KSXNDM","main-datamodelcolumn-orderIndex":10,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832625872896');
		-- 新增【字段】：main-abdtrapyfa-ksxqdm
		insert into lowcode_model_column (logical_type,model_id,column_display,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdtrapyfa',1,'ksxqdm',0,'text','开始学期',6,0,'main-abdtrapyfa-ksxqdm',0,0,'text',0,0,'java.lang.String',0,1,'KSXQDM',0,11,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdtrapyfa-ksxqdm');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_TRA_PYFA ADD KSXQDM VARCHAR2(6)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_TRA_PYFA.KSXQDM is ''开始学期''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832630067200','1880186819095048202','upgrade','add','column','main-abdtrapyfa-ksxqdm',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"ksxqdm","main-datamodelcolumn-columnLabel":"开始学期","main-datamodelcolumn-columnWidth":6,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"KSXQDM","main-datamodelcolumn-orderIndex":11,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832630067200');
		-- 新增【字段】：main-abdtrapyfa-njdm
		insert into lowcode_model_column (logical_type,model_id,column_display,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdtrapyfa',1,'njdm',0,'text','年级',12,0,'main-abdtrapyfa-njdm',0,0,'text',0,0,'java.lang.String',0,1,'NJDM',0,4,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdtrapyfa-njdm');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_TRA_PYFA ADD NJDM VARCHAR2(12)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_TRA_PYFA.NJDM is ''年级''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832638455808','1880186819095048202','upgrade','add','column','main-abdtrapyfa-njdm',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"njdm","main-datamodelcolumn-columnLabel":"年级","main-datamodelcolumn-columnWidth":12,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"NJDM","main-datamodelcolumn-orderIndex":4,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832638455808');
		-- 新增【字段】：main-abdtrapyfa-pyccdm
		insert into lowcode_model_column (logical_type,model_id,column_display,column_javaname,column_readonly,search_xtype,column_label,column_dict,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdtrapyfa',1,'pyccdm',0,'multi-select','培养层次','PYCC',120,0,'main-abdtrapyfa-pyccdm',0,0,'text',0,0,'java.lang.String',0,1,'PYCCDM',0,21,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdtrapyfa-pyccdm');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_TRA_PYFA ADD PYCCDM VARCHAR2(120)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_TRA_PYFA.PYCCDM is ''培养层次''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832646844416','1880186819095048202','upgrade','add','column','main-abdtrapyfa-pyccdm',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"pyccdm","main-datamodelcolumn-columnLabel":"培养层次","main-datamodelcolumn-columnDict":"PYCC","main-datamodelcolumn-columnWidth":120,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"PYCCDM","main-datamodelcolumn-orderIndex":21,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832646844416');
		-- 新增【字段】：main-abdtrapyfa-pyfadm
		insert into lowcode_model_column (logical_type,model_id,column_display,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdtrapyfa',1,'pyfadm',0,'text','培养方案代码',120,0,'main-abdtrapyfa-pyfadm',0,0,'text',0,0,'java.lang.String',0,1,'PYFADM',0,1,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdtrapyfa-pyfadm');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_TRA_PYFA ADD PYFADM VARCHAR2(120)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_TRA_PYFA.PYFADM is ''培养方案代码''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832651038720','1880186819095048202','upgrade','add','column','main-abdtrapyfa-pyfadm',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"pyfadm","main-datamodelcolumn-columnLabel":"培养方案代码","main-datamodelcolumn-columnWidth":120,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"PYFADM","main-datamodelcolumn-orderIndex":1,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832651038720');
		-- 新增【字段】：main-abdtrapyfa-pyfamc
		insert into lowcode_model_column (logical_type,model_id,column_display,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdtrapyfa',1,'pyfamc',0,'text','培养方案名称',600,0,'main-abdtrapyfa-pyfamc',0,0,'text',0,0,'java.lang.String',0,1,'PYFAMC',0,2,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdtrapyfa-pyfamc');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_TRA_PYFA ADD PYFAMC VARCHAR2(600)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_TRA_PYFA.PYFAMC is ''培养方案名称''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832659427328','1880186819095048202','upgrade','add','column','main-abdtrapyfa-pyfamc',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"pyfamc","main-datamodelcolumn-columnLabel":"培养方案名称","main-datamodelcolumn-columnWidth":600,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"PYFAMC","main-datamodelcolumn-orderIndex":2,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832659427328');
		-- 新增【字段】：main-abdtrapyfa-sfjcdlfa
		insert into lowcode_model_column (logical_type,model_id,column_display,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdtrapyfa',1,'sfjcdlfa',0,'text','是否继承大类方案',6,0,'main-abdtrapyfa-sfjcdlfa',0,0,'text',0,0,'java.lang.String',0,1,'SFJCDLFA',0,18,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdtrapyfa-sfjcdlfa');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_TRA_PYFA ADD SFJCDLFA VARCHAR2(6)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_TRA_PYFA.SFJCDLFA is ''是否继承大类方案''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832667815936','1880186819095048202','upgrade','add','column','main-abdtrapyfa-sfjcdlfa',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"sfjcdlfa","main-datamodelcolumn-columnLabel":"是否继承大类方案","main-datamodelcolumn-columnWidth":6,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"SFJCDLFA","main-datamodelcolumn-orderIndex":18,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832667815936');
		-- 新增【字段】：main-abdtrapyfa-sfyc
		insert into lowcode_model_column (logical_type,model_id,column_display,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdtrapyfa',1,'sfyc',0,'text','是否异常',12,0,'main-abdtrapyfa-sfyc',0,0,'text',0,0,'java.lang.String',0,1,'SFYC',0,23,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdtrapyfa-sfyc');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_TRA_PYFA ADD SFYC VARCHAR2(12)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_TRA_PYFA.SFYC is ''是否异常''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832672010240','1880186819095048202','upgrade','add','column','main-abdtrapyfa-sfyc',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"sfyc","main-datamodelcolumn-columnLabel":"是否异常","main-datamodelcolumn-columnWidth":12,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"SFYC","main-datamodelcolumn-orderIndex":23,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832672010240');
		-- 新增【字段】：main-abdtrapyfa-updateBy
		insert into lowcode_model_column (logical_type,model_id,column_display,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdtrapyfa',1,'updateBy',0,'text','更新人',300,0,'main-abdtrapyfa-updateBy',0,1,'text',0,0,'java.lang.String',0,1,'UPDATE_BY',0,26,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdtrapyfa-updateBy');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_TRA_PYFA.UPDATE_BY is ''更新人''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832680398848','1880186819095048202','upgrade','add','column','main-abdtrapyfa-updateBy',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"updateBy","main-datamodelcolumn-columnLabel":"更新人","main-datamodelcolumn-columnWidth":300,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"UPDATE_BY","main-datamodelcolumn-orderIndex":26,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832680398848');
		-- 新增【字段】：main-abdtrapyfa-updateTime
		insert into lowcode_model_column (logical_type,model_id,column_display,column_javaname,column_readonly,search_xtype,column_label,event_time_point,id,column_format,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_pattern,column_dbname,search_jsonparam,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'datetime','main-abdtrapyfa',1,'updateTime',0,'datetime-range','更新时间',0,'main-abdtrapyfa-updateTime','yyyy-MM-dd HH:mm:ss',0,1,'date-full',0,0,'java.util.Date',0,1,'yyyy-MM-dd HH:mm:ss','UPDATE_TIME','{"type": "datetimerange","format": "yyyy-MM-dd HH:mm:ss","value-format": "yyyy-MM-dd HH:mm:ss","pattern": "yyyy-MM-dd HH:mm:ss"}',0,27,93,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdtrapyfa-updateTime');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_TRA_PYFA.UPDATE_TIME is ''更新时间''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832684593152','1880186819095048202','upgrade','add','column','main-abdtrapyfa-updateTime',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"datetime","main-datamodelcolumn-columnJavaname":"updateTime","main-datamodelcolumn-columnLabel":"更新时间","main-datamodelcolumn-columnFormat":"yyyy-MM-dd HH:mm:ss","main-datamodelcolumn-columnXtype":"date-full","main-datamodelcolumn-columnJavatype":"java.util.Date","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnPattern":"yyyy-MM-dd HH:mm:ss","main-datamodelcolumn-columnDbname":"UPDATE_TIME","main-datamodelcolumn-orderIndex":27,"main-datamodelcolumn-columnDbtype":93}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832684593152');
		-- 新增【字段】：main-abdtrapyfa-xdlxdm
		insert into lowcode_model_column (logical_type,model_id,column_display,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdtrapyfa',1,'xdlxdm',0,'text','修读类型',30,0,'main-abdtrapyfa-xdlxdm',0,0,'text',0,0,'java.lang.String',0,1,'XDLXDM',0,13,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdtrapyfa-xdlxdm');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_TRA_PYFA ADD XDLXDM VARCHAR2(30)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_TRA_PYFA.XDLXDM is ''修读类型''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832692981760','1880186819095048202','upgrade','add','column','main-abdtrapyfa-xdlxdm',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"xdlxdm","main-datamodelcolumn-columnLabel":"修读类型","main-datamodelcolumn-columnWidth":30,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"XDLXDM","main-datamodelcolumn-orderIndex":13,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832692981760');
		-- 新增【字段】：main-abdtrapyfa-xlccdm
		insert into lowcode_model_column (logical_type,model_id,column_display,column_javaname,column_readonly,search_xtype,column_label,column_dict,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdtrapyfa',1,'xlccdm',0,'multi-select','学历层次','XLCC',30,0,'main-abdtrapyfa-xlccdm',0,0,'text',0,0,'java.lang.String',0,1,'XLCCDM',0,8,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdtrapyfa-xlccdm');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_TRA_PYFA ADD XLCCDM VARCHAR2(30)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_TRA_PYFA.XLCCDM is ''学历层次''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832701370368','1880186819095048202','upgrade','add','column','main-abdtrapyfa-xlccdm',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"xlccdm","main-datamodelcolumn-columnLabel":"学历层次","main-datamodelcolumn-columnDict":"XLCC","main-datamodelcolumn-columnWidth":30,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"XLCCDM","main-datamodelcolumn-orderIndex":8,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832701370368');
		-- 新增【字段】：main-abdtrapyfa-xqlxdm
		insert into lowcode_model_column (logical_type,model_id,column_display,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdtrapyfa',1,'xqlxdm',0,'text','学期类型',6,0,'main-abdtrapyfa-xqlxdm',0,0,'text',0,0,'java.lang.String',0,1,'XQLXDM',0,12,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdtrapyfa-xqlxdm');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_TRA_PYFA ADD XQLXDM VARCHAR2(6)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_TRA_PYFA.XQLXDM is ''学期类型''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832705564672','1880186819095048202','upgrade','add','column','main-abdtrapyfa-xqlxdm',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"xqlxdm","main-datamodelcolumn-columnLabel":"学期类型","main-datamodelcolumn-columnWidth":6,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"XQLXDM","main-datamodelcolumn-orderIndex":12,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832705564672');
		-- 新增【字段】：main-abdtrapyfa-xwdm
		insert into lowcode_model_column (logical_type,model_id,column_display,column_javaname,column_readonly,search_xtype,column_label,column_dict,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdtrapyfa',1,'xwdm',0,'multi-select','学位','XWLB',30,0,'main-abdtrapyfa-xwdm',0,0,'text',0,0,'java.lang.String',0,1,'XWDM',0,14,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdtrapyfa-xwdm');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_TRA_PYFA ADD XWDM VARCHAR2(30)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_TRA_PYFA.XWDM is ''学位''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832713953280','1880186819095048202','upgrade','add','column','main-abdtrapyfa-xwdm',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"xwdm","main-datamodelcolumn-columnLabel":"学位","main-datamodelcolumn-columnDict":"XWLB","main-datamodelcolumn-columnWidth":30,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"XWDM","main-datamodelcolumn-orderIndex":14,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832713953280');
		-- 新增【字段】：main-abdtrapyfa-xznx
		insert into lowcode_model_column (logical_type,model_id,column_display,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'number','main-abdtrapyfa',1,'xznx',0,'number-range','学制年限',22,0,'main-abdtrapyfa-xznx',0,0,'number',0,0,'java.lang.Integer',0,1,'XZNX',0,9,4,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdtrapyfa-xznx');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_TRA_PYFA ADD XZNX NUMBER(22)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_TRA_PYFA.XZNX is ''学制年限''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832718147584','1880186819095048202','upgrade','add','column','main-abdtrapyfa-xznx',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"number","main-datamodelcolumn-columnJavaname":"xznx","main-datamodelcolumn-columnLabel":"学制年限","main-datamodelcolumn-columnWidth":22,"main-datamodelcolumn-columnXtype":"number","main-datamodelcolumn-columnJavatype":"java.lang.Integer","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"XZNX","main-datamodelcolumn-orderIndex":9,"main-datamodelcolumn-columnDbtype":4}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832718147584');
		-- 新增【字段】：main-abdtrapyfa-ypyfadm
		insert into lowcode_model_column (logical_type,model_id,column_display,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdtrapyfa',1,'ypyfadm',0,'text','原培养方案代码',300,0,'main-abdtrapyfa-ypyfadm',0,0,'text',0,0,'java.lang.String',0,1,'YPYFADM',0,20,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdtrapyfa-ypyfadm');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_TRA_PYFA ADD YPYFADM VARCHAR2(300)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_TRA_PYFA.YPYFADM is ''原培养方案代码''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832726536192','1880186819095048202','upgrade','add','column','main-abdtrapyfa-ypyfadm',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"ypyfadm","main-datamodelcolumn-columnLabel":"原培养方案代码","main-datamodelcolumn-columnWidth":300,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"YPYFADM","main-datamodelcolumn-orderIndex":20,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832726536192');
		-- 新增【字段】：main-abdtrapyfa-zsyqxf
		insert into lowcode_model_column (logical_type,model_id,column_display,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'number','main-abdtrapyfa',1,'zsyqxf',0,'number-range','最少要求学分',22,0,'main-abdtrapyfa-zsyqxf',0,0,'number',0,0,'java.lang.Integer',0,1,'ZSYQXF',0,15,4,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdtrapyfa-zsyqxf');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_TRA_PYFA ADD ZSYQXF NUMBER(22)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_TRA_PYFA.ZSYQXF is ''最少要求学分''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832730730496','1880186819095048202','upgrade','add','column','main-abdtrapyfa-zsyqxf',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"number","main-datamodelcolumn-columnJavaname":"zsyqxf","main-datamodelcolumn-columnLabel":"最少要求学分","main-datamodelcolumn-columnWidth":22,"main-datamodelcolumn-columnXtype":"number","main-datamodelcolumn-columnJavatype":"java.lang.Integer","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"ZSYQXF","main-datamodelcolumn-orderIndex":15,"main-datamodelcolumn-columnDbtype":4}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832730730496');
		-- 新增【字段】：main-abdtrapyfa-zydm
		insert into lowcode_model_column (logical_type,model_id,column_display,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdtrapyfa',1,'zydm',0,'text','专业',60,0,'main-abdtrapyfa-zydm',0,0,'text',0,0,'java.lang.String',0,1,'ZYDM',0,6,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdtrapyfa-zydm');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_TRA_PYFA ADD ZYDM VARCHAR2(60)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_TRA_PYFA.ZYDM is ''专业''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832739119104','1880186819095048202','upgrade','add','column','main-abdtrapyfa-zydm',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"zydm","main-datamodelcolumn-columnLabel":"专业","main-datamodelcolumn-columnWidth":60,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"ZYDM","main-datamodelcolumn-orderIndex":6,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832739119104');
		-- 新增【字段】：main-abdtrapyfa-zyfxdm
		insert into lowcode_model_column (logical_type,model_id,column_display,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdtrapyfa',1,'zyfxdm',0,'text','专业方向',60,0,'main-abdtrapyfa-zyfxdm',0,0,'text',0,0,'java.lang.String',0,1,'ZYFXDM',0,7,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdtrapyfa-zyfxdm');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_TRA_PYFA ADD ZYFXDM VARCHAR2(60)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_TRA_PYFA.ZYFXDM is ''专业方向''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832747507712','1880186819095048202','upgrade','add','column','main-abdtrapyfa-zyfxdm',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"zyfxdm","main-datamodelcolumn-columnLabel":"专业方向","main-datamodelcolumn-columnWidth":60,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"ZYFXDM","main-datamodelcolumn-orderIndex":7,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832747507712');
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-abdtrapyfa' and FIELD_NAME='main-damodelcolumn-entityTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdtrapyfa(main-damodelcolumn-entityTag 是否实体标识)，跳过更新');
			ELSE
				update t_da_model_column_extend set entity_tag = '0'
				where model_id = 'main-abdtrapyfa';
				update t_da_model_column_extend set entity_tag = '1'
				where id in ('main-abdtrapyfa-pyfadm');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1880186832814616576','1880186819095048202','upgrade','update','model','main-abdtrapyfa','main-damodelcolumn-entityTag','{"变更前":{"main-damodelcolumn-entityTag":""},"变更后":{"main-damodelcolumn-entityTag":"培养方案代码"}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1880186832814616576');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdtrapyfa(main-damodelcolumn-entityTag 是否实体标识),sql执行错误:' || SQLERRM);
		END;
		
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-abdtrapyfa' and FIELD_NAME='main-damodelcolumn-entityNameTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdtrapyfa(main-damodelcolumn-entityNameTag 是否实体名称标识)，跳过更新');
			ELSE
				update t_da_model_column_extend set entity_name_tag = '0'
				where model_id = 'main-abdtrapyfa';
				update t_da_model_column_extend set entity_name_tag = '1'
				where id in ('main-abdtrapyfa-pyfamc');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1880186832818810880','1880186819095048202','upgrade','update','model','main-abdtrapyfa','main-damodelcolumn-entityNameTag','{"变更前":{"main-damodelcolumn-entityNameTag":""},"变更后":{"main-damodelcolumn-entityNameTag":"培养方案名称"}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1880186832818810880');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdtrapyfa(main-damodelcolumn-entityNameTag 是否实体名称标识),sql执行错误:' || SQLERRM);
		END;
		
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-abdtrapyfa' and FIELD_NAME='main-damodelcolumn-primaryColumnTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdtrapyfa(main-damodelcolumn-primaryColumnTag 是否主键字段)，跳过更新');
			ELSE
				update t_da_model_column_extend set primary_column_tag = '0'
				where model_id = 'main-abdtrapyfa';
				update t_da_model_column_extend set primary_column_tag = '1'
				where id in ('main-abdtrapyfa-pyfadm');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1880186832823005184','1880186819095048202','upgrade','update','model','main-abdtrapyfa','main-damodelcolumn-primaryColumnTag','{"变更前":{"main-damodelcolumn-primaryColumnTag":""},"变更后":{"main-damodelcolumn-primaryColumnTag":"培养方案代码"}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1880186832823005184');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdtrapyfa(main-damodelcolumn-primaryColumnTag 是否主键字段),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.3',internal_version='0' WHERE model_id ='main-abdtrapyfa';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdtrapyfa成功,模型版本号更新为:V1.0.3,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-abdundbzksjbxx ↓----------------------------------------------
--模型：本专科生基本信息 ABD_UND_BZKSJBXX main-abdundbzksjbxx
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdundbzksjbxx'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdundbzksjbxx版本号:'||v_version);
	IF v_version >= 'V1.0.7.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdundbzksjbxx的本次增量版本号:V1.0.7,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1880186819095048203','main-abdundbzksjbxx','V1.0.7','0','upgrade','update','本专科生基本信息','jcsj','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1880186819095048203');
		-- 修改【字段】：main-abdundbzksjbxx-xy columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-abdundbzksjbxx-xy' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdundbzksjbxx-xy(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'biz_department_student'
				where id = 'main-abdundbzksjbxx-xy';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1880186832881725440','1880186819095048203','upgrade','update','column','main-abdundbzksjbxx-xy','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"xy","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XY","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":180,"main-datamodelcolumn-columnLabel":"学院","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":26,"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnDict":"dept_college","main-datamodelcolumn-columnXtype":"select"},"变更后":{"main-datamodelcolumn-columnDict":"biz_department_student"}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1880186832881725440');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdundbzksjbxx-xy(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.7',internal_version='0' WHERE model_id ='main-abdundbzksjbxx';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdundbzksjbxx成功,模型版本号更新为:V1.0.7,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-abdundxspyfa ↓----------------------------------------------
--模型：学生培养方案 ABD_UND_XSPYFA main-abdundxspyfa
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdundxspyfa'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdundxspyfa版本号:'||v_version);
	IF v_version >= 'V1.0.3.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdundxspyfa的本次增量版本号:V1.0.3,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1880186819095048204','main-abdundxspyfa','V1.0.3','0','upgrade','add','学生培养方案','jcsj','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1880186819095048204');
		-- 新增【模型扩展】：main-abdundxspyfa
		insert into t_da_model_extend (model_encoding,access_level,model_id,model_version,model_enable,entity_id,model_type,use_num,update_mode,theme_code,model_desc,internal_version,id,create_by,create_time,update_by,update_time)
		select 'ABD_UND_0111','1','main-abdundxspyfa','V1.0.3','1','UNDERGRADUATE','jcsj',0,'直接更新','INS','用于存储和管理学生的个人培养方案信息，包括培养方案名称、课程设置、学分要求、培养目标等。','0','main-abdundxspyfa','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_extend
			where id = 'main-abdundxspyfa');
		-- 新增【模型使用范围】:1737011218539001243
		insert into t_da_model_theme (model_id,theme_code,id,create_by,create_time,update_by,update_time)
		select 'main-abdundxspyfa','POS','1737011218539001243','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_theme
			where id = '1737011218539001243');
		-- 新增【模型】：main-abdundxspyfa
		insert into lowcode_model (model_app,publish_time,sys_flag,model_class,permit_ext_flag,extend_flag,model_label,model_name,model_table,model_classify,virtual_model,biz_model,order_index,id,status,create_by,create_time,update_by,update_time)
		select 'main',TIMESTAMP '2025-01-08 10:23:25',0,'com.wisedu.lowcode4j.main.po.Abdundxspyfa',1,1,'学生培养方案','abdundxspyfa','ABD_UND_XSPYFA','UNDERGRADUATE_GLXGXX_JCSJ',1,0,544,'main-abdundxspyfa',1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model
			where id = 'main-abdundxspyfa');
		BEGIN
		EXECUTE IMMEDIATE 'CREATE TABLE ABD_UND_XSPYFA(ID VARCHAR2(100) DEFAULT SYS_GUID() primary key ,CREATE_BY VARCHAR2(100),CREATE_TIME DATE,UPDATE_BY VARCHAR2(100),UPDATE_TIME DATE)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-955) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on table ABD_UND_XSPYFA is ''学生培养方案''';
		END;
		-- 新增【字段扩展】：main-abdundxspyfa-createBy
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdundxspyfa','main-abdundxspyfa-createBy','V1.0.2',NULL,NULL,'0',0,'创建人','main-abdundxspyfa-createBy',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdundxspyfa-createBy');
		-- 新增【字段扩展】：main-abdundxspyfa-createTime
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdundxspyfa','main-abdundxspyfa-createTime','V1.0.2',NULL,NULL,'0',0,'创建时间','main-abdundxspyfa-createTime',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdundxspyfa-createTime');
		-- 新增【字段扩展】：main-abdundxspyfa-id
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdundxspyfa','main-abdundxspyfa-id','V1.0.2',NULL,NULL,'0',0,'ID','main-abdundxspyfa-id',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdundxspyfa-id');
		-- 新增【字段扩展】：main-abdundxspyfa-pyfadm
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdundxspyfa','main-abdundxspyfa-pyfadm','V1.0.2',NULL,NULL,'0',0,'培养方案代码','main-abdundxspyfa-pyfadm',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdundxspyfa-pyfadm');
		-- 新增【字段扩展】：main-abdundxspyfa-updateBy
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdundxspyfa','main-abdundxspyfa-updateBy','V1.0.2',NULL,NULL,'0',0,'更新人','main-abdundxspyfa-updateBy',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdundxspyfa-updateBy');
		-- 新增【字段扩展】：main-abdundxspyfa-updateTime
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdundxspyfa','main-abdundxspyfa-updateTime','V1.0.2',NULL,NULL,'0',0,'更新时间','main-abdundxspyfa-updateTime',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdundxspyfa-updateTime');
		-- 新增【字段扩展】：main-abdundxspyfa-xh
		insert into t_da_model_column_extend (time_tag,model_id,column_id,model_version,entity_tag,primary_column_tag,delete_flag,use_num,label_desc,id,value_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-abdundxspyfa','main-abdundxspyfa-xh','V1.0.2',NULL,NULL,'0',0,'学号','main-abdundxspyfa-xh',NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdundxspyfa-xh');
		-- 新增【字段】：main-abdundxspyfa-createBy
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdundxspyfa',1,0,'createBy',0,'text','创建人',300,0,'main-abdundxspyfa-createBy',0,1,'text',0,0,'java.lang.String',0,1,'CREATE_BY',0,3,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdundxspyfa-createBy');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_UND_XSPYFA.CREATE_BY is ''创建人''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832969805824','1880186819095048204','upgrade','add','column','main-abdundxspyfa-createBy',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"createBy","main-datamodelcolumn-columnLabel":"创建人","main-datamodelcolumn-columnWidth":300,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"CREATE_BY","main-datamodelcolumn-orderIndex":3,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832969805824');
		-- 新增【字段】：main-abdundxspyfa-createTime
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,event_time_point,id,column_format,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_pattern,column_dbname,search_jsonparam,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'datetime','main-abdundxspyfa',1,0,'createTime',0,'datetime-range','创建时间',0,'main-abdundxspyfa-createTime','yyyy-MM-dd HH:mm:ss',0,1,'date-full',0,0,'java.util.Date',0,1,'yyyy-MM-dd HH:mm:ss','CREATE_TIME','{"type": "datetimerange","format": "yyyy-MM-dd HH:mm:ss","value-format": "yyyy-MM-dd HH:mm:ss","pattern": "yyyy-MM-dd HH:mm:ss"}',0,4,93,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdundxspyfa-createTime');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_UND_XSPYFA.CREATE_TIME is ''创建时间''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832974000128','1880186819095048204','upgrade','add','column','main-abdundxspyfa-createTime',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"datetime","main-datamodelcolumn-columnJavaname":"createTime","main-datamodelcolumn-columnLabel":"创建时间","main-datamodelcolumn-columnFormat":"yyyy-MM-dd HH:mm:ss","main-datamodelcolumn-columnXtype":"date-full","main-datamodelcolumn-columnJavatype":"java.util.Date","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnPattern":"yyyy-MM-dd HH:mm:ss","main-datamodelcolumn-columnDbname":"CREATE_TIME","main-datamodelcolumn-orderIndex":4,"main-datamodelcolumn-columnDbtype":93}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832974000128');
		-- 新增【字段】：main-abdundxspyfa-id
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdundxspyfa',1,0,'id',0,'text','ID',300,0,'main-abdundxspyfa-id',0,1,'text',0,0,'java.lang.String',0,1,'ID',0,0,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdundxspyfa-id');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_UND_XSPYFA.ID is ''ID''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832982388736','1880186819095048204','upgrade','add','column','main-abdundxspyfa-id',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"id","main-datamodelcolumn-columnLabel":"ID","main-datamodelcolumn-columnWidth":300,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"ID","main-datamodelcolumn-orderIndex":0,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832982388736');
		-- 新增【字段】：main-abdundxspyfa-pyfadm
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdundxspyfa',1,0,'pyfadm',0,'text','培养方案代码',120,0,'main-abdundxspyfa-pyfadm',0,0,'text',0,0,'java.lang.String',0,1,'PYFADM',0,2,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdundxspyfa-pyfadm');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_UND_XSPYFA ADD PYFADM VARCHAR2(120)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_UND_XSPYFA.PYFADM is ''培养方案代码''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832986583040','1880186819095048204','upgrade','add','column','main-abdundxspyfa-pyfadm',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"pyfadm","main-datamodelcolumn-columnLabel":"培养方案代码","main-datamodelcolumn-columnWidth":120,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"PYFADM","main-datamodelcolumn-orderIndex":2,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832986583040');
		-- 新增【字段】：main-abdundxspyfa-updateBy
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdundxspyfa',1,0,'updateBy',0,'text','更新人',300,0,'main-abdundxspyfa-updateBy',0,1,'text',0,0,'java.lang.String',0,1,'UPDATE_BY',0,5,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdundxspyfa-updateBy');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_UND_XSPYFA.UPDATE_BY is ''更新人''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186832994971648','1880186819095048204','upgrade','add','column','main-abdundxspyfa-updateBy',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"updateBy","main-datamodelcolumn-columnLabel":"更新人","main-datamodelcolumn-columnWidth":300,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"UPDATE_BY","main-datamodelcolumn-orderIndex":5,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186832994971648');
		-- 新增【字段】：main-abdundxspyfa-updateTime
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,event_time_point,id,column_format,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_pattern,column_dbname,search_jsonparam,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'datetime','main-abdundxspyfa',1,0,'updateTime',0,'datetime-range','更新时间',0,'main-abdundxspyfa-updateTime','yyyy-MM-dd HH:mm:ss',0,1,'date-full',0,0,'java.util.Date',0,1,'yyyy-MM-dd HH:mm:ss','UPDATE_TIME','{"type": "datetimerange","format": "yyyy-MM-dd HH:mm:ss","value-format": "yyyy-MM-dd HH:mm:ss","pattern": "yyyy-MM-dd HH:mm:ss"}',0,6,93,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdundxspyfa-updateTime');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_UND_XSPYFA.UPDATE_TIME is ''更新时间''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186833003360256','1880186819095048204','upgrade','add','column','main-abdundxspyfa-updateTime',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"datetime","main-datamodelcolumn-columnJavaname":"updateTime","main-datamodelcolumn-columnLabel":"更新时间","main-datamodelcolumn-columnFormat":"yyyy-MM-dd HH:mm:ss","main-datamodelcolumn-columnXtype":"date-full","main-datamodelcolumn-columnJavatype":"java.util.Date","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnPattern":"yyyy-MM-dd HH:mm:ss","main-datamodelcolumn-columnDbname":"UPDATE_TIME","main-datamodelcolumn-orderIndex":6,"main-datamodelcolumn-columnDbtype":93}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186833003360256');
		-- 新增【字段】：main-abdundxspyfa-xh
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdundxspyfa',1,0,'xh',0,'text','学号',300,0,'main-abdundxspyfa-xh',0,0,'text',0,0,'java.lang.String',0,1,'XH',0,1,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdundxspyfa-xh');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_UND_XSPYFA ADD XH VARCHAR2(300)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_UND_XSPYFA.XH is ''学号''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186833007554560','1880186819095048204','upgrade','add','column','main-abdundxspyfa-xh',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"xh","main-datamodelcolumn-columnLabel":"学号","main-datamodelcolumn-columnWidth":300,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"XH","main-datamodelcolumn-orderIndex":1,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186833007554560');
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-abdundxspyfa' and FIELD_NAME='main-damodelcolumn-entityTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdundxspyfa(main-damodelcolumn-entityTag 是否实体标识)，跳过更新');
			ELSE
				update t_da_model_column_extend set entity_tag = '0'
				where model_id = 'main-abdundxspyfa';
				update t_da_model_column_extend set entity_tag = '1'
				where id in ('main-abdundxspyfa-xh');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1880186833078857728','1880186819095048204','upgrade','update','model','main-abdundxspyfa','main-damodelcolumn-entityTag','{"变更前":{"main-damodelcolumn-entityTag":""},"变更后":{"main-damodelcolumn-entityTag":"学号"}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1880186833078857728');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdundxspyfa(main-damodelcolumn-entityTag 是否实体标识),sql执行错误:' || SQLERRM);
		END;
		
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-abdundxspyfa' and FIELD_NAME='main-damodelcolumn-primaryColumnTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdundxspyfa(main-damodelcolumn-primaryColumnTag 是否主键字段)，跳过更新');
			ELSE
				update t_da_model_column_extend set primary_column_tag = '0'
				where model_id = 'main-abdundxspyfa';
				update t_da_model_column_extend set primary_column_tag = '1'
				where id in ('main-abdundxspyfa-id');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1880186833083052032','1880186819095048204','upgrade','update','model','main-abdundxspyfa','main-damodelcolumn-primaryColumnTag','{"变更前":{"main-damodelcolumn-primaryColumnTag":""},"变更后":{"main-damodelcolumn-primaryColumnTag":"ID"}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1880186833083052032');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdundxspyfa(main-damodelcolumn-primaryColumnTag 是否主键字段),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.3',internal_version='0' WHERE model_id ='main-abdundxspyfa';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdundxspyfa成功,模型版本号更新为:V1.0.3,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-insgccjxyjtd ↓----------------------------------------------
--模型：高层次教学、研究团队 INS_GCCJXYJTD main-insgccjxyjtd
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-insgccjxyjtd'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-insgccjxyjtd版本号:'||v_version);
	IF v_version >= 'V1.0.2.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-insgccjxyjtd的本次增量版本号:V1.0.2,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1880186819095048205','main-insgccjxyjtd','V1.0.2','0','upgrade','update','高层次教学、研究团队','jcsj','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1880186819095048205');
		-- 新增【字段扩展】：main-insgccjxyjtd-tdcy
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-insgccjxyjtd',0,'main-insgccjxyjtd-tdcy','main-insgccjxyjtd-tdcy',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-insgccjxyjtd-tdcy');
		-- 新增【字段扩展】：main-insgccjxyjtd-tdcygh
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-insgccjxyjtd',0,'main-insgccjxyjtd-tdcygh','main-insgccjxyjtd-tdcygh',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-insgccjxyjtd-tdcygh');
		-- 新增【字段扩展】：main-insgccjxyjtd-tdjs
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-insgccjxyjtd',0,'main-insgccjxyjtd-tdjs','main-insgccjxyjtd-tdjs',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-insgccjxyjtd-tdjs');
		-- 新增【字段】：main-insgccjxyjtd-tdcy
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-insgccjxyjtd',1,0,'tdcy',0,'text','团队成员',300,0,'main-insgccjxyjtd-tdcy',0,'text',0,1,'java.lang.String',1,'TDCY',7,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-insgccjxyjtd-tdcy');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE INS_GCCJXYJTD ADD TDCY VARCHAR2(300)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column INS_GCCJXYJTD.TDCY is ''团队成员''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186833104023552','1880186819095048205','upgrade','add','column','main-insgccjxyjtd-tdcy',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"tdcy","main-datamodelcolumn-columnLabel":"团队成员","main-datamodelcolumn-columnWidth":300,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"TDCY","main-datamodelcolumn-orderIndex":7,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186833104023552');
		-- 新增【字段】：main-insgccjxyjtd-tdcygh
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-insgccjxyjtd',1,0,'tdcygh',0,'text','团队成员工号',300,0,'main-insgccjxyjtd-tdcygh',0,'text',0,1,'java.lang.String',1,'TDCYGH',8,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-insgccjxyjtd-tdcygh');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE INS_GCCJXYJTD ADD TDCYGH VARCHAR2(300)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column INS_GCCJXYJTD.TDCYGH is ''团队成员工号''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186833112412160','1880186819095048205','upgrade','add','column','main-insgccjxyjtd-tdcygh',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"tdcygh","main-datamodelcolumn-columnLabel":"团队成员工号","main-datamodelcolumn-columnWidth":300,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"TDCYGH","main-datamodelcolumn-orderIndex":8,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186833112412160');
		-- 新增【字段】：main-insgccjxyjtd-tdjs
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'textarea','main-insgccjxyjtd',1,0,'tdjs',0,'text','团队介绍',8000,0,'main-insgccjxyjtd-tdjs',0,'text',0,1,'java.sql.Clob',1,'TDJS',9,2005,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-insgccjxyjtd-tdjs');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE INS_GCCJXYJTD ADD TDJS CLOB';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column INS_GCCJXYJTD.TDJS is ''团队介绍''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186833120800768','1880186819095048205','upgrade','add','column','main-insgccjxyjtd-tdjs',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"textarea","main-datamodelcolumn-columnJavaname":"tdjs","main-datamodelcolumn-columnLabel":"团队介绍","main-datamodelcolumn-columnWidth":8000,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.sql.Clob","main-datamodelcolumn-columnDbname":"TDJS","main-datamodelcolumn-orderIndex":9,"main-datamodelcolumn-columnDbtype":2005}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186833120800768');
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.2',internal_version='0' WHERE model_id ='main-insgccjxyjtd';
		DBMS_OUTPUT.PUT_LINE('升级模型main-insgccjxyjtd成功,模型版本号更新为:V1.0.2,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-insxscjdxscxcyxljhqk ↓----------------------------------------------
--模型：学生参加大学生创新创业训练计划情况 INS_XSCJDXSCXCYXLJHQK main-insxscjdxscxcyxljhqk
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-insxscjdxscxcyxljhqk'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-insxscjdxscxcyxljhqk版本号:'||v_version);
	IF v_version >= 'V1.0.2.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-insxscjdxscxcyxljhqk的本次增量版本号:V1.0.2,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1880186819095048206','main-insxscjdxscxcyxljhqk','V1.0.2','0','upgrade','update','学生参加大学生创新创业训练计划情况','jcsj','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1880186819095048206');
		-- 新增【字段扩展】：main-insxscjdxscxcyxljhqk-xmlbfl
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-insxscjdxscxcyxljhqk',0,'main-insxscjdxscxcyxljhqk-xmlbfl','main-insxscjdxscxcyxljhqk-xmlbfl',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-insxscjdxscxcyxljhqk-xmlbfl');
		-- 新增【字段】：main-insxscjdxscxcyxljhqk-xmlbfl
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-insxscjdxscxcyxljhqk',1,0,'xmlbfl',0,'text','项目类别分类',300,0,'main-insxscjdxscxcyxljhqk-xmlbfl',0,'text',0,1,'java.lang.String',1,'XMLBFL',19,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-insxscjdxscxcyxljhqk-xmlbfl');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE INS_XSCJDXSCXCYXLJHQK ADD XMLBFL VARCHAR2(300)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column INS_XSCJDXSCXCYXLJHQK.XMLBFL is ''项目类别分类''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186833246629888','1880186819095048206','upgrade','add','column','main-insxscjdxscxcyxljhqk-xmlbfl',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"xmlbfl","main-datamodelcolumn-columnLabel":"项目类别分类","main-datamodelcolumn-columnWidth":300,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XMLBFL","main-datamodelcolumn-orderIndex":19,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186833246629888');
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.2',internal_version='0' WHERE model_id ='main-insxscjdxscxcyxljhqk';
		DBMS_OUTPUT.PUT_LINE('升级模型main-insxscjdxscxcyxljhqk成功,模型版本号更新为:V1.0.2,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-insxshsjjysgljsjlqk ↓----------------------------------------------
--模型：学生各类竞赛奖励情况 INS_XSHSJJYSGLJSJLQK main-insxshsjjysgljsjlqk
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-insxshsjjysgljsjlqk'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-insxshsjjysgljsjlqk版本号:'||v_version);
	IF v_version >= 'V1.0.3.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-insxshsjjysgljsjlqk的本次增量版本号:V1.0.3,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1880186819095048207','main-insxshsjjysgljsjlqk','V1.0.3','0','upgrade','update','学生各类竞赛奖励情况','jcsj','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1880186819095048207');
		-- 新增【字段扩展】：main-insxshsjjysgljsjlqk-qtcsxs
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-insxshsjjysgljsjlqk',0,'main-insxshsjjysgljsjlqk-qtcsxs','main-insxshsjjysgljsjlqk-qtcsxs',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-insxshsjjysgljsjlqk-qtcsxs');
		-- 新增【字段扩展】：main-insxshsjjysgljsjlqk-qtdsz
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-insxshsjjysgljsjlqk',0,'main-insxshsjjysgljsjlqk-qtdsz','main-insxshsjjysgljsjlqk-qtdsz',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-insxshsjjysgljsjlqk-qtdsz');
		-- 新增【字段】：main-insxshsjjysgljsjlqk-qtcsxs
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-insxshsjjysgljsjlqk',1,0,'qtcsxs',0,'text','其他参赛学生',800,0,'main-insxshsjjysgljsjlqk-qtcsxs',0,'text',0,1,'java.lang.String',1,'QTCSXS',19,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-insxshsjjysgljsjlqk-qtcsxs');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE INS_XSHSJJYSGLJSJLQK ADD QTCSXS VARCHAR2(800)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column INS_XSHSJJYSGLJSJLQK.QTCSXS is ''其他参赛学生''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186833347293184','1880186819095048207','upgrade','add','column','main-insxshsjjysgljsjlqk-qtcsxs',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"qtcsxs","main-datamodelcolumn-columnLabel":"其他参赛学生","main-datamodelcolumn-columnWidth":800,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"QTCSXS","main-datamodelcolumn-orderIndex":19,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186833347293184');
		-- 新增【字段】：main-insxshsjjysgljsjlqk-qtdsz
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-insxshsjjysgljsjlqk',1,0,'qtdsz',0,'text','全体导师组',800,0,'main-insxshsjjysgljsjlqk-qtdsz',0,'text',0,1,'java.lang.String',1,'QTDSZ',18,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-insxshsjjysgljsjlqk-qtdsz');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE INS_XSHSJJYSGLJSJLQK ADD QTDSZ VARCHAR2(800)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column INS_XSHSJJYSGLJSJLQK.QTDSZ is ''全体导师组''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186833351487488','1880186819095048207','upgrade','add','column','main-insxshsjjysgljsjlqk-qtdsz',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"qtdsz","main-datamodelcolumn-columnLabel":"全体导师组","main-datamodelcolumn-columnWidth":800,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"QTDSZ","main-datamodelcolumn-orderIndex":18,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186833351487488');
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.3',internal_version='0' WHERE model_id ='main-insxshsjjysgljsjlqk';
		DBMS_OUTPUT.PUT_LINE('升级模型main-insxshsjjysgljsjlqk成功,模型版本号更新为:V1.0.3,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-insyjbkbysqxlsqk ↓----------------------------------------------
--模型：应届本科毕业生去向落实情况 INS_YJBKBYSQXLSQK main-insyjbkbysqxlsqk
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-insyjbkbysqxlsqk'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-insyjbkbysqxlsqk版本号:'||v_version);
	IF v_version >= 'V1.0.2.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-insyjbkbysqxlsqk的本次增量版本号:V1.0.2,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1880186819095048208','main-insyjbkbysqxlsqk','V1.0.2','0','upgrade','update','应届本科毕业生去向落实情况','jcsj','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1880186819095048208');
		-- 新增【字段扩展】：main-insyjbkbysqxlsqk-qxyx
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-insyjbkbysqxlsqk',0,'main-insyjbkbysqxlsqk-qxyx','main-insyjbkbysqxlsqk-qxyx',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-insyjbkbysqxlsqk-qxyx');
		-- 新增【字段】：main-insyjbkbysqxlsqk-qxyx
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-insyjbkbysqxlsqk',1,0,'qxyx',0,'去向院校',600,0,'main-insyjbkbysqxlsqk-qxyx',0,'text',0,1,'java.lang.String',1,'QXYX',20,12,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-insyjbkbysqxlsqk-qxyx');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE INS_YJBKBYSQXLSQK ADD QXYX VARCHAR2(600)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column INS_YJBKBYSQXLSQK.QXYX is ''去向院校''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186833443762176','1880186819095048208','upgrade','add','column','main-insyjbkbysqxlsqk-qxyx',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"qxyx","main-datamodelcolumn-columnLabel":"去向院校","main-datamodelcolumn-columnWidth":600,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"QXYX","main-datamodelcolumn-orderIndex":20,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186833443762176');
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.2',internal_version='0' WHERE model_id ='main-insyjbkbysqxlsqk';
		DBMS_OUTPUT.PUT_LINE('升级模型main-insyjbkbysqxlsqk成功,模型版本号更新为:V1.0.2,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-possisch0320 ↓----------------------------------------------
--模型：每年全校授课人均学时 POS_SI_SCH_0320 main-possisch0320
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-possisch0320'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-possisch0320版本号:'||v_version);
	IF v_version >= 'V1.0.1.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-possisch0320的本次增量版本号:V1.0.1,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1880186819095048209','main-possisch0320','V1.0.1','0','upgrade','update','每年全校授课人均学时','zb','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1880186819095048209');
		-- 新增【字段扩展】：main-possisch0320-zgjjsrjxs
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-possisch0320',0,'main-possisch0320-zgjjsrjxs','main-possisch0320-zgjjsrjxs',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-possisch0320-zgjjsrjxs');
		-- 新增【字段】：main-possisch0320-zgjjsrjxs
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,search_xtype,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'number','main-possisch0320',1,0,'zgjjsrjxs',0,'number-range','正高级教师人均学时',19,0,'main-possisch0320-zgjjsrjxs',0,'number',0,1,'java.lang.Integer',1,'ZGJJSRJXS',5,4,1,1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-possisch0320-zgjjsrjxs');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE POS_SI_SCH_0320 ADD ZGJJSRJXS NUMBER(19)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column POS_SI_SCH_0320.ZGJJSRJXS is ''正高级教师人均学时''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1880186833531842560','1880186819095048209','upgrade','add','column','main-possisch0320-zgjjsrjxs',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"number","main-datamodelcolumn-columnJavaname":"zgjjsrjxs","main-datamodelcolumn-columnLabel":"正高级教师人均学时","main-datamodelcolumn-columnWidth":19,"main-datamodelcolumn-columnXtype":"number","main-datamodelcolumn-columnJavatype":"java.lang.Integer","main-datamodelcolumn-columnDbname":"ZGJJSRJXS","main-datamodelcolumn-orderIndex":5,"main-datamodelcolumn-columnDbtype":4}}','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1880186833531842560');
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.1',internal_version='0' WHERE model_id ='main-possisch0320';
		DBMS_OUTPUT.PUT_LINE('升级模型main-possisch0320成功,模型版本号更新为:V1.0.1,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-tralabel ↓----------------------------------------------
--模型：TRA标签宽表 TRA_LABEL main-tralabel
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-tralabel'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-tralabel版本号:'||v_version);
	IF v_version >= 'V1.0.3.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-tralabel的本次增量版本号:V1.0.3,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1880186819095048210','main-tralabel','V1.0.3','0','upgrade','add','TRA标签宽表','bq','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1880186819095048210');
		-- 新增【模型扩展】：main-tralabel
		insert into t_da_model_extend (model_encoding,access_level,model_id,model_version,model_enable,entity_id,model_type,use_num,update_mode,theme_code,model_desc,internal_version,id,create_by,create_time,update_by,update_time)
		select 'TRALABEL','0','main-tralabel','V1.0.3','1','TRAINNING PLAN','bq',0,'周期快照','INS','TRA标签宽表','0','main-tralabel','dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from t_da_model_extend
			where id = 'main-tralabel');
		-- 新增【模型】：main-tralabel
		insert into lowcode_model (model_app,publish_time,sys_flag,model_class,permit_ext_flag,extend_flag,model_label,model_name,model_table,model_classify,virtual_model,biz_model,order_index,id,status,create_by,create_time,update_by,update_time)
		select 'main',TIMESTAMP '2024-06-20 01:00:00',0,'com.wisedu.lowcode4j.main.po.Tralabel',1,1,'TRA标签宽表','tralabel','TRA_LABEL','default',1,0,538,'main-tralabel',1,'dataapp',TIMESTAMP '2025-01-17 17:34:07','dataapp',TIMESTAMP '2025-01-17 17:34:07'
		from dual
		where not exists (
			select 1
			from lowcode_model
			where id = 'main-tralabel');
		BEGIN
		EXECUTE IMMEDIATE 'CREATE TABLE TRA_LABEL(ID VARCHAR2(100) DEFAULT SYS_GUID() primary key ,KZRQ VARCHAR2(10),CREATE_BY VARCHAR2(100),CREATE_TIME DATE,UPDATE_BY VARCHAR2(100),UPDATE_TIME DATE) PARTITION BY RANGE (kzrq) (PARTITION initial_partition VALUES LESS THAN (''2024-01''))';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-955) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'CREATE VIEW V_TRA_LABEL AS SELECT * FROM TRA_LABEL WHERE kzrq = TO_CHAR(SYSDATE, ''yyyy-MM-dd'')';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-955) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on table TRA_LABEL is ''TRA标签宽表''';
		END;
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.3',internal_version='0' WHERE model_id ='main-tralabel';
		DBMS_OUTPUT.PUT_LINE('升级模型main-tralabel成功,模型版本号更新为:V1.0.3,内部版本号更新为：0');
	END IF;
END;
/
