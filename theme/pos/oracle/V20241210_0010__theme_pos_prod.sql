----------------------------------------------↓ 模型 main-abdschbsbmxx ↓----------------------------------------------
--模型：博士报名信息 ABD_SCH_BSBMXX main-abdschbsbmxx
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdschbsbmxx'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdschbsbmxx版本号:'||v_version);
	IF v_version >= 'V1.0.1.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdschbsbmxx的本次增量版本号:V1.0.1,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1866312606156337152','main-abdschbsbmxx','V1.0.1','0','upgrade','update','博士报名信息','jcsj','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1866312606156337152');
		-- 新增【字段扩展】：main-abdschbsbmxx-sfyzsy
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdschbsbmxx',0,'main-abdschbsbmxx-sfyzsy','main-abdschbsbmxx-sfyzsy',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdschbsbmxx-sfyzsy');
		-- 新增【字段】：main-abdschbsbmxx-sfyzsy
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_dict,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdschbsbmxx',1,0,'sfyzsy',0,'是否优质生源','SFBZ',100,0,'main-abdschbsbmxx-sfyzsy',0,'select',0,1,'java.lang.String',1,'SFYZSY',5,12,1,1,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdschbsbmxx-sfyzsy');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_SCH_BSBMXX ADD SFYZSY VARCHAR2(100)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_SCH_BSBMXX.SFYZSY is ''是否优质生源''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312615861956608','1866312606156337152','upgrade','add','column','main-abdschbsbmxx-sfyzsy',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"sfyzsy","main-datamodelcolumn-columnLabel":"是否优质生源","main-datamodelcolumn-columnDict":"SFBZ","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnXtype":"select","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"SFYZSY","main-datamodelcolumn-orderIndex":5,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312615861956608');
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.1',internal_version='0' WHERE model_id ='main-abdschbsbmxx';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdschbsbmxx成功,模型版本号更新为:V1.0.1,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-abdschbslqxx ↓----------------------------------------------
--模型：博士录取信息 ABD_SCH_BSLQXX main-abdschbslqxx
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdschbslqxx'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdschbslqxx版本号:'||v_version);
	IF v_version >= 'V1.0.1.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdschbslqxx的本次增量版本号:V1.0.1,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1866312606156337153','main-abdschbslqxx','V1.0.1','0','upgrade','update','博士录取信息','jcsj','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1866312606156337153');
		-- 新增【字段扩展】：main-abdschbslqxx-sfyzsy
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdschbslqxx',0,'main-abdschbslqxx-sfyzsy','main-abdschbslqxx-sfyzsy',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdschbslqxx-sfyzsy');
		-- 新增【字段】：main-abdschbslqxx-sfyzsy
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_dict,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdschbslqxx',1,0,'sfyzsy',0,'是否优质生源','SFBZ',100,0,'main-abdschbslqxx-sfyzsy',0,'select',0,1,'java.lang.String',1,'SFYZSY',13,12,1,1,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdschbslqxx-sfyzsy');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_SCH_BSLQXX ADD SFYZSY VARCHAR2(100)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_SCH_BSLQXX.SFYZSY is ''是否优质生源''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312615950036992','1866312606156337153','upgrade','add','column','main-abdschbslqxx-sfyzsy',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"sfyzsy","main-datamodelcolumn-columnLabel":"是否优质生源","main-datamodelcolumn-columnDict":"SFBZ","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnXtype":"select","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"SFYZSY","main-datamodelcolumn-orderIndex":13,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312615950036992');
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.1',internal_version='0' WHERE model_id ='main-abdschbslqxx';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdschbslqxx成功,模型版本号更新为:V1.0.1,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-abdschssbmxx ↓----------------------------------------------
--模型：硕士报名信息 ABD_SCH_SSBMXX main-abdschssbmxx
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdschssbmxx'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdschssbmxx版本号:'||v_version);
	IF v_version >= 'V1.0.1.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdschssbmxx的本次增量版本号:V1.0.1,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1866312606156337154','main-abdschssbmxx','V1.0.1','0','upgrade','update','硕士报名信息','jcsj','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1866312606156337154');
		-- 新增【字段扩展】：main-abdschssbmxx-sfyzsy
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdschssbmxx',0,'main-abdschssbmxx-sfyzsy','main-abdschssbmxx-sfyzsy',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdschssbmxx-sfyzsy');
		-- 新增【字段】：main-abdschssbmxx-sfyzsy
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_dict,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdschssbmxx',1,0,'sfyzsy',0,'是否优质生源','SFBZ',100,0,'main-abdschssbmxx-sfyzsy',0,'select',0,1,'java.lang.String',1,'SFYZSY',5,12,1,1,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdschssbmxx-sfyzsy');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_SCH_SSBMXX ADD SFYZSY VARCHAR2(100)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_SCH_SSBMXX.SFYZSY is ''是否优质生源''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616029728768','1866312606156337154','upgrade','add','column','main-abdschssbmxx-sfyzsy',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"sfyzsy","main-datamodelcolumn-columnLabel":"是否优质生源","main-datamodelcolumn-columnDict":"SFBZ","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnXtype":"select","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"SFYZSY","main-datamodelcolumn-orderIndex":5,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616029728768');
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.1',internal_version='0' WHERE model_id ='main-abdschssbmxx';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdschssbmxx成功,模型版本号更新为:V1.0.1,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-abdschsslqxx ↓----------------------------------------------
--模型：硕士录取信息 ABD_SCH_SSLQXX main-abdschsslqxx
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdschsslqxx'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdschsslqxx版本号:'||v_version);
	IF v_version >= 'V1.0.1.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdschsslqxx的本次增量版本号:V1.0.1,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1866312606156337155','main-abdschsslqxx','V1.0.1','0','upgrade','update','硕士录取信息','jcsj','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1866312606156337155');
		-- 新增【字段扩展】：main-abdschsslqxx-sfyzsy
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdschsslqxx',0,'main-abdschsslqxx-sfyzsy','main-abdschsslqxx-sfyzsy',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdschsslqxx-sfyzsy');
		-- 新增【字段】：main-abdschsslqxx-sfyzsy
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_dict,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdschsslqxx',1,0,'sfyzsy',0,'是否优质生源','SFBZ',100,0,'main-abdschsslqxx-sfyzsy',0,'select',0,1,'java.lang.String',1,'SFYZSY',13,12,1,1,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdschsslqxx-sfyzsy');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_SCH_SSLQXX ADD SFYZSY VARCHAR2(100)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_SCH_SSLQXX.SFYZSY is ''是否优质生源''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616109420544','1866312606156337155','upgrade','add','column','main-abdschsslqxx-sfyzsy',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"sfyzsy","main-datamodelcolumn-columnLabel":"是否优质生源","main-datamodelcolumn-columnDict":"SFBZ","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnXtype":"select","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"SFYZSY","main-datamodelcolumn-orderIndex":13,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616109420544');
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.1',internal_version='0' WHERE model_id ='main-abdschsslqxx';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdschsslqxx成功,模型版本号更新为:V1.0.1,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-abdschtkjlb ↓----------------------------------------------
--模型：听课记录表 ABD_SCH_TKJLB main-abdschtkjlb
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdschtkjlb'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdschtkjlb版本号:'||v_version);
	IF v_version >= 'V1.0.3.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdschtkjlb的本次增量版本号:V1.0.3,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1866312606156337156','main-abdschtkjlb','V1.0.3','0','upgrade','update','听课记录表','jcsj','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1866312606156337156');
		-- 新增【字段扩展】：main-abdschtkjlb-pjnr
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,model_version,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdschtkjlb',0,'main-abdschtkjlb-pjnr','V1.0.2','main-abdschtkjlb-pjnr',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdschtkjlb-pjnr');
		-- 新增【字段扩展】：main-abdschtkjlb-sfdfkc
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,model_version,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdschtkjlb',0,'main-abdschtkjlb-sfdfkc','V1.0.2','main-abdschtkjlb-sfdfkc',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdschtkjlb-sfdfkc');
		-- 新增【字段扩展】：main-abdschtkjlb-sfgfkc
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,model_version,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdschtkjlb',0,'main-abdschtkjlb-sfgfkc','V1.0.2','main-abdschtkjlb-sfgfkc',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdschtkjlb-sfgfkc');
		-- 新增【字段】：main-abdschtkjlb-pjnr
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdschtkjlb',1,0,'pjnr',0,'评价内容',3000,0,'main-abdschtkjlb-pjnr',0,'text',0,1,'java.lang.String',1,'PJNR',25,12,1,1,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdschtkjlb-pjnr');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_SCH_TKJLB ADD PJNR VARCHAR2(3000)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_SCH_TKJLB.PJNR is ''评价内容''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616193306624','1866312606156337156','upgrade','add','column','main-abdschtkjlb-pjnr',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"pjnr","main-datamodelcolumn-columnLabel":"评价内容","main-datamodelcolumn-columnWidth":3000,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"PJNR","main-datamodelcolumn-orderIndex":25,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616193306624');
		-- 新增【字段】：main-abdschtkjlb-sfdfkc
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdschtkjlb',1,0,'sfdfkc',0,'是否低分课程',40,0,'main-abdschtkjlb-sfdfkc',0,'text',0,1,'java.lang.String',1,'SFDFKC',27,12,1,1,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdschtkjlb-sfdfkc');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_SCH_TKJLB ADD SFDFKC VARCHAR2(40)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_SCH_TKJLB.SFDFKC is ''是否低分课程''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616201695232','1866312606156337156','upgrade','add','column','main-abdschtkjlb-sfdfkc',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"sfdfkc","main-datamodelcolumn-columnLabel":"是否低分课程","main-datamodelcolumn-columnWidth":40,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"SFDFKC","main-datamodelcolumn-orderIndex":27,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616201695232');
		-- 新增【字段】：main-abdschtkjlb-sfgfkc
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdschtkjlb',1,0,'sfgfkc',0,'是否高分课程',40,0,'main-abdschtkjlb-sfgfkc',0,'text',0,1,'java.lang.String',1,'SFGFKC',26,12,1,1,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdschtkjlb-sfgfkc');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_SCH_TKJLB ADD SFGFKC VARCHAR2(40)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_SCH_TKJLB.SFGFKC is ''是否高分课程''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616205889536','1866312606156337156','upgrade','add','column','main-abdschtkjlb-sfgfkc',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"sfgfkc","main-datamodelcolumn-columnLabel":"是否高分课程","main-datamodelcolumn-columnWidth":40,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"SFGFKC","main-datamodelcolumn-orderIndex":26,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616205889536');
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.3',internal_version='0' WHERE model_id ='main-abdschtkjlb';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdschtkjlb成功,模型版本号更新为:V1.0.3,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-abdteapjjxb ↓----------------------------------------------
--模型：教学班评教结果 ABD_TEA_PJJXB main-abdteapjjxb
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdteapjjxb'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdteapjjxb版本号:'||v_version);
	IF v_version >= 'V1.0.3.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdteapjjxb的本次增量版本号:V1.0.3,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1866312606156337157','main-abdteapjjxb','V1.0.3','0','upgrade','update','教学班评教结果','jcsj','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1866312606156337157');
		-- 新增【字段扩展】：main-abdteapjjxb-jxbmc
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,model_version,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdteapjjxb',0,'main-abdteapjjxb-jxbmc','V1.0.2','main-abdteapjjxb-jxbmc',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdteapjjxb-jxbmc');
		-- 新增【字段扩展】：main-abdteapjjxb-sfdfkc
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,model_version,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdteapjjxb',0,'main-abdteapjjxb-sfdfkc','V1.0.2','main-abdteapjjxb-sfdfkc',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdteapjjxb-sfdfkc');
		-- 新增【字段扩展】：main-abdteapjjxb-sfgfkc
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,model_version,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdteapjjxb',0,'main-abdteapjjxb-sfgfkc','V1.0.2','main-abdteapjjxb-sfgfkc',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdteapjjxb-sfgfkc');
		-- 新增【字段】：main-abdteapjjxb-jxbmc
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdteapjjxb',1,0,'jxbmc',0,'教学班名称',600,0,'main-abdteapjjxb-jxbmc',0,'text',0,1,'java.lang.String',1,'JXBMC',25,12,1,1,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdteapjjxb-jxbmc');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_TEA_PJJXB ADD JXBMC VARCHAR2(600)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_TEA_PJJXB.JXBMC is ''教学班名称''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616289775616','1866312606156337157','upgrade','add','column','main-abdteapjjxb-jxbmc',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"jxbmc","main-datamodelcolumn-columnLabel":"教学班名称","main-datamodelcolumn-columnWidth":600,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"JXBMC","main-datamodelcolumn-orderIndex":25,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616289775616');
		-- 新增【字段】：main-abdteapjjxb-sfdfkc
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdteapjjxb',1,0,'sfdfkc',0,'是否低分课程',40,0,'main-abdteapjjxb-sfdfkc',0,'text',0,1,'java.lang.String',1,'SFDFKC',24,12,1,1,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdteapjjxb-sfdfkc');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_TEA_PJJXB ADD SFDFKC VARCHAR2(40)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_TEA_PJJXB.SFDFKC is ''是否低分课程''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616293969920','1866312606156337157','upgrade','add','column','main-abdteapjjxb-sfdfkc',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"sfdfkc","main-datamodelcolumn-columnLabel":"是否低分课程","main-datamodelcolumn-columnWidth":40,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"SFDFKC","main-datamodelcolumn-orderIndex":24,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616293969920');
		-- 新增【字段】：main-abdteapjjxb-sfgfkc
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdteapjjxb',1,0,'sfgfkc',0,'是否高分课程',40,0,'main-abdteapjjxb-sfgfkc',0,'text',0,1,'java.lang.String',1,'SFGFKC',23,12,1,1,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdteapjjxb-sfgfkc');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_TEA_PJJXB ADD SFGFKC VARCHAR2(40)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_TEA_PJJXB.SFGFKC is ''是否高分课程''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616298164224','1866312606156337157','upgrade','add','column','main-abdteapjjxb-sfgfkc',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"sfgfkc","main-datamodelcolumn-columnLabel":"是否高分课程","main-datamodelcolumn-columnWidth":40,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"SFGFKC","main-datamodelcolumn-orderIndex":23,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616298164224');
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.3',internal_version='0' WHERE model_id ='main-abdteapjjxb';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdteapjjxb成功,模型版本号更新为:V1.0.3,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-abdundxskcb ↓----------------------------------------------
--模型：学生课程表 ABD_UND_XSKCB main-abdundxskcb
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdundxskcb'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdundxskcb版本号:'||v_version);
	IF v_version >= 'V1.0.3.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdundxskcb的本次增量版本号:V1.0.3,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1866312606156337158','main-abdundxskcb','V1.0.3','0','upgrade','update','学生课程表','jcsj','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1866312606156337158');
		-- 新增【字段扩展】：main-abdundxskcb-jxbmc
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,model_version,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-abdundxskcb',0,'main-abdundxskcb-jxbmc','V1.0.2','main-abdundxskcb-jxbmc',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-abdundxskcb-jxbmc');
		-- 新增【字段】：main-abdundxskcb-jxbmc
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-abdundxskcb',1,0,'jxbmc',0,'教学班名称',600,0,'main-abdundxskcb-jxbmc',0,'text',0,1,'java.lang.String',1,'JXBMC',15,12,1,1,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-abdundxskcb-jxbmc');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE ABD_UND_XSKCB ADD JXBMC VARCHAR2(600)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column ABD_UND_XSKCB.JXBMC is ''教学班名称''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616386244608','1866312606156337158','upgrade','add','column','main-abdundxskcb-jxbmc',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"jxbmc","main-datamodelcolumn-columnLabel":"教学班名称","main-datamodelcolumn-columnWidth":600,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"JXBMC","main-datamodelcolumn-orderIndex":15,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616386244608');
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.3',internal_version='0' WHERE model_id ='main-abdundxskcb';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdundxskcb成功,模型版本号更新为:V1.0.3,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-possicol0127 ↓----------------------------------------------
--模型：每年各学院授课人均学时 POS_SI_COL_0127 main-possicol0127
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-possicol0127'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-possicol0127版本号:'||v_version);
	IF v_version >= 'V1.0.1.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-possicol0127的本次增量版本号:V1.0.1,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1866312606156337159','main-possicol0127','V1.0.1','0','upgrade','update','每年各学院授课人均学时','zb','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1866312606156337159');
		-- 新增【字段扩展】：main-possicol0127-zgjjsrjxs
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,label_desc,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-possicol0127',0,'main-possicol0127-zgjjsrjxs','正高级教师人均学时','main-possicol0127-zgjjsrjxs',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-possicol0127-zgjjsrjxs');
		-- 新增【字段】：main-possicol0127-zgjjsrjxs
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'number','main-possicol0127',1,0,'zgjjsrjxs',0,'正高级教师人均学时',19,0,'main-possicol0127-zgjjsrjxs',0,'number',0,1,'java.lang.Integer',1,'ZGJJSRJXS',5,4,1,1,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-possicol0127-zgjjsrjxs');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE POS_SI_COL_0127 ADD ZGJJSRJXS NUMBER(19)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column POS_SI_COL_0127.ZGJJSRJXS is ''正高级教师人均学时''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616465936384','1866312606156337159','upgrade','add','column','main-possicol0127-zgjjsrjxs',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"number","main-datamodelcolumn-columnJavaname":"zgjjsrjxs","main-datamodelcolumn-columnLabel":"正高级教师人均学时","main-datamodelcolumn-columnWidth":19,"main-datamodelcolumn-columnXtype":"number","main-datamodelcolumn-columnJavatype":"java.lang.Integer","main-datamodelcolumn-columnDbname":"ZGJJSRJXS","main-datamodelcolumn-orderIndex":5,"main-datamodelcolumn-columnDbtype":4}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616465936384');
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.1',internal_version='0' WHERE model_id ='main-possicol0127';
		DBMS_OUTPUT.PUT_LINE('升级模型main-possicol0127成功,模型版本号更新为:V1.0.1,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-possicol0129 ↓----------------------------------------------
--模型：每年各学院未上课教师人数 POS_SI_COL_0129 main-possicol0129
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-possicol0129'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-possicol0129版本号:'||v_version);
	IF v_version >= 'V1.0.1.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-possicol0129的本次增量版本号:V1.0.1,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1866312606156337160','main-possicol0129','V1.0.1','0','upgrade','update','每年各学院未上课教师人数','zb','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1866312606156337160');
		-- 新增【字段扩展】：main-possicol0129-zyjszwjb
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-possicol0129',0,'main-possicol0129-zyjszwjb','main-possicol0129-zyjszwjb',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-possicol0129-zyjszwjb');
		-- 新增【字段】：main-possicol0129-zyjszwjb
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-possicol0129',1,0,'zyjszwjb',0,'专业技术职务级别',100,0,'main-possicol0129-zyjszwjb',0,'text',0,1,'java.lang.String',1,'ZYJSZWJB',6,12,1,1,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-possicol0129-zyjszwjb');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE POS_SI_COL_0129 ADD ZYJSZWJB VARCHAR2(100)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column POS_SI_COL_0129.ZYJSZWJB is ''专业技术职务级别''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616549822464','1866312606156337160','upgrade','add','column','main-possicol0129-zyjszwjb',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"zyjszwjb","main-datamodelcolumn-columnLabel":"专业技术职务级别","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"ZYJSZWJB","main-datamodelcolumn-orderIndex":6,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616549822464');
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.1',internal_version='0' WHERE model_id ='main-possicol0129';
		DBMS_OUTPUT.PUT_LINE('升级模型main-possicol0129成功,模型版本号更新为:V1.0.1,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-possicol0311 ↓----------------------------------------------
--模型：每年各学院录取人数 POS_SI_COL_0311 main-possicol0311
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-possicol0311'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-possicol0311版本号:'||v_version);
	IF v_version >= 'V1.0.1.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-possicol0311的本次增量版本号:V1.0.1,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1866312606156337161','main-possicol0311','V1.0.1','0','upgrade','update','每年各学院录取人数','zb','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1866312606156337161');
		-- 新增【字段扩展】：main-possicol0311-ksfs
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-possicol0311',0,'main-possicol0311-ksfs','main-possicol0311-ksfs',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-possicol0311-ksfs');
		-- 新增【字段】：main-possicol0311-ksfs
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-possicol0311',1,0,'ksfs',0,'考试方式',100,0,'main-possicol0311-ksfs',0,'text',0,1,'java.lang.String',1,'KSFS',8,12,1,1,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-possicol0311-ksfs');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE POS_SI_COL_0311 ADD KSFS VARCHAR2(100)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column POS_SI_COL_0311.KSFS is ''考试方式''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616629514240','1866312606156337161','upgrade','add','column','main-possicol0311-ksfs',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"ksfs","main-datamodelcolumn-columnLabel":"考试方式","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"KSFS","main-datamodelcolumn-orderIndex":8,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616629514240');
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.1',internal_version='0' WHERE model_id ='main-possicol0311';
		DBMS_OUTPUT.PUT_LINE('升级模型main-possicol0311成功,模型版本号更新为:V1.0.1,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-possicol0325 ↓----------------------------------------------
--模型：每学年各学院主讲研究生课程教师人数 POS_SI_COL_0325 main-possicol0325
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-possicol0325'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-possicol0325版本号:'||v_version);
	IF v_version >= 'V1.0.0.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-possicol0325的本次增量版本号:V1.0.0,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1866312606156337162','main-possicol0325','V1.0.0','0','upgrade','add','每学年各学院主讲研究生课程教师人数','zb','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1866312606156337162');
		-- 新增【模型】：main-possicol0325
		insert into lowcode_model (model_app,publish_time,sys_flag,model_class,permit_ext_flag,extend_flag,model_label,model_name,model_table,model_classify,virtual_model,biz_model,order_index,id,status,create_by,create_time,update_by,update_time)
		select 'main',TIMESTAMP '2024-12-05 17:14:46',0,'com.wisedu.lowcode4j.main.po.Possicol0325',1,1,'每学年各学院主讲研究生课程教师人数','possicol0325','POS_SI_COL_0325','COLLEGE_SZDW_ZB',1,0,639,'main-possicol0325',1,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model
			where id = 'main-possicol0325');
		BEGIN
		EXECUTE IMMEDIATE 'CREATE TABLE POS_SI_COL_0325(ID VARCHAR2(100) DEFAULT SYS_GUID() primary key ,CREATE_BY VARCHAR2(100),CREATE_TIME DATE,UPDATE_BY VARCHAR2(100),UPDATE_TIME DATE)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-955) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on table POS_SI_COL_0325 is ''每学年各学院主讲研究生课程教师人数''';
		END;
		-- 新增【模型使用范围】:1733213464951001243
		insert into t_da_model_theme (model_id,theme_code,id,create_by,create_time,update_by,update_time)
		select 'main-possicol0325','POS','1733213464951001243','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_theme
			where id = '1733213464951001243');
		-- 新增【模型扩展】：main-possicol0325
		insert into t_da_model_extend (model_encoding,access_level,model_id,model_version,model_enable,entity_id,model_type,metric_id,use_num,theme_code,model_desc,internal_version,id,create_by,create_time,update_by,update_time)
		select 'POS_SI_COL_0325','1','main-possicol0325','V1.0.0','1','COLLEGE','zb',NULL,0,'POS','按学年统计','0','main-possicol0325','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_extend
			where id = 'main-possicol0325');
		-- 新增【字段扩展】：main-possicol0325-createBy
		insert into t_da_model_column_extend (time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-possicol0325',0,'main-possicol0325-createBy','main-possicol0325-createBy',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-possicol0325-createBy');
		-- 新增【字段扩展】：main-possicol0325-createTime
		insert into t_da_model_column_extend (time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-possicol0325',0,'main-possicol0325-createTime','main-possicol0325-createTime',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-possicol0325-createTime');
		-- 新增【字段扩展】：main-possicol0325-id
		insert into t_da_model_column_extend (time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,primary_column_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-possicol0325',0,'main-possicol0325-id','main-possicol0325-id',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-possicol0325-id');
		-- 新增【字段扩展】：main-possicol0325-sfgdrc
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-possicol0325',0,'main-possicol0325-sfgdrc','main-possicol0325-sfgdrc',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-possicol0325-sfgdrc');
		-- 新增【字段扩展】：main-possicol0325-skjsrs
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-possicol0325',0,'main-possicol0325-skjsrs','main-possicol0325-skjsrs',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-possicol0325-skjsrs');
		-- 新增【字段扩展】：main-possicol0325-tjnd
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-possicol0325',0,'main-possicol0325-tjnd','main-possicol0325-tjnd',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-possicol0325-tjnd');
		-- 新增【字段扩展】：main-possicol0325-updateBy
		insert into t_da_model_column_extend (time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-possicol0325',0,'main-possicol0325-updateBy','main-possicol0325-updateBy',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-possicol0325-updateBy');
		-- 新增【字段扩展】：main-possicol0325-updateTime
		insert into t_da_model_column_extend (time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-possicol0325',0,'main-possicol0325-updateTime','main-possicol0325-updateTime',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-possicol0325-updateTime');
		-- 新增【字段扩展】：main-possicol0325-xybh
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-possicol0325',0,'main-possicol0325-xybh','main-possicol0325-xybh',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-possicol0325-xybh');
		-- 新增【字段扩展】：main-possicol0325-zyjszw
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-possicol0325',0,'main-possicol0325-zyjszw','main-possicol0325-zyjszw',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-possicol0325-zyjszw');
		-- 新增【字段扩展】：main-possicol0325-zyjszwjb
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-possicol0325',0,'main-possicol0325-zyjszwjb','main-possicol0325-zyjszwjb',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-possicol0325-zyjszwjb');
		-- 新增【字段】：main-possicol0325-createBy
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-possicol0325',1,'createBy',0,'创建人',100,0,'main-possicol0325-createBy',1,0,1,'java.lang.String',0,0,1,'CREATE_BY',0,301,12,1,0,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-possicol0325-createBy');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column POS_SI_COL_0325.CREATE_BY is ''创建人''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616713400320','1866312606156337162','upgrade','add','column','main-possicol0325-createBy',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"createBy","main-datamodelcolumn-columnLabel":"创建人","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"CREATE_BY","main-datamodelcolumn-orderIndex":301,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616713400320');
		-- 新增【字段】：main-possicol0325-createTime
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-possicol0325',1,'createTime',0,'创建时间',0,0,'main-possicol0325-createTime',1,'date-full',0,1,'java.util.Date',0,0,1,'CREATE_TIME',0,302,93,1,0,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-possicol0325-createTime');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column POS_SI_COL_0325.CREATE_TIME is ''创建时间''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616717594624','1866312606156337162','upgrade','add','column','main-possicol0325-createTime',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"createTime","main-datamodelcolumn-columnLabel":"创建时间","main-datamodelcolumn-columnWidth":0,"main-datamodelcolumn-columnXtype":"date-full","main-datamodelcolumn-columnJavatype":"java.util.Date","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"CREATE_TIME","main-datamodelcolumn-orderIndex":302,"main-datamodelcolumn-columnDbtype":93}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616717594624');
		-- 新增【字段】：main-possicol0325-id
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-possicol0325',1,'id',0,'ID',100,0,'main-possicol0325-id',1,0,1,'java.lang.String',0,0,1,'ID',0,300,12,1,0,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-possicol0325-id');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column POS_SI_COL_0325.ID is ''ID''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616721788928','1866312606156337162','upgrade','add','column','main-possicol0325-id',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"id","main-datamodelcolumn-columnLabel":"ID","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"ID","main-datamodelcolumn-orderIndex":300,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616721788928');
		-- 新增【字段】：main-possicol0325-sfgdrc
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-possicol0325',1,0,'sfgdrc',0,'是否高端人才',100,0,'main-possicol0325-sfgdrc',0,'text',0,1,'java.lang.String',1,'SFGDRC',4,12,1,1,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-possicol0325-sfgdrc');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE POS_SI_COL_0325 ADD SFGDRC VARCHAR2(100)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column POS_SI_COL_0325.SFGDRC is ''是否高端人才''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616725983232','1866312606156337162','upgrade','add','column','main-possicol0325-sfgdrc',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"sfgdrc","main-datamodelcolumn-columnLabel":"是否高端人才","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"SFGDRC","main-datamodelcolumn-orderIndex":4,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616725983232');
		-- 新增【字段】：main-possicol0325-skjsrs
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'number','main-possicol0325',1,0,'skjsrs',0,'授课教师人数',38,0,'main-possicol0325-skjsrs',0,'number',0,1,'java.lang.Integer',1,'SKJSRS',5,4,1,1,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-possicol0325-skjsrs');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE POS_SI_COL_0325 ADD SKJSRS NUMBER(38)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column POS_SI_COL_0325.SKJSRS is ''授课教师人数''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616730177536','1866312606156337162','upgrade','add','column','main-possicol0325-skjsrs',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"number","main-datamodelcolumn-columnJavaname":"skjsrs","main-datamodelcolumn-columnLabel":"授课教师人数","main-datamodelcolumn-columnWidth":38,"main-datamodelcolumn-columnXtype":"number","main-datamodelcolumn-columnJavatype":"java.lang.Integer","main-datamodelcolumn-columnDbname":"SKJSRS","main-datamodelcolumn-orderIndex":5,"main-datamodelcolumn-columnDbtype":4}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616730177536');
		-- 新增【字段】：main-possicol0325-tjnd
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_format,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'schoolYear','main-possicol0325',1,0,'tjnd',0,'统计年度',4,0,'main-possicol0325-tjnd','yyyy',0,'text',0,1,'java.lang.String',1,'TJND',1,12,1,1,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-possicol0325-tjnd');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE POS_SI_COL_0325 ADD TJND VARCHAR2(4)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column POS_SI_COL_0325.TJND is ''统计年度''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616738566144','1866312606156337162','upgrade','add','column','main-possicol0325-tjnd',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"schoolYear","main-datamodelcolumn-columnJavaname":"tjnd","main-datamodelcolumn-columnLabel":"统计年度","main-datamodelcolumn-columnWidth":4,"main-datamodelcolumn-columnFormat":"yyyy","main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"TJND","main-datamodelcolumn-orderIndex":1,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616738566144');
		-- 新增【字段】：main-possicol0325-updateBy
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-possicol0325',1,'updateBy',0,'更新人',100,0,'main-possicol0325-updateBy',1,0,1,'java.lang.String',0,0,1,'UPDATE_BY',0,303,12,1,0,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-possicol0325-updateBy');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column POS_SI_COL_0325.UPDATE_BY is ''更新人''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616742760448','1866312606156337162','upgrade','add','column','main-possicol0325-updateBy',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"updateBy","main-datamodelcolumn-columnLabel":"更新人","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"UPDATE_BY","main-datamodelcolumn-orderIndex":303,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616742760448');
		-- 新增【字段】：main-possicol0325-updateTime
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-possicol0325',1,'updateTime',0,'更新时间',0,0,'main-possicol0325-updateTime',1,'date-full',0,1,'java.util.Date',0,0,1,'UPDATE_TIME',0,304,93,1,0,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-possicol0325-updateTime');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column POS_SI_COL_0325.UPDATE_TIME is ''更新时间''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616746954752','1866312606156337162','upgrade','add','column','main-possicol0325-updateTime',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"updateTime","main-datamodelcolumn-columnLabel":"更新时间","main-datamodelcolumn-columnWidth":0,"main-datamodelcolumn-columnXtype":"date-full","main-datamodelcolumn-columnJavatype":"java.util.Date","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"UPDATE_TIME","main-datamodelcolumn-orderIndex":304,"main-datamodelcolumn-columnDbtype":93}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616746954752');
		-- 新增【字段】：main-possicol0325-xybh
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-possicol0325',1,0,'xybh',0,'学院编号',180,0,'main-possicol0325-xybh',0,'text',0,1,'java.lang.String',1,'XYBH',3,12,1,1,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-possicol0325-xybh');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE POS_SI_COL_0325 ADD XYBH VARCHAR2(180)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column POS_SI_COL_0325.XYBH is ''学院编号''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616751149056','1866312606156337162','upgrade','add','column','main-possicol0325-xybh',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"xybh","main-datamodelcolumn-columnLabel":"学院编号","main-datamodelcolumn-columnWidth":180,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XYBH","main-datamodelcolumn-orderIndex":3,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616751149056');
		-- 新增【字段】：main-possicol0325-zyjszw
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-possicol0325',1,0,'zyjszw',0,'专业技术职务',100,0,'main-possicol0325-zyjszw',0,'text',0,1,'java.lang.String',1,'ZYJSZW',2,12,1,1,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-possicol0325-zyjszw');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE POS_SI_COL_0325 ADD ZYJSZW VARCHAR2(100)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column POS_SI_COL_0325.ZYJSZW is ''专业技术职务''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616755343360','1866312606156337162','upgrade','add','column','main-possicol0325-zyjszw',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"zyjszw","main-datamodelcolumn-columnLabel":"专业技术职务","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"ZYJSZW","main-datamodelcolumn-orderIndex":2,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616755343360');
		-- 新增【字段】：main-possicol0325-zyjszwjb
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-possicol0325',1,0,'zyjszwjb',0,'专业技术职务级别',100,0,'main-possicol0325-zyjszwjb',0,'text',0,1,'java.lang.String',1,'ZYJSZWJB',6,12,1,1,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-possicol0325-zyjszwjb');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE POS_SI_COL_0325 ADD ZYJSZWJB VARCHAR2(100)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column POS_SI_COL_0325.ZYJSZWJB is ''专业技术职务级别''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616759537664','1866312606156337162','upgrade','add','column','main-possicol0325-zyjszwjb',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"zyjszwjb","main-datamodelcolumn-columnLabel":"专业技术职务级别","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"ZYJSZWJB","main-datamodelcolumn-orderIndex":6,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616759537664');
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-possicol0325' and FIELD_NAME='main-damodelcolumn-entityTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-possicol0325(main-damodelcolumn-entityTag 是否实体标识)，跳过更新');
			ELSE
				update t_da_model_column_extend set entity_tag = '0'
				where model_id = 'main-possicol0325';
				update t_da_model_column_extend set entity_tag = '1'
				where id in ('main-possicol0325-xybh');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1866312616839229440','1866312606156337162','upgrade','update','model','main-possicol0325','main-damodelcolumn-entityTag','{"变更前":{"main-damodelcolumn-entityTag":""},"变更后":{"main-damodelcolumn-entityTag":"学院编号"}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1866312616839229440');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-possicol0325(main-damodelcolumn-entityTag 是否实体标识),sql执行错误:' || SQLERRM);
		END;
		
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-possicol0325' and FIELD_NAME='main-damodelcolumn-timeTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-possicol0325(main-damodelcolumn-timeTag 是否时点标识字段)，跳过更新');
			ELSE
				update t_da_model_column_extend set time_tag = '0'
				where model_id = 'main-possicol0325';
				update t_da_model_column_extend set time_tag = '1'
				where id in ('main-possicol0325-tjnd');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1866312616843423744','1866312606156337162','upgrade','update','model','main-possicol0325','main-damodelcolumn-timeTag','{"变更前":{"main-damodelcolumn-timeTag":""},"变更后":{"main-damodelcolumn-timeTag":"统计年度"}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1866312616843423744');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-possicol0325(main-damodelcolumn-timeTag 是否时点标识字段),sql执行错误:' || SQLERRM);
		END;
		
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-possicol0325' and FIELD_NAME='main-damodelcolumn-valueTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-possicol0325(main-damodelcolumn-valueTag 是否指标值字段)，跳过更新');
			ELSE
				update t_da_model_column_extend set value_tag = '0'
				where model_id = 'main-possicol0325';
				update t_da_model_column_extend set value_tag = '1'
				where id in ('main-possicol0325-skjsrs');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1866312616847618048','1866312606156337162','upgrade','update','model','main-possicol0325','main-damodelcolumn-valueTag','{"变更前":{"main-damodelcolumn-valueTag":""},"变更后":{"main-damodelcolumn-valueTag":"授课教师人数"}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1866312616847618048');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-possicol0325(main-damodelcolumn-valueTag 是否指标值字段),sql执行错误:' || SQLERRM);
		END;
		
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-possicol0325' and FIELD_NAME='main-damodelcolumn-primaryColumnTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-possicol0325(main-damodelcolumn-primaryColumnTag 是否主键字段)，跳过更新');
			ELSE
				update t_da_model_column_extend set primary_column_tag = '0'
				where model_id = 'main-possicol0325';
				update t_da_model_column_extend set primary_column_tag = '1'
				where id in ('main-possicol0325-id');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1866312616851812352','1866312606156337162','upgrade','update','model','main-possicol0325','main-damodelcolumn-primaryColumnTag','{"变更前":{"main-damodelcolumn-primaryColumnTag":""},"变更后":{"main-damodelcolumn-primaryColumnTag":"ID"}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1866312616851812352');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-possicol0325(main-damodelcolumn-primaryColumnTag 是否主键字段),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.0',internal_version='0' WHERE model_id ='main-possicol0325';
		DBMS_OUTPUT.PUT_LINE('升级模型main-possicol0325成功,模型版本号更新为:V1.0.0,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-possicol0335 ↓----------------------------------------------
--模型：每年各学院授课教师各专业技术职务级别人数及占比 POS_SI_COL_0335 main-possicol0335
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-possicol0335'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-possicol0335版本号:'||v_version);
	IF v_version >= 'V1.0.0.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-possicol0335的本次增量版本号:V1.0.0,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1866312606156337163','main-possicol0335','V1.0.0','0','upgrade','add','每年各学院授课教师各专业技术职务级别人数及占比','zb','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1866312606156337163');
		-- 新增【模型】：main-possicol0335
		insert into lowcode_model (model_app,publish_time,sys_flag,model_class,permit_ext_flag,extend_flag,model_label,model_name,model_table,model_classify,virtual_model,biz_model,order_index,id,status,create_by,create_time,update_by,update_time)
		select 'main',TIMESTAMP '2024-12-05 17:58:26',0,'com.wisedu.lowcode4j.main.po.Possicol0335',1,1,'每年各学院授课教师各专业技术职务级别人数及占比','possicol0335','POS_SI_COL_0335','COLLEGE_SZDW_ZB',1,0,662,'main-possicol0335',1,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model
			where id = 'main-possicol0335');
		BEGIN
		EXECUTE IMMEDIATE 'CREATE TABLE POS_SI_COL_0335(ID VARCHAR2(100) DEFAULT SYS_GUID() primary key ,CREATE_BY VARCHAR2(100),CREATE_TIME DATE,UPDATE_BY VARCHAR2(100),UPDATE_TIME DATE)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-955) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on table POS_SI_COL_0335 is ''每年各学院授课教师各专业技术职务级别人数及占比''';
		END;
		-- 新增【模型使用范围】:1733392428083001243
		insert into t_da_model_theme (model_id,theme_code,id,create_by,create_time,update_by,update_time)
		select 'main-possicol0335','POS','1733392428083001243','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_theme
			where id = '1733392428083001243');
		-- 新增【模型扩展】：main-possicol0335
		insert into t_da_model_extend (model_encoding,access_level,model_id,model_version,model_enable,entity_id,model_type,metric_id,use_num,theme_code,internal_version,id,create_by,create_time,update_by,update_time)
		select 'POS_SI_COL_0335','1','main-possicol0335','V1.0.0','1','COLLEGE','zb',NULL,0,'POS','0','main-possicol0335','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_extend
			where id = 'main-possicol0335');
		-- 新增【字段扩展】：main-possicol0335-createBy
		insert into t_da_model_column_extend (time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-possicol0335',0,'main-possicol0335-createBy','main-possicol0335-createBy',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-possicol0335-createBy');
		-- 新增【字段扩展】：main-possicol0335-createTime
		insert into t_da_model_column_extend (time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-possicol0335',0,'main-possicol0335-createTime','main-possicol0335-createTime',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-possicol0335-createTime');
		-- 新增【字段扩展】：main-possicol0335-id
		insert into t_da_model_column_extend (time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,primary_column_tag,entity_name_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-possicol0335',0,'main-possicol0335-id','main-possicol0335-id',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-possicol0335-id');
		-- 新增【字段扩展】：main-possicol0335-skjsrs
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-possicol0335',0,'main-possicol0335-skjsrs','main-possicol0335-skjsrs',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-possicol0335-skjsrs');
		-- 新增【字段扩展】：main-possicol0335-skjszb
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-possicol0335',0,'main-possicol0335-skjszb','main-possicol0335-skjszb',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-possicol0335-skjszb');
		-- 新增【字段扩展】：main-possicol0335-tjnf
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-possicol0335',0,'main-possicol0335-tjnf','main-possicol0335-tjnf',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-possicol0335-tjnf');
		-- 新增【字段扩展】：main-possicol0335-updateBy
		insert into t_da_model_column_extend (time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-possicol0335',0,'main-possicol0335-updateBy','main-possicol0335-updateBy',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-possicol0335-updateBy');
		-- 新增【字段扩展】：main-possicol0335-updateTime
		insert into t_da_model_column_extend (time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select NULL,'main-possicol0335',0,'main-possicol0335-updateTime','main-possicol0335-updateTime',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-possicol0335-updateTime');
		-- 新增【字段扩展】：main-possicol0335-xybh
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-possicol0335',0,'main-possicol0335-xybh','main-possicol0335-xybh',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-possicol0335-xybh');
		-- 新增【字段扩展】：main-possicol0335-zyjszwjb
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-possicol0335',0,'main-possicol0335-zyjszwjb','main-possicol0335-zyjszwjb',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-possicol0335-zyjszwjb');
		-- 新增【字段】：main-possicol0335-createBy
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-possicol0335',1,'createBy',0,'创建人',100,0,'main-possicol0335-createBy',1,0,1,'java.lang.String',0,0,1,'CREATE_BY',0,301,12,1,0,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-possicol0335-createBy');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column POS_SI_COL_0335.CREATE_BY is ''创建人''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616868589568','1866312606156337163','upgrade','add','column','main-possicol0335-createBy',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"createBy","main-datamodelcolumn-columnLabel":"创建人","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"CREATE_BY","main-datamodelcolumn-orderIndex":301,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616868589568');
		-- 新增【字段】：main-possicol0335-createTime
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-possicol0335',1,'createTime',0,'创建时间',0,0,'main-possicol0335-createTime',1,'date-full',0,1,'java.util.Date',0,0,1,'CREATE_TIME',0,302,93,1,0,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-possicol0335-createTime');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column POS_SI_COL_0335.CREATE_TIME is ''创建时间''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616872783872','1866312606156337163','upgrade','add','column','main-possicol0335-createTime',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"createTime","main-datamodelcolumn-columnLabel":"创建时间","main-datamodelcolumn-columnWidth":0,"main-datamodelcolumn-columnXtype":"date-full","main-datamodelcolumn-columnJavatype":"java.util.Date","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"CREATE_TIME","main-datamodelcolumn-orderIndex":302,"main-datamodelcolumn-columnDbtype":93}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616872783872');
		-- 新增【字段】：main-possicol0335-id
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-possicol0335',1,'id',0,'ID',100,0,'main-possicol0335-id',1,0,1,'java.lang.String',0,0,1,'ID',0,300,12,1,0,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-possicol0335-id');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column POS_SI_COL_0335.ID is ''ID''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616876978176','1866312606156337163','upgrade','add','column','main-possicol0335-id',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"id","main-datamodelcolumn-columnLabel":"ID","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"ID","main-datamodelcolumn-orderIndex":300,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616876978176');
		-- 新增【字段】：main-possicol0335-skjsrs
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'number','main-possicol0335',1,0,'skjsrs',0,'授课教师人数',30,0,'main-possicol0335-skjsrs',0,'number',0,1,'java.lang.Integer',1,'SKJSRS',3,4,1,1,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-possicol0335-skjsrs');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE POS_SI_COL_0335 ADD SKJSRS NUMBER(30)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column POS_SI_COL_0335.SKJSRS is ''授课教师人数''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616885366784','1866312606156337163','upgrade','add','column','main-possicol0335-skjsrs',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"number","main-datamodelcolumn-columnJavaname":"skjsrs","main-datamodelcolumn-columnLabel":"授课教师人数","main-datamodelcolumn-columnWidth":30,"main-datamodelcolumn-columnXtype":"number","main-datamodelcolumn-columnJavatype":"java.lang.Integer","main-datamodelcolumn-columnDbname":"SKJSRS","main-datamodelcolumn-orderIndex":3,"main-datamodelcolumn-columnDbtype":4}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616885366784');
		-- 新增【字段】：main-possicol0335-skjszb
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_precision,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'percentage','main-possicol0335',1,0,'skjszb',0,'授课教师占比',8,0,'main-possicol0335-skjszb',4,0,'number',0,1,'java.lang.Double',1,'SKJSZB',5,8,1,1,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-possicol0335-skjszb');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE POS_SI_COL_0335 ADD SKJSZB NUMBER(8,4)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column POS_SI_COL_0335.SKJSZB is ''授课教师占比''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616889561088','1866312606156337163','upgrade','add','column','main-possicol0335-skjszb',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"percentage","main-datamodelcolumn-columnJavaname":"skjszb","main-datamodelcolumn-columnLabel":"授课教师占比","main-datamodelcolumn-columnWidth":8,"main-datamodelcolumn-columnXtype":"number","main-datamodelcolumn-columnJavatype":"java.lang.Double","main-datamodelcolumn-columnDbname":"SKJSZB","main-datamodelcolumn-orderIndex":5,"main-datamodelcolumn-columnDbtype":8}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616889561088');
		-- 新增【字段】：main-possicol0335-tjnf
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_jsonparam,column_width,event_time_point,id,column_format,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'year','main-possicol0335',1,0,'tjnf',0,'统计年份','{"type":"year"}',4,0,'main-possicol0335-tjnf','yyyy',0,'date-local',0,1,'java.lang.String',1,'TJNF',2,12,1,1,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-possicol0335-tjnf');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE POS_SI_COL_0335 ADD TJNF VARCHAR2(4)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column POS_SI_COL_0335.TJNF is ''统计年份''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616893755392','1866312606156337163','upgrade','add','column','main-possicol0335-tjnf',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"year","main-datamodelcolumn-columnJavaname":"tjnf","main-datamodelcolumn-columnLabel":"统计年份","main-datamodelcolumn-columnWidth":4,"main-datamodelcolumn-columnFormat":"yyyy","main-datamodelcolumn-columnXtype":"date-local","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"TJNF","main-datamodelcolumn-orderIndex":2,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616893755392');
		-- 新增【字段】：main-possicol0335-updateBy
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-possicol0335',1,'updateBy',0,'更新人',100,0,'main-possicol0335-updateBy',1,0,1,'java.lang.String',0,0,1,'UPDATE_BY',0,303,12,1,0,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-possicol0335-updateBy');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column POS_SI_COL_0335.UPDATE_BY is ''更新人''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616897949696','1866312606156337163','upgrade','add','column','main-possicol0335-updateBy',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"updateBy","main-datamodelcolumn-columnLabel":"更新人","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"UPDATE_BY","main-datamodelcolumn-orderIndex":303,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616897949696');
		-- 新增【字段】：main-possicol0335-updateTime
		insert into lowcode_model_column (model_id,column_display,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,dbf_size,column_notnull,column_update,column_dbname,form_required,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'main-possicol0335',1,'updateTime',0,'更新时间',0,0,'main-possicol0335-updateTime',1,'date-full',0,1,'java.util.Date',0,0,1,'UPDATE_TIME',0,304,93,1,0,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-possicol0335-updateTime');
		BEGIN
		EXECUTE IMMEDIATE 'comment on column POS_SI_COL_0335.UPDATE_TIME is ''更新时间''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616902144000','1866312606156337163','upgrade','add','column','main-possicol0335-updateTime',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-columnJavaname":"updateTime","main-datamodelcolumn-columnLabel":"更新时间","main-datamodelcolumn-columnWidth":0,"main-datamodelcolumn-columnXtype":"date-full","main-datamodelcolumn-columnJavatype":"java.util.Date","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-columnDbname":"UPDATE_TIME","main-datamodelcolumn-orderIndex":304,"main-datamodelcolumn-columnDbtype":93}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616902144000');
		-- 新增【字段】：main-possicol0335-xybh
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'number','main-possicol0335',1,0,'xybh',0,'学院编号',30,0,'main-possicol0335-xybh',0,'number',0,1,'java.lang.Integer',1,'XYBH',1,4,1,1,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-possicol0335-xybh');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE POS_SI_COL_0335 ADD XYBH NUMBER(30)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column POS_SI_COL_0335.XYBH is ''学院编号''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616910532608','1866312606156337163','upgrade','add','column','main-possicol0335-xybh',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"number","main-datamodelcolumn-columnJavaname":"xybh","main-datamodelcolumn-columnLabel":"学院编号","main-datamodelcolumn-columnWidth":30,"main-datamodelcolumn-columnXtype":"number","main-datamodelcolumn-columnJavatype":"java.lang.Integer","main-datamodelcolumn-columnDbname":"XYBH","main-datamodelcolumn-orderIndex":1,"main-datamodelcolumn-columnDbtype":4}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616910532608');
		-- 新增【字段】：main-possicol0335-zyjszwjb
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-possicol0335',1,0,'zyjszwjb',0,'专业技术职务级别',100,0,'main-possicol0335-zyjszwjb',0,'text',0,1,'java.lang.String',1,'ZYJSZWJB',4,12,1,1,'dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-possicol0335-zyjszwjb');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE POS_SI_COL_0335 ADD ZYJSZWJB VARCHAR2(100)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column POS_SI_COL_0335.ZYJSZWJB is ''专业技术职务级别''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1866312616914726912','1866312606156337163','upgrade','add','column','main-possicol0335-zyjszwjb',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"zyjszwjb","main-datamodelcolumn-columnLabel":"专业技术职务级别","main-datamodelcolumn-columnWidth":100,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"ZYJSZWJB","main-datamodelcolumn-orderIndex":4,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1866312616914726912');
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-possicol0335' and FIELD_NAME='main-damodelcolumn-entityTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-possicol0335(main-damodelcolumn-entityTag 是否实体标识)，跳过更新');
			ELSE
				update t_da_model_column_extend set entity_tag = '0'
				where model_id = 'main-possicol0335';
				update t_da_model_column_extend set entity_tag = '1'
				where id in ('main-possicol0335-xybh');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1866312616990224384','1866312606156337163','upgrade','update','model','main-possicol0335','main-damodelcolumn-entityTag','{"变更前":{"main-damodelcolumn-entityTag":""},"变更后":{"main-damodelcolumn-entityTag":"学院编号"}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1866312616990224384');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-possicol0335(main-damodelcolumn-entityTag 是否实体标识),sql执行错误:' || SQLERRM);
		END;
		
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-possicol0335' and FIELD_NAME='main-damodelcolumn-timeTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-possicol0335(main-damodelcolumn-timeTag 是否时点标识字段)，跳过更新');
			ELSE
				update t_da_model_column_extend set time_tag = '0'
				where model_id = 'main-possicol0335';
				update t_da_model_column_extend set time_tag = '1'
				where id in ('main-possicol0335-tjnf');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1866312616994418688','1866312606156337163','upgrade','update','model','main-possicol0335','main-damodelcolumn-timeTag','{"变更前":{"main-damodelcolumn-timeTag":""},"变更后":{"main-damodelcolumn-timeTag":"统计年份"}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1866312616994418688');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-possicol0335(main-damodelcolumn-timeTag 是否时点标识字段),sql执行错误:' || SQLERRM);
		END;
		
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-possicol0335' and FIELD_NAME='main-damodelcolumn-valueTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-possicol0335(main-damodelcolumn-valueTag 是否指标值字段)，跳过更新');
			ELSE
				update t_da_model_column_extend set value_tag = '0'
				where model_id = 'main-possicol0335';
				update t_da_model_column_extend set value_tag = '1'
				where id in ('main-possicol0335-skjsrs','main-possicol0335-skjszb');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1866312616998612992','1866312606156337163','upgrade','update','model','main-possicol0335','main-damodelcolumn-valueTag','{"变更前":{"main-damodelcolumn-valueTag":""},"变更后":{"main-damodelcolumn-valueTag":"授课教师人数,授课教师占比"}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1866312616998612992');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-possicol0335(main-damodelcolumn-valueTag 是否指标值字段),sql执行错误:' || SQLERRM);
		END;
		
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='model' and FIELD_ID='main-possicol0335' and FIELD_NAME='main-damodelcolumn-primaryColumnTag';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-possicol0335(main-damodelcolumn-primaryColumnTag 是否主键字段)，跳过更新');
			ELSE
				update t_da_model_column_extend set primary_column_tag = '0'
				where model_id = 'main-possicol0335';
				update t_da_model_column_extend set primary_column_tag = '1'
				where id in ('main-possicol0335-id');
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1866312617002807296','1866312606156337163','upgrade','update','model','main-possicol0335','main-damodelcolumn-primaryColumnTag','{"变更前":{"main-damodelcolumn-primaryColumnTag":""},"变更后":{"main-damodelcolumn-primaryColumnTag":"ID"}}','dataapp',TIMESTAMP '2024-12-10 10:42:56','dataapp',TIMESTAMP '2024-12-10 10:42:56'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1866312617002807296');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-possicol0335(main-damodelcolumn-primaryColumnTag 是否主键字段),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.0',internal_version='0' WHERE model_id ='main-possicol0335';
		DBMS_OUTPUT.PUT_LINE('升级模型main-possicol0335成功,模型版本号更新为:V1.0.0,内部版本号更新为：0');
	END IF;
END;
/
