----------------------------------------------↓ 模型 main-abdteabdsjbxx ↓----------------------------------------------
--模型：班导师基本信息 ABD_TEA_BDSJBXX main-abdteabdsjbxx
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdteabdsjbxx'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdteabdsjbxx版本号:'||v_version);
	IF v_version >= 'V1.0.2.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdteabdsjbxx的本次增量版本号:V1.0.2,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1877188266877792256','main-abdteabdsjbxx','V1.0.2','0','upgrade','update','班导师基本信息','jcsj','dataapp',TIMESTAMP '2025-01-09 10:58:57','dataapp',TIMESTAMP '2025-01-09 10:58:57'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1877188266877792256');
		-- 修改【字段】：main-abdteabdsjbxx-zy columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-abdteabdsjbxx-zy' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdteabdsjbxx-zy(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'major'
				where id = 'main-abdteabdsjbxx-zy';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1877188283432710144','1877188266877792256','upgrade','update','column','main-abdteabdsjbxx-zy','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"zy","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"ZY","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":40,"main-datamodelcolumn-columnLabel":"专业","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":5,"main-datamodelcolumn-logicalType":"quoteCode","main-datamodelcolumn-columnDict":"PTGDXXBZKZYML","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"major"}}','dataapp',TIMESTAMP '2025-01-09 10:58:57','dataapp',TIMESTAMP '2025-01-09 10:58:57'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1877188283432710144');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdteabdsjbxx-zy(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.2',internal_version='0' WHERE model_id ='main-abdteabdsjbxx';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdteabdsjbxx成功,模型版本号更新为:V1.0.2,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-abdteapjbds ↓----------------------------------------------
--模型：班导师评教结果 ABD_TEA_PJBDS main-abdteapjbds
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdteapjbds'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdteapjbds版本号:'||v_version);
	IF v_version >= 'V1.0.2.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdteapjbds的本次增量版本号:V1.0.2,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1877188266877792257','main-abdteapjbds','V1.0.2','0','upgrade','update','班导师评教结果','jcsj','dataapp',TIMESTAMP '2025-01-09 10:58:57','dataapp',TIMESTAMP '2025-01-09 10:58:57'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1877188266877792257');
		-- 修改【字段】：main-abdteapjbds-zy columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-abdteapjbds-zy' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdteapjbds-zy(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'major'
				where id = 'main-abdteapjbds-zy';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1877188283520790528','1877188266877792257','upgrade','update','column','main-abdteapjbds-zy','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"zy","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"ZY","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":600,"main-datamodelcolumn-columnLabel":"专业","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":9,"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnDict":"PTGDXXBZKZYML","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"major"}}','dataapp',TIMESTAMP '2025-01-09 10:58:57','dataapp',TIMESTAMP '2025-01-09 10:58:57'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1877188283520790528');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdteapjbds-zy(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		-- 修改【字段】：main-abdteapjbds-zydm columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-abdteapjbds-zydm' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdteapjbds-zydm(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'major'
				where id = 'main-abdteapjbds-zydm';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1877188283529179136','1877188266877792257','upgrade','update','column','main-abdteapjbds-zydm','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"zydm","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"ZYDM","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":40,"main-datamodelcolumn-columnLabel":"专业代码","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":13,"main-datamodelcolumn-logicalType":"quoteCode","main-datamodelcolumn-columnDict":"PTGDXXBZKZYML","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"major"}}','dataapp',TIMESTAMP '2025-01-09 10:58:57','dataapp',TIMESTAMP '2025-01-09 10:58:57'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1877188283529179136');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdteapjbds-zydm(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.2',internal_version='0' WHERE model_id ='main-abdteapjbds';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdteapjbds成功,模型版本号更新为:V1.0.2,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-abdteapjjxb ↓----------------------------------------------
--模型：教学班评教结果 ABD_TEA_PJJXB main-abdteapjjxb
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdteapjjxb'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdteapjjxb版本号:'||v_version);
	IF v_version >= 'V1.0.4.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdteapjjxb的本次增量版本号:V1.0.4,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1877188266877792258','main-abdteapjjxb','V1.0.4','0','upgrade','update','教学班评教结果','jcsj','dataapp',TIMESTAMP '2025-01-09 10:58:57','dataapp',TIMESTAMP '2025-01-09 10:58:57'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1877188266877792258');
		-- 修改【字段】：main-abdteapjjxb-kclb columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-abdteapjjxb-kclb' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdteapjjxb-kclb(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = ''
				where id = 'main-abdteapjjxb-kclb';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1877188283608870912','1877188266877792258','upgrade','update','column','main-abdteapjjxb-kclb','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"kclb","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"KCLB","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":40,"main-datamodelcolumn-columnLabel":"课程类别","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":18,"main-datamodelcolumn-logicalType":"quoteCode","main-datamodelcolumn-columnDict":"KCLB","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":""}}','dataapp',TIMESTAMP '2025-01-09 10:58:57','dataapp',TIMESTAMP '2025-01-09 10:58:57'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1877188283608870912');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdteapjjxb-kclb(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		-- 修改【字段】：main-abdteapjjxb-zydm columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-abdteapjjxb-zydm' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdteapjjxb-zydm(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'major'
				where id = 'main-abdteapjjxb-zydm';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1877188283613065216','1877188266877792258','upgrade','update','column','main-abdteapjjxb-zydm','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"zydm","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"ZYDM","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":40,"main-datamodelcolumn-columnLabel":"专业代码","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":21,"main-datamodelcolumn-logicalType":"quoteCode","main-datamodelcolumn-columnDict":"PTGDXXBZKZYML","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"major"}}','dataapp',TIMESTAMP '2025-01-09 10:58:57','dataapp',TIMESTAMP '2025-01-09 10:58:57'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1877188283613065216');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdteapjjxb-zydm(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		-- 修改【字段】：main-abdteapjjxb-zymc columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-abdteapjjxb-zymc' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdteapjjxb-zymc(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'major'
				where id = 'main-abdteapjjxb-zymc';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1877188283621453824','1877188266877792258','upgrade','update','column','main-abdteapjjxb-zymc','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"zymc","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"ZYMC","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":600,"main-datamodelcolumn-columnLabel":"专业名称","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":22,"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnDict":"PTGDXXBZKZYML","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"major"}}','dataapp',TIMESTAMP '2025-01-09 10:58:57','dataapp',TIMESTAMP '2025-01-09 10:58:57'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1877188283621453824');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdteapjjxb-zymc(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.4',internal_version='0' WHERE model_id ='main-abdteapjjxb';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdteapjjxb成功,模型版本号更新为:V1.0.4,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-abdundxscjxx ↓----------------------------------------------
--模型：学生成绩信息 ABD_UND_XSCJXX main-abdundxscjxx
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdundxscjxx'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdundxscjxx版本号:'||v_version);
	IF v_version >= 'V1.0.2.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdundxscjxx的本次增量版本号:V1.0.2,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1877188266877792259','main-abdundxscjxx','V1.0.2','0','upgrade','update','学生成绩信息','jcsj','dataapp',TIMESTAMP '2025-01-09 10:58:57','dataapp',TIMESTAMP '2025-01-09 10:58:57'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1877188266877792259');
		-- 修改【字段】：main-abdundxscjxx-zydm columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-abdundxscjxx-zydm' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdundxscjxx-zydm(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'major'
				where id = 'main-abdundxscjxx-zydm';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1877188283701145600','1877188266877792259','upgrade','update','column','main-abdundxscjxx-zydm','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"zydm","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"ZYDM","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":40,"main-datamodelcolumn-columnLabel":"专业代码","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":27,"main-datamodelcolumn-logicalType":"quoteCode","main-datamodelcolumn-columnDict":"PTGDXXBZKZYML","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"major"}}','dataapp',TIMESTAMP '2025-01-09 10:58:57','dataapp',TIMESTAMP '2025-01-09 10:58:57'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1877188283701145600');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdundxscjxx-zydm(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.2',internal_version='0' WHERE model_id ='main-abdundxscjxx';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdundxscjxx成功,模型版本号更新为:V1.0.2,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-abdundxyyjjg ↓----------------------------------------------
--模型：学业预警结果 ABD_UND_XYYJJG main-abdundxyyjjg
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-abdundxyyjjg'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-abdundxyyjjg版本号:'||v_version);
	IF v_version >= 'V1.0.2.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-abdundxyyjjg的本次增量版本号:V1.0.2,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1877188266877792260','main-abdundxyyjjg','V1.0.2','0','upgrade','update','学业预警结果','jcsj','dataapp',TIMESTAMP '2025-01-09 10:58:57','dataapp',TIMESTAMP '2025-01-09 10:58:57'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1877188266877792260');
		-- 修改【字段】：main-abdundxyyjjg-xnzydm columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-abdundxyyjjg-xnzydm' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdundxyyjjg-xnzydm(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'major'
				where id = 'main-abdundxyyjjg-xnzydm';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1877188283797614592','1877188266877792260','upgrade','update','column','main-abdundxyyjjg-xnzydm','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"xnzydm","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XNZYDM","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":40,"main-datamodelcolumn-columnLabel":"校内专业代码","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":17,"main-datamodelcolumn-logicalType":"quoteCode","main-datamodelcolumn-columnDict":"PTGDXXBZKZYML","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"major"}}','dataapp',TIMESTAMP '2025-01-09 10:58:57','dataapp',TIMESTAMP '2025-01-09 10:58:57'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1877188283797614592');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdundxyyjjg-xnzydm(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		-- 修改【字段】：main-abdundxyyjjg-xnzymc columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-abdundxyyjjg-xnzymc' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-abdundxyyjjg-xnzymc(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'major'
				where id = 'main-abdundxyyjjg-xnzymc';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1877188283806003200','1877188266877792260','upgrade','update','column','main-abdundxyyjjg-xnzymc','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"xnzymc","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XNZYMC","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":600,"main-datamodelcolumn-columnLabel":"校内专业名称","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":5,"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnDict":"PTGDXXBZKZYML","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"major"}}','dataapp',TIMESTAMP '2025-01-09 10:58:57','dataapp',TIMESTAMP '2025-01-09 10:58:57'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1877188283806003200');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-abdundxyyjjg-xnzymc(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.2',internal_version='0' WHERE model_id ='main-abdundxyyjjg';
		DBMS_OUTPUT.PUT_LINE('升级模型main-abdundxyyjjg成功,模型版本号更新为:V1.0.2,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-insbksfxsxwqk ↓----------------------------------------------
--模型：本科生辅修、双学位情况 INS_BKSFXSXWQK main-insbksfxsxwqk
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-insbksfxsxwqk'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-insbksfxsxwqk版本号:'||v_version);
	IF v_version >= 'V1.0.2.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-insbksfxsxwqk的本次增量版本号:V1.0.2,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1877188266877792261','main-insbksfxsxwqk','V1.0.2','0','upgrade','update','本科生辅修、双学位情况','jcsj','dataapp',TIMESTAMP '2025-01-09 10:58:58','dataapp',TIMESTAMP '2025-01-09 10:58:58'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1877188266877792261');
		-- 修改【字段】：main-insbksfxsxwqk-fxxnzydm columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-insbksfxsxwqk-fxxnzydm' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-insbksfxsxwqk-fxxnzydm(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'major'
				where id = 'main-insbksfxsxwqk-fxxnzydm';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1877188283919249408','1877188266877792261','upgrade','update','column','main-insbksfxsxwqk-fxxnzydm','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"fxxnzydm","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"FXXNZYDM","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":40,"main-datamodelcolumn-columnLabel":"辅修（双学位）校内专业代码","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":4,"main-datamodelcolumn-logicalType":"quoteCode","main-datamodelcolumn-columnDict":"PTGDXXBZKZYML","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"major"}}','dataapp',TIMESTAMP '2025-01-09 10:58:58','dataapp',TIMESTAMP '2025-01-09 10:58:58'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1877188283919249408');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-insbksfxsxwqk-fxxnzydm(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		-- 修改【字段】：main-insbksfxsxwqk-fxxnzymc columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-insbksfxsxwqk-fxxnzymc' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-insbksfxsxwqk-fxxnzymc(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'major'
				where id = 'main-insbksfxsxwqk-fxxnzymc';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1877188283927638016','1877188266877792261','upgrade','update','column','main-insbksfxsxwqk-fxxnzymc','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"fxxnzymc","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"FXXNZYMC","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":600,"main-datamodelcolumn-columnLabel":"辅修（双学位）校内专业名称","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":5,"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnDict":"PTGDXXBZKZYML","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"major"}}','dataapp',TIMESTAMP '2025-01-09 10:58:58','dataapp',TIMESTAMP '2025-01-09 10:58:58'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1877188283927638016');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-insbksfxsxwqk-fxxnzymc(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.2',internal_version='0' WHERE model_id ='main-insbksfxsxwqk';
		DBMS_OUTPUT.PUT_LINE('升级模型main-insbksfxsxwqk成功,模型版本号更新为:V1.0.2,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-insbkszzyqk ↓----------------------------------------------
--模型：本科生转专业情况 INS_BKSZZYQK main-insbkszzyqk
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-insbkszzyqk'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-insbkszzyqk版本号:'||v_version);
	IF v_version >= 'V1.0.2.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-insbkszzyqk的本次增量版本号:V1.0.2,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1877188266877792262','main-insbkszzyqk','V1.0.2','0','upgrade','update','本科生转专业情况','jcsj','dataapp',TIMESTAMP '2025-01-09 10:58:58','dataapp',TIMESTAMP '2025-01-09 10:58:58'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1877188266877792262');
		-- 修改【字段】：main-insbkszzyqk-zcxnzydm columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-insbkszzyqk-zcxnzydm' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-insbkszzyqk-zcxnzydm(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'major'
				where id = 'main-insbkszzyqk-zcxnzydm';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1877188284032495616','1877188266877792262','upgrade','update','column','main-insbkszzyqk-zcxnzydm','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"zcxnzydm","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"ZCXNZYDM","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":40,"main-datamodelcolumn-columnLabel":"转出校内专业（大类）代码","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":3,"main-datamodelcolumn-logicalType":"quoteCode","main-datamodelcolumn-columnDict":"PTGDXXBZKZYML","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"major"}}','dataapp',TIMESTAMP '2025-01-09 10:58:58','dataapp',TIMESTAMP '2025-01-09 10:58:58'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1877188284032495616');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-insbkszzyqk-zcxnzydm(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		-- 修改【字段】：main-insbkszzyqk-zcxnzymc columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-insbkszzyqk-zcxnzymc' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-insbkszzyqk-zcxnzymc(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'major'
				where id = 'main-insbkszzyqk-zcxnzymc';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1877188284040884224','1877188266877792262','upgrade','update','column','main-insbkszzyqk-zcxnzymc','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"zcxnzymc","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"ZCXNZYMC","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":600,"main-datamodelcolumn-columnLabel":"转出校内专业（大类）名称","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":4,"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnDict":"PTGDXXBZKZYML","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"major"}}','dataapp',TIMESTAMP '2025-01-09 10:58:58','dataapp',TIMESTAMP '2025-01-09 10:58:58'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1877188284040884224');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-insbkszzyqk-zcxnzymc(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		-- 修改【字段】：main-insbkszzyqk-zrxnzydm columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-insbkszzyqk-zrxnzydm' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-insbkszzyqk-zrxnzydm(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'major'
				where id = 'main-insbkszzyqk-zrxnzydm';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1877188284049272832','1877188266877792262','upgrade','update','column','main-insbkszzyqk-zrxnzydm','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"zrxnzydm","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"ZRXNZYDM","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":40,"main-datamodelcolumn-columnLabel":"转入校内专业（大类）代码","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":5,"main-datamodelcolumn-logicalType":"quoteCode","main-datamodelcolumn-columnDict":"PTGDXXBZKZYML","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"major"}}','dataapp',TIMESTAMP '2025-01-09 10:58:58','dataapp',TIMESTAMP '2025-01-09 10:58:58'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1877188284049272832');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-insbkszzyqk-zrxnzydm(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		-- 修改【字段】：main-insbkszzyqk-zrxnzymc columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-insbkszzyqk-zrxnzymc' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-insbkszzyqk-zrxnzymc(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'major'
				where id = 'main-insbkszzyqk-zrxnzymc';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1877188284053467136','1877188266877792262','upgrade','update','column','main-insbkszzyqk-zrxnzymc','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"zrxnzymc","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"ZRXNZYMC","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":600,"main-datamodelcolumn-columnLabel":"转入校内专业（大类）名称","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":6,"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnDict":"PTGDXXBZKZYML","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"major"}}','dataapp',TIMESTAMP '2025-01-09 10:58:58','dataapp',TIMESTAMP '2025-01-09 10:58:58'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1877188284053467136');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-insbkszzyqk-zrxnzymc(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.2',internal_version='0' WHERE model_id ='main-insbkszzyqk';
		DBMS_OUTPUT.PUT_LINE('升级模型main-insbkszzyqk成功,模型版本号更新为:V1.0.2,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-insjzgqtxx ↓----------------------------------------------
--模型：教职工其他信息 INS_JZGQTXX main-insjzgqtxx
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-insjzgqtxx'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-insjzgqtxx版本号:'||v_version);
	IF v_version >= 'V1.0.2.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-insjzgqtxx的本次增量版本号:V1.0.2,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1877188266877792263','main-insjzgqtxx','V1.0.2','0','upgrade','update','教职工其他信息','jcsj','dataapp',TIMESTAMP '2025-01-09 10:58:58','dataapp',TIMESTAMP '2025-01-09 10:58:58'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1877188266877792263');
		-- 修改【字段】：main-insjzgqtxx-rjzydm columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-insjzgqtxx-rjzydm' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-insjzgqtxx-rjzydm(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'major'
				where id = 'main-insjzgqtxx-rjzydm';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1877188284137353216','1877188266877792263','upgrade','update','column','main-insjzgqtxx-rjzydm','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"rjzydm","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"RJZYDM","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":40,"main-datamodelcolumn-columnLabel":"任教专业代码","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":5,"main-datamodelcolumn-logicalType":"quoteCode","main-datamodelcolumn-columnDict":"PTGDXXBZKZYML","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"major"}}','dataapp',TIMESTAMP '2025-01-09 10:58:58','dataapp',TIMESTAMP '2025-01-09 10:58:58'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1877188284137353216');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-insjzgqtxx-rjzydm(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		-- 修改【字段】：main-insjzgqtxx-zymc columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-insjzgqtxx-zymc' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-insjzgqtxx-zymc(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'major'
				where id = 'main-insjzgqtxx-zymc';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1877188284145741824','1877188266877792263','upgrade','update','column','main-insjzgqtxx-zymc','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"zymc","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"ZYMC","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":600,"main-datamodelcolumn-columnLabel":"任教专业名称","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":4,"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnDict":"PTGDXXBZKZYML","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"major"}}','dataapp',TIMESTAMP '2025-01-09 10:58:58','dataapp',TIMESTAMP '2025-01-09 10:58:58'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1877188284145741824');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-insjzgqtxx-zymc(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.2',internal_version='0' WHERE model_id ='main-insjzgqtxx';
		DBMS_OUTPUT.PUT_LINE('升级模型main-insjzgqtxx成功,模型版本号更新为:V1.0.2,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-inskkqk ↓----------------------------------------------
--模型：开课情况 INS_KKQK main-inskkqk
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-inskkqk'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-inskkqk版本号:'||v_version);
	IF v_version >= 'V1.0.4.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-inskkqk的本次增量版本号:V1.0.4,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1877188266877792264','main-inskkqk','V1.0.4','0','upgrade','update','开课情况','jcsj','dataapp',TIMESTAMP '2025-01-09 10:58:58','dataapp',TIMESTAMP '2025-01-09 10:58:58'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1877188266877792264');
		-- 修改【字段】：main-inskkqk-kclb columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-inskkqk-kclb' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-inskkqk-kclb(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'KCSX'
				where id = 'main-inskkqk-kclb';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1877188284225433600','1877188266877792264','upgrade','update','column','main-inskkqk-kclb','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"kclb","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"KCLB","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":40,"main-datamodelcolumn-columnLabel":"课程类别","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":5,"main-datamodelcolumn-logicalType":"quoteCode","main-datamodelcolumn-columnDict":"KCLB","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"KCSX"}}','dataapp',TIMESTAMP '2025-01-09 10:58:58','dataapp',TIMESTAMP '2025-01-09 10:58:58'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1877188284225433600');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-inskkqk-kclb(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		-- 修改【字段】：main-inskkqk-kcxz columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-inskkqk-kcxz' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-inskkqk-kcxz(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'KCXZ'
				where id = 'main-inskkqk-kcxz';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1877188284233822208','1877188266877792264','upgrade','update','column','main-inskkqk-kcxz','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"kcxz","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"KCXZ","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":40,"main-datamodelcolumn-columnLabel":"课程性质","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":6,"main-datamodelcolumn-logicalType":"quoteCode","main-datamodelcolumn-columnDict":"KCSX","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"KCXZ"}}','dataapp',TIMESTAMP '2025-01-09 10:58:58','dataapp',TIMESTAMP '2025-01-09 10:58:58'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1877188284233822208');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-inskkqk-kcxz(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.4',internal_version='0' WHERE model_id ='main-inskkqk';
		DBMS_OUTPUT.PUT_LINE('升级模型main-inskkqk成功,模型版本号更新为:V1.0.4,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-inssicol0249 ↓----------------------------------------------
--模型：每年各学院毕业综合训练指导教师数量 INS_SI_COL_0249 main-inssicol0249
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-inssicol0249'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-inssicol0249版本号:'||v_version);
	IF v_version >= 'V1.0.3.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-inssicol0249的本次增量版本号:V1.0.3,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1877188266877792265','main-inssicol0249','V1.0.3','0','upgrade','update','每年各学院毕业综合训练指导教师数量','zb','dataapp',TIMESTAMP '2025-01-09 10:58:58','dataapp',TIMESTAMP '2025-01-09 10:58:58'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1877188266877792265');
		-- 新增【字段扩展】：main-inssicol0249-xtlb
		insert into t_da_model_column_extend (delete_flag,time_tag,model_id,use_num,column_id,model_version,id,value_tag,entity_tag,entity_name_tag,primary_column_tag,create_by,create_time,update_by,update_time)
		select '0',NULL,'main-inssicol0249',0,'main-inssicol0249-xtlb','V1.0.2','main-inssicol0249-xtlb',NULL,NULL,NULL,NULL,'dataapp',TIMESTAMP '2025-01-09 10:58:58','dataapp',TIMESTAMP '2025-01-09 10:58:58'
		from dual
		where not exists (
			select 1
			from t_da_model_column_extend
			where id = 'main-inssicol0249-xtlb');
		-- 新增【字段】：main-inssicol0249-xtlb
		insert into lowcode_model_column (logical_type,model_id,column_display,virtual_column_status,column_javaname,column_readonly,column_label,column_width,event_time_point,id,column_hidden,column_xtype,column_encrypt,extend_flag,column_javatype,column_update,column_dbname,order_index,column_dbtype,column_add,virtual_column,create_by,create_time,update_by,update_time)
		select 'text','main-inssicol0249',1,0,'xtlb',0,'选题类别',600,0,'main-inssicol0249-xtlb',0,'text',0,1,'java.lang.String',1,'XTLB',5,12,1,1,'dataapp',TIMESTAMP '2025-01-09 10:58:58','dataapp',TIMESTAMP '2025-01-09 10:58:58'
		from dual
		where not exists (
			select 1
			from lowcode_model_column
			where id = 'main-inssicol0249-xtlb');
		BEGIN
		EXECUTE IMMEDIATE 'ALTER TABLE INS_SI_COL_0249 ADD XTLB VARCHAR2(600)';
		EXCEPTION
		WHEN OTHERS THEN
			IF SQLCODE NOT IN (-1430) THEN
				RAISE;
			END IF;
		END;
		BEGIN
		EXECUTE IMMEDIATE 'comment on column INS_SI_COL_0249.XTLB is ''选题类别''';
		END;
		-- 增量操作日志明细表
		insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
		select '1877188284309319680','1877188266877792265','upgrade','add','column','main-inssicol0249-xtlb',NULL,'{"变更前":{},"变更后":{"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnJavaname":"xtlb","main-datamodelcolumn-columnLabel":"选题类别","main-datamodelcolumn-columnWidth":600,"main-datamodelcolumn-columnXtype":"text","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XTLB","main-datamodelcolumn-orderIndex":5,"main-datamodelcolumn-columnDbtype":12}}','dataapp',TIMESTAMP '2025-01-09 10:58:58','dataapp',TIMESTAMP '2025-01-09 10:58:58'
		from dual
		where not exists (
			select 1
			from t_da_model_change_detail
			where id = '1877188284309319680');
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.3',internal_version='0' WHERE model_id ='main-inssicol0249';
		DBMS_OUTPUT.PUT_LINE('升级模型main-inssicol0249成功,模型版本号更新为:V1.0.3,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-insxnwsxsjsxjd ↓----------------------------------------------
--模型：校内外实习、实践、实训基地 INS_XNWSXSJSXJD main-insxnwsxsjsxjd
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-insxnwsxsjsxjd'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-insxnwsxsjsxjd版本号:'||v_version);
	IF v_version >= 'V1.0.2.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-insxnwsxsjsxjd的本次增量版本号:V1.0.2,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1877188266877792266','main-insxnwsxsjsxjd','V1.0.2','0','upgrade','update','校内外实习、实践、实训基地','jcsj','dataapp',TIMESTAMP '2025-01-09 10:58:58','dataapp',TIMESTAMP '2025-01-09 10:58:58'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1877188266877792266');
		-- 修改【字段】：main-insxnwsxsjsxjd-mxxnzy columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-insxnwsxsjsxjd-mxxnzy' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-insxnwsxsjsxjd-mxxnzy(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'major'
				where id = 'main-insxnwsxsjsxjd-mxxnzy';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1877188284384817152','1877188266877792266','upgrade','update','column','main-insxnwsxsjsxjd-mxxnzy','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"mxxnzy","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"MXXNZY","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":600,"main-datamodelcolumn-columnLabel":"面向校内专业","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":5,"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnDict":"PTGDXXBZKZYML","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"major"}}','dataapp',TIMESTAMP '2025-01-09 10:58:58','dataapp',TIMESTAMP '2025-01-09 10:58:58'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1877188284384817152');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-insxnwsxsjsxjd-mxxnzy(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		-- 修改【字段】：main-insxnwsxsjsxjd-xnzydm columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-insxnwsxsjsxjd-xnzydm' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-insxnwsxsjsxjd-xnzydm(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'major'
				where id = 'main-insxnwsxsjsxjd-xnzydm';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1877188284393205760','1877188266877792266','upgrade','update','column','main-insxnwsxsjsxjd-xnzydm','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"xnzydm","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XNZYDM","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":40,"main-datamodelcolumn-columnLabel":"校内专业代码","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":6,"main-datamodelcolumn-logicalType":"quoteCode","main-datamodelcolumn-columnDict":"PTGDXXBZKZYML","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"major"}}','dataapp',TIMESTAMP '2025-01-09 10:58:58','dataapp',TIMESTAMP '2025-01-09 10:58:58'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1877188284393205760');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-insxnwsxsjsxjd-xnzydm(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.2',internal_version='0' WHERE model_id ='main-insxnwsxsjsxjd';
		DBMS_OUTPUT.PUT_LINE('升级模型main-insxnwsxsjsxjd成功,模型版本号更新为:V1.0.2,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-insyszyqk ↓----------------------------------------------
--模型：优势（一流）专业情况 INS_YSZYQK main-insyszyqk
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-insyszyqk'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-insyszyqk版本号:'||v_version);
	IF v_version >= 'V1.0.2.0' THEN
		DBMS_OUTPUT.PUT_LINE('跳过升级,模型main-insyszyqk的本次增量版本号:V1.0.2,当前版本号'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1877188266877792267','main-insyszyqk','V1.0.2','0','upgrade','update','优势（一流）专业情况','jcsj','dataapp',TIMESTAMP '2025-01-09 10:58:58','dataapp',TIMESTAMP '2025-01-09 10:58:58'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1877188266877792267');
		-- 修改【字段】：main-insyszyqk-xnzydm columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-insyszyqk-xnzydm' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-insyszyqk-xnzydm(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'major'
				where id = 'main-insyszyqk-xnzydm';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1877188284481286144','1877188266877792267','upgrade','update','column','main-insyszyqk-xnzydm','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"xnzydm","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XNZYDM","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":40,"main-datamodelcolumn-columnLabel":"校内专业（大类）代码","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":2,"main-datamodelcolumn-logicalType":"quoteCode","main-datamodelcolumn-columnDict":"PTGDXXBZKZYML","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"major"}}','dataapp',TIMESTAMP '2025-01-09 10:58:58','dataapp',TIMESTAMP '2025-01-09 10:58:58'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1877188284481286144');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-insyszyqk-xnzydm(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		-- 修改【字段】：main-insyszyqk-xnzymc columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-insyszyqk-xnzymc' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-insyszyqk-xnzymc(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'major'
				where id = 'main-insyszyqk-xnzymc';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1877188284493869056','1877188266877792267','upgrade','update','column','main-insyszyqk-xnzymc','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"xnzymc","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"XNZYMC","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":600,"main-datamodelcolumn-columnLabel":"校内专业（大类）名称","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":3,"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnDict":"PTGDXXBZKZYML","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"major"}}','dataapp',TIMESTAMP '2025-01-09 10:58:58','dataapp',TIMESTAMP '2025-01-09 10:58:58'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1877188284493869056');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-insyszyqk-xnzymc(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = 'V1.0.2',internal_version='0' WHERE model_id ='main-insyszyqk';
		DBMS_OUTPUT.PUT_LINE('升级模型main-insyszyqk成功,模型版本号更新为:V1.0.2,内部版本号更新为：0');
	END IF;
END;
/
