/*
 Description		: [教师(TEA)]主题域报表表名对照表
 Author				: dataapp
 Date				: 2024-06-20 01:00:00
 Lowcode Version	: V1.1.1_Beta5
 Database           : oracle
*/

BEGIN
    EXECUTE IMMEDIATE 'CREATE TABLE t_table_map (
  id VARCHAR2(100 BYTE),
  table_name VARCHAR2(200 BYTE),
  comments VARCHAR2(500 BYTE),
  sql_str CLOB,
  create_by VARCHAR2(100),
  create_time DATE,
  update_by VARCHAR2(100),
  update_time DATE,PRIMARY KEY (id))';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

COMMENT ON TABLE t_table_map IS '报表模型表名映射';
COMMENT ON COLUMN t_table_map.id IS 'ID';
COMMENT ON COLUMN t_table_map.table_name IS '表名';
COMMENT ON COLUMN t_table_map.comments IS '表注释';
COMMENT ON COLUMN t_table_map.sql_str IS 'sql脚本';
COMMENT ON COLUMN t_table_map.create_by IS '创建人';
COMMENT ON COLUMN t_table_map.create_time IS '创建时间';
COMMENT ON COLUMN t_table_map.update_by IS '更新人';
COMMENT ON COLUMN t_table_map.update_time IS '更新时间';

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_col_xyjbxx', 'abd_col_xyjbxx', '学院基本信息', TO_CLOB('
select
学院基本信息.xybh as 学院编号,
学院基本信息.xymc as 学院名称
from abd_col_xyjbxx 学院基本信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_col_xyjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_col_xyxxx', 'abd_col_xyxxx', '学院系信息', TO_CLOB('
select
学院系信息.xybh as 学院编号,
学院系信息.xbh as 系编号,
学院系信息.xmc as 系名称,
学院系信息.ssxkly as 所属学科领域
from abd_col_xyxxx 学院系信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_col_xyxxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xxjbxx', 'abd_sch_xxjbxx', '学校基本信息', TO_CLOB('
select
学校基本信息.xxbsm as 学校标识码,
学校基本信息.xxmc as 学校名称,
学校基本信息.xxywmc as 学校英文名称,
学校基本信息.xxdz as 学校地址,
学校基本信息.xxyzbm as 学校邮政编码,
学校基本信息.xzqh as 行政区划,
学校基本信息.szdcxlx as 所在地城乡类型,
学校基本信息.jxny as 建校年月,
学校基本信息.xqr as 校庆日,
学校基本信息.xxbxlx as 学校办学类型,
学校基本信息.xxjbz as 学校举办者,
学校基本信息.xxzgbm as 学校主管部门,
学校基本信息.fddbrh as 法定代表人号,
学校基本信息.frzsh as 法人证书号,
学校基本信息.xzxm as 校长姓名,
学校基本信息.dwfzr as 党委负责人,
学校基本信息.zzjgm as 组织机构码,
学校基本信息.lxdh as 联系电话,
学校基本信息.czdh as 传真电话,
学校基本信息.dzxx as 电子信箱,
学校基本信息.xxbb as 学校办别,
学校基本信息.xxxz as 学校性质
from abd_sch_xxjbxx 学校基本信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xxjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xn', 'abd_sch_xn', '学年', TO_CLOB('
select
学年.xxbsm as 学校标识码,
学年.xh as 序号,
学年.xnbm as 学年编码,
学年.xnmc as 学年名称,
学年.xnksrq as 学年开始日期,
学年.xnjsrq as 学年结束日期
from abd_sch_xn 学年'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xn'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xnxq', 'abd_sch_xnxq', '学年学期', TO_CLOB('
select
学年学期.xxbsm as 学校标识码,
学年学期.xh as 序号,
学年学期.xnxqbm as 学年学期编码,
学年学期.xnxqmc as 学年学期名称,
学年学期.xqksrq as 学期开始日期,
学年学期.xqjsrq as 学期结束日期,
学年学期.ssxnbm as 所属学年编码,
学年学期.ssxn as 所属学年
from abd_sch_xnxq 学年学期'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xnxq'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_zzjgxx', 'abd_sch_zzjgxx', '组织机构信息', TO_CLOB('
select
组织机构信息.xxbsm as 学校标识码,
组织机构信息.jgdm as 机构代码,
组织机构信息.jgmc as 机构名称,
组织机构信息.jgjc as 机构简称,
组织机构信息.lsjgdm as 隶属机构代码,
组织机构信息.lsjg as 隶属机构,
组织机构信息.jglb as 机构类别,
组织机构信息.jlny as 建立年月,
组织机构信息.jgpx as 机构排序
from abd_sch_zzjgxx 组织机构信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_zzjgxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgjbxx', 'abd_tea_jzgjbxx', '教职工基本信息', TO_CLOB('
select
教职工基本信息.zgh as 教职工号,
教职工基本信息.xm as 姓名,
教职工基本信息.xb as 性别,
教职工基本信息.mz as 民族,
教职工基本信息.sfzjlx as 身份证件类型,
教职工基本信息.gatqw as 港澳台侨外,
教职工基本信息.zjxy as 宗教信仰,
教职工基本信息.zzmm as 政治面貌,
教职工基本信息.jg as 籍贯,
教职工基本信息.hkszs as 户口所在省,
教职工基本信息.hkszds as 户口所在地市,
教职工基本信息.hkszd as 户口所在地,
教职工基本信息.xx as 血型,
教职工基本信息.csrq as 出生日期,
教职工基本信息.csd as 出生地,
教职工基本信息.gjdq as 国家地区,
教职工基本信息.hyzk as 婚姻状况,
教职工基本信息.zgxl as 最高学历,
教职工基本信息.zgxw as 最高学位,
教职工基本信息.dyxl as 第一学历,
教职工基本信息.dyxlbyyx as 第一学历毕业院校,
教职工基本信息.dyxlbyyxlx as 第一学历毕业院校类型,
教职工基本信息.zgxlbyyx as 最高学历毕业院校,
教职工基本信息.zgxlbyyxlx as 最高学历毕业院校类型,
教职工基本信息.zhbyyx as 最后毕业院校,
教职工基本信息.zhbyyxlx as 最后毕业院校类型,
教职工基本信息.sfbxby as 是否本校毕业,
教职工基本信息.sjhm as 手机号码,
教职工基本信息.dzyx as 电子邮箱,
教职工基本信息.jtdz as 家庭地址,
教职工基本信息.ssjgdm as 所属机构代码,
教职工基本信息.s')||TO_CLOB('sjg as 所属机构,
教职工基本信息.ssxdm as 所属系代码,
教职工基本信息.ssx as 所属系,
教职工基本信息.jzglb as 教职工类别,
教职工基本信息.jzgly as 教职工来源,
教职工基本信息.bzlb as 编制类别,
教职工基本信息.yrfs as 用人方式,
教职工基本信息.cjny as 从教年月,
教职工基本信息.lxny as 来校年月,
教职工基本信息.jzjsprlb as 兼职教师聘任类别,
教职工基本信息.dslb as 导师类别,
教职工基本信息.fdylb as 辅导员类别,
教职工基本信息.sfssxjs as 是否双师型教师,
教职工基本信息.sfsjt as 是否双肩挑,
教职工基本信息.xkml as 学科门类,
教职工基本信息.yjxk as 一级学科,
教职工基本信息.ejxk as 二级学科,
教职工基本信息.yjfx as 研究方向,
教职工基本信息.jzgdqzt as 教职工当前状态,
教职工基本信息.lxrq as 离校日期,
教职工基本信息.yjtxrq as 预计退休日期,
教职工基本信息.zyjszw as 专业技术职务,
教职工基本信息.zyjszwjb as 专业技术职务级别,
教职工基本信息.zyjsgwdj as 专业技术岗位等级,
教职工基本信息.glgwdj as 管理岗位等级,
教职工基本信息.gqgwdj as 工勤岗位等级,
教职工基本信息.zygwlx as 主要岗位类型,
教职工基本信息.gwmc as 岗位名称,
教职工基本信息.gbzw as 干部职务,
教职工基本信息.gbzwjb as 干部职务级别,
教职工基本信息.nl as 年龄,
教职工基本信息.rjlx ')||TO_CLOB('as 任教类型,
教职工基本信息.rjzymc as 任教专业名称,
教职工基本信息.rjzydm as 任教专业代码,
教职工基本信息.zyrjsj as 专业任教时间,
教职工基本信息.sfsyjsry as 是否实验技术人员,
教职工基本信息.sfwp as 是否外聘,
教职工基本信息.glrylb as 管理人员类别,
教职工基本信息.sfbds as 是否班导师,
教职工基本信息.sfskjs as 是否授课教师,
教职工基本信息.sfszkjs as 是否思政课教师,
教职工基本信息.kzrq as 快照日期,
教职工基本信息.jkzk as 健康状况,
教职工基本信息.yjjgdm as 一级机构代码,
教职工基本信息.yjjg as 一级机构,
教职工基本信息.cjgzny as 参加工作年月,
教职工基本信息.sffdy as 是否辅导员,
教职工基本信息.zyjsgwlb as 专业技术岗位类别,
教职工基本信息.zrjslx as 专任教师类型,
教职工基本信息.zgxwcc as 最高学位层次,
教职工基本信息.zyjszwdm as 专业技术职务代码,
教职工基本信息.zyjszwjbdm as 专业技术职务级别代码
from abd_tea_jzgjbxx 教职工基本信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgjyjl', 'abd_tea_jzgjyjl', '教职工教育经历', TO_CLOB('
select
教职工教育经历.zgh as 教职工号,
教职工教育经历.rxny as 入学年月,
教职工教育经历.byny as 毕业年月,
教职工教育经历.byyxxhdw as 毕肄业学校或单位,
教职工教育经历.xl as 学历,
教职工教育经历.xw as 学位,
教职工教育经历.sxzy as 所学专业,
教职工教育经历.xwsygj as 学位授予国家,
教职工教育经历.gxlb as 高校类别,
教职工教育经历.xz as 学制
from abd_tea_jzgjyjl 教职工教育经历'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgjyjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgzzxx', 'abd_tea_jzgzzxx', '教职工资质信息', TO_CLOB('
select
教职工资质信息.zgh as 教职工号,
教职工资质信息.zzmc as 资质名称,
教职工资质信息.zzlb as 资质类别,
教职工资质信息.zzdj as 资质等级,
教职工资质信息.hdrq as 获得日期
from abd_tea_jzgzzxx 教职工资质信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgzzxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzggzjl', 'abd_tea_jzggzjl', '教职工工作经历', TO_CLOB('
select
教职工工作经历.zgh as 教职工号,
教职工工作经历.szdwmc as 所在单位名称,
教职工工作经历.gjdq as 国家地区,
教职工工作经历.crdzzw as 曾任党政职务,
教职工工作经历.crzyjszw as 曾任专业技术职务,
教职工工作经历.sfhwjl as 是否海外经历,
教职工工作经历.qsny as 起始年月,
教职工工作经历.jzny as 截止年月
from abd_tea_jzggzjl 教职工工作经历'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzggzjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_gccrcxx', 'abd_tea_gccrcxx', '高层次人才信息', TO_CLOB('
select
高层次人才信息.zgh as 教职工号,
高层次人才信息.rclb as 人才类别,
高层次人才信息.pzdw as 批准单位,
高层次人才信息.pzdwjb as 批准单位级别,
高层次人才信息.pzny as 批准年月
from abd_tea_gccrcxx 高层次人才信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_gccrcxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgjzxx', 'abd_tea_jzgjzxx', '教职工兼职信息', TO_CLOB('
select
教职工兼职信息.zgh as 教职工号,
教职工兼职信息.jzlx as 兼职类型,
教职工兼职信息.jzdw as 兼职单位,
教职工兼职信息.jzdwjb as 兼职单位级别,
教职工兼职信息.jzzw as 兼职职务,
教职工兼职信息.jzzwjb as 兼职职务级别,
教职工兼职信息.gjdq as 国家地区,
教职工兼职信息.qsrq as 起始日期,
教职工兼职信息.zzrq as 终止日期
from abd_tea_jzgjzxx 教职工兼职信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgjzxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_gbzwxx', 'abd_tea_gbzwxx', '干部职务信息', TO_CLOB('
select
干部职务信息.zgh as 教职工号,
干部职务信息.zwmc as 职务名称,
干部职务信息.zwlb as 职务类别,
干部职务信息.zwjb as 职务级别,
干部职务信息.rzdw as 任职单位,
干部职务信息.rzny as 任职年月,
干部职务信息.rzfs as 任职方式,
干部职务信息.rzqx as 任职期限,
干部职务信息.sfzr as 是否在任,
干部职务信息.mzrq as 免职日期,
干部职务信息.rzdwbm as 任职单位编码
from abd_tea_gbzwxx 干部职务信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_gbzwxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgjljryxx', 'abd_tea_jzgjljryxx', '教职工奖励及荣誉信息', TO_CLOB('
select
教职工奖励及荣誉信息.zgh as 教职工号,
教职工奖励及荣誉信息.jlmc as 奖励名称,
教职工奖励及荣誉信息.rych as 荣誉称号,
教职工奖励及荣誉信息.hjrq as 获奖日期,
教职工奖励及荣誉信息.jljb as 奖励级别,
教职工奖励及荣誉信息.jldj as 奖励等级,
教职工奖励及荣誉信息.jllb as 奖励类别,
教职工奖励及荣誉信息.bjdw as 颁奖单位,
教职工奖励及荣誉信息.hjxm as 获奖项目,
教职工奖励及荣誉信息.brpm as 本人排名
from abd_tea_jzgjljryxx 教职工奖励及荣誉信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgjljryxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_gnpx', 'abd_tea_gnpx', '国内培训', TO_CLOB('
select
国内培训.zgh as 教职工号,
国内培训.pxxmmc as 培训项目名称,
国内培训.pxjgmc as 培训机构名称,
国内培训.pxjb as 培训级别,
国内培训.pxfs as 培训方式,
国内培训.pxhdxs as 培训获得学时,
国内培训.pxnd as 培训年度
from abd_tea_gnpx 国内培训'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_gnpx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_hwyx', 'abd_tea_hwyx', '海外研修', TO_CLOB('
select
海外研修.zgh as 教职工号,
海外研修.ksrq as 开始日期,
海外研修.jsrq as 结束日期,
海外研修.gjdq as 国家地区,
海外研修.yxfxjg as 研修访学机构,
海外研修.xmmc as 项目名称,
海外研修.xmzzdw as 项目组织单位
from abd_tea_hwyx 海外研修'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_hwyx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzggwpyjl', 'abd_tea_jzggwpyjl', '教职工岗位聘用记录', TO_CLOB('
select
教职工岗位聘用记录.zgh as 教职工号,
教职工岗位聘用记录.gwmc as 岗位名称,
教职工岗位聘用记录.prqsrq as 聘任起始日期,
教职工岗位聘用记录.przzrq as 聘任终止日期,
教职工岗位聘用记录.sjprjsrq as 实际聘任结束日期,
教职工岗位聘用记录.sfzr as 是否在任
from abd_tea_jzggwpyjl 教职工岗位聘用记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzggwpyjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzggwdjpyjl', 'abd_tea_jzggwdjpyjl', '教职工岗位等级聘用记录', TO_CLOB('
select
教职工岗位等级聘用记录.zgh as 教职工号,
教职工岗位等级聘用记录.gwlx as 岗位类型,
教职工岗位等级聘用记录.gwdj as 岗位等级,
教职工岗位等级聘用记录.spdw as 受聘单位,
教职工岗位等级聘用记录.prqsrq as 聘任起始日期,
教职工岗位等级聘用记录.przzrq as 聘任终止日期
from abd_tea_jzggwdjpyjl 教职工岗位等级聘用记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzggwdjpyjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_zcppjl', 'abd_tea_zcppjl', '职称评聘记录', TO_CLOB('
select
职称评聘记录.zgh as 教职工号,
职称评聘记录.przyjszw as 聘任专业技术职务,
职称评聘记录.przyjszwjb as 聘任专业技术职务级别,
职称评聘记录.prqsrq as 聘任起始日期,
职称评聘记录.przzrq as 聘任终止日期,
职称评聘记录.prdw as 聘任单位,
职称评聘记录.pdzyjszw as 评定专业技术职务,
职称评聘记录.pdzyjszwjb as 评定专业技术职务级别,
职称评聘记录.pdrq as 评定日期,
职称评聘记录.psdw as 评审单位
from abd_tea_zcppjl 职称评聘记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_zcppjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgxnddjl', 'abd_tea_jzgxnddjl', '教职工校内调动记录', TO_CLOB('
select
教职工校内调动记录.zgh as 教职工号,
教职工校内调动记录.dcdw as 调出单位,
教职工校内调动记录.drdw as 调入单位,
教职工校内调动记录.ddrq as 调动日期,
教职工校内调动记录.sfzg as 是否转岗,
教职工校内调动记录.dcgw as 调出岗位,
教职工校内调动记录.drgw as 调入岗位,
教职工校内调动记录.dcdwbm as 调出单位编码,
教职工校内调动记录.drdwbm as 调入单位编码
from abd_tea_jzgxnddjl 教职工校内调动记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgxnddjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgndkhjl', 'abd_tea_jzgndkhjl', '教职工年度考核记录', TO_CLOB('
select
教职工年度考核记录.zgh as 教职工号,
教职工年度考核记录.khdw as 考核单位,
教职工年度考核记录.khmc as 考核名称,
教职工年度考核记录.khlb as 考核类别,
教职工年度考核记录.khnf as 考核年份,
教职工年度考核记录.khjl as 考核结论
from abd_tea_jzgndkhjl 教职工年度考核记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgndkhjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgskjl', 'abd_tea_jzgskjl', '教职工授课记录', TO_CLOB('
select
教职工授课记录.zgh as 教职工号,
教职工授课记录.kcmc as 课程名称,
教职工授课记录.kcywmc as 课程英文名称,
教职工授课记录.kclx as 课程类型,
教职工授课记录.kcjb as 课程级别,
教职工授课记录.kclb as 课程类别,
教职工授课记录.kcsx as 课程属性,
教职工授课记录.skfs as 授课方式,
教职工授课记录.skyz as 授课语种,
教职工授课记录.sfxgxk as 是否校公选课,
教职工授课记录.sfsysk as 是否双语授课,
教职工授课记录.sfzjr as 是否主讲人,
教职工授课记录.xnxq as 学年学期,
教职工授课记录.xkzrs as 选课总人数,
教职工授课记录.skxs as 授课学时,
教职工授课记录.kkdwdm as 开课单位代码,
教职工授课记录.kkdw as 开课单位,
教职工授课记录.kcxz as 课程性质,
教职工授课记录.kcfl as 课程分类,
教职工授课记录.kccc as 课程层次,
教职工授课记录.kclbdm as 课程类别代码
from abd_tea_jzgskjl 教职工授课记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgskjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgkchjjl', 'abd_tea_jzgkchjjl', '教职工课程获奖记录', TO_CLOB('
select
教职工课程获奖记录.zgh as 教职工号,
教职工课程获奖记录.kcbm as 课程编码,
教职工课程获奖记录.kcmc as 课程名称,
教职工课程获奖记录.kclx as 课程类型,
教职工课程获奖记录.kcjb as 课程级别,
教职工课程获奖记录.sfzjr as 是否主讲人,
教职工课程获奖记录.jlmc as 奖励名称,
教职工课程获奖记录.jljb as 奖励级别,
教职工课程获奖记录.jldj as 奖励等级,
教职工课程获奖记录.bjdw as 颁奖单位,
教职工课程获奖记录.hjrq as 获奖日期
from abd_tea_jzgkchjjl 教职工课程获奖记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgkchjjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgjxgzl', 'abd_tea_jzgjxgzl', '教职工教学工作量', TO_CLOB('
select
教职工教学工作量.zgh as 教职工号,
教职工教学工作量.xnxq as 学年学期,
教职工教学工作量.gzl as 工作量,
教职工教学工作量.gzllx as 工作量类型
from abd_tea_jzgjxgzl 教职工教学工作量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgjxgzl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgjxpj', 'abd_tea_jzgjxpj', '教职工教学评价', TO_CLOB('
select
教职工教学评价.zgh as 教职工号,
教职工教学评价.xnxq as 学年学期,
教职工教学评价.pjrq as 评教日期,
教职工教学评价.xspjcj as 学生评教成绩,
教职工教学评价.jspjcj as 教师评教成绩,
教职工教学评价.ddpjcj as 督导评教成绩,
教职工教学评价.xypjcj as 学院评教成绩,
教职工教学评价.gcpjcj as 过程评教成绩,
教职工教学评价.zcj as 总成绩,
教职工教学评价.pjdj as 评教等级
from abd_tea_jzgjxpj 教职工教学评价'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgjxpj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgcyjgxm', 'abd_tea_jzgcyjgxm', '教职工参与教改项目', TO_CLOB('
select
教职工参与教改项目.zgh as 教职工号,
教职工参与教改项目.xmbh as 项目编号,
教职工参与教改项目.xmmc as 项目名称,
教职工参与教改项目.xmjb as 项目级别,
教职工参与教改项目.xmlx as 项目类型,
教职工参与教改项目.ssdwbh as 所属单位编号,
教职工参与教改项目.ssdw as 所属单位,
教职工参与教改项目.lxrq as 立项日期,
教职工参与教改项目.ksrq as 开始日期,
教职工参与教改项目.jxrq as 结项日期,
教职工参与教改项目.xmjf as 项目经费,
教职工参与教改项目.xmzt as 项目状态,
教职工参与教改项目.xmly as 项目来源,
教职工参与教改项目.bxcyqk as 本校参与情况,
教职工参与教改项目.brcyqk as 本人参与情况,
教职工参与教改项目.xmjbdm as 项目级别代码
from abd_tea_jzgcyjgxm 教职工参与教改项目'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgcyjgxm'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgjxcghjxx', 'abd_tea_jzgjxcghjxx', '教职工教学成果获奖信息', TO_CLOB('
select
教职工教学成果获奖信息.zgh as 教职工号,
教职工教学成果获奖信息.jxcgbh as 教学成果编号,
教职工教学成果获奖信息.jxcgmc as 教学成果名称,
教职工教学成果获奖信息.brwc as 本人位次,
教职工教学成果获奖信息.jxmc as 奖项名称,
教职工教学成果获奖信息.jljb as 奖励级别,
教职工教学成果获奖信息.jldj as 奖励等级,
教职工教学成果获奖信息.hjrq as 获奖日期,
教职工教学成果获奖信息.xkml as 学科门类,
教职工教学成果获奖信息.yjxk as 一级学科,
教职工教学成果获奖信息.xxsm as 学校署名,
教职工教学成果获奖信息.jljbdm as 奖励级别代码
from abd_tea_jzgjxcghjxx 教职工教学成果获奖信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgjxcghjxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgjxbshjxx', 'abd_tea_jzgjxbshjxx', '教职工教学比赛获奖信息', TO_CLOB('
select
教职工教学比赛获奖信息.zgh as 教职工号,
教职工教学比赛获奖信息.bsmc as 比赛名称,
教职工教学比赛获奖信息.jljb as 奖励级别,
教职工教学比赛获奖信息.jldj as 奖励等级,
教职工教学比赛获奖信息.brpx as 本人排序,
教职工教学比赛获奖信息.hjrq as 获奖日期
from abd_tea_jzgjxbshjxx 教职工教学比赛获奖信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgjxbshjxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgzdxkjs', 'abd_tea_jzgzdxkjs', '教职工指导学科竞赛', TO_CLOB('
select
教职工指导学科竞赛.zgh as 教职工号,
教职工指导学科竞赛.jsmc as 竞赛名称,
教职工指导学科竞赛.jsxk as 竞赛学科,
教职工指导学科竞赛.jsjc as 竞赛届次,
教职工指导学科竞赛.jsjb as 竞赛级别,
教职工指导学科竞赛.zdgzl as 指导工作量,
教职工指导学科竞赛.jljb as 奖励级别,
教职工指导学科竞赛.jldj as 奖励等级,
教职工指导学科竞赛.hjrq as 获奖日期
from abd_tea_jzgzdxkjs 教职工指导学科竞赛'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgzdxkjs'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgzdxsqk', 'abd_tea_jzgzdxsqk', '教职工指导学生情况', TO_CLOB('
select
教职工指导学生情况.zgh as 教职工号,
教职工指导学生情况.xnxq as 学年学期,
教职工指导学生情况.pycc as 培养层次,
教职工指导学生情况.zdxsrs as 指导学生人数
from abd_tea_jzgzdxsqk 教职工指导学生情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgzdxsqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgcykyxm', 'abd_tea_jzgcykyxm', '教职工参与科研项目', TO_CLOB('
select
教职工参与科研项目.zgh as 教职工号,
教职工参与科研项目.xmbh as 项目编号,
教职工参与科研项目.xmmc as 项目名称,
教职工参与科研项目.xmlb as 项目类别,
教职工参与科研项目.xmlx as 项目类型,
教职工参与科研项目.kjxmlb as 科技项目类别,
教职工参与科研项目.xmjb as 项目级别,
教职工参与科研项目.xmly as 项目来源,
教职工参与科研项目.xmlydw as 项目来源单位,
教职工参与科研项目.xmzxzt as 项目执行状态,
教职工参与科研项目.gxl as 贡献率,
教职工参与科研项目.smsx as 署名顺序,
教职工参与科研项目.cyrq as 参与日期,
教职工参与科研项目.cyzt as 参与状态,
教职工参与科研项目.sfxmfzr as 是否项目负责人,
教职工参与科研项目.lxrq as 立项日期,
教职工参与科研项目.jxrq as 结项日期,
教职工参与科研项目.xmid as 项目ID,
教职工参与科研项目.xmjbdm as 项目级别代码
from abd_tea_jzgcykyxm 教职工参与科研项目'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgcykyxm'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgfblw', 'abd_tea_jzgfblw', '教职工发表论文', TO_CLOB('
select
教职工发表论文.zgh as 教职工号,
教职工发表论文.lwbh as 论文编号,
教职工发表论文.lwmc as 论文名称,
教职工发表论文.lwywmc as 论文英文名称,
教职工发表论文.lwlx as 论文类型,
教职工发表论文.sllb as 收录类别,
教职工发表论文.xxsm as 学校署名,
教职工发表论文.lwfblx as 论文发表类型,
教职工发表论文.hymc as 会议名称,
教职工发表论文.hygjdq as 会议国家地区,
教职工发表论文.hydj as 会议等级,
教职工发表论文.kwjb as 刊物级别,
教职工发表论文.kwmc as 刊物名称,
教职工发表论文.qklb as 期刊类别,
教职工发表论文.lwzs as 论文字数,
教职工发表论文.yz as 语种,
教职工发表论文.fbrq as 发表日期,
教职工发表论文.lwrdjb as 论文认定级别,
教职工发表论文.zy as 摘要,
教职工发表论文.gjc as 关键词,
教职工发表论文.smsx as 署名顺序,
教职工发表论文.gxl as 贡献率,
教职工发表论文.sftxzz as 是否通讯作者,
教职工发表论文.sfdyzz as 是否第一作者,
教职工发表论文.slwfq as SCI论文分区
from abd_tea_jzgfblw 教职工发表论文'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgfblw'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgfmzl', 'abd_tea_jzgfmzl', '教职工发明专利', TO_CLOB('
select
教职工发明专利.zgh as 教职工号,
教职工发明专利.zlbh as 专利编号,
教职工发明专利.zlmc as 专利名称,
教职工发明专利.zlsqbh as 专利申请编号,
教职工发明专利.zllx as 专利类型,
教职工发明专利.zlzt as 专利状态,
教职工发明专利.zlsqggh as 专利授权公告号,
教职工发明专利.zlsqggrq as 专利授权公告日期,
教职工发明专利.sfbxzl as 是否本校专利,
教职工发明专利.sfpzl as 是否PCT专利,
教职工发明专利.zlgj as 专利国家,
教职工发明专利.gxl as 贡献率,
教职工发明专利.fmrpm as 发明人排名,
教职工发明专利.sfbxdyfmr as 是否本校第一发明人
from abd_tea_jzgfmzl 教职工发明专利'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgfmzl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgcbzz', 'abd_tea_jzgcbzz', '教职工出版著作', TO_CLOB('
select
教职工出版著作.zgh as 教职工号,
教职工出版著作.zzbh as 著作编号,
教职工出版著作.zzmc as 著作名称,
教职工出版著作.zzywmc as 著作英文名称,
教职工出版著作.cbsmc as 出版社名称,
教职工出版著作.cbsjb as 出版社级别,
教职工出版著作.cbsszgjdq as 出版社所在国家地区,
教职工出版著作.cbrq as 出版日期,
教职工出版著作.zs as 字数,
教职工出版著作.lzlb as 论著类别,
教职工出版著作.xxsm as 学校署名,
教职工出版著作.smsx as 署名顺序,
教职工出版著作.gxl as 贡献率,
教职工出版著作.zxzs as 撰写字数,
教职工出版著作.sfdyzz as 是否第一作者,
教职工出版著作.sfbxdyzz as 是否本校第一作者,
教职工出版著作.cbsjbdm as 出版社级别代码
from abd_tea_jzgcbzz 教职工出版著作'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgcbzz'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgkyhj', 'abd_tea_jzgkyhj', '教职工科研获奖', TO_CLOB('
select
教职工科研获奖.zgh as 教职工号,
教职工科研获奖.hjcglx as 获奖成果类型,
教职工科研获奖.hjcgbh as 获奖成果编号,
教职工科研获奖.hjcgmc as 获奖成果名称,
教职工科研获奖.jlmc as 奖励名称,
教职工科研获奖.hjlb as 获奖类别,
教职工科研获奖.hjjb as 获奖级别,
教职工科研获奖.hjdj as 获奖等级,
教职工科研获奖.hjrq as 获奖日期,
教职工科研获奖.hjje as 获奖金额,
教职工科研获奖.dwpm as 单位排名,
教职工科研获奖.smsx as 署名顺序,
教职工科研获奖.hjjbdm as 获奖级别代码
from abd_tea_jzgkyhj 教职工科研获奖'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgkyhj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgfzxmjfjl', 'abd_tea_jzgfzxmjfjl', '教职工负责项目经费记录', TO_CLOB('
select
教职工负责项目经费记录.zgh as 教职工号,
教职工负责项目经费记录.xmbh as 项目编号,
教职工负责项目经费记录.xmmc as 项目名称,
教职工负责项目经费记录.xmlb as 项目类别,
教职工负责项目经费记录.xmlx as 项目类型,
教职工负责项目经费记录.kjxmlb as 科技项目类别,
教职工负责项目经费记录.xmly as 项目来源,
教职工负责项目经费记录.xmjfze as 项目经费总额,
教职工负责项目经费记录.lxjf as 留校经费,
教职工负责项目经费记录.wbjf as 外拨经费,
教职工负责项目经费记录.dzjf as 到账经费,
教职工负责项目经费记录.dzrq as 到账日期,
教职工负责项目经费记录.xmid as 项目ID
from abd_tea_jzgfzxmjfjl 教职工负责项目经费记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgfzxmjfjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzglgjl', 'abd_tea_jzglgjl', '教职工离岗记录', TO_CLOB('
select
教职工离岗记录.zgh as 教职工号,
教职工离岗记录.lgyy as 离岗原因,
教职工离岗记录.lgrq as 离岗日期,
教职工离岗记录.fgrq as 返岗日期,
教职工离岗记录.sqdwmc as 所去单位名称
from abd_tea_jzglgjl 教职工离岗记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzglgjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzglxjl', 'abd_tea_jzglxjl', '教职工离校记录', TO_CLOB('
select
教职工离校记录.zgh as 教职工号,
教职工离校记录.lxyy as 离校原因,
教职工离校记录.lxrq as 离校日期,
教职工离校记录.lxqx as 离校去向
from abd_tea_jzglxjl 教职工离校记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzglxjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgcgjjl', 'abd_tea_jzgcgjjl', '教职工出国境记录', TO_CLOB('
select
教职工出国境记录.zgh as 教职工号,
教职工出国境记录.cgjgb as 出国境国别,
教职工出国境记录.sqdwywmc as 所去单位英文名称,
教职工出国境记录.sqdwzwmc as 所去单位中文名称,
教职工出国境记录.tzmc as 团组名称,
教职工出国境记录.jfly as 经费来源,
教职工出国境记录.spdw as 审批单位,
教职工出国境记录.sprq as 审批日期,
教职工出国境记录.spwh as 审批文号,
教职工出国境记录.cgjmd as 出国境目的,
教职工出国境记录.xxgznr as 学习工作内容,
教职工出国境记录.hzhhtxzh as 护照号或通行证号,
教职工出国境记录.cgjrq as 出国境日期,
教职工出国境记录.hgrq as 回国日期,
教职工出国境记录.xxgzcj as 学习工作成绩,
教职工出国境记录.xxcdfy as 学校承担费用
from abd_tea_jzgcgjjl 教职工出国境记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgcgjjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgltxjl', 'abd_tea_jzgltxjl', '教职工离退休记录', TO_CLOB('
select
教职工离退休记录.zgh as 教职工号,
教职工离退休记录.ltlb as 离退类别,
教职工离退休记录.ltrq as 离退日期,
教职工离退休记录.lthxsjb as 离退后享受级别,
教职工离退休记录.lthgldw as 离退后管理单位,
教职工离退休记录.ltxfzfdw as 离退休费支付单位,
教职工离退休记录.ydazdd as 异地安置地点
from abd_tea_jzgltxjl 教职工离退休记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgltxjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgqsjl', 'abd_tea_jzgqsjl', '教职工去世记录', TO_CLOB('
select
教职工去世记录.zgh as 教职工号,
教职工去世记录.qsrq as 去世日期,
教职工去世记录.qsdd as 去世地点,
教职工去世记录.qsyy as 去世原因,
教职工去世记录.qslb as 去世类别,
教职工去世记录.qslx as 去世类型,
教职工去世记录.szbzj as 丧葬补助金,
教职工去世记录.ycxfxj as 一次性抚恤金
from abd_tea_jzgqsjl 教职工去世记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgqsjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgzpxx', 'abd_tea_jzgzpxx', '教职工照片信息', TO_CLOB('
select
教职工照片信息.zgh as 教职工号,
教职工照片信息.zplx as 照片类型,
教职工照片信息.zpwj as 照片文件
from abd_tea_jzgzpxx 教职工照片信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgzpxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgfpjl', 'abd_tea_jzgfpjl', '教职工返聘记录', TO_CLOB('
select
教职工返聘记录.zgh as 教职工号,
教职工返聘记录.fpdwbm as 返聘单位编码,
教职工返聘记录.fpdw as 返聘单位,
教职工返聘记录.fpqsrq as 返聘起始日期,
教职工返聘记录.fpzzrq as 返聘终止日期,
教职工返聘记录.gwmc as 岗位名称,
教职工返聘记录.fpcj as 返聘酬金,
教职工返聘记录.fpjly as 返聘金来源
from abd_tea_jzgfpjl 教职工返聘记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgfpjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgzdcxcyxmqk', 'abd_tea_jzgzdcxcyxmqk', '教职工指导创新创业项目情况', TO_CLOB('
select
教职工指导创新创业项目情况.zgh as 教职工号,
教职工指导创新创业项目情况.xh as 学号,
教职工指导创新创业项目情况.xsxm as 学生姓名,
教职工指导创新创业项目情况.pycc as 培养层次,
教职工指导创新创业项目情况.xmmc as 项目名称,
教职工指导创新创业项目情况.xmlx as 项目类型,
教职工指导创新创业项目情况.xmjb as 项目级别,
教职工指导创新创业项目情况.hjdj as 获奖等级,
教职工指导创新创业项目情况.xn as 学年
from abd_tea_jzgzdcxcyxmqk 教职工指导创新创业项目情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgzdcxcyxmqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgzdbylwsjqk', 'abd_tea_jzgzdbylwsjqk', '教职工指导毕业论文（设计）情况', TO_CLOB('
select
教职工指导毕业论文设计情况.zgh as 教职工号,
教职工指导毕业论文设计情况.xh as 学号,
教职工指导毕业论文设计情况.xsxm as 学生姓名,
教职工指导毕业论文设计情况.pycc as 培养层次,
教职工指导毕业论文设计情况.lwmc as 论文名称,
教职工指导毕业论文设计情况.sftg as 是否通过,
教职工指导毕业论文设计情况.nf as 年份
from abd_tea_jzgzdbylwsjqk 教职工指导毕业论文设计情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgzdbylwsjqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgzdxssxsjqk', 'abd_tea_jzgzdxssxsjqk', '教职工指导学生实习实践情况', TO_CLOB('
select
教职工指导学生实习实践情况.zgh as 教职工号,
教职工指导学生实习实践情况.xh as 学号,
教职工指导学生实习实践情况.xsxm as 学生姓名,
教职工指导学生实习实践情况.pycc as 培养层次,
教职工指导学生实习实践情况.qyjd as 企业基地,
教职工指导学生实习实践情况.ksrq as 开始日期,
教职工指导学生实习实践情况.jsrq as 结束日期
from abd_tea_jzgzdxssxsjqk 教职工指导学生实习实践情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgzdxssxsjqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgzzbgcnjl', 'abd_tea_jzgzzbgcnjl', '教职工资政报告采纳记录', TO_CLOB('
select
教职工资政报告采纳记录.zgh as 教职工号,
教职工资政报告采纳记录.yjbgbm as 研究报告编码,
教职工资政报告采纳记录.yjbgmc as 研究报告名称,
教职工资政报告采纳记录.cndx as 采纳对象,
教职工资政报告采纳记录.psjbdm as 批示级别代码,
教职工资政报告采纳记录.psjb as 批示级别,
教职工资政报告采纳记录.tjrq as 提交日期,
教职工资政报告采纳记录.psrq as 批示日期,
教职工资政报告采纳记录.smsx as 署名顺序
from abd_tea_jzgzzbgcnjl 教职工资政报告采纳记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgzzbgcnjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgfbjsjrjzzq', 'abd_tea_jzgfbjsjrjzzq', '教职工发表计算机软件著作权', TO_CLOB('
select
教职工发表计算机软件著作权.zgh as 教职工号,
教职工发表计算机软件著作权.rjzzmc as 软件著作名称,
教职工发表计算机软件著作权.zsh as 证书号,
教职工发表计算机软件著作权.djh as 登记号,
教职工发表计算机软件著作权.zzqr as 著作权人,
教职工发表计算机软件著作权.smsx as 署名顺序,
教职工发表计算机软件著作权.scfbrq as 首次发表日期,
教职工发表计算机软件著作权.zsqdrq as 证书取得日期,
教职工发表计算机软件著作权.zsbfjg as 证书颁发机构,
教职工发表计算机软件著作权.qlqdfs as 权利取得方式
from abd_tea_jzgfbjsjrjzzq 教职工发表计算机软件著作权'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgfbjsjrjzzq'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgkygzl', 'abd_tea_jzgkygzl', '教职工科研工作量', TO_CLOB('
select
教职工科研工作量.zgh as 教职工号,
教职工科研工作量.tjnf as 统计年份,
教职工科研工作量.gzl as 工作量
from abd_tea_jzgkygzl 教职工科研工作量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgkygzl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_nr_0001', 'tea_nr_0001', '每年博士学位专任教师比例', TO_CLOB('
select
每年博士学位专任教师比例.xxbsm as 学校标识码,
每年博士学位专任教师比例.tjnf as 统计年份,
每年博士学位专任教师比例.bsxwzrjsbl as 博士学位专任教师比例
from tea_nr_0001 每年博士学位专任教师比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_nr_0001'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_nr_0002', 'tea_nr_0002', '每年专任教师生师比', TO_CLOB('
select
每年专任教师生师比.xxbsm as 学校标识码,
每年专任教师生师比.tjnf as 统计年份,
每年专任教师生师比.xxlb as 学校类别,
每年专任教师生师比.xxxz as 学校性质,
每年专任教师生师比.zrjsssb as 专任教师生师比
from tea_nr_0002 每年专任教师生师比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_nr_0002'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_nr_0003', 'tea_nr_0003', 'QS高校排名', TO_CLOB('
select
QS高校排名.xxmc as 学校名称,
QS高校排名.nf as 年份,
QS高校排名.xxpm as 学校排名
from tea_nr_0003 QS高校排名'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_nr_0003'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_nr_0004', 'tea_nr_0004', '全国重点高校名单', TO_CLOB('
select
全国重点高校名单.xxmc as 学校名称,
全国重点高校名单.nf as 年份,
全国重点高校名单.sf985 as 是否985,
全国重点高校名单.sf211 as 是否211
from tea_nr_0004 全国重点高校名单'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_nr_0004'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0001', 'tea_si_col_0001', '每年各学院专任教师人数', TO_CLOB('
select
每年各学院专任教师人数.xybh as 学院编号,
每年各学院专任教师人数.xy as 学院,
每年各学院专任教师人数.tjnf as 统计年份,
每年各学院专任教师人数.zrjss as 专任教师数
from tea_si_col_0001 每年各学院专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0001'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0002', 'tea_si_col_0002', '每年各学院外籍专任教师人数', TO_CLOB('
select
每年各学院外籍专任教师人数.xybh as 学院编号,
每年各学院外籍专任教师人数.xy as 学院,
每年各学院外籍专任教师人数.tjnf as 统计年份,
每年各学院外籍专任教师人数.wjzrjss as 外籍专任教师数
from tea_si_col_0002 每年各学院外籍专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0002'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0003', 'tea_si_col_0003', '每年各学院外籍专任教师比例', TO_CLOB('
select
每年各学院外籍专任教师比例.xybh as 学院编号,
每年各学院外籍专任教师比例.xy as 学院,
每年各学院外籍专任教师比例.tjnf as 统计年份,
每年各学院外籍专任教师比例.wjzrjsbl as 外籍专任教师比例
from tea_si_col_0003 每年各学院外籍专任教师比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0003'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0004', 'tea_si_col_0004', '每年各学院具有博士学位专任教师人数', TO_CLOB('
select
每年各学院具有博士学位专任教师人数.xybh as 学院编号,
每年各学院具有博士学位专任教师人数.xy as 学院,
每年各学院具有博士学位专任教师人数.tjnf as 统计年份,
每年各学院具有博士学位专任教师人数.bsxwzrjss as 博士学位专任教师数
from tea_si_col_0004 每年各学院具有博士学位专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0004'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0005', 'tea_si_col_0005', '每年各学院博士学位专任教师比例', TO_CLOB('
select
每年各学院博士学位专任教师比例.xybh as 学院编号,
每年各学院博士学位专任教师比例.xy as 学院,
每年各学院博士学位专任教师比例.tjnf as 统计年份,
每年各学院博士学位专任教师比例.bsxwzrjsbl as 博士学位专任教师比例
from tea_si_col_0005 每年各学院博士学位专任教师比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0005'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0006', 'tea_si_col_0006', '每年各系专任教师人数', TO_CLOB('
select
每年各系专任教师人数.xybh as 学院编号,
每年各系专任教师人数.xy as 学院,
每年各系专任教师人数.ssxbh as 所属系编号,
每年各系专任教师人数.ssx as 所属系,
每年各系专任教师人数.tjnf as 统计年份,
每年各系专任教师人数.zrjss as 专任教师数
from tea_si_col_0006 每年各系专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0006'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0007', 'tea_si_col_0007', '每年各系外籍专任教师人数', TO_CLOB('
select
每年各系外籍专任教师人数.xybh as 学院编号,
每年各系外籍专任教师人数.xy as 学院,
每年各系外籍专任教师人数.ssxbh as 所属系编号,
每年各系外籍专任教师人数.ssx as 所属系,
每年各系外籍专任教师人数.tjnf as 统计年份,
每年各系外籍专任教师人数.wjzrjss as 外籍专任教师数
from tea_si_col_0007 每年各系外籍专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0007'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0008', 'tea_si_col_0008', '每年各系具有博士学位专任教师人数', TO_CLOB('
select
每年各系具有博士学位专任教师人数.xybh as 学院编号,
每年各系具有博士学位专任教师人数.xy as 学院,
每年各系具有博士学位专任教师人数.ssxbh as 所属系编号,
每年各系具有博士学位专任教师人数.ssx as 所属系,
每年各系具有博士学位专任教师人数.tjnf as 统计年份,
每年各系具有博士学位专任教师人数.bsxwzrjss as 博士学位专任教师数
from tea_si_col_0008 每年各系具有博士学位专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0008'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0009', 'tea_si_col_0009', '每年各单位专任教师生师比', TO_CLOB('
select
每年各单位专任教师生师比.xybh as 学院编号,
每年各单位专任教师生师比.xy as 学院,
每年各单位专任教师生师比.tjnf as 统计年份,
每年各单位专任教师生师比.ssb as 生师比
from tea_si_col_0009 每年各单位专任教师生师比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0009'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0010', 'tea_si_col_0010', '每年学校各学院各性别专任教师人数', TO_CLOB('
select
每年学校各学院各性别专任教师人数.xybh as 学院编号,
每年学校各学院各性别专任教师人数.xy as 学院,
每年学校各学院各性别专任教师人数.xb as 性别,
每年学校各学院各性别专任教师人数.tjnf as 统计年份,
每年学校各学院各性别专任教师人数.zrjss as 专任教师数
from tea_si_col_0010 每年学校各学院各性别专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0010'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0011', 'tea_si_col_0011', '每年学校各学院各年龄段专任教师人数', TO_CLOB('
select
每年学校各学院各年龄段专任教师人数.xybh as 学院编号,
每年学校各学院各年龄段专任教师人数.xy as 学院,
每年学校各学院各年龄段专任教师人数.nld as 年龄段,
每年学校各学院各年龄段专任教师人数.tjnf as 统计年份,
每年学校各学院各年龄段专任教师人数.zrjss as 专任教师数
from tea_si_col_0011 每年学校各学院各年龄段专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0011'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0012', 'tea_si_col_0012', '每年学校各学院各学缘专任教师人数', TO_CLOB('
select
每年学校各学院各学缘专任教师人数.xybh as 学院编号,
每年学校各学院各学缘专任教师人数.xy as 学院,
每年学校各学院各学缘专任教师人数.sfbxby as 是否本校毕业,
每年学校各学院各学缘专任教师人数.tjnf as 统计年份,
每年学校各学院各学缘专任教师人数.zrjss as 专任教师数
from tea_si_col_0012 每年学校各学院各学缘专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0012'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0013', 'tea_si_col_0013', '每年学校各学院各职称专任教师人数', TO_CLOB('
select
每年学校各学院各职称专任教师人数.xybh as 学院编号,
每年学校各学院各职称专任教师人数.xy as 学院,
每年学校各学院各职称专任教师人数.zc as 职称,
每年学校各学院各职称专任教师人数.tjnf as 统计年份,
每年学校各学院各职称专任教师人数.zrjss as 专任教师数
from tea_si_col_0013 每年学校各学院各职称专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0013'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0014', 'tea_si_col_0014', '每年学校各学院各职称各学位专任教师人数', TO_CLOB('
select
每年学校各学院各职称各学位专任教师人数.xybh as 学院编号,
每年学校各学院各职称各学位专任教师人数.zc as 职称,
每年学校各学院各职称各学位专任教师人数.xw as 学位,
每年学校各学院各职称各学位专任教师人数.xy as 学院,
每年学校各学院各职称各学位专任教师人数.tjnf as 统计年份,
每年学校各学院各职称各学位专任教师人数.zrjss as 专任教师数
from tea_si_col_0014 每年学校各学院各职称各学位专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0014'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0015', 'tea_si_col_0015', '每年学校各学院各职称各学缘专任教师人数', TO_CLOB('
select
每年学校各学院各职称各学缘专任教师人数.xybh as 学院编号,
每年学校各学院各职称各学缘专任教师人数.zc as 职称,
每年学校各学院各职称各学缘专任教师人数.sfbxby as 是否本校毕业,
每年学校各学院各职称各学缘专任教师人数.tjnf as 统计年份,
每年学校各学院各职称各学缘专任教师人数.xy as 学院,
每年学校各学院各职称各学缘专任教师人数.zrjss as 专任教师数
from tea_si_col_0015 每年学校各学院各职称各学缘专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0015'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0016', 'tea_si_col_0016', '每年学校各学院各职称各年龄段专任教师人数', TO_CLOB('
select
每年学校各学院各职称各年龄段专任教师人数.xybh as 学院编号,
每年学校各学院各职称各年龄段专任教师人数.zc as 职称,
每年学校各学院各职称各年龄段专任教师人数.xy as 学院,
每年学校各学院各职称各年龄段专任教师人数.nld as 年龄段,
每年学校各学院各职称各年龄段专任教师人数.tjnf as 统计年份,
每年学校各学院各职称各年龄段专任教师人数.zrjss as 专任教师数
from tea_si_col_0016 每年学校各学院各职称各年龄段专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0016'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0017', 'tea_si_col_0017', '每年学校各学院各学位专任教师人数', TO_CLOB('
select
每年学校各学院各学位专任教师人数.xybh as 学院编号,
每年学校各学院各学位专任教师人数.xy as 学院,
每年学校各学院各学位专任教师人数.zgxw as 最高学位,
每年学校各学院各学位专任教师人数.tjnf as 统计年份,
每年学校各学院各学位专任教师人数.zrjss as 专任教师数
from tea_si_col_0017 每年学校各学院各学位专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0017'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0018', 'tea_si_col_0018', '每年学校各学院有无海外经历专任教师人数', TO_CLOB('
select
每年学校各学院有无海外经历专任教师人数.xybh as 学院编号,
每年学校各学院有无海外经历专任教师人数.xy as 学院,
每年学校各学院有无海外经历专任教师人数.sfyhwjl as 是否有海外经历,
每年学校各学院有无海外经历专任教师人数.tjnf as 统计年份,
每年学校各学院有无海外经历专任教师人数.zrjss as 专任教师数
from tea_si_col_0018 每年学校各学院有无海外经历专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0018'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0019', 'tea_si_col_0019', '每年学校各学院各海外经历专任教师人数', TO_CLOB('
select
每年学校各学院各海外经历专任教师人数.xybh as 学院编号,
每年学校各学院各海外经历专任教师人数.xy as 学院,
每年学校各学院各海外经历专任教师人数.hwjl as 海外经历,
每年学校各学院各海外经历专任教师人数.tjnf as 统计年份,
每年学校各学院各海外经历专任教师人数.zrjss as 专任教师数
from tea_si_col_0019 每年学校各学院各海外经历专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0019'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0020', 'tea_si_col_0020', '每年各系各性别专任教师人数', TO_CLOB('
select
每年各系各性别专任教师人数.xybh as 学院编号,
每年各系各性别专任教师人数.xy as 学院,
每年各系各性别专任教师人数.ssxbh as 所属系编号,
每年各系各性别专任教师人数.ssx as 所属系,
每年各系各性别专任教师人数.xb as 性别,
每年各系各性别专任教师人数.tjnf as 统计年份,
每年各系各性别专任教师人数.zrjss as 专任教师数
from tea_si_col_0020 每年各系各性别专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0020'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0021', 'tea_si_col_0021', '每年各系各年龄段专任教师人数', TO_CLOB('
select
每年各系各年龄段专任教师人数.xybh as 学院编号,
每年各系各年龄段专任教师人数.xy as 学院,
每年各系各年龄段专任教师人数.ssxbh as 所属系编号,
每年各系各年龄段专任教师人数.ssx as 所属系,
每年各系各年龄段专任教师人数.nld as 年龄段,
每年各系各年龄段专任教师人数.tjnf as 统计年份,
每年各系各年龄段专任教师人数.zrjss as 专任教师数
from tea_si_col_0021 每年各系各年龄段专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0021'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0022', 'tea_si_col_0022', '每年各系各学缘专任教师人数', TO_CLOB('
select
每年各系各学缘专任教师人数.xybh as 学院编号,
每年各系各学缘专任教师人数.xy as 学院,
每年各系各学缘专任教师人数.ssxbh as 所属系编号,
每年各系各学缘专任教师人数.ssx as 所属系,
每年各系各学缘专任教师人数.sfbxby as 是否本校毕业,
每年各系各学缘专任教师人数.tjnf as 统计年份,
每年各系各学缘专任教师人数.zrjss as 专任教师数
from tea_si_col_0022 每年各系各学缘专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0022'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0023', 'tea_si_col_0023', '每年各系各职称专任教师人数', TO_CLOB('
select
每年各系各职称专任教师人数.xybh as 学院编号,
每年各系各职称专任教师人数.xy as 学院,
每年各系各职称专任教师人数.ssxbh as 所属系编号,
每年各系各职称专任教师人数.ssx as 所属系,
每年各系各职称专任教师人数.zyjszw as 专业技术职务,
每年各系各职称专任教师人数.tjnf as 统计年份,
每年各系各职称专任教师人数.zrjss as 专任教师数
from tea_si_col_0023 每年各系各职称专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0023'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0024', 'tea_si_col_0024', '每年各系各学位专任教师人数', TO_CLOB('
select
每年各系各学位专任教师人数.xybh as 学院编号,
每年各系各学位专任教师人数.xy as 学院,
每年各系各学位专任教师人数.ssxbh as 所属系编号,
每年各系各学位专任教师人数.ssx as 所属系,
每年各系各学位专任教师人数.zgxw as 最高学位,
每年各系各学位专任教师人数.tjnf as 统计年份,
每年各系各学位专任教师人数.zrjss as 专任教师数
from tea_si_col_0024 每年各系各学位专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0024'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0025', 'tea_si_col_0025', '每年各系有无海外经历专任教师人数', TO_CLOB('
select
每年各系有无海外经历专任教师人数.xybh as 学院编号,
每年各系有无海外经历专任教师人数.xy as 学院,
每年各系有无海外经历专任教师人数.ssxbh as 所属系编号,
每年各系有无海外经历专任教师人数.ssx as 所属系,
每年各系有无海外经历专任教师人数.sfyhwjl as 是否有海外经历,
每年各系有无海外经历专任教师人数.tjnf as 统计年份,
每年各系有无海外经历专任教师人数.zrjss as 专任教师数
from tea_si_col_0025 每年各系有无海外经历专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0025'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0026', 'tea_si_col_0026', '每年各系各海外经历专任教师人数', TO_CLOB('
select
每年各系各海外经历专任教师人数.xybh as 学院编号,
每年各系各海外经历专任教师人数.xy as 学院,
每年各系各海外经历专任教师人数.ssxbh as 所属系编号,
每年各系各海外经历专任教师人数.ssx as 所属系,
每年各系各海外经历专任教师人数.hwjllx as 海外经历类型,
每年各系各海外经历专任教师人数.tjnf as 统计年份,
每年各系各海外经历专任教师人数.zrjss as 专任教师数
from tea_si_col_0026 每年各系各海外经历专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0026'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0027', 'tea_si_col_0027', '每年学校各学院各职称教师的平均年龄', TO_CLOB('
select
每年学校各学院各职称教师的平均年龄.xybh as 学院编号,
每年学校各学院各职称教师的平均年龄.xy as 学院,
每年学校各学院各职称教师的平均年龄.zc as 职称,
每年学校各学院各职称教师的平均年龄.tjnf as 统计年份,
每年学校各学院各职称教师的平均年龄.pjnl as 平均年龄
from tea_si_col_0027 每年学校各学院各职称教师的平均年龄'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0027'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0028', 'tea_si_col_0028', '每年学校各学院预计退休的专任教师人数', TO_CLOB('
select
每年学校各学院预计退休的专任教师人数.xybh as 学院编号,
每年学校各学院预计退休的专任教师人数.xy as 学院,
每年学校各学院预计退休的专任教师人数.yjtxnf as 预计退休年份,
每年学校各学院预计退休的专任教师人数.tjnf as 统计年份,
每年学校各学院预计退休的专任教师人数.zrjss as 专任教师数
from tea_si_col_0028 每年学校各学院预计退休的专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0028'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0029', 'tea_si_col_0029', '每年学校各学院预计退休的各职称专任教师人数', TO_CLOB('
select
每年学校各学院预计退休的各职称专任教师人数.xybh as 学院编号,
每年学校各学院预计退休的各职称专任教师人数.xy as 学院,
每年学校各学院预计退休的各职称专任教师人数.zc as 职称,
每年学校各学院预计退休的各职称专任教师人数.yjtxnf as 预计退休年份,
每年学校各学院预计退休的各职称专任教师人数.tjnf as 统计年份,
每年学校各学院预计退休的各职称专任教师人数.zrjss as 专任教师数
from tea_si_col_0029 每年学校各学院预计退休的各职称专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0029'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0030', 'tea_si_col_0030', '每年学校各系预计退休的专任教师人数', TO_CLOB('
select
每年学校各系预计退休的专任教师人数.xybh as 学院编号,
每年学校各系预计退休的专任教师人数.xy as 学院,
每年学校各系预计退休的专任教师人数.ssxbh as 所属系编号,
每年学校各系预计退休的专任教师人数.ssx as 所属系,
每年学校各系预计退休的专任教师人数.yjtxnf as 预计退休年份,
每年学校各系预计退休的专任教师人数.tjnf as 统计年份,
每年学校各系预计退休的专任教师人数.zrjss as 专任教师数
from tea_si_col_0030 每年学校各系预计退休的专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0030'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0031', 'tea_si_col_0031', '每年学校各系预计退休的各职称专任教师人数', TO_CLOB('
select
每年学校各系预计退休的各职称专任教师人数.xybh as 学院编号,
每年学校各系预计退休的各职称专任教师人数.xy as 学院,
每年学校各系预计退休的各职称专任教师人数.ssxbh as 所属系编号,
每年学校各系预计退休的各职称专任教师人数.ssx as 所属系,
每年学校各系预计退休的各职称专任教师人数.zc as 职称,
每年学校各系预计退休的各职称专任教师人数.yjtxnf as 预计退休年份,
每年学校各系预计退休的各职称专任教师人数.tjnf as 统计年份,
每年学校各系预计退休的各职称专任教师人数.zrjss as 专任教师数
from tea_si_col_0031 每年学校各系预计退休的各职称专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0031'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0032', 'tea_si_col_0032', '每年各学院各人才类别人数', TO_CLOB('
select
每年各学院各人才类别人数.xybh as 学院编号,
每年各学院各人才类别人数.xy as 学院,
每年各学院各人才类别人数.rclb as 人才类别,
每年各学院各人才类别人数.tjnf as 统计年份,
每年各学院各人才类别人数.rcsl as 人才数量
from tea_si_col_0032 每年各学院各人才类别人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0032'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0033', 'tea_si_col_0033', '每年各学院各人才类别学科匹配、学科交叉人数', TO_CLOB('
select
每年各学院各人才类别学科匹配学科交叉人数.xybh as 学院编号,
每年各学院各人才类别学科匹配学科交叉人数.xy as 学院,
每年各学院各人才类别学科匹配学科交叉人数.rclb as 人才类别,
每年各学院各人才类别学科匹配学科交叉人数.xkppx as 学科匹配性,
每年各学院各人才类别学科匹配学科交叉人数.tjnf as 统计年份,
每年各学院各人才类别学科匹配学科交叉人数.rcsl as 人才数量
from tea_si_col_0033 每年各学院各人才类别学科匹配学科交叉人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0033'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0034', 'tea_si_col_0034', '各学院各类科研成果的总数', TO_CLOB('
select
各学院各类科研成果的总数.xybh as 学院编号,
各学院各类科研成果的总数.kycglx as 科研成果类型,
各学院各类科研成果的总数.xy as 学院,
各学院各类科研成果的总数.tjnf as 统计年份,
各学院各类科研成果的总数.cgsl as 成果数量
from tea_si_col_0034 各学院各类科研成果的总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0034'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0035', 'tea_si_col_0035', '各学院各刊物级别论文发表情况', TO_CLOB('
select
各学院各刊物级别论文发表情况.xybh as 学院编号,
各学院各刊物级别论文发表情况.kwjb as 刊物级别,
各学院各刊物级别论文发表情况.xy as 学院,
各学院各刊物级别论文发表情况.tjrq as 统计日期,
各学院各刊物级别论文发表情况.lwsl as 论文数量
from tea_si_col_0035 各学院各刊物级别论文发表情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0035'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0036', 'tea_si_col_0036', '每年各学院科研成果转化合同数', TO_CLOB('
select
每年各学院科研成果转化合同数.xybh as 学院编号,
每年各学院科研成果转化合同数.xy as 学院,
每年各学院科研成果转化合同数.tjnf as 统计年份,
每年各学院科研成果转化合同数.hts as 合同数
from tea_si_col_0036 每年各学院科研成果转化合同数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0036'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0037', 'tea_si_col_0037', '每年各学院科研成果转化合同额', TO_CLOB('
select
每年各学院科研成果转化合同额.xybh as 学院编号,
每年各学院科研成果转化合同额.xy as 学院,
每年各学院科研成果转化合同额.tjnf as 统计年份,
每年各学院科研成果转化合同额.hte as 合同额
from tea_si_col_0037 每年各学院科研成果转化合同额'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0037'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0038', 'tea_si_col_0038', '每年各学院科研成果转化率', TO_CLOB('
select
每年各学院科研成果转化率.xybh as 学院编号,
每年各学院科研成果转化率.xy as 学院,
每年各学院科研成果转化率.tjnf as 统计年份,
每年各学院科研成果转化率.zhl as 转化率
from tea_si_col_0038 每年各学院科研成果转化率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0038'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0039', 'tea_si_col_0039', '各学院的横纵向科研项目数', TO_CLOB('
select
各学院的横纵向科研项目数.xybh as 学院编号,
各学院的横纵向科研项目数.xy as 学院,
各学院的横纵向科研项目数.xmlb as 项目类别,
各学院的横纵向科研项目数.tjrq as 统计日期,
各学院的横纵向科研项目数.xmsl as 项目数量
from tea_si_col_0039 各学院的横纵向科研项目数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0039'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0040', 'tea_si_col_0040', '各学院的科研经费总额', TO_CLOB('
select
各学院的科研经费总额.xybh as 学院编号,
各学院的科研经费总额.xy as 学院,
各学院的科研经费总额.tjrq as 统计日期,
各学院的科研经费总额.xmze as 项目总额
from tea_si_col_0040 各学院的科研经费总额'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0040'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0041', 'tea_si_col_0041', '各学院各来源科研项目数', TO_CLOB('
select
各学院各来源科研项目数.xybh as 学院编号,
各学院各来源科研项目数.xmly as 项目来源,
各学院各来源科研项目数.xy as 学院,
各学院各来源科研项目数.tjrq as 统计日期,
各学院各来源科研项目数.xmsl as 项目数量
from tea_si_col_0041 各学院各来源科研项目数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0041'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0042', 'tea_si_col_0042', '每年各学院各级别科研项目数', TO_CLOB('
select
每年各学院各级别科研项目数.xybh as 学院编号,
每年各学院各级别科研项目数.xmjb as 项目级别,
每年各学院各级别科研项目数.tjnf as 统计年份,
每年各学院各级别科研项目数.xy as 学院,
每年各学院各级别科研项目数.xmsl as 项目数量
from tea_si_col_0042 每年各学院各级别科研项目数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0042'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0043', 'tea_si_col_0043', '各学院各级别科研奖项数', TO_CLOB('
select
各学院各级别科研奖项数.xybh as 学院编号,
各学院各级别科研奖项数.jxjb as 奖项级别,
各学院各级别科研奖项数.xy as 学院,
各学院各级别科研奖项数.tjrq as 统计日期,
各学院各级别科研奖项数.jxsl as 奖项数量
from tea_si_col_0043 各学院各级别科研奖项数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0043'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0044', 'tea_si_col_0044', '每年各学院各级别科研奖项数', TO_CLOB('
select
每年各学院各级别科研奖项数.xybh as 学院编号,
每年各学院各级别科研奖项数.jxjb as 奖项级别,
每年各学院各级别科研奖项数.tjnf as 统计年份,
每年各学院各级别科研奖项数.xy as 学院,
每年各学院各级别科研奖项数.jxsl as 奖项数量
from tea_si_col_0044 每年各学院各级别科研奖项数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0044'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0045', 'tea_si_col_0045', '各学院各类科研成果获奖情况', TO_CLOB('
select
各学院各类科研成果获奖情况.xybh as 学院编号,
各学院各类科研成果获奖情况.kycglx as 科研成果类型,
各学院各类科研成果获奖情况.xy as 学院,
各学院各类科研成果获奖情况.tjrq as 统计日期,
各学院各类科研成果获奖情况.jxsl as 奖项数量
from tea_si_col_0045 各学院各类科研成果获奖情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0045'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0046', 'tea_si_col_0046', '每年各学院人均教学工作量', TO_CLOB('
select
每年各学院人均教学工作量.xybh as 学院编号,
每年各学院人均教学工作量.xy as 学院,
每年各学院人均教学工作量.tjnf as 统计年份,
每年各学院人均教学工作量.jxgzl as 教学工作量
from tea_si_col_0046 每年各学院人均教学工作量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0046'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0047', 'tea_si_col_0047', '每年各学院各职称人均教学工作量', TO_CLOB('
select
每年各学院各职称人均教学工作量.xybh as 学院编号,
每年各学院各职称人均教学工作量.zc as 职称,
每年各学院各职称人均教学工作量.xy as 学院,
每年各学院各职称人均教学工作量.tjnf as 统计年份,
每年各学院各职称人均教学工作量.jxgzl as 教学工作量
from tea_si_col_0047 每年各学院各职称人均教学工作量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0047'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0048', 'tea_si_col_0048', '每年各学院各学位人均教学工作量', TO_CLOB('
select
每年各学院各学位人均教学工作量.xybh as 学院编号,
每年各学院各学位人均教学工作量.xw as 学位,
每年各学院各学位人均教学工作量.xy as 学院,
每年各学院各学位人均教学工作量.tjnf as 统计年份,
每年各学院各学位人均教学工作量.jxgzl as 教学工作量
from tea_si_col_0048 每年各学院各学位人均教学工作量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0048'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0049', 'tea_si_col_0049', '每年各学院人均授课数', TO_CLOB('
select
每年各学院人均授课数.xybh as 学院编号,
每年各学院人均授课数.xy as 学院,
每年各学院人均授课数.tjnf as 统计年份,
每年各学院人均授课数.skms as 授课门数
from tea_si_col_0049 每年各学院人均授课数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0049'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0050', 'tea_si_col_0050', '每年各学院人均授课学时', TO_CLOB('
select
每年各学院人均授课学时.xybh as 学院编号,
每年各学院人均授课学时.xy as 学院,
每年各学院人均授课学时.tjnf as 统计年份,
每年各学院人均授课学时.skxs as 授课学时
from tea_si_col_0050 每年各学院人均授课学时'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0050'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0051', 'tea_si_col_0051', '各学院各职称教师指导各类型学生数', TO_CLOB('
select
各学院各职称教师指导各类型学生数.xybh as 学院编号,
各学院各职称教师指导各类型学生数.xy as 学院,
各学院各职称教师指导各类型学生数.zc as 职称,
各学院各职称教师指导各类型学生数.xslx as 学生类型,
各学院各职称教师指导各类型学生数.tjrq as 统计日期,
各学院各职称教师指导各类型学生数.xss as 学生数
from tea_si_col_0051 各学院各职称教师指导各类型学生数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0051'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0052', 'tea_si_col_0052', '每年各学院各职称教师的评教均分', TO_CLOB('
select
每年各学院各职称教师的评教均分.xybh as 学院编号,
每年各学院各职称教师的评教均分.zc as 职称,
每年各学院各职称教师的评教均分.xy as 学院,
每年各学院各职称教师的评教均分.tjnf as 统计年份,
每年各学院各职称教师的评教均分.pjjf as 评教均分
from tea_si_col_0052 每年各学院各职称教师的评教均分'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0052'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0053', 'tea_si_col_0053', '每年各学院教改项目总数', TO_CLOB('
select
每年各学院教改项目总数.xybh as 学院编号,
每年各学院教改项目总数.xy as 学院,
每年各学院教改项目总数.tjnf as 统计年份,
每年各学院教改项目总数.xmsl as 项目数量
from tea_si_col_0053 每年各学院教改项目总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0053'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_col_0054', 'tea_si_col_0054', '各学院各级别各等级教学成果获奖情况', TO_CLOB('
select
各学院各级别各等级教学成果获奖情况.xybh as 学院编号,
各学院各级别各等级教学成果获奖情况.jljb as 奖励级别,
各学院各级别各等级教学成果获奖情况.jldj as 奖励等级,
各学院各级别各等级教学成果获奖情况.xy as 学院,
各学院各级别各等级教学成果获奖情况.tjrq as 统计日期,
各学院各级别各等级教学成果获奖情况.jxsl as 奖项数量
from tea_si_col_0054 各学院各级别各等级教学成果获奖情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_col_0054'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0001', 'tea_si_sch_0001', '每年学校专任教师人数', TO_CLOB('
select
每年学校专任教师人数.xxbsm as 学校标识码,
每年学校专任教师人数.tjnf as 统计年份,
每年学校专任教师人数.zrjss as 专任教师数
from tea_si_sch_0001 每年学校专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0001'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0002', 'tea_si_sch_0002', '每年学校各学科属性专任教师人数', TO_CLOB('
select
每年学校各学科属性专任教师人数.xxbsm as 学校标识码,
每年学校各学科属性专任教师人数.xksx as 学科属性,
每年学校各学科属性专任教师人数.tjnf as 统计年份,
每年学校各学科属性专任教师人数.zrjss as 专任教师数,
每年学校各学科属性专任教师人数.mnzzgccrcs as 每年在职高层次人才数
from tea_si_sch_0002 每年学校各学科属性专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0002'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0003', 'tea_si_sch_0003', '每年学校外籍专任教师人数', TO_CLOB('
select
每年学校外籍专任教师人数.xxbsm as 学校标识码,
每年学校外籍专任教师人数.tjnf as 统计年份,
每年学校外籍专任教师人数.wjzrjss as 外籍专任教师数
from tea_si_sch_0003 每年学校外籍专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0003'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0004', 'tea_si_sch_0004', '每年学校外籍专任教师比例', TO_CLOB('
select
每年学校外籍专任教师比例.xxbsm as 学校标识码,
每年学校外籍专任教师比例.tjnf as 统计年份,
每年学校外籍专任教师比例.wjzrjsbl as 外籍专任教师比例
from tea_si_sch_0004 每年学校外籍专任教师比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0004'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0005', 'tea_si_sch_0005', '每年学校各学科属性外籍专任教师人数', TO_CLOB('
select
每年学校各学科属性外籍专任教师人数.xxbsm as 学校标识码,
每年学校各学科属性外籍专任教师人数.xksx as 学科属性,
每年学校各学科属性外籍专任教师人数.tjnf as 统计年份,
每年学校各学科属性外籍专任教师人数.wjzrjss as 外籍专任教师数
from tea_si_sch_0005 每年学校各学科属性外籍专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0005'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0006', 'tea_si_sch_0006', '每年学校各学科属性外籍专任教师比例', TO_CLOB('
select
每年学校各学科属性外籍专任教师比例.xxbsm as 学校标识码,
每年学校各学科属性外籍专任教师比例.xksx as 学科属性,
每年学校各学科属性外籍专任教师比例.tjnf as 统计年份,
每年学校各学科属性外籍专任教师比例.wjzrjsbl as 外籍专任教师比例
from tea_si_sch_0006 每年学校各学科属性外籍专任教师比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0006'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0007', 'tea_si_sch_0007', '每年学校具有博士学位专任教师人数', TO_CLOB('
select
每年学校具有博士学位专任教师人数.xxbsm as 学校标识码,
每年学校具有博士学位专任教师人数.tjnf as 统计年份,
每年学校具有博士学位专任教师人数.bsxwzrjss as 博士学位专任教师数
from tea_si_sch_0007 每年学校具有博士学位专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0007'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0008', 'tea_si_sch_0008', '每年学校博士学位专任教师比例', TO_CLOB('
select
每年学校博士学位专任教师比例.xxbsm as 学校标识码,
每年学校博士学位专任教师比例.tjnf as 统计年份,
每年学校博士学位专任教师比例.bsxwzrjsbl as 博士学位专任教师比例
from tea_si_sch_0008 每年学校博士学位专任教师比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0008'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0009', 'tea_si_sch_0009', '每年学校各学科属性具有博士学位专任教师人数', TO_CLOB('
select
每年学校各学科属性具有博士学位专任教师人数.xxbsm as 学校标识码,
每年学校各学科属性具有博士学位专任教师人数.xksx as 学科属性,
每年学校各学科属性具有博士学位专任教师人数.tjnf as 统计年份,
每年学校各学科属性具有博士学位专任教师人数.bsxwzrjss as 博士学位专任教师数
from tea_si_sch_0009 每年学校各学科属性具有博士学位专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0009'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0010', 'tea_si_sch_0010', '每年学校各学科属性博士学位专任教师比例', TO_CLOB('
select
每年学校各学科属性博士学位专任教师比例.xxbsm as 学校标识码,
每年学校各学科属性博士学位专任教师比例.xksx as 学科属性,
每年学校各学科属性博士学位专任教师比例.tjnf as 统计年份,
每年学校各学科属性博士学位专任教师比例.bsxwzrjsbl as 博士学位专任教师比例
from tea_si_sch_0010 每年学校各学科属性博士学位专任教师比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0010'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0011', 'tea_si_sch_0011', '每年学校专任教师中高层次人才比例', TO_CLOB('
select
每年学校专任教师中高层次人才比例.xxbsm as 学校标识码,
每年学校专任教师中高层次人才比例.tjnf as 统计年份,
每年学校专任教师中高层次人才比例.bl as 比例
from tea_si_sch_0011 每年学校专任教师中高层次人才比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0011'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0012', 'tea_si_sch_0012', '每年学校专任教师生师比', TO_CLOB('
select
每年学校专任教师生师比.xxbsm as 学校标识码,
每年学校专任教师生师比.tjnf as 统计年份,
每年学校专任教师生师比.zrjsssb as 专任教师生师比
from tea_si_sch_0012 每年学校专任教师生师比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0012'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0013', 'tea_si_sch_0013', '每年学校各学科属性专任教师生师比', TO_CLOB('
select
每年学校各学科属性专任教师生师比.xxbsm as 学校标识码,
每年学校各学科属性专任教师生师比.xksx as 学科属性,
每年学校各学科属性专任教师生师比.tjnf as 统计年份,
每年学校各学科属性专任教师生师比.zrjsssb as 专任教师生师比
from tea_si_sch_0013 每年学校各学科属性专任教师生师比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0013'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0014', 'tea_si_sch_0014', '每年学校各性别专任教师人数', TO_CLOB('
select
每年学校各性别专任教师人数.xxbsm as 学校标识码,
每年学校各性别专任教师人数.xb as 性别,
每年学校各性别专任教师人数.tjnf as 统计年份,
每年学校各性别专任教师人数.zrjss as 专任教师数
from tea_si_sch_0014 每年学校各性别专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0014'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0015', 'tea_si_sch_0015', '每年学校各年龄段专任教师人数', TO_CLOB('
select
每年学校各年龄段专任教师人数.xxbsm as 学校标识码,
每年学校各年龄段专任教师人数.nld as 年龄段,
每年学校各年龄段专任教师人数.tjnf as 统计年份,
每年学校各年龄段专任教师人数.zrjss as 专任教师数
from tea_si_sch_0015 每年学校各年龄段专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0015'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0016', 'tea_si_sch_0016', '每年学校各学缘专任教师人数', TO_CLOB('
select
每年学校各学缘专任教师人数.xxbsm as 学校标识码,
每年学校各学缘专任教师人数.sfbxby as 是否本校毕业,
每年学校各学缘专任教师人数.tjnf as 统计年份,
每年学校各学缘专任教师人数.zrjss as 专任教师数
from tea_si_sch_0016 每年学校各学缘专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0016'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0017', 'tea_si_sch_0017', '每年学校各职称专任教师人数', TO_CLOB('
select
每年学校各职称专任教师人数.xxbsm as 学校标识码,
每年学校各职称专任教师人数.zc as 职称,
每年学校各职称专任教师人数.tjnf as 统计年份,
每年学校各职称专任教师人数.zrjss as 专任教师数
from tea_si_sch_0017 每年学校各职称专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0017'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0018', 'tea_si_sch_0018', '每年学校各职称各学位专任教师人数', TO_CLOB('
select
每年学校各职称各学位专任教师人数.xxbsm as 学校标识码,
每年学校各职称各学位专任教师人数.zc as 职称,
每年学校各职称各学位专任教师人数.xw as 学位,
每年学校各职称各学位专任教师人数.tjnf as 统计年份,
每年学校各职称各学位专任教师人数.zrjss as 专任教师数
from tea_si_sch_0018 每年学校各职称各学位专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0018'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0019', 'tea_si_sch_0019', '每年学校各职称各学缘专任教师人数', TO_CLOB('
select
每年学校各职称各学缘专任教师人数.xxbsm as 学校标识码,
每年学校各职称各学缘专任教师人数.zc as 职称,
每年学校各职称各学缘专任教师人数.sfbxby as 是否本校毕业,
每年学校各职称各学缘专任教师人数.tjnf as 统计年份,
每年学校各职称各学缘专任教师人数.zrjss as 专任教师数
from tea_si_sch_0019 每年学校各职称各学缘专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0019'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0020', 'tea_si_sch_0020', '每年学校各职称各年龄段专任教师人数', TO_CLOB('
select
每年学校各职称各年龄段专任教师人数.xxbsm as 学校标识码,
每年学校各职称各年龄段专任教师人数.zc as 职称,
每年学校各职称各年龄段专任教师人数.nld as 年龄段,
每年学校各职称各年龄段专任教师人数.tjnf as 统计年份,
每年学校各职称各年龄段专任教师人数.zrjss as 专任教师数
from tea_si_sch_0020 每年学校各职称各年龄段专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0020'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0021', 'tea_si_sch_0021', '每年学校各学位专任教师人数', TO_CLOB('
select
每年学校各学位专任教师人数.xxbsm as 学校标识码,
每年学校各学位专任教师人数.zgxw as 最高学位,
每年学校各学位专任教师人数.tjnf as 统计年份,
每年学校各学位专任教师人数.zrjss as 专任教师数
from tea_si_sch_0021 每年学校各学位专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0021'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0022', 'tea_si_sch_0022', '每年学校有无海外经历专任教师人数', TO_CLOB('
select
每年学校有无海外经历专任教师人数.xxbsm as 学校标识码,
每年学校有无海外经历专任教师人数.sfyhwjl as 是否有海外经历,
每年学校有无海外经历专任教师人数.tjnf as 统计年份,
每年学校有无海外经历专任教师人数.zrjss as 专任教师数
from tea_si_sch_0022 每年学校有无海外经历专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0022'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0023', 'tea_si_sch_0023', '每年学校各海外经历专任教师人数', TO_CLOB('
select
每年学校各海外经历专任教师人数.xxbsm as 学校标识码,
每年学校各海外经历专任教师人数.hwjllx as 海外经历类型,
每年学校各海外经历专任教师人数.tjnf as 统计年份,
每年学校各海外经历专任教师人数.zrjss as 专任教师数
from tea_si_sch_0023 每年学校各海外经历专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0023'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0024', 'tea_si_sch_0024', '每年学校各学科属性各性别专任教师人数', TO_CLOB('
select
每年学校各学科属性各性别专任教师人数.xxbsm as 学校标识码,
每年学校各学科属性各性别专任教师人数.xksx as 学科属性,
每年学校各学科属性各性别专任教师人数.xb as 性别,
每年学校各学科属性各性别专任教师人数.tjnf as 统计年份,
每年学校各学科属性各性别专任教师人数.zrjss as 专任教师数
from tea_si_sch_0024 每年学校各学科属性各性别专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0024'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0025', 'tea_si_sch_0025', '每年学校各学科属性各年龄段专任教师人数', TO_CLOB('
select
每年学校各学科属性各年龄段专任教师人数.xxbsm as 学校标识码,
每年学校各学科属性各年龄段专任教师人数.xksx as 学科属性,
每年学校各学科属性各年龄段专任教师人数.nld as 年龄段,
每年学校各学科属性各年龄段专任教师人数.tjnf as 统计年份,
每年学校各学科属性各年龄段专任教师人数.zrjss as 专任教师数
from tea_si_sch_0025 每年学校各学科属性各年龄段专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0025'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0026', 'tea_si_sch_0026', '每年学校各学科属性各学缘专任教师人数', TO_CLOB('
select
每年学校各学科属性各学缘专任教师人数.xxbsm as 学校标识码,
每年学校各学科属性各学缘专任教师人数.xksx as 学科属性,
每年学校各学科属性各学缘专任教师人数.sfbxby as 是否本校毕业,
每年学校各学科属性各学缘专任教师人数.tjnf as 统计年份,
每年学校各学科属性各学缘专任教师人数.zrjss as 专任教师数
from tea_si_sch_0026 每年学校各学科属性各学缘专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0026'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0027', 'tea_si_sch_0027', '每年学校各学科属性各职称专任教师人数', TO_CLOB('
select
每年学校各学科属性各职称专任教师人数.xxbsm as 学校标识码,
每年学校各学科属性各职称专任教师人数.xksx as 学科属性,
每年学校各学科属性各职称专任教师人数.zc as 职称,
每年学校各学科属性各职称专任教师人数.tjnf as 统计年份,
每年学校各学科属性各职称专任教师人数.zrjss as 专任教师数
from tea_si_sch_0027 每年学校各学科属性各职称专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0027'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0028', 'tea_si_sch_0028', '每年学校各学科属性各学位专任教师人数', TO_CLOB('
select
每年学校各学科属性各学位专任教师人数.xxbsm as 学校标识码,
每年学校各学科属性各学位专任教师人数.xksx as 学科属性,
每年学校各学科属性各学位专任教师人数.zgxw as 最高学位,
每年学校各学科属性各学位专任教师人数.tjnf as 统计年份,
每年学校各学科属性各学位专任教师人数.zrjss as 专任教师数
from tea_si_sch_0028 每年学校各学科属性各学位专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0028'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0029', 'tea_si_sch_0029', '每年学校各学科属性有无海外经历专任教师人数', TO_CLOB('
select
每年学校各学科属性有无海外经历专任教师人数.xxbsm as 学校标识码,
每年学校各学科属性有无海外经历专任教师人数.xksx as 学科属性,
每年学校各学科属性有无海外经历专任教师人数.sfyhwjl as 是否有海外经历,
每年学校各学科属性有无海外经历专任教师人数.tjnf as 统计年份,
每年学校各学科属性有无海外经历专任教师人数.zrjss as 专任教师数
from tea_si_sch_0029 每年学校各学科属性有无海外经历专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0029'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0030', 'tea_si_sch_0030', '每年学校各学科属性各海外经历专任教师人数', TO_CLOB('
select
每年学校各学科属性各海外经历专任教师人数.xxbsm as 学校标识码,
每年学校各学科属性各海外经历专任教师人数.xksx as 学科属性,
每年学校各学科属性各海外经历专任教师人数.hwjl as 海外经历,
每年学校各学科属性各海外经历专任教师人数.tjnf as 统计年份,
每年学校各学科属性各海外经历专任教师人数.zrjss as 专任教师数
from tea_si_sch_0030 每年学校各学科属性各海外经历专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0030'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0031', 'tea_si_sch_0031', '每年学校各职称教师的平均年龄', TO_CLOB('
select
每年学校各职称教师的平均年龄.xxbsm as 学校标识码,
每年学校各职称教师的平均年龄.zc as 职称,
每年学校各职称教师的平均年龄.tjnf as 统计年份,
每年学校各职称教师的平均年龄.pjnl as 平均年龄
from tea_si_sch_0031 每年学校各职称教师的平均年龄'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0031'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0032', 'tea_si_sch_0032', '每年学校预计退休的专任教师人数', TO_CLOB('
select
每年学校预计退休的专任教师人数.xxbsm as 学校标识码,
每年学校预计退休的专任教师人数.yjtxnf as 预计退休年份,
每年学校预计退休的专任教师人数.tjnf as 统计年份,
每年学校预计退休的专任教师人数.zrjss as 专任教师数
from tea_si_sch_0032 每年学校预计退休的专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0032'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0033', 'tea_si_sch_0033', '每年学校预计退休的各职称专任教师人数', TO_CLOB('
select
每年学校预计退休的各职称专任教师人数.xxbsm as 学校标识码,
每年学校预计退休的各职称专任教师人数.zc as 职称,
每年学校预计退休的各职称专任教师人数.yjtxnf as 预计退休年份,
每年学校预计退休的各职称专任教师人数.tjnf as 统计年份,
每年学校预计退休的各职称专任教师人数.zrjss as 专任教师数
from tea_si_sch_0033 每年学校预计退休的各职称专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0033'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0034', 'tea_si_sch_0034', '每年学校各学科属性预计退休的专任教师人数', TO_CLOB('
select
每年学校各学科属性预计退休的专任教师人数.xxbsm as 学校标识码,
每年学校各学科属性预计退休的专任教师人数.xksx as 学科属性,
每年学校各学科属性预计退休的专任教师人数.yjtxnf as 预计退休年份,
每年学校各学科属性预计退休的专任教师人数.tjnf as 统计年份,
每年学校各学科属性预计退休的专任教师人数.zrjss as 专任教师数
from tea_si_sch_0034 每年学校各学科属性预计退休的专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0034'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0035', 'tea_si_sch_0035', '每年学校各学科属性预计退休的各职称专任教师人数', TO_CLOB('
select
每年学校各学科属性预计退休的各职称专任教师人数.xxbsm as 学校标识码,
每年学校各学科属性预计退休的各职称专任教师人数.xksx as 学科属性,
每年学校各学科属性预计退休的各职称专任教师人数.zc as 职称,
每年学校各学科属性预计退休的各职称专任教师人数.yjtxnf as 预计退休年份,
每年学校各学科属性预计退休的各职称专任教师人数.tjnf as 统计年份,
每年学校各学科属性预计退休的各职称专任教师人数.zrjss as 专任教师数
from tea_si_sch_0035 每年学校各学科属性预计退休的各职称专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0035'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0036', 'tea_si_sch_0036', '每年各人才类别人数', TO_CLOB('
select
每年各人才类别人数.xxbsm as 学校标识码,
每年各人才类别人数.rclb as 人才类别,
每年各人才类别人数.tjnf as 统计年份,
每年各人才类别人数.rcsl as 人才数量
from tea_si_sch_0036 每年各人才类别人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0036'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0037', 'tea_si_sch_0037', '每年各人才类别新增人数', TO_CLOB('
select
每年各人才类别新增人数.xxbsm as 学校标识码,
每年各人才类别新增人数.rclb as 人才类别,
每年各人才类别新增人数.tjnf as 统计年份,
每年各人才类别新增人数.rcsl as 人才数量
from tea_si_sch_0037 每年各人才类别新增人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0037'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0038', 'tea_si_sch_0038', '每年各人才类别学科匹配、学科交叉人数', TO_CLOB('
select
每年各人才类别学科匹配学科交叉人数.xxbsm as 学校标识码,
每年各人才类别学科匹配学科交叉人数.rclb as 人才类别,
每年各人才类别学科匹配学科交叉人数.xkppx as 学科匹配性,
每年各人才类别学科匹配学科交叉人数.tjnf as 统计年份,
每年各人才类别学科匹配学科交叉人数.rcsl as 人才数量
from tea_si_sch_0038 每年各人才类别学科匹配学科交叉人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0038'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0039', 'tea_si_sch_0039', '每年各人才类别来源于各国家地区人数', TO_CLOB('
select
每年各人才类别来源于各国家地区人数.xxbsm as 学校标识码,
每年各人才类别来源于各国家地区人数.rclb as 人才类别,
每年各人才类别来源于各国家地区人数.gjdq as 国家地区,
每年各人才类别来源于各国家地区人数.tjnf as 统计年份,
每年各人才类别来源于各国家地区人数.rcsl as 人才数量
from tea_si_sch_0039 每年各人才类别来源于各国家地区人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0039'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0040', 'tea_si_sch_0040', '每年各人才类别来源于各省份人数', TO_CLOB('
select
每年各人才类别来源于各省份人数.xxbsm as 学校标识码,
每年各人才类别来源于各省份人数.rclb as 人才类别,
每年各人才类别来源于各省份人数.jg as 籍贯,
每年各人才类别来源于各省份人数.tjnf as 统计年份,
每年各人才类别来源于各省份人数.rcsl as 人才数量
from tea_si_sch_0040 每年各人才类别来源于各省份人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0040'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0041', 'tea_si_sch_0041', '学校各类科研成果的总数', TO_CLOB('
select
学校各类科研成果的总数.xxbsm as 学校标识码,
学校各类科研成果的总数.kycglx as 科研成果类型,
学校各类科研成果的总数.tjnf as 统计年份,
学校各类科研成果的总数.cgsl as 成果数量
from tea_si_sch_0041 学校各类科研成果的总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0041'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0042', 'tea_si_sch_0042', '学校各刊物级别论文发表情况', TO_CLOB('
select
学校各刊物级别论文发表情况.xxbsm as 学校标识码,
学校各刊物级别论文发表情况.kwjb as 刊物级别,
学校各刊物级别论文发表情况.tjrq as 统计日期,
学校各刊物级别论文发表情况.lwsl as 论文数量
from tea_si_sch_0042 学校各刊物级别论文发表情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0042'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0043', 'tea_si_sch_0043', '每年学校科研成果转化合同数', TO_CLOB('
select
每年学校科研成果转化合同数.xxbsm as 学校标识码,
每年学校科研成果转化合同数.tjnf as 统计年份,
每年学校科研成果转化合同数.hts as 合同数
from tea_si_sch_0043 每年学校科研成果转化合同数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0043'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0044', 'tea_si_sch_0044', '每年学校科研成果转化合同额', TO_CLOB('
select
每年学校科研成果转化合同额.xxbsm as 学校标识码,
每年学校科研成果转化合同额.tjnf as 统计年份,
每年学校科研成果转化合同额.hte as 合同额
from tea_si_sch_0044 每年学校科研成果转化合同额'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0044'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0045', 'tea_si_sch_0045', '每年学校科研成果转化率', TO_CLOB('
select
每年学校科研成果转化率.xxbsm as 学校标识码,
每年学校科研成果转化率.tjnf as 统计年份,
每年学校科研成果转化率.zhl as 转化率
from tea_si_sch_0045 每年学校科研成果转化率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0045'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0046', 'tea_si_sch_0046', '学校的横纵向科研项目数', TO_CLOB('
select
学校的横纵向科研项目数.xxbsm as 学校标识码,
学校的横纵向科研项目数.xmlb as 项目类别,
学校的横纵向科研项目数.tjrq as 统计日期,
学校的横纵向科研项目数.xmsl as 项目数量
from tea_si_sch_0046 学校的横纵向科研项目数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0046'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0047', 'tea_si_sch_0047', '学校的科研经费总额', TO_CLOB('
select
学校的科研经费总额.xxbsm as 学校标识码,
学校的科研经费总额.tjrq as 统计日期,
学校的科研经费总额.xmze as 项目总额
from tea_si_sch_0047 学校的科研经费总额'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0047'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0048', 'tea_si_sch_0048', '学校各来源科研项目数', TO_CLOB('
select
学校各来源科研项目数.xxbsm as 学校标识码,
学校各来源科研项目数.xmly as 项目来源,
学校各来源科研项目数.tjrq as 统计日期,
学校各来源科研项目数.xmsl as 项目数量
from tea_si_sch_0048 学校各来源科研项目数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0048'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0049', 'tea_si_sch_0049', '每年学校各级别科研项目数', TO_CLOB('
select
每年学校各级别科研项目数.xxbsm as 学校标识码,
每年学校各级别科研项目数.xmjb as 项目级别,
每年学校各级别科研项目数.tjnf as 统计年份,
每年学校各级别科研项目数.xmsl as 项目数量
from tea_si_sch_0049 每年学校各级别科研项目数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0049'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0050', 'tea_si_sch_0050', '学校中各级别科研奖项数', TO_CLOB('
select
学校中各级别科研奖项数.xxbsm as 学校标识码,
学校中各级别科研奖项数.jxjb as 奖项级别,
学校中各级别科研奖项数.tjrq as 统计日期,
学校中各级别科研奖项数.jxsl as 奖项数量
from tea_si_sch_0050 学校中各级别科研奖项数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0050'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0051', 'tea_si_sch_0051', '每年学校各级别科研奖项数', TO_CLOB('
select
每年学校各级别科研奖项数.xxbsm as 学校标识码,
每年学校各级别科研奖项数.jxjb as 奖项级别,
每年学校各级别科研奖项数.tjnf as 统计年份,
每年学校各级别科研奖项数.jxsl as 奖项数量
from tea_si_sch_0051 每年学校各级别科研奖项数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0051'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0052', 'tea_si_sch_0052', '学校各类科研成果获奖情况', TO_CLOB('
select
学校各类科研成果获奖情况.xxbsm as 学校标识码,
学校各类科研成果获奖情况.kycglx as 科研成果类型,
学校各类科研成果获奖情况.tjrq as 统计日期,
学校各类科研成果获奖情况.jxsl as 奖项数量
from tea_si_sch_0052 学校各类科研成果获奖情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0052'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0053', 'tea_si_sch_0053', '每年学校人均教学工作量', TO_CLOB('
select
每年学校人均教学工作量.xxbsm as 学校标识码,
每年学校人均教学工作量.tjnf as 统计年份,
每年学校人均教学工作量.jxgzl as 教学工作量
from tea_si_sch_0053 每年学校人均教学工作量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0053'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0054', 'tea_si_sch_0054', '每年学校各职称人均教学工作量', TO_CLOB('
select
每年学校各职称人均教学工作量.xxbsm as 学校标识码,
每年学校各职称人均教学工作量.zc as 职称,
每年学校各职称人均教学工作量.tjnf as 统计年份,
每年学校各职称人均教学工作量.jxgzl as 教学工作量
from tea_si_sch_0054 每年学校各职称人均教学工作量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0054'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0055', 'tea_si_sch_0055', '每年学校各学位人均教学工作量', TO_CLOB('
select
每年学校各学位人均教学工作量.xxbsm as 学校标识码,
每年学校各学位人均教学工作量.xw as 学位,
每年学校各学位人均教学工作量.tjnf as 统计年份,
每年学校各学位人均教学工作量.jxgzl as 教学工作量
from tea_si_sch_0055 每年学校各学位人均教学工作量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0055'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0056', 'tea_si_sch_0056', '每年学校人均授课数', TO_CLOB('
select
每年学校人均授课数.xxbsm as 学校标识码,
每年学校人均授课数.tjnf as 统计年份,
每年学校人均授课数.skms as 授课门数
from tea_si_sch_0056 每年学校人均授课数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0056'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0057', 'tea_si_sch_0057', '每年学校人均授课学时', TO_CLOB('
select
每年学校人均授课学时.xxbsm as 学校标识码,
每年学校人均授课学时.tjnf as 统计年份,
每年学校人均授课学时.skxs as 授课学时
from tea_si_sch_0057 每年学校人均授课学时'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0057'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0058', 'tea_si_sch_0058', '学校各职称教师指导各类型学生数', TO_CLOB('
select
学校各职称教师指导各类型学生数.xxbsm as 学校标识码,
学校各职称教师指导各类型学生数.zc as 职称,
学校各职称教师指导各类型学生数.xslx as 学生类型,
学校各职称教师指导各类型学生数.tjrq as 统计日期,
学校各职称教师指导各类型学生数.xss as 学生数
from tea_si_sch_0058 学校各职称教师指导各类型学生数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0058'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0059', 'tea_si_sch_0059', '每年各职称教师的评教均分', TO_CLOB('
select
每年各职称教师的评教均分.xxbsm as 学校标识码,
每年各职称教师的评教均分.zc as 职称,
每年各职称教师的评教均分.tjnf as 统计年份,
每年各职称教师的评教均分.pjjf as 评教均分
from tea_si_sch_0059 每年各职称教师的评教均分'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0059'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0060', 'tea_si_sch_0060', '每年学校各职称教师为本科生上课的比例', TO_CLOB('
select
每年学校各职称教师为本科生上课的比例.xxbsm as 学校标识码,
每年学校各职称教师为本科生上课的比例.zc as 职称,
每年学校各职称教师为本科生上课的比例.tjnf as 统计年份,
每年学校各职称教师为本科生上课的比例.bl as 比例
from tea_si_sch_0060 每年学校各职称教师为本科生上课的比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0060'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0061', 'tea_si_sch_0061', '每年学校各职称教师为本科生上课的人均学时数', TO_CLOB('
select
每年学校各职称教师为本科生上课的人均学时数.xxbsm as 学校标识码,
每年学校各职称教师为本科生上课的人均学时数.zc as 职称,
每年学校各职称教师为本科生上课的人均学时数.tjnf as 统计年份,
每年学校各职称教师为本科生上课的人均学时数.rjxss as 人均学时数
from tea_si_sch_0061 每年学校各职称教师为本科生上课的人均学时数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0061'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0062', 'tea_si_sch_0062', '每年学校教改项目总数', TO_CLOB('
select
每年学校教改项目总数.xxbsm as 学校标识码,
每年学校教改项目总数.tjnf as 统计年份,
每年学校教改项目总数.xmsl as 项目数量
from tea_si_sch_0062 每年学校教改项目总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0062'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0063', 'tea_si_sch_0063', '学校各级别各等级教学成果获奖情况', TO_CLOB('
select
学校各级别各等级教学成果获奖情况.xxbsm as 学校标识码,
学校各级别各等级教学成果获奖情况.jljb as 奖励级别,
学校各级别各等级教学成果获奖情况.jldj as 奖励等级,
学校各级别各等级教学成果获奖情况.tjrq as 统计日期,
学校各级别各等级教学成果获奖情况.jxsl as 奖项数量
from tea_si_sch_0063 学校各级别各等级教学成果获奖情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0063'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0323', 'tea_si_sch_0323', '每年在职教职工数', TO_CLOB('
select
每年在职教职工数.xxbsm as 学校标识码,
每年在职教职工数.xb as 性别,
每年在职教职工数.nld as 年龄段,
每年在职教职工数.xygx as 学缘关系,
每年在职教职工数.zgxw as 最高学位,
每年在职教职工数.sfyhwjl as 是否有海外经历,
每年在职教职工数.jzglb as 教职工类别,
每年在职教职工数.zyjszwjbdm as 专业技术职务级别代码,
每年在职教职工数.zyjszwjb as 专业技术职务级别,
每年在职教职工数.yjtxnf as 预计退休年份,
每年在职教职工数.tjnf as 统计年份,
每年在职教职工数.jzgrs as 教职工人数,
每年在职教职工数.zgxwcc as 最高学位层次,
每年在职教职工数.ssjgdm as 所属机构代码,
每年在职教职工数.ssjg as 所属机构
from tea_si_sch_0323 每年在职教职工数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0323'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0354', 'tea_si_sch_0354', '每年在职专任教师数', TO_CLOB('
select
每年在职专任教师数.xxbsm as 学校标识码,
每年在职专任教师数.xkly as 学科领域,
每年在职专任教师数.ssjgdm as 所属机构代码,
每年在职专任教师数.ssjg as 所属机构,
每年在职专任教师数.xb as 性别,
每年在职专任教师数.gjdq as 国家地区,
每年在职专任教师数.nld as 年龄段,
每年在职专任教师数.xygx as 学缘关系,
每年在职专任教师数.zgxw as 最高学位,
每年在职专任教师数.zgxwcc as 最高学位层次,
每年在职专任教师数.sfyhwjl as 是否有海外经历,
每年在职专任教师数.zyjszwdm as 专业技术职务代码,
每年在职专任教师数.zyjszw as 专业技术职务,
每年在职专任教师数.yjtxnf as 预计退休年份,
每年在职专任教师数.tjnf as 统计年份,
每年在职专任教师数.zrjss as 专任教师数
from tea_si_sch_0354 每年在职专任教师数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0354'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0355', 'tea_si_sch_0355', '每年在职高层次人才数', TO_CLOB('
select
每年在职高层次人才数.xxbsm as 学校标识码,
每年在职高层次人才数.rclb as 人才类别,
每年在职高层次人才数.ssjgdm as 所属机构代码,
每年在职高层次人才数.ssjg as 所属机构,
每年在职高层次人才数.zyjszwjbdm as 专业技术职务级别代码,
每年在职高层次人才数.zyjszwjb as 专业技术职务级别,
每年在职高层次人才数.gjdq as 国家地区,
每年在职高层次人才数.nld as 年龄段,
每年在职高层次人才数.tjnf as 统计年份,
每年在职高层次人才数.rcsl as 人才数量
from tea_si_sch_0355 每年在职高层次人才数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0355'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0356', 'tea_si_sch_0356', '每年各第一学历毕业院校类型在职高层次人才数', TO_CLOB('
select
每年各第一学历毕业院校类型在职高层次人才数.xxbsm as 学校标识码,
每年各第一学历毕业院校类型在职高层次人才数.rclb as 人才类别,
每年各第一学历毕业院校类型在职高层次人才数.dyxlbyyxlx as 第一学历毕业院校类型,
每年各第一学历毕业院校类型在职高层次人才数.tjnf as 统计年份,
每年各第一学历毕业院校类型在职高层次人才数.rcsl as 人才数量
from tea_si_sch_0356 每年各第一学历毕业院校类型在职高层次人才数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0356'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_sch_0357', 'tea_si_sch_0357', '每年各博士毕业院校类型在职高层次人才数', TO_CLOB('
select
每年各博士毕业院校类型在职高层次人才数.xxbsm as 学校标识码,
每年各博士毕业院校类型在职高层次人才数.rclb as 人才类别,
每年各博士毕业院校类型在职高层次人才数.bsbyyxlx as 博士毕业院校类型,
每年各博士毕业院校类型在职高层次人才数.tjnf as 统计年份,
每年各博士毕业院校类型在职高层次人才数.rcsl as 人才数量
from tea_si_sch_0357 每年各博士毕业院校类型在职高层次人才数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_sch_0357'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0001', 'tea_si_tea_0001', '每位教师各类科研成果的总数', TO_CLOB('
select
每位教师各类科研成果的总数.zgh as 教职工号,
每位教师各类科研成果的总数.kycglx as 科研成果类型,
每位教师各类科研成果的总数.tjrq as 统计日期,
每位教师各类科研成果的总数.cgsl as 成果数量
from tea_si_tea_0001 每位教师各类科研成果的总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0001'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0002', 'tea_si_tea_0002', '每年每位教师各类科研成果的数量', TO_CLOB('
select
每年每位教师各类科研成果的数量.zgh as 教职工号,
每年每位教师各类科研成果的数量.kycglx as 科研成果类型,
每年每位教师各类科研成果的数量.tjnf as 统计年份,
每年每位教师各类科研成果的数量.cgsl as 成果数量
from tea_si_tea_0002 每年每位教师各类科研成果的数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0002'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0003', 'tea_si_tea_0003', '每位教师发表论文情况', TO_CLOB('
select
每位教师发表论文情况.zgh as 教职工号,
每位教师发表论文情况.tjrq as 统计日期,
每位教师发表论文情况.lwzs as 论文总数,
每位教师发表论文情况.yzlws as 一作论文数,
每位教师发表论文情况.txzzlws as 通讯作者论文数
from tea_si_tea_0003 每位教师发表论文情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0003'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0004', 'tea_si_tea_0004', '每位教师各刊物级别论文发表情况', TO_CLOB('
select
每位教师各刊物级别论文发表情况.zgh as 教职工号,
每位教师各刊物级别论文发表情况.kwjb as 刊物级别,
每位教师各刊物级别论文发表情况.tjrq as 统计日期,
每位教师各刊物级别论文发表情况.lwsl as 论文数量
from tea_si_tea_0004 每位教师各刊物级别论文发表情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0004'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0005', 'tea_si_tea_0005', '每位教师的科研经费情况', TO_CLOB('
select
每位教师的科研经费情况.zgh as 教职工号,
每位教师的科研经费情况.tjrq as 统计日期,
每位教师的科研经费情况.xmze as 项目总额,
每位教师的科研经费情况.dked as 到款额度,
每位教师的科研经费情况.lxjfze as 留校经费总额
from tea_si_tea_0005 每位教师的科研经费情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0005'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0006', 'tea_si_tea_0006', '每年每位教师的科研经费总额', TO_CLOB('
select
每年每位教师的科研经费总额.zgh as 教职工号,
每年每位教师的科研经费总额.tjnf as 统计年份,
每年每位教师的科研经费总额.xmze as 项目总额,
每年每位教师的科研经费总额.dked as 到款额度,
每年每位教师的科研经费总额.lxjfze as 留校经费总额
from tea_si_tea_0006 每年每位教师的科研经费总额'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0006'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0007', 'tea_si_tea_0007', '每位教师参与科研项目数', TO_CLOB('
select
每位教师参与科研项目数.zgh as 教职工号,
每位教师参与科研项目数.tjrq as 统计日期,
每位教师参与科研项目数.xmzs as 项目总数,
每位教师参与科研项目数.xmcyrsm as 项目参与人数目,
每位教师参与科研项目数.xmfzrxm as 项目负责人项目
from tea_si_tea_0007 每位教师参与科研项目数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0007'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0008', 'tea_si_tea_0008', '每位教师参与横向科研项目数', TO_CLOB('
select
每位教师参与横向科研项目数.zgh as 教职工号,
每位教师参与横向科研项目数.tjrq as 统计日期,
每位教师参与横向科研项目数.xmsl as 项目数量
from tea_si_tea_0008 每位教师参与横向科研项目数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0008'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0009', 'tea_si_tea_0009', '每位教师参与纵向科研项目数', TO_CLOB('
select
每位教师参与纵向科研项目数.zgh as 教职工号,
每位教师参与纵向科研项目数.tjrq as 统计日期,
每位教师参与纵向科研项目数.xmsl as 项目数量
from tea_si_tea_0009 每位教师参与纵向科研项目数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0009'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0010', 'tea_si_tea_0010', '每位教师参与各级别科研项目情况', TO_CLOB('
select
每位教师参与各级别科研项目情况.zgh as 教职工号,
每位教师参与各级别科研项目情况.xmjb as 项目级别,
每位教师参与各级别科研项目情况.tjrq as 统计日期,
每位教师参与各级别科研项目情况.xmsl as 项目数量
from tea_si_tea_0010 每位教师参与各级别科研项目情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0010'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0011', 'tea_si_tea_0011', '每年每位教师参与科研项目数', TO_CLOB('
select
每年每位教师参与科研项目数.zgh as 教职工号,
每年每位教师参与科研项目数.tjnf as 统计年份,
每年每位教师参与科研项目数.xmzs as 项目总数,
每年每位教师参与科研项目数.xmcyrsm as 项目参与人数目,
每年每位教师参与科研项目数.xmfzrxm as 项目负责人项目
from tea_si_tea_0011 每年每位教师参与科研项目数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0011'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0012', 'tea_si_tea_0012', '每年每位教师参与横向科研项目数', TO_CLOB('
select
每年每位教师参与横向科研项目数.zgh as 教职工号,
每年每位教师参与横向科研项目数.tjnf as 统计年份,
每年每位教师参与横向科研项目数.xmsl as 项目数量
from tea_si_tea_0012 每年每位教师参与横向科研项目数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0012'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0013', 'tea_si_tea_0013', '每年每位教师参与纵向科研项目数', TO_CLOB('
select
每年每位教师参与纵向科研项目数.zgh as 教职工号,
每年每位教师参与纵向科研项目数.tjnf as 统计年份,
每年每位教师参与纵向科研项目数.xmsl as 项目数量
from tea_si_tea_0013 每年每位教师参与纵向科研项目数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0013'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0014', 'tea_si_tea_0014', '每年每位教师参与各级别科研项目情况', TO_CLOB('
select
每年每位教师参与各级别科研项目情况.zgh as 教职工号,
每年每位教师参与各级别科研项目情况.xmjb as 项目级别,
每年每位教师参与各级别科研项目情况.tjnf as 统计年份,
每年每位教师参与各级别科研项目情况.xmsl as 项目数量
from tea_si_tea_0014 每年每位教师参与各级别科研项目情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0014'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0015', 'tea_si_tea_0015', '每位教师参与各类来源科研项目情况', TO_CLOB('
select
每位教师参与各类来源科研项目情况.zgh as 教职工号,
每位教师参与各类来源科研项目情况.xmly as 项目来源,
每位教师参与各类来源科研项目情况.tjrq as 统计日期,
每位教师参与各类来源科研项目情况.xmsl as 项目数量
from tea_si_tea_0015 每位教师参与各类来源科研项目情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0015'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0016', 'tea_si_tea_0016', '每位教师获得的科研奖项总数', TO_CLOB('
select
每位教师获得的科研奖项总数.zgh as 教职工号,
每位教师获得的科研奖项总数.tjrq as 统计日期,
每位教师获得的科研奖项总数.jxsl as 奖项数量
from tea_si_tea_0016 每位教师获得的科研奖项总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0016'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0017', 'tea_si_tea_0017', '每位教师获得各级别科研奖项数', TO_CLOB('
select
每位教师获得各级别科研奖项数.zgh as 教职工号,
每位教师获得各级别科研奖项数.jxjb as 奖项级别,
每位教师获得各级别科研奖项数.tjrq as 统计日期,
每位教师获得各级别科研奖项数.jxsl as 奖项数量
from tea_si_tea_0017 每位教师获得各级别科研奖项数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0017'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0018', 'tea_si_tea_0018', '每年每位教师获得各级别科研奖项数', TO_CLOB('
select
每年每位教师获得各级别科研奖项数.zgh as 教职工号,
每年每位教师获得各级别科研奖项数.jxjb as 奖项级别,
每年每位教师获得各级别科研奖项数.tjnf as 统计年份,
每年每位教师获得各级别科研奖项数.jxsl as 奖项数量
from tea_si_tea_0018 每年每位教师获得各级别科研奖项数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0018'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0019', 'tea_si_tea_0019', '每位教师以第一负责人获科研奖项的数目', TO_CLOB('
select
每位教师以第一负责人获科研奖项的数目.zgh as 教职工号,
每位教师以第一负责人获科研奖项的数目.tjrq as 统计日期,
每位教师以第一负责人获科研奖项的数目.jxsl as 奖项数量
from tea_si_tea_0019 每位教师以第一负责人获科研奖项的数目'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0019'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0020', 'tea_si_tea_0020', '每年每位教师的教学工作量', TO_CLOB('
select
每年每位教师的教学工作量.zgh as 教职工号,
每年每位教师的教学工作量.tjnf as 统计年份,
每年每位教师的教学工作量.jxgzl as 教学工作量
from tea_si_tea_0020 每年每位教师的教学工作量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0020'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0021', 'tea_si_tea_0021', '每年每位教师的所有课程评教均分', TO_CLOB('
select
每年每位教师的所有课程评教均分.zgh as 教职工号,
每年每位教师的所有课程评教均分.tjnf as 统计年份,
每年每位教师的所有课程评教均分.pjjf as 评教均分
from tea_si_tea_0021 每年每位教师的所有课程评教均分'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0021'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0022', 'tea_si_tea_0022', '每年每位教师给本专科生授课总学时', TO_CLOB('
select
每年每位教师给本专科生授课总学时.zgh as 教职工号,
每年每位教师给本专科生授课总学时.tjnf as 统计年份,
每年每位教师给本专科生授课总学时.skzxs as 授课总学时
from tea_si_tea_0022 每年每位教师给本专科生授课总学时'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0022'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0023', 'tea_si_tea_0023', '每年每位教师给研究生授课总学时', TO_CLOB('
select
每年每位教师给研究生授课总学时.zgh as 教职工号,
每年每位教师给研究生授课总学时.tjnf as 统计年份,
每年每位教师给研究生授课总学时.skzxs as 授课总学时
from tea_si_tea_0023 每年每位教师给研究生授课总学时'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0023'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0024', 'tea_si_tea_0024', '每年每位教师指导本专科生人数', TO_CLOB('
select
每年每位教师指导本专科生人数.zgh as 教职工号,
每年每位教师指导本专科生人数.tjnf as 统计年份,
每年每位教师指导本专科生人数.zdxsrs as 指导学生人数
from tea_si_tea_0024 每年每位教师指导本专科生人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0024'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0025', 'tea_si_tea_0025', '每年每位教师指导研究生人数', TO_CLOB('
select
每年每位教师指导研究生人数.zgh as 教职工号,
每年每位教师指导研究生人数.tjnf as 统计年份,
每年每位教师指导研究生人数.zdxsrs as 指导学生人数
from tea_si_tea_0025 每年每位教师指导研究生人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0025'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0026', 'tea_si_tea_0026', '每年每位教师给本专科生授课门数', TO_CLOB('
select
每年每位教师给本专科生授课门数.zgh as 教职工号,
每年每位教师给本专科生授课门数.tjnf as 统计年份,
每年每位教师给本专科生授课门数.skms as 授课门数
from tea_si_tea_0026 每年每位教师给本专科生授课门数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0026'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0027', 'tea_si_tea_0027', '每年每位教师给研究生授课门数', TO_CLOB('
select
每年每位教师给研究生授课门数.zgh as 教职工号,
每年每位教师给研究生授课门数.tjnf as 统计年份,
每年每位教师给研究生授课门数.skms as 授课门数
from tea_si_tea_0027 每年每位教师给研究生授课门数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0027'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0028', 'tea_si_tea_0028', '每年每位教师指导各种类型学位的学生论文总数', TO_CLOB('
select
每年每位教师指导各种类型学位的学生论文总数.zgh as 教职工号,
每年每位教师指导各种类型学位的学生论文总数.xwlx as 学位类型,
每年每位教师指导各种类型学位的学生论文总数.tjnf as 统计年份,
每年每位教师指导各种类型学位的学生论文总数.lws as 论文数
from tea_si_tea_0028 每年每位教师指导各种类型学位的学生论文总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0028'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0029', 'tea_si_tea_0029', '每年每位教师指导各种类型学位的学生论文被抽检的数目', TO_CLOB('
select
每年每位教师指导各种类型学位的学生论文被抽检的数目.zgh as 教职工号,
每年每位教师指导各种类型学位的学生论文被抽检的数目.xwlx as 学位类型,
每年每位教师指导各种类型学位的学生论文被抽检的数目.tjnf as 统计年份,
每年每位教师指导各种类型学位的学生论文被抽检的数目.cjlws as 抽检论文数
from tea_si_tea_0029 每年每位教师指导各种类型学位的学生论文被抽检的数目'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0029'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0030', 'tea_si_tea_0030', '每年每位教师指导各种类型学位的学生论文的抽检合格率', TO_CLOB('
select
每年每位教师指导各种类型学位的学生论文的抽检合格率.zgh as 教职工号,
每年每位教师指导各种类型学位的学生论文的抽检合格率.xwlx as 学位类型,
每年每位教师指导各种类型学位的学生论文的抽检合格率.tjnf as 统计年份,
每年每位教师指导各种类型学位的学生论文的抽检合格率.cjhgl as 抽检合格率
from tea_si_tea_0030 每年每位教师指导各种类型学位的学生论文的抽检合格率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0030'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0031', 'tea_si_tea_0031', '每年每位教师指导各类学生的各类科研实践情况', TO_CLOB('
select
每年每位教师指导各类学生的各类科研实践情况.zgh as 教职工号,
每年每位教师指导各类学生的各类科研实践情况.xslx as 学生类型,
每年每位教师指导各类学生的各类科研实践情况.kysjlx as 科研实践类型,
每年每位教师指导各类学生的各类科研实践情况.tjnf as 统计年份,
每年每位教师指导各类学生的各类科研实践情况.kysjsl as 科研实践数量
from tea_si_tea_0031 每年每位教师指导各类学生的各类科研实践情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0031'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0032', 'tea_si_tea_0032', '每年每位教师各级别一流课程的数目', TO_CLOB('
select
每年每位教师各级别一流课程的数目.zgh as 教职工号,
每年每位教师各级别一流课程的数目.ylkcjb as 一流课程级别,
每年每位教师各级别一流课程的数目.tjnf as 统计年份,
每年每位教师各级别一流课程的数目.kcs as 课程数
from tea_si_tea_0032 每年每位教师各级别一流课程的数目'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0032'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0033', 'tea_si_tea_0033', '每位教师各级别一流课程的总数', TO_CLOB('
select
每位教师各级别一流课程的总数.zgh as 教职工号,
每位教师各级别一流课程的总数.ylkcjb as 一流课程级别,
每位教师各级别一流课程的总数.tjrq as 统计日期,
每位教师各级别一流课程的总数.kcs as 课程数
from tea_si_tea_0033 每位教师各级别一流课程的总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0033'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0034', 'tea_si_tea_0034', '每年每位教师参与教改项目情况', TO_CLOB('
select
每年每位教师参与教改项目情况.zgh as 教职工号,
每年每位教师参与教改项目情况.tjnf as 统计年份,
每年每位教师参与教改项目情况.xmzs as 项目总数,
每年每位教师参与教改项目情况.xmcyrsm as 项目参与人数目,
每年每位教师参与教改项目情况.xmfzrxm as 项目负责人项目
from tea_si_tea_0034 每年每位教师参与教改项目情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0034'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0035', 'tea_si_tea_0035', '每年每位教师申报教改项目情况', TO_CLOB('
select
每年每位教师申报教改项目情况.zgh as 教职工号,
每年每位教师申报教改项目情况.tjnf as 统计年份,
每年每位教师申报教改项目情况.sbzs as 申报总数,
每年每位教师申报教改项目情况.lxzs as 立项总数
from tea_si_tea_0035 每年每位教师申报教改项目情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0035'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0036', 'tea_si_tea_0036', '每年每位教师参与各级别教改项目情况', TO_CLOB('
select
每年每位教师参与各级别教改项目情况.zgh as 教职工号,
每年每位教师参与各级别教改项目情况.xmjb as 项目级别,
每年每位教师参与各级别教改项目情况.tjnf as 统计年份,
每年每位教师参与各级别教改项目情况.xmsl as 项目数量
from tea_si_tea_0036 每年每位教师参与各级别教改项目情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0036'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0037', 'tea_si_tea_0037', '每年每位教师获得各级别教学成果奖情况', TO_CLOB('
select
每年每位教师获得各级别教学成果奖情况.zgh as 教职工号,
每年每位教师获得各级别教学成果奖情况.jxjb as 奖项级别,
每年每位教师获得各级别教学成果奖情况.tjnf as 统计年份,
每年每位教师获得各级别教学成果奖情况.jxsl as 奖项数量
from tea_si_tea_0037 每年每位教师获得各级别教学成果奖情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0037'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0038', 'tea_si_tea_0038', '每位教师参与教改项目总数', TO_CLOB('
select
每位教师参与教改项目总数.zgh as 教职工号,
每位教师参与教改项目总数.tjrq as 统计日期,
每位教师参与教改项目总数.xmsl as 项目数量
from tea_si_tea_0038 每位教师参与教改项目总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0038'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0039', 'tea_si_tea_0039', '每位教师获得各级别教学成果奖情况', TO_CLOB('
select
每位教师获得各级别教学成果奖情况.zgh as 教职工号,
每位教师获得各级别教学成果奖情况.jxjb as 奖项级别,
每位教师获得各级别教学成果奖情况.tjrq as 统计日期,
每位教师获得各级别教学成果奖情况.jxsl as 奖项数量
from tea_si_tea_0039 每位教师获得各级别教学成果奖情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0039'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0040', 'tea_si_tea_0040', '每年教师发表论文数量', TO_CLOB('
select
每年教师发表论文数量.zgh as 教职工号,
每年教师发表论文数量.tjnf as 统计年份,
每年教师发表论文数量.yzhtxzzlwsl as 一作或通讯作者论文数量
from tea_si_tea_0040 每年教师发表论文数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0040'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0041', 'tea_si_tea_0041', '每年每位教师发表的各收录类别论文数量', TO_CLOB('
select
每年每位教师发表的各收录类别论文数量.zgh as 教职工号,
每年每位教师发表的各收录类别论文数量.sllb as 收录类别,
每年每位教师发表的各收录类别论文数量.tjnf as 统计年份,
每年每位教师发表的各收录类别论文数量.yzhtxzzlwsl as 一作或通讯作者论文数量
from tea_si_tea_0041 每年每位教师发表的各收录类别论文数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0041'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0042', 'tea_si_tea_0042', '每年每位教师发表的各SCI分区论文数量', TO_CLOB('
select
每年每位教师发表的各SCI分区论文数量.zgh as 教职工号,
每年每位教师发表的各SCI分区论文数量.sfq as SCI分区,
每年每位教师发表的各SCI分区论文数量.tjnf as 统计年份,
每年每位教师发表的各SCI分区论文数量.yzhtxzzlwsl as 一作或通讯作者论文数量
from tea_si_tea_0042 每年每位教师发表的各SCI分区论文数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0042'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0043', 'tea_si_tea_0043', '每年每位教师出版著作数量', TO_CLOB('
select
每年每位教师出版著作数量.zgh as 教职工号,
每年每位教师出版著作数量.zzlb as 著作类别,
每年每位教师出版著作数量.cbsjbdm as 出版社级别代码,
每年每位教师出版著作数量.cbsjb as 出版社级别,
每年每位教师出版著作数量.tjnf as 统计年份,
每年每位教师出版著作数量.yzzzsl as 一作著作数量
from tea_si_tea_0043 每年每位教师出版著作数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0043'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0044', 'tea_si_tea_0044', '每年每位教师发明专利数量', TO_CLOB('
select
每年每位教师发明专利数量.zgh as 教职工号,
每年每位教师发明专利数量.zllx as 专利类型,
每年每位教师发明专利数量.tjnf as 统计年份,
每年每位教师发明专利数量.dyfmrzlsl as 第一发明人专利数量
from tea_si_tea_0044 每年每位教师发明专利数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0044'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0045', 'tea_si_tea_0045', '每年每位教师被采纳的资政报告数量', TO_CLOB('
select
每年每位教师被采纳的资政报告数量.zgh as 教职工号,
每年每位教师被采纳的资政报告数量.psjbdm as 批示级别代码,
每年每位教师被采纳的资政报告数量.psjb as 批示级别,
每年每位教师被采纳的资政报告数量.tjnf as 统计年份,
每年每位教师被采纳的资政报告数量.yzzzbgsl as 一作资政报告数量
from tea_si_tea_0045 每年每位教师被采纳的资政报告数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0045'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0046', 'tea_si_tea_0046', '每年每位教师获得的科研奖励数', TO_CLOB('
select
每年每位教师获得的科研奖励数.zgh as 教职工号,
每年每位教师获得的科研奖励数.hjjbdm as 获奖级别代码,
每年每位教师获得的科研奖励数.hjjb as 获奖级别,
每年每位教师获得的科研奖励数.tjnf as 统计年份,
每年每位教师获得的科研奖励数.dycyrkyjlsl as 第一参与人科研奖励数量
from tea_si_tea_0046 每年每位教师获得的科研奖励数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0046'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0047', 'tea_si_tea_0047', '每年每位教师科研项目数', TO_CLOB('
select
每年每位教师科研项目数.zgh as 教职工号,
每年每位教师科研项目数.xmlb as 项目类别,
每年每位教师科研项目数.xmly as 项目来源,
每年每位教师科研项目数.xmjbdm as 项目级别代码,
每年每位教师科研项目数.xmjb as 项目级别,
每年每位教师科研项目数.tjnf as 统计年份,
每年每位教师科研项目数.zcxmsl as 主持项目数量
from tea_si_tea_0047 每年每位教师科研项目数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0047'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0048', 'tea_si_tea_0048', '每年每位教师科研项目经费', TO_CLOB('
select
每年每位教师科研项目经费.zgh as 教职工号,
每年每位教师科研项目经费.xmlb as 项目类别,
每年每位教师科研项目经费.tjnf as 统计年份,
每年每位教师科研项目经费.kyxmjfze as 科研项目经费总额,
每年每位教师科研项目经费.dzjf as 到账经费
from tea_si_tea_0048 每年每位教师科研项目经费'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0048'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0049', 'tea_si_tea_0049', '每年每位教师科研工作量', TO_CLOB('
select
每年每位教师科研工作量.zgh as 教职工号,
每年每位教师科研工作量.tjnf as 统计年份,
每年每位教师科研工作量.kygzl as 科研工作量
from tea_si_tea_0049 每年每位教师科研工作量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0049'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0050', 'tea_si_tea_0050', '每学期每位教师教学工作量', TO_CLOB('
select
每学期每位教师教学工作量.zgh as 教职工号,
每学期每位教师教学工作量.xnxqbm as 学年学期编码,
每学期每位教师教学工作量.xnxqmc as 学年学期名称,
每学期每位教师教学工作量.tjnf as 统计年份,
每学期每位教师教学工作量.jxgzl as 教学工作量
from tea_si_tea_0050 每学期每位教师教学工作量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0050'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0051', 'tea_si_tea_0051', '每学期每位教师授课门数及学时', TO_CLOB('
select
每学期每位教师授课门数及学时.zgh as 教职工号,
每学期每位教师授课门数及学时.kclb as 课程类别,
每学期每位教师授课门数及学时.xnxqbm as 学年学期编码,
每学期每位教师授课门数及学时.xnxqmc as 学年学期名称,
每学期每位教师授课门数及学时.tjnf as 统计年份,
每学期每位教师授课门数及学时.skms as 授课门数,
每学期每位教师授课门数及学时.skxs as 授课学时,
每学期每位教师授课门数及学时.kclx as 课程类型
from tea_si_tea_0051 每学期每位教师授课门数及学时'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0051'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0052', 'tea_si_tea_0052', '每学期每位教师指导学生人数', TO_CLOB('
select
每学期每位教师指导学生人数.zgh as 教职工号,
每学期每位教师指导学生人数.pycc as 培养层次,
每学期每位教师指导学生人数.xnxqbm as 学年学期编码,
每学期每位教师指导学生人数.xnxqmc as 学年学期名称,
每学期每位教师指导学生人数.tjnf as 统计年份,
每学期每位教师指导学生人数.zdxsrs as 指导学生人数
from tea_si_tea_0052 每学期每位教师指导学生人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0052'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0053', 'tea_si_tea_0053', '每年每位教师指导双创竞赛获奖数量', TO_CLOB('
select
每年每位教师指导双创竞赛获奖数量.zgh as 教职工号,
每年每位教师指导双创竞赛获奖数量.xnbm as 学年编码,
每年每位教师指导双创竞赛获奖数量.xnmc as 学年名称,
每年每位教师指导双创竞赛获奖数量.tjnf as 统计年份,
每年每位教师指导双创竞赛获奖数量.zdscjshjsl as 指导双创竞赛获奖数量
from tea_si_tea_0053 每年每位教师指导双创竞赛获奖数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0053'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0054', 'tea_si_tea_0054', '每年每位教师指导学生实习人数', TO_CLOB('
select
每年每位教师指导学生实习人数.zgh as 教职工号,
每年每位教师指导学生实习人数.tjnf as 统计年份,
每年每位教师指导学生实习人数.zdxssxrs as 指导学生实习人数
from tea_si_tea_0054 每年每位教师指导学生实习人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0054'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0055', 'tea_si_tea_0055', '每年每位教师指导毕业论文（设计）数量', TO_CLOB('
select
每年每位教师指导毕业论文设计数量.zgh as 教职工号,
每年每位教师指导毕业论文设计数量.pycc as 培养层次,
每年每位教师指导毕业论文设计数量.tjnf as 统计年份,
每年每位教师指导毕业论文设计数量.zdbysjlwsl as 指导毕业设计论文数量
from tea_si_tea_0055 每年每位教师指导毕业论文设计数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0055'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0056', 'tea_si_tea_0056', '每年每位教师教改项目数量', TO_CLOB('
select
每年每位教师教改项目数量.zgh as 教职工号,
每年每位教师教改项目数量.xmjbdm as 项目级别代码,
每年每位教师教改项目数量.xmjb as 项目级别,
每年每位教师教改项目数量.tjnf as 统计年份,
每年每位教师教改项目数量.zcjgxmsl as 主持教改项目数量
from tea_si_tea_0056 每年每位教师教改项目数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0056'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_si_tea_0057', 'tea_si_tea_0057', '每年每位教师获得的教学成果奖数量', TO_CLOB('
select
每年每位教师获得的教学成果奖数量.zgh as 教职工号,
每年每位教师获得的教学成果奖数量.jljbdm as 奖励级别代码,
每年每位教师获得的教学成果奖数量.jljb as 奖励级别,
每年每位教师获得的教学成果奖数量.tjnf as 统计年份,
每年每位教师获得的教学成果奖数量.dycyrjxcgjsl as 第一参与人教学成果奖数量
from tea_si_tea_0057 每年每位教师获得的教学成果奖数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_si_tea_0057'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'col_label', 'col_label', 'COL标签宽表', TO_CLOB('
select
COL标签宽表.kzrq as 快照日期,
COL标签宽表.xybh as 学院编号
from col_label COL标签宽表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'col_label'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'sch_label', 'sch_label', 'SCH标签宽表', TO_CLOB('
select
SCH标签宽表.kzrq as 快照日期,
SCH标签宽表.xxbsm as 学校标识码
from sch_label SCH标签宽表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'sch_label'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 't_da_model_column_extend', 't_da_model_column_extend', '', TO_CLOB('
select
t_da_model_column_extend.primary_column_tag as 是否主键字段
from t_da_model_column_extend t_da_model_column_extend'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 't_da_model_column_extend'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 't_da_model_column_info', 't_da_model_column_info', '模型字段信息', TO_CLOB('
select
模型字段信息.model_id as 模型ID,
模型字段信息.column_id as 字段ID,
模型字段信息.syn_value as 同义词
from t_da_model_column_info 模型字段信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 't_da_model_column_info'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 't_da_model_info', 't_da_model_info', '模型信息', TO_CLOB('
select
模型信息.model_id as 模型ID,
模型信息.syn_value as 同义词
from t_da_model_info 模型信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 't_da_model_info'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 't_da_model_relation', 't_da_model_relation', '模型关系表', TO_CLOB('
select
模型关系表.model_id as 主模型ID,
模型关系表.fields as 主模型字段,
模型关系表.mapped_model_id as 关联模型,
模型关系表.mapped_fields as 关联模型字段
from t_da_model_relation 模型关系表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 't_da_model_relation'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_label', 'tea_label', 'TEA标签宽表', TO_CLOB('
select
TEA标签宽表.nd as 年代,
TEA标签宽表.nld as 年龄段,
TEA标签宽表.hwjl as 海外经历,
TEA标签宽表.xkly as 学科领域,
TEA标签宽表.rclb as 人才类别,
TEA标签宽表.bkxlxy as 本科学历学缘,
TEA标签宽表.yjsxlxy as 研究生学历学缘,
TEA标签宽表.bsxlxy as 博士学历学缘,
TEA标签宽表.xygx as 学缘关系,
TEA标签宽表.hwxx as 海外学习,
TEA标签宽表.dyxlbyyxlx as 第一学历毕业院校类型,
TEA标签宽表.bsbyyxlx as 博士毕业院校类型,
TEA标签宽表.bxgl as 本校工龄,
TEA标签宽表.cygl as 从业工龄,
TEA标签宽表.jsgwlx as 教师岗位类型,
TEA标签宽表.hwgz as 海外工作,
TEA标签宽表.sjt as 双肩挑,
TEA标签宽表.ssxjs as 双师型教师,
TEA标签宽表.zczqxdsp as 职称在全校的水平,
TEA标签宽表.jzjs as 兼职教授,
TEA标签宽表.hwpx as 海外培训,
TEA标签宽表.jxms as 教学名师,
TEA标签宽表.jgdtr as 教改带头人,
TEA标签宽表.gjjylkc as 国家级一流课程,
TEA标签宽表.sbjylkc as 省部级一流课程,
TEA标签宽表.yzkcskz as 优质课程授课者,
TEA标签宽表.xshp as 学生好评,
TEA标签宽表.ddhp as 督导好评,
TEA标签宽表.qrjh as 千人计划,
TEA标签宽表.gjjcqnkxjjxm as 国家杰出青年科学基金项目,
TEA标签宽')||TO_CLOB('表.zjxzjljh as 长江学者奖励计划,
TEA标签宽表.wrjh as 万人计划,
TEA标签宽表.qnqrjh as 青年千人计划,
TEA标签宽表.qnzjxz as 青年长江学者,
TEA标签宽表.yxqnkxjjxm as 优秀青年科学基金项目,
TEA标签宽表.wrqb as 万人青拔,
TEA标签宽表.gbyxz as 高被引学者,
TEA标签宽表.kyjfdr as 科研经费达人,
TEA标签宽表.cggcc as 成果高产出,
TEA标签宽表.kzrq as 快照日期,
TEA标签宽表.zgh as 教职工号
from tea_label TEA标签宽表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_label'
);