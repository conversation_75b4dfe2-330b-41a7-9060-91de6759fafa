/*
 Description		: [智能数据查询(IDQ)]主题域报表表名对照表
 Author				: dataapp
 Date				: 2024-06-20 01:00:00
 Lowcode Version	: V1.1.1_Beta5
 Database           : oracle
*/

BEGIN
    EXECUTE IMMEDIATE 'CREATE TABLE t_table_map (
  id VARCHAR2(100 BYTE),
  table_name VARCHAR2(200 BYTE),
  comments VARCHAR2(500 BYTE),
  sql_str CLOB,
  create_by VARCHAR2(100),
  create_time DATE,
  update_by VARCHAR2(100),
  update_time DATE,PRIMARY KEY (id))';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

COMMENT ON TABLE t_table_map IS '报表模型表名映射';
COMMENT ON COLUMN t_table_map.id IS 'ID';
COMMENT ON COLUMN t_table_map.table_name IS '表名';
COMMENT ON COLUMN t_table_map.comments IS '表注释';
COMMENT ON COLUMN t_table_map.sql_str IS 'sql脚本';
COMMENT ON COLUMN t_table_map.create_by IS '创建人';
COMMENT ON COLUMN t_table_map.create_time IS '创建时间';
COMMENT ON COLUMN t_table_map.update_by IS '更新人';
COMMENT ON COLUMN t_table_map.update_time IS '更新时间';

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_col_xyjbxx', 'abd_col_xyjbxx', '学院基本信息', TO_CLOB('
select
学院基本信息.xybh as 学院编号,
学院基本信息.xymc as 学院名称
from abd_col_xyjbxx 学院基本信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_col_xyjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_col_xyxxx', 'abd_col_xyxxx', '学院系信息', TO_CLOB('
select
学院系信息.xybh as 学院编号,
学院系信息.xbh as 系编号,
学院系信息.xmc as 系名称,
学院系信息.ssxkly as 所属学科领域
from abd_col_xyxxx 学院系信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_col_xyxxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_piw_zgryjbxx', 'abd_piw_zgryjbxx', '政工人员基本信息', TO_CLOB('
select
政工人员基本信息.zgh as 教职工号,
政工人员基本信息.xm as 姓名,
政工人员基本信息.xb as 性别,
政工人员基本信息.csrq as 出生日期,
政工人员基本信息.zp as 照片,
政工人员基本信息.mz as 民族,
政工人员基本信息.zzmm as 政治面貌,
政工人员基本信息.rdrq as 入党日期,
政工人员基本信息.hyzk as 婚姻状况,
政工人员基本信息.csd as 出生地,
政工人员基本信息.xw as 学位,
政工人员基本信息.xl as 学历,
政工人员基本信息.zyjszw as 专业技术职务,
政工人员基本信息.zyjszwjb as 专业技术职务级别,
政工人员基本信息.xzzw as 行政职务,
政工人员基本信息.xzjb as 行政级别,
政工人员基本信息.yrfs as 用人方式,
政工人员基本信息.yxkh as 银行卡号,
政工人员基本信息.lsdwdm as 隶属单位代码,
政工人员基本信息.gzdwdm as 工作单位代码,
政工人员基本信息.xqh as 校区号,
政工人员基本信息.sjhm as 手机号码,
政工人员基本信息.wxzh as 微信账号,
政工人员基本信息.bgdd as 办公地点,
政工人员基本信息.bgdh as 办公电话,
政工人员基本信息.lxdh as 联系电话,
政工人员基本信息.czdz as 常住地址,
政工人员基本信息.dzxx as 电子信箱,
政工人员基本信息.qh as QQ号,
政工人员基本信息.yzbm as 邮政编码,
政工人员基本信息.txdz as 通讯地址,
政工人员基本信息.byyx as 毕业院校,
政工人员基本信息.sxzy as 所')||TO_CLOB('学专业,
政工人员基本信息.cjgzrq as 参加工作日期,
政工人员基本信息.srrq as 首任日期,
政工人员基本信息.lylx as 来源类型,
政工人员基本信息.fdylb as 辅导员类别,
政工人员基本信息.zyzg as 职业资格,
政工人员基本信息.sgrq as 上岗日期,
政工人员基本信息.lgrq as 离岗日期,
政工人员基本信息.sfzg as 是否在岗,
政工人员基本信息.csxsgzksrq as 从事学生工作开始日期,
政工人员基本信息.csxsgzjsrq as 从事学生工作结束日期,
政工人员基本信息.bzlx as 编制类型,
政工人员基本信息.xwlb as 学位类别,
政工人员基本信息.sfbsh as 是否博士后,
政工人员基本信息.gznx as 工作年限,
政工人员基本信息.nl as 年龄,
政工人员基本信息.kzrq as 快照日期
from abd_piw_zgryjbxx 政工人员基本信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_piw_zgryjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_piw_zgryhjjl', 'abd_piw_zgryhjjl', '政工人员获奖记录', TO_CLOB('
select
政工人员获奖记录.zgh as 教职工号,
政工人员获奖记录.jlmc as 奖励名称,
政工人员获奖记录.jljb as 奖励级别,
政工人员获奖记录.bjdw as 颁奖单位,
政工人员获奖记录.hjxn as 获奖学年,
政工人员获奖记录.hjrq as 获奖日期
from abd_piw_zgryhjjl 政工人员获奖记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_piw_zgryhjjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xxjbxx', 'abd_sch_xxjbxx', '学校基本信息', TO_CLOB('
select
学校基本信息.xxbsm as 学校标识码,
学校基本信息.xxmc as 学校名称,
学校基本信息.xxywmc as 学校英文名称,
学校基本信息.xxdz as 学校地址,
学校基本信息.xxyzbm as 学校邮政编码,
学校基本信息.xzqh as 行政区划,
学校基本信息.szdcxlx as 所在地城乡类型,
学校基本信息.jxny as 建校年月,
学校基本信息.xqr as 校庆日,
学校基本信息.xxbxlx as 学校办学类型,
学校基本信息.xxjbz as 学校举办者,
学校基本信息.xxzgbm as 学校主管部门,
学校基本信息.fddbrh as 法定代表人号,
学校基本信息.frzsh as 法人证书号,
学校基本信息.xzxm as 校长姓名,
学校基本信息.dwfzr as 党委负责人,
学校基本信息.zzjgm as 组织机构码,
学校基本信息.lxdh as 联系电话,
学校基本信息.czdh as 传真电话,
学校基本信息.dzxx as 电子信箱,
学校基本信息.xxbb as 学校办别,
学校基本信息.xxxz as 学校性质
from abd_sch_xxjbxx 学校基本信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xxjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xn', 'abd_sch_xn', '学年', TO_CLOB('
select
学年.xxbsm as 学校标识码,
学年.xh as 序号,
学年.xnbm as 学年编码,
学年.xnmc as 学年名称,
学年.xnksrq as 学年开始日期,
学年.xnjsrq as 学年结束日期
from abd_sch_xn 学年'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xn'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xnxq', 'abd_sch_xnxq', '学年学期', TO_CLOB('
select
学年学期.xxbsm as 学校标识码,
学年学期.xh as 序号,
学年学期.xnxqbm as 学年学期编码,
学年学期.xnxqmc as 学年学期名称,
学年学期.xqksrq as 学期开始日期,
学年学期.xqjsrq as 学期结束日期,
学年学期.ssxnbm as 所属学年编码,
学年学期.ssxn as 所属学年
from abd_sch_xnxq 学年学期'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xnxq'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_bzksjxj', 'abd_sch_bzksjxj', '本专科生奖学金', TO_CLOB('
select
本专科生奖学金.xxbsm as 学校标识码,
本专科生奖学金.jxjbm as 奖学金编码,
本专科生奖学金.xnxq as 学年学期,
本专科生奖学金.jxjmc as 奖学金名称,
本专科生奖学金.jxjdj as 奖学金等级,
本专科生奖学金.jxjlx as 奖学金类型,
本专科生奖学金.jljb as 奖励级别,
本专科生奖学金.sldwhgr as 设立单位或个人,
本专科生奖学金.zjly as 资金来源,
本专科生奖学金.jlje as 奖励金额
from abd_sch_bzksjxj 本专科生奖学金'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_bzksjxj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_bzksrych', 'abd_sch_bzksrych', '本专科生荣誉称号', TO_CLOB('
select
本专科生荣誉称号.xxbsm as 学校标识码,
本专科生荣誉称号.rychbm as 荣誉称号编码,
本专科生荣誉称号.rychmc as 荣誉称号名称,
本专科生荣誉称号.sldw as 设立单位,
本专科生荣誉称号.rychlx as 荣誉称号类型,
本专科生荣誉称号.jljb as 奖励级别,
本专科生荣誉称号.xnxq as 学年学期
from abd_sch_bzksrych 本专科生荣誉称号'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_bzksrych'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xqjbxx', 'abd_sch_xqjbxx', '校区基本信息', TO_CLOB('
select
校区基本信息.xxbsm as 学校标识码,
校区基本信息.xqh as 校区号,
校区基本信息.xqmc as 校区名称
from abd_sch_xqjbxx 校区基本信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xqjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_bzkszyxx', 'abd_sch_bzkszyxx', '本专科生专业信息', TO_CLOB('
select
本专科生专业信息.xxbsm as 学校标识码,
本专科生专业信息.xnzydm as 校内专业代码,
本专科生专业信息.xnzymc as 校内专业名称,
本专科生专业信息.zyjc as 专业简称,
本专科生专业信息.zyywmc as 专业英文名称,
本专科生专业信息.ssdwh as 所属单位号,
本专科生专业信息.jbzydm as 教标专业代码,
本专科生专业信息.xz as 学制,
本专科生专业信息.xslb as 学生类别,
本专科生专业信息.xkml as 学科门类
from abd_sch_bzkszyxx 本专科生专业信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_bzkszyxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_bzksxzbjxx', 'abd_sch_bzksxzbjxx', '本专科生行政班级信息', TO_CLOB('
select
本专科生行政班级信息.xxbsm as 学校标识码,
本专科生行政班级信息.bh as 班号,
本专科生行政班级信息.bjmc as 班级名称,
本专科生行政班级信息.szdwbm as 所在单位编码,
本专科生行政班级信息.zybm as 专业编码,
本专科生行政班级信息.nj as 年级
from abd_sch_bzksxzbjxx 本专科生行政班级信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_bzksxzbjxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_bzkszxj', 'abd_sch_bzkszxj', '本专科生助学金', TO_CLOB('
select
本专科生助学金.xxbsm as 学校标识码,
本专科生助学金.zxjbm as 助学金编码,
本专科生助学金.zxjmc as 助学金名称,
本专科生助学金.zxjdj as 助学金等级,
本专科生助学金.jljb as 奖励级别,
本专科生助学金.zxje as 助学金额,
本专科生助学金.zzdwhgr as 资助单位或个人,
本专科生助学金.zjly as 资金来源,
本专科生助学金.jj as 简介,
本专科生助学金.xnxq as 学年学期
from abd_sch_bzkszxj 本专科生助学金'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_bzkszxj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_qgzxgw', 'abd_sch_qgzxgw', '勤工助学岗位', TO_CLOB('
select
勤工助学岗位.xxbsm as 学校标识码,
勤工助学岗位.gwbm as 岗位编码,
勤工助学岗位.gwmc as 岗位名称,
勤工助学岗位.gwlx as 岗位类型,
勤工助学岗位.ygdw as 用工单位,
勤工助学岗位.xqxslx as 需求学生类型,
勤工助学岗位.gwyq as 岗位要求,
勤工助学岗位.gzdd as 工作地点,
勤工助学岗位.bcbz as 报酬标准,
勤工助学岗位.bcjsdw as 报酬计算单位,
勤工助学岗位.xqrs as 需求人数,
勤工助学岗位.xnxq as 学年学期,
勤工助学岗位.gzksrq as 工作开始日期,
勤工助学岗位.gzjsrq as 工作结束日期
from abd_sch_qgzxgw 勤工助学岗位'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_qgzxgw'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_bzksknbz', 'abd_sch_bzksknbz', '本专科生困难补助', TO_CLOB('
select
本专科生困难补助.xxbsm as 学校标识码,
本专科生困难补助.knbzbm as 困难补助编码,
本专科生困难补助.knbzmc as 困难补助名称,
本专科生困难补助.knbzdj as 困难补助等级,
本专科生困难补助.zzdwhgr as 资助单位或个人,
本专科生困难补助.zjly as 资金来源,
本专科生困难补助.bzje as 补助金额,
本专科生困难补助.jj as 简介,
本专科生困难补助.xnxq as 学年学期
from abd_sch_bzksknbz 本专科生困难补助'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_bzksknbz'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgjbxx', 'abd_tea_jzgjbxx', '教职工基本信息', TO_CLOB('
select
教职工基本信息.zgh as 教职工号,
教职工基本信息.xm as 姓名,
教职工基本信息.xb as 性别,
教职工基本信息.mz as 民族,
教职工基本信息.sfzjlx as 身份证件类型,
教职工基本信息.gatqw as 港澳台侨外,
教职工基本信息.zjxy as 宗教信仰,
教职工基本信息.zzmm as 政治面貌,
教职工基本信息.jg as 籍贯,
教职工基本信息.hkszs as 户口所在省,
教职工基本信息.hkszds as 户口所在地市,
教职工基本信息.hkszd as 户口所在地,
教职工基本信息.xx as 血型,
教职工基本信息.csrq as 出生日期,
教职工基本信息.csd as 出生地,
教职工基本信息.gjdq as 国家地区,
教职工基本信息.hyzk as 婚姻状况,
教职工基本信息.zgxl as 最高学历,
教职工基本信息.zgxw as 最高学位,
教职工基本信息.dyxl as 第一学历,
教职工基本信息.dyxlbyyx as 第一学历毕业院校,
教职工基本信息.dyxlbyyxlx as 第一学历毕业院校类型,
教职工基本信息.zgxlbyyx as 最高学历毕业院校,
教职工基本信息.zgxlbyyxlx as 最高学历毕业院校类型,
教职工基本信息.zhbyyx as 最后毕业院校,
教职工基本信息.zhbyyxlx as 最后毕业院校类型,
教职工基本信息.sfbxby as 是否本校毕业,
教职工基本信息.sjhm as 手机号码,
教职工基本信息.dzyx as 电子邮箱,
教职工基本信息.jtdz as 家庭地址,
教职工基本信息.ssjgdm as 所属机构代码,
教职工基本信息.s')||TO_CLOB('sjg as 所属机构,
教职工基本信息.ssxdm as 所属系代码,
教职工基本信息.ssx as 所属系,
教职工基本信息.jzglb as 教职工类别,
教职工基本信息.jzgly as 教职工来源,
教职工基本信息.bzlb as 编制类别,
教职工基本信息.yrfs as 用人方式,
教职工基本信息.cjny as 从教年月,
教职工基本信息.lxny as 来校年月,
教职工基本信息.jzjsprlb as 兼职教师聘任类别,
教职工基本信息.dslb as 导师类别,
教职工基本信息.fdylb as 辅导员类别,
教职工基本信息.sfssxjs as 是否双师型教师,
教职工基本信息.sfsjt as 是否双肩挑,
教职工基本信息.xkml as 学科门类,
教职工基本信息.yjxk as 一级学科,
教职工基本信息.ejxk as 二级学科,
教职工基本信息.yjfx as 研究方向,
教职工基本信息.jzgdqzt as 教职工当前状态,
教职工基本信息.lxrq as 离校日期,
教职工基本信息.yjtxrq as 预计退休日期,
教职工基本信息.zyjszw as 专业技术职务,
教职工基本信息.zyjszwjb as 专业技术职务级别,
教职工基本信息.zyjsgwdj as 专业技术岗位等级,
教职工基本信息.glgwdj as 管理岗位等级,
教职工基本信息.gqgwdj as 工勤岗位等级,
教职工基本信息.zygwlx as 主要岗位类型,
教职工基本信息.gwmc as 岗位名称,
教职工基本信息.gbzw as 干部职务,
教职工基本信息.gbzwjb as 干部职务级别,
教职工基本信息.nl as 年龄,
教职工基本信息.rjlx ')||TO_CLOB('as 任教类型,
教职工基本信息.rjzymc as 任教专业名称,
教职工基本信息.rjzydm as 任教专业代码,
教职工基本信息.zyrjsj as 专业任教时间,
教职工基本信息.sfsyjsry as 是否实验技术人员,
教职工基本信息.sfwp as 是否外聘,
教职工基本信息.glrylb as 管理人员类别,
教职工基本信息.sfbds as 是否班导师,
教职工基本信息.sfskjs as 是否授课教师,
教职工基本信息.sfszkjs as 是否思政课教师,
教职工基本信息.kzrq as 快照日期,
教职工基本信息.jkzk as 健康状况,
教职工基本信息.yjjgdm as 一级机构代码,
教职工基本信息.yjjg as 一级机构,
教职工基本信息.cjgzny as 参加工作年月,
教职工基本信息.sffdy as 是否辅导员,
教职工基本信息.zyjsgwlb as 专业技术岗位类别,
教职工基本信息.zrjslx as 专任教师类型,
教职工基本信息.zgxwcc as 最高学位层次,
教职工基本信息.zyjszwdm as 专业技术职务代码,
教职工基本信息.zyjszwjbdm as 专业技术职务级别代码
from abd_tea_jzgjbxx 教职工基本信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgjyjl', 'abd_tea_jzgjyjl', '教职工教育经历', TO_CLOB('
select
教职工教育经历.zgh as 教职工号,
教职工教育经历.rxny as 入学年月,
教职工教育经历.byny as 毕业年月,
教职工教育经历.byyxxhdw as 毕肄业学校或单位,
教职工教育经历.xl as 学历,
教职工教育经历.xw as 学位,
教职工教育经历.sxzy as 所学专业,
教职工教育经历.xwsygj as 学位授予国家,
教职工教育经历.gxlb as 高校类别,
教职工教育经历.xz as 学制
from abd_tea_jzgjyjl 教职工教育经历'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgjyjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzggzjl', 'abd_tea_jzggzjl', '教职工工作经历', TO_CLOB('
select
教职工工作经历.zgh as 教职工号,
教职工工作经历.szdwmc as 所在单位名称,
教职工工作经历.gjdq as 国家地区,
教职工工作经历.crdzzw as 曾任党政职务,
教职工工作经历.crzyjszw as 曾任专业技术职务,
教职工工作经历.sfhwjl as 是否海外经历,
教职工工作经历.qsny as 起始年月,
教职工工作经历.jzny as 截止年月
from abd_tea_jzggzjl 教职工工作经历'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzggzjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_gccrcxx', 'abd_tea_gccrcxx', '高层次人才信息', TO_CLOB('
select
高层次人才信息.zgh as 教职工号,
高层次人才信息.rclb as 人才类别,
高层次人才信息.pzdw as 批准单位,
高层次人才信息.pzdwjb as 批准单位级别,
高层次人才信息.pzny as 批准年月
from abd_tea_gccrcxx 高层次人才信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_gccrcxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgjzxx', 'abd_tea_jzgjzxx', '教职工兼职信息', TO_CLOB('
select
教职工兼职信息.zgh as 教职工号,
教职工兼职信息.jzlx as 兼职类型,
教职工兼职信息.jzdw as 兼职单位,
教职工兼职信息.jzdwjb as 兼职单位级别,
教职工兼职信息.jzzw as 兼职职务,
教职工兼职信息.jzzwjb as 兼职职务级别,
教职工兼职信息.gjdq as 国家地区,
教职工兼职信息.qsrq as 起始日期,
教职工兼职信息.zzrq as 终止日期
from abd_tea_jzgjzxx 教职工兼职信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgjzxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_gbzwxx', 'abd_tea_gbzwxx', '干部职务信息', TO_CLOB('
select
干部职务信息.zgh as 教职工号,
干部职务信息.zwmc as 职务名称,
干部职务信息.zwlb as 职务类别,
干部职务信息.zwjb as 职务级别,
干部职务信息.rzdw as 任职单位,
干部职务信息.rzny as 任职年月,
干部职务信息.rzfs as 任职方式,
干部职务信息.rzqx as 任职期限,
干部职务信息.sfzr as 是否在任,
干部职务信息.mzrq as 免职日期,
干部职务信息.rzdwbm as 任职单位编码
from abd_tea_gbzwxx 干部职务信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_gbzwxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgjljryxx', 'abd_tea_jzgjljryxx', '教职工奖励及荣誉信息', TO_CLOB('
select
教职工奖励及荣誉信息.zgh as 教职工号,
教职工奖励及荣誉信息.jlmc as 奖励名称,
教职工奖励及荣誉信息.rych as 荣誉称号,
教职工奖励及荣誉信息.hjrq as 获奖日期,
教职工奖励及荣誉信息.jljb as 奖励级别,
教职工奖励及荣誉信息.jldj as 奖励等级,
教职工奖励及荣誉信息.jllb as 奖励类别,
教职工奖励及荣誉信息.bjdw as 颁奖单位,
教职工奖励及荣誉信息.hjxm as 获奖项目,
教职工奖励及荣誉信息.brpm as 本人排名
from abd_tea_jzgjljryxx 教职工奖励及荣誉信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgjljryxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_gnpx', 'abd_tea_gnpx', '国内培训', TO_CLOB('
select
国内培训.zgh as 教职工号,
国内培训.pxxmmc as 培训项目名称,
国内培训.pxjgmc as 培训机构名称,
国内培训.pxjb as 培训级别,
国内培训.pxfs as 培训方式,
国内培训.pxhdxs as 培训获得学时,
国内培训.pxnd as 培训年度
from abd_tea_gnpx 国内培训'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_gnpx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_hwyx', 'abd_tea_hwyx', '海外研修', TO_CLOB('
select
海外研修.zgh as 教职工号,
海外研修.ksrq as 开始日期,
海外研修.jsrq as 结束日期,
海外研修.gjdq as 国家地区,
海外研修.yxfxjg as 研修访学机构,
海外研修.xmmc as 项目名称,
海外研修.xmzzdw as 项目组织单位
from abd_tea_hwyx 海外研修'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_hwyx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzggwpyjl', 'abd_tea_jzggwpyjl', '教职工岗位聘用记录', TO_CLOB('
select
教职工岗位聘用记录.zgh as 教职工号,
教职工岗位聘用记录.gwmc as 岗位名称,
教职工岗位聘用记录.prqsrq as 聘任起始日期,
教职工岗位聘用记录.przzrq as 聘任终止日期,
教职工岗位聘用记录.sjprjsrq as 实际聘任结束日期,
教职工岗位聘用记录.sfzr as 是否在任
from abd_tea_jzggwpyjl 教职工岗位聘用记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzggwpyjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzggwdjpyjl', 'abd_tea_jzggwdjpyjl', '教职工岗位等级聘用记录', TO_CLOB('
select
教职工岗位等级聘用记录.zgh as 教职工号,
教职工岗位等级聘用记录.gwlx as 岗位类型,
教职工岗位等级聘用记录.gwdj as 岗位等级,
教职工岗位等级聘用记录.spdw as 受聘单位,
教职工岗位等级聘用记录.prqsrq as 聘任起始日期,
教职工岗位等级聘用记录.przzrq as 聘任终止日期
from abd_tea_jzggwdjpyjl 教职工岗位等级聘用记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzggwdjpyjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_zcppjl', 'abd_tea_zcppjl', '职称评聘记录', TO_CLOB('
select
职称评聘记录.zgh as 教职工号,
职称评聘记录.przyjszw as 聘任专业技术职务,
职称评聘记录.przyjszwjb as 聘任专业技术职务级别,
职称评聘记录.prqsrq as 聘任起始日期,
职称评聘记录.przzrq as 聘任终止日期,
职称评聘记录.prdw as 聘任单位,
职称评聘记录.pdzyjszw as 评定专业技术职务,
职称评聘记录.pdzyjszwjb as 评定专业技术职务级别,
职称评聘记录.pdrq as 评定日期,
职称评聘记录.psdw as 评审单位
from abd_tea_zcppjl 职称评聘记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_zcppjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgxnddjl', 'abd_tea_jzgxnddjl', '教职工校内调动记录', TO_CLOB('
select
教职工校内调动记录.zgh as 教职工号,
教职工校内调动记录.dcdw as 调出单位,
教职工校内调动记录.drdw as 调入单位,
教职工校内调动记录.ddrq as 调动日期,
教职工校内调动记录.sfzg as 是否转岗,
教职工校内调动记录.dcgw as 调出岗位,
教职工校内调动记录.drgw as 调入岗位,
教职工校内调动记录.dcdwbm as 调出单位编码,
教职工校内调动记录.drdwbm as 调入单位编码
from abd_tea_jzgxnddjl 教职工校内调动记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgxnddjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgndkhjl', 'abd_tea_jzgndkhjl', '教职工年度考核记录', TO_CLOB('
select
教职工年度考核记录.zgh as 教职工号,
教职工年度考核记录.khdw as 考核单位,
教职工年度考核记录.khmc as 考核名称,
教职工年度考核记录.khlb as 考核类别,
教职工年度考核记录.khnf as 考核年份,
教职工年度考核记录.khjl as 考核结论
from abd_tea_jzgndkhjl 教职工年度考核记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgndkhjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzglgjl', 'abd_tea_jzglgjl', '教职工离岗记录', TO_CLOB('
select
教职工离岗记录.zgh as 教职工号,
教职工离岗记录.lgyy as 离岗原因,
教职工离岗记录.lgrq as 离岗日期,
教职工离岗记录.fgrq as 返岗日期,
教职工离岗记录.sqdwmc as 所去单位名称
from abd_tea_jzglgjl 教职工离岗记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzglgjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzglxjl', 'abd_tea_jzglxjl', '教职工离校记录', TO_CLOB('
select
教职工离校记录.zgh as 教职工号,
教职工离校记录.lxyy as 离校原因,
教职工离校记录.lxrq as 离校日期,
教职工离校记录.lxqx as 离校去向
from abd_tea_jzglxjl 教职工离校记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzglxjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgcgjjl', 'abd_tea_jzgcgjjl', '教职工出国境记录', TO_CLOB('
select
教职工出国境记录.zgh as 教职工号,
教职工出国境记录.cgjgb as 出国境国别,
教职工出国境记录.sqdwywmc as 所去单位英文名称,
教职工出国境记录.sqdwzwmc as 所去单位中文名称,
教职工出国境记录.tzmc as 团组名称,
教职工出国境记录.jfly as 经费来源,
教职工出国境记录.spdw as 审批单位,
教职工出国境记录.sprq as 审批日期,
教职工出国境记录.spwh as 审批文号,
教职工出国境记录.cgjmd as 出国境目的,
教职工出国境记录.xxgznr as 学习工作内容,
教职工出国境记录.hzhhtxzh as 护照号或通行证号,
教职工出国境记录.cgjrq as 出国境日期,
教职工出国境记录.hgrq as 回国日期,
教职工出国境记录.xxgzcj as 学习工作成绩,
教职工出国境记录.xxcdfy as 学校承担费用
from abd_tea_jzgcgjjl 教职工出国境记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgcgjjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgltxjl', 'abd_tea_jzgltxjl', '教职工离退休记录', TO_CLOB('
select
教职工离退休记录.zgh as 教职工号,
教职工离退休记录.ltlb as 离退类别,
教职工离退休记录.ltrq as 离退日期,
教职工离退休记录.lthxsjb as 离退后享受级别,
教职工离退休记录.lthgldw as 离退后管理单位,
教职工离退休记录.ltxfzfdw as 离退休费支付单位,
教职工离退休记录.ydazdd as 异地安置地点
from abd_tea_jzgltxjl 教职工离退休记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgltxjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgqsjl', 'abd_tea_jzgqsjl', '教职工去世记录', TO_CLOB('
select
教职工去世记录.zgh as 教职工号,
教职工去世记录.qsrq as 去世日期,
教职工去世记录.qsdd as 去世地点,
教职工去世记录.qsyy as 去世原因,
教职工去世记录.qslb as 去世类别,
教职工去世记录.qslx as 去世类型,
教职工去世记录.szbzj as 丧葬补助金,
教职工去世记录.ycxfxj as 一次性抚恤金
from abd_tea_jzgqsjl 教职工去世记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgqsjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgfpjl', 'abd_tea_jzgfpjl', '教职工返聘记录', TO_CLOB('
select
教职工返聘记录.zgh as 教职工号,
教职工返聘记录.fpdwbm as 返聘单位编码,
教职工返聘记录.fpdw as 返聘单位,
教职工返聘记录.fpqsrq as 返聘起始日期,
教职工返聘记录.fpzzrq as 返聘终止日期,
教职工返聘记录.gwmc as 岗位名称,
教职工返聘记录.fpcj as 返聘酬金,
教职工返聘记录.fpjly as 返聘金来源
from abd_tea_jzgfpjl 教职工返聘记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgfpjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzksjbxx', 'abd_und_bzksjbxx', '本专科生基本信息', TO_CLOB('
select
本专科生基本信息.xh as 学号,
本专科生基本信息.xm as 姓名,
本专科生基本信息.xmpy as 姓名拼音,
本专科生基本信息.cym as 曾用名,
本专科生基本信息.xb as 性别,
本专科生基本信息.nl as 年龄,
本专科生基本信息.xx as 血型,
本专科生基本信息.csrq as 出生日期,
本专科生基本信息.csd as 出生地,
本专科生基本信息.jg as 籍贯,
本专科生基本信息.gjdq as 国家地区,
本专科生基本信息.mz as 民族,
本专科生基本信息.sfzjlx as 身份证件类型,
本专科生基本信息.hyzk as 婚姻状况,
本专科生基本信息.jkzk as 健康状况,
本专科生基本信息.gatqw as 港澳台侨外,
本专科生基本信息.zjxy as 宗教信仰,
本专科生基本信息.zzmm as 政治面貌,
本专科生基本信息.sstzz as 所属团组织,
本专科生基本信息.ssdzz as 所属党组织,
本专科生基本信息.yktkh as 一卡通卡号,
本专科生基本信息.wlzh as 网络账号,
本专科生基本信息.yxzh as 邮箱账号,
本专科生基本信息.xqh as 校区号,
本专科生基本信息.xq as 校区,
本专科生基本信息.xybm as 学院编码,
本专科生基本信息.xy as 学院,
本专科生基本信息.bh as 班号,
本专科生基本信息.bj as 班级,
本专科生基本信息.zybm as 专业编码,
本专科生基本信息.zy as 专业,
本专科生基本信息.nj as 年级,
本专科生基本信息.ssq as 宿舍区,
本专科生基本信息.ssl as 宿舍楼,
本专科生基本信息.s')||TO_CLOB('sdz as 宿舍地址,
本专科生基本信息.rxzp as 入学照片,
本专科生基本信息.xslb as 学生类别,
本专科生基本信息.pyfs as 培养方式,
本专科生基本信息.pycc as 培养层次,
本专科生基本信息.pyfa as 培养方案,
本专科生基本信息.xz as 学制,
本专科生基本信息.yjbyrq as 预计毕业日期,
本专科生基本信息.xjzt as 学籍状态,
本专科生基本信息.xsdqzt as 学生当前状态,
本专科生基本信息.dszgh as 导师职工号,
本专科生基本信息.kzrq as 快照日期
from abd_und_bzksjbxx 本专科生基本信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzksjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzksxsxx', 'abd_und_bzksxsxx', '本专科生新生信息', TO_CLOB('
select
本专科生新生信息.xh as 学号,
本专科生新生信息.ksh as 考生号,
本专科生新生信息.kslb as 考生类别,
本专科生新生信息.lqlb as 录取类别,
本专科生新生信息.tzsh as 通知书号,
本专科生新生信息.rxny as 入学年月,
本专科生新生信息.rxjj as 入学季节,
本专科生新生信息.rxfs as 入学方式,
本专科生新生信息.rxnl as 入学年龄,
本专科生新生信息.sydsf as 生源地省份,
本专科生新生信息.gkzf as 高考总分,
本专科生新生信息.hkszd as 户口所在地,
本专科生新生信息.rxqdw as 入学前单位,
本专科生新生信息.dxhwpdw as 定向或委培单位,
本专科生新生信息.gkzp as 高考照片,
本专科生新生信息.syd as 生源地
from abd_und_bzksxsxx 本专科生新生信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzksxsxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzkszhcpjg', 'abd_und_bzkszhcpjg', '本专科生综合测评结果', TO_CLOB('
select
本专科生综合测评结果.xh as 学号,
本专科生综合测评结果.xnxq as 学年学期,
本专科生综合测评结果.cprq as 测评日期,
本专科生综合测评结果.cpzcj as 测评总成绩,
本专科生综合测评结果.dj as 等级,
本专科生综合测评结果.cpzcjpm as 测评总成绩排名,
本专科生综合测评结果.cpzcjbjpm as 测评总成绩班级排名,
本专科生综合测评结果.cpzcjzypm as 测评总成绩专业排名
from abd_und_bzkszhcpjg 本专科生综合测评结果'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzkszhcpjg'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzkshdjxjjl', 'abd_und_bzkshdjxjjl', '本专科生获得奖学金记录', TO_CLOB('
select
本专科生获得奖学金记录.xh as 学号,
本专科生获得奖学金记录.jxjbm as 奖学金编码,
本专科生获得奖学金记录.jxjmc as 奖学金名称,
本专科生获得奖学金记录.jxjlx as 奖学金类型,
本专科生获得奖学金记录.jljb as 奖励级别,
本专科生获得奖学金记录.jxjdj as 奖学金等级,
本专科生获得奖学金记录.xnxq as 学年学期,
本专科生获得奖学金记录.hjje as 获奖金额,
本专科生获得奖学金记录.ffrq as 发放日期,
本专科生获得奖学金记录.ffje as 发放金额
from abd_und_bzkshdjxjjl 本专科生获得奖学金记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzkshdjxjjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzkshdrychjl', 'abd_und_bzkshdrychjl', '本专科生获得荣誉称号记录', TO_CLOB('
select
本专科生获得荣誉称号记录.xh as 学号,
本专科生获得荣誉称号记录.rychbm as 荣誉称号编码,
本专科生获得荣誉称号记录.rychmc as 荣誉称号名称,
本专科生获得荣誉称号记录.sldw as 设立单位,
本专科生获得荣誉称号记录.rychlx as 荣誉称号类型,
本专科生获得荣誉称号记录.jljb as 奖励级别,
本专科生获得荣誉称号记录.xnxq as 学年学期,
本专科生获得荣誉称号记录.xy as 学院,
本专科生获得荣誉称号记录.hjje as 获奖金额
from abd_und_bzkshdrychjl 本专科生获得荣誉称号记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzkshdrychjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzkswjcf', 'abd_und_bzkswjcf', '本专科生违纪处分', TO_CLOB('
select
本专科生违纪处分.xh as 学号,
本专科生违纪处分.wjlb as 违纪类别,
本专科生违纪处分.xnxq as 学年学期,
本专科生违纪处分.wjrq as 违纪日期,
本专科生违纪处分.wjqkms as 违纪情况描述,
本专科生违纪处分.cfmc as 处分名称,
本专科生违纪处分.cfyy as 处分原因,
本专科生违纪处分.cfwh as 处分文号,
本专科生违纪处分.ckqy as 察看期月,
本专科生违纪处分.ckjzrq as 察看截止日期,
本专科生违纪处分.ssrq as 申诉日期,
本专科生违纪处分.ssjg as 申诉结果,
本专科生违纪处分.cfsfcx as 处分是否撤销,
本专科生违纪处分.cfcxrq as 处分撤消日期,
本专科生违纪处分.cfcxwh as 处分撤消文号,
本专科生违纪处分.cfcxyy as 处分撤消原因,
本专科生违纪处分.cfsfjc as 处分是否解除,
本专科生违纪处分.cfjcrq as 处分解除日期,
本专科生违纪处分.cfjcwh as 处分解除文号,
本专科生违纪处分.cfjcyy as 处分解除原因
from abd_und_bzkswjcf 本专科生违纪处分'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzkswjcf'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzksknsxx', 'abd_und_bzksknsxx', '本专科生困难生信息', TO_CLOB('
select
本专科生困难生信息.xh as 学号,
本专科生困难生信息.rdxn as 认定学年,
本专科生困难生信息.rdrq as 认定日期,
本专科生困难生信息.rdknslx as 认定困难生类型
from abd_und_bzksknsxx 本专科生困难生信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzksknsxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzkshdzxjjl', 'abd_und_bzkshdzxjjl', '本专科生获得助学金记录', TO_CLOB('
select
本专科生获得助学金记录.xh as 学号,
本专科生获得助学金记录.zxjbm as 助学金编码,
本专科生获得助学金记录.zxjmc as 助学金名称,
本专科生获得助学金记录.jljb as 奖励级别,
本专科生获得助学金记录.zxjdj as 助学金等级,
本专科生获得助学金记录.zxje as 助学金额,
本专科生获得助学金记录.zzdwhgr as 资助单位或个人,
本专科生获得助学金记录.zjly as 资金来源,
本专科生获得助学金记录.xnxq as 学年学期
from abd_und_bzkshdzxjjl 本专科生获得助学金记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzkshdzxjjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzkszxjffjl', 'abd_und_bzkszxjffjl', '本专科生助学金发放记录', TO_CLOB('
select
本专科生助学金发放记录.xh as 学号,
本专科生助学金发放记录.zxjbm as 助学金编码,
本专科生助学金发放记录.zxjmc as 助学金名称,
本专科生助学金发放记录.zzdwhgr as 资助单位或个人,
本专科生助学金发放记录.zjly as 资金来源,
本专科生助学金发放记录.jljb as 奖励级别,
本专科生助学金发放记录.zxjdj as 助学金等级,
本专科生助学金发放记录.xnxq as 学年学期,
本专科生助学金发放记录.ffrq as 发放日期,
本专科生助学金发放记录.ffje as 发放金额
from abd_und_bzkszxjffjl 本专科生助学金发放记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzkszxjffjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzkshdknbzjl', 'abd_und_bzkshdknbzjl', '本专科生获得困难补助记录', TO_CLOB('
select
本专科生获得困难补助记录.xh as 学号,
本专科生获得困难补助记录.knbzbm as 困难补助编码,
本专科生获得困难补助记录.knbzmc as 困难补助名称,
本专科生获得困难补助记录.knbzdj as 困难补助等级,
本专科生获得困难补助记录.zjly as 资金来源,
本专科生获得困难补助记录.xnxq as 学年学期,
本专科生获得困难补助记录.bzje as 补助金额
from abd_und_bzkshdknbzjl 本专科生获得困难补助记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzkshdknbzjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzkshdzxdkjl', 'abd_und_bzkshdzxdkjl', '本专科生获得助学贷款记录', TO_CLOB('
select
本专科生获得助学贷款记录.xh as 学号,
本专科生获得助学贷款记录.jbxn as 经办学年,
本专科生获得助学贷款记录.dklx as 贷款类型,
本专科生获得助学贷款记录.dkhth as 贷款合同号,
本专科生获得助学贷款记录.dkbm as 贷款编码,
本专科生获得助学贷款记录.dkze as 贷款总额,
本专科生获得助学贷款记录.dknx as 贷款年限,
本专科生获得助学贷款记录.fkzt as 放款状态
from abd_und_bzkshdzxdkjl 本专科生获得助学贷款记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzkshdzxdkjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzkssscqxx', 'abd_und_bzkssscqxx', '本专科生宿舍查寝信息', TO_CLOB('
select
本专科生宿舍查寝信息.xh as 学号,
本专科生宿舍查寝信息.xn as 学年,
本专科生宿舍查寝信息.kqrq as 考勤日期,
本专科生宿舍查寝信息.gqlx as 归寝类型,
本专科生宿舍查寝信息.gqsj as 归寝时间,
本专科生宿舍查寝信息.wgyy as 晚归原因
from abd_und_bzkssscqxx 本专科生宿舍查寝信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzkssscqxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzksyktxfxx', 'abd_und_bzksyktxfxx', '本专科生一卡通消费信息', TO_CLOB('
select
本专科生一卡通消费信息.xh as 学号,
本专科生一卡通消费信息.kh as 卡号,
本专科生一卡通消费信息.zhye as 账户余额,
本专科生一卡通消费信息.kzt as 卡状态,
本专科生一卡通消费信息.lsh as 流水号,
本专科生一卡通消费信息.shmc as 商户名称,
本专科生一卡通消费信息.shlx as 商户类型,
本专科生一卡通消费信息.sbbh as 设备编号,
本专科生一卡通消费信息.jysj as 交易时间,
本专科生一卡通消费信息.jylx as 交易类型,
本专科生一卡通消费信息.xfje as 消费金额,
本专科生一卡通消费信息.ljsycs as 累计使用次数
from abd_und_bzksyktxfxx 本专科生一卡通消费信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzksyktxfxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzksjyxx', 'abd_und_bzksjyxx', '本专科生就业信息', TO_CLOB('
select
本专科生就业信息.xh as 学号,
本专科生就业信息.xysbh as 协议书编号,
本专科生就业信息.xyqdrq as 协议签订日期,
本专科生就业信息.xynx as 协议年限,
本专科生就业信息.jylsfs as 就业落实方式,
本专科生就业信息.byqx as 毕业去向,
本专科生就业信息.jydwhsxxx as 就业单位或升学学校,
本专科生就业信息.dwhxxszgjhdq as 单位或学校所在国家或地区,
本专科生就业信息.dwhxxszsf as 单位或学校所在省份,
本专科生就业信息.dwhxxszcs as 单位或学校所在城市,
本专科生就业信息.dwhxxzgbm as 单位或学校主管部门,
本专科生就业信息.shdwxz as 社会单位性质,
本专科生就业信息.dwjjxz as 单位经济性质,
本专科生就业信息.dwtgddy as 单位提供的待遇,
本专科生就业信息.dajsdz as 档案接收地址,
本专科生就业信息.jsdyzbm as 接收地邮政编码,
本专科生就业信息.gzgwxz as 工作岗位性质,
本专科生就业信息.dwxy as 单位行业,
本专科生就业信息.bdzh as 报到证号,
本专科生就业信息.yrdwyrxs as 用人单位用人形式
from abd_und_bzksjyxx 本专科生就业信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzksjyxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzksqgzxsgjl', 'abd_und_bzksqgzxsgjl', '本专科生勤工助学上岗记录', TO_CLOB('
select
本专科生勤工助学上岗记录.xh as 学号,
本专科生勤工助学上岗记录.xnxq as 学年学期,
本专科生勤工助学上岗记录.gwbm as 岗位编码,
本专科生勤工助学上岗记录.sfxndw as 是否校内单位,
本专科生勤工助学上岗记录.gwmc as 岗位名称,
本专科生勤工助学上岗记录.gwlx as 岗位类型,
本专科生勤工助学上岗记录.ygdw as 用工单位,
本专科生勤工助学上岗记录.xqxslx as 需求学生类型,
本专科生勤工助学上岗记录.gwyq as 岗位要求,
本专科生勤工助学上岗记录.gzdd as 工作地点,
本专科生勤工助学上岗记录.bcbz as 报酬标准,
本专科生勤工助学上岗记录.bcjsdw as 报酬计算单位,
本专科生勤工助学上岗记录.gzksrq as 工作开始日期,
本专科生勤工助学上岗记录.gzjsrq as 工作结束日期,
本专科生勤工助学上岗记录.gs as 工时,
本专科生勤工助学上岗记录.zgzt as 在岗状态
from abd_und_bzksqgzxsgjl 本专科生勤工助学上岗记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzksqgzxsgjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzksqgzxbcffjl', 'abd_und_bzksqgzxbcffjl', '本专科生勤工助学报酬发放记录', TO_CLOB('
select
本专科生勤工助学报酬发放记录.xh as 学号,
本专科生勤工助学报酬发放记录.xnxq as 学年学期,
本专科生勤工助学报酬发放记录.gwmc as 岗位名称,
本专科生勤工助学报酬发放记录.sfxndw as 是否校内单位,
本专科生勤工助学报酬发放记录.gwlx as 岗位类型,
本专科生勤工助学报酬发放记录.gwbm as 岗位编码,
本专科生勤工助学报酬发放记录.sfje as 实发金额,
本专科生勤工助学报酬发放记录.ffrq as 发放日期
from abd_und_bzksqgzxbcffjl 本专科生勤工助学报酬发放记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzksqgzxbcffjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzksknbzffjl', 'abd_und_bzksknbzffjl', '本专科生困难补助发放记录', TO_CLOB('
select
本专科生困难补助发放记录.xh as 学号,
本专科生困难补助发放记录.knbzbm as 困难补助编码,
本专科生困难补助发放记录.knbzmc as 困难补助名称,
本专科生困难补助发放记录.knbzdj as 困难补助等级,
本专科生困难补助发放记录.zjly as 资金来源,
本专科生困难补助发放记录.xnxq as 学年学期,
本专科生困难补助发放记录.ffrq as 发放日期,
本专科生困难补助发放记录.ffje as 发放金额
from abd_und_bzksknbzffjl 本专科生困难补助发放记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzksknbzffjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzksrwjl', 'abd_und_bzksrwjl', '本专科生入伍记录', TO_CLOB('
select
本专科生入伍记录.xh as 学号,
本专科生入伍记录.rwtzsbh as 入伍通知书编号,
本专科生入伍记录.rwrq as 入伍日期,
本专科生入伍记录.rwlb as 入伍类别,
本专科生入伍记录.rwddlb as 入伍地点类别,
本专科生入伍记录.zbbgs as 征兵办公室,
本专科生入伍记录.bylb as 兵役类别,
本专科生入伍记录.tyrq as 退役日期,
本专科生入伍记录.fxrq as 复学日期,
本专科生入伍记录.fxjdnx as 复学就读年限
from abd_und_bzksrwjl 本专科生入伍记录'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzksrwjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'col_label', 'col_label', 'COL标签宽表', TO_CLOB('
select
COL标签宽表.kzrq as 快照日期,
COL标签宽表.xybh as 学院编号
from col_label COL标签宽表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'col_label'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'piw_label', 'piw_label', 'PIW标签宽表', TO_CLOB('
select
PIW标签宽表.kzrq as 快照日期,
PIW标签宽表.zgh as 教职工号
from piw_label PIW标签宽表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'piw_label'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'sch_label', 'sch_label', 'SCH标签宽表', TO_CLOB('
select
SCH标签宽表.kzrq as 快照日期,
SCH标签宽表.xxbsm as 学校标识码
from sch_label SCH标签宽表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'sch_label'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 't_da_model_column_extend', 't_da_model_column_extend', '', TO_CLOB('
select
t_da_model_column_extend.primary_column_tag as 是否主键字段
from t_da_model_column_extend t_da_model_column_extend'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 't_da_model_column_extend'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 't_da_model_column_info', 't_da_model_column_info', '模型字段信息', TO_CLOB('
select
模型字段信息.model_id as 模型ID,
模型字段信息.column_id as 字段ID,
模型字段信息.syn_value as 同义词
from t_da_model_column_info 模型字段信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 't_da_model_column_info'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 't_da_model_info', 't_da_model_info', '模型信息', TO_CLOB('
select
模型信息.model_id as 模型ID,
模型信息.syn_value as 同义词
from t_da_model_info 模型信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 't_da_model_info'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 't_da_model_relation', 't_da_model_relation', '模型关系表', TO_CLOB('
select
模型关系表.model_id as 主模型ID,
模型关系表.fields as 主模型字段,
模型关系表.mapped_model_id as 关联模型,
模型关系表.mapped_fields as 关联模型字段
from t_da_model_relation 模型关系表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 't_da_model_relation'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_label', 'tea_label', 'TEA标签宽表', TO_CLOB('
select
TEA标签宽表.kzrq as 快照日期,
TEA标签宽表.zgh as 教职工号
from tea_label TEA标签宽表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_label'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_label', 'und_label', 'UND标签宽表', TO_CLOB('
select
UND标签宽表.kzrq as 快照日期,
UND标签宽表.xh as 学号
from und_label UND标签宽表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_label'
);