DECLARE
BEGIN
EXECUTE IMMEDIATE 'ALTER TABLE LOWCODE_MODEL_COLUMN ADD SEARCH_USE_DEFAULT VARCHAR2(10 BYTE)';
EXCEPTION
WHEN OTHERS THEN
 IF sqlcode != -1430 THEN RAISE; END IF;
END;
/

--SELECT CHECK_UPDATE('2.11111113.0.29', '10.0.0.30') FROM dual;
-- -1：需要升级
-- 0或1：无需升级

CREATE OR REPLACE FUNCTION check_update(version1 VARCHAR2, version2 VARCHAR2)
    RETURN NUMBER
IS
    v_parts1 SYS.ODCINUMBERLIST;
    v_parts2 SYS.ODCINUMBERLIST;
    v_max_length NUMBER;
    v_version1 VARCHAR2(255);
    v_version2 VARCHAR2(255);
BEGIN
    -- 处理空值
    v_version1 := NVL(version1, '0');
    v_version2 := NVL(version2, '0');

    -- 将版本号字符串拆分成数字数组
    SELECT REGEXP_SUBSTR(v_version1, '[^.]+', 1, LEVEL)
    BULK COLLECT INTO v_parts1
    FROM DUAL
    CONNECT BY REGEXP_SUBSTR(v_version1, '[^.]+', 1, LEVEL) IS NOT NULL;

    SELECT REGEXP_SUBSTR(v_version2, '[^.]+', 1, LEVEL)
    BULK COLLECT INTO v_parts2
    FROM DUAL
    CONNECT BY REGEXP_SUBSTR(v_version2, '[^.]+', 1, LEVEL) IS NOT NULL;

    -- 确定最大长度
    v_max_length := GREATEST(v_parts1.COUNT, v_parts2.COUNT);

    -- 逐位比较版本号
    FOR i IN 1..v_max_length LOOP
        DECLARE
            v_num1 NUMBER;
            v_num2 NUMBER;
        BEGIN
            -- 检查索引是否超出集合范围
            IF i <= v_parts1.COUNT THEN
                v_num1 := v_parts1(i);
            ELSE
                v_num1 := 0;
            END IF;

            IF i <= v_parts2.COUNT THEN
                v_num2 := v_parts2(i);
            ELSE
                v_num2 := 0;
            END IF;

            IF v_num1 > v_num2 THEN
                -- 当前版本>更新版本，无需升级
                RETURN 1;
            ELSIF v_num1 < v_num2 THEN
                -- 当前版本<更新版本，需要升级
                RETURN -1;
            END IF;
        END;
    END LOOP;

    -- 版本号相等，无需升级
    RETURN 0;
END;
