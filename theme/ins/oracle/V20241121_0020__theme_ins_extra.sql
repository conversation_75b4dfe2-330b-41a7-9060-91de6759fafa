/*
 Description		: [教学(INS)]主题域手动创建视图
 Author				: dataapp
 Date				: 2024-05-26 01:00:00
 Lowcode Version	: V1.1.1_Beta3
 Database           : oracle
*/

-- 视图 INS_V_SKJS
BEGIN
	EXECUTE IMMEDIATE 'CREATE VIEW INS_V_SKJS AS
        SELECT JZGH AS GH,XM,XB,CSRQ AS CSNY,RXSJ,RZZT,DWH,DWMC,XL,ZGXW,XYMC,ZYJSZC,XKLB,ZZMM,GJ,ID,
            CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,TJNF,NLD
        FROM INS_JZGJBXX a WHERE a.RZZT = ''在职'' AND
        EXISTS(SELECT 1 FROM INS_KKQK ik WHERE ik.SKJSGH = a.JZGH AND ik.TJNF = a.TJNF)';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

-- 视图 INS_V_ZRJS
-- BEGIN
-- 	EXECUTE IMMEDIATE 'CREATE VIEW INS_V_ZRJS AS
--         SELECT a.JZGH as GH,a.XM,a.XB,a.CSRQ AS CSNY,a.RXSJ,a.RZZT,a.DWH,a.DWMC,a.XL,a.ZGXW,a.XYMC,a.ZYJSZC,
--             a.XKLB,a.ZZMM,a.GJ,a.ID,a.CREATE_BY,a.CREATE_TIME,a.UPDATE_BY,a.UPDATE_TIME,a.TJNF,a.NLD,
--             b.RJLX,b.ZYMC,b.RJZYDM,b.ZYRJSJ,b.SFSYJSRY,b.SFSSSNX,b.SFGCBJ,b.SFHYBJ,b.SFJYGWYNYS,b.SFWKCSZYXJS
--         FROM INS_JZGJBXX a
--         LEFT JOIN INS_JZGQTXX b ON a.JZGH = b.GH AND a.TJNF = b.TJNF
--         WHERE a.RZZT = ''在职'' AND b.SFSYJSRY = ''否'' AND b.RJLX !=''无任教''
--             AND EXISTS (SELECT 1 FROM ABD_SCH_ZZJGXX dw WHERE dw.JGDM=a.DWH)
--             AND NOT EXISTS (SELECT 1 FROM INS_XLDJBXX LD WHERE LD.GH = a.JZGH AND a.TJNF =LD.TJNF)
--             AND NOT EXISTS (SELECT 1 FROM INS_XGGLRYJBXX GL WHERE GL.GH = a.JZGH AND GL.GLRYLB !=''本科生辅导员'' AND GL.GLRYLB !=''其他专职辅导员'' AND a.TJNF =GL.TJNF)';
-- EXCEPTION
-- WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
-- END IF;
-- END;
-- /

-- 视图 V_INS_XNXQ
BEGIN
EXECUTE IMMEDIATE 'CREATE VIEW V_INS_XNXQ AS
    SELECT T.XNBM,
        T.XNMC,
        T.XNXQBM,
        T.XNXQMC,
        DENSE_RANK() OVER(ORDER BY T.XNBM DESC) AS PX,
        ROW_NUMBER() OVER(ORDER BY T.XNXQBM DESC) AS XNXQPX
    FROM (SELECT DISTINCT T_DA_XL.XNBM,
        T_DA_XL.XNMC,
        T_DA_XL.XNXQBM,
        T_DA_XL.XNXQMC
    FROM T_DA_XL
    WHERE RQ <= TO_CHAR(SYSDATE, ''YYYY-MM-DD'')) T';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

-- 视图 V_INS_DICT_BJGXFFW
BEGIN
EXECUTE IMMEDIATE 'CREATE VIEW V_INS_DICT_BJGXFFW AS
    SELECT ''8-16分'' AS mc, 8 AS zxz, 16 AS zdz, 1 AS px
    FROM dual
    UNION ALL
    SELECT ''16-25分'' AS mc, 16 AS zxz, 25 AS zdz, 2 AS px
    FROM dual
    UNION ALL
    SELECT ''25分及以上'' AS mc, 25 AS zxz, 1000000 AS zdz, 3 AS px
    FROM dual';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

-- 视图 V_INS_DICT_NLD
BEGIN
EXECUTE IMMEDIATE 'CREATE VIEW V_INS_DICT_NLD AS
    SELECT ITEM_NAME AS MC, SHOW_ORDER AS PX
    FROM LOWCODE_DICT_ITEM WHERE DICT_CODE = ''NLD''';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

-- 视图 V_INS_DICT_XMJB
BEGIN
EXECUTE IMMEDIATE 'CREATE VIEW V_INS_DICT_XMJB AS
    SELECT ITEM_NAME AS MC, SHOW_ORDER AS PX
    FROM LOWCODE_DICT_ITEM WHERE DICT_CODE = ''XMJB''';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

-- 视图 V_INS_DICT_ZC
BEGIN
EXECUTE IMMEDIATE 'CREATE VIEW V_INS_DICT_ZC AS
    SELECT ''正高级'' AS MC, 1 AS PX FROM DUAL
    UNION ALL
    SELECT ''副高级'' AS MC, 2 AS PX FROM DUAL
    UNION ALL
    SELECT ''中级'' AS MC, 3 AS PX FROM DUAL
    UNION ALL
    SELECT ''初级及以下'' AS MC, 4 AS PX FROM DUAL';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

-- 视图 V_INS_DICT_ZGXW
BEGIN
EXECUTE IMMEDIATE 'CREATE VIEW V_INS_DICT_ZGXW AS
    SELECT ''博士'' AS MC, 1 AS PX FROM DUAL
    UNION ALL
    SELECT ''硕士'' AS MC, 2 AS PX FROM DUAL
    UNION ALL
    SELECT ''学士及以下'' AS MC, 3 AS PX FROM DUAL';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

-- 视图 V_INS_DICT_JSXY
BEGIN
EXECUTE IMMEDIATE 'CREATE VIEW V_INS_DICT_JSXY AS
    SELECT ITEM_NAME AS MC, SHOW_ORDER AS PX
    FROM LOWCODE_DICT_ITEM WHERE DICT_CODE = ''XY''';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

-- 视图 V_INS_DICT_CXCY
BEGIN
EXECUTE IMMEDIATE 'CREATE VIEW V_INS_DICT_CXCY AS
    SELECT ''创新'' AS MC, 1 AS PX FROM DUAL
    UNION ALL
    SELECT ''创业'' AS MC, 2 AS PX FROM DUAL';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

-- 视图 V_INS_DICT_LWDJ
BEGIN
EXECUTE IMMEDIATE 'CREATE VIEW V_INS_DICT_LWDJ AS
    SELECT ''优秀'' AS MC, 1 AS PX FROM DUAL
    UNION ALL
    SELECT ''良好'' AS MC, 2 AS PX FROM DUAL
    UNION ALL
    SELECT ''合格'' AS MC, 3 AS PX FROM DUAL
    UNION ALL
    SELECT ''不合格'' AS MC, 4 AS PX FROM DUAL';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

-- 视图 V_INS_ZYJBQK
BEGIN
EXECUTE IMMEDIATE 'CREATE VIEW V_INS_ZYJBQK AS
    SELECT XNZYDM, XNZYMC, ZYMC, ZYDM, SSDWMC, SSDWH, SZZYNF, XZ, YXXYNX, SYXWML, NVL(ZSZT, ''在招'') AS ZSZT, SFXZY, SFSFLZY, SFTSZY, EXTRACT(YEAR FROM SYSDATE) AS TJNF FROM INS_ZYJBQK WHERE SZZYNF <= EXTRACT(YEAR FROM SYSDATE)
    UNION ALL
    SELECT XNZYDM, XNZYMC, ZYMC, ZYDM, SSDWMC, SSDWH, SZZYNF, XZ, YXXYNX, SYXWML, NVL(ZSZT, ''在招'') AS ZSZT, SFXZY, SFSFLZY, SFTSZY, EXTRACT(YEAR FROM SYSDATE) - 1 AS TJNF FROM INS_ZYJBQK WHERE SZZYNF <= EXTRACT(YEAR FROM SYSDATE) - 1
    UNION ALL
    SELECT XNZYDM, XNZYMC, ZYMC, ZYDM, SSDWMC, SSDWH, SZZYNF, XZ, YXXYNX, SYXWML, NVL(ZSZT, ''在招'') AS ZSZT, SFXZY, SFSFLZY, SFTSZY, EXTRACT(YEAR FROM SYSDATE) - 2 AS TJNF FROM INS_ZYJBQK WHERE SZZYNF <= EXTRACT(YEAR FROM SYSDATE) - 2
    UNION ALL
    SELECT XNZYDM, XNZYMC, ZYMC, ZYDM, SSDWMC, SSDWH, SZZYNF, XZ, YXXYNX, SYXWML, NVL(ZSZT, ''在招'') AS ZSZT, SFXZY, SFSFLZY, SFTSZY, EXTRACT(YEAR FROM SYSDATE) - 3 AS TJNF FROM INS_ZYJBQK WHERE SZZYNF <= EXTRACT(YEAR FROM SYSDATE) - 3
    UNION ALL
    SELECT XNZYDM, XNZYMC, ZYMC, ZYDM, SSDWMC, SSDWH, SZZYNF, XZ, YXXYNX, SYXWML, NVL(ZSZT, ''在招'') AS ZSZT, SFXZY, SFSFLZY, SFTSZY, EXTRACT(YEAR FROM SYSDATE) - 4 AS TJNF FROM INS_ZYJBQK WHERE SZZYNF <= EXTRACT(YEAR FROM SYSDATE) - 4';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/