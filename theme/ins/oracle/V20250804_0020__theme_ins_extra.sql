/*
 Description		: 【教务质量监测】手动创建视图
 Author				: dataapp
 Database           : oracle
*/

-- 视图 V_ADS_DICT_JSLX
BEGIN
EXECUTE IMMEDIATE 'CREATE
OR REPLACE VIEW  V_ADS_DICT_JSLX AS
SELECT ITEM_NAME AS MC, SHOW_ORDER AS PX
FROM LOWCODE_DICT_ITEM WHERE DICT_CODE = ''jwdatapub_jslx''';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

-- 视图 V_ADS_DICT_SJD
BEGIN
EXECUTE IMMEDIATE 'CREATE
OR REPLACE VIEW  V_ADS_DICT_SJD AS
SELECT ITEM_NAME AS MC, SHOW_ORDER AS PX
FROM LOWCODE_DICT_ITEM WHERE DICT_CODE = ''jwdatapub_sjd''';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

-- 视图 V_ADS_DICT_JSZYQK
BEGIN
EXECUTE IMMEDIATE 'CREATE
OR REPLACE VIEW  V_ADS_DICT_JSZYQK AS
SELECT ITEM_NAME AS MC, SHOW_ORDER AS PX
FROM LOWCODE_DICT_ITEM WHERE DICT_CODE = ''jwdatapub_jszyqk''';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

-- 视图 V_ADS_DICT_XSLX
BEGIN
EXECUTE IMMEDIATE 'CREATE
OR REPLACE VIEW  V_ADS_DICT_XSLX AS
SELECT ITEM_NAME AS MC, SHOW_ORDER AS PX
FROM LOWCODE_DICT_ITEM WHERE DICT_CODE = ''jwdatapub_xslx''';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

-- 视图 V_ADS_DICT_GZLLX
BEGIN
EXECUTE IMMEDIATE 'CREATE
OR REPLACE VIEW  V_ADS_DICT_GZLLX AS
SELECT ITEM_NAME AS MC, SHOW_ORDER AS PX
FROM LOWCODE_DICT_ITEM WHERE DICT_CODE = ''jwdatapub_gzllx''';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

-- 视图 V_ADS_DICT_ZC
BEGIN
EXECUTE IMMEDIATE 'CREATE
OR REPLACE VIEW  V_ADS_DICT_ZC AS
SELECT ITEM_NAME AS MC, SHOW_ORDER AS PX
FROM LOWCODE_DICT_ITEM WHERE DICT_CODE = ''ZC''';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

-- 视图 V_ADS_DICT_JB
BEGIN
EXECUTE IMMEDIATE 'CREATE
OR REPLACE VIEW  V_ADS_DICT_JB AS
SELECT ITEM_NAME AS MC, SHOW_ORDER AS PX
FROM LOWCODE_DICT_ITEM WHERE DICT_CODE = ''jwdatapub_jb''';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

-- 视图 V_ADS_DICT_RKKCFL
BEGIN
EXECUTE IMMEDIATE 'CREATE
OR REPLACE VIEW  V_ADS_DICT_RKKCFL AS
SELECT ITEM_NAME AS MC, SHOW_ORDER AS PX
FROM LOWCODE_DICT_ITEM WHERE DICT_CODE = ''jwdatapub_rkkcfl'' AND PID IS NULL';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

-- 视图 V_ADS_DICT_KCSX
BEGIN
EXECUTE IMMEDIATE 'CREATE
OR REPLACE VIEW  V_ADS_DICT_KCSX AS
SELECT ITEM_NAME AS MC, SHOW_ORDER AS PX
FROM LOWCODE_DICT_ITEM WHERE DICT_CODE = ''jwdatapub_kcsx''';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

-- 视图 V_ADS_DICT_XDLX
BEGIN
EXECUTE IMMEDIATE 'CREATE
OR REPLACE VIEW  V_ADS_DICT_XDLX AS
SELECT ITEM_NAME AS MC, SHOW_ORDER AS PX
FROM LOWCODE_DICT_ITEM WHERE DICT_CODE = ''jwdatapub_xdlx''';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

-- 视图 V_ADS_DICT_BGLX
BEGIN
EXECUTE IMMEDIATE 'CREATE
OR REPLACE VIEW  V_ADS_DICT_BGLX AS
SELECT ITEM_NAME AS MC, SHOW_ORDER AS PX
FROM LOWCODE_DICT_ITEM WHERE DICT_CODE = ''jwdatapub_bglx''';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

-- 视图 V_ADS_DICT_TSXXLB
BEGIN
EXECUTE IMMEDIATE 'CREATE
OR REPLACE VIEW  V_ADS_DICT_TSXXLB AS
SELECT ITEM_NAME AS MC, SHOW_ORDER AS PX
FROM LOWCODE_DICT_ITEM WHERE DICT_CODE = ''jwdatapub_tsxxlb''';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

-- 视图 V_ADS_DICT_XSLX
BEGIN
EXECUTE IMMEDIATE 'CREATE
OR REPLACE VIEW  V_ADS_DICT_XSLX AS
SELECT ITEM_NAME AS MC, SHOW_ORDER AS PX
FROM LOWCODE_DICT_ITEM WHERE DICT_CODE = ''jwdatapub_xslx''';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/