/*
 Description		: [教学(INS)]主题域报表表名对照表
 Author				: dataapp
 Date				: 2024-06-20 01:00:00
 Lowcode Version	: V1.1.1_Beta5
 Database           : oracle
*/

BEGIN
    EXECUTE IMMEDIATE 'CREATE TABLE t_table_map (
  id VARCHAR2(100 BYTE),
  table_name VARCHAR2(200 BYTE),
  comments VARCHAR2(500 BYTE),
  sql_str CLOB,
  create_by VARCHAR2(100),
  create_time DATE,
  update_by VARCHAR2(100),
  update_time DATE,PRIMARY KEY (id))';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

COMMENT ON TABLE t_table_map IS '报表模型表名映射';
COMMENT ON COLUMN t_table_map.id IS 'ID';
COMMENT ON COLUMN t_table_map.table_name IS '表名';
COMMENT ON COLUMN t_table_map.comments IS '表注释';
COMMENT ON COLUMN t_table_map.sql_str IS 'sql脚本';
COMMENT ON COLUMN t_table_map.create_by IS '创建人';
COMMENT ON COLUMN t_table_map.create_time IS '创建时间';
COMMENT ON COLUMN t_table_map.update_by IS '更新人';
COMMENT ON COLUMN t_table_map.update_time IS '更新时间';

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_cla_jsjbxx', 'abd_cla_jsjbxx', '教室基本信息', TO_CLOB('
select
教室基本信息.jasdm as 教室代码,
教室基本信息.jasmc as 教室名称,
教室基本信息.jaslxdm as 教室类型,
教室基本信息.jxldm as 教学楼,
教室基本信息.lc as 楼层,
教室基本信息.xxxqdm as 学校校区,
教室基本信息.zt as 状态,
教室基本信息.skzws as 上课座位数,
教室基本信息.kszws as 考试座位数,
教室基本信息.dwdm as 单位,
教室基本信息.sfyxpk as 是否允许排课,
教室基本信息.sfyxks as 是否允许考试,
教室基本信息.sfyxjy as 是否允许借用,
教室基本信息.sfyxcx as 是否允许查询,
教室基本信息.jsyt as 教室用途,
教室基本信息.jsls as 教室隶属,
教室基本信息.sfzsjas as 是否专属教室
from abd_cla_jsjbxx 教室基本信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_cla_jsjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_cla_jszyzy', 'abd_cla_jszyzy', '教室资源占用', TO_CLOB('
select
教室资源占用.jasdm as 教室代码,
教室资源占用.xnxqdm as 学年学期,
教室资源占用.zc as 周次,
教室资源占用.xq as 星期,
教室资源占用.jc as 节次,
教室资源占用.kssj as 考试时间,
教室资源占用.jssj as 结束时间,
教室资源占用.zylxdm as 占用类型,
教室资源占用.jxbid as 教学班ID,
教室资源占用.kch as 课程号,
教室资源占用.kxh as 课序号,
教室资源占用.kbid as 课表ID,
教室资源占用.pkly as 排课来源
from abd_cla_jszyzy 教室资源占用'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_cla_jszyzy'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_cla_xxxq', 'abd_cla_xxxq', '学校校区', TO_CLOB('
select
学校校区.xxxqdm as 学校校区代码,
学校校区.mc as 校区名称,
学校校区.sfsy as 是否使用
from abd_cla_xxxq 学校校区'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_cla_xxxq'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_cla_jxl', 'abd_cla_jxl', '教学楼', TO_CLOB('
select
教学楼.jxldm as 教学楼代码,
教学楼.jxlmc as 教学楼名称,
教学楼.xxxqdm as 学校校区代码
from abd_cla_jxl 教学楼'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_cla_jxl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_col_xyjbxx', 'abd_col_xyjbxx', '学院基本信息', TO_CLOB('
select
学院基本信息.xybh as 学院编号,
学院基本信息.xymc as 学院名称
from abd_col_xyjbxx 学院基本信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_col_xyjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_col_xyxxx', 'abd_col_xyxxx', '学院系信息', TO_CLOB('
select
学院系信息.xybh as 学院编号,
学院系信息.xbh as 系编号,
学院系信息.xmc as 系名称,
学院系信息.ssxkly as 所属学科领域
from abd_col_xyxxx 学院系信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_col_xyxxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_col_ndzy', 'abd_col_ndzy', '年度专业', TO_CLOB('
select
年度专业.zydm as 专业代码,
年度专业.zymc as 专业名称,
年度专业.jlny as 建立年月,
年度专业.xz as 学制,
年度专业.yxdm as 所在单位代码,
年度专业.sfsy as 是否使用,
年度专业.xkmldm as 学科门类,
年度专业.xidm as 系代码
from abd_col_ndzy 年度专业'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_col_ndzy'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_col_zyfx', 'abd_col_zyfx', '专业方向', TO_CLOB('
select
专业方向.zyfxdm as 专业方向代码,
专业方向.zyfxmc as 专业方向名称,
专业方向.sfsy as 是否使用,
专业方向.yxdm as 院系代码
from abd_col_zyfx 专业方向'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_col_zyfx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_col_bj', 'abd_col_bj', '班级', TO_CLOB('
select
班级.xqdm as 校区代码,
班级.bjdm as 班级代码,
班级.bjmc as 班级名称,
班级.yxdm as 单位,
班级.zydm as 专业,
班级.nj as 年级,
班级.xslbdm as 学生类别,
班级.sfsy as 是否使用,
班级.sjrs as 实际人数,
班级.zyfxdm as 专业方向代码,
班级.tgyxdm as 托管院系代码,
班级.xdlxdm as 修读类型
from abd_col_bj 班级'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_col_bj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_xkjsxxpm', 'ins_xkjsxxpm', '学科竞赛学院排名', TO_CLOB('
select
学科竞赛学院排名.xydm as 学院代码,
学科竞赛学院排名.xymc as 学院名称,
学科竞赛学院排名.xxpm as 学院排名,
学科竞赛学院排名.tjnf as 统计年份
from ins_xkjsxxpm 学科竞赛学院排名'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_xkjsxxpm'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_col_cyxy', 'abd_col_cyxy', '产业学院', TO_CLOB('
select
产业学院.cyxymc as 产业学院名称,
产业学院.xydm as 学院代码,
产业学院.xymc as 学院名称,
产业学院.xylx as 学院类型,
产业学院.jb as 级别,
产业学院.lxsj as 立项时间,
产业学院.hzqymc as 合作企业名称,
产业学院.gjzymc as 共建专业名称
from abd_col_cyxy 产业学院'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_col_cyxy'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_cou_kcjbxx', 'abd_cou_kcjbxx', '课程基本信息', TO_CLOB('
select
课程基本信息.sqxnxq as 申请学年学期,
课程基本信息.kcdm as 课程代码,
课程基本信息.kcmc as 课程名称,
课程基本信息.kkdwdm as 开课单位代码,
课程基本信息.kkdw as 开课单位,
课程基本信息.cddw as 承担单位,
课程基本信息.kkjj as 开课季节,
课程基本信息.kkxqs as 开课学期数,
课程基本信息.kkzt as 开课状态,
课程基本信息.zxs as 总学时,
课程基本信息.xf as 学分,
课程基本信息.zxsv as 周学时,
课程基本信息.kslx as 考试类型,
课程基本信息.mldm as 门类代码,
课程基本信息.ml as 门类,
课程基本信息.dyyjxk as 对应一级学科,
课程基本信息.jxzs as 教学周数,
课程基本信息.kcbq as 课程标签,
课程基本信息.kccc as 课程层次,
课程基本信息.kcfl as 课程分类,
课程基本信息.kcfzrgh as 课程负责人工号,
课程基本信息.kcfzrxm as 课程负责人姓名,
课程基本信息.kclb as 课程类别,
课程基本信息.kclx as 课程类型,
课程基本信息.kcxz as 课程性质,
课程基本信息.syzy as 适用专业,
课程基本信息.syxslb as 适用学生类别,
课程基本信息.syxwlx as 适用学位类型,
课程基本信息.llxf as 理论学分,
课程基本信息.sjxf as 实践学分,
课程基本信息.sfgjh as 是否国际化,
课程基本信息.sfkb as 是否开班,
课程基本信息.sfkxq as 是否跨学期,
课程基本信息.sflxskc as 是否留学')||TO_CLOB('生课程,
课程基本信息.sfqydslhsk as 是否企业导师联合授课,
课程基本信息.sfsyk as 是否实验课,
课程基本信息.sfxwk as 是否学位课,
课程基本信息.skfs as 授课方式,
课程基本信息.skxs as 授课形式,
课程基本信息.skyy as 授课语言
from abd_cou_kcjbxx 课程基本信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_cou_kcjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_cou_jxjhkc', 'abd_cou_jxjhkc', '教学计划课程', TO_CLOB('
select
教学计划课程.kch as 课程号,
教学计划课程.kcm as 课程名,
教学计划课程.kzh as 课组号,
教学计划课程.kclydm as 课程来源,
教学计划课程.xnxqdm as 学年学期,
教学计划课程.yjhxnxqdm as 原计划学年学期,
教学计划课程.tzzxnxqdm as 调整至学年学期,
教学计划课程.cddw as 承担单位,
教学计划课程.cdjys as 承担教研室,
教学计划课程.bgxxms as 变更信息,
教学计划课程.bglx as 变更类型,
教学计划课程.xs as 学时,
教学计划课程.fxxs01 as 计划课程分项学时01,
教学计划课程.fxxs02 as 计划课程分项学时02,
教学计划课程.fxxs03 as 计划课程分项学时03,
教学计划课程.fxxs04 as 计划课程分项学时04,
教学计划课程.fxxs05 as 计划课程分项学时05,
教学计划课程.fxxs06 as 计划课程分项学时06,
教学计划课程.fxxs07 as 计划课程分项学时07,
教学计划课程.fxxs08 as 计划课程分项学时08,
教学计划课程.fxxs09 as 计划课程分项学时09,
教学计划课程.fxxs10 as 计划课程分项学时10,
教学计划课程.fxxs11 as 计划课程分项学时11,
教学计划课程.sfks as 是否开设,
教学计划课程.zt as 状态,
教学计划课程.bgsm as 变更说明,
教学计划课程.fxxs12 as 计划课程分项学时12,
教学计划课程.fxxs13 as 计划课程分项学时13,
教学计划课程.fxxs14 as 计划课程分项学时14,
教学计划课程.xgxk')||TO_CLOB('lbdm as 校公选课类别,
教学计划课程.kcxzdm as 课程性质,
教学计划课程.kslxdm as 考试类型
from abd_cou_jxjhkc 教学计划课程'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_cou_jxjhkc'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_cou_kcxgxx', 'abd_cou_kcxgxx', '课程相关信息', TO_CLOB('
select
课程相关信息.kch as 课程号,
课程相关信息.kcm as 课程名,
课程相关信息.kcjbdm as 课程级别,
课程相关信息.ywkcm as 英文课程名,
课程相关信息.jysdm as 教研室,
课程相关信息.jxmd as 教学目的,
课程相关信息.ktjsxs as 课堂讲授学时,
课程相关信息.syxs as 实验学时,
课程相关信息.sjixs as 设计学时,
课程相关信息.sjxs as 上机学时,
课程相关信息.tlxs as 讨论学时,
课程相关信息.qtxs1 as 其他学时1,
课程相关信息.qtxs2 as 其他学时2,
课程相关信息.qtxs3 as 其他学时3,
课程相关信息.qtxs4 as 其他学时4,
课程相关信息.qtxs5 as 其他学时5,
课程相关信息.qtxs6 as 其他学时6,
课程相关信息.qtxs7 as 其他学时7,
课程相关信息.qtxs8 as 其他学时8,
课程相关信息.jzsjz as 集中实践周,
课程相关信息.kcsjxs as 课程实践学时,
课程相关信息.zwjc as 中文教材,
课程相关信息.zwcks as 中文参考书,
课程相关信息.wwjc as 外文教材,
课程相关信息.wwcks as 外文参考书,
课程相关信息.ksrq as 开始日期,
课程相关信息.ztsj as 暂停时间,
课程相关信息.ybzs as 预备知识,
课程相关信息.xxkc as 先修课程,
课程相关信息.tkyy as 停开原因,
课程相关信息.hfyy as 恢复原因,
课程相关信息.xgxklbdm as 校公选课类别,
课程相关信息.bqmc as 课程标签名称,
课程相关信息.xxk')||TO_CLOB('dm as 先修课代码,
课程相关信息.xxkmc as 先修课名称,
课程相关信息.sfxgxk as 是否校公选课,
课程相关信息.tykcbs as 同一课程标识,
课程相关信息.xidm as 系代码,
课程相关信息.kcsjzxs as 课程实践周学时,
课程相关信息.sfsysk as 是否双语授课,
课程相关信息.sfqywsk as 是否全英文授课,
课程相关信息.sfmgckc as 是否马工程课程,
课程相关信息.sfjzsjhj as 是否集中实践环节,
课程相关信息.xxkmc2 as 先修课名称2,
课程相关信息.xxksfqy as 先修课是否启用,
课程相关信息.xxkbxtg as 先修课必须通过,
课程相关信息.mxnj as 面向年级,
课程相关信息.syzxs as 实验周学时,
课程相关信息.sjizxs as 设计周学时,
课程相关信息.sjzxs as 上机周学时,
课程相关信息.tlzxs as 讨论周学时,
课程相关信息.qtzxs1 as 其他周学时1
from abd_cou_kcxgxx 课程相关信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_cou_kcxgxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_cou_bkkcjbxx', 'abd_cou_bkkcjbxx', '本科课程基本信息', TO_CLOB('
select
本科课程基本信息.kch as 课程号,
本科课程基本信息.kcm as 课程名,
本科课程基本信息.ywkcm as 英文课程名,
本科课程基本信息.kcccdm as 课程层次,
本科课程基本信息.kcjb as 课程级别,
本科课程基本信息.kcfldm as 课程分类,
本科课程基本信息.jxfsdm as 教学方式,
本科课程基本信息.skyzdm as 授课语种,
本科课程基本信息.kcztdm as 课程状态,
本科课程基本信息.kkdw as 开课单位,
本科课程基本信息.kslx as 考试类型,
本科课程基本信息.sfcxcylkc as 是否创新创业类课程,
本科课程基本信息.sfsysk as 是否双语授课,
本科课程基本信息.sfqywkc as 是否全英文授课,
本科课程基本信息.sfgzkc as 是否关注课程,
本科课程基本信息.kcbq as 课程标签,
本科课程基本信息.hjlb as 获奖类别,
本科课程基本信息.kcfzr as 课程负责人,
本科课程基本信息.zwjc as 中文教材,
本科课程基本信息.wwjc as 外文教材,
本科课程基本信息.xxkc as 先修课程,
本科课程基本信息.sfszkc as 是否思政课程,
本科课程基本信息.rdnf as 认定年份,
本科课程基本信息.kkpt as 开课平台,
本科课程基本信息.url as 链接地址,
本科课程基本信息.tjnf as 统计年份
from abd_cou_bkkcjbxx 本科课程基本信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_cou_bkkcjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_xxjcjxzz', 'ins_xxjcjxzz', '学校基层教学组织', TO_CLOB('
select
学校基层教学组织.tjnf as 统计年份,
学校基层教学组织.zymc as 专业名称,
学校基层教学组织.zydm as 专业代码,
学校基层教学组织.jcjxzzmc as 基层教学组织名称,
学校基层教学组织.jcjxzzlx as 基层教学组织类型,
学校基层教学组织.slsj as 设立时间,
学校基层教学组织.fzrxm as 负责人姓名,
学校基层教学组织.fzrgh as 负责人工号,
学校基层教学组织.jj as 简介,
学校基层教学组织.jjtp as 简介图片,
学校基层教学组织.xydm as 学院代码
from ins_xxjcjxzz 学校基层教学组织'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_xxjcjxzz'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_zyjbqk', 'ins_zyjbqk', '专业基本情况', TO_CLOB('
select
专业基本情况.xnzydm as 校内专业代码,
专业基本情况.xnzymc as 校内专业名称,
专业基本情况.zymc as 专业名称,
专业基本情况.zydm as 专业代码,
专业基本情况.ssdwmc as 所属单位名称,
专业基本情况.ssdwh as 所属单位号,
专业基本情况.szzynf as 专业设置年份,
专业基本情况.xz as 学制,
专业基本情况.yxxynx as 允许修业年限,
专业基本情况.syxwml as 授予学位门类,
专业基本情况.zszt as 招生状态,
专业基本情况.sfxzy as 是否新专业,
专业基本情况.sfsflzy as 是否师范类专业,
专业基本情况.sftszy as 是否特色专业,
专业基本情况.tjnf as 统计年份
from ins_zyjbqk 专业基本情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_zyjbqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_maj_fab', 'abd_maj_fab', '方案表', TO_CLOB('
select
方案表.pyfadm as 培养方案代码,
方案表.pyfamc as 培养方案名称,
方案表.faccdm as 方案层次,
方案表.njdm as 年级,
方案表.dvd as 单位,
方案表.zydm as 专业代码,
方案表.zymc as 专业名称,
方案表.zyfxdm as 专业方向,
方案表.xlccdm as 学历层次,
方案表.xznx as 学制年限,
方案表.ksxndm as 开始学年,
方案表.ksxqdm as 开始学期,
方案表.xqlxdm as 学期类型,
方案表.xdlx as 修读类型,
方案表.xvdm as 学位,
方案表.zsyqxf as 最少要求学分,
方案表.pymb as 培养目标,
方案表.xdyo as 修读要求,
方案表.zgkc as 主干课程,
方案表.fats as 方案特色,
方案表.faztdm as 方案状态,
方案表.sffb as 是否发布,
方案表.sjkc as 实践课程,
方案表.sykc as 双语课程,
方案表.qyvkc as 全英文课程,
方案表.byyo as 毕业要求,
方案表.kcxfyq as 课程学分要求,
方案表.sjxfyq as 实践学分要求,
方案表.xvccdm as 学位层次代码,
方案表.falxdm as 方案类型代码,
方案表.pyccdm as 培养层次代码,
方案表.sfyc as 是否异常,
方案表.url as 链接地址,
方案表.xnxq as 学年学期
from abd_maj_fab 方案表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_maj_fab'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_pos_yjsjbxx', 'abd_pos_yjsjbxx', '研究生基本信息', TO_CLOB('
select
研究生基本信息.xh as 学号,
研究生基本信息.xm as 姓名,
研究生基本信息.xmpy as 姓名拼音,
研究生基本信息.xslb as 学生类别,
研究生基本信息.xb as 性别,
研究生基本信息.mz as 民族,
研究生基本信息.csrq as 出生日期,
研究生基本信息.yx as 院系,
研究生基本信息.zy as 专业,
研究生基本信息.zyfx as 专业方向,
研究生基本信息.nj as 年级,
研究生基本信息.bj as 班级,
研究生基本信息.pycc as 培养层次,
研究生基本信息.pyfs as 培养方式,
研究生基本信息.rxfs as 入学方式,
研究生基本信息.xxfs as 学习方式,
研究生基本信息.rxjj as 入学季节,
研究生基本信息.rxny as 入学年月,
研究生基本信息.sflxs as 是否留学生,
研究生基本信息.lxsly as 留学生来源,
研究生基本信息.ml as 门类,
研究生基本信息.yjxkzyxwlb as 一级学科专业学位类别,
研究生基本信息.xwlx as 学位类型,
研究生基本信息.xq as 校区,
研究生基本信息.xz as 学制,
研究生基本信息.yjbysj as 预计毕业时间,
研究生基本信息.zxbs as 在校标识,
研究生基本信息.dsgh as 导师工号,
研究生基本信息.dsxm as 导师姓名,
研究生基本信息.xjzt as 学籍状态,
研究生基本信息.syd as 生源地,
研究生基本信息.gjdq as 国家地区,
研究生基本信息.zjxy as 宗教信仰,
研究生基本信息.zzmm as 政治面貌,
研究生基本信息.kzrq as 快照日期')||TO_CLOB(',
研究生基本信息.yxdm as 院系代码,
研究生基本信息.zydm as 专业代码,
研究生基本信息.mldm as 门类代码,
研究生基本信息.yjxkzyxwlbdm as 一级学科专业学位类别代码,
研究生基本信息.zzxxnx as 最长学习年限,
研究生基本信息.sjbysj as 实际毕业时间,
研究生基本信息.njdm as 年级代码
from abd_pos_yjsjbxx 研究生基本信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_pos_yjsjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_xxjbxx', 'abd_sch_xxjbxx', '学校基本信息', TO_CLOB('
select
学校基本信息.xxbsm as 学校标识码,
学校基本信息.xxmc as 学校名称,
学校基本信息.xxywmc as 学校英文名称,
学校基本信息.xxdz as 学校地址,
学校基本信息.xxyzbm as 学校邮政编码,
学校基本信息.xzqh as 行政区划,
学校基本信息.szdcxlx as 所在地城乡类型,
学校基本信息.jxny as 建校年月,
学校基本信息.xqr as 校庆日,
学校基本信息.xxbxlx as 学校办学类型,
学校基本信息.xxjbz as 学校举办者,
学校基本信息.xxzgbm as 学校主管部门,
学校基本信息.fddbrh as 法定代表人号,
学校基本信息.frzsh as 法人证书号,
学校基本信息.xzxm as 校长姓名,
学校基本信息.dwfzr as 党委负责人,
学校基本信息.zzjgm as 组织机构码,
学校基本信息.lxdh as 联系电话,
学校基本信息.czdh as 传真电话,
学校基本信息.dzxx as 电子信箱,
学校基本信息.xxbb as 学校办别,
学校基本信息.xxxz as 学校性质
from abd_sch_xxjbxx 学校基本信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_xxjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_jxzlpgtjb', 'ins_jxzlpgtjb', '教学质量评估统计表', TO_CLOB('
select
教学质量评估统计表.xxbsm as 学校标识码,
教学质量评估统计表.bkscypjrcs as 本科生参与评教人次数人次,
教学质量评估统计表.xxzjzddyrs as 学校专兼职督导员人数人,
教学质量评估统计表.xnnddtkxss as 学年内督导听课学时数,
教学质量评估统计表.xnnxldtkxss as 学年内校领导听课学时数,
教学质量评估统计表.xnnxldtszbxkcxss as 学年内校领导听思政必修课程学时数,
教学质量评估统计表.xnnzcldtkxss as 学年内中层领导听课学时数,
教学质量评估统计表.xnxq as 学年学期,
教学质量评估统计表.tjnf as 统计年份
from ins_jxzlpgtjb 教学质量评估统计表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_jxzlpgtjb'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_bksjdb', 'ins_bksjdb', '本科生奖贷补', TO_CLOB('
select
本科生奖贷补.xxbsm as 学校标识码,
本科生奖贷补.zzjezj as 资助金额总计,
本科生奖贷补.zzxsszj as 资助学生数总计,
本科生奖贷补.zfjzxjzzje as 政府奖助学金资助金额,
本科生奖贷补.zfjzxjzzxss as 政府奖助学金资助学生数,
本科生奖贷补.shjzxjzzje as 社会奖助学金资助金额,
本科生奖贷补.shjzxjzzxss as 社会奖助学金资助学生数,
本科生奖贷补.xxjxjzzje as 学校奖学金资助金额,
本科生奖贷补.xxjxjzzxss as 学校奖学金资助学生数,
本科生奖贷补.qgzxjzzje as 勤工助学金资助金额,
本科生奖贷补.qgzxjzzxss as 勤工助学金资助学生数,
本科生奖贷补.jmxfzzje as 减免学费资助金额,
本科生奖贷补.jmxfzzxss as 减免学费资助学生数,
本科生奖贷补.lsknbzzzje as 临时困难补助资助金额,
本科生奖贷补.lsknbzzzxss as 临时困难补助资助学生数,
本科生奖贷补.qtjzxjzzje as 其它奖助学金资助金额,
本科生奖贷补.qtjzxjzzxss as 其它奖助学金资助学生数,
本科生奖贷补.tjnf as 统计年份
from ins_bksjdb 本科生奖贷补'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_bksjdb'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_bksjlqk', 'ins_bksjlqk', '本科生交流情况', TO_CLOB('
select
本科生交流情况.xxbsm as 学校标识码,
本科生交流情况.xnzydm as 校内专业大类代码,
本科生交流情况.xnzymc as 校内专业大类名称,
本科生交流情况.jwbzywcjlxsrs as 境外本专业外出交流学生人数,
本科生交流情况.jnbzywcjlxsrs as 境内本专业外出交流学生人数,
本科生交流情况.jwdbzyjlxsrs as 境外到本专业交流学生人数,
本科生交流情况.jndbzyjlxsrs as 境内到本专业交流学生人数,
本科生交流情况.tjnf as 统计年份
from ins_bksjlqk 本科生交流情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_bksjlqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_bkssycs', 'ins_bkssycs', '本科实验场所', TO_CLOB('
select
本科实验场所.xxbsm as 学校标识码,
本科实验场所.sycsdm as 实验场所代码,
本科实验场所.sycsmc as 实验场所名称,
本科实验场所.ssdwmc as 所属单位名称,
本科实验场所.ssdwh as 所属单位号,
本科实验场所.xz as 性质,
本科实验场所.symj as 使用面积,
本科实验场所.tjnf as 统计年份
from ins_bkssycs 本科实验场所'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_bkssycs'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_bksxxcx', 'ins_bksxxcx', '本科生学习成效', TO_CLOB('
select
本科生学习成效.xxbsm as 学校标识码,
本科生学习成效.xkjshjzs as 学科竞赛获奖总数,
本科生学习成效.xkjshjgjj as 学科竞赛获奖国际级,
本科生学习成效.gjjxkjshj as 国家级学科竞赛获奖,
本科生学习成效.sbjxkjshj as 省部级学科竞赛获奖,
本科生学习成效.wytyjshjzs as 文艺体育竞赛获奖总数,
本科生学习成效.gjjwytyjshjzs as 国际级文艺体育竞赛获奖总数,
本科生学习成效.gjjwytyjshjs as 国家级文艺体育竞赛获奖总数,
本科生学习成效.sbjwytyjshjzs as 省部级文艺体育竞赛获奖总数,
本科生学习成效.xsfbxslwzs as 学生发表学术论文总数,
本科生学习成效.xsfbzpzs as 学生发表作品数总数,
本科生学习成效.xshqzls as 学生获准专利著作权数,
本科生学习成效.yysjksljtgl as 英语四级考试累计通过率,
本科生学习成效.yyljksljtgl as 英语六级考试累计通过率,
本科生学习成效.cjgjhys as 参加国际会议,
本科生学习成效.hdzyzgzszs as 获得职业资格证书总数,
本科生学习成效.hdzyjsryzyzgzs as 专业技术人员职业资格总数,
本科生学习成效.jnryzyzgzs as 技能人员职业资格总数,
本科生学习成效.xsdgjzzxxrzrs as 学生到国际组织实习任职人数,
本科生学习成效.tjnf as 统计年份
from ins_bksxxcx 本科生学习成效'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_bksxxcx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_bksysbqk', 'ins_bksysbqk', '本科实验设备情况', TO_CLOB('
select
本科实验设备情况.xxbsm as 学校标识码,
本科实验设备情况.sycsdm as 实验场所代码,
本科实验设备情况.sycsmc as 实验场所名称,
本科实验设备情况.zyjxsyyqdb as 主要教学实验仪器设备含软件名,
本科实验设备情况.zyjxsyyqdbbh as 主要教学实验仪器设备编号,
本科实验设备情况.dj as 单价,
本科实验设备情况.gzsj as 购置时间,
本科实验设备情况.tjnf as 统计年份,
本科实验设备情况.ddbfnx as 达到报废年限还有几年
from ins_bksysbqk 本科实验设备情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_bksysbqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_bkzxkcqk', 'ins_bkzxkcqk', '本科在线课程情况', TO_CLOB('
select
本科在线课程情况.xxbsm as 学校标识码,
本科在线课程情况.kcmc as 课程名称,
本科在线课程情况.kch as 课程号,
本科在线课程情况.xmlx as 项目类型,
本科在线课程情况.xmjb as 项目级别,
本科在线课程情况.jsfs as 建设方式,
本科在线课程情况.lxsj as 立项时间,
本科在线课程情况.kcfzr as 课程负责人,
本科在线课程情况.ljdz as 链接地址,
本科在线课程情况.xnxq as 学年学期
from ins_bkzxkcqk 本科在线课程情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_bkzxkcqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_cxcyjyqk', 'ins_cxcyjyqk', '创新创业教育情况', TO_CLOB('
select
创新创业教育情况.xxbsm as 学校标识码,
创新创业教育情况.sfclldxz as 是否成立创新创业教育工作领导小组,
创新创业教育情况.sfksxy as 是否开设创新创业学院,
创新创业教育情况.qtdw as 创新创业教育工作牵头单位,
创新创业教育情况.sfyqxdpyfa as 是否按创新创业教育目标要求修订人才培养方案,
创新创业教育情况.jxj as 创新创业奖学金,
创新创业教育情况.zxzjtr as 创新创业专项资金投入,
创新创业教育情况.jcs as 创新创业教育教材数,
创新创业教育情况.cyxlxmbkzxsrs as 参与创新创业训练项目全日制本科在校学生数,
创新创业教育情况.cyjsbkzxsrs as 参与创新创业竞赛全日制本科在校学生数,
创新创业教育情况.zxxscyxms as 在校学生创业项目数,
创新创业教育情况.zxxscyxmcyxss as 在校学生创业项目参与学生数,
创新创业教育情况.zxxscyxmhdzzje as 在校学生创业项目获得资助金额,
创新创业教育情况.xsxxcyxms as 学生休学创业项目数,
创新创业教育情况.xsxxcyxmcyxsrs as 学生休学创业项目参与学生人数,
创新创业教育情况.tjnf as 统计年份
from ins_cxcyjyqk 创新创业教育情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_cxcyjyqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_djsskqk', 'ins_djsskqk', '多教师授课情况', TO_CLOB('
select
多教师授课情况.xxbsm as 学校标识码,
多教师授课情况.kch as 开课号,
多教师授课情况.kcmc as 课程名称,
多教师授课情况.zxs as 总学时,
多教师授课情况.skjs as 授课教师,
多教师授课情况.skjsgh as 授课教师工号,
多教师授课情况.jsskxs as 教师授课学时,
多教师授课情况.sfzj as 是否主讲,
多教师授课情况.xnxq as 学年学期
from ins_djsskqk 多教师授课情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_djsskqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_fzyzysykqk', 'ins_fzyzysykqk', '分专业（大类）专业实验课情况', TO_CLOB('
select
分专业大类专业实验课情况.xxbsm as 学校标识码,
分专业大类专业实验课情况.xnzydm as 校内专业大类代码,
分专业大类专业实验课情况.xnzymc as 校内专业大类名称,
分专业大类专业实验课情况.kch as 课程号,
分专业大类专业实验课情况.kcmc as 课程名称,
分专业大类专业实验课情况.sysycsmc as 所用实验场所名称,
分专业大类专业实验课情况.sycsdm as 实验场所代码,
分专业大类专业实验课情况.syxs as 实验学时,
分专业大类专业实验课情况.tjnf as 统计年份,
分专业大类专业实验课情况.xnxq as 学年学期
from ins_fzyzysykqk 分专业大类专业实验课情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_fzyzysykqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_gdzc', 'ins_gdzc', '固定资产', TO_CLOB('
select
固定资产.xxbsm as 学校标识码,
固定资产.gdzczz as 固定资产总值,
固定资产.jxkyyqsbzczz as 教学科研仪器设备资产总值,
固定资产.dnxzz as 当年新增值,
固定资产.xxhsbzczz as 信息化设备资产总值,
固定资产.xxhsbzcrj as 信息化设备资产其中软件,
固定资产.tjnf as 统计年份
from ins_gdzc 固定资产'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_gdzc'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_gxcxcyjysjjd', 'ins_gxcxcyjysjjd', '高校创新创业教育实践基地（平台）', TO_CLOB('
select
高校创新创业教育实践基地平台.xxbsm as 学校标识码,
高校创新创业教育实践基地平台.jdmc as 基地平台名称,
高校创新创业教育实践基地平台.jdlx as 基地平台类型,
高校创新创业教育实践基地平台.jdjb as 基地平台级别,
高校创新创业教育实践基地平台.jshj as 建设环境,
高校创新创业教育实践基地平台.pznf as 批准建设年份,
高校创新创业教育实践基地平台.trjf as 投入经费,
高校创新创业教育实践基地平台.jfly as 经费来源,
高校创新创业教育实践基地平台.tjnf as 统计年份
from ins_gxcxcyjysjjd 高校创新创业教育实践基地平台'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_gxcxcyjysjjd'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_jsjxfzjg', 'ins_jsjxfzjg', '教师教学发展机构', TO_CLOB('
select
教师教学发展机构.xxbsm as 学校标识码,
教师教学发展机构.jgmc as 机构名称,
教师教学发展机构.dwh as 单位号,
教师教学发展机构.pxlx as 培训类型,
教师教学发展机构.pxcs as 培训次数,
教师教学发展机构.pxrc as 培训人次,
教师教学发展机构.pxbxjsrcs as 其中培训本校教师人次数,
教师教学发展机构.tjnf as 统计年份
from ins_jsjxfzjg 教师教学发展机构'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_jsjxfzjg'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_jxxzyfmj', 'ins_jxxzyfmj', '教学行政用房面积', TO_CLOB('
select
教学行政用房面积.xxbsm as 学校标识码,
教学行政用房面积.jxkyjfzyfmj as 教学科研及辅助用房面积,
教学行政用房面积.jsmj as 教室面积,
教学行政用房面积.zhjsmj as 智慧教室面积,
教学行政用房面积.zhjssl as 智慧教室数量,
教学行政用房面积.zhjszws as 智慧教室座位数,
教学行政用房面积.tsgmj as 图书馆面积,
教学行政用房面积.syssxcsmj as 实验室实习场所面积,
教学行政用房面积.zykyyfmj as 专用科研用房面积,
教学行政用房面积.tygmj as 体育馆面积,
教学行政用房面积.sshdyfmj as 师生活动用房面积,
教学行政用房面积.htmj as 会堂面积,
教学行政用房面积.jxjyyfmj as 继续教育用房面积,
教学行政用房面积.xzyfmj as 行政用房面积平方米,
教学行政用房面积.tjnf as 统计年份
from ins_jxxzyfmj 教学行政用房面积'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_jxxzyfmj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_jyjbkslqbzjrs', 'ins_jyjbkslqbzjrs', '近一级本科生录取标准及人数', TO_CLOB('
select
近一级本科生录取标准及人数.xxbsm as 学校标识码,
近一级本科生录取标准及人数.sf as 省份,
近一级本科生录取标准及人数.pc as 批次,
近一级本科生录取标准及人数.zslx as 招生类型,
近一级本科生录取标准及人数.lqs as 录取数人,
近一级本科生录取标准及人数.pczdkzx as 批次最低控制线分,
近一级本科生录取标准及人数.dnlqpjfs as 当年录取平均分数分,
近一级本科生录取标准及人数.sm as 说明,
近一级本科生录取标准及人数.tjnf as 统计年份,
近一级本科生录取标准及人数.zslqzdf as 招生录取最低分分,
近一级本科生录取标准及人数.zslqzdfpm as 招生录取最低分排名
from ins_jyjbkslqbzjrs 近一级本科生录取标准及人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_jyjbkslqbzjrs'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_jyjbkszslbqk', 'ins_jyjbkszslbqk', '近一级本科生招生类别情况', TO_CLOB('
select
近一级本科生招生类别情况.xxbsm as 学校标识码,
近一级本科生招生类别情况.zsjhs as 招生计划数,
近一级本科生招生类别情况.sjlqs as 实际录取数,
近一级本科生招生类别情况.sjbds as 实际报到数,
近一级本科生招生类别情况.tslxzss as 特殊类型招生数,
近一级本科生招生类别情况.tslxqjjhzss as 强基计划招生数,
近一级本科生招生类别情况.gxzxjhzss as 高校专项计划招生数,
近一级本科生招生类别情况.bsszss as 保送生招生数,
近一级本科生招生类别情况.gspystzss as 高水平艺术团招生数,
近一级本科生招生类别情况.gspydyzss as 高水平运动队招生数,
近一级本科生招生类别情况.yslzss as 艺术类招生数,
近一级本科生招生类别情况.gatzss as 港澳台招生数,
近一级本科生招生类别情况.zwhzbxzss as 中外合作办学招生数,
近一级本科生招生类别情况.ssmzykbzss as 少数民族预科班招生数,
近一级本科生招生类别情况.zhpjzss as 综合评价招生数,
近一级本科生招生类别情况.zsbsxss as 招收本省学生数,
近一级本科生招生类别情况.tjnf as 统计年份
from ins_jyjbkszslbqk 近一级本科生招生类别情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_jyjbkszslbqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_jyjfgk', 'ins_jyjfgk', '教育经费概况', TO_CLOB('
select
教育经费概况.xxbsm as 学校标识码,
教育经费概况.xxndjszsr as 学校年度决算总收入,
教育经费概况.xxjsshjkze as 学校接收社会捐赠总额,
教育经费概况.xyjkze as 校友捐赠总额,
教育经费概况.xxndjszzc as 学校年度决算总支出,
教育经费概况.xxjyzcze as 学校教育支出总额,
教育经费概况.szdwgzdwjszxjfzc as 思政工作和党务工作队伍建设专项经费支出,
教育经费概况.wlszgzzxjfzc as 网络思政工作专项经费支出,
教育经费概况.sxzzllkczxjsjfzc as 思想政治理论课程专项建设经费支出,
教育经费概况.tjnf as 统计年份
from ins_jyjfgk 教育经费概况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_jyjfgk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_jyjfszqk', 'ins_jyjfszqk', '教育经费收支情况', TO_CLOB('
select
教育经费收支情况.xxbsm as 学校标识码,
教育经费收支情况.jxjfzczj as 教学经费支出总计,
教育经费收支情况.jxrcyxzc as 教学日常运行支出,
教育经费收支情况.jxggzc as 教学改革支出,
教育经费收支情况.zyjszc as 专业建设支出,
教育经费收支情况.sjjxzc as 实践教学支出,
教育经费收支情况.qzsyjfzc as 实验经费支出,
教育经费收支情况.sxjfzc as 实习经费支出,
教育经费收支情况.sxzzllkczxjsjfzc as 思想政治理论课程专项建设经费支出,
教育经费收支情况.qtjxzx as 其他教学专项,
教育经费收支情况.xshdjfzc as 学生活动经费支出,
教育经费收支情况.jspxjxzxjfzc as 教师培训进修专项经费支出,
教育经费收支情况.jcxysnsyfbk as 经常性预算内事业费拨款,
教育经费收支情况.jgzxbkgj as 教改专项拨款国家,
教育经费收支情况.jgzxbkdf as 教改专项拨款地方,
教育经费收支情况.gjbkssjbkze as 国家本科生生均拨款总额,
教育经费收支情况.dfbkssjbkze as 地方本科生生均拨款总额,
教育经费收支情况.zkssjbkze as 专科生生均拨款总额,
教育经费收支情况.bksxfsr as 本科生学费收入,
教育经费收支情况.gzgzxfsr as 高职高专学费收入,
教育经费收支情况.tjnf as 统计年份
from ins_jyjfszqk 教育经费收支情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_jyjfszqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_jyjgzyzsbdqk', 'ins_jyjgzyzsbdqk', '近一级各专业（大类）招生报到情况', TO_CLOB('
select
近一级各专业大类招生报到情况.xxbsm as 学校标识码,
近一级各专业大类招生报到情况.xnzymc as 校内专业大类名称,
近一级各专业大类招生报到情况.xnzydm as 校内专业大类代码,
近一级各专业大类招生报到情况.sf as 省份,
近一级各专业大类招生报到情况.zsjhs as 招生计划数,
近一级各专业大类招生报到情况.sjlqs as 实际录取数,
近一级各专业大类招生报到情况.dyzyzylqs as 第一志愿专业录取数,
近一级各专业大类招生报到情况.sjbdrs as 实际报到人数,
近一级各专业大类招生报到情况.tjnf as 统计年份
from ins_jyjgzyzsbdqk 近一级各专业大类招生报到情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_jyjgzyzsbdqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_jyjxyjyggxm', 'ins_jyjxyjyggxm', '教育教学研究与改革项目', TO_CLOB('
select
教育教学研究与改革项目.xxbsm as 学校标识码,
教育教学研究与改革项目.xmmc as 项目名称,
教育教学研究与改革项目.zcr as 主持人,
教育教学研究与改革项目.zcrgh as 主持人工号,
教育教学研究与改革项目.jb as 级别,
教育教学研究与改革项目.lxsj as 立项时间,
教育教学研究与改革项目.jf as 经费,
教育教学研究与改革项目.cyjss as 参与教师数,
教育教学研究与改革项目.tjnf as 统计年份,
教育教学研究与改革项目.xmlb as 项目类别,
教育教学研究与改革项目.xmlx as 项目类型,
教育教学研究与改革项目.xymc as 学院名称,
教育教学研究与改革项目.xydm as 学院代码,
教育教学研究与改革项目.hjdj as 获奖等级
from ins_jyjxyjyggxm 教育教学研究与改革项目'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_jyjxyjyggxm'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_kkqk', 'ins_kkqk', '开课情况', TO_CLOB('
select
开课情况.xxbsm as 学校标识码,
开课情况.kkh as 开课号,
开课情况.kcmc as 课程名称,
开课情况.kch as 课程号,
开课情况.kclb as 课程类别,
开课情况.kcxz as 课程性质,
开课情况.skfs as 授课方式,
开课情况.khfs as 考核方式,
开课情况.xs as 学时,
开课情况.xf as 学分,
开课情况.kkdw as 开课单位,
开课情况.dwh as 单位号,
开课情况.skjs as 授课教师,
开课情况.skjsgh as 授课教师工号,
开课情况.bkxss as 本科学生数,
开课情况.jcsyqk as 教材使用情况,
开课情况.jclx as 教材类型,
开课情况.tjnf as 统计年份,
开课情况.xnxq as 学年学期
from ins_kkqk 开课情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_kkqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_kyjd', 'ins_kyjd', '科研基地', TO_CLOB('
select
科研基地.xxbsm as 学校标识码,
科研基地.kyjdmc as 科研基地名称,
科研基地.ssdwmc as 所属单位名称,
科研基地.ssdwh as 所属单位号,
科研基地.kyjdlx as 科研基地类别,
科研基地.gjqk as 共建情况,
科研基地.tjnf as 统计年份
from ins_kyjd 科研基地'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_kyjd'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_sjjysbkjxxmjsqk', 'ins_sjjysbkjxxmjsqk', '省级及以上本科教学项目建设情况', TO_CLOB('
select
省级及以上本科教学项目建设情况.xxbsm as 学校标识码,
省级及以上本科教学项目建设情况.xmmc as 项目名称,
省级及以上本科教学项目建设情况.xmlb as 项目类别,
省级及以上本科教学项目建设情况.xmjb as 项目级别,
省级及以上本科教学项目建设情况.zcrgh as 主持人工号,
省级及以上本科教学项目建设情况.zcrxm as 主持人姓名,
省级及以上本科教学项目建设情况.hpsj as 获批时间,
省级及以上本科教学项目建设情况.tjnf as 统计年份
from ins_sjjysbkjxxmjsqk 省级及以上本科教学项目建设情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_sjjysbkjxxmjsqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_syjxxnfzsysfzx', 'ins_syjxxnfzsysfzx', '实验教学示范中心、虚拟仿真实验示范中心', TO_CLOB('
select
实验教学示范中心虚拟仿真实验示范中心.xxbsm as 学校标识码,
实验教学示范中心虚拟仿真实验示范中心.zxmc as 中心名称,
实验教学示范中心虚拟仿真实验示范中心.jb as 级别,
实验教学示范中心虚拟仿真实验示范中心.slsj as 设立时间,
实验教学示范中心虚拟仿真实验示范中心.xnncdxnjxrss as 学年内承担校内教学人时数,
实验教学示范中心虚拟仿真实验示范中心.xnncdxnwsyxms as 学年内承担校内外实验项目数,
实验教学示范中心虚拟仿真实验示范中心.xnndwkfrsh as 学年内对外开放人时数,
实验教学示范中心虚拟仿真实验示范中心.sfyhyqygj as 是否与行业企业共建,
实验教学示范中心虚拟仿真实验示范中心.tzlj as 跳转路径,
实验教学示范中心虚拟仿真实验示范中心.tjnf as 统计年份,
实验教学示范中心虚拟仿真实验示范中心.sfsyjxsfzx as 是否实验教学示范中心,
实验教学示范中心虚拟仿真实验示范中心.sfxnfzsysfzx as 是否虚拟仿真实验示范中心
from ins_syjxxnfzsysfzx 实验教学示范中心虚拟仿真实验示范中心'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_syjxxnfzsysfzx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_tsg', 'ins_tsg', '图书馆', TO_CLOB('
select
图书馆.xxbsm as 学校标识码,
图书馆.xxtsgsl as 学校图书馆数量,
图书馆.ylszws as 阅览室座位数,
图书馆.tszl as 图书总量万册,
图书馆.dztscs as 电子图书册数册,
图书馆.xwlwsl as 学位论文数量册,
图书馆.yspsl as 音视频数量小时,
图书馆.dzqkcs as 电子期刊册数册,
图书馆.tjnf as 统计年份
from ins_tsg 图书馆'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_tsg'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_tsxzqk', 'ins_tsxzqk', '图书新增情况', TO_CLOB('
select
图书新增情况.xxbsm as 学校标识码,
图书新增情况.xzzztssl as 新增纸质图书数量,
图书新增情况.zztsjf as 纸质图书经费,
图书新增情况.dzzljf as 电子资源经费,
图书新增情况.tsltl as 当年图书流通量,
图书新增情况.dzzyfwl as 当年电子资源访问量,
图书新增情况.dzzyxzl as 当年电子资源下载量,
图书新增情况.tjnf as 统计年份
from ins_tsxzqk 图书新增情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_tsxzqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_xgjsqk', 'ins_xgjsqk', '相关教师情况', TO_CLOB('
select
相关教师情况.xxbsm as 学校标识码,
相关教师情况.jzfdysl as 兼职辅导员数量,
相关教师情况.cxcyjjzzjs as 创新创业教育专职教师,
相关教师情况.cxcyjjdss as 创新创业兼职导师数,
相关教师情况.jyzdzzjs as 就业指导专职教师,
相关教师情况.gzdws as 挂职单位数,
相关教师情况.gzdlrs as 挂职锻炼人数,
相关教师情况.jzcyrs as 兼职创业人数,
相关教师情况.lgcyrs as 离岗创业人数,
相关教师情况.tjnf as 统计年份
from ins_xgjsqk 相关教师情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_xgjsqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_xnfzsyjxxm', 'ins_xnfzsyjxxm', '虚拟仿真实验教学项目', TO_CLOB('
select
虚拟仿真实验教学项目.xxbsm as 学校标识码,
虚拟仿真实验教学项目.syxmmc as 实验项目名称,
虚拟仿真实验教学项目.jb as 级别,
虚拟仿真实验教学项目.slsj as 设立时间,
虚拟仿真实验教学项目.xnncdbxjxrss as 学年内承担本校教学人时数,
虚拟仿真实验教学项目.xnnxmlls as 学年内项目浏览数,
虚拟仿真实验教学项目.xnnxmcyrs as 学年内项目参与人数,
虚拟仿真实验教学项目.xmxs as 项目学时,
虚拟仿真实验教学项目.visit as 浏览量,
虚拟仿真实验教学项目.url as 跳转地址,
虚拟仿真实验教学项目.xnxq as 学年学期,
虚拟仿真实验教学项目.tjnf as 统计年份
from ins_xnfzsyjxxm 虚拟仿真实验教学项目'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_xnfzsyjxxm'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_xnwsxsjsxjd', 'ins_xnwsxsjsxjd', '校内外实习、实践、实训基地', TO_CLOB('
select
校内外实习实践实训基地.xxbsm as 学校标识码,
校内外实习实践实训基地.jdmc as 基地名称,
校内外实习实践实训基地.dd as 地点,
校内外实习实践实训基地.jlsj as 建立时间,
校内外实习实践实训基地.mxxnzy as 面向校内专业,
校内外实习实践实训基地.xnzydm as 校内专业代码,
校内外实习实践实训基地.sfscysxjd as 是否是创业实习基地,
校内外实习实践实训基地.sfssfxjysjjd as 是否是示范性教育实践基地,
校内外实习实践实训基地.jnxszs as 当年接纳学生总数,
校内外实习实践实训基地.sfyhyqygj as 是否与行业企业共建,
校内外实习实践实训基地.tjnf as 统计年份,
校内外实习实践实训基地.sfyx as 是否有效,
校内外实习实践实训基地.jb as 级别
from ins_xnwsxsjsxjd 校内外实习实践实训基地'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_xnwsxsjsxjd'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_xsst', 'ins_xsst', '学生社团', TO_CLOB('
select
学生社团.xxbsm as 学校标识码,
学生社团.stzs as 社团总数,
学生社团.stsxzzl as 社团思想政治类,
学生社团.stxskjl as 社团学术科技类,
学生社团.stwhtyl as 社团文化体育类,
学生社团.stzygyl as 社团志愿公益类,
学生社团.stcxcyl as 社团创新创业类,
学生社团.qtst as 其他社团,
学生社团.cyrcszs as 参与人次数总数,
学生社团.sxzzlcyrcs as 思想政治类参与人次数,
学生社团.xskjlcyrcs as 学术科技类参与人次数,
学生社团.whtylcyrcs as 文化体育类参与人次数,
学生社团.zygylcyrcs as 志愿公益类参与人次数,
学生社团.cxcylcyrcs as 创新创业类参与人次数,
学生社团.qtcyrcs as 其他参与人次数,
学生社团.tjnf as 统计年份
from ins_xsst 学生社团'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_xsst'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_xstzjkdbl', 'ins_xstzjkdbl', '学生体质健康达标率', TO_CLOB('
select
学生体质健康达标率.xxbsm as 学校标识码,
学生体质健康达标率.xnzydm as 校内专业大类代码,
学生体质健康达标率.xnzymc as 校内专业大类名称,
学生体质健康达标率.cytzcsrs as 参与体质测试人数,
学生体质健康达标率.jyjbyscytzcsrs as 近一届毕业生参与体质测试人数,
学生体质健康达标率.cshgrs as 测试合格人数,
学生体质健康达标率.jyjbyscshgrs as 近一届毕业生测试合格人数,
学生体质健康达标率.tjnf as 统计年份,
学生体质健康达标率.xnxq as 学年学期
from ins_xstzjkdbl 学生体质健康达标率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_xstzjkdbl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_xxgk', 'ins_xxgk', '学校概况', TO_CLOB('
select
学校概况.xxbsm as 学校标识码,
学校概况.xxmc as 学校名称,
学校概况.dm as 代码,
学校概况.ywmc as 英文名称,
学校概况.bxlx as 办学类型,
学校概况.xxxz as 学校性质,
学校概况.jbz as 举办者,
学校概况.zgbm as 主管部门,
学校概况.xxwz as 学校网址,
学校概况.kbbkjynf as 开办本科教育年份,
学校概况.xqmcjdz as 校区名称及地址,
学校概况.xxnr as 校训内容,
学校概况.dwyfzmbnr as 定位与发展目标内容,
学校概况.xm as 姓名,
学校概况.lxdh as 联系电话,
学校概况.lxdzyx as 联系电子邮箱,
学校概况.tjnf as 统计年份,
学校概况.xxszsf as 学校所在省份,
学校概况.bxdwnr as 办学定位内容,
学校概况.fzmbnr as 发展目标内容,
学校概况.rcpymb as 人才培养目标内容,
学校概况.bxdwdbt as 办学定位顶部图,
学校概况.bxdwbjt as 办学定位背景图
from ins_xxgk 学校概况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_xxgk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_xxsljbqk', 'ins_xxsljbqk', '学生数量基本情况', TO_CLOB('
select
学生数量基本情况.xxbsm as 学校标识码,
学生数量基本情况.ptbkxss as 普通本科学生数,
学生数量基本情况.ygwdxlhpyxss as 与国境外大学联合培养的学生数,
学生数量基本情况.dexsxwxss as 第二学士学位学生数,
学生数量基本情况.ptgzxss as 普通高职含专科学生数,
学生数量基本情况.ssyjss as 硕士研究生数,
学生数量基本情况.qrzssyjss as 硕士研究生数全日制,
学生数量基本情况.fqrzssyjss as 硕士研究生数非全日制,
学生数量基本情况.bsyjss as 博士研究生数,
学生数量基本情况.qrzbsyjss as 博士研究生数全日制,
学生数量基本情况.fqrzbsyjss as 博士研究生数非全日制,
学生数量基本情况.lxss as 留学生数,
学生数量基本情况.xljybkslxss as 学历教育本科生留学生数,
学生数量基本情况.fxljybkslxss as 非学历教育本科生留学生数,
学生数量基本情况.xljyssyjslxss as 学历教育硕士研究生留学生数,
学生数量基本情况.fxljyssyjslxss as 非学历教育硕士研究生留学生数,
学生数量基本情况.xljybsyjslxss as 学历教育博士研究生留学生数,
学生数量基本情况.fxljybsyjslxss as 非学历教育博士研究生留学生数,
学生数量基本情况.sybsxwlxss as 授予博士学位的留学生数,
学生数量基本情况.ptykss as 普通预科生数,
学生数量基本情况.jxss as 进修生数,
学生数量基本情况.crtcxss as 成人脱产学生数,
学生数量基本情况.y')||TO_CLOB('dxss as 夜大业余学生数,
学生数量基本情况.hsxss as 函授学生数,
学生数量基本情况.wlxss as 网络学生数,
学生数量基本情况.zkxss as 自考学生数,
学生数量基本情况.zzzxss as 中职在校生数,
学生数量基本情况.ssmzxss as 少数民族学生数,
学生数量基本情况.ssmzyjss as 研究生数,
学生数量基本情况.bkss as 本科生数,
学生数量基本情况.zkss as 专科生数,
学生数量基本情况.ykbrs as 预科班,
学生数量基本情况.tjnf as 统计年份
from ins_xxsljbqk 学生数量基本情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_xxsljbqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_yszyqk', 'ins_yszyqk', '优势（一流）专业情况', TO_CLOB('
select
优势一流专业情况.xxbsm as 学校标识码,
优势一流专业情况.xnzydm as 校内专业大类代码,
优势一流专业情况.xnzymc as 校内专业大类名称,
优势一流专业情况.zylx as 专业类型,
优势一流专业情况.hptgsj as 获批通过时间,
优势一流专业情况.tzlj as 跳转链接,
优势一流专业情况.tjnf as 统计年份,
优势一流专业情况.jb as 级别
from ins_yszyqk 优势一流专业情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_yszyqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_zdyjzmj', 'ins_zdyjzmj', '占地与建筑面积', TO_CLOB('
select
占地与建筑面积.xxbsm as 学校标识码,
占地与建筑面积.zzdmj as 总占地面积,
占地与建筑面积.zdmjxxcq as 占地面积学校产权,
占地与建筑面积.zdmjxxcqydcdmj as 占地面积学校产权运动场地面积,
占地与建筑面积.zdmjfxxcq as 占地面积非学校产权,
占地与建筑面积.zdmjfxxcqdlsy as 占地面积非学校产权独立使用,
占地与建筑面积.zdmjfxxcqgtsy as 占地面积非学校产权共同使用,
占地与建筑面积.zjzmj as 总建筑面积,
占地与建筑面积.jzmjxxcq as 建筑面积学校产权,
占地与建筑面积.jzmjfxxcq as 建筑面积非学校产权,
占地与建筑面积.jzmjfxxcqdlsy as 建筑面积非学校产权独立使用,
占地与建筑面积.jzmjfxxcqgtsy as 建筑面积非学校产权共同使用,
占地与建筑面积.tjnf as 统计年份
from ins_zdyjzmj 占地与建筑面积'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_zdyjzmj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_zydlqk', 'ins_zydlqk', '专业大类情况表', TO_CLOB('
select
专业大类情况表.xxbsm as 学校标识码,
专业大类情况表.dlmc as 大类名称,
专业大类情况表.dldm as 大类代码,
专业大类情况表.flsj as 分流时间,
专业大类情况表.xnzydm as 包含校内专业代码,
专业大类情况表.xnzymc as 包含校内专业名称,
专业大类情况表.tjnf as 统计年份
from ins_zydlqk 专业大类情况表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_zydlqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_zykjxssqk', 'ins_zykjxssqk', '专业课教学实施情况', TO_CLOB('
select
专业课教学实施情况.xxbsm as 学校标识码,
专业课教学实施情况.xnzymc as 校内专业大类名称,
专业课教学实施情况.xnzydm as 校内专业大类代码,
专业课教学实施情况.kkh as 开课号,
专业课教学实施情况.kcmc as 课程名称,
专业课教学实施情况.kclb as 课程类别,
专业课教学实施情况.xf as 学分,
专业课教学实施情况.nj as 年级,
专业课教学实施情况.sfzyhxkc as 是否专业核心课程,
专业课教学实施情况.tjnf as 统计年份
from ins_zykjxssqk 专业课教学实施情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_zykjxssqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_zypyjhb', 'ins_zypyjhb', '专业培养计划表', TO_CLOB('
select
专业培养计划表.xxbsm as 学校标识码,
专业培养计划表.xnzydm as 校内专业代码,
专业培养计划表.xnzymc as 校内专业名称,
专业培养计划表.zydtrgh as 专业带头人工号,
专业培养计划表.zydtrxm as 专业带头人姓名,
专业培养计划表.xszz as 学时总数,
专业培养计划表.bxkxss as 必修课学时数,
专业培养计划表.xxkxss as 选修课学时数,
专业培养计划表.ldjyxss as 劳动教育学时数,
专业培养计划表.lljxxss as 理论教学学时数,
专业培养计划表.syjxxss as 实验教学学时数,
专业培养计划表.jzxsjhjzs as 集中性实践环节周数,
专业培养计划表.zxfs as 总学分数,
专业培养计划表.ggbxkxfs as 公共必修课学分数,
专业培养计划表.ggxxkxfs as 公共选修课学分数,
专业培养计划表.zybxkxfs as 专业必修课学分数,
专业培养计划表.zyxxkxfs as 专业选修课学分数,
专业培养计划表.jzxsjjjhjxfs as 集中性实践教学环节学分数,
专业培养计划表.lljxxfs as 理论教学学分数,
专业培养计划表.syjjxfs as 实验教学学分数,
专业培养计划表.kwkjhdxfs as 课外科技活动学分数,
专业培养计划表.cxcyjyxfs as 创新创业教育学分数,
专业培养计划表.ggyskcxfs as 公共艺术课程学分数,
专业培养计划表.tjnf as 统计年份
from ins_zypyjhb 专业培养计划表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_zypyjhb'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_tkjlb', 'abd_sch_tkjlb', '听课记录表', TO_CLOB('
select
听课记录表.xxbsm as 学校标识码,
听课记录表.xnxqdm as 学期,
听课记录表.kkxy as 开课学院,
听课记录表.kcdm as 课程号,
听课记录表.kcmc as 课程名称,
听课记录表.kclx as 课程类型,
听课记录表.jxbid as 教学班号,
听课记录表.jxbmc as 教学班名称,
听课记录表.tkrgh as 听课人工号,
听课记录表.tkrxm as 听课人姓名,
听课记录表.tkrsf as 听课人身份,
听课记录表.bprgh as 被评人工号,
听课记录表.bprxm as 被评人姓名,
听课记录表.fs as 分数,
听课记录表.tbsj as 同步时间,
听课记录表.title as 课程类型听课人身份原始数据,
听课记录表.tksj as 听课时间,
听课记录表.xnmc as 学年名称,
听课记录表.xndm as 学年代码,
听课记录表.xnxq as 学年学期,
听课记录表.tkdd as 听课等第,
听课记录表.sfyzkc as 是否优质课程,
听课记录表.kkxydm as 开课学院代码
from abd_sch_tkjlb 听课记录表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_tkjlb'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_zzjgxx', 'abd_sch_zzjgxx', '组织机构信息', TO_CLOB('
select
组织机构信息.xxbsm as 学校标识码,
组织机构信息.jgdm as 机构代码,
组织机构信息.jgmc as 机构名称,
组织机构信息.jgjc as 机构简称,
组织机构信息.lsjgdm as 隶属机构代码,
组织机构信息.lsjg as 隶属机构,
组织机构信息.jglb as 机构类别,
组织机构信息.jlny as 建立年月,
组织机构信息.jgpx as 机构排序
from abd_sch_zzjgxx 组织机构信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_zzjgxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_cxhzxtyr', 'abd_sch_cxhzxtyr', '产学合作协同育人立项项目', TO_CLOB('
select
产学合作协同育人立项项目.xxbsm as 学校标识码,
产学合作协同育人立项项目.dwmc as 单位名称,
产学合作协同育人立项项目.dwh as 单位号,
产学合作协同育人立项项目.xmmc as 项目名称,
产学合作协同育人立项项目.lxpc as 立项批次,
产学合作协同育人立项项目.fwzh as 发文字号,
产学合作协同育人立项项目.xmfzr as 项目负责人,
产学合作协同育人立项项目.xmlx as 项目类型,
产学合作协同育人立项项目.fwsj as 发文时间,
产学合作协同育人立项项目.lxsj as 立项时间,
产学合作协同育人立项项目.tjnf as 统计年份
from abd_sch_cxhzxtyr 产学合作协同育人立项项目'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_cxhzxtyr'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_szsfkc', 'abd_sch_szsfkc', '思政示范课程', TO_CLOB('
select
思政示范课程.xxbsm as 学校标识码,
思政示范课程.kcmx as 课程名称,
思政示范课程.kcdm as 课程代码,
思政示范课程.rq as 日期,
思政示范课程.url as 链接地址,
思政示范课程.tjnf as 统计年份
from abd_sch_szsfkc 思政示范课程'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_szsfkc'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_cxxsyxm', 'abd_sch_cxxsyxm', '创新性实验项目', TO_CLOB('
select
创新性实验项目.xxbsm as 学校标识码,
创新性实验项目.xmmc as 项目名称,
创新性实验项目.xmlx as 项目类型,
创新性实验项目.zrr as 责任人,
创新性实验项目.jb as 级别,
创新性实验项目.xymc as 学院名称,
创新性实验项目.xydm as 学院代码,
创新性实验项目.sszyxk as 所属学科专业,
创新性实验项目.xmyt as 项目依托,
创新性实验项目.tdjsrs as 团队教师人数,
创新性实验项目.tdxsrs as 团队学生人数,
创新性实验项目.tdqyrs as 团队企业人数,
创新性实验项目.tjnf as 统计年份
from abd_sch_cxxsyxm 创新性实验项目'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_cxxsyxm'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_zxjcxx', 'abd_sch_zxjcxx', '专项检查信息', TO_CLOB('
select
专项检查信息.xxbsm as 学校标识码,
专项检查信息.jcdx as 检查对象,
专项检查信息.xydm as 学院代码,
专项检查信息.xymc as 学院名称,
专项检查信息.tjnf as 统计年份,
专项检查信息.jclx as 检查类型,
专项检查信息.jcdj as 检查等级,
专项检查信息.jcsl as 检查数量,
专项检查信息.kcmbdcd as 课程目标达成度,
专项检查信息.kccjsl as 课程抽检数量
from abd_sch_zxjcxx 专项检查信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_zxjcxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_xxjxkydw', 'ins_xxjxkydw', '学校教学科研单位', TO_CLOB('
select
学校教学科研单位.xxbsm as 学校标识码,
学校教学科研单位.jxkydwmc as 教学科研单位名称,
学校教学科研单位.dwh as 单位号,
学校教学科研单位.dwzn as 单位职能,
学校教学科研单位.dwjc as 单位简称
from ins_xxjxkydw 学校教学科研单位'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_xxjxkydw'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_xxxgdzdw', 'ins_xxxgdzdw', '学校相关党政单位', TO_CLOB('
select
学校相关党政单位.xxbsm as 学校标识码,
学校相关党政单位.dzdwmc as 党政单位名称,
学校相关党政单位.dwh as 单位号,
学校相关党政单位.dwzn as 单位职能,
学校相关党政单位.dwjc as 单位简称
from ins_xxxgdzdw 学校相关党政单位'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_xxxgdzdw'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_jsjbxx', 'abd_sch_jsjbxx', '教室基本信息', TO_CLOB('
select
教室基本信息.xxbsm as 学校标识码,
教室基本信息.tjnf as 统计年份,
教室基本信息.jsdm as 教室代码,
教室基本信息.jsmc as 教室名称,
教室基本信息.jslxdm as 教室类型代码,
教室基本信息.jslx as 教室类型,
教室基本信息.jxldm as 教学楼代码,
教室基本信息.lc as 楼层,
教室基本信息.xxxqdm as 学校校区代码,
教室基本信息.sfyx as 是否有效,
教室基本信息.dwdm as 单位代码,
教室基本信息.jsls as 教室隶属,
教室基本信息.jsyt as 教室用途
from abd_sch_jsjbxx 教室基本信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_jsjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_pptdhjqk', 'abd_sch_pptdhjqk', '品牌团队获奖情况', TO_CLOB('
select
品牌团队获奖情况.xxbsm as 学校标识码,
品牌团队获奖情况.tdmc as 团队名称,
品牌团队获奖情况.xmmc as 项目名称,
品牌团队获奖情况.mxjb as 项目级别,
品牌团队获奖情况.hjdj as 获奖等级,
品牌团队获奖情况.tjnf as 统计年份
from abd_sch_pptdhjqk 品牌团队获奖情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_pptdhjqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_kcgmqk', 'abd_sch_kcgmqk', '课程规模情况', TO_CLOB('
select
课程规模情况.xxbsm as 学校标识码,
课程规模情况.gmmc as 规模名称,
课程规模情况.max as 最小值,
课程规模情况.min as 最大值,
课程规模情况.kcxz as 课程类别,
课程规模情况.gmlb as 规模类别,
课程规模情况.tjnf as 统计年份
from abd_sch_kcgmqk 课程规模情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_kcgmqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_fzlc', 'abd_sch_fzlc', '发展历程', TO_CLOB('
select
发展历程.xxbsm as 学校标识码,
发展历程.nf as 年份,
发展历程.sjmc as 事件名称,
发展历程.sjdx as 事件对象,
发展历程.xydm as 学院代码,
发展历程.xymc as 学院名称
from abd_sch_fzlc 发展历程'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_fzlc'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_tszyqk', 'abd_sch_tszyqk', '特色专业情况', TO_CLOB('
select
特色专业情况.xxbsm as 学校标识码,
特色专业情况.tjnf as 统计年份,
特色专业情况.xnzydm as 校内专业大类代码,
特色专业情况.xnzymc as 校内专业大类名称,
特色专业情况.jb as 级别,
特色专业情况.hptgsj as 获批通过时间,
特色专业情况.tzlj as 跳转链接
from abd_sch_tszyqk 特色专业情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_tszyqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_kcszqk', 'abd_sch_kcszqk', '课程思政情况', TO_CLOB('
select
课程思政情况.xxbsm as 学校标识码,
课程思政情况.szxmmc as 思政项目名称,
课程思政情况.szlx as 思政类型,
课程思政情况.jb as 级别,
课程思政情况.rdsj as 认定时间
from abd_sch_kcszqk 课程思政情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_kcszqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sch_kcljyjl', 'abd_sch_kcljyjl', '课程类教研奖励', TO_CLOB('
select
课程类教研奖励.xxbsm as 学校标识码,
课程类教研奖励.gh as 工号,
课程类教研奖励.xm as 姓名,
课程类教研奖励.szdw as 所在单位,
课程类教研奖励.cgmc as 成果名称,
课程类教研奖励.cglbjdj as 成果类别及等级,
课程类教研奖励.lxjxsj as 立项结项时间
from abd_sch_kcljyjl 课程类教研奖励'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sch_kcljyjl'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_bsdssd', 'ins_bsdssd', '博士点、硕士点', TO_CLOB('
select
博士点硕士点.yjxkdm as 一级学科代码,
博士点硕士点.mc as 名称,
博士点硕士点.xkdm as 学科代码,
博士点硕士点.dwmc as 单位名称,
博士点硕士点.dwh as 单位号,
博士点硕士点.lx as 类型,
博士点硕士点.tjnf as 统计年份
from ins_bsdssd 博士点硕士点'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_bsdssd'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_xkjs', 'ins_xkjs', '学科建设', TO_CLOB('
select
学科建设.yjxkdm as 一级学科代码,
学科建设.bshldz as 博士后流动站,
学科建设.bszyxwsqlb as 博士专业学位授权类别,
学科建设.sszyxwsqlb as 硕士专业学位授权类别,
学科建设.bkzyzz as 本科专业总数,
学科建设.xzz as 新专业,
学科建设.zkzy as 专科专业,
学科建设.tjnf as 统计年份
from ins_xkjs 学科建设'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_xkjs'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_ylxk', 'ins_ylxk', '一流学科', TO_CLOB('
select
一流学科.yjxkdm as 一级学科代码,
一流学科.ylxkmc as 一流学科名称,
一流学科.xkdm as 学科代码,
一流学科.ssdw as 所属单位,
一流学科.dwh as 单位号,
一流学科.xkml as 学科门类,
一流学科.jb as 级别,
一流学科.tjnf as 统计年份
from ins_ylxk 一流学科'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_ylxk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_sub_xkxx', 'abd_sub_xkxx', '学科信息', TO_CLOB('
select
学科信息.yjxkdm as 一级学科代码,
学科信息.yjxkmc as 一级学科名称,
学科信息.xkmldm as 学科门类代码,
学科信息.xkmlmc as 学科门类名称,
学科信息.xwlx as 学位类型,
学科信息.sqjb as 授权级别,
学科信息.sqsjssd as 授权时间硕士点,
学科信息.sqsjbsd as 授权时间博士点,
学科信息.kszsrq as 开始招生日期,
学科信息.szrq as 设站日期,
学科信息.zt as 状态,
学科信息.gbdm as 国标代码,
学科信息.xkmlfl as 学科门类分类,
学科信息.sfssd as 是否硕士点,
学科信息.sfbsd as 是否博士点,
学科信息.xkjc as 学科简称,
学科信息.px as 排序
from abd_sub_xkxx 学科信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_sub_xkxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tcl_kccj', 'abd_tcl_kccj', '课程成绩', TO_CLOB('
select
课程成绩.xnxqdm as 学年学期,
课程成绩.jxbid as 教学班ID,
课程成绩.kxh as 课序号,
课程成绩.kch as 课程号,
课程成绩.xh as 学号,
课程成绩.kssj as 考试时间,
课程成绩.xdfsdm as 修读方式,
课程成绩.cxckdm as 重修重考,
课程成绩.cjlrztdm as 成绩录入状态,
课程成绩.djcjlxdm as 等级成绩类型,
课程成绩.pyfadm as 培养方案代码,
课程成绩.kclbdm as 课程类别,
课程成绩.xgxklbdm as 校公选课类别,
课程成绩.kcxzdm as 课程性质,
课程成绩.kslxdm as 考试类型,
课程成绩.tdkch as 替代课程号,
课程成绩.tsyydm2 as 特殊原因,
课程成绩.pscj as 平时成绩,
课程成绩.qzcj as 期中成绩,
课程成绩.qmcj as 期末成绩,
课程成绩.sycj as 实验成绩,
课程成绩.djcj as 等级成绩,
课程成绩.zcj as 总成绩,
课程成绩.sfcyxfjjs as 是否参与学分绩计算,
课程成绩.sfyx as 是否有效,
课程成绩.sfjg as 是否及格,
课程成绩.czr as 操作人,
课程成绩.sjcj as 实践成绩,
课程成绩.jd as 绩点,
课程成绩.sfzx as 是否主修,
课程成绩.ycjsfhk as 原成绩是否缓考,
课程成绩.cjrdfsdm as 成绩认定方式,
课程成绩.yzcj as 原总成绩,
课程成绩.jf as 加分,
课程成绩.ly as 成绩录入来源,
课程成绩.zszcj as 折算前成绩数值,
课程成绩.zsdjcj')||TO_CLOB(' as 折算前成绩显示,
课程成绩.zsgs as 折算公式,
课程成绩.jasdm as 教室代码,
课程成绩.zwh as 座位号,
课程成绩.sjksrq as 实际考试日期,
课程成绩.ksapbh as 考试安排编号,
课程成绩.zcjxsz as 总成绩显示值
from abd_tcl_kccj 课程成绩'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tcl_kccj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tcl_skddap', 'abd_tcl_skddap', '上课地点安排', TO_CLOB('
select
上课地点安排.kbid as 课表ID,
上课地点安排.xnxqdm as 学年学期,
上课地点安排.kch as 课程号,
上课地点安排.kxh as 课序号,
上课地点安排.jxbid as 教学班ID,
上课地点安排.skzc as 上课周次,
上课地点安排.skxq as 上课星期,
上课地点安排.ksjc as 开始节次,
上课地点安排.jsjc as 结束节次,
上课地点安排.jasdm as 教室代码,
上课地点安排.sjsm as 时间说明,
上课地点安排.xslxdm as 学时类型,
上课地点安排.qpyy as 强排原因,
上课地点安排.zdyjasmc as 自定义教室名称此时JASDM为空,
上课地点安排.zdykssj as 自定义开始时间,
上课地点安排.zdyjssj as 自定义结束时间
from abd_tcl_skddap 上课地点安排'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tcl_skddap'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tcl_jsskap', 'abd_tcl_jsskap', '教师上课安排', TO_CLOB('
select
教师上课安排.kbid as 课表ID,
教师上课安排.xnxqdm as 学年学期,
教师上课安排.kch as 课程号,
教师上课安排.kxh as 课序号,
教师上课安排.jxbid as 教学班ID,
教师上课安排.skzc as 上课周次,
教师上课安排.skxq as 上课星期,
教师上课安排.ksjc as 开始节次,
教师上课安排.jsjc as 结束节次,
教师上课安排.jsh as 教师号,
教师上课安排.sjsm as 时间说明,
教师上课安排.zyid as 占用ID,
教师上课安排.xslxdm as 学时类型
from abd_tcl_jsskap 教师上课安排'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tcl_jsskap'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tcl_rwjs', 'abd_tcl_rwjs', '任务教师', TO_CLOB('
select
任务教师.jxbid as 教学班ID,
任务教师.jsh as 教师号,
任务教师.skzc as 上课周次,
任务教师.sffzjs as 是否负责教师,
任务教师.xnxqdm as 学年学期,
任务教师.kch as 课程号,
任务教师.kxh as 课序号,
任务教师.zcdm as 职称代码,
任务教师.xslxdm as 学时类型
from abd_tcl_rwjs 任务教师'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tcl_rwjs'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tcl_rwbj', 'abd_tcl_rwbj', '任务班级', TO_CLOB('
select
任务班级.jxbid as 教学班ID,
任务班级.bjdm as 班级名称,
任务班级.rs as 人数,
任务班级.xnxqdm as 学年学期,
任务班级.kch as 课程号,
任务班级.kxh as 课序号
from abd_tcl_rwbj 任务班级'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tcl_rwbj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tcl_jxrw', 'abd_tcl_jxrw', '教学任务', TO_CLOB('
select
教学任务.kclbdm as 课程类别,
教学任务.kcxzdm as 课程性质,
教学任务.jxbid as 教学班ID,
教学任务.xnxqdm as 学年学期,
教学任务.kch as 课程号,
教学任务.kxh as 课序号,
教学任务.jxbmc as 教学班名称
from abd_tcl_jxrw 教学任务'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tcl_jxrw'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tcl_jxrwxgxx', 'abd_tcl_jxrwxgxx', '教学任务相关信息', TO_CLOB('
select
教学任务相关信息.jxbid as 教学班ID,
教学任务相关信息.xs as 学时,
教学任务相关信息.skxs as 授课学时,
教学任务相关信息.kslxdm as 考试类型,
教学任务相关信息.krl as 课容量,
教学任务相关信息.jcjg as 任务检查结果,
教学任务相关信息.skzc as 上课周次,
教学任务相关信息.sfxzxb as 是否限制性别,
教学任务相关信息.nsrs as 男生人数,
教学任务相关信息.nvsrs as 女生人数,
教学任务相关信息.rwztdm as 任务状态,
教学任务相关信息.tyxmdm as 体育项目,
教学任务相关信息.skjs as 授课教师,
教学任务相关信息.jxfsdm as 教学方式,
教学任务相关信息.skbj as 上课班级,
教学任务相关信息.sfcxb as 是否重修班,
教学任务相关信息.pkdwdm as 排课单位,
教学任务相关信息.xxxqdm as 学校校区,
教学任务相关信息.sfxypk as 是否需要排课,
教学任务相关信息.pkpcdm as 排课批次,
教学任务相关信息.pkctbjdm as 排课冲突标记,
教学任务相关信息.pkztdm as 排课状态,
教学任务相关信息.xkkzh as 选课课组号,
教学任务相关信息.ypxs as 已排学时,
教学任务相关信息.ypsjdd as 已排时间地点,
教学任务相关信息.sfsdpk as 是否锁定排课,
教学任务相关信息.sfxyks as 是否需要考试,
教学任务相关信息.sflrcj as 是否录入成绩,
教学任务相关信息.shyj as 审核意见,
教学任务相关信息.sqztdm as 申请状态,
教')||TO_CLOB('学任务相关信息.sftbxkxz as 是否同步选课限制,
教学任务相关信息.skjssj as 上课教师时间,
教学任务相关信息.tjnj as 推荐年级,
教学任务相关信息.ypsj as 已排时间,
教学任务相关信息.tkkrl as 停开课容量,
教学任务相关信息.jxblxdm as 教学班类型,
教学任务相关信息.sfbk as 是否补考,
教学任务相关信息.skyzdm as 授课语种,
教学任务相关信息.sfmooc as 是否MOOC,
教学任务相关信息.jysdm as 教研室,
教学任务相关信息.skpt as 授课平台,
教学任务相关信息.ptxx as 平台信息,
教学任务相关信息.jqxx as 建群信息,
教学任务相关信息.fjjxdjdm as 分级教学等级,
教学任务相关信息.fxxs01 as 任务课程分项学时01,
教学任务相关信息.fxxs02 as 任务课程分项学时02,
教学任务相关信息.fxxs03 as 任务课程分项学时03,
教学任务相关信息.fxxs04 as 任务课程分项学时04,
教学任务相关信息.fxxs05 as 任务课程分项学时05,
教学任务相关信息.fxxs06 as 任务课程分项学时06,
教学任务相关信息.fxxs07 as 任务课程分项学时07,
教学任务相关信息.fxxs08 as 任务课程分项学时08,
教学任务相关信息.fxxs09 as 任务课程分项学时09,
教学任务相关信息.fxxs10 as 任务课程分项学时10,
教学任务相关信息.fxxs11 as 任务课程分项学时11,
教学任务相关信息.fxxs12 as 任务课程分项学时12,
教学任务相关信息.fxxs13 as 任务课程分项学时')||TO_CLOB('13,
教学任务相关信息.fxxs14 as 任务课程分项学时14,
教学任务相关信息.sfzzsybkmssc as 是否正在使用板块模式生成,
教学任务相关信息.zjjs as 主讲教师,
教学任务相关信息.kkzc as 开课周次,
教学任务相关信息.jkzc as 结课周次,
教学任务相关信息.sqr as 申请人,
教学任务相关信息.sqrxm as 申请人姓名,
教学任务相关信息.sqrq as 申请日期,
教学任务相关信息.yxxk as 允许选课,
教学任务相关信息.jzxk as 禁止选课,
教学任务相关信息.skjsh as 授课教师号,
教学任务相关信息.skjsm as 授课教师名,
教学任务相关信息.sfajzsjzmsap as 是否按集中实践周模式安排,
教学任务相关信息.pksffb as 排课是否发布,
教学任务相关信息.yxxkxzms as 允许选课限制描述,
教学任务相关信息.jzxkxzms as 禁止选课限制描述,
教学任务相关信息.xkxzms as 选课限制描述,
教学任务相关信息.bkskrl as 是否需要选课,
教学任务相关信息.bkskrlv as 本科生课容量,
教学任务相关信息.sftk as 是否停开,
教学任务相关信息.skrs as 上课人数
from abd_tcl_jxrwxgxx 教学任务相关信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tcl_jxrwxgxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgjbxx', 'abd_tea_jzgjbxx', '教职工基本信息', TO_CLOB('
select
教职工基本信息.zgh as 教职工号,
教职工基本信息.xm as 姓名,
教职工基本信息.xb as 性别,
教职工基本信息.mz as 民族,
教职工基本信息.sfzjlx as 身份证件类型,
教职工基本信息.gatqw as 港澳台侨外,
教职工基本信息.zjxy as 宗教信仰,
教职工基本信息.zzmm as 政治面貌,
教职工基本信息.jg as 籍贯,
教职工基本信息.hkszs as 户口所在省,
教职工基本信息.hkszds as 户口所在地市,
教职工基本信息.hkszd as 户口所在地,
教职工基本信息.xx as 血型,
教职工基本信息.csrq as 出生日期,
教职工基本信息.csd as 出生地,
教职工基本信息.gjdq as 国家地区,
教职工基本信息.hyzk as 婚姻状况,
教职工基本信息.zgxl as 最高学历,
教职工基本信息.zgxw as 最高学位,
教职工基本信息.dyxl as 第一学历,
教职工基本信息.dyxlbyyx as 第一学历毕业院校,
教职工基本信息.dyxlbyyxlx as 第一学历毕业院校类型,
教职工基本信息.zgxlbyyx as 最高学历毕业院校,
教职工基本信息.zgxlbyyxlx as 最高学历毕业院校类型,
教职工基本信息.zhbyyx as 最后毕业院校,
教职工基本信息.zhbyyxlx as 最后毕业院校类型,
教职工基本信息.sfbxby as 是否本校毕业,
教职工基本信息.sjhm as 手机号码,
教职工基本信息.dzyx as 电子邮箱,
教职工基本信息.jtdz as 家庭地址,
教职工基本信息.ssjgdm as 所属机构代码,
教职工基本信息.s')||TO_CLOB('sjg as 所属机构,
教职工基本信息.ssxdm as 所属系代码,
教职工基本信息.ssx as 所属系,
教职工基本信息.jzglb as 教职工类别,
教职工基本信息.jzgly as 教职工来源,
教职工基本信息.bzlb as 编制类别,
教职工基本信息.yrfs as 用人方式,
教职工基本信息.cjny as 从教年月,
教职工基本信息.lxny as 来校年月,
教职工基本信息.jzjsprlb as 兼职教师聘任类别,
教职工基本信息.dslb as 导师类别,
教职工基本信息.fdylb as 辅导员类别,
教职工基本信息.sfssxjs as 是否双师型教师,
教职工基本信息.sfsjt as 是否双肩挑,
教职工基本信息.xkml as 学科门类,
教职工基本信息.yjxk as 一级学科,
教职工基本信息.ejxk as 二级学科,
教职工基本信息.yjfx as 研究方向,
教职工基本信息.jzgdqzt as 教职工当前状态,
教职工基本信息.lxrq as 离校日期,
教职工基本信息.yjtxrq as 预计退休日期,
教职工基本信息.zyjszw as 专业技术职务,
教职工基本信息.zyjszwjb as 专业技术职务级别,
教职工基本信息.zyjsgwdj as 专业技术岗位等级,
教职工基本信息.glgwdj as 管理岗位等级,
教职工基本信息.gqgwdj as 工勤岗位等级,
教职工基本信息.zygwlx as 主要岗位类型,
教职工基本信息.gwmc as 岗位名称,
教职工基本信息.gbzw as 干部职务,
教职工基本信息.gbzwjb as 干部职务级别,
教职工基本信息.nl as 年龄,
教职工基本信息.rjlx ')||TO_CLOB('as 任教类型,
教职工基本信息.rjzymc as 任教专业名称,
教职工基本信息.rjzydm as 任教专业代码,
教职工基本信息.zyrjsj as 专业任教时间,
教职工基本信息.sfsyjsry as 是否实验技术人员,
教职工基本信息.sfwp as 是否外聘,
教职工基本信息.glrylb as 管理人员类别,
教职工基本信息.sfbds as 是否班导师,
教职工基本信息.sfskjs as 是否授课教师,
教职工基本信息.sfszkjs as 是否思政课教师,
教职工基本信息.kzrq as 快照日期,
教职工基本信息.jkzk as 健康状况,
教职工基本信息.yjjgdm as 一级机构代码,
教职工基本信息.yjjg as 一级机构,
教职工基本信息.cjgzny as 参加工作年月,
教职工基本信息.sffdy as 是否辅导员,
教职工基本信息.zyjsgwlb as 专业技术岗位类别,
教职工基本信息.zrjslx as 专任教师类型,
教职工基本信息.zgxwcc as 最高学位层次,
教职工基本信息.zyjszwdm as 专业技术职务代码,
教职工基本信息.zyjszwjbdm as 专业技术职务级别代码
from abd_tea_jzgjbxx 教职工基本信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_gccjxyjtd', 'ins_gccjxyjtd', '高层次教学、研究团队', TO_CLOB('
select
高层次教学研究团队.tdmc as 团队名称,
高层次教学研究团队.fzr as 负责人,
高层次教学研究团队.fzrgh as 负责人工号,
高层次教学研究团队.lx as 类型,
高层次教学研究团队.hdsj as 获得时间,
高层次教学研究团队.tjnf as 统计年份
from ins_gccjxyjtd 高层次教学研究团队'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_gccjxyjtd'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_gccrc', 'ins_gccrc', '高层次人才', TO_CLOB('
select
高层次人才.gh as 工号,
高层次人才.xm as 姓名,
高层次人才.lx as 类型,
高层次人才.yjfx as 研究方向,
高层次人才.hdsj as 获得时间,
高层次人才.tjnf as 统计年份
from ins_gccrc 高层次人才'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_gccrc'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_jscbzzhzbjcqk', 'ins_jscbzzhzbjcqk', '教师出版专著和主编教材情况', TO_CLOB('
select
教师出版专著和主编教材情况.gh as 工号,
教师出版专著和主编教材情况.jsxm as 教师姓名,
教师出版专著和主编教材情况.zzhjcmc as 专著或教材名称,
教师出版专著和主编教材情况.isbn as ISBN书号,
教师出版专著和主编教材情况.lb as 类别,
教师出版专著和主编教材情况.cbs as 出版社,
教师出版专著和主编教材情况.cbsj as 出版时间,
教师出版专著和主编教材情况.tjnf as 统计年份,
教师出版专著和主编教材情况.sfcxcyjc as 是否创业创业类教材,
教师出版专著和主编教材情况.jchj as 教材获奖
from ins_jscbzzhzbjcqk 教师出版专著和主编教材情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_jscbzzhzbjcqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_jskycgzhqk', 'ins_jskycgzhqk', '教师科研成果转化情况', TO_CLOB('
select
教师科研成果转化情况.gh as 工号,
教师科研成果转化情况.jsxm as 教师姓名,
教师科研成果转化情况.xmcgmc as 项目成果名称,
教师科研成果转化情况.zhfz as 转化方式,
教师科研成果转化情况.zhje as 转化金额,
教师科研成果转化情况.cyxss as 参与学生数,
教师科研成果转化情况.tjnf as 统计年份
from ins_jskycgzhqk 教师科研成果转化情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_jskycgzhqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_jspxjxjlqk', 'ins_jspxjxjlqk', '教师培训进修、交流情况', TO_CLOB('
select
教师培训进修交流情况.gh as 工号,
教师培训进修交流情况.jsxm as 教师姓名,
教师培训进修交流情况.pxjxjllx as 培训进修交流类型,
教师培训进修交流情况.kssj as 开始时间,
教师培训进修交流情况.jssj as 结束时间,
教师培训进修交流情况.bz as 备注,
教师培训进修交流情况.tjnf as 统计年份,
教师培训进修交流情况.pxfs as 培训方式,
教师培训进修交流情况.dwh as 单位号,
教师培训进修交流情况.dwmc as 单位名称
from ins_jspxjxjlqk 教师培训进修交流情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_jspxjxjlqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_jszlsqqk', 'ins_jszlsqqk', '教师专利（著作权）授权情况', TO_CLOB('
select
教师专利著作权授权情况.gh as 工号,
教师专利著作权授权情况.jsxm as 教师姓名,
教师专利著作权授权情况.mc as 名称,
教师专利著作权授权情况.lx as 类型,
教师专利著作权授权情况.sqh as 授权号,
教师专利著作权授权情况.hpsj as 获批时间,
教师专利著作权授权情况.sfyy as 是否应用,
教师专利著作权授权情况.sfhylhzl as 是否行业联合专利著作权,
教师专利著作权授权情况.tjnf as 统计年份
from ins_jszlsqqk 教师专利著作权授权情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_jszlsqqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_jxcgj', 'ins_jxcgj', '教学成果奖', TO_CLOB('
select
教学成果奖.jsgh as 教师工号,
教学成果奖.jsxm as 教师姓名,
教学成果奖.hjcgmc as 获奖成果名称,
教学成果奖.brpm as 本人排名,
教学成果奖.wcdwpm as 完成单位排名,
教学成果奖.jb as 级别,
教学成果奖.hjsj as 获奖时间,
教学成果奖.tjnf as 统计年份,
教学成果奖.cgjj as 成果简介,
教学成果奖.zywcdw as 主要完成单位,
教学成果奖.jjtp as 简介图片,
教学成果奖.hjdj as 获奖等级
from ins_jxcgj 教学成果奖'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_jxcgj'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_jzgjbxx', 'ins_jzgjbxx', '教职工基础信息', TO_CLOB('
select
教职工基础信息.jzgh as 工号,
教职工基础信息.xm as 姓名,
教职工基础信息.xb as 性别,
教职工基础信息.csny as 出生年月,
教职工基础信息.nld as 年龄段,
教职工基础信息.rxsj as 入校时间,
教职工基础信息.rzzt as 任职状态,
教职工基础信息.dwh as 单位号,
教职工基础信息.dwmc as 单位名称,
教职工基础信息.xl as 学历,
教职工基础信息.zgxw as 最高学位,
教职工基础信息.xymc as 学缘,
教职工基础信息.zyjszc as 专业技术职称,
教职工基础信息.xklb as 学科类别,
教职工基础信息.zzmm as 政治面貌,
教职工基础信息.gj as 国籍,
教职工基础信息.tjnf as 统计年份,
教职工基础信息.csrq as 出生日期,
教职工基础信息.zgxwgxmc as 最高学位高校名称
from ins_jzgjbxx 教职工基础信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_jzgjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_jzgqtxx', 'ins_jzgqtxx', '教职工其他信息', TO_CLOB('
select
教职工其他信息.gh as 工号,
教职工其他信息.xm as 姓名,
教职工其他信息.rjlx as 任教类型,
教职工其他信息.zymc as 任教专业名称,
教职工其他信息.rjzydm as 任教专业代码,
教职工其他信息.zyrjsj as 专业任教时间,
教职工其他信息.sfsyjsry as 是否实验技术人员,
教职工其他信息.sfsssnx as 是否双师双能型,
教职工其他信息.sfgcbj as 是否工程背景,
教职工其他信息.sfhybj as 是否行业背景,
教职工其他信息.sfjygwynys as 是否具有国境外一年及以上经历,
教职工其他信息.sfwkcszyxjs as 是否为课程思政优秀教师,
教职工其他信息.tjnf as 统计年份,
教职工其他信息.sfzrjs as 是否专任教师,
教职工其他信息.zrjslx as 专任教师类型,
教职工其他信息.sfjyqyjy as 是否具有企业经验,
教职工其他信息.sfjylnysqyjy as 是否具有两年以上企业经验,
教职工其他信息.sfxxpq as 是否学校派去企业实践锻炼累计3个月及以上,
教职工其他信息.sfxljkzzjs as 是否心理健康教育专职教师
from ins_jzgqtxx 教职工其他信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_jzgqtxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_szkjsqk', 'ins_szkjsqk', '思政课教师情况', TO_CLOB('
select
思政课教师情况.gh as 工号,
思政课教师情况.xm as 姓名,
思政课教师情况.dwh as 单位号,
思政课教师情况.dwmc as 单位名称,
思政课教师情况.rzlx as 任职类型,
思政课教师情况.tjnf as 统计年份
from ins_szkjsqk 思政课教师情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_szkjsqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_wphjzjsjbxx', 'ins_wphjzjsjbxx', '外聘和兼职教师基本信息', TO_CLOB('
select
外聘和兼职教师基本信息.gh as 工号,
外聘和兼职教师基本信息.xm as 姓名,
外聘和兼职教师基本信息.xb as 性别,
外聘和兼职教师基本信息.csny as 出生年月,
外聘和兼职教师基本信息.prsj as 聘任时间,
外聘和兼职教师基本信息.rzzt as 任职状态,
外聘和兼职教师基本信息.pq as 聘期,
外聘和兼职教师基本信息.dwh as 单位号,
外聘和兼职教师基本信息.dwmc as 单位名称,
外聘和兼职教师基本信息.xl as 学历,
外聘和兼职教师基本信息.zgxw as 最高学位,
外聘和兼职教师基本信息.zyjszc as 专业技术职称,
外聘和兼职教师基本信息.gzdwlb as 工作单位类别,
外聘和兼职教师基本信息.cdbkjxrw as 承担本科教学任务,
外聘和兼职教师基本信息.dq as 地区,
外聘和兼职教师基本信息.tjnf as 统计年份
from ins_wphjzjsjbxx 外聘和兼职教师基本信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_wphjzjsjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_xgglryjbxx', 'ins_xgglryjbxx', '相关管理人员基本信息', TO_CLOB('
select
相关管理人员基本信息.gh as 工号,
相关管理人员基本信息.xm as 姓名,
相关管理人员基本信息.glrylb as 管理人员类别,
相关管理人员基本信息.dwh as 单位号,
相关管理人员基本信息.dwmc as 单位名称,
相关管理人员基本信息.zw as 职务,
相关管理人员基本信息.tjnf as 统计年份,
相关管理人员基本信息.rzlx as 任职类型,
相关管理人员基本信息.zyjszc as 专业技术职称
from ins_xgglryjbxx 相关管理人员基本信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_xgglryjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_xldjbxx', 'ins_xldjbxx', '校领导基本信息', TO_CLOB('
select
校领导基本信息.gh as 工号,
校领导基本信息.xm as 姓名,
校领导基本信息.zw as 职务,
校领导基本信息.rxsj as 入校时间,
校领导基本信息.rxzsj as 任现职时间,
校领导基本信息.xnfggz as 校内分管工作,
校领导基本信息.tjnf as 统计年份
from ins_xldjbxx 校领导基本信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_xldjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_pjbds', 'abd_tea_pjbds', '班导师评教结果', TO_CLOB('
select
班导师评教结果.cpybs as 参评样本数,
班导师评教结果.cpl as 参评率,
班导师评教结果.yxpm as 院系排名,
班导师评教结果.xnxq as 学年学期,
班导师评教结果.jgh as 教工号,
班导师评教结果.xm as 姓名,
班导师评教结果.xb as 性别,
班导师评教结果.xy as 所属学院,
班导师评教结果.zy as 专业,
班导师评教结果.bj as 班级,
班导师评教结果.pjf as 评教分,
班导师评教结果.xydm as 学院代码,
班导师评教结果.zydm as 专业代码
from abd_tea_pjbds 班导师评教结果'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_pjbds'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_pjjxb', 'abd_tea_pjjxb', '教学班评教结果', TO_CLOB('
select
教学班评教结果.kcmc as 课程名称,
教学班评教结果.kcsx as 课程性质,
教学班评教结果.pjf as 评教分,
教学班评教结果.cpybs as 参评样本数,
教学班评教结果.cpl as 参评率,
教学班评教结果.yxpm as 院系排名,
教学班评教结果.xnxq as 学年学期,
教学班评教结果.jgh as 教工号,
教学班评教结果.xm as 姓名,
教学班评教结果.xb as 性别,
教学班评教结果.jxbid as 教学班号,
教学班评教结果.xy as 所属学院,
教学班评教结果.kch as 课程号,
教学班评教结果.sfmooc as 是否MOOC课程,
教学班评教结果.sftyk as 是否体育课程,
教学班评教结果.sfsyk as 是否独立实验课程,
教学班评教结果.pjnr as 评价内容,
教学班评教结果.kclb as 课程类别,
教学班评教结果.kcfl as 课程分类,
教学班评教结果.xydm as 学院代码,
教学班评教结果.zydm as 专业代码,
教学班评教结果.zymc as 专业名称
from abd_tea_pjjxb 教学班评教结果'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_pjjxb'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jzgjxxx', 'abd_tea_jzgjxxx', '教职工教学信息', TO_CLOB('
select
教职工教学信息.zgh as 教职工号,
教职工教学信息.sftgpx as 是否通过培训,
教职工教学信息.sfyzjzg as 是否有主讲资格,
教职工教学信息.sfznwzjk as 是否只能为主监考,
教职工教学信息.sfapjk as 是否安排监考
from abd_tea_jzgjxxx 教职工教学信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jzgjxxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jshdqk', 'abd_tea_jshdqk', '教师活动情况', TO_CLOB('
select
教师活动情况.tjnf as 统计年份,
教师活动情况.gh as 工号,
教师活动情况.jsxm as 教师姓名,
教师活动情况.hdmc as 活动名称,
教师活动情况.hdlx as 活动类型,
教师活动情况.hjdj as 获奖等级,
教师活动情况.hjjb as 获奖级别,
教师活动情况.dwmc as 单位名称,
教师活动情况.hdsj as 活动时间,
教师活动情况.hddd as 活动地点,
教师活动情况.hddx as 活动对象
from abd_tea_jshdqk 教师活动情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jshdqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jsgrry', 'abd_tea_jsgrry', '教师个人荣誉', TO_CLOB('
select
教师个人荣誉.tjnf as 统计年份,
教师个人荣誉.gh as 工号,
教师个人荣誉.jsxm as 教师姓名,
教师个人荣誉.hjxm as 获奖项目,
教师个人荣誉.xmlx as 项目类型,
教师个人荣誉.jljb as 奖励级别,
教师个人荣誉.jldj as 奖励等级,
教师个人荣誉.hjlb as 获奖类别,
教师个人荣誉.jlfs as 奖励方式,
教师个人荣誉.jlmc as 奖励名称,
教师个人荣誉.hjyy as 获奖原因,
教师个人荣誉.jlnr as 奖励内容,
教师个人荣誉.bjdw as 颁奖单位,
教师个人荣誉.hjrq as 获奖日期,
教师个人荣誉.rychm as 荣誉称号,
教师个人荣誉.rychlx as 荣誉称号类型,
教师个人荣誉.hjjsm as 获奖角色,
教师个人荣誉.jlzsbh as 奖励证书编号奖励文号,
教师个人荣誉.hryrq as 获荣誉日期,
教师个人荣誉.grpm as 个人排名
from abd_tea_jsgrry 教师个人荣誉'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jsgrry'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_bdsjbxx', 'abd_tea_bdsjbxx', '班导师基本信息', TO_CLOB('
select
班导师基本信息.gh as 工号,
班导师基本信息.xm as 姓名,
班导师基本信息.dwh as 单位号,
班导师基本信息.dwmc as 单位名称,
班导师基本信息.zy as 专业,
班导师基本信息.bj as 班级,
班导师基本信息.tjnf as 统计年份
from abd_tea_bdsjbxx 班导师基本信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_bdsjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jcxx', 'abd_tea_jcxx', '教材信息', TO_CLOB('
select
教材信息.jsgh as 教师工号,
教材信息.jsxm as 教师姓名,
教材信息.jcmc as 教材名称,
教材信息.jclb as 教材类别,
教材信息.tjnf as 统计年份
from abd_tea_jcxx 教材信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jcxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tea_jskcb', 'abd_tea_jskcb', '教师课程表', TO_CLOB('
select
教师课程表.xnxq as 学年学期,
教师课程表.kbid as 课表ID,
教师课程表.jxbid as 教学班ID,
教师课程表.xh as 工号,
教师课程表.xm as 姓名,
教师课程表.kch as 课程号,
教师课程表.kcm as 课程名,
教师课程表.skzc as 上课周次,
教师课程表.skxq as 上课星期,
教师课程表.ksjc as 开始节次,
教师课程表.jsjc as 结束节次,
教师课程表.jsdm as 教室代码
from abd_tea_jskcb 教师课程表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tea_jskcb'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tra_fankz', 'abd_tra_fankz', '方案内课组', TO_CLOB('
select
方案内课组.pyfadm as 培养方案代码,
方案内课组.kzh as 课组号,
方案内课组.kzm as 课组名,
方案内课组.fkzh as 父课组号,
方案内课组.kzlxdm as 课组类型,
方案内课组.kclbdm as 课程类别,
方案内课组.kcxzdm as 课程性质,
方案内课组.zsxdxf as 最少修读学分,
方案内课组.zsxdms as 最少修读门数,
方案内课组.zswckzs as 最少完成课组数,
方案内课组.kczms as 课程总门数,
方案内课组.kczxs as 课程总学时,
方案内课组.kczxf as 课程总学分,
方案内课组.xdyq as 修读要求,
方案内课组.sfxgxkz as 是否校公选课组,
方案内课组.xgxklbdm as 校公选课类别,
方案内课组.zyfxmc as 专业方向名称,
方案内课组.ggkzh as 公共课组号,
方案内课组.ggkzly as 公共课组来源,
方案内课组.ykzh as 原课组号,
方案内课组.yggkzh as 原公共课组号,
方案内课组.istkz as 是否头课组引用或复制,
方案内课组.zdxdxf as 最大修读学分
from abd_tra_fankz 方案内课组'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tra_fankz'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tra_fankzkc', 'abd_tra_fankzkc', '方案内课组课程', TO_CLOB('
select
方案内课组课程.pyfadm as 培养方案代码,
方案内课组课程.kzh as 课组号,
方案内课组课程.kch as 课程号,
方案内课组课程.jhxqdm as 计划学期,
方案内课组课程.kcxzdm as 课程性质,
方案内课组课程.kslxdm as 考试类型,
方案内课组课程.sfzgkc as 是否主干课程,
方案内课组课程.xxkc as 先修课程,
方案内课组课程.ggkzly as 公共课组来源,
方案内课组课程.ykzh as 原课组号,
方案内课组课程.yggkzh as 原公共课组号,
方案内课组课程.xnxq as 学年学期,
方案内课组课程.xdxq as 相对学期,
方案内课组课程.fxxs01 as 方案课程分项学时1,
方案内课组课程.fxxs02 as 方案课程分项学时2,
方案内课组课程.fxxs03 as 方案课程分项学时3,
方案内课组课程.fxxs04 as 方案课程分项学时4,
方案内课组课程.fxxs05 as 方案课程分项学时5,
方案内课组课程.fxxs06 as 方案课程分项学时6,
方案内课组课程.fxxs07 as 方案课程分项学时7,
方案内课组课程.fxxs08 as 方案课程分项学时8,
方案内课组课程.fxxs09 as 方案课程分项学时9,
方案内课组课程.fxxs10 as 方案课程分项学时10,
方案内课组课程.fxxs11 as 方案课程分项学时11,
方案内课组课程.fxxs12 as 方案课程分项学时12,
方案内课组课程.fxxs13 as 方案课程分项学时13,
方案内课组课程.fxxs14 as 方案课程分项学时14,
方案内课组课程.cddwdm as 承担单位,
方案内')||TO_CLOB('课组课程.jysdm as 教研室,
方案内课组课程.xs as 学时
from abd_tra_fankzkc 方案内课组课程'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tra_fankzkc'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_tra_pyfa', 'abd_tra_pyfa', '培养方案', TO_CLOB('
select
培养方案.pyfadm as 培养方案代码,
培养方案.pyfamc as 培养方案名称,
培养方案.faccdm as 方案层次,
培养方案.njdm as 年级,
培养方案.dwdm as 单位,
培养方案.zydm as 专业,
培养方案.zyfxdm as 专业方向,
培养方案.xlccdm as 学历层次,
培养方案.xznx as 学制年限,
培养方案.ksxndm as 开始学年,
培养方案.ksxqdm as 开始学期,
培养方案.xqlxdm as 学期类型,
培养方案.xdlxdm as 修读类型,
培养方案.xwdm as 学位,
培养方案.zsyqxf as 最少要求学分,
培养方案.faztdm as 方案状态,
培养方案.falxdm as 方案类型,
培养方案.sfjcdlfa as 是否继承大类方案,
培养方案.jcdlfadm as 继承大类方案代码,
培养方案.ypyfadm as 原培养方案代码,
培养方案.pyccdm as 培养层次,
培养方案.dlxq as 大类学期,
培养方案.sfyc as 是否异常
from abd_tra_pyfa 培养方案'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_tra_pyfa'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bzksjbxx', 'abd_und_bzksjbxx', '本专科生基本信息', TO_CLOB('
select
本专科生基本信息.xh as 学号,
本专科生基本信息.xm as 姓名,
本专科生基本信息.xmpy as 姓名拼音,
本专科生基本信息.cym as 曾用名,
本专科生基本信息.xb as 性别,
本专科生基本信息.nl as 年龄,
本专科生基本信息.xx as 血型,
本专科生基本信息.csrq as 出生日期,
本专科生基本信息.csd as 出生地,
本专科生基本信息.jg as 籍贯,
本专科生基本信息.gjdq as 国家地区,
本专科生基本信息.mz as 民族,
本专科生基本信息.sfzjlx as 身份证件类型,
本专科生基本信息.hyzk as 婚姻状况,
本专科生基本信息.jkzk as 健康状况,
本专科生基本信息.gatqw as 港澳台侨外,
本专科生基本信息.zjxy as 宗教信仰,
本专科生基本信息.zzmm as 政治面貌,
本专科生基本信息.sstzz as 所属团组织,
本专科生基本信息.ssdzz as 所属党组织,
本专科生基本信息.yktkh as 一卡通卡号,
本专科生基本信息.wlzh as 网络账号,
本专科生基本信息.yxzh as 邮箱账号,
本专科生基本信息.xqh as 校区号,
本专科生基本信息.xq as 校区,
本专科生基本信息.xybm as 学院编码,
本专科生基本信息.xy as 学院,
本专科生基本信息.bh as 班号,
本专科生基本信息.bj as 班级,
本专科生基本信息.zybm as 专业编码,
本专科生基本信息.zy as 专业,
本专科生基本信息.nj as 年级,
本专科生基本信息.ssq as 宿舍区,
本专科生基本信息.ssl as 宿舍楼,
本专科生基本信息.s')||TO_CLOB('sdz as 宿舍地址,
本专科生基本信息.rxzp as 入学照片,
本专科生基本信息.xslb as 学生类别,
本专科生基本信息.pyfs as 培养方式,
本专科生基本信息.pycc as 培养层次,
本专科生基本信息.pyfa as 培养方案,
本专科生基本信息.xz as 学制,
本专科生基本信息.yjbyrq as 预计毕业日期,
本专科生基本信息.xjzt as 学籍状态,
本专科生基本信息.xsdqzt as 学生当前状态,
本专科生基本信息.dszgh as 导师职工号,
本专科生基本信息.kzrq as 快照日期
from abd_und_bzksjbxx 本专科生基本信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bzksjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_bksfxsxwqk', 'ins_bksfxsxwqk', '本科生辅修、双学位情况', TO_CLOB('
select
本科生辅修双学位情况.xh as 学号,
本科生辅修双学位情况.xsxm as 学生姓名,
本科生辅修双学位情况.xxlx as 学习类型,
本科生辅修双学位情况.fxxnzydm as 辅修双学位校内专业代码,
本科生辅修双学位情况.fxxnzymc as 辅修双学位校内专业名称,
本科生辅修双学位情况.tjnf as 统计年份
from ins_bksfxsxwqk 本科生辅修双学位情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_bksfxsxwqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_bksjbqk', 'ins_bksjbqk', '本科生基本情况', TO_CLOB('
select
本科生基本情况.xh as 学号,
本科生基本情况.xsxm as 学生姓名,
本科生基本情况.xb as 性别,
本科生基本情况.xnzydlmc as 校内专业大类名称,
本科生基本情况.xnzydldm as 校内专业大类代码,
本科生基本情况.sylb as 生源类别,
本科生基本情况.xslb as 学生类别,
本科生基本情况.nj as 年级,
本科生基本情况.rxnf as 入学年份,
本科生基本情况.tjnf as 统计年份,
本科生基本情况.xy as 学院,
本科生基本情况.xzb as 班级,
本科生基本情况.xsbq as 学生标签,
本科生基本情况.xjzt as 学籍状态
from ins_bksjbqk 本科生基本情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_bksjbqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_bkszzyqk', 'ins_bkszzyqk', '本科生转专业情况', TO_CLOB('
select
本科生转专业情况.xh as 学号,
本科生转专业情况.xsxm as 学生姓名,
本科生转专业情况.zcxnzydm as 转出校内专业大类代码,
本科生转专业情况.zcxnzymc as 转出校内专业大类名称,
本科生转专业情况.zrxnzydm as 转入校内专业大类代码,
本科生转专业情况.zrxnzymc as 转入校内专业大类名称,
本科生转专业情况.tjnf as 统计年份,
本科生转专业情况.xnxq as 学年学期
from ins_bkszzyqk 本科生转专业情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_bkszzyqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_tylxshzybsjlqk', 'ins_tylxshzybsjlqk', '学生获专业比赛奖励情况（体育类专业用）', TO_CLOB('
select
学生获专业比赛奖励情况体育类专业用.xh as 学号,
学生获专业比赛奖励情况体育类专业用.xsxm as 学生姓名,
学生获专业比赛奖励情况体育类专业用.bsmc as 比赛名称,
学生获专业比赛奖励情况体育类专业用.sslb as 赛事类别,
学生获专业比赛奖励情况体育类专业用.hjmc as 获奖名次,
学生获专业比赛奖励情况体育类专业用.hjsj as 获奖时间,
学生获专业比赛奖励情况体育类专业用.zbdw as 主办单位,
学生获专业比赛奖励情况体育类专业用.xspm as 学生排名,
学生获专业比赛奖励情况体育类专业用.sm as 说明,
学生获专业比赛奖励情况体育类专业用.tjnf as 统计年份
from ins_tylxshzybsjlqk 学生获专业比赛奖励情况体育类专业用'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_tylxshzybsjlqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_xsbyzhxlqk', 'ins_xsbyzhxlqk', '学生毕业综合训练情况', TO_CLOB('
select
学生毕业综合训练情况.xh as 学号,
学生毕业综合训练情况.xsxm as 学生姓名,
学生毕业综合训练情况.byzhxltm as 毕业综合训练题目,
学生毕业综合训练情况.xtlb as 选题类别,
学生毕业综合训练情况.sfzshsjzwc as 是否在实验实习工程实践和社会调查等社会实践中完成,
学生毕业综合训练情况.zdjsxm as 指导教师姓名,
学生毕业综合训练情况.zdjsgh as 指导教师工号,
学生毕业综合训练情况.twsftg as 论文是否通过,
学生毕业综合训练情况.ktly as 课题来源,
学生毕业综合训练情况.lwdj as 论文等级,
学生毕业综合训练情况.xnxq as 学年学期,
学生毕业综合训练情况.tjnf as 统计年份,
学生毕业综合训练情况.xtsftg as 选题是否通过,
学生毕业综合训练情况.xtsfwc as 选题是否完成
from ins_xsbyzhxlqk 学生毕业综合训练情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_xsbyzhxlqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_xscjdxscxcyxljhqk', 'ins_xscjdxscxcyxljhqk', '学生参加大学生创新创业训练计划情况', TO_CLOB('
select
学生参加大学生创新创业训练计划情况.xh as 学号,
学生参加大学生创新创业训练计划情况.xsxm as 学生姓名,
学生参加大学生创新创业训练计划情况.xmmc as 项目名称,
学生参加大学生创新创业训练计划情况.xmjb as 项目级别,
学生参加大学生创新创业训练计划情况.xmlb as 项目类别,
学生参加大学生创新创业训练计划情况.tjnf as 统计年份,
学生参加大学生创新创业训练计划情况.sfppjs as 是否品牌竞赛,
学生参加大学生创新创业训练计划情况.sfyxal as 是否优秀案例,
学生参加大学生创新创业训练计划情况.xmzt as 项目状态,
学生参加大学生创新创业训练计划情况.sqsj as 申请时间,
学生参加大学生创新创业训练计划情况.xmcyjsgh as 项目参与教师工号,
学生参加大学生创新创业训练计划情况.xmcyjsxm as 项目参与教师姓名,
学生参加大学生创新创业训练计划情况.sfrxgcnhcg as 是否入选国创年会成果,
学生参加大学生创新创业训练计划情况.cglb as 成果类别,
学生参加大学生创新创业训练计划情况.cgmc as 成果名称,
学生参加大学生创新创业训练计划情况.sfhj as 是否获奖
from ins_xscjdxscxcyxljhqk 学生参加大学生创新创业训练计划情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_xscjdxscxcyxljhqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_xscyjskyxmqk', 'ins_xscyjskyxmqk', '学生参与教师科研项目情况', TO_CLOB('
select
学生参与教师科研项目情况.xh as 学号,
学生参与教师科研项目情况.xsxm as 学生姓名,
学生参与教师科研项目情况.cykyxmmc as 参与科研项目名称,
学生参与教师科研项目情况.xmfzr as 项目负责人,
学生参与教师科研项目情况.gh as 工号,
学生参与教师科研项目情况.tjnf as 统计年份
from ins_xscyjskyxmqk 学生参与教师科研项目情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_xscyjskyxmqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_xsczbyddbxzp', 'ins_xsczbyddbxzp', '学生创作、表演的代表性作品', TO_CLOB('
select
学生创作表演的代表性作品.xh as 学号,
学生创作表演的代表性作品.xsxm as 学生姓名,
学生创作表演的代表性作品.zpmc as 作品名称,
学生创作表演的代表性作品.lb as 类别,
学生创作表演的代表性作品.lx as 类型,
学生创作表演的代表性作品.fbsj as 发布时间,
学生创作表演的代表性作品.fbch as 发布场合,
学生创作表演的代表性作品.zbdw as 主办单位,
学生创作表演的代表性作品.yxfw as 影响范围,
学生创作表演的代表性作品.sm as 说明,
学生创作表演的代表性作品.tjnf as 统计年份
from ins_xsczbyddbxzp 学生创作表演的代表性作品'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_xsczbyddbxzp'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_xsfbxslwqk', 'ins_xsfbxslwqk', '学生发表学术论文情况', TO_CLOB('
select
学生发表学术论文情况.xh as 学号,
学生发表学术论文情况.xsxm as 学生姓名,
学生发表学术论文情况.lwmc as 论文名称,
学生发表学术论文情况.fbqk as 发表期刊,
学生发表学术论文情况.fbsj as 发表时间,
学生发表学术论文情况.slqk as 收录情况,
学生发表学术论文情况.tjnf as 统计年份
from ins_xsfbxslwqk 学生发表学术论文情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_xsfbxslwqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_xszlsqqk', 'ins_xszlsqqk', '学生专利（著作权）授权情况', TO_CLOB('
select
学生专利著作权授权情况.xh as 学号,
学生专利著作权授权情况.xsxm as 学生姓名,
学生专利著作权授权情况.mc as 名称,
学生专利著作权授权情况.lb as 类别,
学生专利著作权授权情况.sqh as 授权号,
学生专利著作权授权情况.hpsj as 获批时间,
学生专利著作权授权情况.sfdyfmr as 是否第一发明人,
学生专利著作权授权情况.tjnf as 统计年份
from ins_xszlsqqk 学生专利著作权授权情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_xszlsqqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_yjbkbysqxlsqk', 'ins_yjbkbysqxlsqk', '应届本科毕业生去向落实情况', TO_CLOB('
select
应届本科毕业生去向落实情况.xh as 学号,
应届本科毕业生去向落实情况.xm as 姓名,
应届本科毕业生去向落实情况.sysf as 生源省份,
应届本科毕业生去向落实情况.sycs as 生源城市,
应届本科毕业生去向落实情况.sfsyxw as 是否授予学位,
应届本科毕业生去向落实情况.qxlx as 去向类型,
应届本科毕业生去向落实情况.qxsf as 去向省份,
应届本科毕业生去向落实情况.qxcs as 去向城市,
应届本科毕业生去向落实情况.dwxz as 单位性质,
应届本科毕业生去向落实情况.dwhy as 单位行业,
应届本科毕业生去向落实情况.zwlb as 职位类别,
应届本科毕业生去向落实情况.qxfl as 去向分类,
应届本科毕业生去向落实情况.xymc as 学院名称,
应届本科毕业生去向落实情况.tjnf as 统计年份,
应届本科毕业生去向落实情况.xnxq as 学年学期,
应届本科毕业生去向落实情况.qxdy as 去向地域,
应届本科毕业生去向落实情况.xydm as 学院代码
from ins_yjbkbysqxlsqk 应届本科毕业生去向落实情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_yjbkbysqxlsqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_yslxshzybsjlqk', 'ins_yslxshzybsjlqk', '学生获专业比赛奖励情况（艺术类专业用）', TO_CLOB('
select
学生获专业比赛奖励情况艺术类专业用.xh as 学号,
学生获专业比赛奖励情况艺术类专业用.xsxm as 学生姓名,
学生获专业比赛奖励情况艺术类专业用.bsmc as 比赛名称,
学生获专业比赛奖励情况艺术类专业用.sslb as 赛事类别,
学生获专业比赛奖励情况艺术类专业用.hjdj as 获奖等级,
学生获专业比赛奖励情况艺术类专业用.hjsj as 获奖时间,
学生获专业比赛奖励情况艺术类专业用.zbdw as 主办单位,
学生获专业比赛奖励情况艺术类专业用.xspm as 学生排名,
学生获专业比赛奖励情况艺术类专业用.sm as 说明,
学生获专业比赛奖励情况艺术类专业用.tjnf as 统计年份
from ins_yslxshzybsjlqk 学生获专业比赛奖励情况艺术类专业用'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_yslxshzybsjlqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_yzsy', 'abd_und_yzsy', '优质生源', TO_CLOB('
select
优质生源.lqnf as 录取年份,
优质生源.cc as 层次,
优质生源.kslqlx as 考生录取类型,
优质生源.kslqlxxh as 考生录取类型细化,
优质生源.tjsjlx as 统计数据类型,
优质生源.smsf as 省码省份,
优质生源.xm as 姓名,
优质生源.xb as 性别,
优质生源.zxmc as 中学名称,
优质生源.lqxymc as 录取学院名称,
优质生源.lqzymc as 录取专业名称,
优质生源.zymc as 专业名称,
优质生源.zydmgb as 专业代码国标,
优质生源.xh as 学号,
优质生源.bj as 班级,
优质生源.sfwyzsyjd as 是否为优质生源基地,
优质生源.cwyzsyjdnf as 成为优质生源基地年份,
优质生源.sfsyzsyjdclhlr as 是否是优质生源基地成立后录入,
优质生源.tjnf as 统计年份,
优质生源.zxpm as 中学排名
from abd_und_yzsy 优质生源'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_yzsy'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_bysbyxx', 'abd_und_bysbyxx', '毕业生毕业信息', TO_CLOB('
select
毕业生毕业信息.pyfadm as 培养方案代码,
毕业生毕业信息.pyfamc as 培养方案名称,
毕业生毕业信息.xdlxdm as 修读类型,
毕业生毕业信息.byzsh as 毕业证书号,
毕业生毕业信息.xwzsh as 学位证书号,
毕业生毕业信息.bypcid as 毕业批次,
毕业生毕业信息.byjlyx as 学院毕业结论,
毕业生毕业信息.byjlzs as 终审毕业结论,
毕业生毕业信息.byjlsffb as 毕业结论是否发布,
毕业生毕业信息.xwjlyx as 学院学位结论,
毕业生毕业信息.xwjlzs as 终审学位结论,
毕业生毕业信息.xwjlsffb as 学位结论是否发布,
毕业生毕业信息.bynf as 毕业年份,
毕业生毕业信息.xh as 学号,
毕业生毕业信息.byslx as 毕业生类型,
毕业生毕业信息.xjsfgx as 学籍是否更新,
毕业生毕业信息.fbrq as 发布日期,
毕业生毕业信息.sfxtzfa as 是否系统中方案,
毕业生毕业信息.xjsftb as 学籍是否同步,
毕业生毕业信息.byjlyxtzyy as 学院毕业结论调整原因,
毕业生毕业信息.byjlzstzyy as 终审毕业结论调整原因,
毕业生毕业信息.xwjlyxtzyy as 学院学位结论调整原因,
毕业生毕业信息.xwjlzstzyy as 终审学位结论调整原因,
毕业生毕业信息.byzshlsh as 毕业证书号流水号,
毕业生毕业信息.xwzshlsh as 学位证书号流水号,
毕业生毕业信息.byzmb as 毕业证书模版,
毕业生毕业信息.xwzmb as 学位证书模版,
毕业生毕业信息.fbr as 发布人')||TO_CLOB(',
毕业生毕业信息.fbrxm as 发布人姓名,
毕业生毕业信息.xnxq as 学年学期
from abd_und_bysbyxx 毕业生毕业信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_bysbyxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_xspyfa', 'abd_und_xspyfa', '学生培养方案', TO_CLOB('
select
学生培养方案.xh as 学号,
学生培养方案.pyfadm as 培养方案代码
from abd_und_xspyfa 学生培养方案'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_xspyfa'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_xkjg', 'abd_und_xkjg', '选课结果', TO_CLOB('
select
选课结果.xklcdm as 选课轮次,
选课结果.sfzx as 是否主修,
选课结果.sfyz as 是否预置,
选课结果.xnxqdm as 学年学期,
选课结果.xh as 学号,
选课结果.jxbid as 教学班ID,
选课结果.tdkch as 替代课程号,
选课结果.pyfadm as 培养方案代码,
选课结果.kclbdm as 课程类别,
选课结果.xgxklbdm as 校公选课类别,
选课结果.kcxzdm as 课程性质,
选课结果.kslxdm as 考试类型,
选课结果.xdfsdm as 修读方式,
选课结果.cxckdm as 重修重考,
选课结果.xkzy as 选课志愿,
选课结果.xkzt as 选课状态,
选课结果.sfkt as 是否可退,
选课结果.kch as 课程号,
选课结果.kxh as 课序号,
选课结果.syjxbid as 实验教学班ID,
选课结果.cxcklx as 重修重考类型,
选课结果.krllx as 课容量类型,
选课结果.sfdgjc as 是否订购教材,
选课结果.jxblx as 教学班类型
from abd_und_xkjg 选课结果'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_xkjg'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_xsjxjbxx', 'abd_und_xsjxjbxx', '学生教学基本信息', TO_CLOB('
select
学生教学基本信息.bqmc as 标签名称,
学生教学基本信息.xz as 学制,
学生教学基本信息.rxnj as 入学年级,
学生教学基本信息.xxxqdm as 学校校区,
学生教学基本信息.xh as 学号,
学生教学基本信息.syddm as 生源地,
学生教学基本信息.pyfsdm as 培养方式,
学生教学基本信息.xidm as 院系,
学生教学基本信息.tgyxdm as 托管院系,
学生教学基本信息.zydm as 专业,
学生教学基本信息.bjdm as 班级,
学生教学基本信息.xznj as 现在年级,
学生教学基本信息.yjbyrq as 预计毕业日期,
学生教学基本信息.xslbdm as 学生类别,
学生教学基本信息.pyccdm as 培养层次,
学生教学基本信息.rxny as 入学年月,
学生教学基本信息.sjbyrq as 实际毕业日期,
学生教学基本信息.xjztdm as 学籍状态,
学生教学基本信息.sfzx as 是否在校,
学生教学基本信息.sfzj as 是否在籍,
学生教学基本信息.zyfxdm as 专业方向,
学生教学基本信息.bjyjldm as 毕结业结论,
学生教学基本信息.byzsh as 毕业证书号,
学生教学基本信息.jyzsh as 结业证书号,
学生教学基本信息.sfsyxw as 是否授予学位,
学生教学基本信息.syxwdm as 授予学位,
学生教学基本信息.xwzh as 学位证号,
学生教学基本信息.xwsysj as 学位授予时间,
学生教学基本信息.rxzy as 入学专业,
学生教学基本信息.rxbj as 入学班级,
学生教学基本信息.xslb2 as 学生类别2,
学')||TO_CLOB('生教学基本信息.sxwzsh as 双学位证书号,
学生教学基本信息.sxwbysh as 双学位毕业时间,
学生教学基本信息.sxwzy as 双学位专业,
学生教学基本信息.fxzydm as 辅修专业,
学生教学基本信息.fxbysj as 辅修毕业时间,
学生教学基本信息.fxzsh as 辅修证书号,
学生教学基本信息.byzydm as 毕业专业,
学生教学基本信息.fxxydm as 辅修学院,
学生教学基本信息.fxblsj as 辅修办理时间,
学生教学基本信息.fxbjyjldm as 辅修毕结业结论,
学生教学基本信息.fxsfsyxw as 辅修是否授予学位,
学生教学基本信息.fxsyxwdm as 辅修授予学位,
学生教学基本信息.fxxwzh as 辅修学位证号,
学生教学基本信息.fxxwsysj as 辅修学位授予时间,
学生教学基本信息.lqlbdm as 录取类别
from abd_und_xsjxjbxx 学生教学基本信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_xsjxjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_xsgrry', 'abd_und_xsgrry', '学生个人荣誉', TO_CLOB('
select
学生个人荣誉.tjnf as 统计年份,
学生个人荣誉.xh as 学号,
学生个人荣誉.xm as 姓名,
学生个人荣誉.xy as 学院,
学生个人荣誉.hjxm as 获奖项目,
学生个人荣誉.xmlx as 项目类型,
学生个人荣誉.jljb as 奖励级别,
学生个人荣誉.jldj as 奖励等级,
学生个人荣誉.hjlb as 获奖类别,
学生个人荣誉.jlfs as 奖励方式,
学生个人荣誉.jlmc as 奖励名称,
学生个人荣誉.hjyy as 获奖原因,
学生个人荣誉.jlnr as 奖励内容,
学生个人荣誉.bjdw as 颁奖单位,
学生个人荣誉.hjrq as 获奖日期,
学生个人荣誉.rychm as 荣誉称号,
学生个人荣誉.rychlx as 荣誉称号类型,
学生个人荣誉.jlzsbh as 奖励证书编号奖励文号,
学生个人荣誉.hryrq as 获荣誉日期,
学生个人荣誉.xsjl as 学生经历
from abd_und_xsgrry 学生个人荣誉'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_xsgrry'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_yysljtgqk', 'abd_und_yysljtgqk', '英语四六级通过情况', TO_CLOB('
select
英语四六级通过情况.xh as 学号,
英语四六级通过情况.xm as 姓名,
英语四六级通过情况.yydj as 英语等级,
英语四六级通过情况.sftg as 是否通过,
英语四六级通过情况.tjnf as 统计年份
from abd_und_yysljtgqk 英语四六级通过情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_yysljtgqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_xyyjjg', 'abd_und_xyyjjg', '学业预警结果', TO_CLOB('
select
学业预警结果.xh as 学号,
学业预警结果.xm as 姓名,
学业预警结果.xydm as 学院代码,
学业预警结果.xymc as 学院名称,
学业预警结果.xnzymc as 校内专业名称,
学业预警结果.sfyj as 是否预警,
学业预警结果.yjjl as 预警结论,
学业预警结果.yjkssj as 预警开始时间,
学业预警结果.yjjssj as 预警结束时间,
学业预警结果.xnxq as 学年学期,
学业预警结果.yjjb as 预警级别,
学业预警结果.xqbjgxf as 学期不及格学分,
学业预警结果.ljbjgxf as 累计不及格学分,
学业预警结果.ljbjgxffw as 累计不及格学分范围,
学业预警结果.xqgkms as 学期挂科门数,
学业预警结果.ljgkms as 累计挂科门数,
学业预警结果.xnzydm as 校内专业代码,
学业预警结果.pyfadm as 培养方案代码,
学业预警结果.pyfamc as 培养方案名称
from abd_und_xyyjjg 学业预警结果'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_xyyjjg'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_sxjbxx', 'abd_und_sxjbxx', '实习基本信息', TO_CLOB('
select
实习基本信息.xh as 学号,
实习基本信息.xsxm as 学生姓名,
实习基本信息.sxlx as 实习类型,
实习基本信息.xydm as 学院代码,
实习基本信息.xymc as 学院名称,
实习基本信息.xnxq as 学年学期,
实习基本信息.sxmc as 实习名称,
实习基本信息.tjnf as 统计年份
from abd_und_sxjbxx 实习基本信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_sxjbxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_xshsjjysgljsjlqk', 'ins_xshsjjysgljsjlqk', '学生各类竞赛奖励情况', TO_CLOB('
select
学生各类竞赛奖励情况.xh as 学号,
学生各类竞赛奖励情况.xsxm as 学生姓名,
学生各类竞赛奖励情况.zdjsgh as 指导教师工号,
学生各类竞赛奖励情况.zdjsxm as 指导教师姓名,
学生各类竞赛奖励情况.jsmc as 竞赛名称,
学生各类竞赛奖励情况.hjsj as 获奖时间,
学生各类竞赛奖励情况.hjlb as 获奖类别,
学生各类竞赛奖励情况.hjdj as 获奖等级,
学生各类竞赛奖励情况.jslx as 竞赛类型,
学生各类竞赛奖励情况.sdlx as 赛道类型,
学生各类竞赛奖励情况.xmlb as 项目类别,
学生各类竞赛奖励情况.xmzt as 项目状态,
学生各类竞赛奖励情况.sm as 说明,
学生各类竞赛奖励情况.tjnf as 统计年份,
学生各类竞赛奖励情况.hjjb as 获奖级别
from ins_xshsjjysgljsjlqk 学生各类竞赛奖励情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_xshsjjysgljsjlqk'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_xspjxx', 'abd_und_xspjxx', '学生评教信息', TO_CLOB('
select
学生评教信息.xsxh as 学生学号,
学生评教信息.xsxm as 学生姓名,
学生评教信息.kch as 课程号,
学生评教信息.bpjsgh as 被评教师工号,
学生评教信息.bpjsxm as 被评教师姓名,
学生评教信息.jxbid as 教学班ID,
学生评教信息.pjzt as 评教状态,
学生评教信息.xnxq as 学年学期
from abd_und_xspjxx 学生评教信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_xspjxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_xscjxx', 'abd_und_xscjxx', '学生成绩信息', TO_CLOB('
select
学生成绩信息.xnxqdm as 学年学期,
学生成绩信息.jxbid as 教学班ID,
学生成绩信息.kcm as 课程名,
学生成绩信息.kch as 课程号,
学生成绩信息.xh as 学号,
学生成绩信息.xm as 姓名,
学生成绩信息.xymc as 学院名称,
学生成绩信息.xydm as 学院代码,
学生成绩信息.xdfsdm as 修读方式,
学生成绩信息.cxckdm as 重修重考,
学生成绩信息.djcjlxdm as 等级成绩类型,
学生成绩信息.pyfadm as 培养方案代码,
学生成绩信息.kclb as 课程类别,
学生成绩信息.kcxz as 课程性质,
学生成绩信息.kslxdm as 考试类型,
学生成绩信息.tdkch as 替代课程号,
学生成绩信息.tsyy as 特殊原因,
学生成绩信息.pscj as 平时成绩,
学生成绩信息.qzcj as 期中成绩,
学生成绩信息.qmcj as 期末成绩,
学生成绩信息.sycj as 实验成绩,
学生成绩信息.djcj as 等级成绩,
学生成绩信息.zcj as 总成绩,
学生成绩信息.jd as 绩点,
学生成绩信息.sfyx as 是否有效,
学生成绩信息.sfjg as 是否及格
from abd_und_xscjxx 学生成绩信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_xscjxx'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'abd_und_xskcb', 'abd_und_xskcb', '学生课程表', TO_CLOB('
select
学生课程表.xnxq as 学年学期,
学生课程表.kbid as 课表ID,
学生课程表.jxbid as 教学班ID,
学生课程表.xh as 学号,
学生课程表.xm as 姓名,
学生课程表.kch as 课程号,
学生课程表.kcm as 课程名,
学生课程表.skzc as 上课周次,
学生课程表.skxq as 上课星期,
学生课程表.ksjc as 开始节次,
学生课程表.jsjc as 结束节次,
学生课程表.jsdm as 教室代码
from abd_und_xskcb 学生课程表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'abd_und_xskcb'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0001', 'ins_nr_0001', '思政课专任教师与折合在校生比', TO_CLOB('
select
思政课专任教师与折合在校生比.tjfw as 统计范围,
思政课专任教师与折合在校生比.tjnf as 统计年份,
思政课专任教师与折合在校生比.zhssb as 折合生师比
from ins_nr_0001 思政课专任教师与折合在校生比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0001'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0002', 'ins_nr_0002', '思政课专任教师与全日制在校生比例', TO_CLOB('
select
思政课专任教师与全日制在校生比例.tjfw as 统计范围,
思政课专任教师与全日制在校生比例.tjnf as 统计年份,
思政课专任教师与全日制在校生比例.ssb as 生师比
from ins_nr_0002 思政课专任教师与全日制在校生比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0002'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0003', 'ins_nr_0003', '专职党务工作人员和思想政治工作人员总数与全校师生人数比例', TO_CLOB('
select
专职党务工作人员和思想政治工作人员总数与全校师生人数比例.tjfw as 统计范围,
专职党务工作人员和思想政治工作人员总数与全校师生人数比例.tjnf as 统计年份,
专职党务工作人员和思想政治工作人员总数与全校师生人数比例.ssb as 生师比
from ins_nr_0003 专职党务工作人员和思想政治工作人员总数与全校师生人数比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0003'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0004', 'ins_nr_0004', '生均思政工作和党务工作队伍建设专项经费', TO_CLOB('
select
生均思政工作和党务工作队伍建设专项经费.tjfw as 统计范围,
生均思政工作和党务工作队伍建设专项经费.tjnf as 统计年份,
生均思政工作和党务工作队伍建设专项经费.jf as 经费
from ins_nr_0004 生均思政工作和党务工作队伍建设专项经费'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0004'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0005', 'ins_nr_0005', '生均网络思政工作专项经费', TO_CLOB('
select
生均网络思政工作专项经费.tjfw as 统计范围,
生均网络思政工作专项经费.tjnf as 统计年份,
生均网络思政工作专项经费.jf as 经费
from ins_nr_0005 生均网络思政工作专项经费'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0005'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0006', 'ins_nr_0006', '生均年教学日常运行支出', TO_CLOB('
select
生均年教学日常运行支出.tjfw as 统计范围,
生均年教学日常运行支出.tjnf as 统计年份,
生均年教学日常运行支出.zcfy as 支出费用
from ins_nr_0006 生均年教学日常运行支出'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0006'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0007', 'ins_nr_0007', '教学日常运行支出占经常性预算内教育事业费拨款与学费收入之和的比例', TO_CLOB('
select
教学日常运行支出占经常性预算内教育事业费拨款与学费收入之和的比例.tjfw as 统计范围,
教学日常运行支出占经常性预算内教育事业费拨款与学费收入之和的比例.tjnf as 统计年份,
教学日常运行支出占经常性预算内教育事业费拨款与学费收入之和的比例.zczb as 支出占比
from ins_nr_0007 教学日常运行支出占经常性预算内教育事业费拨款与学费收入之和的比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0007'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0008', 'ins_nr_0008', '年新增教学科研仪器设备所占比例', TO_CLOB('
select
年新增教学科研仪器设备所占比例.tjfw as 统计范围,
年新增教学科研仪器设备所占比例.tjnf as 统计年份,
年新增教学科研仪器设备所占比例.nxzyqzb as 年新增仪器占比
from ins_nr_0008 年新增教学科研仪器设备所占比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0008'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0009', 'ins_nr_0009', '生均教学科研仪器设备值', TO_CLOB('
select
生均教学科研仪器设备值.tjfw as 统计范围,
生均教学科研仪器设备值.tjnf as 统计年份,
生均教学科研仪器设备值.sbz as 设备值
from ins_nr_0009 生均教学科研仪器设备值'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0009'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0010', 'ins_nr_0010', '学生毕业必须修满的公共艺术课程学分数', TO_CLOB('
select
学生毕业必须修满的公共艺术课程学分数.tjfw as 统计范围,
学生毕业必须修满的公共艺术课程学分数.tjnf as 统计年份,
学生毕业必须修满的公共艺术课程学分数.xf as 学分
from ins_nr_0010 学生毕业必须修满的公共艺术课程学分数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0010'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0011', 'ins_nr_0011', '劳动教育必修课或必修课程中劳动教育模块学时总数', TO_CLOB('
select
劳动教育必修课或必修课程中劳动教育模块学时总数.tjfw as 统计范围,
劳动教育必修课或必修课程中劳动教育模块学时总数.tjnf as 统计年份,
劳动教育必修课或必修课程中劳动教育模块学时总数.xs as 学时
from ins_nr_0011 劳动教育必修课或必修课程中劳动教育模块学时总数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0011'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0012', 'ins_nr_0012', '通过认证专业占专业总数比例', TO_CLOB('
select
通过认证专业占专业总数比例.tjfw as 统计范围,
通过认证专业占专业总数比例.tjnf as 统计年份,
通过认证专业占专业总数比例.rzzybl as 认证专业比例
from ins_nr_0012 通过认证专业占专业总数比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0012'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0013', 'ins_nr_0013', '实践教学学分占总学分(学时)比例', TO_CLOB('
select
实践教学学分占总学分学时比例.tjfw as 统计范围,
实践教学学分占总学分学时比例.tjnf as 统计年份,
实践教学学分占总学分学时比例.sjxfbl as 实践学分比例
from ins_nr_0013 实践教学学分占总学分学时比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0013'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0014', 'ins_nr_0014', '国家级、省级实践教学基地数', TO_CLOB('
select
国家级省级实践教学基地数.tjfw as 统计范围,
国家级省级实践教学基地数.tjnf as 统计年份,
国家级省级实践教学基地数.jdsl as 基地数量
from ins_nr_0014 国家级省级实践教学基地数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0014'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0015', 'ins_nr_0015', '以实验、实习、工程实践和社会调查等实践性工作为基础的毕业论文(设计)比例', TO_CLOB('
select
以实验实习工程实践和社会调查等实践性工作为基础的毕业论文设计比例.tjfw as 统计范围,
以实验实习工程实践和社会调查等实践性工作为基础的毕业论文设计比例.tjnf as 统计年份,
以实验实习工程实践和社会调查等实践性工作为基础的毕业论文设计比例.bl as 比例
from ins_nr_0015 以实验实习工程实践和社会调查等实践性工作为基础的毕业论文设计比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0015'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0016', 'ins_nr_0016', '使用马工程重点教材课程数量与学校应使用马工程重点教材课程数量的比例', TO_CLOB('
select
使用马工程重点教材课程数量与学校应使用马工程重点教材课程数量的比例.tjfw as 统计范围,
使用马工程重点教材课程数量与学校应使用马工程重点教材课程数量的比例.tjnf as 统计年份,
使用马工程重点教材课程数量与学校应使用马工程重点教材课程数量的比例.bl as 比例
from ins_nr_0016 使用马工程重点教材课程数量与学校应使用马工程重点教材课程数量的比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0016'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0017', 'ins_nr_0017', '本科生生均课程门数', TO_CLOB('
select
本科生生均课程门数.tjfw as 统计范围,
本科生生均课程门数.tjnf as 统计年份,
本科生生均课程门数.ms as 门数
from ins_nr_0017 本科生生均课程门数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0017'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0018', 'ins_nr_0018', '本科生参加各级各类创新创业实践活动人数', TO_CLOB('
select
本科生参加各级各类创新创业实践活动人数.tjfw as 统计范围,
本科生参加各级各类创新创业实践活动人数.tjnf as 统计年份,
本科生参加各级各类创新创业实践活动人数.rs as 人数
from ins_nr_0018 本科生参加各级各类创新创业实践活动人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0018'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0019', 'ins_nr_0019', '本科生参加各级各类创新创业实践活动比例', TO_CLOB('
select
本科生参加各级各类创新创业实践活动比例.tjfw as 统计范围,
本科生参加各级各类创新创业实践活动比例.tjnf as 统计年份,
本科生参加各级各类创新创业实践活动比例.bl as 比例
from ins_nr_0019 本科生参加各级各类创新创业实践活动比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0019'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0020', 'ins_nr_0020', '“互联网+”大学生创新创业大赛获奖数', TO_CLOB('
select
互联网大学生创新创业大赛获奖数.tjfw as 统计范围,
互联网大学生创新创业大赛获奖数.tjnf as 统计年份,
互联网大学生创新创业大赛获奖数.hjsl as 获奖数量
from ins_nr_0020 互联网大学生创新创业大赛获奖数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0020'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0021', 'ins_nr_0021', '主讲本科课程教授占教授总数的比例', TO_CLOB('
select
主讲本科课程教授占教授总数的比例.tjfw as 统计范围,
主讲本科课程教授占教授总数的比例.tjnf as 统计年份,
主讲本科课程教授占教授总数的比例.skjszb as 授课教授占比
from ins_nr_0021 主讲本科课程教授占教授总数的比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0021'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0022', 'ins_nr_0022', '教授主讲本科课程人均学时数', TO_CLOB('
select
教授主讲本科课程人均学时数.tjfw as 统计范围,
教授主讲本科课程人均学时数.tjnf as 统计年份,
教授主讲本科课程人均学时数.xs as 学时
from ins_nr_0022 教授主讲本科课程人均学时数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0022'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0023', 'ins_nr_0023', '教授、副教授担任专业负责人的专业占专业总数的比例', TO_CLOB('
select
教授副教授担任专业负责人的专业占专业总数的比例.tjfw as 统计范围,
教授副教授担任专业负责人的专业占专业总数的比例.tjnf as 统计年份,
教授副教授担任专业负责人的专业占专业总数的比例.bl as 比例
from ins_nr_0023 教授副教授担任专业负责人的专业占专业总数的比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0023'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0024', 'ins_nr_0024', '设有基层教学组织的专业占专业总数的比例', TO_CLOB('
select
设有基层教学组织的专业占专业总数的比例.tjfw as 统计范围,
设有基层教学组织的专业占专业总数的比例.tjnf as 统计年份,
设有基层教学组织的专业占专业总数的比例.bl as 比例
from ins_nr_0024 设有基层教学组织的专业占专业总数的比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0024'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0025', 'ins_nr_0025', '本科生体质测试达标率', TO_CLOB('
select
本科生体质测试达标率.tjfw as 统计范围,
本科生体质测试达标率.tjnf as 统计年份,
本科生体质测试达标率.dbl as 达标率
from ins_nr_0025 本科生体质测试达标率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0025'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0026', 'ins_nr_0026', '专职辅导员岗位与在校生比例', TO_CLOB('
select
专职辅导员岗位与在校生比例.tjfw as 统计范围,
专职辅导员岗位与在校生比例.tjnf as 统计年份,
专职辅导员岗位与在校生比例.ssb as 生师比
from ins_nr_0026 专职辅导员岗位与在校生比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0026'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0027', 'ins_nr_0027', '专职从事心理健康教育教师数', TO_CLOB('
select
专职从事心理健康教育教师数.tjfw as 统计范围,
专职从事心理健康教育教师数.tjnf as 统计年份,
专职从事心理健康教育教师数.rs as 人数
from ins_nr_0027 专职从事心理健康教育教师数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0027'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0028', 'ins_nr_0028', '专职从事心理健康教育教师与在校生比例', TO_CLOB('
select
专职从事心理健康教育教师与在校生比例.tjfw as 统计范围,
专职从事心理健康教育教师与在校生比例.tjnf as 统计年份,
专职从事心理健康教育教师与在校生比例.ssb as 生师比
from ins_nr_0028 专职从事心理健康教育教师与在校生比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0028'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0029', 'ins_nr_0029', '专职就业指导教师和专职就业工作人员与应届毕业生比例', TO_CLOB('
select
专职就业指导教师和专职就业工作人员与应届毕业生比例.tjfw as 统计范围,
专职就业指导教师和专职就业工作人员与应届毕业生比例.tjnf as 统计年份,
专职就业指导教师和专职就业工作人员与应届毕业生比例.ssb as 生师比
from ins_nr_0029 专职就业指导教师和专职就业工作人员与应届毕业生比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0029'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0030', 'ins_nr_0030', '生均本科实验经费', TO_CLOB('
select
生均本科实验经费.tjfw as 统计范围,
生均本科实验经费.tjnf as 统计年份,
生均本科实验经费.jf as 经费
from ins_nr_0030 生均本科实验经费'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0030'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0031', 'ins_nr_0031', '生均本科实习经费', TO_CLOB('
select
生均本科实习经费.tjfw as 统计范围,
生均本科实习经费.tjnf as 统计年份,
生均本科实习经费.jf as 经费
from ins_nr_0031 生均本科实习经费'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0031'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0032', 'ins_nr_0032', '生师比', TO_CLOB('
select
生师比.tjfw as 统计范围,
生师比.tjnf as 统计年份,
生师比.ssb as 生师比
from ins_nr_0032 生师比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0032'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0033', 'ins_nr_0033', '本科生师比', TO_CLOB('
select
本科生师比.tjfw as 统计范围,
本科生师比.tjnf as 统计年份,
本科生师比.ssb as 生师比
from ins_nr_0033 本科生师比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0033'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0034', 'ins_nr_0034', '具有硕士学位、博士学位教师占专任教师比例', TO_CLOB('
select
具有硕士学位博士学位教师占专任教师比例.tjfw as 统计范围,
具有硕士学位博士学位教师占专任教师比例.tjnf as 统计年份,
具有硕士学位博士学位教师占专任教师比例.bl as 比例
from ins_nr_0034 具有硕士学位博士学位教师占专任教师比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0034'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0035', 'ins_nr_0035', '近三年新增专业数', TO_CLOB('
select
近三年新增专业数.tjfw as 统计范围,
近三年新增专业数.tjnf as 统计年份,
近三年新增专业数.zysl as 专业数量
from ins_nr_0035 近三年新增专业数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0035'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0036', 'ins_nr_0036', '近三年停招专业数', TO_CLOB('
select
近三年停招专业数.tjfw as 统计范围,
近三年停招专业数.tjnf as 统计年份,
近三年停招专业数.zysl as 专业数量
from ins_nr_0036 近三年停招专业数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0036'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0037', 'ins_nr_0037', '与行业企业共建的实验教学中心数', TO_CLOB('
select
与行业企业共建的实验教学中心数.tjfw as 统计范围,
与行业企业共建的实验教学中心数.tjnf as 统计年份,
与行业企业共建的实验教学中心数.sl as 数量
from ins_nr_0037 与行业企业共建的实验教学中心数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0037'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0038', 'ins_nr_0038', '近五年公开出版的教材数', TO_CLOB('
select
近五年公开出版的教材数.tjfw as 统计范围,
近五年公开出版的教材数.tjnf as 统计年份,
近五年公开出版的教材数.jcsl as 教材数量
from ins_nr_0038 近五年公开出版的教材数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0038'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0039', 'ins_nr_0039', '基础学科拔尖学生培养计划学生数', TO_CLOB('
select
基础学科拔尖学生培养计划学生数.tjfw as 统计范围,
基础学科拔尖学生培养计划学生数.tjnf as 统计年份,
基础学科拔尖学生培养计划学生数.rs as 人数
from ins_nr_0039 基础学科拔尖学生培养计划学生数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0039'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0040', 'ins_nr_0040', '产学合作协同育人项目数', TO_CLOB('
select
产学合作协同育人项目数.tjfw as 统计范围,
产学合作协同育人项目数.tjnf as 统计年份,
产学合作协同育人项目数.xmsl as 项目数量
from ins_nr_0040 产学合作协同育人项目数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0040'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0041', 'ins_nr_0041', '与行业企业共建、共同讲授的课程数', TO_CLOB('
select
与行业企业共建共同讲授的课程数.tjfw as 统计范围,
与行业企业共建共同讲授的课程数.tjnf as 统计年份,
与行业企业共建共同讲授的课程数.kcsl as 课程数量
from ins_nr_0041 与行业企业共建共同讲授的课程数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0041'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0042', 'ins_nr_0042', '省级以上学科竞赛获奖学生人次数占学生总数的比例', TO_CLOB('
select
省级以上学科竞赛获奖学生人次数占学生总数的比例.tjfw as 统计范围,
省级以上学科竞赛获奖学生人次数占学生总数的比例.tjnf as 统计年份,
省级以上学科竞赛获奖学生人次数占学生总数的比例.bl as 比例
from ins_nr_0042 省级以上学科竞赛获奖学生人次数占学生总数的比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0042'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0043', 'ins_nr_0043', '教师发展中心培训本校教师的比例', TO_CLOB('
select
教师发展中心培训本校教师的比例.tjfw as 统计范围,
教师发展中心培训本校教师的比例.tjnf as 统计年份,
教师发展中心培训本校教师的比例.bl as 比例
from ins_nr_0043 教师发展中心培训本校教师的比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0043'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0044', 'ins_nr_0044', '专任教师中双师双能型教师的比例', TO_CLOB('
select
专任教师中双师双能型教师的比例.tjfw as 统计范围,
专任教师中双师双能型教师的比例.tjnf as 统计年份,
专任教师中双师双能型教师的比例.bl as 比例
from ins_nr_0044 专任教师中双师双能型教师的比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0044'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0045', 'ins_nr_0045', '本科生以第一作者/通讯作者在公开发行期刊发表的论文数', TO_CLOB('
select
本科生以第一作者通讯作者在公开发行期刊发表的论文数.tjfw as 统计范围,
本科生以第一作者通讯作者在公开发行期刊发表的论文数.tjnf as 统计年份,
本科生以第一作者通讯作者在公开发行期刊发表的论文数.lwsl as 论文数量
from ins_nr_0045 本科生以第一作者通讯作者在公开发行期刊发表的论文数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0045'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0046', 'ins_nr_0046', '本科生第一作者获批国家发明专利数', TO_CLOB('
select
本科生第一作者获批国家发明专利数.tjfw as 统计范围,
本科生第一作者获批国家发明专利数.tjnf as 统计年份,
本科生第一作者获批国家发明专利数.zlsl as 专利数量
from ins_nr_0046 本科生第一作者获批国家发明专利数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0046'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0047', 'ins_nr_0047', '在学期间获得国家认可的职业资格证书学生数占在校生数的比例', TO_CLOB('
select
在学期间获得国家认可的职业资格证书学生数占在校生数的比例.tjfw as 统计范围,
在学期间获得国家认可的职业资格证书学生数占在校生数的比例.tjnf as 统计年份,
在学期间获得国家认可的职业资格证书学生数占在校生数的比例.bl as 比例
from ins_nr_0047 在学期间获得国家认可的职业资格证书学生数占在校生数的比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0047'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0048', 'ins_nr_0048', '省级以上艺术展演、体育竟赛参赛获奖学生人次数占学生总数的比例', TO_CLOB('
select
省级以上艺术展演体育竟赛参赛获奖学生人次数占学生总数的比例.tjfw as 统计范围,
省级以上艺术展演体育竟赛参赛获奖学生人次数占学生总数的比例.tjnf as 统计年份,
省级以上艺术展演体育竟赛参赛获奖学生人次数占学生总数的比例.bl as 比例
from ins_nr_0048 省级以上艺术展演体育竟赛参赛获奖学生人次数占学生总数的比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0048'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0049', 'ins_nr_0049', '在学期间赴国(境)外交流、访学、实习的学生数占在校生数的比例', TO_CLOB('
select
在学期间赴国境外交流访学实习的学生数占在校生数的比例.tjfw as 统计范围,
在学期间赴国境外交流访学实习的学生数占在校生数的比例.tjnf as 统计年份,
在学期间赴国境外交流访学实习的学生数占在校生数的比例.bl as 比例
from ins_nr_0049 在学期间赴国境外交流访学实习的学生数占在校生数的比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0049'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0050', 'ins_nr_0050', '升学率', TO_CLOB('
select
升学率.tjfw as 统计范围,
升学率.tjnf as 统计年份,
升学率.sxl as 升学率
from ins_nr_0050 升学率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0050'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0051', 'ins_nr_0051', '应届本科生初次就业率', TO_CLOB('
select
应届本科生初次就业率.tjfw as 统计范围,
应届本科生初次就业率.tjnf as 统计年份,
应届本科生初次就业率.jyl as 就业率
from ins_nr_0051 应届本科生初次就业率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0051'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0052', 'ins_nr_0052', '生均教学行政用房', TO_CLOB('
select
生均教学行政用房.tjfw as 统计范围,
生均教学行政用房.tjnf as 统计年份,
生均教学行政用房.yfmj as 用房面积
from ins_nr_0052 生均教学行政用房'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0052'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0053', 'ins_nr_0053', '生均图书', TO_CLOB('
select
生均图书.tjfw as 统计范围,
生均图书.tjnf as 统计年份,
生均图书.tssl as 图书数量
from ins_nr_0053 生均图书'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0053'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0054', 'ins_nr_0054', '具有高级职务教师占专任教师的比例', TO_CLOB('
select
具有高级职务教师占专任教师的比例.tjfw as 统计范围,
具有高级职务教师占专任教师的比例.tjnf as 统计年份,
具有高级职务教师占专任教师的比例.bl as 比例
from ins_nr_0054 具有高级职务教师占专任教师的比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0054'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0055', 'ins_nr_0055', '生均占地面积', TO_CLOB('
select
生均占地面积.tjfw as 统计范围,
生均占地面积.tjnf as 统计年份,
生均占地面积.zdmj as 占地面积
from ins_nr_0055 生均占地面积'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0055'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0056', 'ins_nr_0056', '生均宿舍面积', TO_CLOB('
select
生均宿舍面积.tjfw as 统计范围,
生均宿舍面积.tjnf as 统计年份,
生均宿舍面积.ssmj as 宿舍面积
from ins_nr_0056 生均宿舍面积'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0056'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0057', 'ins_nr_0057', '百名学生配教学用计算机台数', TO_CLOB('
select
百名学生配教学用计算机台数.tjfw as 统计范围,
百名学生配教学用计算机台数.tjnf as 统计年份,
百名学生配教学用计算机台数.sl as 数量
from ins_nr_0057 百名学生配教学用计算机台数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0057'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0058', 'ins_nr_0058', '百名学生配多媒体教室和语音实验室座位数', TO_CLOB('
select
百名学生配多媒体教室和语音实验室座位数.tjfw as 统计范围,
百名学生配多媒体教室和语音实验室座位数.tjnf as 统计年份,
百名学生配多媒体教室和语音实验室座位数.zwsl as 座位数量
from ins_nr_0058 百名学生配多媒体教室和语音实验室座位数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0058'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0059', 'ins_nr_0059', '生均年进书量', TO_CLOB('
select
生均年进书量.tjfw as 统计范围,
生均年进书量.tjnf as 统计年份,
生均年进书量.sl as 数量
from ins_nr_0059 生均年进书量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0059'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_nr_0060', 'ins_nr_0060', '竞赛学校排名', TO_CLOB('
select
竞赛学校排名.xxmc as 学校名称,
竞赛学校排名.xxsf as 学校省份,
竞赛学校排名.zybd as 专业布点,
竞赛学校排名.pjdc as 评级档次,
竞赛学校排名.jslx as 竞赛类型,
竞赛学校排名.tjnf as 统计年份,
竞赛学校排名.qgpm as 全国排名,
竞赛学校排名.jwnqgpm as 近五年全国排名,
竞赛学校排名.snpm as 省内排名,
竞赛学校排名.hjsl as 获奖数量
from ins_nr_0060 竞赛学校排名'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_nr_0060'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0136', 'ins_si_col_0136', '每年外聘教师人数', TO_CLOB('
select
每年外聘教师人数.xybh as 学院编号,
每年外聘教师人数.zy as 专业,
每年外聘教师人数.tjnf as 统计年份,
每年外聘教师人数.wpjss as 外聘教师数,
每年外聘教师人数.xymc as 学院名称,
每年外聘教师人数.zymc as 专业名称
from ins_si_col_0136 每年外聘教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0136'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0137', 'ins_si_col_0137', '每年各职称教职工人数', TO_CLOB('
select
每年各职称教职工人数.xybh as 学院编号,
每年各职称教职工人数.zyjszc as 专业技术职称,
每年各职称教职工人数.zy as 专业,
每年各职称教职工人数.tjnf as 统计年份,
每年各职称教职工人数.jzgrs as 教职工人数,
每年各职称教职工人数.xymc as 学院名称,
每年各职称教职工人数.zymc as 专业名称
from ins_si_col_0137 每年各职称教职工人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0137'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0138', 'ins_si_col_0138', '每年各年龄教职工人数', TO_CLOB('
select
每年各年龄教职工人数.xybh as 学院编号,
每年各年龄教职工人数.nld as 年龄段,
每年各年龄教职工人数.zy as 专业,
每年各年龄教职工人数.tjnf as 统计年份,
每年各年龄教职工人数.jzgrs as 教职工人数,
每年各年龄教职工人数.xymc as 学院名称,
每年各年龄教职工人数.zymc as 专业名称
from ins_si_col_0138 每年各年龄教职工人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0138'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0139', 'ins_si_col_0139', '每年各最高学位教职工人数', TO_CLOB('
select
每年各最高学位教职工人数.xybh as 学院编号,
每年各最高学位教职工人数.zgxw as 最高学位,
每年各最高学位教职工人数.zy as 专业,
每年各最高学位教职工人数.tjnf as 统计年份,
每年各最高学位教职工人数.jzgrs as 教职工人数,
每年各最高学位教职工人数.xymc as 学院名称,
每年各最高学位教职工人数.zymc as 专业名称
from ins_si_col_0139 每年各最高学位教职工人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0139'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0140', 'ins_si_col_0140', '每年各学缘教职工人数', TO_CLOB('
select
每年各学缘教职工人数.xybh as 学院编号,
每年各学缘教职工人数.xy as 学缘,
每年各学缘教职工人数.zy as 专业,
每年各学缘教职工人数.tjnf as 统计年份,
每年各学缘教职工人数.jzgrs as 教职工人数,
每年各学缘教职工人数.xymc as 学院名称,
每年各学缘教职工人数.zymc as 专业名称
from ins_si_col_0140 每年各学缘教职工人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0140'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0141', 'ins_si_col_0141', '每年各职称专任教师人数', TO_CLOB('
select
每年各职称专任教师人数.xybh as 学院编号,
每年各职称专任教师人数.zyjszc as 专业技术职称,
每年各职称专任教师人数.zy as 专业,
每年各职称专任教师人数.tjnf as 统计年份,
每年各职称专任教师人数.zrjss as 专任教师数,
每年各职称专任教师人数.xymc as 学院名称,
每年各职称专任教师人数.zymc as 专业名称
from ins_si_col_0141 每年各职称专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0141'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0142', 'ins_si_col_0142', '每年各年龄专任教师人数', TO_CLOB('
select
每年各年龄专任教师人数.xybh as 学院编号,
每年各年龄专任教师人数.nld as 年龄段,
每年各年龄专任教师人数.zy as 专业,
每年各年龄专任教师人数.tjnf as 统计年份,
每年各年龄专任教师人数.zrjss as 专任教师数,
每年各年龄专任教师人数.xymc as 学院名称,
每年各年龄专任教师人数.zymc as 专业名称
from ins_si_col_0142 每年各年龄专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0142'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0143', 'ins_si_col_0143', '每年各最高学位专任教师人数', TO_CLOB('
select
每年各最高学位专任教师人数.xybh as 学院编号,
每年各最高学位专任教师人数.zgxw as 最高学位,
每年各最高学位专任教师人数.zy as 专业,
每年各最高学位专任教师人数.tjnf as 统计年份,
每年各最高学位专任教师人数.zrjss as 专任教师数,
每年各最高学位专任教师人数.xymc as 学院名称,
每年各最高学位专任教师人数.zymc as 专业名称
from ins_si_col_0143 每年各最高学位专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0143'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0144', 'ins_si_col_0144', '每年各学缘专任教师人数', TO_CLOB('
select
每年各学缘专任教师人数.xybh as 学院编号,
每年各学缘专任教师人数.xy as 学缘,
每年各学缘专任教师人数.zy as 专业,
每年各学缘专任教师人数.tjnf as 统计年份,
每年各学缘专任教师人数.zrjss as 专任教师数,
每年各学缘专任教师人数.xymc as 学院名称,
每年各学缘专任教师人数.zymc as 专业名称
from ins_si_col_0144 每年各学缘专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0144'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0145', 'ins_si_col_0145', '每年专任教师中高层次人才数量', TO_CLOB('
select
每年专任教师中高层次人才数量.xybh as 学院编号,
每年专任教师中高层次人才数量.tjnf as 统计年份,
每年专任教师中高层次人才数量.gccrcsl as 高层次人才数量,
每年专任教师中高层次人才数量.xymc as 学院名称
from ins_si_col_0145 每年专任教师中高层次人才数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0145'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0146', 'ins_si_col_0146', '每年各专任教师类型人数', TO_CLOB('
select
每年各专任教师类型人数.xybh as 学院编号,
每年各专任教师类型人数.zrjslx as 专任教师类型,
每年各专任教师类型人数.tjnf as 统计年份,
每年各专任教师类型人数.zrjss as 专任教师数,
每年各专任教师类型人数.xymc as 学院名称
from ins_si_col_0146 每年各专任教师类型人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0146'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0147', 'ins_si_col_0147', '每年各专任教师类型比例', TO_CLOB('
select
每年各专任教师类型比例.xybh as 学院编号,
每年各专任教师类型比例.zrjslx as 专任教师类型,
每年各专任教师类型比例.tjnf as 统计年份,
每年各专任教师类型比例.zrjsbl as 专任教师比例,
每年各专任教师类型比例.xymc as 学院名称
from ins_si_col_0147 每年各专任教师类型比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0147'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0148', 'ins_si_col_0148', '每年临近退休专任教师人数', TO_CLOB('
select
每年临近退休专任教师人数.xybh as 学院编号,
每年临近退休专任教师人数.zyjszc as 专业技术职称,
每年临近退休专任教师人数.zy as 专业,
每年临近退休专任教师人数.tjnf as 统计年份,
每年临近退休专任教师人数.zrjss as 专任教师数,
每年临近退休专任教师人数.xymc as 学院名称,
每年临近退休专任教师人数.zymc as 专业名称
from ins_si_col_0148 每年临近退休专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0148'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0149', 'ins_si_col_0149', '每年管理人员数量', TO_CLOB('
select
每年管理人员数量.xybh as 学院编号,
每年管理人员数量.glrylb as 管理人员类别,
每年管理人员数量.tjnf as 统计年份,
每年管理人员数量.glrysl as 管理人员数量,
每年管理人员数量.xymc as 学院名称
from ins_si_col_0149 每年管理人员数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0149'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0150', 'ins_si_col_0150', '每年各职称实验技术人员数量', TO_CLOB('
select
每年各职称实验技术人员数量.xybh as 学院编号,
每年各职称实验技术人员数量.zyjszc as 专业技术职称,
每年各职称实验技术人员数量.zy as 专业,
每年各职称实验技术人员数量.tjnf as 统计年份,
每年各职称实验技术人员数量.syjsrysl as 实验技术人员数量,
每年各职称实验技术人员数量.xymc as 学院名称,
每年各职称实验技术人员数量.zymc as 专业名称
from ins_si_col_0150 每年各职称实验技术人员数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0150'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0151', 'ins_si_col_0151', '每年各年龄实验技术人员数量', TO_CLOB('
select
每年各年龄实验技术人员数量.xybh as 学院编号,
每年各年龄实验技术人员数量.nlfw as 年龄范围,
每年各年龄实验技术人员数量.zy as 专业,
每年各年龄实验技术人员数量.tjnf as 统计年份,
每年各年龄实验技术人员数量.syjsrysl as 实验技术人员数量,
每年各年龄实验技术人员数量.xymc as 学院名称,
每年各年龄实验技术人员数量.zymc as 专业名称
from ins_si_col_0151 每年各年龄实验技术人员数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0151'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0152', 'ins_si_col_0152', '每年各最高学位实验技术人员数量', TO_CLOB('
select
每年各最高学位实验技术人员数量.xybh as 学院编号,
每年各最高学位实验技术人员数量.zgxw as 最高学位,
每年各最高学位实验技术人员数量.zy as 专业,
每年各最高学位实验技术人员数量.tjnf as 统计年份,
每年各最高学位实验技术人员数量.syjsrysl as 实验技术人员数量,
每年各最高学位实验技术人员数量.xymc as 学院名称,
每年各最高学位实验技术人员数量.zymc as 专业名称
from ins_si_col_0152 每年各最高学位实验技术人员数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0152'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0153', 'ins_si_col_0153', '每年未上课教授人数', TO_CLOB('
select
每年未上课教授人数.xybh as 学院编号,
每年未上课教授人数.tjnf as 统计年份,
每年未上课教授人数.jsrs as 教授人数,
每年未上课教授人数.xymc as 学院名称,
每年未上课教授人数.zymc as 专业名称,
每年未上课教授人数.zydm as 专业代码
from ins_si_col_0153 每年未上课教授人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0153'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0154', 'ins_si_col_0154', '每年未上课高层次人才数量', TO_CLOB('
select
每年未上课高层次人才数量.xybh as 学院编号,
每年未上课高层次人才数量.tjnf as 统计年份,
每年未上课高层次人才数量.gccrcsl as 高层次人才数量,
每年未上课高层次人才数量.xymc as 学院名称
from ins_si_col_0154 每年未上课高层次人才数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0154'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0155', 'ins_si_col_0155', '每年临近退休授课教师人数', TO_CLOB('
select
每年临近退休授课教师人数.xybh as 学院编号,
每年临近退休授课教师人数.zy as 专业,
每年临近退休授课教师人数.tjnf as 统计年份,
每年临近退休授课教师人数.skjss as 授课教师数,
每年临近退休授课教师人数.xymc as 学院名称,
每年临近退休授课教师人数.zymc as 专业名称
from ins_si_col_0155 每年临近退休授课教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0155'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0156', 'ins_si_col_0156', '每年专任教师最高学历毕业的前十名学校', TO_CLOB('
select
每年专任教师最高学历毕业的前十名学校.xybh as 学院编号,
每年专任教师最高学历毕业的前十名学校.zgxwgxmc as 最高学位高校名称,
每年专任教师最高学历毕业的前十名学校.tjnf as 统计年份,
每年专任教师最高学历毕业的前十名学校.xxmc as 学校名称,
每年专任教师最高学历毕业的前十名学校.zrjss as 专任教师数,
每年专任教师最高学历毕业的前十名学校.xymc as 学院名称
from ins_si_col_0156 每年专任教师最高学历毕业的前十名学校'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0156'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0157', 'ins_si_col_0157', '每年双师双能型专任教师人数', TO_CLOB('
select
每年双师双能型专任教师人数.xybh as 学院编号,
每年双师双能型专任教师人数.tjnf as 统计年份,
每年双师双能型专任教师人数.zrjss as 专任教师数,
每年双师双能型专任教师人数.xymc as 学院名称
from ins_si_col_0157 每年双师双能型专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0157'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0158', 'ins_si_col_0158', '每年各学院具有一年以上国(境)外经历专任教师比例', TO_CLOB('
select
每年各学院具有一年以上国境外经历专任教师比例.xybh as 学院编号,
每年各学院具有一年以上国境外经历专任教师比例.tjnf as 统计年份,
每年各学院具有一年以上国境外经历专任教师比例.zrjsbl as 专任教师比例,
每年各学院具有一年以上国境外经历专任教师比例.xymc as 学院名称
from ins_si_col_0158 每年各学院具有一年以上国境外经历专任教师比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0158'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0159', 'ins_si_col_0159', '每年具有一年以上国(境)外经历专任教师人数', TO_CLOB('
select
每年具有一年以上国境外经历专任教师人数.xybh as 学院编号,
每年具有一年以上国境外经历专任教师人数.tjnf as 统计年份,
每年具有一年以上国境外经历专任教师人数.zrjss as 专任教师数,
每年具有一年以上国境外经历专任教师人数.xymc as 学院名称
from ins_si_col_0159 每年具有一年以上国境外经历专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0159'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0160', 'ins_si_col_0160', '每年各学院教授主讲本科课程人均学时数', TO_CLOB('
select
每年各学院教授主讲本科课程人均学时数.xybh as 学院编号,
每年各学院教授主讲本科课程人均学时数.jsrjskxs as 教授人均授课学时,
每年各学院教授主讲本科课程人均学时数.xymc as 学院名称,
每年各学院教授主讲本科课程人均学时数.xn as 学年
from ins_si_col_0160 每年各学院教授主讲本科课程人均学时数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0160'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0161', 'ins_si_col_0161', '学校主讲本科课程教师人数', TO_CLOB('
select
学校主讲本科课程教师人数.xybh as 学院编号,
学校主讲本科课程教师人数.zyjszc as 专业技术职称,
学校主讲本科课程教师人数.zy as 专业,
学校主讲本科课程教师人数.skjss as 授课教师数,
学校主讲本科课程教师人数.xymc as 学院名称,
学校主讲本科课程教师人数.zymc as 专业名称,
学校主讲本科课程教师人数.xn as 学年
from ins_si_col_0161 学校主讲本科课程教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0161'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0162', 'ins_si_col_0162', '学校学院及专业下主讲本科课程教授占教授总数的比例', TO_CLOB('
select
学校学院及专业下主讲本科课程教授占教授总数的比例.xybh as 学院编号,
学校学院及专业下主讲本科课程教授占教授总数的比例.skjsb as 授课教授比,
学校学院及专业下主讲本科课程教授占教授总数的比例.xymc as 学院名称,
学校学院及专业下主讲本科课程教授占教授总数的比例.xn as 学年
from ins_si_col_0162 学校学院及专业下主讲本科课程教授占教授总数的比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0162'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0163', 'ins_si_col_0163', '每年高层次人才授课人数', TO_CLOB('
select
每年高层次人才授课人数.xybh as 学院编号,
每年高层次人才授课人数.skgccrcs as 授课高层次人才数,
每年高层次人才授课人数.xymc as 学院名称,
每年高层次人才授课人数.xn as 学年
from ins_si_col_0163 每年高层次人才授课人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0163'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0164', 'ins_si_col_0164', '每年各学院高层次人才授课比例', TO_CLOB('
select
每年各学院高层次人才授课比例.xybh as 学院编号,
每年各学院高层次人才授课比例.skgccrcb as 授课高层次人才比,
每年各学院高层次人才授课比例.xymc as 学院名称,
每年各学院高层次人才授课比例.xn as 学年
from ins_si_col_0164 每年各学院高层次人才授课比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0164'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0165', 'ins_si_col_0165', '每年教师授课门次', TO_CLOB('
select
每年教师授课门次.xybh as 学院编号,
每年教师授课门次.zy as 专业,
每年教师授课门次.xnxq as 学年学期,
每年教师授课门次.skmc as 授课门次,
每年教师授课门次.xymc as 学院名称,
每年教师授课门次.zymc as 专业名称
from ins_si_col_0165 每年教师授课门次'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0165'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0166', 'ins_si_col_0166', '每年教师授课门数', TO_CLOB('
select
每年教师授课门数.xybh as 学院编号,
每年教师授课门数.zy as 专业,
每年教师授课门数.skms as 授课门数,
每年教师授课门数.xymc as 学院名称,
每年教师授课门数.zymc as 专业名称,
每年教师授课门数.xn as 学年
from ins_si_col_0166 每年教师授课门数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0166'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0167', 'ins_si_col_0167', '每年教师授课比例', TO_CLOB('
select
每年教师授课比例.xybh as 学院编号,
每年教师授课比例.zy as 专业,
每年教师授课比例.skbl as 授课比例,
每年教师授课比例.xymc as 学院名称,
每年教师授课比例.zymc as 专业名称,
每年教师授课比例.xn as 学年
from ins_si_col_0167 每年教师授课比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0167'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0168', 'ins_si_col_0168', '每年教学团队数量', TO_CLOB('
select
每年教学团队数量.xybh as 学院编号,
每年教学团队数量.jcjxzzlx as 基层教学组织类型,
每年教学团队数量.zy as 专业,
每年教学团队数量.tjnf as 统计年份,
每年教学团队数量.jxtdsl as 教学团队数量,
每年教学团队数量.xymc as 学院名称,
每年教学团队数量.zymc as 专业名称
from ins_si_col_0168 每年教学团队数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0168'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0169', 'ins_si_col_0169', '每年新进教师培训人数', TO_CLOB('
select
每年新进教师培训人数.xybh as 学院编号,
每年新进教师培训人数.rxsj as 入校时间,
每年新进教师培训人数.tjnf as 统计年份,
每年新进教师培训人数.jzgrs as 教职工人数,
每年新进教师培训人数.xymc as 学院名称
from ins_si_col_0169 每年新进教师培训人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0169'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0170', 'ins_si_col_0170', '学院签约基地数量', TO_CLOB('
select
学院签约基地数量.xybh as 学院编号,
学院签约基地数量.tjnf as 统计年份,
学院签约基地数量.sfyx as 是否有效,
学院签约基地数量.jdsl as 基地数量,
学院签约基地数量.xymc as 学院名称
from ins_si_col_0170 学院签约基地数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0170'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0175', 'ins_si_col_0175', '校外实习基地', TO_CLOB('
select
校外实习基地.xybh as 学院编号,
校外实习基地.tjnf as 统计年份,
校外实习基地.dd as 地点,
校外实习基地.jdsl as 基地数量,
校外实习基地.xymc as 学院名称
from ins_si_col_0175 校外实习基地'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0175'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0176', 'ins_si_col_0176', '独立实验课程', TO_CLOB('
select
独立实验课程.xybh as 学院编号,
独立实验课程.tjnf as 统计年份,
独立实验课程.kcxz as 课程性质,
独立实验课程.kcsl as 课程数量,
独立实验课程.xymc as 学院名称
from ins_si_col_0176 独立实验课程'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0176'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0177', 'ins_si_col_0177', '产学合作协同育人立项', TO_CLOB('
select
产学合作协同育人立项.xybh as 学院编号,
产学合作协同育人立项.tjnf as 统计年份,
产学合作协同育人立项.lxxmsl as 立项项目数量,
产学合作协同育人立项.xymc as 学院名称,
产学合作协同育人立项.lxsl as 立项数量
from ins_si_col_0177 产学合作协同育人立项'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0177'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0178', 'ins_si_col_0178', '每年学校各学院实习开展次数', TO_CLOB('
select
每年学校各学院实习开展次数.xybh as 学院编号,
每年学校各学院实习开展次数.sxlb as 实习类别,
每年学校各学院实习开展次数.kzcs as 开展次数,
每年学校各学院实习开展次数.xymc as 学院名称,
每年学校各学院实习开展次数.tjnf as 统计年份
from ins_si_col_0178 每年学校各学院实习开展次数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0178'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0188', 'ins_si_col_0188', '每年学院生师比', TO_CLOB('
select
每年学院生师比.xybh as 学院编号,
每年学院生师比.tjnf as 统计年份,
每年学院生师比.xymc as 学院名称,
每年学院生师比.ssb as 生师比
from ins_si_col_0188 每年学院生师比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0188'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0189', 'ins_si_col_0189', '每年学院专职辅导员与在校生比', TO_CLOB('
select
每年学院专职辅导员与在校生比.xybh as 学院编号,
每年学院专职辅导员与在校生比.tjnf as 统计年份,
每年学院专职辅导员与在校生比.xymc as 学院名称,
每年学院专职辅导员与在校生比.zrfdyssb as 专任辅导员生师比
from ins_si_col_0189 每年学院专职辅导员与在校生比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0189'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0190', 'ins_si_col_0190', '每年学院班导师与在校生比', TO_CLOB('
select
每年学院班导师与在校生比.xybh as 学院编号,
每年学院班导师与在校生比.zy as 专业,
每年学院班导师与在校生比.tjnf as 统计年份,
每年学院班导师与在校生比.xymc as 学院名称,
每年学院班导师与在校生比.bdsssb as 班导师生师比
from ins_si_col_0190 每年学院班导师与在校生比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0190'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0191', 'ins_si_col_0191', '每学期课程开设门数', TO_CLOB('
select
每学期课程开设门数.xybh as 学院编号,
每学期课程开设门数.kclb as 课程类别,
每学期课程开设门数.xnxq as 学年学期,
每学期课程开设门数.xymc as 学院名称,
每学期课程开设门数.kkms as 开课门数
from ins_si_col_0191 每学期课程开设门数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0191'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0192', 'ins_si_col_0192', '每学期课程开设学时数', TO_CLOB('
select
每学期课程开设学时数.xybh as 学院编号,
每学期课程开设学时数.kclb as 课程类别,
每学期课程开设学时数.xnxq as 学年学期,
每学期课程开设学时数.xymc as 学院名称,
每学期课程开设学时数.kkpjxss as 开课平均学时数
from ins_si_col_0192 每学期课程开设学时数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0192'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0193', 'ins_si_col_0193', '每年双语课程开设门数', TO_CLOB('
select
每年双语课程开设门数.xybh as 学院编号,
每年双语课程开设门数.xymc as 学院名称,
每年双语课程开设门数.kkms as 开课门数,
每年双语课程开设门数.kclb as 课程类别,
每年双语课程开设门数.xn as 学年
from ins_si_col_0193 每年双语课程开设门数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0193'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0194', 'ins_si_col_0194', '每学期开课平均学时', TO_CLOB('
select
每学期开课平均学时.xybh as 学院编号,
每学期开课平均学时.xymc as 学院名称,
每学期开课平均学时.xs as 学时,
每学期开课平均学时.xnxq as 学年学期,
每学期开课平均学时.kclb as 课程类别
from ins_si_col_0194 每学期开课平均学时'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0194'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0195', 'ins_si_col_0195', '每学期教学班数量', TO_CLOB('
select
每学期教学班数量.xybh as 学院编号,
每学期教学班数量.xymc as 学院名称,
每学期教学班数量.jxbsl as 教学班数量,
每学期教学班数量.xnxq as 学年学期
from ins_si_col_0195 每学期教学班数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0195'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0196', 'ins_si_col_0196', '每年学校各学院不同类型优势专业数量', TO_CLOB('
select
每年学校各学院不同类型优势专业数量.xybh as 学院编号,
每年学校各学院不同类型优势专业数量.zylx as 专业类型,
每年学校各学院不同类型优势专业数量.tjnf as 统计年份,
每年学校各学院不同类型优势专业数量.xymc as 学院名称,
每年学校各学院不同类型优势专业数量.zysl as 专业数量
from ins_si_col_0196 每年学校各学院不同类型优势专业数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0196'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0197', 'ins_si_col_0197', '每年学校各学院国家级双万专业占专业总数比例', TO_CLOB('
select
每年学校各学院国家级双万专业占专业总数比例.xybh as 学院编号,
每年学校各学院国家级双万专业占专业总数比例.tjnf as 统计年份,
每年学校各学院国家级双万专业占专业总数比例.xymc as 学院名称,
每年学校各学院国家级双万专业占专业总数比例.swzyzb as 双万专业占比
from ins_si_col_0197 每年学校各学院国家级双万专业占专业总数比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0197'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0198', 'ins_si_col_0198', '每年优质生源录取人数', TO_CLOB('
select
每年优质生源录取人数.xybh as 学院编号,
每年优质生源录取人数.tjnf as 统计年份,
每年优质生源录取人数.xymc as 学院名称,
每年优质生源录取人数.lqrs as 录取人数
from ins_si_col_0198 每年优质生源录取人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0198'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0199', 'ins_si_col_0199', '每年优质生源中学数量', TO_CLOB('
select
每年优质生源中学数量.xybh as 学院编号,
每年优质生源中学数量.tjnf as 统计年份,
每年优质生源中学数量.xymc as 学院名称,
每年优质生源中学数量.yzsyzxs as 优质生源中学数
from ins_si_col_0199 每年优质生源中学数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0199'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0200', 'ins_si_col_0200', '每年国外港澳台学生数量', TO_CLOB('
select
每年国外港澳台学生数量.xybh as 学院编号,
每年国外港澳台学生数量.sylb as 生源类别,
每年国外港澳台学生数量.tjnf as 统计年份,
每年国外港澳台学生数量.xymc as 学院名称,
每年国外港澳台学生数量.xssl as 学生数量
from ins_si_col_0200 每年国外港澳台学生数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0200'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0201', 'ins_si_col_0201', '每年学院毕业生英语等级通过率', TO_CLOB('
select
每年学院毕业生英语等级通过率.xybh as 学院编号,
每年学院毕业生英语等级通过率.yydj as 英语等级,
每年学院毕业生英语等级通过率.xymc as 学院名称,
每年学院毕业生英语等级通过率.tgl as 通过率,
每年学院毕业生英语等级通过率.tjnf as 统计年份
from ins_si_col_0201 每年学院毕业生英语等级通过率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0201'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0202', 'ins_si_col_0202', '每年学校各类型教改项目立项项目', TO_CLOB('
select
每年学校各类型教改项目立项项目.xybh as 学院编号,
每年学校各类型教改项目立项项目.lxnf as 立项年份,
每年学校各类型教改项目立项项目.xmlx as 项目类型,
每年学校各类型教改项目立项项目.xymc as 学院名称,
每年学校各类型教改项目立项项目.xmsl as 项目数量,
每年学校各类型教改项目立项项目.lxsj as 立项时间
from ins_si_col_0202 每年学校各类型教改项目立项项目'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0202'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0203', 'ins_si_col_0203', '每年学校各级别教学改革项目', TO_CLOB('
select
每年学校各级别教学改革项目.xybh as 学院编号,
每年学校各级别教学改革项目.tjnf as 统计年份,
每年学校各级别教学改革项目.jb as 级别,
每年学校各级别教学改革项目.xymc as 学院名称,
每年学校各级别教学改革项目.xmsl as 项目数量
from ins_si_col_0203 每年学校各级别教学改革项目'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0203'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0204', 'ins_si_col_0204', '每年学校各学院双创竞赛获奖数量', TO_CLOB('
select
每年学校各学院双创竞赛获奖数量.xybh as 学院编号,
每年学校各学院双创竞赛获奖数量.hjjb as 获奖级别,
每年学校各学院双创竞赛获奖数量.xmlb as 项目类别,
每年学校各学院双创竞赛获奖数量.tjnf as 统计年份,
每年学校各学院双创竞赛获奖数量.xymc as 学院名称,
每年学校各学院双创竞赛获奖数量.schjsl as 双创获奖数量
from ins_si_col_0204 每年学校各学院双创竞赛获奖数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0204'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0205', 'ins_si_col_0205', '每年学院大创项目数量', TO_CLOB('
select
每年学院大创项目数量.xybh as 学院编号,
每年学院大创项目数量.tjnf as 统计年份,
每年学院大创项目数量.xmzt as 项目状态,
每年学院大创项目数量.xmjb as 项目级别,
每年学院大创项目数量.xmlb as 项目类别,
每年学院大创项目数量.xymc as 学院名称,
每年学院大创项目数量.xmsl as 项目数量
from ins_si_col_0205 每年学院大创项目数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0205'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0208', 'ins_si_col_0208', '学科竞赛获奖人数', TO_CLOB('
select
学科竞赛获奖人数.xybh as 学院编号,
学科竞赛获奖人数.tjnf as 统计年份,
学科竞赛获奖人数.xmjb as 项目级别,
学科竞赛获奖人数.xmlb as 项目类别,
学科竞赛获奖人数.xymc as 学院名称,
学科竞赛获奖人数.hjrs as 获奖人数
from ins_si_col_0208 学科竞赛获奖人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0208'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0209', 'ins_si_col_0209', '每年本科生发表学术论文', TO_CLOB('
select
每年本科生发表学术论文.xybh as 学院编号,
每年本科生发表学术论文.tjnf as 统计年份,
每年本科生发表学术论文.fbqk as 发表期刊,
每年本科生发表学术论文.xymc as 学院名称,
每年本科生发表学术论文.xslwsl as 学术论文数量
from ins_si_col_0209 每年本科生发表学术论文'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0209'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0210', 'ins_si_col_0210', '每年本科生获准专利著作权数', TO_CLOB('
select
每年本科生获准专利著作权数.xybh as 学院编号,
每年本科生获准专利著作权数.tjnf as 统计年份,
每年本科生获准专利著作权数.lx as 类型,
每年本科生获准专利著作权数.xymc as 学院名称,
每年本科生获准专利著作权数.sl as 数量
from ins_si_col_0210 每年本科生获准专利著作权数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0210'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0211', 'ins_si_col_0211', '每年毕业综合训练课题数量', TO_CLOB('
select
每年毕业综合训练课题数量.xybh as 学院编号,
每年毕业综合训练课题数量.xtlb as 选题类别,
每年毕业综合训练课题数量.xymc as 学院名称,
每年毕业综合训练课题数量.bylwsl as 毕业论文数量,
每年毕业综合训练课题数量.xn as 学年
from ins_si_col_0211 每年毕业综合训练课题数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0211'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0212', 'ins_si_col_0212', '每年各学院以实验、实习、工程实践和社会调查等为基础的论文占比', TO_CLOB('
select
每年各学院以实验实习工程实践和社会调查等为基础的论文占比.xybh as 学院编号,
每年各学院以实验实习工程实践和社会调查等为基础的论文占比.sfzsy as 是否在实验,
每年各学院以实验实习工程实践和社会调查等为基础的论文占比.sx as 实习,
每年各学院以实验实习工程实践和社会调查等为基础的论文占比.gcsjhshdcdshsjzwc as 工程实践和社会调查等社会实践中完成,
每年各学院以实验实习工程实践和社会调查等为基础的论文占比.xymc as 学院名称,
每年各学院以实验实习工程实践和社会调查等为基础的论文占比.sjllwzb as 实践类论文占比,
每年各学院以实验实习工程实践和社会调查等为基础的论文占比.xn as 学年
from ins_si_col_0212 每年各学院以实验实习工程实践和社会调查等为基础的论文占比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0212'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0213', 'ins_si_col_0213', '每年学校各学院毕业综合训练课题完成率', TO_CLOB('
select
每年学校各学院毕业综合训练课题完成率.xybh as 学院编号,
每年学校各学院毕业综合训练课题完成率.zy as 专业,
每年学校各学院毕业综合训练课题完成率.xymc as 学院名称,
每年学校各学院毕业综合训练课题完成率.xlwcl as 训练完成率,
每年学校各学院毕业综合训练课题完成率.xn as 学年
from ins_si_col_0213 每年学校各学院毕业综合训练课题完成率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0213'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0214', 'ins_si_col_0214', '每年学校各学院及专业平均指导毕业生数', TO_CLOB('
select
每年学校各学院及专业平均指导毕业生数.xybh as 学院编号,
每年学校各学院及专业平均指导毕业生数.zy as 专业,
每年学校各学院及专业平均指导毕业生数.xymc as 学院名称,
每年学校各学院及专业平均指导毕业生数.pjzdbysrs as 平均指导毕业生人数,
每年学校各学院及专业平均指导毕业生数.xn as 学年,
每年学校各学院及专业平均指导毕业生数.zymc as 专业名称
from ins_si_col_0214 每年学校各学院及专业平均指导毕业生数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0214'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0217', 'ins_si_col_0217', '每年发表学术论文人数', TO_CLOB('
select
每年发表学术论文人数.xybh as 学院编号,
每年发表学术论文人数.tjnf as 统计年份,
每年发表学术论文人数.xymc as 学院名称,
每年发表学术论文人数.xsrs as 学生人数
from ins_si_col_0217 每年发表学术论文人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0217'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0218', 'ins_si_col_0218', '每年以第一作者获批专利人数', TO_CLOB('
select
每年以第一作者获批专利人数.xybh as 学院编号,
每年以第一作者获批专利人数.tjnf as 统计年份,
每年以第一作者获批专利人数.xymc as 学院名称,
每年以第一作者获批专利人数.xsrs as 学生人数
from ins_si_col_0218 每年以第一作者获批专利人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0218'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0219', 'ins_si_col_0219', '每年学科竞赛参与人数', TO_CLOB('
select
每年学科竞赛参与人数.xybh as 学院编号,
每年学科竞赛参与人数.tjnf as 统计年份,
每年学科竞赛参与人数.xymc as 学院名称,
每年学科竞赛参与人数.xscyrs as 学生参与人数,
每年学科竞赛参与人数.jscyrs as 教师参与人数
from ins_si_col_0219 每年学科竞赛参与人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0219'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0220', 'ins_si_col_0220', '每年学科竞赛参与比例', TO_CLOB('
select
每年学科竞赛参与比例.xybh as 学院编号,
每年学科竞赛参与比例.tjnf as 统计年份,
每年学科竞赛参与比例.xymc as 学院名称,
每年学科竞赛参与比例.xscybl as 学生参与比例
from ins_si_col_0220 每年学科竞赛参与比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0220'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0221', 'ins_si_col_0221', '每年体质健康合格率', TO_CLOB('
select
每年体质健康合格率.xybh as 学院编号,
每年体质健康合格率.tjnf as 统计年份,
每年体质健康合格率.xymc as 学院名称,
每年体质健康合格率.tzjkhgl as 体质健康合格率
from ins_si_col_0221 每年体质健康合格率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0221'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0222', 'ins_si_col_0222', '每年学校各学院毕业生去向落实率', TO_CLOB('
select
每年学校各学院毕业生去向落实率.xybh as 学院编号,
每年学校各学院毕业生去向落实率.xymc as 学院名称,
每年学校各学院毕业生去向落实率.lsl as 落实率,
每年学校各学院毕业生去向落实率.xn as 学年
from ins_si_col_0222 每年学校各学院毕业生去向落实率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0222'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0223', 'ins_si_col_0223', '每年学位授予率', TO_CLOB('
select
每年学位授予率.xybh as 学院编号,
每年学位授予率.xymc as 学院名称,
每年学位授予率.xwsyl as 学位授予率,
每年学位授予率.xn as 学年
from ins_si_col_0223 每年学位授予率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0223'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0224', 'ins_si_col_0224', '每年学校各学院学位人数', TO_CLOB('
select
每年学校各学院学位人数.xybh as 学院编号,
每年学校各学院学位人数.xymc as 学院名称,
每年学校各学院学位人数.xwsyrs as 学位授予人数,
每年学校各学院学位人数.xn as 学年
from ins_si_col_0224 每年学校各学院学位人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0224'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0225', 'ins_si_col_0225', '每学年学业预警学生数量', TO_CLOB('
select
每学年学业预警学生数量.xybh as 学院编号,
每学年学业预警学生数量.yjjb as 预警级别,
每学年学业预警学生数量.xymc as 学院名称,
每学年学业预警学生数量.xsrs as 学生人数,
每学年学业预警学生数量.xn as 学年
from ins_si_col_0225 每学年学业预警学生数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0225'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0226', 'ins_si_col_0226', '每学年不及格学生数量', TO_CLOB('
select
每学年不及格学生数量.xybh as 学院编号,
每学年不及格学生数量.xymc as 学院名称,
每学年不及格学生数量.xsrs as 学生人数,
每学年不及格学生数量.xn as 学年,
每学年不及格学生数量.gkms as 挂科门数
from ins_si_col_0226 每学年不及格学生数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0226'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0227', 'ins_si_col_0227', '每学年不同累计不及格学分数范围的学生数量', TO_CLOB('
select
每学年不同累计不及格学分数范围的学生数量.xybh as 学院编号,
每学年不同累计不及格学分数范围的学生数量.ljbjgxffw as 累计不及格学分范围,
每学年不同累计不及格学分数范围的学生数量.xymc as 学院名称,
每学年不同累计不及格学分数范围的学生数量.xsrs as 学生人数,
每学年不同累计不及格学分数范围的学生数量.xn as 学年,
每学年不同累计不及格学分数范围的学生数量.zy as 专业
from ins_si_col_0227 每学年不同累计不及格学分数范围的学生数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0227'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0228', 'ins_si_col_0228', '每年学校各学院课程测评均分', TO_CLOB('
select
每年学校各学院课程测评均分.xybh as 学院编号,
每年学校各学院课程测评均分.xymc as 学院名称,
每年学校各学院课程测评均分.kcxz as 课程性质,
每年学校各学院课程测评均分.cpjf as 测评均分,
每年学校各学院课程测评均分.xn as 学年,
每年学校各学院课程测评均分.kcfl as 课程分类
from ins_si_col_0228 每年学校各学院课程测评均分'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0228'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0230', 'ins_si_col_0230', '每年关注课程数量', TO_CLOB('
select
每年关注课程数量.xybh as 学院编号,
每年关注课程数量.tjnf as 统计年份,
每年关注课程数量.xymc as 学院名称,
每年关注课程数量.kcsl as 课程数量
from ins_si_col_0230 每年关注课程数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0230'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0231', 'ins_si_col_0231', '每年各学院听课均分', TO_CLOB('
select
每年各学院听课均分.xybh as 学院编号,
每年各学院听课均分.zy as 专业,
每年各学院听课均分.xymc as 学院名称,
每年各学院听课均分.ddtkjf as 督导听课均分,
每年各学院听课均分.xn as 学年,
每年各学院听课均分.tkrysf as 听课人员身份,
每年各学院听课均分.tkjf as 听课均分
from ins_si_col_0231 每年各学院听课均分'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0231'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0232', 'ins_si_col_0232', '每年各学院课程听课覆盖率', TO_CLOB('
select
每年各学院课程听课覆盖率.xybh as 学院编号,
每年各学院课程听课覆盖率.zy as 专业,
每年各学院课程听课覆盖率.xymc as 学院名称,
每年各学院课程听课覆盖率.fgl as 覆盖率,
每年各学院课程听课覆盖率.xn as 学年
from ins_si_col_0232 每年各学院课程听课覆盖率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0232'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0233', 'ins_si_col_0233', '每年学校各学院课程目标检查数量', TO_CLOB('
select
每年学校各学院课程目标检查数量.xybh as 学院编号,
每年学校各学院课程目标检查数量.tjnf as 统计年份,
每年学校各学院课程目标检查数量.jcdx as 检查对象,
每年学校各学院课程目标检查数量.jcdj as 检查等级,
每年学校各学院课程目标检查数量.xymc as 学院名称,
每年学校各学院课程目标检查数量.jcsl as 检查数量
from ins_si_col_0233 每年学校各学院课程目标检查数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0233'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0234', 'ins_si_col_0234', '每年学校各学院课程考核检查数量', TO_CLOB('
select
每年学校各学院课程考核检查数量.xybh as 学院编号,
每年学校各学院课程考核检查数量.tjnf as 统计年份,
每年学校各学院课程考核检查数量.jcdx as 检查对象,
每年学校各学院课程考核检查数量.jcdj as 检查等级,
每年学校各学院课程考核检查数量.xymc as 学院名称,
每年学校各学院课程考核检查数量.jcsl as 检查数量,
每年学校各学院课程考核检查数量.kccjsl as 课程抽检数量
from ins_si_col_0234 每年学校各学院课程考核检查数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0234'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0235', 'ins_si_col_0235', '每年学校各学院试卷检查数量', TO_CLOB('
select
每年学校各学院试卷检查数量.xybh as 学院编号,
每年学校各学院试卷检查数量.tjnf as 统计年份,
每年学校各学院试卷检查数量.jcdx as 检查对象,
每年学校各学院试卷检查数量.jcdj as 检查等级,
每年学校各学院试卷检查数量.xymc as 学院名称,
每年学校各学院试卷检查数量.jcsl as 检查数量
from ins_si_col_0235 每年学校各学院试卷检查数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0235'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0236', 'ins_si_col_0236', '每年学校各学院毕业论文检查数量', TO_CLOB('
select
每年学校各学院毕业论文检查数量.xybh as 学院编号,
每年学校各学院毕业论文检查数量.tjnf as 统计年份,
每年学校各学院毕业论文检查数量.jcdx as 检查对象,
每年学校各学院毕业论文检查数量.jcdj as 检查等级,
每年学校各学院毕业论文检查数量.xymc as 学院名称,
每年学校各学院毕业论文检查数量.jcsl as 检查数量
from ins_si_col_0236 每年学校各学院毕业论文检查数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0236'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0237', 'ins_si_col_0237', '每年学校各学院实验报告检查数量', TO_CLOB('
select
每年学校各学院实验报告检查数量.xybh as 学院编号,
每年学校各学院实验报告检查数量.tjnf as 统计年份,
每年学校各学院实验报告检查数量.jcdx as 检查对象,
每年学校各学院实验报告检查数量.jcdj as 检查等级,
每年学校各学院实验报告检查数量.xymc as 学院名称,
每年学校各学院实验报告检查数量.jcsl as 检查数量
from ins_si_col_0237 每年学校各学院实验报告检查数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0237'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0238', 'ins_si_col_0238', '每年学校各学院实习报告检查数量', TO_CLOB('
select
每年学校各学院实习报告检查数量.xybh as 学院编号,
每年学校各学院实习报告检查数量.tjnf as 统计年份,
每年学校各学院实习报告检查数量.jcdx as 检查对象,
每年学校各学院实习报告检查数量.jcdj as 检查等级,
每年学校各学院实习报告检查数量.xymc as 学院名称,
每年学校各学院实习报告检查数量.jcsl as 检查数量
from ins_si_col_0238 每年学校各学院实习报告检查数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0238'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0239', 'ins_si_col_0239', '每年学校各学院课程考核检查抽检率', TO_CLOB('
select
每年学校各学院课程考核检查抽检率.xybh as 学院编号,
每年学校各学院课程考核检查抽检率.xymc as 学院名称,
每年学校各学院课程考核检查抽检率.tjnf as 统计年份,
每年学校各学院课程考核检查抽检率.cjl as 抽检率
from ins_si_col_0239 每年学校各学院课程考核检查抽检率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0239'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0240', 'ins_si_col_0240', '每年学校各学院管理人员数量', TO_CLOB('
select
每年学校各学院管理人员数量.xybh as 学院编号,
每年学校各学院管理人员数量.xymc as 学院名称,
每年学校各学院管理人员数量.tjnf as 统计年份,
每年学校各学院管理人员数量.glrylb as 管理人员类别,
每年学校各学院管理人员数量.rysl as 人员数量
from ins_si_col_0240 每年学校各学院管理人员数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0240'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0241', 'ins_si_col_0241', '每年学校各学院班导师情况', TO_CLOB('
select
每年学校各学院班导师情况.xybh as 学院编号,
每年学校各学院班导师情况.xymc as 学院名称,
每年学校各学院班导师情况.tjnf as 统计年份,
每年学校各学院班导师情况.rysl as 人员数量
from ins_si_col_0241 每年学校各学院班导师情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0241'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0242', 'ins_si_col_0242', '每年教学成果奖数量', TO_CLOB('
select
每年教学成果奖数量.xybh as 学院编号,
每年教学成果奖数量.xymc as 学院名称,
每年教学成果奖数量.tjnf as 统计年份,
每年教学成果奖数量.jb as 级别,
每年教学成果奖数量.cgsl as 成果数量
from ins_si_col_0242 每年教学成果奖数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0242'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0243', 'ins_si_col_0243', '每学期课程开设门次数', TO_CLOB('
select
每学期课程开设门次数.xybh as 学院编号,
每学期课程开设门次数.kclb as 课程类别,
每学期课程开设门次数.xnxq as 学年学期,
每学期课程开设门次数.xymc as 学院名称,
每学期课程开设门次数.kkmcs as 开课门次数
from ins_si_col_0243 每学期课程开设门次数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0243'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0244', 'ins_si_col_0244', '每年各学院开设课程规模', TO_CLOB('
select
每年各学院开设课程规模.xybh as 学院编号,
每年各学院开设课程规模.kclb as 课程类别,
每年各学院开设课程规模.xn as 学年,
每年各学院开设课程规模.pjbgm as 平均班规模,
每年各学院开设课程规模.xymc as 学院名称
from ins_si_col_0244 每年各学院开设课程规模'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0244'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0245', 'ins_si_col_0245', '学院不同毕业论文等级学生数量', TO_CLOB('
select
学院不同毕业论文等级学生数量.xybh as 学院编号,
学院不同毕业论文等级学生数量.lwdj as 论文等级,
学院不同毕业论文等级学生数量.xymc as 学院名称,
学院不同毕业论文等级学生数量.bylwxsrs as 毕业论文学生人数,
学院不同毕业论文等级学生数量.xn as 学年
from ins_si_col_0245 学院不同毕业论文等级学生数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0245'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0246', 'ins_si_col_0246', '每年毕业综合训练指导学生数量', TO_CLOB('
select
每年毕业综合训练指导学生数量.xybh as 学院编号,
每年毕业综合训练指导学生数量.xtlb as 选题类别,
每年毕业综合训练指导学生数量.xymc as 学院名称,
每年毕业综合训练指导学生数量.bysjrs as 毕业实践人数,
每年毕业综合训练指导学生数量.xn as 学年
from ins_si_col_0246 每年毕业综合训练指导学生数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0246'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0247', 'ins_si_col_0247', '每学年课程开设门数', TO_CLOB('
select
每学年课程开设门数.xybh as 学院编号,
每学年课程开设门数.kclb as 课程类别,
每学年课程开设门数.xn as 学年,
每学年课程开设门数.xymc as 学院名称,
每学年课程开设门数.kkms as 开课门数
from ins_si_col_0247 每学年课程开设门数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0247'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0249', 'ins_si_col_0249', '每年各学院毕业综合训练指导教师数量', TO_CLOB('
select
每年各学院毕业综合训练指导教师数量.xybh as 学院编号,
每年各学院毕业综合训练指导教师数量.xn as 学年,
每年各学院毕业综合训练指导教师数量.xymc as 学院名称,
每年各学院毕业综合训练指导教师数量.zdjszs as 指导教师总数
from ins_si_col_0249 每年各学院毕业综合训练指导教师数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0249'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0252', 'ins_si_col_0252', '每学期不及格学生数量', TO_CLOB('
select
每学期不及格学生数量.xybh as 学院编号,
每学期不及格学生数量.gkms as 挂科门数,
每学期不及格学生数量.xnxq as 学年学期,
每学期不及格学生数量.xymc as 学院名称,
每学期不及格学生数量.xsrs as 学生人数,
每学期不及格学生数量.zy as 专业
from ins_si_col_0252 每学期不及格学生数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0252'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0253', 'ins_si_col_0253', '每年高级职称实验技术人员比例', TO_CLOB('
select
每年高级职称实验技术人员比例.xybh as 学院编号,
每年高级职称实验技术人员比例.zyjszc as 专业技术职称,
每年高级职称实验技术人员比例.zy as 专业,
每年高级职称实验技术人员比例.tjnf as 统计年份,
每年高级职称实验技术人员比例.xymc as 学院名称,
每年高级职称实验技术人员比例.bl as 比例
from ins_si_col_0253 每年高级职称实验技术人员比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0253'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0254', 'ins_si_col_0254', '每年各年龄实验技术人员比例', TO_CLOB('
select
每年各年龄实验技术人员比例.xybh as 学院编号,
每年各年龄实验技术人员比例.nlfw as 年龄范围,
每年各年龄实验技术人员比例.zy as 专业,
每年各年龄实验技术人员比例.tjnf as 统计年份,
每年各年龄实验技术人员比例.xymc as 学院名称,
每年各年龄实验技术人员比例.bl as 比例
from ins_si_col_0254 每年各年龄实验技术人员比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0254'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0255', 'ins_si_col_0255', '每年学校各学院管理人员比例', TO_CLOB('
select
每年学校各学院管理人员比例.xybh as 学院编号,
每年学校各学院管理人员比例.xymc as 学院名称,
每年学校各学院管理人员比例.tjnf as 统计年份,
每年学校各学院管理人员比例.glrylb as 管理人员类别,
每年学校各学院管理人员比例.rybl as 人员比例
from ins_si_col_0255 每年学校各学院管理人员比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0255'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0256', 'ins_si_col_0256', '每年学校各学院班导师比例', TO_CLOB('
select
每年学校各学院班导师比例.xybh as 学院编号,
每年学校各学院班导师比例.xymc as 学院名称,
每年学校各学院班导师比例.tjnf as 统计年份,
每年学校各学院班导师比例.rybl as 人员比例
from ins_si_col_0256 每年学校各学院班导师比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0256'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0257', 'ins_si_col_0257', '每年正高级职称教职工比例', TO_CLOB('
select
每年正高级职称教职工比例.xybh as 学院编号,
每年正高级职称教职工比例.tjnf as 统计年份,
每年正高级职称教职工比例.xymc as 学院名称,
每年正高级职称教职工比例.bl as 比例
from ins_si_col_0257 每年正高级职称教职工比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0257'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0258', 'ins_si_col_0258', '每年各年龄教职工比例', TO_CLOB('
select
每年各年龄教职工比例.xybh as 学院编号,
每年各年龄教职工比例.nld as 年龄段,
每年各年龄教职工比例.tjnf as 统计年份,
每年各年龄教职工比例.xymc as 学院名称,
每年各年龄教职工比例.bl as 比例
from ins_si_col_0258 每年各年龄教职工比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0258'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0259', 'ins_si_col_0259', '每年各年龄专任教师比例', TO_CLOB('
select
每年各年龄专任教师比例.xybh as 学院编号,
每年各年龄专任教师比例.nld as 年龄段,
每年各年龄专任教师比例.tjnf as 统计年份,
每年各年龄专任教师比例.xymc as 学院名称,
每年各年龄专任教师比例.bl as 比例
from ins_si_col_0259 每年各年龄专任教师比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0259'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0260', 'ins_si_col_0260', '每年大创项目参与人数', TO_CLOB('
select
每年大创项目参与人数.xybh as 学院编号,
每年大创项目参与人数.tjnf as 统计年份,
每年大创项目参与人数.xymc as 学院名称,
每年大创项目参与人数.xscyrs as 学生参与人数,
每年大创项目参与人数.jscyrs as 教师参与人数
from ins_si_col_0260 每年大创项目参与人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0260'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0261', 'ins_si_col_0261', '每年学院各大创项目级别参与人数', TO_CLOB('
select
每年学院各大创项目级别参与人数.xybh as 学院编号,
每年学院各大创项目级别参与人数.xmjb as 项目级别,
每年学院各大创项目级别参与人数.xymc as 学院名称,
每年学院各大创项目级别参与人数.tjnf as 统计年份,
每年学院各大创项目级别参与人数.xscyrs as 学生参与人数,
每年学院各大创项目级别参与人数.jscyrs as 教师参与人数
from ins_si_col_0261 每年学院各大创项目级别参与人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0261'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0262', 'ins_si_col_0262', '每年学院各大创项目类型参与人数', TO_CLOB('
select
每年学院各大创项目类型参与人数.xybh as 学院编号,
每年学院各大创项目类型参与人数.xmlb as 项目类别,
每年学院各大创项目类型参与人数.xymc as 学院名称,
每年学院各大创项目类型参与人数.tjnf as 统计年份,
每年学院各大创项目类型参与人数.xscyrs as 学生参与人数,
每年学院各大创项目类型参与人数.jscyrs as 教师参与人数
from ins_si_col_0262 每年学院各大创项目类型参与人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0262'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0263', 'ins_si_col_0263', '每年学院双创项目参与人数', TO_CLOB('
select
每年学院双创项目参与人数.xybh as 学院编号,
每年学院双创项目参与人数.xymc as 学院名称,
每年学院双创项目参与人数.tjnf as 统计年份,
每年学院双创项目参与人数.xscyrs as 学生参与人数,
每年学院双创项目参与人数.jscyrs as 教师参与人数
from ins_si_col_0263 每年学院双创项目参与人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0263'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0264', 'ins_si_col_0264', '每年各学院一流课程数量', TO_CLOB('
select
每年各学院一流课程数量.xybh as 学院编号,
每年各学院一流课程数量.xymc as 学院名称,
每年各学院一流课程数量.tjnf as 统计年份,
每年各学院一流课程数量.kcsl as 课程数量
from ins_si_col_0264 每年各学院一流课程数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0264'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0265', 'ins_si_col_0265', '每年各学院出版教材数量', TO_CLOB('
select
每年各学院出版教材数量.xybh as 学院编号,
每年各学院出版教材数量.xymc as 学院名称,
每年各学院出版教材数量.tjnf as 统计年份,
每年各学院出版教材数量.jcsl as 教材数量
from ins_si_col_0265 每年各学院出版教材数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0265'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0267', 'ins_si_col_0267', '每年学校各学院班导师测评均分', TO_CLOB('
select
每年学校各学院班导师测评均分.xybh as 学院编号,
每年学校各学院班导师测评均分.xymc as 学院名称,
每年学校各学院班导师测评均分.xn as 学年,
每年学校各学院班导师测评均分.bdsjf as 班导师均分
from ins_si_col_0267 每年学校各学院班导师测评均分'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0267'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0268', 'ins_si_col_0268', '每年学校各专业班导师测评均分', TO_CLOB('
select
每年学校各专业班导师测评均分.xybh as 学院编号,
每年学校各专业班导师测评均分.zymc as 专业名称,
每年学校各专业班导师测评均分.xn as 学年,
每年学校各专业班导师测评均分.bdsjf as 班导师均分
from ins_si_col_0268 每年学校各专业班导师测评均分'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0268'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0271', 'ins_si_col_0271', '每年学院思政课专任教师与在校生比', TO_CLOB('
select
每年学院思政课专任教师与在校生比.xybh as 学院编号,
每年学院思政课专任教师与在校生比.xymc as 学院名称,
每年学院思政课专任教师与在校生比.tjnf as 统计年份,
每年学院思政课专任教师与在校生比.szssb as 思政生师比
from ins_si_col_0271 每年学院思政课专任教师与在校生比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0271'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0272', 'ins_si_col_0272', '每年各类学科竞赛各类赛道参与人数', TO_CLOB('
select
每年各类学科竞赛各类赛道参与人数.xybh as 学院编号,
每年各类学科竞赛各类赛道参与人数.tjnf as 统计年份,
每年各类学科竞赛各类赛道参与人数.jslx as 竞赛类型,
每年各类学科竞赛各类赛道参与人数.sd as 赛道,
每年各类学科竞赛各类赛道参与人数.xsrs as 学生人数,
每年各类学科竞赛各类赛道参与人数.xymc as 学院名称
from ins_si_col_0272 每年各类学科竞赛各类赛道参与人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0272'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0273', 'ins_si_col_0273', '每年各级别一流专业数量', TO_CLOB('
select
每年各级别一流专业数量.xybh as 学院编号,
每年各级别一流专业数量.jb as 级别,
每年各级别一流专业数量.tjnf as 统计年份,
每年各级别一流专业数量.xymc as 学院名称,
每年各级别一流专业数量.zysl as 专业数量
from ins_si_col_0273 每年各级别一流专业数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0273'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0274', 'ins_si_col_0274', '每年各学院实际录取人数', TO_CLOB('
select
每年各学院实际录取人数.xybh as 学院编号,
每年各学院实际录取人数.tjnf as 统计年份,
每年各学院实际录取人数.xymc as 学院名称,
每年各学院实际录取人数.sjlqs as 实际录取数
from ins_si_col_0274 每年各学院实际录取人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0274'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0275', 'ins_si_col_0275', '每年各学院录取人数前五名的中学分布', TO_CLOB('
select
每年各学院录取人数前五名的中学分布.xybh as 学院编号,
每年各学院录取人数前五名的中学分布.zxmc as 中学名称,
每年各学院录取人数前五名的中学分布.zxdm as 中学代码,
每年各学院录取人数前五名的中学分布.xymc as 学院名称,
每年各学院录取人数前五名的中学分布.tjnf as 统计年份,
每年各学院录取人数前五名的中学分布.lqrs as 录取人数
from ins_si_col_0275 每年各学院录取人数前五名的中学分布'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0275'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_col_0276', 'ins_si_col_0276', '每年招生中学录取人数', TO_CLOB('
select
每年招生中学录取人数.xybh as 学院编号,
每年招生中学录取人数.zxpm as 中学排名,
每年招生中学录取人数.xymc as 学院名称,
每年招生中学录取人数.lqrs as 录取人数,
每年招生中学录取人数.tjnf as 统计年份
from ins_si_col_0276 每年招生中学录取人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_col_0276'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_cou_0001', 'ins_si_cou_0001', '每年实验课程所用实验场所数量', TO_CLOB('
select
每年实验课程所用实验场所数量.kcdm as 课程代码,
每年实验课程所用实验场所数量.kcmc as 课程名称,
每年实验课程所用实验场所数量.tjnf as 统计年份,
每年实验课程所用实验场所数量.sycssl as 实验场所数量
from ins_si_cou_0001 每年实验课程所用实验场所数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_cou_0001'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_cou_0002', 'ins_si_cou_0002', '每年实验设备种类数量', TO_CLOB('
select
每年实验设备种类数量.kcdm as 课程代码,
每年实验设备种类数量.sycsmc as 实验场所名称,
每年实验设备种类数量.sycsdm as 实验场所代码,
每年实验设备种类数量.kcmc as 课程名称,
每年实验设备种类数量.tjnf as 统计年份,
每年实验设备种类数量.sbzlsl as 设备种类数量
from ins_si_cou_0002 每年实验设备种类数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_cou_0002'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_cou_0003', 'ins_si_cou_0003', '每年实验设备数量', TO_CLOB('
select
每年实验设备数量.kcdm as 课程代码,
每年实验设备数量.sycsmc as 实验场所名称,
每年实验设备数量.sycsdm as 实验场所代码,
每年实验设备数量.sbmc as 设备名称,
每年实验设备数量.tjnf as 统计年份,
每年实验设备数量.sl as 数量
from ins_si_cou_0003 每年实验设备数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_cou_0003'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_cou_0004', 'ins_si_cou_0004', '每年学校实验设备数量', TO_CLOB('
select
每年学校实验设备数量.kcdm as 课程代码,
每年学校实验设备数量.xymc as 学院名称,
每年学校实验设备数量.kcmc as 课程名称,
每年学校实验设备数量.sycsmc as 实验场所名称,
每年学校实验设备数量.sbmc as 设备名称,
每年学校实验设备数量.tjnf as 统计年份,
每年学校实验设备数量.sbsl as 设备数量
from ins_si_cou_0004 每年学校实验设备数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_cou_0004'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_cou_0005', 'ins_si_cou_0005', '课程生均核心设备占比', TO_CLOB('
select
课程生均核心设备占比.kcdm as 课程代码,
课程生均核心设备占比.yxmc as 院系名称,
课程生均核心设备占比.kcmc as 课程名称,
课程生均核心设备占比.csmc as 场所名称,
课程生均核心设备占比.tjnf as 统计年份,
课程生均核心设备占比.sjzb as 生均占比
from ins_si_cou_0005 课程生均核心设备占比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_cou_0005'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_cou_0006', 'ins_si_cou_0006', '每年体育课程测评均分', TO_CLOB('
select
每年体育课程测评均分.kcdm as 课程代码,
每年体育课程测评均分.kcm as 课程名,
每年体育课程测评均分.cpjf as 测评均分,
每年体育课程测评均分.xn as 学年
from ins_si_cou_0006 每年体育课程测评均分'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_cou_0006'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_maj_0001', 'ins_si_maj_0001', '每年专业生师比', TO_CLOB('
select
每年专业生师比.zydm as 专业代码,
每年专业生师比.zymc as 专业名称,
每年专业生师比.tjnf as 统计年份,
每年专业生师比.ssb as 生师比,
每年专业生师比.xymc as 学院名称,
每年专业生师比.xydm as 学院代码
from ins_si_maj_0001 每年专业生师比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_maj_0001'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_maj_0003', 'ins_si_maj_0003', '全校各专业教师数量', TO_CLOB('
select
全校各专业教师数量.zydm as 专业代码,
全校各专业教师数量.zymc as 专业名称,
全校各专业教师数量.tjnf as 统计年份,
全校各专业教师数量.rjzymc as 任教专业名称,
全校各专业教师数量.jssl as 教师数量
from ins_si_maj_0003 全校各专业教师数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_maj_0003'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_maj_0004', 'ins_si_maj_0004', '全校各年级专业学生数量', TO_CLOB('
select
全校各年级专业学生数量.zydm as 专业代码,
全校各年级专业学生数量.zymc as 专业名称,
全校各年级专业学生数量.nj as 年级,
全校各年级专业学生数量.xssl as 学生数量,
全校各年级专业学生数量.tjnf as 统计年份
from ins_si_maj_0004 全校各年级专业学生数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_maj_0004'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_maj_0005', 'ins_si_maj_0005', '学年内学科门类学生流动净值', TO_CLOB('
select
学年内学科门类学生流动净值.zydm as 专业代码,
学年内学科门类学生流动净值.tjnf as 统计年份,
学年内学科门类学生流动净值.syxwml as 授予学位门类,
学年内学科门类学生流动净值.xsrs as 学生人数,
学年内学科门类学生流动净值.zcxwml as 转出学位门类,
学年内学科门类学生流动净值.zrxwml as 转入学位门类,
学年内学科门类学生流动净值.rs as 人数
from ins_si_maj_0005 学年内学科门类学生流动净值'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_maj_0005'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_maj_0006', 'ins_si_maj_0006', '各专业学生毕业人数', TO_CLOB('
select
各专业学生毕业人数.zydm as 专业代码,
各专业学生毕业人数.zymc as 专业名称,
各专业学生毕业人数.bynf as 毕业年份,
各专业学生毕业人数.zsbyjl as 终审毕业结论,
各专业学生毕业人数.bysrs as 毕业生人数
from ins_si_maj_0006 各专业学生毕业人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_maj_0006'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_maj_0007', 'ins_si_maj_0007', '各专业学生毕业率', TO_CLOB('
select
各专业学生毕业率.zydm as 专业代码,
各专业学生毕业率.zymc as 专业名称,
各专业学生毕业率.bynf as 毕业年份,
各专业学生毕业率.zsbyjl as 终审毕业结论,
各专业学生毕业率.byl as 毕业率
from ins_si_maj_0007 各专业学生毕业率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_maj_0007'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_maj_0008', 'ins_si_maj_0008', '每年报到人数', TO_CLOB('
select
每年报到人数.zydm as 专业代码,
每年报到人数.zymc as 专业名称,
每年报到人数.dlmc as 大类名称,
每年报到人数.dldm as 大类代码,
每年报到人数.tjnf as 统计年份,
每年报到人数.bdrs as 报到人数
from ins_si_maj_0008 每年报到人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_maj_0008'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_maj_0009', 'ins_si_maj_0009', '每年专业（大类）报到率', TO_CLOB('
select
每年专业大类报到率.zydm as 专业代码,
每年专业大类报到率.zymc as 专业名称,
每年专业大类报到率.dlmc as 大类名称,
每年专业大类报到率.dldm as 大类代码,
每年专业大类报到率.tjnf as 统计年份,
每年专业大类报到率.bdl as 报到率
from ins_si_maj_0009 每年专业大类报到率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_maj_0009'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_maj_0010', 'ins_si_maj_0010', '每年专业（大类）第一志愿录取率', TO_CLOB('
select
每年专业大类第一志愿录取率.zydm as 专业代码,
每年专业大类第一志愿录取率.zymc as 专业名称,
每年专业大类第一志愿录取率.dlmc as 大类名称,
每年专业大类第一志愿录取率.dldm as 大类代码,
每年专业大类第一志愿录取率.tjnf as 统计年份,
每年专业大类第一志愿录取率.dyzylql as 第一志愿录取率
from ins_si_maj_0010 每年专业大类第一志愿录取率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_maj_0010'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_maj_0011', 'ins_si_maj_0011', '每年学校各专业课程测评均分', TO_CLOB('
select
每年学校各专业课程测评均分.zydm as 专业代码,
每年学校各专业课程测评均分.zymc as 专业名称,
每年学校各专业课程测评均分.xn as 学年,
每年学校各专业课程测评均分.kcfl as 课程分类,
每年学校各专业课程测评均分.cpjf as 测评均分,
每年学校各专业课程测评均分.xymc as 学院名称,
每年学校各专业课程测评均分.xydm as 学院代码
from ins_si_maj_0011 每年学校各专业课程测评均分'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_maj_0011'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_maj_0012', 'ins_si_maj_0012', '每年各专业关注课程数量', TO_CLOB('
select
每年各专业关注课程数量.zydm as 专业代码,
每年各专业关注课程数量.tjnf as 统计年份,
每年各专业关注课程数量.zymc as 专业名称,
每年各专业关注课程数量.kcsl as 课程数量
from ins_si_maj_0012 每年各专业关注课程数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_maj_0012'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_maj_0013', 'ins_si_maj_0013', '每年各专业听课均分', TO_CLOB('
select
每年各专业听课均分.zydm as 专业代码,
每年各专业听课均分.xn as 学年,
每年各专业听课均分.zymc as 专业名称,
每年各专业听课均分.tkrysf as 听课人员身份,
每年各专业听课均分.tkjf as 听课均分,
每年各专业听课均分.xymc as 学院名称,
每年各专业听课均分.xydm as 学院代码
from ins_si_maj_0013 每年各专业听课均分'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_maj_0013'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_maj_0014', 'ins_si_maj_0014', '各专业学生毕业情况', TO_CLOB('
select
各专业学生毕业情况.zydm as 专业代码,
各专业学生毕业情况.zymc as 专业名称,
各专业学生毕业情况.bynf as 毕业年份,
各专业学生毕业情况.byrs as 毕业人数,
各专业学生毕业情况.wasbyrs as 未按时毕业人数,
各专业学生毕业情况.byl as 毕业率
from ins_si_maj_0014 各专业学生毕业情况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_maj_0014'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_maj_0015', 'ins_si_maj_0015', '每年专业思政课专任教师与在校生比', TO_CLOB('
select
每年专业思政课专任教师与在校生比.zydm as 专业代码,
每年专业思政课专任教师与在校生比.zymc as 专业名称,
每年专业思政课专任教师与在校生比.xymc as 学院名称,
每年专业思政课专任教师与在校生比.xydm as 学院代码,
每年专业思政课专任教师与在校生比.tjnf as 统计年份,
每年专业思政课专任教师与在校生比.szssb as 思政生师比
from ins_si_maj_0015 每年专业思政课专任教师与在校生比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_maj_0015'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_maj_0016', 'ins_si_maj_0016', '每年各专业教授主讲本科课程人均学时数', TO_CLOB('
select
每年各专业教授主讲本科课程人均学时数.zydm as 专业代码,
每年各专业教授主讲本科课程人均学时数.xn as 学年,
每年各专业教授主讲本科课程人均学时数.zymc as 专业名称,
每年各专业教授主讲本科课程人均学时数.xymc as 学院名称,
每年各专业教授主讲本科课程人均学时数.xydm as 学院代码,
每年各专业教授主讲本科课程人均学时数.jsrjskxs as 教授人均授课学时
from ins_si_maj_0016 每年各专业教授主讲本科课程人均学时数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_maj_0016'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_maj_0017', 'ins_si_maj_0017', '每年学校各专业班导师均分', TO_CLOB('
select
每年学校各专业班导师均分.zydm as 专业代码,
每年学校各专业班导师均分.zymc as 专业名称,
每年学校各专业班导师均分.xn as 学年,
每年学校各专业班导师均分.xymc as 学院名称,
每年学校各专业班导师均分.xydm as 学院代码,
每年学校各专业班导师均分.bdsjf as 班导师均分
from ins_si_maj_0017 每年学校各专业班导师均分'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_maj_0017'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_maj_0018', 'ins_si_maj_0018', '每年学校新设专业', TO_CLOB('
select
每年学校新设专业.zydm as 专业代码,
每年学校新设专业.tjnf as 统计年份,
每年学校新设专业.zymc as 专业名称,
每年学校新设专业.bzyskjsrs as 本专业授课教师人数,
每年学校新设专业.bksrs as 本科生人数,
每年学校新设专业.xsyskjsb as 学生与授课教师比,
每年学校新设专业.yjbysrs as 应届毕业生人数,
每年学校新设专业.xsbyl as 学生毕业率,
每年学校新设专业.qxlsl as 去向落实率
from ins_si_maj_0018 每年学校新设专业'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_maj_0018'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0217', 'ins_si_sch_0217', '每年思政课专任教师数量', TO_CLOB('
select
每年思政课专任教师数量.xxbsm as 学校标识码,
每年思政课专任教师数量.tjnf as 统计年份,
每年思政课专任教师数量.rzlx as 任职类型,
每年思政课专任教师数量.rzzt as 任职状态,
每年思政课专任教师数量.szzrjss as 思政专任教师数
from ins_si_sch_0217 每年思政课专任教师数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0217'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0324', 'ins_si_sch_0324', '每年全体教职工人数', TO_CLOB('
select
每年全体教职工人数.xxbsm as 学校标识码,
每年全体教职工人数.tjnf as 统计年份,
每年全体教职工人数.nld as 年龄段,
每年全体教职工人数.jzgrs as 教职工人数
from ins_si_sch_0324 每年全体教职工人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0324'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0325', 'ins_si_sch_0325', '每年专任教师人数', TO_CLOB('
select
每年专任教师人数.xxbsm as 学校标识码,
每年专任教师人数.tjnf as 统计年份,
每年专任教师人数.zrjss as 专任教师数
from ins_si_sch_0325 每年专任教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0325'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0326', 'ins_si_sch_0326', '每年专任教师比例', TO_CLOB('
select
每年专任教师比例.xxbsm as 学校标识码,
每年专任教师比例.tjnf as 统计年份,
每年专任教师比例.zrjsbl as 专任教师比例
from ins_si_sch_0326 每年专任教师比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0326'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0327', 'ins_si_sch_0327', '每年授课教师人数', TO_CLOB('
select
每年授课教师人数.xxbsm as 学校标识码,
每年授课教师人数.tjnf as 统计年份,
每年授课教师人数.skjss as 授课教师数
from ins_si_sch_0327 每年授课教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0327'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0328', 'ins_si_sch_0328', '每年高级职称专任教师比例', TO_CLOB('
select
每年高级职称专任教师比例.xxbsm as 学校标识码,
每年高级职称专任教师比例.tjnf as 统计年份,
每年高级职称专任教师比例.zrjss as 专任教师数,
每年高级职称专任教师比例.zrjsbl as 专任教师比例
from ins_si_sch_0328 每年高级职称专任教师比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0328'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0329', 'ins_si_sch_0329', '每年专任教师中高层次人才比例', TO_CLOB('
select
每年专任教师中高层次人才比例.xxbsm as 学校标识码,
每年专任教师中高层次人才比例.tjnf as 统计年份,
每年专任教师中高层次人才比例.gccrcbl as 高层次人才比例
from ins_si_sch_0329 每年专任教师中高层次人才比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0329'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0330', 'ins_si_sch_0330', '每年专任教师与实验技术队伍比', TO_CLOB('
select
每年专任教师与实验技术队伍比.xxbsm as 学校标识码,
每年专任教师与实验技术队伍比.tjnf as 统计年份,
每年专任教师与实验技术队伍比.dwbl as 队伍比例,
每年专任教师与实验技术队伍比.px as 排序
from ins_si_sch_0330 每年专任教师与实验技术队伍比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0330'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0331', 'ins_si_sch_0331', '每年实验技术队伍最高学位博、硕、本之比', TO_CLOB('
select
每年实验技术队伍最高学位博硕本之比.xxbsm as 学校标识码,
每年实验技术队伍最高学位博硕本之比.tjnf as 统计年份,
每年实验技术队伍最高学位博硕本之比.xwbl as 学位比例,
每年实验技术队伍最高学位博硕本之比.px as 排序,
每年实验技术队伍最高学位博硕本之比.zgxw as 最高学位
from ins_si_sch_0331 每年实验技术队伍最高学位博硕本之比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0331'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0332', 'ins_si_sch_0332', '每年外籍专任教师数量', TO_CLOB('
select
每年外籍专任教师数量.xxbsm as 学校标识码,
每年外籍专任教师数量.tjnf as 统计年份,
每年外籍专任教师数量.wjzrjss as 外籍专任教师数
from ins_si_sch_0332 每年外籍专任教师数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0332'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0333', 'ins_si_sch_0333', '每年实验技术人员中专任教师占比', TO_CLOB('
select
每年实验技术人员中专任教师占比.xxbsm as 学校标识码,
每年实验技术人员中专任教师占比.tjnf as 统计年份,
每年实验技术人员中专任教师占比.zrjsbl as 专任教师比例
from ins_si_sch_0333 每年实验技术人员中专任教师占比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0333'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0334', 'ins_si_sch_0334', '每年具有一年以上国(境)外经历专任教师比例', TO_CLOB('
select
每年具有一年以上国境外经历专任教师比例.xxbsm as 学校标识码,
每年具有一年以上国境外经历专任教师比例.tjnf as 统计年份,
每年具有一年以上国境外经历专任教师比例.zrjsbl as 专任教师比例
from ins_si_sch_0334 每年具有一年以上国境外经历专任教师比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0334'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0335', 'ins_si_sch_0335', '每年具有博士学位专任教师占比', TO_CLOB('
select
每年具有博士学位专任教师占比.xxbsm as 学校标识码,
每年具有博士学位专任教师占比.zgxw as 最高学位,
每年具有博士学位专任教师占比.tjnf as 统计年份,
每年具有博士学位专任教师占比.zrjsbl as 专任教师比例
from ins_si_sch_0335 每年具有博士学位专任教师占比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0335'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0336', 'ins_si_sch_0336', '每年教授主讲本科课程人均学时数', TO_CLOB('
select
每年教授主讲本科课程人均学时数.xxbsm as 学校标识码,
每年教授主讲本科课程人均学时数.jsrjskxs as 教授人均授课学时,
每年教授主讲本科课程人均学时数.xn as 学年
from ins_si_sch_0336 每年教授主讲本科课程人均学时数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0336'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0337', 'ins_si_sch_0337', '学校主讲本科课程教授占教授总数的比例', TO_CLOB('
select
学校主讲本科课程教授占教授总数的比例.xxbsm as 学校标识码,
学校主讲本科课程教授占教授总数的比例.skjsb as 授课教授比,
学校主讲本科课程教授占教授总数的比例.xn as 学年
from ins_si_sch_0337 学校主讲本科课程教授占教授总数的比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0337'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0338', 'ins_si_sch_0338', '每年高层次人才授课比例', TO_CLOB('
select
每年高层次人才授课比例.xxbsm as 学校标识码,
每年高层次人才授课比例.skgccrcb as 授课高层次人才比,
每年高层次人才授课比例.xn as 学年
from ins_si_sch_0338 每年高层次人才授课比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0338'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0339', 'ins_si_sch_0339', '每年具有企业经验的专任教师数量', TO_CLOB('
select
每年具有企业经验的专任教师数量.xxbsm as 学校标识码,
每年具有企业经验的专任教师数量.tjnf as 统计年份,
每年具有企业经验的专任教师数量.zrjss as 专任教师数
from ins_si_sch_0339 每年具有企业经验的专任教师数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0339'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0340', 'ins_si_sch_0340', '每年具有两年以上企业经验的专任教师数量', TO_CLOB('
select
每年具有两年以上企业经验的专任教师数量.xxbsm as 学校标识码,
每年具有两年以上企业经验的专任教师数量.tjnf as 统计年份,
每年具有两年以上企业经验的专任教师数量.zrjss as 专任教师数
from ins_si_sch_0340 每年具有两年以上企业经验的专任教师数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0340'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0341', 'ins_si_sch_0341', '每年学校派去企业实践锻炼累计3个月及以上专任教师数量', TO_CLOB('
select
每年学校派去企业实践锻炼累计3个月及以上专任教师数量.xxbsm as 学校标识码,
每年学校派去企业实践锻炼累计3个月及以上专任教师数量.tjnf as 统计年份,
每年学校派去企业实践锻炼累计3个月及以上专任教师数量.zrjss as 专任教师数
from ins_si_sch_0341 每年学校派去企业实践锻炼累计3个月及以上专任教师数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0341'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0342', 'ins_si_sch_0342', '每年具有企业经验的专任教师比例', TO_CLOB('
select
每年具有企业经验的专任教师比例.xxbsm as 学校标识码,
每年具有企业经验的专任教师比例.tjnf as 统计年份,
每年具有企业经验的专任教师比例.zrjsbl as 专任教师比例
from ins_si_sch_0342 每年具有企业经验的专任教师比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0342'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0343', 'ins_si_sch_0343', '每年开展活动数量', TO_CLOB('
select
每年开展活动数量.xxbsm as 学校标识码,
每年开展活动数量.tjnf as 统计年份,
每年开展活动数量.hdsl as 活动数量
from ins_si_sch_0343 每年开展活动数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0343'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0344', 'ins_si_sch_0344', '每年开展教学竞赛数量', TO_CLOB('
select
每年开展教学竞赛数量.xxbsm as 学校标识码,
每年开展教学竞赛数量.tjnf as 统计年份,
每年开展教学竞赛数量.jssl as 竞赛数量
from ins_si_sch_0344 每年开展教学竞赛数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0344'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0345', 'ins_si_sch_0345', '获得荣誉称号教师人数', TO_CLOB('
select
获得荣誉称号教师人数.xxbsm as 学校标识码,
获得荣誉称号教师人数.rych as 荣誉称号,
获得荣誉称号教师人数.tjnf as 统计年份,
获得荣誉称号教师人数.jzgrs as 教职工人数
from ins_si_sch_0345 获得荣誉称号教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0345'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0346', 'ins_si_sch_0346', '全校生均核心设备占比', TO_CLOB('
select
全校生均核心设备占比.xxbsm as 学校标识码,
全校生均核心设备占比.tjnf as 统计年份,
全校生均核心设备占比.sjzb as 生均占比
from ins_si_sch_0346 全校生均核心设备占比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0346'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0347', 'ins_si_sch_0347', '创新性实验项目数量', TO_CLOB('
select
创新性实验项目数量.xxbsm as 学校标识码,
创新性实验项目数量.tjnf as 统计年份,
创新性实验项目数量.xmsl as 项目数量
from ins_si_sch_0347 创新性实验项目数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0347'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0348', 'ins_si_sch_0348', '实验课上课人数', TO_CLOB('
select
实验课上课人数.xxbsm as 学校标识码,
实验课上课人数.tjnf as 统计年份,
实验课上课人数.kcxz as 课程性质,
实验课上课人数.skrs as 上课人数
from ins_si_sch_0348 实验课上课人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0348'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0349', 'ins_si_sch_0349', '虚拟仿真实验项目', TO_CLOB('
select
虚拟仿真实验项目.xxbsm as 学校标识码,
虚拟仿真实验项目.tjnf as 统计年份,
虚拟仿真实验项目.xmsl as 项目数量
from ins_si_sch_0349 虚拟仿真实验项目'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0349'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0350', 'ins_si_sch_0350', '虚拟仿真项目浏览数', TO_CLOB('
select
虚拟仿真项目浏览数.xxbsm as 学校标识码,
虚拟仿真项目浏览数.tjnf as 统计年份,
虚拟仿真项目浏览数.xmllsl as 项目浏览数量
from ins_si_sch_0350 虚拟仿真项目浏览数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0350'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0351', 'ins_si_sch_0351', '虚拟实验项目参与人数对比', TO_CLOB('
select
虚拟实验项目参与人数对比.xxbsm as 学校标识码,
虚拟实验项目参与人数对比.tjnf as 统计年份,
虚拟实验项目参与人数对比.cyrs as 参与人数
from ins_si_sch_0351 虚拟实验项目参与人数对比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0351'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0352', 'ins_si_sch_0352', '每年实验技术人员数量', TO_CLOB('
select
每年实验技术人员数量.xxbsm as 学校标识码,
每年实验技术人员数量.rxsj as 入校时间,
每年实验技术人员数量.tjnf as 统计年份,
每年实验技术人员数量.syjsrysl as 实验技术人员数量
from ins_si_sch_0352 每年实验技术人员数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0352'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0353', 'ins_si_sch_0353', '每年参与教学竞赛教师人数', TO_CLOB('
select
每年参与教学竞赛教师人数.xxbsm as 学校标识码,
每年参与教学竞赛教师人数.xmlx as 项目类型,
每年参与教学竞赛教师人数.tjnf as 统计年份,
每年参与教学竞赛教师人数.jzgrs as 教职工人数
from ins_si_sch_0353 每年参与教学竞赛教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0353'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0374', 'ins_si_sch_0374', '每年生师比', TO_CLOB('
select
每年生师比.xxbsm as 学校标识码,
每年生师比.sfzrjs as 是否专任教师,
每年生师比.tjnf as 统计年份,
每年生师比.ssb as 生师比
from ins_si_sch_0374 每年生师比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0374'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0375', 'ins_si_sch_0375', '每年专职辅导员与在校生比', TO_CLOB('
select
每年专职辅导员与在校生比.xxbsm as 学校标识码,
每年专职辅导员与在校生比.tjnf as 统计年份,
每年专职辅导员与在校生比.zrfdyssb as 专任辅导员生师比
from ins_si_sch_0375 每年专职辅导员与在校生比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0375'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0376', 'ins_si_sch_0376', '每年班导师与在校生比', TO_CLOB('
select
每年班导师与在校生比.xxbsm as 学校标识码,
每年班导师与在校生比.tjnf as 统计年份,
每年班导师与在校生比.bdsssb as 班导师生师比
from ins_si_sch_0376 每年班导师与在校生比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0376'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0377', 'ins_si_sch_0377', '每年思政课专任教师与在校生比', TO_CLOB('
select
每年思政课专任教师与在校生比.xxbsm as 学校标识码,
每年思政课专任教师与在校生比.tjnf as 统计年份,
每年思政课专任教师与在校生比.szssb as 思政生师比
from ins_si_sch_0377 每年思政课专任教师与在校生比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0377'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0378', 'ins_si_sch_0378', '学校专职党务工作人员和思想政治工作人员总数与全校师生人数比', TO_CLOB('
select
学校专职党务工作人员和思想政治工作人员总数与全校师生人数比.xxbsm as 学校标识码,
学校专职党务工作人员和思想政治工作人员总数与全校师生人数比.tjnf as 统计年份,
学校专职党务工作人员和思想政治工作人员总数与全校师生人数比.zzdwhszryssb as 专职党务和思政人员生师比
from ins_si_sch_0378 学校专职党务工作人员和思想政治工作人员总数与全校师生人数比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0378'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0379', 'ins_si_sch_0379', '学校专职就业指导教师和专职就业工作人员与应届毕业生比', TO_CLOB('
select
学校专职就业指导教师和专职就业工作人员与应届毕业生比.xxbsm as 学校标识码,
学校专职就业指导教师和专职就业工作人员与应届毕业生比.tjnf as 统计年份,
学校专职就业指导教师和专职就业工作人员与应届毕业生比.zzjyzdhgzryssb as 专职就业指导和工作人员生师比
from ins_si_sch_0379 学校专职就业指导教师和专职就业工作人员与应届毕业生比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0379'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0380', 'ins_si_sch_0380', '学校专职从事心理健康教育教师与在校生比', TO_CLOB('
select
学校专职从事心理健康教育教师与在校生比.xxbsm as 学校标识码,
学校专职从事心理健康教育教师与在校生比.tjnf as 统计年份,
学校专职从事心理健康教育教师与在校生比.zzcsxljkjyjsssb as 专职从事心理健康教育教师生师比
from ins_si_sch_0380 学校专职从事心理健康教育教师与在校生比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0380'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0381', 'ins_si_sch_0381', '获得不同类型荣誉称号教师人数', TO_CLOB('
select
获得不同类型荣誉称号教师人数.xxbsm as 学校标识码,
获得不同类型荣誉称号教师人数.rych as 荣誉称号,
获得不同类型荣誉称号教师人数.tjnf as 统计年份,
获得不同类型荣誉称号教师人数.jzgrs as 教职工人数
from ins_si_sch_0381 获得不同类型荣誉称号教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0381'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0382', 'ins_si_sch_0382', '每年学校教学行政用房面积', TO_CLOB('
select
每年学校教学行政用房面积.xxbsm as 学校标识码,
每年学校教学行政用房面积.tjnf as 统计年份,
每年学校教学行政用房面积.xzyfmj as 行政用房面积
from ins_si_sch_0382 每年学校教学行政用房面积'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0382'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0383', 'ins_si_sch_0383', '每年教室数量', TO_CLOB('
select
每年教室数量.xxbsm as 学校标识码,
每年教室数量.jslx as 教室类型,
每年教室数量.tjnf as 统计年份,
每年教室数量.jssl as 教室数量
from ins_si_sch_0383 每年教室数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0383'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0384', 'ins_si_sch_0384', '每年学校实验场所教学科研仪器数量', TO_CLOB('
select
每年学校实验场所教学科研仪器数量.xxbsm as 学校标识码,
每年学校实验场所教学科研仪器数量.sycs as 实验场所,
每年学校实验场所教学科研仪器数量.tjnf as 统计年份,
每年学校实验场所教学科研仪器数量.kyyqs as 科研仪器数
from ins_si_sch_0384 每年学校实验场所教学科研仪器数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0384'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0385', 'ins_si_sch_0385', '每年学校生均教学科研仪器设备值', TO_CLOB('
select
每年学校生均教学科研仪器设备值.xxbsm as 学校标识码,
每年学校生均教学科研仪器设备值.tjnf as 统计年份,
每年学校生均教学科研仪器设备值.sjsbzwy as 生均设备值万元
from ins_si_sch_0385 每年学校生均教学科研仪器设备值'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0385'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0386', 'ins_si_sch_0386', '每年学校年新增教学科研仪器投入比', TO_CLOB('
select
每年学校年新增教学科研仪器投入比.xxbsm as 学校标识码,
每年学校年新增教学科研仪器投入比.tjnf as 统计年份,
每年学校年新增教学科研仪器投入比.kyyqtrb as 科研仪器投入比
from ins_si_sch_0386 每年学校年新增教学科研仪器投入比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0386'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0387', 'ins_si_sch_0387', '每年学校教学、科研仪器设备资产及新增', TO_CLOB('
select
每年学校教学科研仪器设备资产及新增.xxbsm as 学校标识码,
每年学校教学科研仪器设备资产及新增.tjnf as 统计年份,
每年学校教学科研仪器设备资产及新增.zczzwy as 资产总值万元,
每年学校教学科研仪器设备资产及新增.xzwy as 新增万元
from ins_si_sch_0387 每年学校教学科研仪器设备资产及新增'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0387'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0388', 'ins_si_sch_0388', '每年学校实验室及实习实训场所设备数量', TO_CLOB('
select
每年学校实验室及实习实训场所设备数量.xxbsm as 学校标识码,
每年学校实验室及实习实训场所设备数量.sycsxz as 实验场所性质,
每年学校实验室及实习实训场所设备数量.tjnf as 统计年份,
每年学校实验室及实习实训场所设备数量.sbs as 设备数
from ins_si_sch_0388 每年学校实验室及实习实训场所设备数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0388'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0389', 'ins_si_sch_0389', '每年学校实验室及实习实训场所设备概况', TO_CLOB('
select
每年学校实验室及实习实训场所设备概况.xxbsm as 学校标识码,
每年学校实验室及实习实训场所设备概况.sycsxz as 实验场所性质,
每年学校实验室及实习实训场所设备概况.tjnf as 统计年份,
每年学校实验室及实习实训场所设备概况.cssl as 场所数量,
每年学校实验室及实习实训场所设备概况.csmj as 场所面积,
每年学校实验室及实习实训场所设备概况.cssbtts as 场所设备台套数,
每年学校实验室及实习实训场所设备概况.cssbzwy as 场所设备值万元,
每年学校实验室及实习实训场所设备概况.cskssykcs as 场所开设实验课程数
from ins_si_sch_0389 每年学校实验室及实习实训场所设备概况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0389'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0390', 'ins_si_sch_0390', '每年学校图书流通量', TO_CLOB('
select
每年学校图书流通量.xxbsm as 学校标识码,
每年学校图书流通量.tjnf as 统计年份,
每年学校图书流通量.tsltl as 图书流通量
from ins_si_sch_0390 每年学校图书流通量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0390'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0391', 'ins_si_sch_0391', '每年学校不同类型图书变化', TO_CLOB('
select
每年学校不同类型图书变化.xxbsm as 学校标识码,
每年学校不同类型图书变化.tslx as 图书类型,
每年学校不同类型图书变化.tjnf as 统计年份,
每年学校不同类型图书变化.bdsl as 变动数量,
每年学校不同类型图书变化.bdbl as 变动比例
from ins_si_sch_0391 每年学校不同类型图书变化'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0391'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0392', 'ins_si_sch_0392', '每年不同规模课程门次数', TO_CLOB('
select
每年不同规模课程门次数.xxbsm as 学校标识码,
每年不同规模课程门次数.gm as 规模,
每年不同规模课程门次数.kclb as 课程类别,
每年不同规模课程门次数.xnxq as 学年学期,
每年不同规模课程门次数.kkmcs as 开课门次数,
每年不同规模课程门次数.gmlx as 规模类型,
每年不同规模课程门次数.px as 排序
from ins_si_sch_0392 每年不同规模课程门次数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0392'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0393', 'ins_si_sch_0393', '每年开设课程规模', TO_CLOB('
select
每年开设课程规模.xxbsm as 学校标识码,
每年开设课程规模.kclb as 课程类别,
每年开设课程规模.pjbgm as 平均班规模,
每年开设课程规模.xn as 学年
from ins_si_sch_0393 每年开设课程规模'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0393'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0394', 'ins_si_sch_0394', '每年学校在线开放课程门数', TO_CLOB('
select
每年学校在线开放课程门数.xxbsm as 学校标识码,
每年学校在线开放课程门数.zxkfkcms as 在线开放课程门数,
每年学校在线开放课程门数.xmlx as 项目类型,
每年学校在线开放课程门数.xn as 学年
from ins_si_sch_0394 每年学校在线开放课程门数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0394'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0395', 'ins_si_sch_0395', '课程门数', TO_CLOB('
select
课程门数.xxbsm as 学校标识码,
课程门数.tjnf as 统计年份,
课程门数.kcsl as 课程数量
from ins_si_sch_0395 课程门数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0395'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0396', 'ins_si_sch_0396', '每年优秀课程', TO_CLOB('
select
每年优秀课程.xxbsm as 学校标识码,
每年优秀课程.tjnf as 统计年份,
每年优秀课程.kcjb as 课程级别,
每年优秀课程.kcbq as 课程标签,
每年优秀课程.kcsl as 课程数量
from ins_si_sch_0396 每年优秀课程'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0396'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0397', 'ins_si_sch_0397', '每年思政课及课程思政', TO_CLOB('
select
每年思政课及课程思政.xxbsm as 学校标识码,
每年思政课及课程思政.tjnf as 统计年份,
每年思政课及课程思政.kcjb as 课程级别,
每年思政课及课程思政.kcbq as 课程标签,
每年思政课及课程思政.szkcsl as 思政课程数量
from ins_si_sch_0397 每年思政课及课程思政'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0397'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0398', 'ins_si_sch_0398', '每年全英文课程数量', TO_CLOB('
select
每年全英文课程数量.xxbsm as 学校标识码,
每年全英文课程数量.tjnf as 统计年份,
每年全英文课程数量.kcsl as 课程数量
from ins_si_sch_0398 每年全英文课程数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0398'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0399', 'ins_si_sch_0399', '一流课程', TO_CLOB('
select
一流课程.xxbsm as 学校标识码,
一流课程.tjnf as 统计年份,
一流课程.kcjb as 课程级别,
一流课程.jxfs as 教学方式,
一流课程.kcsl as 课程数量
from ins_si_sch_0399 一流课程'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0399'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0400', 'ins_si_sch_0400', '思政示范课程', TO_CLOB('
select
思政示范课程.xxbsm as 学校标识码,
思政示范课程.tjnf as 统计年份,
思政示范课程.kcsl as 课程数量
from ins_si_sch_0400 思政示范课程'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0400'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0401', 'ins_si_sch_0401', '名师获奖', TO_CLOB('
select
名师获奖.xxbsm as 学校标识码,
名师获奖.tjnf as 统计年份,
名师获奖.hjsl as 获奖数量
from ins_si_sch_0401 名师获奖'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0401'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0402', 'ins_si_sch_0402', '每年优秀教材数量', TO_CLOB('
select
每年优秀教材数量.xxbsm as 学校标识码,
每年优秀教材数量.tjnf as 统计年份,
每年优秀教材数量.jcsl as 教材数量
from ins_si_sch_0402 每年优秀教材数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0402'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0403', 'ins_si_sch_0403', '教师获奖数量', TO_CLOB('
select
教师获奖数量.xxbsm as 学校标识码,
教师获奖数量.tjnf as 统计年份,
教师获奖数量.hjsl as 获奖数量
from ins_si_sch_0403 教师获奖数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0403'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0404', 'ins_si_sch_0404', '虚拟仿真实验教学中心', TO_CLOB('
select
虚拟仿真实验教学中心.xxbsm as 学校标识码,
虚拟仿真实验教学中心.tjnf as 统计年份,
虚拟仿真实验教学中心.sl as 数量
from ins_si_sch_0404 虚拟仿真实验教学中心'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0404'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0405', 'ins_si_sch_0405', '实验教学示范中心', TO_CLOB('
select
实验教学示范中心.xxbsm as 学校标识码,
实验教学示范中心.tjnf as 统计年份,
实验教学示范中心.sl as 数量
from ins_si_sch_0405 实验教学示范中心'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0405'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0406', 'ins_si_sch_0406', '每年学校生均网络思政工作专项经费', TO_CLOB('
select
每年学校生均网络思政工作专项经费.xxbsm as 学校标识码,
每年学校生均网络思政工作专项经费.tjnf as 统计年份,
每年学校生均网络思政工作专项经费.sjwlszzxjf as 生均网络思政专项经费
from ins_si_sch_0406 每年学校生均网络思政工作专项经费'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0406'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0407', 'ins_si_sch_0407', '每年学校生均思政工作和党务工作队伍建设经费', TO_CLOB('
select
每年学校生均思政工作和党务工作队伍建设经费.xxbsm as 学校标识码,
每年学校生均思政工作和党务工作队伍建设经费.tjnf as 统计年份,
每年学校生均思政工作和党务工作队伍建设经费.sjszhdwjsjf as 生均思政和党务建设经费
from ins_si_sch_0407 每年学校生均思政工作和党务工作队伍建设经费'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0407'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0408', 'ins_si_sch_0408', '每年学校生均教学日常运行支出', TO_CLOB('
select
每年学校生均教学日常运行支出.xxbsm as 学校标识码,
每年学校生均教学日常运行支出.tjnf as 统计年份,
每年学校生均教学日常运行支出.sjjxrcyxzc as 生均教学日常运行支出
from ins_si_sch_0408 每年学校生均教学日常运行支出'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0408'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0409', 'ins_si_sch_0409', '教学日常运行支出占经常性预算内教育事业费拨款与学费收入之和的比例', TO_CLOB('
select
教学日常运行支出占经常性预算内教育事业费拨款与学费收入之和的比例.xxbsm as 学校标识码,
教学日常运行支出占经常性预算内教育事业费拨款与学费收入之和的比例.tjnf as 统计年份,
教学日常运行支出占经常性预算内教育事业费拨款与学费收入之和的比例.szb as 收支比
from ins_si_sch_0409 教学日常运行支出占经常性预算内教育事业费拨款与学费收入之和的比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0409'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0410', 'ins_si_sch_0410', '学校年度教学改革与建设专项经费', TO_CLOB('
select
学校年度教学改革与建设专项经费.xxbsm as 学校标识码,
学校年度教学改革与建设专项经费.tjnf as 统计年份,
学校年度教学改革与建设专项经费.jgzxbkdf as 教改专项拨款地方,
学校年度教学改革与建设专项经费.jgzxbkgj as 教改专项拨款国家
from ins_si_sch_0410 学校年度教学改革与建设专项经费'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0410'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0411', 'ins_si_sch_0411', '每年学校不同类型优势专业数量', TO_CLOB('
select
每年学校不同类型优势专业数量.xxbsm as 学校标识码,
每年学校不同类型优势专业数量.zylx as 专业类型,
每年学校不同类型优势专业数量.tjnf as 统计年份,
每年学校不同类型优势专业数量.zysl as 专业数量
from ins_si_sch_0411 每年学校不同类型优势专业数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0411'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0412', 'ins_si_sch_0412', '每年特色专业数量', TO_CLOB('
select
每年特色专业数量.xxbsm as 学校标识码,
每年特色专业数量.tjnf as 统计年份,
每年特色专业数量.zysl as 专业数量
from ins_si_sch_0412 每年特色专业数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0412'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0413', 'ins_si_sch_0413', '每年学校国家级双万专业数占比', TO_CLOB('
select
每年学校国家级双万专业数占比.xxbsm as 学校标识码,
每年学校国家级双万专业数占比.tjnf as 统计年份,
每年学校国家级双万专业数占比.swzyzb as 双万专业占比
from ins_si_sch_0413 每年学校国家级双万专业数占比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0413'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0414', 'ins_si_sch_0414', '每年学校新设专业数量', TO_CLOB('
select
每年学校新设专业数量.xxbsm as 学校标识码,
每年学校新设专业数量.tjnf as 统计年份,
每年学校新设专业数量.xszys as 新设专业数
from ins_si_sch_0414 每年学校新设专业数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0414'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0415', 'ins_si_sch_0415', '每年学校新设专业概况', TO_CLOB('
select
每年学校新设专业概况.xxbsm as 学校标识码,
每年学校新设专业概况.tjnf as 统计年份,
每年学校新设专业概况.bzyskjsrs as 本专业授课教师人数,
每年学校新设专业概况.bksrs as 本科生人数,
每年学校新设专业概况.xsyskjsb as 学生与授课教师比,
每年学校新设专业概况.yjbysrs as 应届毕业生人数,
每年学校新设专业概况.xsbyl as 学生毕业率,
每年学校新设专业概况.qxlsl as 去向落实率
from ins_si_sch_0415 每年学校新设专业概况'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0415'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0416', 'ins_si_sch_0416', '全校年度专业数量', TO_CLOB('
select
全校年度专业数量.xxbsm as 学校标识码,
全校年度专业数量.tjnf as 统计年份,
全校年度专业数量.syxwml as 授予学位门类,
全校年度专业数量.zysl as 专业数量
from ins_si_sch_0416 全校年度专业数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0416'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0417', 'ins_si_sch_0417', '每年全校文科招生人数', TO_CLOB('
select
每年全校文科招生人数.xxbsm as 学校标识码,
每年全校文科招生人数.tjnf as 统计年份,
每年全校文科招生人数.syxwml as 授予学位门类,
每年全校文科招生人数.zsrs as 招生人数
from ins_si_sch_0417 每年全校文科招生人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0417'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0418', 'ins_si_sch_0418', '每年全校理科招生人数', TO_CLOB('
select
每年全校理科招生人数.xxbsm as 学校标识码,
每年全校理科招生人数.tjnf as 统计年份,
每年全校理科招生人数.syxwml as 授予学位门类,
每年全校理科招生人数.zsrs as 招生人数
from ins_si_sch_0418 每年全校理科招生人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0418'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0419', 'ins_si_sch_0419', '全校专业调整数量', TO_CLOB('
select
全校专业调整数量.xxbsm as 学校标识码,
全校专业调整数量.tjnf as 统计年份,
全校专业调整数量.sfxzy as 是否新专业,
全校专业调整数量.zszt as 招生状态,
全校专业调整数量.zysl as 专业数量,
全校专业调整数量.fl as 分类
from ins_si_sch_0419 全校专业调整数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0419'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0421', 'ins_si_sch_0421', '全校通过行业认证专业数量', TO_CLOB('
select
全校通过行业认证专业数量.xxbsm as 学校标识码,
全校通过行业认证专业数量.tjnf as 统计年份,
全校通过行业认证专业数量.zysl as 专业数量
from ins_si_sch_0421 全校通过行业认证专业数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0421'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0422', 'ins_si_sch_0422', '每年学生数量', TO_CLOB('
select
每年学生数量.xxbsm as 学校标识码,
每年学生数量.tjnf as 统计年份,
每年学生数量.xssl as 学生数量
from ins_si_sch_0422 每年学生数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0422'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0423', 'ins_si_sch_0423', '全校学生流转数量', TO_CLOB('
select
全校学生流转数量.xxbsm as 学校标识码,
全校学生流转数量.xssl as 学生数量,
全校学生流转数量.xnxq as 学年学期
from ins_si_sch_0423 全校学生流转数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0423'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0424', 'ins_si_sch_0424', '全校毕业率', TO_CLOB('
select
全校毕业率.xxbsm as 学校标识码,
全校毕业率.bynf as 毕业年份,
全校毕业率.zsbyjl as 终审毕业结论,
全校毕业率.byl as 毕业率
from ins_si_sch_0424 全校毕业率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0424'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0425', 'ins_si_sch_0425', '交叉复合型人才培养对比', TO_CLOB('
select
交叉复合型人才培养对比.xxbsm as 学校标识码,
交叉复合型人才培养对比.xdlx as 修读类型,
交叉复合型人才培养对比.pyfasl as 培养方案数量,
交叉复合型人才培养对比.xn as 学年
from ins_si_sch_0425 交叉复合型人才培养对比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0425'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0426', 'ins_si_sch_0426', '每年招生计划人数', TO_CLOB('
select
每年招生计划人数.xxbsm as 学校标识码,
每年招生计划人数.tjnf as 统计年份,
每年招生计划人数.zsjhs as 招生计划数
from ins_si_sch_0426 每年招生计划人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0426'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0427', 'ins_si_sch_0427', '每年实际录取人数', TO_CLOB('
select
每年实际录取人数.xxbsm as 学校标识码,
每年实际录取人数.tjnf as 统计年份,
每年实际录取人数.sjlqs as 实际录取数
from ins_si_sch_0427 每年实际录取人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0427'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0428', 'ins_si_sch_0428', '每年生源来源省份数量', TO_CLOB('
select
每年生源来源省份数量.xxbsm as 学校标识码,
每年生源来源省份数量.tjnf as 统计年份,
每年生源来源省份数量.sf as 省份,
每年生源来源省份数量.sysfs as 生源省份数
from ins_si_sch_0428 每年生源来源省份数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0428'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0429', 'ins_si_sch_0429', '每年报到率', TO_CLOB('
select
每年报到率.xxbsm as 学校标识码,
每年报到率.tjnf as 统计年份,
每年报到率.bdl as 报到率
from ins_si_sch_0429 每年报到率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0429'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0430', 'ins_si_sch_0430', '每年第一志愿录取率', TO_CLOB('
select
每年第一志愿录取率.xxbsm as 学校标识码,
每年第一志愿录取率.tjnf as 统计年份,
每年第一志愿录取率.dyzylql as 第一志愿录取率
from ins_si_sch_0430 每年第一志愿录取率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0430'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0431', 'ins_si_sch_0431', '每年奖贷补资助金额', TO_CLOB('
select
每年奖贷补资助金额.xxbsm as 学校标识码,
每年奖贷补资助金额.tjnf as 统计年份,
每年奖贷补资助金额.zzjezj as 资助金额总计
from ins_si_sch_0431 每年奖贷补资助金额'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0431'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0432', 'ins_si_sch_0432', '每年学生社团数量', TO_CLOB('
select
每年学生社团数量.xxbsm as 学校标识码,
每年学生社团数量.tjnf as 统计年份,
每年学生社团数量.stzs as 社团总数
from ins_si_sch_0432 每年学生社团数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0432'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0433', 'ins_si_sch_0433', '每年获得荣誉称号人数', TO_CLOB('
select
每年获得荣誉称号人数.xxbsm as 学校标识码,
每年获得荣誉称号人数.tjnf as 统计年份,
每年获得荣誉称号人数.rychrs as 荣誉称号人数
from ins_si_sch_0433 每年获得荣誉称号人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0433'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0434', 'ins_si_sch_0434', '每年毕业生英语等级通过率', TO_CLOB('
select
每年毕业生英语等级通过率.xxbsm as 学校标识码,
每年毕业生英语等级通过率.yydj as 英语等级,
每年毕业生英语等级通过率.tgl as 通过率,
每年毕业生英语等级通过率.tjnf as 统计年份
from ins_si_sch_0434 每年毕业生英语等级通过率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0434'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0435', 'ins_si_sch_0435', '每年学校卓越计划项目数量', TO_CLOB('
select
每年学校卓越计划项目数量.xxbsm as 学校标识码,
每年学校卓越计划项目数量.tjnf as 统计年份,
每年学校卓越计划项目数量.xmsl as 项目数量
from ins_si_sch_0435 每年学校卓越计划项目数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0435'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0436', 'ins_si_sch_0436', '每年双创教育实践基地数量', TO_CLOB('
select
每年双创教育实践基地数量.xxbsm as 学校标识码,
每年双创教育实践基地数量.jdptlx as 基地平台类型,
每年双创教育实践基地数量.jdptjb as 基地平台级别,
每年双创教育实践基地数量.tjnf as 统计年份,
每年双创教育实践基地数量.jdsl as 基地数量
from ins_si_sch_0436 每年双创教育实践基地数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0436'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0437', 'ins_si_sch_0437', '每年学校双创参与学生比例', TO_CLOB('
select
每年学校双创参与学生比例.xxbsm as 学校标识码,
每年学校双创参与学生比例.tjnf as 统计年份,
每年学校双创参与学生比例.scxsbl as 双创学生比例
from ins_si_sch_0437 每年学校双创参与学生比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0437'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0438', 'ins_si_sch_0438', '学科竞赛立项数', TO_CLOB('
select
学科竞赛立项数.xxbsm as 学校标识码,
学科竞赛立项数.tjnf as 统计年份,
学科竞赛立项数.jsmc as 竞赛名称,
学科竞赛立项数.xmsl as 项目数量
from ins_si_sch_0438 学科竞赛立项数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0438'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0439', 'ins_si_sch_0439', '省级及以上重点竞赛获奖数', TO_CLOB('
select
省级及以上重点竞赛获奖数.xxbsm as 学校标识码,
省级及以上重点竞赛获奖数.tjnf as 统计年份,
省级及以上重点竞赛获奖数.hjsl as 获奖数量
from ins_si_sch_0439 省级及以上重点竞赛获奖数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0439'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0440', 'ins_si_sch_0440', '双创投入', TO_CLOB('
select
双创投入.xxbsm as 学校标识码,
双创投入.tjnf as 统计年份,
双创投入.sctrjf as 双创投入经费
from ins_si_sch_0440 双创投入'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0440'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0441', 'ins_si_sch_0441', '创新创业成果分布', TO_CLOB('
select
创新创业成果分布.xxbsm as 学校标识码,
创新创业成果分布.tjnf as 统计年份,
创新创业成果分布.cglb as 成果类别,
创新创业成果分布.cgsl as 成果数量
from ins_si_sch_0441 创新创业成果分布'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0441'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0442', 'ins_si_sch_0442', '学科竞赛排行榜（单年排名）', TO_CLOB('
select
学科竞赛排行榜单年排名.xxbsm as 学校标识码,
学科竞赛排行榜单年排名.tjnf as 统计年份,
学科竞赛排行榜单年排名.mnxxpm as 每年学校排名
from ins_si_sch_0442 学科竞赛排行榜单年排名'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0442'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0443', 'ins_si_sch_0443', '学科竞赛排行榜（五年排名）', TO_CLOB('
select
学科竞赛排行榜五年排名.xxbsm as 学校标识码,
学科竞赛排行榜五年排名.tjnf as 统计年份,
学科竞赛排行榜五年排名.wnxxpm as 五年学校排名
from ins_si_sch_0443 学科竞赛排行榜五年排名'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0443'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0444', 'ins_si_sch_0444', '每年学生参与“互联网+”大学生创新创业大赛各赛道项目数量', TO_CLOB('
select
每年学生参与互联网大学生创新创业大赛各赛道项目数量.xxbsm as 学校标识码,
每年学生参与互联网大学生创新创业大赛各赛道项目数量.tjnf as 统计年份,
每年学生参与互联网大学生创新创业大赛各赛道项目数量.sdlx as 赛道类型,
每年学生参与互联网大学生创新创业大赛各赛道项目数量.jsmc as 竞赛名称,
每年学生参与互联网大学生创新创业大赛各赛道项目数量.csxms as 参赛项目数
from ins_si_sch_0444 每年学生参与互联网大学生创新创业大赛各赛道项目数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0444'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0445', 'ins_si_sch_0445', '每年学生参与“互联网+”大学生创新创业大赛获奖数量', TO_CLOB('
select
每年学生参与互联网大学生创新创业大赛获奖数量.xxbsm as 学校标识码,
每年学生参与互联网大学生创新创业大赛获奖数量.tjnf as 统计年份,
每年学生参与互联网大学生创新创业大赛获奖数量.hjsl as 获奖数量
from ins_si_sch_0445 每年学生参与互联网大学生创新创业大赛获奖数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0445'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0446', 'ins_si_sch_0446', '学科竞赛立项项目数量', TO_CLOB('
select
学科竞赛立项项目数量.xxbsm as 学校标识码,
学科竞赛立项项目数量.tjnf as 统计年份,
学科竞赛立项项目数量.xmzt as 项目状态,
学科竞赛立项项目数量.xmsl as 项目数量
from ins_si_sch_0446 学科竞赛立项项目数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0446'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0447', 'ins_si_sch_0447', '品牌竞赛创新团队获奖数量', TO_CLOB('
select
品牌竞赛创新团队获奖数量.xxbsm as 学校标识码,
品牌竞赛创新团队获奖数量.tjnf as 统计年份,
品牌竞赛创新团队获奖数量.xmjb as 项目级别,
品牌竞赛创新团队获奖数量.hjdj as 获奖等级,
品牌竞赛创新团队获奖数量.hjsl as 获奖数量
from ins_si_sch_0447 品牌竞赛创新团队获奖数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0447'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0448', 'ins_si_sch_0448', '创新创业类课程', TO_CLOB('
select
创新创业类课程.xxbsm as 学校标识码,
创新创业类课程.tjnf as 统计年份,
创新创业类课程.kcjb as 课程级别,
创新创业类课程.kcsl as 课程数量
from ins_si_sch_0448 创新创业类课程'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0448'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0449', 'ins_si_sch_0449', '每年毕业综合训练指导教师数量', TO_CLOB('
select
每年毕业综合训练指导教师数量.xxbsm as 学校标识码,
每年毕业综合训练指导教师数量.zdjszs as 指导教师总数,
每年毕业综合训练指导教师数量.xn as 学年,
每年毕业综合训练指导教师数量.xtlb as 选题类别
from ins_si_sch_0449 每年毕业综合训练指导教师数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0449'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0450', 'ins_si_sch_0450', '每年毕业论文通过率', TO_CLOB('
select
每年毕业论文通过率.xxbsm as 学校标识码,
每年毕业论文通过率.lwsftg as 论文是否通过,
每年毕业论文通过率.bylwtgl as 毕业论文通过率,
每年毕业论文通过率.xn as 学年
from ins_si_sch_0450 每年毕业论文通过率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0450'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0451', 'ins_si_sch_0451', '每年以实验、实习、工程实践和社会调查等为基础的论文占比', TO_CLOB('
select
每年以实验实习工程实践和社会调查等为基础的论文占比.xxbsm as 学校标识码,
每年以实验实习工程实践和社会调查等为基础的论文占比.sfzsy as 是否在实验,
每年以实验实习工程实践和社会调查等为基础的论文占比.sx as 实习,
每年以实验实习工程实践和社会调查等为基础的论文占比.gcsjhshdcdshsjzwc as 工程实践和社会调查等社会实践中完成,
每年以实验实习工程实践和社会调查等为基础的论文占比.sjllwzb as 实践类论文占比,
每年以实验实习工程实践和社会调查等为基础的论文占比.xn as 学年
from ins_si_sch_0451 每年以实验实习工程实践和社会调查等为基础的论文占比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0451'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0452', 'ins_si_sch_0452', '每年学校毕业综合训练课题完成率', TO_CLOB('
select
每年学校毕业综合训练课题完成率.xxbsm as 学校标识码,
每年学校毕业综合训练课题完成率.xlwcl as 训练完成率,
每年学校毕业综合训练课题完成率.xn as 学年
from ins_si_sch_0452 每年学校毕业综合训练课题完成率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0452'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0453', 'ins_si_sch_0453', '每年学校指导毕业生数', TO_CLOB('
select
每年学校指导毕业生数.xxbsm as 学校标识码,
每年学校指导毕业生数.zdbysrs as 指导毕业生人数,
每年学校指导毕业生数.xn as 学年
from ins_si_sch_0453 每年学校指导毕业生数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0453'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0454', 'ins_si_sch_0454', '每年学校平均指导毕业生数', TO_CLOB('
select
每年学校平均指导毕业生数.xxbsm as 学校标识码,
每年学校平均指导毕业生数.pjzdbysrs as 平均指导毕业生人数,
每年学校平均指导毕业生数.xn as 学年
from ins_si_sch_0454 每年学校平均指导毕业生数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0454'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0455', 'ins_si_sch_0455', '每年学生参与科研项目数量', TO_CLOB('
select
每年学生参与科研项目数量.xxbsm as 学校标识码,
每年学生参与科研项目数量.tjnf as 统计年份,
每年学生参与科研项目数量.kyxmsl as 科研项目数量
from ins_si_sch_0455 每年学生参与科研项目数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0455'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0456', 'ins_si_sch_0456', '每年学生参与创作、表演作品数量', TO_CLOB('
select
每年学生参与创作表演作品数量.xxbsm as 学校标识码,
每年学生参与创作表演作品数量.lx as 类型,
每年学生参与创作表演作品数量.tjnf as 统计年份,
每年学生参与创作表演作品数量.zpsl as 作品数量
from ins_si_sch_0456 每年学生参与创作表演作品数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0456'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0457', 'ins_si_sch_0457', '每年学科竞赛获奖数量', TO_CLOB('
select
每年学科竞赛获奖数量.xxbsm as 学校标识码,
每年学科竞赛获奖数量.hjlb as 获奖类别,
每年学科竞赛获奖数量.hjdj as 获奖等级,
每年学科竞赛获奖数量.jslx as 竞赛类型,
每年学科竞赛获奖数量.tjnf as 统计年份,
每年学科竞赛获奖数量.hjsl as 获奖数量,
每年学科竞赛获奖数量.hjjb as 获奖级别
from ins_si_sch_0457 每年学科竞赛获奖数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0457'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0458', 'ins_si_sch_0458', '每年艺术类竞赛获奖数量', TO_CLOB('
select
每年艺术类竞赛获奖数量.xxbsm as 学校标识码,
每年艺术类竞赛获奖数量.sslb as 赛事类别,
每年艺术类竞赛获奖数量.hjdj as 获奖等级,
每年艺术类竞赛获奖数量.tjnf as 统计年份,
每年艺术类竞赛获奖数量.hjsl as 获奖数量
from ins_si_sch_0458 每年艺术类竞赛获奖数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0458'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0459', 'ins_si_sch_0459', '每年体育类竞赛获奖数量', TO_CLOB('
select
每年体育类竞赛获奖数量.xxbsm as 学校标识码,
每年体育类竞赛获奖数量.sslb as 赛事类别,
每年体育类竞赛获奖数量.hjmc as 获奖名次,
每年体育类竞赛获奖数量.tjnf as 统计年份,
每年体育类竞赛获奖数量.hjsl as 获奖数量
from ins_si_sch_0459 每年体育类竞赛获奖数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0459'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0460', 'ins_si_sch_0460', '每年学校毕业生去向落实率', TO_CLOB('
select
每年学校毕业生去向落实率.xxbsm as 学校标识码,
每年学校毕业生去向落实率.lsl as 落实率,
每年学校毕业生去向落实率.xn as 学年
from ins_si_sch_0460 每年学校毕业生去向落实率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0460'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0461', 'ins_si_sch_0461', '每年学校毕业生升学或深造率', TO_CLOB('
select
每年学校毕业生升学或深造率.xxbsm as 学校标识码,
每年学校毕业生升学或深造率.sxhszl as 升学或深造率,
每年学校毕业生升学或深造率.xn as 学年
from ins_si_sch_0461 每年学校毕业生升学或深造率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0461'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0462', 'ins_si_sch_0462', '每年学校毕业生就业率', TO_CLOB('
select
每年学校毕业生就业率.xxbsm as 学校标识码,
每年学校毕业生就业率.qxlx as 去向类型,
每年学校毕业生就业率.qxsf as 去向省份,
每年学校毕业生就业率.jyl as 就业率,
每年学校毕业生就业率.xn as 学年
from ins_si_sch_0462 每年学校毕业生就业率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0462'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0463', 'ins_si_sch_0463', '每年学校毕业生签约就业占比', TO_CLOB('
select
每年学校毕业生签约就业占比.xxbsm as 学校标识码,
每年学校毕业生签约就业占比.qyjyzb as 签约就业占比,
每年学校毕业生签约就业占比.xn as 学年
from ins_si_sch_0463 每年学校毕业生签约就业占比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0463'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0464', 'ins_si_sch_0464', '每年学校毕业生去向类型人数', TO_CLOB('
select
每年学校毕业生去向类型人数.xxbsm as 学校标识码,
每年学校毕业生去向类型人数.qxlx as 去向类型,
每年学校毕业生去向类型人数.xssl as 学生数量,
每年学校毕业生去向类型人数.xn as 学年,
每年学校毕业生去向类型人数.qxfl as 去向分类
from ins_si_sch_0464 每年学校毕业生去向类型人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0464'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0465', 'ins_si_sch_0465', '每年学校毕业生单位性质人数', TO_CLOB('
select
每年学校毕业生单位性质人数.xxbsm as 学校标识码,
每年学校毕业生单位性质人数.dwxz as 单位性质,
每年学校毕业生单位性质人数.xssl as 学生数量,
每年学校毕业生单位性质人数.xn as 学年
from ins_si_sch_0465 每年学校毕业生单位性质人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0465'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0466', 'ins_si_sch_0466', '每年学校毕业生单位行业人数', TO_CLOB('
select
每年学校毕业生单位行业人数.xxbsm as 学校标识码,
每年学校毕业生单位行业人数.dwxy as 单位行业,
每年学校毕业生单位行业人数.xssl as 学生数量,
每年学校毕业生单位行业人数.xn as 学年
from ins_si_sch_0466 每年学校毕业生单位行业人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0466'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0467', 'ins_si_sch_0467', '每学年累计不及格学分达到专业培养方案总学分25%及以上的学生数量', TO_CLOB('
select
每学年累计不及格学分达到专业培养方案总学分25及以上的学生数量.xxbsm as 学校标识码,
每学年累计不及格学分达到专业培养方案总学分25及以上的学生数量.xsrs as 学生人数,
每学年累计不及格学分达到专业培养方案总学分25及以上的学生数量.xn as 学年
from ins_si_sch_0467 每学年累计不及格学分达到专业培养方案总学分25及以上的学生数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0467'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0468', 'ins_si_sch_0468', '本科生评教参评率', TO_CLOB('
select
本科生评教参评率.xxbsm as 学校标识码,
本科生评教参评率.pjzt as 评教状态,
本科生评教参评率.cpl as 参评率,
本科生评教参评率.xn as 学年
from ins_si_sch_0468 本科生评教参评率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0468'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0469', 'ins_si_sch_0469', '每年听课门次数', TO_CLOB('
select
每年听课门次数.xxbsm as 学校标识码,
每年听课门次数.tkrysf as 听课人员身份,
每年听课门次数.kclx as 课程类型,
每年听课门次数.tksl as 听课数量,
每年听课门次数.xn as 学年
from ins_si_sch_0469 每年听课门次数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0469'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0470', 'ins_si_sch_0470', '每年听课均分', TO_CLOB('
select
每年听课均分.xxbsm as 学校标识码,
每年听课均分.tkrysf as 听课人员身份,
每年听课均分.kclx as 课程类型,
每年听课均分.tkjf as 听课均分,
每年听课均分.xn as 学年
from ins_si_sch_0470 每年听课均分'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0470'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0471', 'ins_si_sch_0471', '每年听课等第占比', TO_CLOB('
select
每年听课等第占比.xxbsm as 学校标识码,
每年听课等第占比.tkrysf as 听课人员身份,
每年听课等第占比.kclx as 课程类型,
每年听课等第占比.tkddzb as 听课等第占比,
每年听课等第占比.xn as 学年,
每年听课等第占比.tkdd as 听课等第
from ins_si_sch_0471 每年听课等第占比'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0471'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0472', 'ins_si_sch_0472', '每年优质课程数量', TO_CLOB('
select
每年优质课程数量.xxbsm as 学校标识码,
每年优质课程数量.kcsl as 课程数量,
每年优质课程数量.xn as 学年
from ins_si_sch_0472 每年优质课程数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0472'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0473', 'ins_si_sch_0473', '每年优质课程的上课学生数量', TO_CLOB('
select
每年优质课程的上课学生数量.xxbsm as 学校标识码,
每年优质课程的上课学生数量.xsrs as 学生人数,
每年优质课程的上课学生数量.xn as 学年
from ins_si_sch_0473 每年优质课程的上课学生数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0473'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0474', 'ins_si_sch_0474', '每年听课覆盖率', TO_CLOB('
select
每年听课覆盖率.xxbsm as 学校标识码,
每年听课覆盖率.fgl as 覆盖率,
每年听课覆盖率.xn as 学年
from ins_si_sch_0474 每年听课覆盖率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0474'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0475', 'ins_si_sch_0475', '每年学校课程目标达成度', TO_CLOB('
select
每年学校课程目标达成度.xxbsm as 学校标识码,
每年学校课程目标达成度.tjnf as 统计年份,
每年学校课程目标达成度.jcdx as 检查对象,
每年学校课程目标达成度.kcmbdcd as 课程目标达成度
from ins_si_sch_0475 每年学校课程目标达成度'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0475'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0476', 'ins_si_sch_0476', '每年学校课程检查抽检率', TO_CLOB('
select
每年学校课程检查抽检率.xxbsm as 学校标识码,
每年学校课程检查抽检率.tjnf as 统计年份,
每年学校课程检查抽检率.jcdx as 检查对象,
每年学校课程检查抽检率.cjl as 抽检率
from ins_si_sch_0476 每年学校课程检查抽检率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0476'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0478', 'ins_si_sch_0478', '每学年主讲本科课程教师人数', TO_CLOB('
select
每学年主讲本科课程教师人数.xxbsm as 学校标识码,
每学年主讲本科课程教师人数.zyjszc as 专业技术职称,
每学年主讲本科课程教师人数.xn as 学年,
每学年主讲本科课程教师人数.skjss as 授课教师数
from ins_si_sch_0478 每学年主讲本科课程教师人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0478'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0479', 'ins_si_sch_0479', '每学年学校开课平均学时', TO_CLOB('
select
每学年学校开课平均学时.xxbsm as 学校标识码,
每学年学校开课平均学时.xn as 学年,
每学年学校开课平均学时.xs as 学时,
每学年学校开课平均学时.kclb as 课程类别
from ins_si_sch_0479 每学年学校开课平均学时'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0479'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0480', 'ins_si_sch_0480', '每年学校各学院教学质量管理人员数量', TO_CLOB('
select
每年学校各学院教学质量管理人员数量.xxbsm as 学校标识码,
每年学校各学院教学质量管理人员数量.tjnf as 统计年份,
每年学校各学院教学质量管理人员数量.rysl as 人员数量
from ins_si_sch_0480 每年学校各学院教学质量管理人员数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0480'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0482', 'ins_si_sch_0482', '每年大创项目学生参与比例', TO_CLOB('
select
每年大创项目学生参与比例.xxbsm as 学校标识码,
每年大创项目学生参与比例.tjnf as 统计年份,
每年大创项目学生参与比例.xmlb as 项目类别,
每年大创项目学生参与比例.cybl as 参与比例
from ins_si_sch_0482 每年大创项目学生参与比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0482'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0483', 'ins_si_sch_0483', '每年学校体质健康合格率', TO_CLOB('
select
每年学校体质健康合格率.xxbsm as 学校标识码,
每年学校体质健康合格率.tjnf as 统计年份,
每年学校体质健康合格率.tzjkhgl as 体质健康合格率,
每年学校体质健康合格率.sslb as 赛事类别,
每年学校体质健康合格率.hjmc as 获奖名次
from ins_si_sch_0483 每年学校体质健康合格率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0483'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0484', 'ins_si_sch_0484', '每年学校学位授予率', TO_CLOB('
select
每年学校学位授予率.xxbsm as 学校标识码,
每年学校学位授予率.xn as 学年,
每年学校学位授予率.xwsyl as 学位授予率
from ins_si_sch_0484 每年学校学位授予率'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0484'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0485', 'ins_si_sch_0485', '每年学校就业地域流向人数', TO_CLOB('
select
每年学校就业地域流向人数.xxbsm as 学校标识码,
每年学校就业地域流向人数.xn as 学年,
每年学校就业地域流向人数.xsrs as 学生人数
from ins_si_sch_0485 每年学校就业地域流向人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0485'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0486', 'ins_si_sch_0486', '每学期全校开课平均学时', TO_CLOB('
select
每学期全校开课平均学时.xxbsm as 学校标识码,
每学期全校开课平均学时.xnxq as 学年学期,
每学期全校开课平均学时.xs as 学时
from ins_si_sch_0486 每学期全校开课平均学时'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0486'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0487', 'ins_si_sch_0487', '每学年全校开课平均学时', TO_CLOB('
select
每学年全校开课平均学时.xxbsm as 学校标识码,
每学年全校开课平均学时.xn as 学年,
每学年全校开课平均学时.xs as 学时
from ins_si_sch_0487 每学年全校开课平均学时'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0487'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0488', 'ins_si_sch_0488', '每学年全校开课平均班规模', TO_CLOB('
select
每学年全校开课平均班规模.xxbsm as 学校标识码,
每学年全校开课平均班规模.xn as 学年,
每学年全校开课平均班规模.pjbgm as 平均班规模
from ins_si_sch_0488 每学年全校开课平均班规模'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0488'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0489', 'ins_si_sch_0489', '每年学校教学成果奖数量', TO_CLOB('
select
每年学校教学成果奖数量.xxbsm as 学校标识码,
每年学校教学成果奖数量.tjnf as 统计年份,
每年学校教学成果奖数量.jb as 级别,
每年学校教学成果奖数量.cgsl as 成果数量
from ins_si_sch_0489 每年学校教学成果奖数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0489'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0490', 'ins_si_sch_0490', '每年临近报废实验设备数量', TO_CLOB('
select
每年临近报废实验设备数量.xxbsm as 学校标识码,
每年临近报废实验设备数量.ddbfnxhyjn as 达到报废年限还有几年,
每年临近报废实验设备数量.sycsdm as 实验场所代码,
每年临近报废实验设备数量.tjnf as 统计年份,
每年临近报废实验设备数量.sbsl as 设备数量
from ins_si_sch_0490 每年临近报废实验设备数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0490'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0491', 'ins_si_sch_0491', '每年一流专业数量', TO_CLOB('
select
每年一流专业数量.xxbsm as 学校标识码,
每年一流专业数量.jb as 级别,
每年一流专业数量.zylx as 专业类型,
每年一流专业数量.tjnf as 统计年份,
每年一流专业数量.zysl as 专业数量
from ins_si_sch_0491 每年一流专业数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0491'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0492', 'ins_si_sch_0492', '每年学校特色专业数量', TO_CLOB('
select
每年学校特色专业数量.xxbsm as 学校标识码,
每年学校特色专业数量.jb as 级别,
每年学校特色专业数量.tjnf as 统计年份,
每年学校特色专业数量.zysl as 专业数量
from ins_si_sch_0492 每年学校特色专业数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0492'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0493', 'ins_si_sch_0493', '每年学校毕业综合训练指导教师数量', TO_CLOB('
select
每年学校毕业综合训练指导教师数量.xxbsm as 学校标识码,
每年学校毕业综合训练指导教师数量.xn as 学年,
每年学校毕业综合训练指导教师数量.zdjszs as 指导教师总数
from ins_si_sch_0493 每年学校毕业综合训练指导教师数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0493'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0494', 'ins_si_sch_0494', '每年学校双创竞赛获奖数量', TO_CLOB('
select
每年学校双创竞赛获奖数量.xxbsm as 学校标识码,
每年学校双创竞赛获奖数量.tjnf as 统计年份,
每年学校双创竞赛获奖数量.schjsl as 双创获奖数量,
每年学校双创竞赛获奖数量.jb as 级别
from ins_si_sch_0494 每年学校双创竞赛获奖数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0494'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0495', 'ins_si_sch_0495', '每年学校大创项目数量', TO_CLOB('
select
每年学校大创项目数量.xxbsm as 学校标识码,
每年学校大创项目数量.tjnf as 统计年份,
每年学校大创项目数量.xmlb as 项目类别,
每年学校大创项目数量.xmsl as 项目数量,
每年学校大创项目数量.xmzt as 项目状态,
每年学校大创项目数量.xmjb as 项目级别
from ins_si_sch_0495 每年学校大创项目数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0495'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0496', 'ins_si_sch_0496', '每年学校本科生发表学术论文', TO_CLOB('
select
每年学校本科生发表学术论文.xxbsm as 学校标识码,
每年学校本科生发表学术论文.tjnf as 统计年份,
每年学校本科生发表学术论文.xslwsl as 学术论文数量
from ins_si_sch_0496 每年学校本科生发表学术论文'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0496'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0497', 'ins_si_sch_0497', '每年学校本科生获准专利著作权数', TO_CLOB('
select
每年学校本科生获准专利著作权数.xxbsm as 学校标识码,
每年学校本科生获准专利著作权数.tjnf as 统计年份,
每年学校本科生获准专利著作权数.sl as 数量
from ins_si_sch_0497 每年学校本科生获准专利著作权数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0497'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0502', 'ins_si_sch_0502', '每年正高级职称专任教师比例', TO_CLOB('
select
每年正高级职称专任教师比例.xxbsm as 学校标识码,
每年正高级职称专任教师比例.tjnf as 统计年份,
每年正高级职称专任教师比例.zrjsbl as 专任教师比例
from ins_si_sch_0502 每年正高级职称专任教师比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0502'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0503', 'ins_si_sch_0503', '每年学校课程测评均分', TO_CLOB('
select
每年学校课程测评均分.xxbsm as 学校标识码,
每年学校课程测评均分.xn as 学年,
每年学校课程测评均分.kcfl as 课程分类,
每年学校课程测评均分.cpjf as 测评均分
from ins_si_sch_0503 每年学校课程测评均分'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0503'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0504', 'ins_si_sch_0504', '每年学校班导师评教均分', TO_CLOB('
select
每年学校班导师评教均分.xxbsm as 学校标识码,
每年学校班导师评教均分.xn as 学年,
每年学校班导师评教均分.bdsjf as 班导师均分
from ins_si_sch_0504 每年学校班导师评教均分'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0504'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0508', 'ins_si_sch_0508', '每年学校优势专业数量', TO_CLOB('
select
每年学校优势专业数量.xxbsm as 学校标识码,
每年学校优势专业数量.tjnf as 统计年份,
每年学校优势专业数量.zysl as 专业数量
from ins_si_sch_0508 每年学校优势专业数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0508'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0509', 'ins_si_sch_0509', '全校专业平均总学分', TO_CLOB('
select
全校专业平均总学分.xxbsm as 学校标识码,
全校专业平均总学分.tjnf as 统计年份,
全校专业平均总学分.zypjzxf as 专业平均总学分
from ins_si_sch_0509 全校专业平均总学分'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0509'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0510', 'ins_si_sch_0510', '每年学校体育课测评均分', TO_CLOB('
select
每年学校体育课测评均分.xxbsm as 学校标识码,
每年学校体育课测评均分.xn as 学年,
每年学校体育课测评均分.tykmc as 体育课名称,
每年学校体育课测评均分.cpjf as 测评均分
from ins_si_sch_0510 每年学校体育课测评均分'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0510'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0511', 'ins_si_sch_0511', '学年内学生流动净值', TO_CLOB('
select
学年内学生流动净值.xxbsm as 学校标识码,
学年内学生流动净值.xnxq as 学年学期,
学年内学生流动净值.zcxwml as 转出学位门类,
学年内学生流动净值.zrxwml as 转入学位门类,
学年内学生流动净值.rs as 人数,
学年内学生流动净值.xsrs as 学生人数
from ins_si_sch_0511 学年内学生流动净值'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0511'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0512', 'ins_si_sch_0512', '每年各类学科竞赛参与人数', TO_CLOB('
select
每年各类学科竞赛参与人数.xxbsm as 学校标识码,
每年各类学科竞赛参与人数.tjnf as 统计年份,
每年各类学科竞赛参与人数.jslx as 竞赛类型,
每年各类学科竞赛参与人数.xsrs as 学生人数
from ins_si_sch_0512 每年各类学科竞赛参与人数'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0512'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0513', 'ins_si_sch_0513', '每年各类学科竞赛参与比例', TO_CLOB('
select
每年各类学科竞赛参与比例.xxbsm as 学校标识码,
每年各类学科竞赛参与比例.tjnf as 统计年份,
每年各类学科竞赛参与比例.jslx as 竞赛类型,
每年各类学科竞赛参与比例.bl as 比例
from ins_si_sch_0513 每年各类学科竞赛参与比例'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0513'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0525', 'ins_si_sch_0525', '每年各类课程思政数量', TO_CLOB('
select
每年各类课程思政数量.xxbsm as 学校标识码,
每年各类课程思政数量.szlx as 思政类型,
每年各类课程思政数量.jb as 级别,
每年各类课程思政数量.sl as 数量,
每年各类课程思政数量.tjnf as 统计年份
from ins_si_sch_0525 每年各类课程思政数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0525'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0526', 'ins_si_sch_0526', '每年产业学院数量', TO_CLOB('
select
每年产业学院数量.xxbsm as 学校标识码,
每年产业学院数量.jb as 级别,
每年产业学院数量.tjnf as 统计年份,
每年产业学院数量.sl as 数量
from ins_si_sch_0526 每年产业学院数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0526'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sch_0527', 'ins_si_sch_0527', '每年各级别实习基地数量', TO_CLOB('
select
每年各级别实习基地数量.xxbsm as 学校标识码,
每年各级别实习基地数量.tjnf as 统计年份,
每年各级别实习基地数量.jb as 级别,
每年各级别实习基地数量.jdsl as 基地数量
from ins_si_sch_0527 每年各级别实习基地数量'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sch_0527'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'ins_si_sub_0092', 'ins_si_sub_0092', '专业培养方案学分结构', TO_CLOB('
select
专业培养方案学分结构.xkbh as 学科编号,
专业培养方案学分结构.tjnf as 统计年份,
专业培养方案学分结构.xkml as 学科门类,
专业培养方案学分结构.zysl as 专业数量,
专业培养方案学分结构.zypjzxf as 专业平均总学分,
专业培养方案学分结构.sjjxxfzb as 实践教学学分占比,
专业培养方案学分结构.xkmlmc as 学科门类名称,
专业培养方案学分结构.xkmlsl as 学科门类数量
from ins_si_sub_0092 专业培养方案学分结构'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'ins_si_sub_0092'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'cla_label', 'cla_label', 'CLA标签宽表', TO_CLOB('
select
CLA标签宽表.kzrq as 快照日期,
CLA标签宽表.jasdm as 教室代码
from cla_label CLA标签宽表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'cla_label'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'col_label', 'col_label', 'COL标签宽表', TO_CLOB('
select
COL标签宽表.kzrq as 快照日期,
COL标签宽表.xybh as 学院编号
from col_label COL标签宽表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'col_label'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'cou_label', 'cou_label', 'COU标签宽表', TO_CLOB('
select
COU标签宽表.kzrq as 快照日期,
COU标签宽表.kcdm as 课程代码
from cou_label COU标签宽表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'cou_label'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'maj_label', 'maj_label', 'MAJ标签宽表', TO_CLOB('
select
MAJ标签宽表.kzrq as 快照日期,
MAJ标签宽表.xnzydm as 校内专业代码
from maj_label MAJ标签宽表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'maj_label'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'pos_label', 'pos_label', 'POS标签宽表', TO_CLOB('
select
POS标签宽表.kzrq as 快照日期,
POS标签宽表.xh as 学号
from pos_label POS标签宽表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'pos_label'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'sch_label', 'sch_label', 'SCH标签宽表', TO_CLOB('
select
SCH标签宽表.kzrq as 快照日期,
SCH标签宽表.xxbsm as 学校标识码
from sch_label SCH标签宽表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'sch_label'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'sub_label', 'sub_label', 'SUB标签宽表', TO_CLOB('
select
SUB标签宽表.kzrq as 快照日期,
SUB标签宽表.yjxkdm as 一级学科代码
from sub_label SUB标签宽表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'sub_label'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 't_da_model_column_extend', 't_da_model_column_extend', '', TO_CLOB('
select
t_da_model_column_extend.primary_column_tag as 是否主键字段
from t_da_model_column_extend t_da_model_column_extend'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 't_da_model_column_extend'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 't_da_model_column_info', 't_da_model_column_info', '模型字段信息', TO_CLOB('
select
模型字段信息.model_id as 模型ID,
模型字段信息.column_id as 字段ID,
模型字段信息.syn_value as 同义词
from t_da_model_column_info 模型字段信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 't_da_model_column_info'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 't_da_model_info', 't_da_model_info', '模型信息', TO_CLOB('
select
模型信息.model_id as 模型ID,
模型信息.syn_value as 同义词
from t_da_model_info 模型信息'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 't_da_model_info'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 't_da_model_relation', 't_da_model_relation', '模型关系表', TO_CLOB('
select
模型关系表.model_id as 主模型ID,
模型关系表.fields as 主模型字段,
模型关系表.mapped_model_id as 关联模型,
模型关系表.mapped_fields as 关联模型字段
from t_da_model_relation 模型关系表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 't_da_model_relation'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tcl_label', 'tcl_label', 'TCL标签宽表', TO_CLOB('
select
TCL标签宽表.kzrq as 快照日期,
TCL标签宽表.jxbid as 教学班ID
from tcl_label TCL标签宽表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tcl_label'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tea_label', 'tea_label', 'TEA标签宽表', TO_CLOB('
select
TEA标签宽表.kzrq as 快照日期,
TEA标签宽表.zgh as 教职工号
from tea_label TEA标签宽表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tea_label'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'tra_label', 'tra_label', 'TRA标签宽表', TO_CLOB('
select
TRA标签宽表.kzrq as 快照日期,
TRA标签宽表.pyfadm as 培养方案代码
from tra_label TRA标签宽表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'tra_label'
);

INSERT INTO t_table_map (id,table_name,comments,sql_str,
create_by,create_time,update_by,update_time)
SELECT 'und_label', 'und_label', 'UND标签宽表', TO_CLOB('
select
UND标签宽表.kzrq as 快照日期,
UND标签宽表.xh as 学号
from und_label UND标签宽表'),
'dataapp',TIMESTAMP '2024-06-20 01:00:00','dataapp',TIMESTAMP '2024-06-20 01:00:00'
FROM dual
WHERE NOT EXISTS (
    SELECT 1
    FROM t_table_map
    WHERE id = 'und_label'
);