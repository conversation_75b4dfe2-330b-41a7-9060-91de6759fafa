----------------------------------------------↓ 模型 main-inssiund0015 ↓----------------------------------------------
--模型：学生每年学术成果数量 INS_SI_UND_0015 main-inssiund0015
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-inssiund0015'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-inssiund0015版本号:'||v_version);
	IF check_update(v_version, '1.0.3.0') >= 0 THEN
		DBMS_OUTPUT.PUT_LINE('无需升级,模型main-inssiund0015的新版本:1.0.3.0,当前版本'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1934536923477979136','main-inssiund0015','1.0.3','0','upgrade','update','学生每年学术成果数量','zb','dataapp',TIMESTAMP '2025-06-16 17:02:00','dataapp',TIMESTAMP '2025-06-16 17:02:00'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1934536923477979136');
		-- 修改【字段】：main-inssiund0015-zydm columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-inssiund0015-zydm' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-inssiund0015-zydm(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'biz_major'
				where id = 'main-inssiund0015-zydm';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1934536927097663488','1934536923477979136','upgrade','update','column','main-inssiund0015-zydm','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"zydm","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"ZYDM","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":600,"main-datamodelcolumn-columnLabel":"专业代码","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":3,"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"biz_major"}}','dataapp',TIMESTAMP '2025-06-16 17:02:00','dataapp',TIMESTAMP '2025-06-16 17:02:00'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1934536927097663488');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-inssiund0015-zydm(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = '1.0.3',internal_version='0' WHERE model_id ='main-inssiund0015';
		DBMS_OUTPUT.PUT_LINE('升级模型main-inssiund0015成功,模型版本号更新为:1.0.3,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-inssiund0016 ↓----------------------------------------------
--模型：学生每年实践实习次数 INS_SI_UND_0016 main-inssiund0016
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-inssiund0016'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-inssiund0016版本号:'||v_version);
	IF check_update(v_version, '1.0.3.0') >= 0 THEN
		DBMS_OUTPUT.PUT_LINE('无需升级,模型main-inssiund0016的新版本:1.0.3.0,当前版本'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1934536923477979137','main-inssiund0016','1.0.3','0','upgrade','update','学生每年实践实习次数','zb','dataapp',TIMESTAMP '2025-06-16 17:02:00','dataapp',TIMESTAMP '2025-06-16 17:02:00'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1934536923477979137');
		-- 修改【字段】：main-inssiund0016-zydm columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-inssiund0016-zydm' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-inssiund0016-zydm(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'biz_major'
				where id = 'main-inssiund0016-zydm';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1934536927382876160','1934536923477979137','upgrade','update','column','main-inssiund0016-zydm','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"zydm","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"ZYDM","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":600,"main-datamodelcolumn-columnLabel":"专业代码","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":3,"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"biz_major"}}','dataapp',TIMESTAMP '2025-06-16 17:02:00','dataapp',TIMESTAMP '2025-06-16 17:02:00'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1934536927382876160');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-inssiund0016-zydm(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = '1.0.3',internal_version='0' WHERE model_id ='main-inssiund0016';
		DBMS_OUTPUT.PUT_LINE('升级模型main-inssiund0016成功,模型版本号更新为:1.0.3,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-inssiund0017 ↓----------------------------------------------
--模型：学生每学期选课学分数 INS_SI_UND_0017 main-inssiund0017
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-inssiund0017'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-inssiund0017版本号:'||v_version);
	IF check_update(v_version, '1.0.3.0') >= 0 THEN
		DBMS_OUTPUT.PUT_LINE('无需升级,模型main-inssiund0017的新版本:1.0.3.0,当前版本'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1934536923477979138','main-inssiund0017','1.0.3','0','upgrade','update','学生每学期选课学分数','zb','dataapp',TIMESTAMP '2025-06-16 17:02:00','dataapp',TIMESTAMP '2025-06-16 17:02:00'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1934536923477979138');
		-- 修改【字段】：main-inssiund0017-zydm columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-inssiund0017-zydm' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-inssiund0017-zydm(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'biz_major'
				where id = 'main-inssiund0017-zydm';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1934536927596785664','1934536923477979138','upgrade','update','column','main-inssiund0017-zydm','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"zydm","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"ZYDM","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":600,"main-datamodelcolumn-columnLabel":"专业代码","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":3,"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"biz_major"}}','dataapp',TIMESTAMP '2025-06-16 17:02:00','dataapp',TIMESTAMP '2025-06-16 17:02:00'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1934536927596785664');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-inssiund0017-zydm(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = '1.0.3',internal_version='0' WHERE model_id ='main-inssiund0017';
		DBMS_OUTPUT.PUT_LINE('升级模型main-inssiund0017成功,模型版本号更新为:1.0.3,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-inssiund0018 ↓----------------------------------------------
--模型：学生每学期已获学分数 INS_SI_UND_0018 main-inssiund0018
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-inssiund0018'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-inssiund0018版本号:'||v_version);
	IF check_update(v_version, '1.0.3.0') >= 0 THEN
		DBMS_OUTPUT.PUT_LINE('无需升级,模型main-inssiund0018的新版本:1.0.3.0,当前版本'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1934536923477979139','main-inssiund0018','1.0.3','0','upgrade','update','学生每学期已获学分数','zb','dataapp',TIMESTAMP '2025-06-16 17:02:00','dataapp',TIMESTAMP '2025-06-16 17:02:00'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1934536923477979139');
		-- 修改【字段】：main-inssiund0018-zydm columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-inssiund0018-zydm' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-inssiund0018-zydm(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'biz_major'
				where id = 'main-inssiund0018-zydm';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1934536927810695168','1934536923477979139','upgrade','update','column','main-inssiund0018-zydm','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"zydm","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"ZYDM","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":600,"main-datamodelcolumn-columnLabel":"专业代码","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":3,"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"biz_major"}}','dataapp',TIMESTAMP '2025-06-16 17:02:00','dataapp',TIMESTAMP '2025-06-16 17:02:00'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1934536927810695168');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-inssiund0018-zydm(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = '1.0.3',internal_version='0' WHERE model_id ='main-inssiund0018';
		DBMS_OUTPUT.PUT_LINE('升级模型main-inssiund0018成功,模型版本号更新为:1.0.3,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-inssiund0019 ↓----------------------------------------------
--模型：学生每学期加权平均学分绩点 INS_SI_UND_0019 main-inssiund0019
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-inssiund0019'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-inssiund0019版本号:'||v_version);
	IF check_update(v_version, '1.0.3.0') >= 0 THEN
		DBMS_OUTPUT.PUT_LINE('无需升级,模型main-inssiund0019的新版本:1.0.3.0,当前版本'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1934536923477979140','main-inssiund0019','1.0.3','0','upgrade','update','学生每学期加权平均学分绩点','zb','dataapp',TIMESTAMP '2025-06-16 17:02:00','dataapp',TIMESTAMP '2025-06-16 17:02:00'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1934536923477979140');
		-- 修改【字段】：main-inssiund0019-zydm columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-inssiund0019-zydm' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-inssiund0019-zydm(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'biz_major'
				where id = 'main-inssiund0019-zydm';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1934536928012021760','1934536923477979140','upgrade','update','column','main-inssiund0019-zydm','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"zydm","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"ZYDM","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":600,"main-datamodelcolumn-columnLabel":"专业代码","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":3,"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"biz_major"}}','dataapp',TIMESTAMP '2025-06-16 17:02:00','dataapp',TIMESTAMP '2025-06-16 17:02:00'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1934536928012021760');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-inssiund0019-zydm(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = '1.0.3',internal_version='0' WHERE model_id ='main-inssiund0019';
		DBMS_OUTPUT.PUT_LINE('升级模型main-inssiund0019成功,模型版本号更新为:1.0.3,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-inssiund0020 ↓----------------------------------------------
--模型：学生每学年加权平均学分绩点 INS_SI_UND_0020 main-inssiund0020
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-inssiund0020'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-inssiund0020版本号:'||v_version);
	IF check_update(v_version, '1.0.3.0') >= 0 THEN
		DBMS_OUTPUT.PUT_LINE('无需升级,模型main-inssiund0020的新版本:1.0.3.0,当前版本'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1934536923477979141','main-inssiund0020','1.0.3','0','upgrade','update','学生每学年加权平均学分绩点','zb','dataapp',TIMESTAMP '2025-06-16 17:02:00','dataapp',TIMESTAMP '2025-06-16 17:02:00'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1934536923477979141');
		-- 修改【字段】：main-inssiund0020-zydm columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-inssiund0020-zydm' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-inssiund0020-zydm(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'biz_major'
				where id = 'main-inssiund0020-zydm';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1934536928188182528','1934536923477979141','upgrade','update','column','main-inssiund0020-zydm','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"zydm","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"ZYDM","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":600,"main-datamodelcolumn-columnLabel":"专业代码","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":3,"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"biz_major"}}','dataapp',TIMESTAMP '2025-06-16 17:02:00','dataapp',TIMESTAMP '2025-06-16 17:02:00'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1934536928188182528');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-inssiund0020-zydm(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = '1.0.3',internal_version='0' WHERE model_id ='main-inssiund0020';
		DBMS_OUTPUT.PUT_LINE('升级模型main-inssiund0020成功,模型版本号更新为:1.0.3,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-inssiund0021 ↓----------------------------------------------
--模型：学生专业内综合排名 INS_SI_UND_0021 main-inssiund0021
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-inssiund0021'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-inssiund0021版本号:'||v_version);
	IF check_update(v_version, '1.0.3.0') >= 0 THEN
		DBMS_OUTPUT.PUT_LINE('无需升级,模型main-inssiund0021的新版本:1.0.3.0,当前版本'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1934536923477979142','main-inssiund0021','1.0.3','0','upgrade','update','学生专业内综合排名','zb','dataapp',TIMESTAMP '2025-06-16 17:02:00','dataapp',TIMESTAMP '2025-06-16 17:02:00'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1934536923477979142');
		-- 修改【字段】：main-inssiund0021-zydm columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-inssiund0021-zydm' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-inssiund0021-zydm(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'biz_major'
				where id = 'main-inssiund0021-zydm';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1934536928385314816','1934536923477979142','upgrade','update','column','main-inssiund0021-zydm','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"zydm","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"ZYDM","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":600,"main-datamodelcolumn-columnLabel":"专业代码","main-datamodelcolumn-orderIndex":8,"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"biz_major"}}','dataapp',TIMESTAMP '2025-06-16 17:02:00','dataapp',TIMESTAMP '2025-06-16 17:02:00'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1934536928385314816');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-inssiund0021-zydm(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = '1.0.3',internal_version='0' WHERE model_id ='main-inssiund0021';
		DBMS_OUTPUT.PUT_LINE('升级模型main-inssiund0021成功,模型版本号更新为:1.0.3,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-inssiund0022 ↓----------------------------------------------
--模型：学生每学年思政成绩 INS_SI_UND_0022 main-inssiund0022
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-inssiund0022'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-inssiund0022版本号:'||v_version);
	IF check_update(v_version, '1.0.3.0') >= 0 THEN
		DBMS_OUTPUT.PUT_LINE('无需升级,模型main-inssiund0022的新版本:1.0.3.0,当前版本'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1934536923477979143','main-inssiund0022','1.0.3','0','upgrade','update','学生每学年思政成绩','zb','dataapp',TIMESTAMP '2025-06-16 17:02:00','dataapp',TIMESTAMP '2025-06-16 17:02:00'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1934536923477979143');
		-- 修改【字段】：main-inssiund0022-zydm columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-inssiund0022-zydm' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-inssiund0022-zydm(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'biz_major'
				where id = 'main-inssiund0022-zydm';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1934536928578252800','1934536923477979143','upgrade','update','column','main-inssiund0022-zydm','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"zydm","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"ZYDM","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":600,"main-datamodelcolumn-columnLabel":"专业代码","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":3,"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"biz_major"}}','dataapp',TIMESTAMP '2025-06-16 17:02:00','dataapp',TIMESTAMP '2025-06-16 17:02:00'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1934536928578252800');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-inssiund0022-zydm(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = '1.0.3',internal_version='0' WHERE model_id ='main-inssiund0022';
		DBMS_OUTPUT.PUT_LINE('升级模型main-inssiund0022成功,模型版本号更新为:1.0.3,内部版本号更新为：0');
	END IF;
END;
/
----------------------------------------------↓ 模型 main-inssiund0023 ↓----------------------------------------------
--模型：学生每学年体育成绩 INS_SI_UND_0023 main-inssiund0023
DECLARE
v_version VARCHAR2(50);
BEGIN
	-- 判断现场模型版本是否需要更新
	SELECT NVL((SELECT model_version ||'.'|| internal_version FROM t_da_model_extend WHERE model_id = 'main-inssiund0023'), NULL) INTO v_version FROM dual;
	DBMS_OUTPUT.PUT_LINE('当前模型main-inssiund0023版本号:'||v_version);
	IF check_update(v_version, '1.0.3.0') >= 0 THEN
		DBMS_OUTPUT.PUT_LINE('无需升级,模型main-inssiund0023的新版本:1.0.3.0,当前版本'||v_version||'较新');
		RETURN;
	ELSE
		-- 增量操作日志主表
		insert into t_da_model_change (id,model_id,model_version,internal_version,operation_source,action_type,model_label,model_type,create_by,create_time,update_by,update_time)
		select '1934536923477979144','main-inssiund0023','1.0.3','0','upgrade','update','学生每学年体育成绩','zb','dataapp',TIMESTAMP '2025-06-16 17:02:00','dataapp',TIMESTAMP '2025-06-16 17:02:00'
		from dual
		where not exists (
			select 1
			from t_da_model_change
			where id = '1934536923477979144');
		-- 修改【字段】：main-inssiund0023-zydm columnDict
		DECLARE
		v_count NUMBER:=0;
		BEGIN
			-- 判断用户是否修改，以用户操作为准
			SELECT COUNT(id) INTO v_count FROM T_DA_MODEL_CHANGE_DETAIL 
			WHERE OPERATION_SOURCE='user' and ACTION_TYPE in ('update','delete') and FIELD_TYPE='column' and FIELD_ID='main-inssiund0023-zydm' and FIELD_NAME='main-datamodelcolumn-columnDict';
			IF v_count >= 1 THEN
				DBMS_OUTPUT.PUT_LINE('用户修改了main-inssiund0023-zydm(main-datamodelcolumn-columnDict 字段字典)，跳过更新');
			ELSE
				update lowcode_model_column set column_dict = 'biz_major'
				where id = 'main-inssiund0023-zydm';
				-- 增量操作日志明细表
				insert into t_da_model_change_detail (id,change_id,operation_source,action_type,field_type,field_id,field_name,change_detail,create_by,create_time,update_by,update_time)
				select '1934536928771190784','1934536923477979144','upgrade','update','column','main-inssiund0023-zydm','main-datamodelcolumn-columnDict','{"变更前":{"main-datamodelcolumn-columnJavaname":"zydm","main-datamodelcolumn-columnJavatype":"java.lang.String","main-datamodelcolumn-columnDbname":"ZYDM","main-datamodelcolumn-columnDbtype":12,"main-datamodelcolumn-columnWidth":600,"main-datamodelcolumn-columnLabel":"专业代码","main-datamodelcolumn-columnNotnull":0,"main-datamodelcolumn-orderIndex":3,"main-datamodelcolumn-logicalType":"text","main-datamodelcolumn-columnXtype":"text"},"变更后":{"main-datamodelcolumn-columnDict":"biz_major"}}','dataapp',TIMESTAMP '2025-06-16 17:02:00','dataapp',TIMESTAMP '2025-06-16 17:02:00'
				from dual
				where not exists (
					select 1
					from t_da_model_change_detail
					where id = '1934536928771190784');
			END IF;
		EXCEPTION
		WHEN OTHERS THEN
			DBMS_OUTPUT.PUT_LINE('main-inssiund0023-zydm(main-datamodelcolumn-columnDict 字段字典),sql执行错误:' || SQLERRM);
		END;
		
		--更新模型版本号
		update t_da_model_extend set model_version = '1.0.3',internal_version='0' WHERE model_id ='main-inssiund0023';
		DBMS_OUTPUT.PUT_LINE('升级模型main-inssiund0023成功,模型版本号更新为:1.0.3,内部版本号更新为：0');
	END IF;
END;
/
