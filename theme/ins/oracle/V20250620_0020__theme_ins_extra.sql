/*
 Description		: [教学(INS)]主题域手动创建视图
 Author				: dataapp
 Database           : oracle
*/

-- 视图 V_INS_ZYJBQK
BEGIN
EXECUTE IMMEDIATE 'CREATE OR REPLACE VIEW V_INS_ZYJBQK_JCXX as
SELECT XNZYDM,
       XNZYMC,
       ZYMC,
       ZYDM,
       SSDWMC,
       SSDWH,
       EXTRACT(YEAR FROM SYSDATE) AS TJNF
FROM INS_ZYJBQK
WHERE SZZYNF <= EXTRACT(YEAR FROM SYSDATE)
UNION ALL
SELECT XNZYDM,
       XNZYMC,
       ZYMC,
       ZYDM,
       SSDWMC,
       SSDWH,
       EXTRACT(YEAR FROM SYSDATE) - 1 AS TJNF
FROM INS_ZYJBQK
WHERE SZZYNF <= EXTRACT(YEAR FROM SYSDATE) - 1
UNION ALL
SELECT XNZYDM,
       XNZYMC,
       <PERSON>Y<PERSON>,
       <PERSON><PERSON><PERSON>,
       SSDWMC,
       SSDWH,
       EXTRACT(YEAR FROM SYSDATE) - 2 AS TJNF
FROM INS_ZYJBQK
WHERE SZZYNF <= EXTRACT(YEAR FROM SYSDATE) - 2
UNION ALL
SELECT XNZYDM,
       XNZYMC,
       ZYMC,
       ZYDM,
       SSDWMC,
       SSDWH,
       EXTRACT(YEAR FROM SYSDATE) - 3 AS TJNF
FROM INS_ZYJBQK
WHERE SZZYNF <= EXTRACT(YEAR FROM SYSDATE) - 3
UNION ALL
SELECT XNZYDM,
       XNZYMC,
       ZYMC,
       ZYDM,
       SSDWMC,
       SSDWH,
       EXTRACT(YEAR FROM SYSDATE) - 4 AS TJNF
FROM INS_ZYJBQK
WHERE SZZYNF <= EXTRACT(YEAR FROM SYSDATE) - 4
UNION ALL
SELECT XNZYDM,
       XNZYMC,
       ZYMC,
       ZYDM,
       SSDWMC,
       SSDWH,
       EXTRACT(YEAR FROM SYSDATE) - 5 AS TJNF
FROM INS_ZYJBQK
WHERE SZZYNF <= EXTRACT(YEAR FROM SYSDATE) - 5
UNION ALL
SELECT XNZYDM,
       XNZYMC,
       ZYMC,
       ZYDM,
       SSDWMC,
       SSDWH,
       EXTRACT(YEAR FROM SYSDATE) - 6 AS TJNF
FROM INS_ZYJBQK
WHERE SZZYNF <= EXTRACT(YEAR FROM SYSDATE) - 6';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/