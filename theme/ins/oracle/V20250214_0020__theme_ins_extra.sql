/*
 Description		: [教学(INS)]主题域手动创建视图
 Author				: dataapp
 Database           : oracle
*/

-- 视图 V_INS_MRNF
BEGIN
	EXECUTE IMMEDIATE 'CREATE OR REPLACE VIEW V_INS_MRNF AS
        SELECT CASE WHEN TO_CHAR(SY<PERSON>AT<PERSON>, ''MMDD'') < ''0901''
            THEN EXTRACT(YEAR FROM SYSDATE) - 1
            ELSE EXTRACT(YEAR FROM SYSDATE) END AS TJNF
        FROM DUAL';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

-- 视图 V_INS_MRXNXQ
BEGIN
	EXECUTE IMMEDIATE 'CREATE OR REPLACE VIEW V_INS_MRXNXQ AS
        SELECT
            XNBM,
            SUBSTR(XNBM, 6, 4) AS TJNF,
            XNMC,
            XNXQBM,
            XNXQMC
        FROM
            V_INS_XNXQ
        WHERE
            XNXQPX IN (SELECT MAX(XNXQPX) FROM V_INS_XNXQ GROUP BY XNBM)';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/

-- 视图 V_INS_XNXQ
BEGIN
EXECUTE IMMEDIATE 'CREATE OR REPLACE VIEW V_INS_XNXQ AS
    SELECT T.XNBM,
        T.XNMC,
        T.XNXQBM,
        T.XNXQMC,
        SUBSTR(T.XNBM, 6, 4) AS TJNF,
        DENSE_RANK() OVER(ORDER BY T.XNBM DESC) AS PX, ROW_NUMBER() OVER(ORDER BY T.XNXQBM DESC) AS XNXQPX
    FROM (SELECT DISTINCT T_DA_XL.XNBM,
        T_DA_XL.XNMC,
        T_DA_XL.XNXQBM,
        T_DA_XL.XNXQMC
    FROM T_DA_XL
    WHERE RQ <= TO_CHAR(SYSDATE, ''YYYY-MM-DD'') AND XNXQBM IS NOT NULL) T';
EXCEPTION
WHEN OTHERS THEN IF sqlcode != -955 THEN RAISE;
END IF;
END;
/