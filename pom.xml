<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">  
  <modelVersion>4.0.0</modelVersion>  
  <groupId>com.wisedu.lowcode4j</groupId>  
  <artifactId>lowcode4j</artifactId>  
  <version>1.0.0</version>  
  <packaging>pom</packaging>  
  <properties> 
    <java.version>1.8</java.version>  
    <spring-brick.version>3.1.3-SNAPSHOT</spring-brick.version>  
    <plugin.build.mode>prod</plugin.build.mode>  
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>  
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>  
    <maven.compiler.encoding>UTF-8</maven.compiler.encoding> 
  </properties>  
  <modules> 
    <module>lowcode4j-main</module>  
    <module>lowcode4j-apps</module>  
    <module>modelpub</module>  
    <module>toolpub</module>  
    <!--    <module>portraittoolpub</module>-->  
    <!--    <module>custom-hr</module>-->  
    <!--    <module>hvspub</module>-->  
    <!--    <module>jspub</module>-->  
    <!--    <module>teapub</module>-->  
    <!--    <module>stupub</module>-->  
<!--    <module>qcapub</module>-->
    <module>jwdatapub</module>
  </modules>  
  <profiles> 
    <profile> 
      <!-- 开发环境 -->  
      <id>dev</id>  
      <properties> 
        <!-- 一些自定义配置，如果选中改环境，则可以使用${env}或${profile}占位符使用，但必须配置build才会生效 -->  
        <env>dev</env>  
        <profile>dev</profile> 
      </properties>  
      <activation> 
        <!-- 默认激活该profile节点-->  
        <activeByDefault>true</activeByDefault> 
      </activation> 
    </profile>  
    <profile> 
      <!-- 发布环境 -->  
      <id>prod</id>  
      <properties> 
        <env>prod</env>  
        <profile>prod</profile> 
      </properties> 
    </profile> 
  </profiles>  
  <dependencyManagement> 
    <dependencies> 
      <dependency> 
        <groupId>com.wisedu.lowcode4j</groupId>  
        <artifactId>lowcode4j-dependencies</artifactId>  
        <version>1.2.6</version>  
        <type>pom</type>  
        <scope>import</scope> 
      </dependency>  
      <dependency> 
        <groupId>com.squareup.okhttp3</groupId>  
        <artifactId>okhttp</artifactId>  
        <version>4.12.0</version>  
        <scope>compile</scope> 
      </dependency> 
    </dependencies> 
  </dependencyManagement>  
  <build> 
    <plugins> 
      <plugin> 
        <artifactId>maven-compiler-plugin</artifactId>  
        <configuration> 
          <target>${java.version}</target>  
          <source>${java.version}</source>  
          <encoding>UTF-8</encoding> 
        </configuration> 
      </plugin>  
      <plugin> 
        <groupId>org.apache.maven.plugins</groupId>  
        <artifactId>maven-jar-plugin</artifactId>  
        <configuration> 
          <archive> 
            <addMavenDescriptor>false</addMavenDescriptor> 
          </archive> 
        </configuration> 
      </plugin> 
    </plugins> 
  </build>  
  <!-- 使用 aliyun 的 Maven 源，提升下载速度 -->  
  <repositories> 
    <repository> 
      <id>wisedu</id>  
      <url>http://***********:9999/repository/maven-public/</url>  
      <releases> 
        <enabled>true</enabled> 
      </releases>  
      <snapshots> 
        <enabled>true</enabled>  
        <updatePolicy>always</updatePolicy> 
      </snapshots> 
    </repository>  
    <repository> 
      <id>cdsp</id>  
      <url>http://***********:8081/nexus/content/repositories/minos-snapshot/</url>  
      <releases> 
        <enabled>true</enabled> 
      </releases>  
      <snapshots> 
        <enabled>true</enabled>  
        <updatePolicy>always</updatePolicy> 
      </snapshots> 
    </repository>  
    <repository> 
      <id>cdsp2</id>  
      <url>http://***********:8081/nexus/content/repositories/wecloud/</url>  
      <releases> 
        <enabled>true</enabled> 
      </releases>  
      <snapshots> 
        <enabled>true</enabled>  
        <updatePolicy>always</updatePolicy> 
      </snapshots> 
    </repository> 
  </repositories>  
  <pluginRepositories> 
    <pluginRepository> 
      <id>wisedu</id>  
      <url>http://***********:9999/repository/maven-public/</url> 
    </pluginRepository>  
    <pluginRepository> 
      <id>cdsp</id>  
      <url>http://***********:8081/nexus/content/repositories/minos-snapshot/</url> 
    </pluginRepository>  
    <pluginRepository> 
      <id>cdsp2</id>  
      <url>http://***********:8081/nexus/content/repositories/wecloud/</url> 
    </pluginRepository> 
  </pluginRepositories> 
</project>
