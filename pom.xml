<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.wisedu.lowcode4j</groupId>
        <artifactId>lowcode4j</artifactId>
        <version>1.0.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <artifactId>teapub</artifactId>
    <packaging>jar</packaging>
    <version>1.0.0</version>

    <dependencies>
        <dependency>
            <groupId>com.gitee.starblues</groupId>
            <artifactId>spring-brick</artifactId>
            <version>${spring-brick.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.thymeleaf</groupId>
                    <artifactId>thymeleaf</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.thymeleaf</groupId>
            <artifactId>thymeleaf</artifactId>
        </dependency>
        <dependency>
            <groupId>org.attoparser</groupId>
            <artifactId>attoparser</artifactId>
        </dependency>
        <dependency>
            <groupId>org.unbescape</groupId>
            <artifactId>unbescape</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <!--底座p级包-->
        <dependency>
            <groupId>com.wisedu.lowcode4j</groupId>
            <artifactId>lowcode4j-p-upms</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.wisedu.lowcode4j</groupId>
            <artifactId>development-main-starter</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.wisedu.lowcode4j</groupId>
            <artifactId>bizpub</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.wisedu.lowcode4j</groupId>
            <artifactId>lowcode4j-p-design</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wisedu.lowcode4j</groupId>
            <artifactId>toolpub</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>


</project>
