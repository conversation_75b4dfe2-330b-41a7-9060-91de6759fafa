<?xml version="1.0" encoding="utf-8"?>
<sqltoy xmlns="http://www.sagframe.com/schema/sqltoy"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.sagframe.com/schema/sqltoy http://www.sagframe.com/schema/sqltoy/sqltoy.xsd">

    <sql id="disable_role_topic_model_columns">
        <value>
            <![CDATA[
                UPDATE T_DQAI_MODEL_COLUMN_ROLE
                   SET COLUMN_ALLOWED = '0'
                 WHERE MODEL_ROLE_ID IN (SELECT id
                                           FROM T_DQAI_MODEL_ROLE
                                          WHERE TOPIC_MODEL_ID IN (SELECT id
                                                                     FROM T_DQAI_TOPIC_MODEL
                                                                    WHERE topic_id = :topicId
                                                                   )
                                                          AND USER_ROLE_ID = :userRoleId
                                         )
            ]]>
        </value>
    </sql>

    <sql id="disable_role_topic_models">
        <value>
            <![CDATA[
                UPDATE T_DQAI_MODEL_ROLE
                   SET visible_role = '0' ,column_role = '0',row_role = '1',row_role_ref_column_dbname = ''
                 WHERE TOPIC_MODEL_ID IN (SELECT id FROM T_DQAI_TOPIC_MODEL WHERE topic_id = :topicId)
                  AND USER_ROLE_ID = :userRoleId
            ]]>
        </value>
    </sql>

    <sql id="update_model_role_with_columndel">
        <value>
            <![CDATA[
                UPDATE T_DQAI_MODEL_ROLE SET COLUMN_ROLE = '2'
                 WHERE topic_model_id IN (SELECT id FROM (SELECT tdmr.ID ,count(*)
                                                            FROM T_DQAI_MODEL_COLUMN_ROLE tdmcr ,T_DQAI_MODEL_ROLE tdmr
                                                           WHERE tdmcr.MODEL_ROLE_ID = tdmr.id
                                                             AND tdmcr.COLUMN_ALLOWED  = 0
                                                             AND tdmr.topic_model_id = :topicModelId
                                                           GROUP BY tdmr.ID
                                                           HAVING count(*) > 1
                                                           )
                                           )
            ]]>
        </value>
    </sql>

    <sql id="clear_model_column_synonyms">
        <value>
            <![CDATA[
                UPDATE T_DQAI_MODEL_COLUMN
                   SET COLUMN_SYNONYMS  = '' ,COLUMN_SEMANTICS = ''
                 WHERE TOPIC_MODEL_ID = :topicModelId
                   AND model_column_id = :modelColumnId
            ]]>
        </value>
    </sql>

    <sql id="delete_model_columns">
        <value>
            <![CDATA[
                DELETE FROM T_DQAI_MODEL_COLUMN WHERE TOPIC_MODEL_ID = :topicModelId AND model_column_id in (:modelColumnIds)
            ]]>
        </value>
    </sql>

    <sql id="delete_model_column">
        <value>
            <![CDATA[
                DELETE FROM T_DQAI_MODEL_COLUMN WHERE TOPIC_MODEL_ID = :topicModelId AND model_column_id = :modelColumnId
            ]]>
        </value>
    </sql>


    <sql id="delete_model_role_column">
        <value>
            <![CDATA[
                DELETE FROM T_DQAI_MODEL_COLUMN_ROLE
                 WHERE model_role_id IN (SELECT id FROM T_DQAI_MODEL_ROLE WHERE topic_model_id = :topicModelId)
                   AND model_column_id = :modelColumnId
            ]]>
        </value>
    </sql>

    <sql id="update_model_role">
        <value>
            <![CDATA[
              UPDATE T_DQAI_MODEL_ROLE SET column_role  = '2' WHERE column_role = '1' AND TOPIC_MODEL_ID = :topicModelId
            ]]>
        </value>
    </sql>

    <sql id="query_role_topics_by_roleIds">
        <value>
            <![CDATA[
                SELECT * FROM T_DQAI_TOPIC tdt
                 WHERE TDT.ID IN (SELECT topic_id
                   FROM T_DQAI_TOPIC_MODEL tdtm
                  WHERE EXISTS(SELECT 1 FROM T_DQAI_MODEL_ROLE tdmr
                                WHERE tdmr.topic_model_id = tdtm.id
                                  AND tdmr.visible_role = '1'
                                  AND tdmr.user_role_id in (:userRoleIds))
                 )
            ]]>
        </value>
    </sql>

    <sql id="query_role_topics">
        <value>
            <![CDATA[
                SELECT * FROM T_DQAI_TOPIC tdt
                 WHERE TDT.ID IN (SELECT topic_id
                   FROM T_DQAI_TOPIC_MODEL tdtm
                  WHERE EXISTS(SELECT 1 FROM T_DQAI_MODEL_ROLE tdmr
                                WHERE tdmr.topic_model_id = tdtm.id
                                  AND tdmr.visible_role = '1'
                                  AND tdmr.user_role_id = :userRoleId)
                 )
            ]]>
        </value>
    </sql>

    <sql id="delete_role_models">
        <value>
            <![CDATA[
                DELETE FROM T_DQAI_MODEL_ROLE
                 WHERE user_role_id = :userRoleId
            ]]>
        </value>
    </sql>

    <sql id="delete_role_model_columns">
        <value>
            <![CDATA[
                DELETE FROM T_DQAI_MODEL_COLUMN_ROLE
                 WHERE model_role_id IN (SELECT id FROM T_DQAI_MODEL_ROLE WHERE user_role_id = :userRoleId)
            ]]>
        </value>
    </sql>

    <sql id="query_topic_model_columns">
        <value>
            <![CDATA[
                SELECT * FROM T_DQAI_MODEL_COLUMN tdmc WHERE TOPIC_MODEL_ID IN (:modelIds)
            ]]>
        </value>
    </sql>

    <sql id="update_topic_model_ai_status">
        <value>
            <![CDATA[
                UPDATE T_DQAI_TOPIC_MODEL
                   SET AI_STUDY_FLAG = :studyFlag
                WHERE id IN (:ids)
            ]]>
        </value>
    </sql>
    
    <sql id="dqai_update_chatdb_config">
        <value>
            <![CDATA[
                UPDATE T_DQAI_CHATDB_CONFIG SET
                  CONFIG_VALUE = :configValue
                WHERE topic_id = :topicId
            ]]>
        </value>
    </sql>

    <sql id="dqai_query_model_role">
        <value>
            <![CDATA[
                SELECT TDMR.ID, TDMR.USER_ROLE_ID, TDMR.VISIBLE_ROLE, TDMR.COLUMN_ROLE, TDMR.ROW_ROLE,
                       TDMR.ROW_ROLE_REF_COLUMN_DBNAME,
                       tdt.TOPIC_NAME,tdt.id as TOPIC_ID,tdtm.id as TOPIC_MODEL_ID,
                       tdtm.MODEL_ID, tdtm.MODEL_TABLE, tdtm.MODEL_NAME
                  FROM T_DQAI_TOPIC_MODEL tdtm
                LEFT JOIN T_DQAI_TOPIC tdt ON tdt.id = TDTM.TOPIC_ID
                LEFT JOIN T_DQAI_MODEL_ROLE tdmr ON tdmr.TOPIC_MODEL_ID = tdtm.ID and tdmr.user_role_id in (:roleIds)
                WHERE 1 = 1
                #[AND @blank(:querySetting) (${querySetting})]
                ORDER BY tdtm.CREATE_TIME ,tdtm.id desc
            ]]>
        </value>
    </sql>
</sqltoy>
