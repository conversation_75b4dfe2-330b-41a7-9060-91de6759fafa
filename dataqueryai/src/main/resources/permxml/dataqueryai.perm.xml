<?xml version="1.0" encoding="UTF-8"?>
<!--菜单（分类）-->
<groups>
    <group id="dataqueryai_group" name="AI问数" showOrder="3">
        <app id="dataqueryai" name="AI问数" menuPattern="pc" groupId="dataqueryai_group"><!--菜单（页面）-->

            <menu bindType="default" formRouterName="zhcx" id="dataqueryai-zhcx" name="AI查询" showOrder="5">
                <routerParams>{   "appCode": "dataqueryai",   "pageCode": "zhcx"}</routerParams>
                <!-- 权限字-片段 -->
                <permCode name="AI查询" permCode="formzhcx:fragmentzhcx" permCodeId="fragmentdataqueryai-zhcx" permCodeType="1">
                    <perm name="综合查询接口" url="/aiChatRecord/**"/>
                    <perm name="查询主题域列表" url="/plugins/dataqueryai/topic/**"/>
                    <perm name="获取推荐问题" url="/plugins/dataqueryai/suggestQuestion/getUserSuggestQuestion"/>
                    <perm name="智能问答" url="/plugins/dataqueryai/chat/**"/>
                </permCode>
                <!-- 权限字-表单 -->
                <permCode name="AI查询" permCode="zhcx" permCodeId="dataqueryai-zhcx" permCodeType="0"/>
                <btn id="dataqueryai-zhcx-sql" menuPattern="pc" name="查看SQL">
                    <permCode name="查看SQL" permCode="formzhcx:fragmentzhcx:showsql" permCodeId="dataqueryai-zhcx-sql"/>
                </btn>
            </menu>

            <menus id="dataqueryai-mxxl" name="模型数据训练" moduleCode="mxxl" showOrder="10">
                <menu bindType="default" formRouterName="modelSubjectArea" id="dataqueryai-modelSubjectArea" name="模型主题域" showOrder="10">
                    <routerParams>{   "appCode": "dataqueryai",   "pageCode": "modelSubjectArea"}</routerParams>
                    <!-- 权限字-片段 -->
                    <permCode name="模型主题域" permCode="formmodelSubjectArea:fragmentmodelSubjectArea" permCodeId="fragmentdataqueryai-modelSubjectArea" permCodeType="1">
                        <perm name="主题域管理" url="/plugins/dataqueryai/topic/**"/>
                        <perm name="模型管理" url="/plugins/dataqueryai/model/**"/>
                        <perm name="文件上传接口" url="/admin/file/dataqueryai/upload"/>
                        <perm name="大语言模型配置相关接口" url="/plugins/dataqueryai/chatDbConfig/**"/>
                    </permCode>
                    <!-- 权限字-表单 -->
                    <permCode name="模型主题域" permCode="formmodelSubjectArea" permCodeId="dataqueryai-formmodelSubjectArea" permCodeType="0"/>
                </menu>

                <menu bindType="default" formRouterName="universityBackground" id="dataqueryai-universityBackground" name="高校行业背景知识" showOrder="20">
                    <routerParams>{   "appCode": "dataqueryai",   "pageCode": "universityBackground"}</routerParams>
                    <!-- 权限字-片段 -->
                    <permCode name="高校行业背景知识" permCode="formuniversityBackground:fragmentuniversityBackground" permCodeId="fragmentdataqueryai-universityBackground" permCodeType="1">
                        <perm name="背景知识管理" url="/plugins/dataqueryai/bgKnowledge/**"/>
                    </permCode>
                    <!-- 权限字-表单 -->
                    <permCode name="高校行业背景知识" permCode="formmuniversityBackground" permCodeId="dataqueryai-formmuniversityBackground" permCodeType="0"/>
                </menu>
            </menus>

            <menu bindType="default" formRouterName="chatDbConfigContainer" id="dataqueryai-chatDbConfigContainer" name="大语言模型配置" showOrder="20">
                <routerParams>{   "appCode": "dataqueryai",   "pageCode": "chatDbConfigContainer"}</routerParams>
                <!-- 权限字-片段 -->
                <permCode name="大语言模型配置" permCode="formchatDbConfigContainer:fragmentchatDbConfigContainer" permCodeId="fragmentdataqueryai-chatDbConfigContainer" permCodeType="1">
                    <perm name="大语言模型配置相关接口" url="/plugins/dataqueryai/chatDbConfig/**"/>
                </permCode>
                <!-- 权限字-表单 -->
                <permCode name="大语言模型配置" permCode="chatDbConfigContainer" permCodeId="dataqueryai-chatDbConfigContainer" permCodeType="0"/>
            </menu>

            <menus id="dataqueryai-qxgl" name="权限管理" moduleCode="qxgl" showOrder="30">
                <menu id="dataqueryai-appUserGroupAuth" name="功能权限管理" bindType="default" formRouterName="appUserGroupAuth" showOrder="40">
                    <routerParams>
                        {"appDomain":"da","appCodeParam":"dataqueryai","appCode":"main","pageCode":"appUserGroupAuth"}
                    </routerParams>
                    <permCode name="应用用户组授权" permCodeId="1724729016113430528" import="1"/>
                </menu>

                <menu bindType="default" formRouterName="modelPermission" id="dataqueryai-modelPermission" name="模型权限管理" showOrder="50">
                    <routerParams>{   "appCode": "dataqueryai",   "pageCode": "modelPermission"}</routerParams>
                    <!-- 权限字-片段 -->
                    <permCode name="模型权限管理" permCode="formmodelPermission:fragmentmodelPermission" permCodeId="fragmentdataqueryai-modelPermission" permCodeType="1">
                        <perm name="群组管理相关接口" url="/plugins/dataqueryai/model/role/**"/>
                        <perm name="用户组列表" url="/toolpub/userGroup/appAdmin/**"/>

                    </permCode>
                    <!-- 权限字-表单 -->
                    <permCode name="模型权限管理" permCode="modelPermission" permCodeId="dataqueryai-modelPermission" permCodeType="0"/>
                </menu>
            </menus>

            <menu bindType="default" formRouterName="recommendQuestion" id="dataqueryai-recommendQuestion" name="推荐问题配置" showOrder="40">
                <routerParams>{   "appCode": "dataqueryai",   "pageCode": "recommendQuestion"}</routerParams>
                <!-- 权限字-片段 -->
                <permCode name="推荐问题配置" permCode="formrecommendQuestion:fragmentrecommendQuestion" permCodeId="fragmentdataqueryai-recommendQuestion" permCodeType="1">
                    <perm name="获取主题列表" url="/plugins/dataqueryai/topic/list"/>
                    <perm name="推荐问题配置" url="/plugins/dataqueryai/suggestQuestion/**"/>
                    <perm name="用户组列表" url="/toolpub/userGroup/appAdmin/**"/>
                </permCode>
                <!-- 权限字-表单 -->
                <permCode name="推荐问题配置" permCode="recommendQuestion" permCodeId="dataqueryai-recommendQuestion" permCodeType="0"/>
            </menu>

            <menu bindType="default" formRouterName="chatHiastory" id="dataqueryai-chatHiastory" name="问答历史记录" showOrder="50">
                <routerParams>{   "appCode": "dataqueryai",   "pageCode": "chatHiastory"}</routerParams>
                <!-- 权限字-片段 -->
                <permCode name="问答历史记录" permCode="formchatHiastory:fragmentchatHiastory" permCodeId="fragmentdataqueryai-chatHiastory" permCodeType="1">
                    <perm name="历史记录列表" url="/aiChatRecord/**"/>
                </permCode>
                <!-- 权限字-表单 -->
                <permCode name="问答历史记录" permCode="chatHiastory" permCodeId="dataqueryai-chatHiastory" permCodeType="0"/>
            </menu>

        </app>
    </group>
</groups>
