define(function (require) {
  return {
    /**
     * 监听指定对象的值发生变化，然后执行特定方法
     */
    pageWatch: {
      //  'globalVars.name':function(newVal,oldVal){
      //      console.log('值改变',newVal,oldVal)
      //  }
    },
    /**
     * 监听指定对象的值发生变化，然后执行特定方法
     */
    pageComputed: {
      // getNewName:function(){
      //   return this.globalVars.name+'新的'
      //}
    },
    // var1:"变量",//响应式变量，该变量不能加到组件参数内,但是写到组件模板里
    /**
     * 响应式变量，当值变化时可以影响所有绑定的值,建议把需要绑定到组件参数里的变量申明到这里面
     */
    globalVars: {
      // name:'一个变量' //响应式变量name,组件内使用时{{globalVars.name}}
    },
    /**
     * 页面被重新激活时调用
     */
    pageActivated: function () {
      //console.log('页面激活')  
    },
    /**
     * 页面失去激活被缓存时调用
     */
    pageDeactivated: function () {
      //console.log('页面失活')  
    },
    /**
     * 固定方法，页面js初始化完成后调用,当前能修改js变量，修改组件初始化属性或者设置组件默认值
     */
    pageCreated: function () {
      //console.log('页面js初始化完成后调用')
      window.$EventBus.$on('cancel-btn-loading', this.setLoading)
    },
    /**
     * 固定方法，页面准备完成后调用，当前可以操作组件属性，调用未隐藏组件实例方法
     */
    pageReady: function () {
      //console.log('页面准备完成后调用')
    },
    /**
     * 固定方法，页面销毁前调用
     */
    pageDestroy: function () {
      //console.log('页面销毁前调用')
      window.$EventBus.$off('cancel-btn-loading', this.setLoading)
    },
    setLoading(bool){
      this.$page("buttons_4hk9lzye").setBtnsProp({
        'btn_1742809912908': { loading: bool },
        "dhhedzvl": {loading: bool}
      })
    },

    /**
     * 描述：保存
     * @param{event}  {btn}:点击的按钮; return:

     */
    action_ev_m9dzqp52: function (event) {
      var self = this;
      window.$EventBus.$emit('submit-chat-config')
      this.setLoading(true)
    },


    /**
     * 描述：上传配置
     * @param{event}  {btn}:点击的按钮; return:

     */
    action_ev_ykx0cn3a: function (event) {
      var self = this;
      this.$FormConfirm({
        title: '上传提示',
        iconColor: '#165dff',
        message: '确认上传配置吗',
        confirmText: '确认',
        confirmType: 'primary',
        cancelText: '取消'
      }).then(res=>{
        window.$EventBus.$emit('report-chat-config')
      })
    },

  }
})