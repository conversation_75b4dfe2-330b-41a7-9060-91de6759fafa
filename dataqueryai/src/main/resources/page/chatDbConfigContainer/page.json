{"list": [{"isRoot": true, "type": "row", "comType": "page", "__tree_label": "页面", "__tree_node_key": "9897157947804942", "key": "page_39t70tv6", "uuid": "uuid_kjl3i6s5", "children": [{"__tree_node_key": "45497826293814536", "key": "row_xlsjm2rk", "__tree_label": "区域容器", "comType": "row", "type": "row", "isCover": false, "children": [{"__tree_node_key": "2159859258216359", "key": "col_ugt0yx8o", "__tree_label": "区块容器", "comType": "col", "type": "col", "isCover": false, "children": [{"uuid": "uuid_tgi6o44h", "key": "title_v9avdqnv", "com": "res-title", "comType": "title", "icon": "pm-icon-title", "comClassify": "com", "dataType": "string", "options": {"readonly": false, "showIcon": "false", "icon": "", "AfterIcon": "", "title": "课题设置", "titleSize": 16, "desc": "", "bindCom": {}, "hidden": false, "anchorLevel": 1, "enabledAnchor": false, "undefined": ""}, "events": {"mounted": {"tip": "组件挂载"}, "beforeDestroy": {"tip": "组件销毁"}}, "name": "标题", "isCover": false, "dataOptions": {"remote": false, "remoteType": "dataset", "remoteModel": "", "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}}, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "res-title", "pageCode": "chatDbConfigContainer"}], "options": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "pageCode": "chatDbConfigContainer", "uuid": "uuid_1pj4dtg1"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "0 0 0 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": false, "paddingTop": 0, "paddingBottom": "0", "paddingLeft": 0, "paddingRight": 0, "hidden": false, "bgType": "bgcolor", "backgroundColor": "", "borderRadius": 0, "borderPos": "borderBottom", "borderColor": "#F0F0F0", "borderWidth": 1, "borderStyle": "solid"}, "showTitle": false, "title": "容器标题", "layout": "grid", "pageCode": "chatDbConfigContainer", "uuid": "uuid_8hikuk8i"}, {"__tree_node_key": "07071674627282976", "key": "row_v59tl1fk", "__tree_label": "区域容器", "comType": "row", "type": "row", "isCover": false, "children": [{"__tree_node_key": "3249427860745848", "key": "col_rp5o9vre", "__tree_label": "区块容器", "comType": "col", "type": "col", "isCover": false, "children": [{"uuid": "uuid_91yvzo6s", "key": "page-runtime_2h5t8wzq", "com": "page-runtime", "comType": "page-runtime", "icon": "pm-icon-page-runtime", "dataType": "object", "options": {"readonly": false, "customClass": "", "labelWidth": 100, "isLabelWidth": false, "hidden": false, "pageInfo": {"pageCode": "chatDbConfig", "pageType": "default", "appCode": "dataqueryai", "label": "大语言模型", "value": "dataqueryai-chatDbConfig"}, "values": {}, "pageParams": {}, "inlinePage": true, "appCode": "dataqueryai"}, "events": {"mounted": {"tip": "组件挂载"}, "pageReady": {"tip": "页面加载完成"}, "pageBeforeDestroy": {"tip": "页面开始销毁"}, "beforeDestroy": {"tip": "组件销毁"}}, "name": "页面", "isCover": false, "dataOptions": {"remote": false, "remoteType": "dataset", "remoteModel": "", "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}}, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "page-runtime", "pageCode": "chatDbConfigContainer"}], "options": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "pageCode": "chatDbConfigContainer", "uuid": "uuid_jto3wfm9"}], "options": {"marginTop": "0", "marginBottom": "0", "marginLeft": 0, "marginRight": 0, "boxShadow": "0 0 0 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": false, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false, "bgType": "bgcolor", "backgroundColor": "", "borderRadius": 0}, "showTitle": false, "title": "容器标题", "layout": "grid", "pageCode": "chatDbConfigContainer", "uuid": "uuid_ikxwqpod"}, {"__tree_node_key": "9849445850322491", "key": "row_2llsankh", "__tree_label": "区域容器", "comType": "row", "type": "row", "isCover": false, "children": [{"__tree_node_key": "7983937859573089", "key": "col_owqg786n", "__tree_label": "区块容器", "comType": "col", "type": "col", "isCover": false, "children": [{"uuid": "uuid_blc2nmyt", "key": "buttons_4hk9lzye", "com": "res-buttons", "comType": "buttons", "icon": "pm-icon-btns", "comClassify": "form", "options": {"readonly": false, "size": "", "schema": "button", "noMoreClick": true, "buttonList": [{"label": "保存", "id": "btn_1742809912908", "uuid": "btn_1742809912908", "loading": false, "type": "", "func": {"name": "action_ev_m9dzqp52", "enName": "保存", "params": [{"name": "event", "des": "{btn}:点击的按钮;"}]}}, {"label": "上传配置", "id": "dhhedzvl", "uuid": "ipksue7j", "type": "primary", "loading": false, "func": {"name": "action_ev_ykx0cn3a", "params": [{"name": "event", "des": "{btn}:点击的按钮;"}], "enName": "上传配置"}, "children": []}], "renderFunc": {}, "hideIcon": false}, "events": {"mounted": {"tip": "组件挂载"}, "click": {"tip": "按钮点击时"}, "beforeDestroy": {"tip": "组件销毁"}}, "name": "按钮组", "isCover": false, "dataOptions": {"remote": false, "remoteType": "dataset", "remoteModel": "", "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}}, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "res-buttons", "pageCode": "chatDbConfigContainer"}], "options": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "pageCode": "chatDbConfigContainer", "uuid": "uuid_am503njf"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "0 0 0 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": false, "paddingTop": "20", "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false, "bgType": "bgcolor", "backgroundColor": "", "borderRadius": 0, "borderPos": "borderTop", "borderColor": "#F0F0F0", "borderWidth": 1, "borderStyle": "solid"}, "showTitle": false, "title": "容器标题", "layout": "grid", "pageCode": "chatDbConfigContainer", "uuid": "uuid_5b<PERSON><PERSON><PERSON>i"}], "options": {"marginTop": 0, "marginBottom": 12, "marginLeft": 0, "marginRight": 0, "widthType": "0", "maxWidth": 0, "minWidth": 0, "width": "260px"}, "pageCode": "chatDbConfigContainer"}], "leftList": [{"isRoot": true, "type": "row", "comType": "page", "__tree_label": "页面", "__tree_node_key": "137739106130794", "key": "page_p5vquvpn", "uuid": "uuid_dokzqem8", "children": [], "pageCode": "chatDbConfigContainer"}], "rightList": [{"isRoot": true, "type": "row", "comType": "page", "__tree_label": "页面", "__tree_node_key": "9748745948011994", "key": "page_jntpb8qr", "uuid": "uuid_9lq2co3l", "children": [], "pageCode": "chatDbConfigContainer"}], "config": {"dataSource": [], "platform": "pc", "layout": "default", "leftWidth": 260, "rightWidth": 260, "foldingSwitchTop": 50, "foldingSwitch": false}, "dialogJson": [], "suspendJson": []}