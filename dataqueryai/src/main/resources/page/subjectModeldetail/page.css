.title_amectgti{
    color: rgba(0, 0, 0, 0.85);
    border-bottom: 1px solid #F0F0F0;
}
.study-status-tag{
    display: flex;
    .study{
        padding: 2px 8px;
        border-radius: 2px;
        background: rgba(0, 0, 0, 0.06);
        box-sizing: border-box;
        border: 1px solid rgba(0, 0, 0, 0.15);
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
    }
    .has-study{
        background: rgba(22, 93, 255, 0.1);
        border: 1px solid #165DFF;
        color: #165DFF;
    }
    .ing-study{
        color: #e6a23c;
        background: #fdf6ec;
        border: 1px solid #e6a23c;
    }
    .modify{
        padding: 2px 8px;
        border-radius: 2px;
        background: rgba(219, 146, 37, 0.1);
        box-sizing: border-box;
        border: 1px solid #DB9225;
        color: #DB9225;
        font-size: 14px;
        margin-left: 6px;
    }
}
.col_n67f1ce9{
    background: #F7F8FA;
    padding: 20px !important;
    .model-detail{
        margin-bottom: 20px;
    }
    .detail-title{
        display: flex;
        justify-content: space-between;
        .span-title{
            color: rgba(0, 0, 0, 0.85);
            font-weight: 700;
        }
        .edit-btn{
            cursor: pointer;
            color: #165dff;
        }
    }
}
.dialog_8edu61ev{
    .form-readonly-text{
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
    }
    .widget-view .el-form-item__content{
        flex: 1;
        width: 0;
    }
}
.dataqueryai_modelSubjectArea{
    .inside-dialog-model .dialog-footer{
        height: 55px;
    }
}