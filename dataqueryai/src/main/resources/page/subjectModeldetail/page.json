{"list": [{"isRoot": true, "type": "row", "comType": "page", "__tree_label": "页面", "__tree_node_key": "07821700764770356", "key": "page_z05pxhp7", "uuid": "uuid_d1ak8eww", "children": [{"__tree_node_key": "15887084265855345", "key": "row_nw4gm4pf", "__tree_label": "区域容器", "comType": "row", "type": "row", "isCover": false, "children": [{"__tree_node_key": "886041095183902", "key": "col_qejn76n8", "__tree_label": "区块容器", "comType": "col", "type": "col", "isCover": false, "children": [{"uuid": "uuid_oc9n36lb", "key": "title_amectgti", "com": "res-title", "comType": "title", "icon": "pm-icon-title", "comClassify": "com", "dataType": "string", "options": {"readonly": false, "showIcon": "false", "icon": "", "AfterIcon": "", "title": "模型列表", "titleSize": 16, "desc": "", "bindCom": {}, "hidden": true, "anchorLevel": 1, "enabledAnchor": false, "undefined": ""}, "events": {"mounted": {"tip": "组件挂载"}, "beforeDestroy": {"tip": "组件销毁"}}, "name": "标题", "isCover": false, "dataOptions": {"remote": false, "remoteType": "dataset", "remoteModel": "", "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}}, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "res-title", "pageCode": "subjectModeldetail"}, {"uuid": "uuid_xgotnjh9", "key": "adv-search_q0t6m2pw", "com": "adv-search", "comType": "adv-search", "icon": "pm-icon-advancedSearch", "comClassify": "model", "dataType": "object", "options": {"readonly": false, "interacTable": "adv-table_e9vp8qyz", "limit": 2, "keyWordsLabel": "关键字", "hideKeyWords": true, "comLinkEnabled": true, "labelWidth": 100, "keyWordsHighlight": true, "size": "", "itemWidth": 285, "singleItemWidth": 400, "tileItemWidth": 320, "searchTime": "", "conditionType": "", "hidden": false, "readyDoSearch": false, "hideNewFields": false, "closeModelSort": false, "showAdv": false, "fullscreen": false, "isCondition": false, "isMore": false, "beforeRender": {}, "showHidden": true, "appendToBody": true, "undefined": "", "beforeSearch": {}}, "events": {"mounted": {"tip": "组件挂载"}, "inited": {"tip": "模型和数据获取完成"}, "search": {"tip": "搜索之前，参数为querySetting(同步)"}, "before-reset": {"tip": "重置之前(同步)"}, "async-before-reset": {"tip": "重置之前(异步，参数为回调函数，执行查询需要调用回调)"}, "reset": {"tip": "重置后"}, "adv-reset": {"tip": "高级筛选重置后(需开启高级筛选功能)"}, "collapse": {"tip": "收起条件后"}, "expand": {"tip": "展开条件后"}, "item-change": {"tip": "每一项的值触发change后"}, "dict-loaded": {"tip": "每当有某一项的字典数据请求完成后"}, "dict-change": {"tip": "字典项控件的值发生改变时触发"}, "beforeDestroy": {"tip": "组件销毁"}}, "dataOptions": {"remote": true, "remoteType": "model", "remoteModel": {"actionType": "search", "id": "dataqueryai-dataqueryaitopicmodel", "url": "/admin/model/design/dataqueryai/perm/dataqueryaitopicmodel", "modelCascades": "", "modelName": "dataqueryaitopicmodel", "modelApp": "dataqueryai"}, "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "prefix": "search", "dataSource": {"type": "in", "url": "", "code": "", "params": {}}, "columnsModel": [{"name": "modelTable"}, {"name": "modelName"}, {"name": "topicName"}, {"name": "topicId"}, {"name": "modelId"}, {"name": "modelType"}, {"name": "modelSynonyms"}, {"name": "modelSemantics"}, {"name": "aiStudyFlag"}, {"name": "modifyFlag"}, {"name": "id"}, {"name": "createBy"}, {"name": "createTime"}, {"name": "updateBy"}, {"name": "updateTime"}], "modelConfig": {"modelTable": {"placeholder": "请输入", "search.omitted": 0, "fuzzySearch": 1}, "modelName": {"placeholder": "请输入", "search.omitted": 0, "fuzzySearch": 1}, "topicName": {"search.hidden": 1}, "topicId": {"search.hidden": 1}, "modelId": {"search.hidden": 1}, "modelType": {"search.hidden": 1}, "modelSynonyms": {"search.hidden": 1}, "modelSemantics": {"search.hidden": 1}, "aiStudyFlag": {"search.hidden": 1}, "modifyFlag": {"search.hidden": 1}}}, "name": "高级搜索", "isCover": false, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "adv-search", "pageCode": "subjectModeldetail"}, {"uuid": "uuid_k42132jc", "key": "adv-table_e9vp8qyz", "com": "adv-vxe-table", "comType": "adv-table", "icon": "pm-icon-adv-table", "comClassify": "model", "dataType": "object", "options": {"readonly": false, "customClass": "", "labelWidth": 100, "bindPager": "", "bindComEmpty": {}, "bindComTop": {}, "bindComBottom": {}, "hidden": false, "editable": false, "hideNewFields": false, "closeModelSort": false, "autoLoad": true, "searchReserveData": false, "localDataSort": false, "scheme": true, "comLinkEnabled": true, "appendToBody": true, "tableConfig": {"border": "default", "seq": {"enabled": false, "seqTitle": "序号", "seqWidth": 60, "align": "center", "headerAlign": "center"}, "rowSort": {"enabled": false, "handle": "", "fixed": "left"}, "selectType": {"type": "", "checkTitle": "", "checkWidth": 40, "labelField": "", "reserve": true, "highlight": true, "range": true, "visibleMethodFunc": {}, "checkMethodFunc": {}, "align": "center", "headerAlign": "center"}, "showHeaderOverflow": true, "showOverflow": true, "fullHeight": false, "height": 0, "minHeight": 0, "maxHeight": 700, "stripe": false, "emptyText": "暂无数据", "align": "left", "header-align": "left", "rowId": "id", "groupField": "tableColumnGroup", "groupEnabled": false, "beforeRenderFunc": {"name": "before_render_1tdd7yl9", "enName": "模型预处理", "params": [{"name": "modelItem", "des": "对应列模型，可以修改模型数据；返回一个新的列模型"}]}, "spanMethodFunc": {}, "dataLoadFunc": {}}, "rowConfig": {"isHover": true, "isCurrent": false, "height": 46}, "statConfig": {"isShowStat": false, "statConfigList": []}, "reportConfig": {"reportText": "导出报表", "reportList": []}, "batchDownloadConfig": {"btnText": "批量下载", "btnConfigList": []}, "columnConfig": {"resizable": true, "minWidth": 80, "width": 0, "isHover": true, "isCurrent": false}, "flowBtnConfig": {"labelWidth": 120, "beforeRender": {}, "limit": 30, "fileSize": 100, "labelPosition": "left", "isTable": false, "listType": "text", "undefined": ""}, "customConfig": {"storage": true, "checkMethodFunc": {}}, "operationConfig": {"enabled": true, "position": "right", "tip": "", "title": "操作", "width": 200, "schema": "text", "buttonList": [{"label": "查看", "id": "detail", "uuid": "8jw6e2fo", "type": "primary", "func": {"name": "action_ev_dpih4d5i", "params": [{"name": "event", "des": "{row}:对应的行数据;{col}:对应的列数据;{btn}:点击的按钮；{scope}:表格对象"}], "enName": "查看"}, "children": []}, {"label": "训练", "id": "train", "uuid": "nt7i8e7b", "type": "primary", "func": {"name": "action_ev_2xot2xvo", "params": [{"name": "event", "des": "{row}:对应的行数据;{col}:对应的列数据;{btn}:点击的按钮；{scope}:表格对象"}], "enName": "训练"}, "children": []}, {"label": "删除", "id": "delete", "uuid": "bshho9tc", "type": "primary", "func": {"name": "action_ev_lljwcj1q", "enName": "删除"}, "children": []}], "buttonEditList": [], "renderFunc": {"name": "btn_render_v5ozz4ur", "enName": "按钮处理", "params": [{"name": "event", "des": "event:{btnList:要处理的按钮组；row:行数据,column:列数据；{isActiveByRow}:是否为编辑态；scope:表格对象；setBtnItemProp:设置指定按钮属性{id,prop};callback:回调函数，返回按钮数组}"}]}, "template": "", "beforeDialog": {}}, "pagerConfig": {"pageStyle": "normal", "enabled": true, "pageSize": 20, "background": false, "pagerCount": 7, "hideOnSinglePage": false, "position": "right", "pageSizes": [5, 10, 20, 50, 100], "layouts": ["prev", "pager", "next", "sizes", "jumper"], "undefined": ""}, "toolbarConfig": {"flow": false, "import": false, "export": false, "print": false, "enabled": true, "zoom": false, "custom": false, "sort": false, "report": false, "batchDownload": false, "exportOptions": {"tooltip": {}}, "printOptions": {"tooltip": {}}, "zoomOptions": {"tooltip": {}}, "customOptions": {"tooltip": {}}, "importOptions": {"tooltip": {}}, "reportOptions": {"tooltip": {}}, "leftButtonList": {"schema": "button", "buttonList": [{"label": "导入", "id": "rfuzyu9b", "uuid": "1mwv70un", "type": "primary", "func": {"name": "action_ev_9qx8mltj", "params": [{"name": "event", "des": "{btn}:点击的按钮;"}], "enName": "导入"}, "children": []}, {"label": "训练所有未学习表", "id": "27toc6m7", "uuid": "m4fa01yt", "type": "", "func": {"name": "action_ev_hx4u69ho", "params": [{"name": "event", "des": "{btn}:点击的按钮;"}], "enName": "训练所有未学习表"}, "children": []}], "renderFunc": {}, "beforeDialog": {}}, "rightButtonList": {"schema": "text", "buttonList": [], "renderFunc": {}}}, "exportConfig": {"filename": "table_1742795748966", "dateFormat": "", "sheetName": "sheet", "type": "xlsx", "showOneClick": true, "showCustomClick": true, "oneClickBtnText": "一键导出", "customBtnText": "自定义导出", "types": ["xlsx"], "translateDictFlag": true, "apiUrl": ""}, "treeConfig": {"lazy": false, "transform": false, "parentField": "parentId", "hasChildField": "<PERSON><PERSON><PERSON><PERSON>", "expandAll": false, "accordion": false, "trigger": "default", "indent": 20, "treeNodeField": ""}, "editConfig": {"trigger": "click", "mode": "row", "showStatus": true, "showAsterisk": true, "autoClear": true, "beforeActiveEditMethodFunc": {}, "beforeEditMethodFunc": {}}, "editRules": {}, "importConfig": {"config": "file", "filename": "模板文件", "dateFormat": "", "isGenerateZdb": true, "customParam": ""}, "undefined": ""}, "dataOptions": {"remote": true, "remoteType": "model", "remoteModel": {"actionType": "table", "id": "dataqueryai-dataqueryaitopicmodel", "url": "/admin/model/design/dataqueryai/perm/dataqueryaitopicmodel", "modelCascades": "", "modelName": "dataqueryaitopicmodel", "modelApp": "dataqueryai"}, "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "prefix": "grid", "columnsModel": [{"name": "topicName"}, {"name": "modelTable"}, {"name": "modelName"}, {"name": "aiStudyFlag"}, {"name": "topicId"}, {"name": "modelId"}, {"name": "modelType"}, {"name": "modelSynonyms"}, {"name": "modelSemantics"}, {"name": "modifyFlag"}, {"name": "id"}, {"name": "createBy"}, {"name": "createTime"}, {"name": "updateBy"}, {"name": "updateTime"}], "dataSource": {"type": "custom", "url": "/plugins/dataqueryai/model/list", "code": "", "dataPath": "rows", "params": {"topicId": "{vars.currentTopic.id}"}}, "modelConfig": {"aiStudyFlag": {"placeholder": "请输入", "grid.width": 0, "grid.omitted": 0, "grid.sortable": 0, "caption": "模型状态"}, "topicId": {"grid.hidden": 1}, "modelId": {"grid.hidden": 1}, "modelType": {"grid.hidden": 1}, "modelSynonyms": {"grid.hidden": 1}, "modelSemantics": {"grid.hidden": 1}, "modifyFlag": {"grid.hidden": 1}}}, "events": {"mounted": {"tip": "组件挂载"}, "model-inited": {"tip": "模型获取完成"}, "reload-data": {"tip": "数据获取完成"}, "operate-click": {"tip": "操作列按钮点击"}, "radio-change": {"tip": "单选切换"}, "checkbox-change": {"tip": "多选点击"}, "checkbox-all": {"tip": "全选点击"}, "cell-click": {"tip": "单元格点击"}, "scroll": {"tip": "表格滚动"}, "page-change": {"tip": "切换当前页面"}, "size-change": {"tip": "每页数量变化"}, "zoom": {"tip": "表格切换最大化"}, "edit-closed": {"tip": "退出当前编辑"}, "row-sort-change": {"tip": "行拖动排序"}, "export-complete": {"tip": "导出完成"}, "startAndTakeUserTask": {"tip": "流程启动"}, "agree": {"tip": "流程同意"}, "startAndSaveDraft": {"tip": "流程保存草稿"}, "afterClick": {"tip": "按钮事件后触发后回调"}, "flowButtonSucceed": {"tip": "流程按钮执行成功后"}, "beforeDestroy": {"tip": "组件销毁"}}, "name": "高级表格模型", "isCover": false, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "adv-vxe-table", "pageCode": "subjectModeldetail"}], "options": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "pageCode": "subjectModeldetail", "uuid": "uuid_lqkh1186"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "0 0 0 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": false, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false, "bgType": "bgcolor", "backgroundColor": "", "borderRadius": 0}, "showTitle": false, "title": "容器标题", "layout": "grid", "pageCode": "subjectModeldetail", "uuid": "uuid_v5t4b8c7"}], "options": {"marginTop": 0, "marginBottom": 12, "marginLeft": 0, "marginRight": 0, "widthType": "0", "maxWidth": 0, "minWidth": 0, "width": "260px"}, "pageCode": "subjectModeldetail"}], "leftList": [{"isRoot": true, "type": "row", "comType": "page", "__tree_label": "页面", "__tree_node_key": "3876492922406296", "key": "page_rzbu56zq", "uuid": "uuid_d66s0kxb", "children": [], "pageCode": "subjectModeldetail"}], "rightList": [{"isRoot": true, "type": "row", "comType": "page", "__tree_label": "页面", "__tree_node_key": "7037190994142151", "key": "page_0idk12tq", "uuid": "uuid_v5shux28", "children": [], "pageCode": "subjectModeldetail"}], "config": {"dataSource": [], "platform": "pc", "layout": "default", "leftWidth": 260, "rightWidth": 260, "foldingSwitchTop": 50, "foldingSwitch": false}, "dialogJson": [{"__tree_node_key": "12799011794255555", "key": "dialog_lxz11a5m", "__tree_label": "区域容器", "comType": "row", "type": "row", "children": [{"__tree_node_key": "7596116494475611", "key": "col_5vl3ud6o", "__tree_label": "区块容器", "comType": "col", "type": "col", "children": [{"__tree_node_key": "3372189193594133", "key": "row_1i4337xf", "__tree_label": "区域容器", "comType": "row", "type": "row", "children": [{"__tree_node_key": "23320505404133351", "key": "col_w5d2vych", "__tree_label": "区块容器", "comType": "col", "type": "col", "children": [{"uuid": "uuid_ftpo6185", "key": "page-runtime_ctjsncvg", "com": "page-runtime", "comType": "page-runtime", "icon": "pm-icon-page-runtime", "dataType": "object", "options": {"readonly": false, "customClass": "", "labelWidth": 100, "isLabelWidth": false, "hidden": false, "pageInfo": {"pageCode": "modelImport", "pageType": "default", "appCode": "dataqueryai", "label": "导入模型", "value": "dataqueryai-modelImport"}, "values": {}, "pageParams": {"currentTopic": "{vars.currentTopic<PERSON>son}", "emitName": "{vars.importModelEmitName}"}, "inlinePage": true, "appCode": "dataqueryai"}, "events": {"mounted": {"tip": "组件挂载"}, "pageReady": {"tip": "页面加载完成"}, "pageBeforeDestroy": {"tip": "页面开始销毁"}, "beforeDestroy": {"tip": "组件销毁"}}, "name": "页面", "isCover": false, "dataOptions": {"remote": true, "remoteType": "dataset", "remoteModel": "", "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "dataSource": {"type": "", "url": "", "code": "", "params": {}, "querySetting": []}}, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "page-runtime", "pageCode": "subjectModeldetail"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "events": {"mounted": "", "destroy": ""}, "pageCode": "subjectModeldetail", "uuid": "uuid_74vdd0we"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "showTitle": false, "title": "容器标题", "events": {"mounted": "", "destroy": ""}, "layout": "grid", "pageCode": "subjectModeldetail", "uuid": "uuid_5t4kqt9p"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "0 2px 12px 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "events": {"mounted": "", "destroy": ""}, "pageCode": "subjectModeldetail", "uuid": "uuid_m8bm8tzh"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "0 2px 12px 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "showTitle": true, "title": "导入模型", "events": {"mounted": {}, "destroy": {}}, "layout": "grid", "sign": "dialog", "btnsPosition": "right", "frameType": "1", "widthType": "percent", "percentWidth": "80%", "size": "medium", "pixelWidth": "784", "commonOptions": {"fullscreen": "false", "closeOnClickModal": "false"}, "drawerOptions": {"frameDirection": "rtl", "wrapperClosable": "true"}, "btnOptions": [{"label": "取消", "id": "cancel_4ghc34ro", "type": "", "func": {"name": "", "params": {}}}, {"label": "确认", "id": "confirm_h612ijmm", "type": "primary", "func": {"name": "action_ev_x57t17pl", "params": [{"name": "event", "des": "事件相关参数"}, {"name": "closeNext", "des": "需要调用该方法才能关闭弹窗"}], "enName": "确认"}}], "btnRenderFunc": {}, "pageCode": "subjectModeldetail", "uuid": "uuid_obpaz0jf"}, {"__tree_node_key": "6338209056633166", "key": "dialog_8edu61ev", "__tree_label": "区域容器", "comType": "row", "type": "row", "children": [{"__tree_node_key": "7645792997544398", "key": "col_tc00wqa8", "__tree_label": "区块容器", "comType": "col", "type": "col", "children": [{"__tree_node_key": "7182010579425198", "key": "row_23k5uzvv", "__tree_label": "区域容器", "comType": "row", "type": "row", "children": [{"__tree_node_key": "6861918272517062", "key": "col_n67f1ce9", "__tree_label": "区块容器", "comType": "col", "type": "col", "children": [{"uuid": "uuid_v1xuhebj", "key": "component_7g7sxtqo", "com": "res-com-template", "comType": "component", "icon": "pm-icon-vue", "options": {"readonly": false, "customClass": "", "labelWidth": 100, "isLabelWidth": false, "hidden": false, "template": "<div class=\"model-detail\">\r\n    <div class=\"detail-title\">\r\n        <span class=\"span-title\">模型基本信息</span>\r\n        <div class=\"edit-btn\">\r\n            <i class=\"el-icon-edit\" @click=\"page.editForm\" v-if=\"!globalVars.isEdit\"></i>\r\n            <div v-if=\"globalVars.isEdit == true\">\r\n                <span @click=\"page.saveForm('cancel')\">取消</span>\r\n                <span @click=\"page.saveForm\">保存</span>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>", "required": false, "pattern": "", "validator": ""}, "dataOptions": {"remote": true, "remoteType": "dataset", "remoteModel": "", "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "dataSource": {"type": "custom", "params": {}, "code": "", "url": ""}}, "events": {"mounted": {"tip": "组件挂载"}, "change": {"tip": "内部绑定的表单组件值发生变化(是否触发根据内部手写的组件有关)"}, "beforeDestroy": {"tip": "组件销毁"}}, "name": "vue模板", "isCover": false, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "res-com-template", "pageCode": "subjectModeldetail"}, {"uuid": "uuid_b0u6yplz", "key": "data-form_f1v738jg", "com": "data-form", "comType": "data-form", "icon": "pm-icon-mode-form", "comClassify": "model", "dataType": "object", "options": {"readonly": true, "titleSize": 16, "showIcon": "true", "width": 0, "formWidth": 0, "isVisualLoading": false, "openGroup": true, "showLabel": true, "labelWidth": 100, "labelPosition": "left", "labelShowType": "default", "tableForm": false, "column": 3, "autoLoad": false, "comLinkEnabled": true, "size": "", "hidden": false, "disabled": false, "groupType": "default", "hideNewFields": false, "closeModelSort": false, "isPrint": false, "validateGroup": "", "beforeRender": {}, "beforeRules": {}, "tipRight": false, "anchorLevel": 1, "enabledAnchor": false, "labelSup": "", "disDictRequest": false, "appendToBody": true, "undefined": "", "modelSort": {}}, "events": {"mounted": {"tip": "组件挂载"}, "inited": {"tip": "模型和数据获取完成"}, "dict-loaded": {"tip": "每当有某一项的字典数据请求完成后"}, "item-change": {"tip": "每一项的值触发change后"}, "dict-change": {"tip": "字典项控件的值发生改变时触发"}, "beforeDestroy": {"tip": "组件销毁"}}, "dataOptions": {"remote": true, "remoteType": "model", "remoteModel": {"actionType": "form", "id": "dataqueryai-dataqueryaitopicmodel", "url": "/admin/model/design/dataqueryai/perm/dataqueryaitopicmodel", "modelCascades": "", "modelName": "dataqueryaitopicmodel", "modelApp": "dataqueryai"}, "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "prefix": "form", "dataSource": {"type": "in", "url": "", "code": "", "dataPath": "", "params": {}}, "columnsModel": [{"name": "modelTable"}, {"name": "modelName"}, {"name": "modelSynonyms"}, {"name": "modelSemantics"}, {"name": "topicName"}, {"name": "topicId"}, {"name": "modelId"}, {"name": "modelType"}, {"name": "aiStudyFlag"}, {"name": "modifyFlag"}, {"name": "id"}, {"name": "createBy"}, {"name": "createTime"}, {"name": "updateBy"}, {"name": "updateTime"}], "modelConfig": {"modelTable": {"placeholder": "请输入", "form.omitted": 0, "form.readonly": 1, "form.template": "<div class=\"form-readonly-text\" :title=\"value\">{{value}}</div>"}, "modelName": {"placeholder": "请输入", "form.omitted": 0, "form.readonly": 1, "form.template": "<div class=\"form-readonly-text\" :title=\"value\">{{value}}</div>"}, "modelSynonyms": {"placeholder": "请输入", "form.omitted": 0, "form.template_edit": "<el-select v-model=\"globalVars.selectBMListValue\" collapse-tags multiple filterable allow-create default-first-option\r\n  placeholder=\"请选择文章标签\">\r\n  <el-option v-for=\"item in globalVars.selectBMList\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\r\n  </el-option>\r\n</el-select>", "form.template": "<div class=\"form-readonly-text\" :title=\"value\">{{value}}</div>", "form.JSONParam": {}}, "modelSemantics": {"placeholder": "请输入", "form.omitted": 0, "form.template": "<div class=\"form-readonly-text\" :title=\"value\">{{value}}</div>", "form.JSONParam": {"isOver": true}}, "topicName": {"form.hidden": 1}, "topicId": {"form.hidden": 1}, "modelId": {"form.hidden": 1}, "modelType": {"form.hidden": 1}, "aiStudyFlag": {"form.hidden": 1}, "modifyFlag": {"form.hidden": 1}}}, "name": "模型表单", "isCover": false, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "data-form", "pageCode": "subjectModeldetail"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "events": {"mounted": "", "destroy": ""}, "pageCode": "subjectModeldetail", "uuid": "uuid_iimhdyrq"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false, "bgType": "bgcolor", "backgroundColor": "", "borderRadius": 0}, "showTitle": false, "title": "容器标题", "events": {"mounted": "", "destroy": ""}, "layout": "grid", "pageCode": "subjectModeldetail", "uuid": "uuid_02475i1l"}, {"__tree_node_key": "2750868241813449", "key": "row_dq009sek", "__tree_label": "区域容器", "comType": "row", "type": "row", "children": [{"__tree_node_key": "110777006319841", "key": "col_dtze5vyc", "__tree_label": "区块容器", "comType": "col", "type": "col", "children": [{"uuid": "uuid_7asat38m", "key": "adv-search_jpkdpyxg", "com": "adv-search", "comType": "adv-search", "icon": "pm-icon-advancedSearch", "comClassify": "model", "dataType": "object", "options": {"readonly": false, "interacTable": "adv-table_eibjqrms", "limit": 1, "keyWordsLabel": "关键字", "hideKeyWords": true, "comLinkEnabled": true, "labelWidth": 100, "keyWordsHighlight": true, "size": "", "itemWidth": 285, "singleItemWidth": 400, "tileItemWidth": 320, "searchTime": "", "conditionType": "", "hidden": false, "readyDoSearch": false, "hideNewFields": false, "closeModelSort": false, "showAdv": false, "fullscreen": false, "isCondition": false, "isMore": false, "beforeRender": {}, "showHidden": true, "appendToBody": true, "undefined": "", "beforeSearch": {}}, "events": {"mounted": {"tip": "组件挂载"}, "inited": {"tip": "模型和数据获取完成"}, "search": {"tip": "搜索之前，参数为querySetting(同步)"}, "before-reset": {"tip": "重置之前(同步)"}, "async-before-reset": {"tip": "重置之前(异步，参数为回调函数，执行查询需要调用回调)"}, "reset": {"tip": "重置后"}, "adv-reset": {"tip": "高级筛选重置后(需开启高级筛选功能)"}, "collapse": {"tip": "收起条件后"}, "expand": {"tip": "展开条件后"}, "item-change": {"tip": "每一项的值触发change后"}, "dict-loaded": {"tip": "每当有某一项的字典数据请求完成后"}, "dict-change": {"tip": "字典项控件的值发生改变时触发"}, "beforeDestroy": {"tip": "组件销毁"}}, "dataOptions": {"remote": true, "remoteType": "model", "remoteModel": {"actionType": "search", "id": "dataqueryai-dataqueryaiemptymodel", "url": "/admin/model/design/dataqueryai/perm/dataqueryaiemptymodel", "modelCascades": "", "modelName": "dataqueryaiemptymodel", "modelApp": "dataqueryai", "params": {"modelId": "datamodelcolumn"}}, "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "prefix": "search", "dataSource": {"type": "in", "url": "", "code": "", "params": {}}, "columnsModel": [{"name": "columnLabel"}, {"name": "columnDbname"}, {"name": "columnType"}, {"name": "columnXtype"}, {"name": "columnWidth"}, {"name": "columnPrecision"}, {"name": "columnSynonyms"}, {"name": "columnSemantics"}, {"name": "columnPattern"}, {"name": "searchStrategy"}, {"name": "createBy"}, {"name": "createTime"}, {"name": "id"}, {"name": "updateBy"}, {"name": "updateTime"}, {"name": "fieldColumnType"}, {"name": "columnJavaname"}, {"name": "modelId"}, {"name": "columnJavatype"}, {"name": "columnDbtype"}, {"name": "columnWidthUt"}, {"name": "columnPrecisionUt"}, {"name": "columnLabelUt"}, {"name": "columnNotnull"}, {"name": "columnNotnullUt"}, {"name": "orderIndex"}, {"name": "orderIndexUt"}, {"name": "dbfSize"}, {"name": "dbfSizeUt"}, {"name": "columnDefault"}, {"name": "columnDefaultUt"}, {"name": "columnAdd"}, {"name": "columnAddUt"}, {"name": "columnLinkSelect"}, {"name": "columnLinkSelectUt"}, {"name": "columnUpdate"}, {"name": "columnUpdateUt"}, {"name": "columnDisplay"}, {"name": "columnDisplayUt"}, {"name": "columnFilter"}, {"name": "columnFilterUt"}, {"name": "columnTypeUt"}, {"name": "prevInsert"}, {"name": "prevInsertUt"}, {"name": "prevUpdate"}, {"name": "prevUpdateUt"}, {"name": "columnEncrypt"}, {"name": "columnSecure"}, {"name": "columnSecureUt"}, {"name": "columnEl"}, {"name": "columnElUt"}, {"name": "virtualColumn"}, {"name": "extendFlag"}, {"name": "extendJavaName"}, {"name": "abbrJavaName"}, {"name": "abbrJavaNameUt"}, {"name": "eventTimePoint"}, {"name": "eventTimePointUt"}, {"name": "logicalType"}, {"name": "logicalTypeUt"}, {"name": "virtualColumnStatus"}, {"name": "columnDescription"}, {"name": "columnDescriptionUt"}, {"name": "columnDict"}, {"name": "columnDictUt"}, {"name": "columnDictSplit"}, {"name": "columnDictSplitUt"}, {"name": "columnGroup"}, {"name": "columnGroupUt"}, {"name": "columnReadonly"}, {"name": "columnReadonlyUt"}, {"name": "quickSearch"}, {"name": "quickSearchUt"}, {"name": "fuzzySearch"}, {"name": "fuzzySearchUt"}, {"name": "columnPatternUt"}, {"name": "columnHidden"}, {"name": "columnHiddenUt"}, {"name": "columnFormat"}, {"name": "columnFormatUt"}, {"name": "columnJsonparam"}, {"name": "columnJsonparamUt"}, {"name": "columnHtmlEditParam"}, {"name": "columnHtmlEditParamUt"}, {"name": "columnHtmlShowParam"}, {"name": "columnHtmlShowParamUt"}, {"name": "columnXtypeUt"}, {"name": "defaultBuilder"}, {"name": "defaultBuilderUt"}, {"name": "tableR<PERSON>only"}, {"name": "tableReadonlyUt"}, {"name": "tableHidden"}, {"name": "tableHiddenUt"}, {"name": "tableXtype"}, {"name": "tableXtypeUt"}, {"name": "tableRequired"}, {"name": "tableRequiredUt"}, {"name": "tableJsonparam"}, {"name": "tableJsonparamUt"}, {"name": "tableHtmlEditParam"}, {"name": "tableHtmlEditParamUt"}, {"name": "tableHtmlShowParam"}, {"name": "tableHtmlShowParamUt"}, {"name": "tableFixed"}, {"name": "tableFixedUt"}, {"name": "tableColumnGroup"}, {"name": "tableColumnGroupUt"}, {"name": "formReadonly"}, {"name": "formReadonlyUt"}, {"name": "formHidden"}, {"name": "formHiddenUt"}, {"name": "formXtype"}, {"name": "formXtypeUt"}, {"name": "formRequired"}, {"name": "formRequiredUt"}, {"name": "formJsonparam"}, {"name": "formJsonparamUt"}, {"name": "formHtmlEditParam"}, {"name": "formHtmlEditParamUt"}, {"name": "formHtmlShowParam"}, {"name": "formHtmlShowParamUt"}, {"name": "searchReadonly"}, {"name": "searchReadonlyUt"}, {"name": "searchHidden"}, {"name": "searchHiddenUt"}, {"name": "searchXtype"}, {"name": "searchXtypeUt"}, {"name": "searchRequired"}, {"name": "searchRequiredUt"}, {"name": "searchUseDefault"}, {"name": "searchUseDefaultUt"}, {"name": "searchJsonparam"}, {"name": "searchJsonparamUt"}, {"name": "searchHtmlShowParam"}, {"name": "searchHtmlShowParamUt"}, {"name": "topicModelId"}, {"name": "modelColumnId"}], "modelConfig": {"columnLabel": {"placeholder": "请输入", "search.omitted": 0, "caption": "显示名", "fuzzySearch": 1}, "columnDbname": {"placeholder": "请输入", "search.omitted": 0, "caption": "字段名", "search.hidden": 1}, "columnType": {"placeholder": "请选择", "search.omitted": 0, "caption": "字段类型", "search.hidden": 1}, "columnXtype": {"search.hidden": 1}, "columnWidth": {"placeholder": "请输入", "search.omitted": 0, "caption": "字段长度", "search.hidden": 1}, "columnPrecision": {"search.hidden": 1}, "columnSynonyms": {"search.hidden": 1}, "columnSemantics": {"placeholder": "请输入", "search.omitted": 0, "caption": "字段描述", "search.hidden": 1}, "columnPattern": {"placeholder": "请选择", "search.omitted": 0, "caption": "字典", "search.hidden": 1}, "searchStrategy": {"search.hidden": 1}, "fieldColumnType": {"search.hidden": 1}, "columnJavaname": {"search.hidden": 1}, "modelId": {"search.hidden": 1}, "columnJavatype": {"search.hidden": 1}, "columnDbtype": {"search.hidden": 1}, "columnWidthUt": {"search.hidden": 1}, "columnPrecisionUt": {"search.hidden": 1}, "columnLabelUt": {"search.hidden": 1}, "columnNotnull": {"search.hidden": 1}, "columnNotnullUt": {"search.hidden": 1}, "orderIndex": {"search.hidden": 1}, "orderIndexUt": {"search.hidden": 1}, "dbfSize": {"search.hidden": 1}, "dbfSizeUt": {"search.hidden": 1}, "columnDefault": {"search.hidden": 1}, "columnDefaultUt": {"search.hidden": 1}, "columnAdd": {"search.hidden": 1}, "columnAddUt": {"search.hidden": 1}, "columnLinkSelect": {"search.hidden": 1}, "columnLinkSelectUt": {"search.hidden": 1}, "columnUpdate": {"search.hidden": 1}, "columnUpdateUt": {"search.hidden": 1}, "columnDisplay": {"search.hidden": 1}, "columnDisplayUt": {"search.hidden": 1}, "columnFilter": {"search.hidden": 1}, "columnFilterUt": {"search.hidden": 1}, "columnTypeUt": {"search.hidden": 1}, "prevInsert": {"search.hidden": 1}, "prevInsertUt": {"search.hidden": 1}, "prevUpdate": {"search.hidden": 1}, "prevUpdateUt": {"search.hidden": 1}, "columnEncrypt": {"search.hidden": 1}, "columnSecure": {"search.hidden": 1}, "columnSecureUt": {"search.hidden": 1}, "columnEl": {"search.hidden": 1}, "columnElUt": {"search.hidden": 1}, "virtualColumn": {"search.hidden": 1}, "extendFlag": {"search.hidden": 1}, "extendJavaName": {"search.hidden": 1}, "abbrJavaName": {"search.hidden": 1}, "abbrJavaNameUt": {"search.hidden": 1}, "eventTimePoint": {"search.hidden": 1}, "eventTimePointUt": {"search.hidden": 1}, "logicalType": {"search.hidden": 1}, "logicalTypeUt": {"search.hidden": 1}, "virtualColumnStatus": {"search.hidden": 1}, "columnDescription": {"search.hidden": 1}, "columnDescriptionUt": {"search.hidden": 1}, "columnDict": {"search.hidden": 1}, "columnDictUt": {"search.hidden": 1}, "columnDictSplit": {"search.hidden": 1}, "columnDictSplitUt": {"search.hidden": 1}, "columnGroup": {"search.hidden": 1}, "columnGroupUt": {"search.hidden": 1}, "columnReadonly": {"search.hidden": 1}, "columnReadonlyUt": {"search.hidden": 1}, "quickSearch": {"search.hidden": 1}, "quickSearchUt": {"search.hidden": 1}, "fuzzySearch": {"search.hidden": 1}, "fuzzySearchUt": {"search.hidden": 1}, "columnPatternUt": {"search.hidden": 1}, "columnHidden": {"search.hidden": 1}, "columnHiddenUt": {"search.hidden": 1}, "columnFormat": {"search.hidden": 1}, "columnFormatUt": {"search.hidden": 1}, "columnJsonparam": {"search.hidden": 1}, "columnJsonparamUt": {"search.hidden": 1}, "columnHtmlEditParam": {"search.hidden": 1}, "columnHtmlEditParamUt": {"search.hidden": 1}, "columnHtmlShowParam": {"search.hidden": 1}, "columnHtmlShowParamUt": {"search.hidden": 1}, "columnXtypeUt": {"search.hidden": 1}, "defaultBuilder": {"search.hidden": 1}, "defaultBuilderUt": {"search.hidden": 1}, "tableReadonly": {"search.hidden": 1}, "tableReadonlyUt": {"search.hidden": 1}, "tableHidden": {"search.hidden": 1}, "tableHiddenUt": {"search.hidden": 1}, "tableXtype": {"search.hidden": 1}, "tableXtypeUt": {"search.hidden": 1}, "tableRequired": {"search.hidden": 1}, "tableRequiredUt": {"search.hidden": 1}, "tableJsonparam": {"search.hidden": 1}, "tableJsonparamUt": {"search.hidden": 1}, "tableHtmlEditParam": {"search.hidden": 1}, "tableHtmlEditParamUt": {"search.hidden": 1}, "tableHtmlShowParam": {"search.hidden": 1}, "tableHtmlShowParamUt": {"search.hidden": 1}, "tableFixed": {"search.hidden": 1}, "tableFixedUt": {"search.hidden": 1}, "tableColumnGroup": {"search.hidden": 1}, "tableColumnGroupUt": {"search.hidden": 1}, "formReadonly": {"search.hidden": 1}, "formReadonlyUt": {"search.hidden": 1}, "formHidden": {"search.hidden": 1}, "formHiddenUt": {"search.hidden": 1}, "formXtype": {"search.hidden": 1}, "formXtypeUt": {"search.hidden": 1}, "formRequired": {"search.hidden": 1}, "formRequiredUt": {"search.hidden": 1}, "formJsonparam": {"search.hidden": 1}, "formJsonparamUt": {"search.hidden": 1}, "formHtmlEditParam": {"search.hidden": 1}, "formHtmlEditParamUt": {"search.hidden": 1}, "formHtmlShowParam": {"search.hidden": 1}, "formHtmlShowParamUt": {"search.hidden": 1}, "searchReadonly": {"search.hidden": 1}, "searchReadonlyUt": {"search.hidden": 1}, "searchHidden": {"search.hidden": 1}, "searchHiddenUt": {"search.hidden": 1}, "searchXtype": {"search.hidden": 1}, "searchXtypeUt": {"search.hidden": 1}, "searchRequired": {"search.hidden": 1}, "searchRequiredUt": {"search.hidden": 1}, "searchUseDefault": {"search.hidden": 1}, "searchUseDefaultUt": {"search.hidden": 1}, "searchJsonparam": {"search.hidden": 1}, "searchJsonparamUt": {"search.hidden": 1}, "searchHtmlShowParam": {"search.hidden": 1}, "searchHtmlShowParamUt": {"search.hidden": 1}, "topicModelId": {"search.hidden": 1}, "modelColumnId": {"search.hidden": 1}}}, "name": "高级搜索", "isCover": false, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "adv-search", "pageCode": "subjectModeldetail"}, {"uuid": "uuid_1pflk57u", "key": "adv-table_eibjqrms", "com": "adv-vxe-table", "comType": "adv-table", "icon": "pm-icon-adv-table", "comClassify": "model", "dataType": "object", "options": {"readonly": false, "customClass": "", "labelWidth": 100, "bindPager": "", "bindComEmpty": {}, "bindComTop": {}, "bindComBottom": {}, "hidden": false, "editable": true, "hideNewFields": false, "closeModelSort": false, "autoLoad": true, "searchReserveData": false, "localDataSort": false, "scheme": true, "comLinkEnabled": true, "appendToBody": true, "tableConfig": {"border": "default", "seq": {"enabled": false, "seqTitle": "序号", "seqWidth": 60, "align": "center", "headerAlign": "center"}, "rowSort": {"enabled": false, "handle": "", "fixed": "left"}, "selectType": {"type": "", "checkTitle": "", "checkWidth": 40, "labelField": "", "reserve": true, "highlight": true, "range": true, "visibleMethodFunc": {}, "checkMethodFunc": {}, "align": "center", "headerAlign": "center"}, "showHeaderOverflow": true, "showOverflow": true, "fullHeight": false, "height": 0, "minHeight": 0, "maxHeight": 700, "stripe": false, "emptyText": "暂无数据", "align": "left", "header-align": "left", "rowId": "id", "groupField": "tableColumnGroup", "groupEnabled": false, "beforeRenderFunc": {}, "spanMethodFunc": {}, "dataLoadFunc": {}}, "rowConfig": {"isHover": true, "isCurrent": false, "height": 48}, "statConfig": {"isShowStat": false, "statConfigList": []}, "reportConfig": {"reportText": "导出报表", "reportList": []}, "batchDownloadConfig": {"btnText": "批量下载", "btnConfigList": []}, "columnConfig": {"resizable": true, "minWidth": 80, "width": 0, "isHover": true, "isCurrent": false}, "flowBtnConfig": {"labelWidth": 120, "beforeRender": {}, "limit": 30, "fileSize": 100, "labelPosition": "left", "isTable": false, "listType": "text", "undefined": ""}, "customConfig": {"storage": true, "checkMethodFunc": {}}, "operationConfig": {"enabled": true, "position": "right", "tip": "", "title": "操作", "width": 100, "schema": "text", "buttonList": [{"label": "编辑", "id": "sr9m8ocs", "uuid": "2pc4<PERSON><PERSON>", "type": "primary", "func": {"name": "action_ev_cmbrxu6t", "params": [{"name": "event", "des": "{row}:对应的行数据;{col}:对应的列数据;{btn}:点击的按钮；{scope}:表格对象"}], "enName": "编辑"}, "children": []}], "buttonEditList": [{"label": "保存", "id": "wx77cvnr", "uuid": "dj2p9i6g", "type": "primary", "func": {"name": "action_ev_hqv6hq8p", "params": [{"name": "event", "des": "{row}:对应的行数据;{col}:对应的列数据;{btn}:点击的按钮；{scope}:表格对象"}], "enName": "保存"}, "children": []}, {"label": "取消", "id": "iflz820n", "uuid": "tpzavunm", "type": "primary", "func": {"name": "action_ev_7la3spoj", "params": [{"name": "event", "des": "{row}:对应的行数据;{col}:对应的列数据;{btn}:点击的按钮；{scope}:表格对象"}], "enName": "取消"}, "children": []}], "renderFunc": {}, "template": "", "beforeDialog": {}}, "pagerConfig": {"pageStyle": "normal", "enabled": true, "pageSize": 20, "background": false, "pagerCount": 7, "hideOnSinglePage": false, "position": "right", "pageSizes": [5, 10, 20, 50, 100], "layouts": ["prev", "pager", "next", "sizes", "jumper"], "undefined": ""}, "toolbarConfig": {"flow": false, "import": false, "export": false, "print": false, "enabled": false, "zoom": true, "custom": true, "sort": false, "report": false, "batchDownload": false, "exportOptions": {"tooltip": {}}, "printOptions": {"tooltip": {}}, "zoomOptions": {"tooltip": {}}, "customOptions": {"tooltip": {}}, "importOptions": {"tooltip": {}}, "reportOptions": {"tooltip": {}}, "leftButtonList": {"schema": "text", "buttonList": [], "renderFunc": {}, "beforeDialog": {}}, "rightButtonList": {"schema": "text", "buttonList": [], "renderFunc": {}}}, "exportConfig": {"filename": "table_1742872600211", "dateFormat": "", "sheetName": "sheet", "type": "xlsx", "showOneClick": true, "showCustomClick": true, "oneClickBtnText": "一键导出", "customBtnText": "自定义导出", "types": ["xlsx"], "translateDictFlag": true, "apiUrl": ""}, "treeConfig": {"lazy": false, "transform": false, "parentField": "parentId", "hasChildField": "<PERSON><PERSON><PERSON><PERSON>", "expandAll": false, "accordion": false, "trigger": "default", "indent": 20, "treeNodeField": ""}, "editConfig": {"trigger": "manual", "mode": "row", "showStatus": true, "showAsterisk": true, "autoClear": false, "beforeActiveEditMethodFunc": {}, "beforeEditMethodFunc": {"name": "edit_func_6nvn86u4", "enName": "编辑前列干预", "params": [{"name": "event", "des": "event:{ row, rowIndex, column, columnIndex },返回boolean控制对应单元格是否可编辑"}]}}, "editRules": {}, "importConfig": {"config": "file", "filename": "模板文件", "dateFormat": "", "isGenerateZdb": true, "customParam": ""}, "undefined": ""}, "dataOptions": {"remote": true, "remoteType": "model", "remoteModel": {"actionType": "table", "id": "dataqueryai-dataqueryaiemptymodel", "url": "/admin/model/design/dataqueryai/perm/dataqueryaiemptymodel", "modelCascades": "", "modelName": "dataqueryaiemptymodel", "modelApp": "dataqueryai", "params": {"modelId": "datamodelcolumn"}}, "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "prefix": "grid", "columnsModel": [{"name": "columnLabel"}, {"name": "columnDbname"}, {"name": "columnSynonyms"}, {"name": "columnSemantics"}, {"name": "searchStrategy"}, {"name": "columnType"}, {"name": "columnXtype"}, {"name": "columnWidth"}, {"name": "columnPrecision"}, {"name": "columnPattern"}, {"name": "columnDescription"}, {"name": "id"}, {"name": "createBy"}, {"name": "createTime"}, {"name": "updateBy"}, {"name": "updateTime"}, {"name": "fieldColumnType"}, {"name": "columnJavaname"}, {"name": "modelId"}, {"name": "columnJavatype"}, {"name": "columnDbtype"}, {"name": "columnWidthUt"}, {"name": "columnPrecisionUt"}, {"name": "columnLabelUt"}, {"name": "columnNotnull"}, {"name": "columnNotnullUt"}, {"name": "orderIndex"}, {"name": "orderIndexUt"}, {"name": "dbfSize"}, {"name": "dbfSizeUt"}, {"name": "columnDefault"}, {"name": "columnDefaultUt"}, {"name": "columnAdd"}, {"name": "columnAddUt"}, {"name": "columnLinkSelect"}, {"name": "columnLinkSelectUt"}, {"name": "columnUpdate"}, {"name": "columnUpdateUt"}, {"name": "columnDisplay"}, {"name": "columnDisplayUt"}, {"name": "columnFilter"}, {"name": "columnFilterUt"}, {"name": "columnTypeUt"}, {"name": "prevInsert"}, {"name": "prevInsertUt"}, {"name": "prevUpdate"}, {"name": "prevUpdateUt"}, {"name": "columnEncrypt"}, {"name": "columnSecure"}, {"name": "columnSecureUt"}, {"name": "columnEl"}, {"name": "columnElUt"}, {"name": "virtualColumn"}, {"name": "extendFlag"}, {"name": "extendJavaName"}, {"name": "abbrJavaName"}, {"name": "abbrJavaNameUt"}, {"name": "eventTimePoint"}, {"name": "eventTimePointUt"}, {"name": "logicalType"}, {"name": "logicalTypeUt"}, {"name": "virtualColumnStatus"}, {"name": "columnDescriptionUt"}, {"name": "columnDict"}, {"name": "columnDictUt"}, {"name": "columnDictSplit"}, {"name": "columnDictSplitUt"}, {"name": "columnGroup"}, {"name": "columnGroupUt"}, {"name": "columnReadonly"}, {"name": "columnReadonlyUt"}, {"name": "quickSearch"}, {"name": "quickSearchUt"}, {"name": "fuzzySearch"}, {"name": "fuzzySearchUt"}, {"name": "columnPatternUt"}, {"name": "columnHidden"}, {"name": "columnHiddenUt"}, {"name": "columnFormat"}, {"name": "columnFormatUt"}, {"name": "columnJsonparam"}, {"name": "columnJsonparamUt"}, {"name": "columnHtmlEditParam"}, {"name": "columnHtmlEditParamUt"}, {"name": "columnHtmlShowParam"}, {"name": "columnHtmlShowParamUt"}, {"name": "columnXtypeUt"}, {"name": "defaultBuilder"}, {"name": "defaultBuilderUt"}, {"name": "tableR<PERSON>only"}, {"name": "tableReadonlyUt"}, {"name": "tableHidden"}, {"name": "tableHiddenUt"}, {"name": "tableXtype"}, {"name": "tableXtypeUt"}, {"name": "tableRequired"}, {"name": "tableRequiredUt"}, {"name": "tableJsonparam"}, {"name": "tableJsonparamUt"}, {"name": "tableHtmlEditParam"}, {"name": "tableHtmlEditParamUt"}, {"name": "tableHtmlShowParam"}, {"name": "tableHtmlShowParamUt"}, {"name": "tableFixed"}, {"name": "tableFixedUt"}, {"name": "tableColumnGroup"}, {"name": "tableColumnGroupUt"}, {"name": "formReadonly"}, {"name": "formReadonlyUt"}, {"name": "formHidden"}, {"name": "formHiddenUt"}, {"name": "formXtype"}, {"name": "formXtypeUt"}, {"name": "formRequired"}, {"name": "formRequiredUt"}, {"name": "formJsonparam"}, {"name": "formJsonparamUt"}, {"name": "formHtmlEditParam"}, {"name": "formHtmlEditParamUt"}, {"name": "formHtmlShowParam"}, {"name": "formHtmlShowParamUt"}, {"name": "searchReadonly"}, {"name": "searchReadonlyUt"}, {"name": "searchHidden"}, {"name": "searchHiddenUt"}, {"name": "searchXtype"}, {"name": "searchXtypeUt"}, {"name": "searchRequired"}, {"name": "searchRequiredUt"}, {"name": "searchUseDefault"}, {"name": "searchUseDefaultUt"}, {"name": "searchJsonparam"}, {"name": "searchJsonparamUt"}, {"name": "searchHtmlShowParam"}, {"name": "searchHtmlShowParamUt"}, {"name": "topicModelId"}, {"name": "modelColumnId"}], "dataSource": {"type": "custom", "url": "/plugins/dataqueryai/model/getModelColumns", "code": "", "dataPath": "", "params": {"topicModelId": "{vars.currentRow.id}"}}, "modelConfig": {"columnLabel": {"placeholder": "请输入", "grid.width": 0, "grid.omitted": 0, "grid.sortable": 0, "caption": "显示名"}, "columnDbname": {"placeholder": "请输入", "grid.width": 0, "grid.omitted": 0, "grid.sortable": 0, "caption": "字段名"}, "columnSynonyms": {"placeholder": "请输入", "grid.width": 0, "grid.omitted": 0, "grid.sortable": 0, "grid.template_edit": "<el-select v-model=\"globalVars.selectTableBMListValue\" collapse-tags multiple filterable allow-create default-first-option\r\n  placeholder=\"请选择别名\">\r\n  <el-option v-for=\"item in globalVars.selectTableBMList\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\r\n  </el-option>\r\n</el-select>", "grid.template": "<span>{{row['columnSynonyms']}}</span>"}, "columnSemantics": {"placeholder": "请输入", "grid.width": 0, "grid.omitted": 0, "grid.sortable": 0, "caption": "字段描述"}, "searchStrategy": {"placeholder": "请输入", "grid.width": 0, "grid.omitted": 0, "grid.sortable": 0, "caption": "字典", "grid.template_edit": "<el-select v-model=\"globalVars.dictValue\" filterable  placeholder=\"请选择类型\">\r\n  <el-option v-for=\"item in globalVars.dictList\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\r\n  </el-option>\r\n</el-select>", "grid.template": "<span>{{page.getLabel(row[field])}}</span>"}, "columnType": {"placeholder": "请选择", "grid.width": 0, "grid.omitted": 0, "grid.sortable": 0, "caption": "字段类型", "grid.hidden": 1}, "columnXtype": {"grid.hidden": 1}, "columnWidth": {"placeholder": "请输入", "grid.width": 0, "grid.omitted": 0, "grid.sortable": 0, "caption": "字段长度", "grid.hidden": 1}, "columnPrecision": {"grid.hidden": 1}, "columnPattern": {"placeholder": "请选择", "grid.width": 0, "grid.omitted": 0, "grid.sortable": 0, "caption": "时间类型", "grid.hidden": 1}, "columnDescription": {"placeholder": "请输入", "grid.width": 0, "grid.omitted": 0, "grid.sortable": 0, "grid.hidden": 1}, "fieldColumnType": {"grid.hidden": 1}, "columnJavaname": {"grid.hidden": 1}, "modelId": {"grid.hidden": 1}, "columnJavatype": {"grid.hidden": 1}, "columnDbtype": {"grid.hidden": 1}, "columnWidthUt": {"grid.hidden": 1}, "columnPrecisionUt": {"grid.hidden": 1}, "columnLabelUt": {"grid.hidden": 1}, "columnNotnull": {"grid.hidden": 1}, "columnNotnullUt": {"grid.hidden": 1}, "orderIndex": {"grid.hidden": 1}, "orderIndexUt": {"grid.hidden": 1}, "dbfSize": {"placeholder": "请输入", "grid.width": 0, "grid.hidden": 1, "grid.omitted": 0, "grid.sortable": 0}, "dbfSizeUt": {"grid.hidden": 1}, "columnDefault": {"grid.hidden": 1}, "columnDefaultUt": {"grid.hidden": 1}, "columnAdd": {"grid.hidden": 1}, "columnAddUt": {"grid.hidden": 1}, "columnLinkSelect": {"grid.hidden": 1}, "columnLinkSelectUt": {"grid.hidden": 1}, "columnUpdate": {"grid.hidden": 1}, "columnUpdateUt": {"grid.hidden": 1}, "columnDisplay": {"grid.hidden": 1}, "columnDisplayUt": {"grid.hidden": 1}, "columnFilter": {"grid.hidden": 1}, "columnFilterUt": {"grid.hidden": 1}, "columnTypeUt": {"grid.hidden": 1}, "prevInsert": {"grid.hidden": 1}, "prevInsertUt": {"grid.hidden": 1}, "prevUpdate": {"grid.hidden": 1}, "prevUpdateUt": {"grid.hidden": 1}, "columnEncrypt": {"grid.hidden": 1}, "columnSecure": {"grid.hidden": 1}, "columnSecureUt": {"grid.hidden": 1}, "columnEl": {"grid.hidden": 1}, "columnElUt": {"grid.hidden": 1}, "virtualColumn": {"grid.hidden": 1}, "extendFlag": {"grid.hidden": 1}, "extendJavaName": {"grid.hidden": 1}, "abbrJavaName": {"grid.hidden": 1}, "abbrJavaNameUt": {"grid.hidden": 1}, "eventTimePoint": {"grid.hidden": 1}, "eventTimePointUt": {"grid.hidden": 1}, "logicalType": {"grid.hidden": 1}, "logicalTypeUt": {"grid.hidden": 1}, "virtualColumnStatus": {"grid.hidden": 1}, "columnDescriptionUt": {"grid.hidden": 1}, "columnDict": {"grid.hidden": 1}, "columnDictUt": {"grid.hidden": 1}, "columnDictSplit": {"grid.hidden": 1}, "columnDictSplitUt": {"grid.hidden": 1}, "columnGroup": {"grid.hidden": 1}, "columnGroupUt": {"grid.hidden": 1}, "columnReadonly": {"grid.hidden": 1}, "columnReadonlyUt": {"grid.hidden": 1}, "quickSearch": {"grid.hidden": 1}, "quickSearchUt": {"grid.hidden": 1}, "fuzzySearch": {"grid.hidden": 1}, "fuzzySearchUt": {"grid.hidden": 1}, "columnPatternUt": {"grid.hidden": 1}, "columnHidden": {"grid.hidden": 1}, "columnHiddenUt": {"grid.hidden": 1}, "columnFormat": {"grid.hidden": 1}, "columnFormatUt": {"grid.hidden": 1}, "columnJsonparam": {"grid.hidden": 1}, "columnJsonparamUt": {"grid.hidden": 1}, "columnHtmlEditParam": {"grid.hidden": 1}, "columnHtmlEditParamUt": {"grid.hidden": 1}, "columnHtmlShowParam": {"grid.hidden": 1}, "columnHtmlShowParamUt": {"grid.hidden": 1}, "columnXtypeUt": {"grid.hidden": 1}, "defaultBuilder": {"grid.hidden": 1}, "defaultBuilderUt": {"grid.hidden": 1}, "tableReadonly": {"grid.hidden": 1}, "tableReadonlyUt": {"grid.hidden": 1}, "tableHidden": {"grid.hidden": 1}, "tableHiddenUt": {"grid.hidden": 1}, "tableXtype": {"grid.hidden": 1}, "tableXtypeUt": {"grid.hidden": 1}, "tableRequired": {"grid.hidden": 1}, "tableRequiredUt": {"grid.hidden": 1}, "tableJsonparam": {"grid.hidden": 1}, "tableJsonparamUt": {"grid.hidden": 1}, "tableHtmlEditParam": {"grid.hidden": 1}, "tableHtmlEditParamUt": {"grid.hidden": 1}, "tableHtmlShowParam": {"grid.hidden": 1}, "tableHtmlShowParamUt": {"grid.hidden": 1}, "tableFixed": {"grid.hidden": 1}, "tableFixedUt": {"grid.hidden": 1}, "tableColumnGroup": {"grid.hidden": 1}, "tableColumnGroupUt": {"grid.hidden": 1}, "formReadonly": {"grid.hidden": 1}, "formReadonlyUt": {"grid.hidden": 1}, "formHidden": {"grid.hidden": 1}, "formHiddenUt": {"grid.hidden": 1}, "formXtype": {"grid.hidden": 1}, "formXtypeUt": {"grid.hidden": 1}, "formRequired": {"grid.hidden": 1}, "formRequiredUt": {"grid.hidden": 1}, "formJsonparam": {"grid.hidden": 1}, "formJsonparamUt": {"grid.hidden": 1}, "formHtmlEditParam": {"grid.hidden": 1}, "formHtmlEditParamUt": {"grid.hidden": 1}, "formHtmlShowParam": {"grid.hidden": 1}, "formHtmlShowParamUt": {"grid.hidden": 1}, "searchReadonly": {"grid.hidden": 1}, "searchReadonlyUt": {"grid.hidden": 1}, "searchHidden": {"grid.hidden": 1}, "searchHiddenUt": {"grid.hidden": 1}, "searchXtype": {"grid.hidden": 1}, "searchXtypeUt": {"grid.hidden": 1}, "searchRequired": {"grid.hidden": 1}, "searchRequiredUt": {"grid.hidden": 1}, "searchUseDefault": {"grid.hidden": 1}, "searchUseDefaultUt": {"grid.hidden": 1}, "searchJsonparam": {"grid.hidden": 1}, "searchJsonparamUt": {"grid.hidden": 1}, "searchHtmlShowParam": {"grid.hidden": 1}, "searchHtmlShowParamUt": {"grid.hidden": 1}, "topicModelId": {"grid.hidden": 1}, "modelColumnId": {"grid.hidden": 1}}}, "events": {"mounted": {"tip": "组件挂载"}, "model-inited": {"tip": "模型获取完成"}, "reload-data": {"tip": "数据获取完成"}, "operate-click": {"tip": "操作列按钮点击"}, "radio-change": {"tip": "单选切换"}, "checkbox-change": {"tip": "多选点击"}, "checkbox-all": {"tip": "全选点击"}, "cell-click": {"tip": "单元格点击"}, "scroll": {"tip": "表格滚动"}, "page-change": {"tip": "切换当前页面"}, "size-change": {"tip": "每页数量变化"}, "zoom": {"tip": "表格切换最大化"}, "edit-closed": {"tip": "退出当前编辑"}, "row-sort-change": {"tip": "行拖动排序"}, "export-complete": {"tip": "导出完成"}, "startAndTakeUserTask": {"tip": "流程启动"}, "agree": {"tip": "流程同意"}, "startAndSaveDraft": {"tip": "流程保存草稿"}, "afterClick": {"tip": "按钮事件后触发后回调"}, "flowButtonSucceed": {"tip": "流程按钮执行成功后"}, "beforeDestroy": {"tip": "组件销毁"}}, "name": "高级表格字段详情", "isCover": false, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "adv-vxe-table", "pageCode": "subjectModeldetail"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "events": {"mounted": "", "destroy": ""}, "pageCode": "subjectModeldetail", "uuid": "uuid_c4i7pii5"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "showTitle": false, "title": "容器标题", "events": {"mounted": "", "destroy": ""}, "layout": "grid", "pageCode": "subjectModeldetail", "uuid": "uuid_r4q5nh0t"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "0 2px 12px 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "events": {"mounted": "", "destroy": ""}, "pageCode": "subjectModeldetail", "uuid": "uuid_80t2tfxp"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "0 2px 12px 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "showTitle": true, "title": "查看模型详情", "events": {"mounted": {}, "destroy": {"name": "destroy_dialog_tnq0rp8k", "enName": "模型详情弹窗销毁", "params": [{"name": "event", "des": "name:事件名称,dialog:当前弹窗对象"}]}}, "layout": "grid", "sign": "dialog", "btnsPosition": "center", "frameType": "3", "widthType": "pixel", "percentWidth": "50%", "size": "medium", "pixelWidth": "784", "commonOptions": {"fullscreen": "false", "closeOnClickModal": "false"}, "drawerOptions": {"frameDirection": "rtl", "wrapperClosable": "true"}, "btnOptions": [{"label": "训练", "id": "confirm_5k9vum5r", "type": "primary", "func": {"name": "action_ev_lm4tg0n7", "params": [{"name": "event", "des": "事件相关参数"}, {"name": "closeNext", "des": "需要调用该方法才能关闭弹窗"}], "enName": "确认"}}], "btnRenderFunc": {}, "pageCode": "subjectModeldetail", "uuid": "uuid_gjjpe57h"}], "suspendJson": []}