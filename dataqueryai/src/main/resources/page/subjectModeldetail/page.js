define(function (require) {
  return {
    /**
     * 监听指定对象的值发生变化，然后执行特定方法
     */
    pageWatch: {
      //  'globalVars.name':function(newVal,oldVal){
      //      console.log('值改变',newVal,oldVal)
      //  }
    },
    /**
     * 监听指定对象的值发生变化，然后执行特定方法
     */
    pageComputed: {

    },
    // var1:"变量",//响应式变量，该变量不能加到组件参数内,但是写到组件模板里
    /**
     * 响应式变量，当值变化时可以影响所有绑定的值,建议把需要绑定到组件参数里的变量申明到这里面
     */
    globalVars: {
      currentTopic: {},
      currentTopicJson: '',
      closeImportDialogFn: new Function(),
      importModelEmitName: 'import-model-close-dialog-from-subjectModeldetail',
      $modelDialog: {},
      indexRow: {},
      currentRow: {},
      isEdit: false,
      selectBMListValue: [],
      selectTableBMListValue: [],
      selectTableBMList: [],
      currentRowData: {},
      emitModelName: '',
      dictValue: '',
      dictList: [
        { label: '无', value: "0" },
        // { label: '字典', value: "1" },
        { label: '枚举', value: "2" },
      ],
      isActive: false,
      activeRowIndex: -1
      // name:'一个变量' //响应式变量name,组件内使用时{{globalVars.name}}
    },
    /**
     * 页面被重新激活时调用
     */
    pageActivated: function () {
      //console.log('页面激活')  
    },
    /**
     * 页面失去激活被缓存时调用
     */
    pageDeactivated: function () {
      //console.log('页面失活')  
    },
    /**
     * 固定方法，页面js初始化完成后调用,当前能修改js变量，修改组件初始化属性或者设置组件默认值
     */
    pageCreated: function () {
      window.$EventBus.$off(this.globalVars.importModelEmitName, this.closeImportDialog)
      window.$EventBus.$on(this.globalVars.importModelEmitName, this.closeImportDialog)
      try {
        this.globalVars.emitModelName = this.pageParams.emitModelName
        this.globalVars.currentTopicJson = this.pageParams?.currentTopic || ''
        this.globalVars.currentTopic = JSON.parse(this.pageParams.currentTopic)
      } catch (e) {
        console.log('e:', e)
      }
      //console.log('页面js初始化完成后调用')
    },
    /**
     * 固定方法，页面准备完成后调用，当前可以操作组件属性，调用未隐藏组件实例方法
     */
    pageReady: function () {
      //console.log('页面准备完成后调用')
    },
    /**
     * 固定方法，页面销毁前调用
     */
    pageDestroy: function () {
      //console.log('页面销毁前调用')
      window.$EventBus.$off(this.globalVars.importModelEmitName, this.closeImportDialog)
    },

    /**
     * 描述：导入
     * @param{event}  {btn}:点击的按钮; return:

     */
    action_ev_9qx8mltj: function (event) {
      var self = this;
      // this.globalVars.currentTopic = this.pageParams.currentTopic
      this.$pageDialog({ key: 'dialog_lxz11a5m', params: '', winParams: '' })
    },


    /**
     * 描述：确认
     * @param{event}  事件相关参数 return:
     * @param{closeNext}  需要调用该方法才能关闭弹窗 return:

     */
    action_ev_x57t17pl: function (event, closeNext) {
      window.$EventBus.$emit('import-model')
      this.globalVars.closeImportDialogFn = closeNext
    },
    closeImportDialog() {
      if (typeof this.globalVars.currentTopic == 'string') {
        this.globalVars.currentTopic = JSON.parse(this.globalVars.currentTopic)
      }
      this.$page('adv-table_e9vp8qyz').reload()
      this.globalVars.closeImportDialogFn()
      window.$EventBus.$emit('refresh-topic', 'subjectModeldetail')
    },

    /**
     * 描述：模型预处理
     * @param{modelItem}  对应列模型，可以修改模型数据；返回一个新的列模型 return:

     */
    before_render_1tdd7yl9: function (modelItem) {
      if (modelItem.name == "aiStudyFlag") {
        /**审核状态 */
        modelItem['grid.template'] = `<div class="study-status-tag"> 
          <div :class="{'study': true, 'no-study': row[field] == 3, 'has-study': row[field] == 1, 'ing-study': row[field] == 2}">
            <template v-if="row[field]==1">学习完毕</template>
            <template v-if="row[field]==2">正在学习</template>
            <template v-if="row[field]==3">未学习</template>
          </div> 
          <div v-if="row['modifyFlag'] == 1" :class="{'modify': true, 'no-modify': row['modifyFlag'] == 0, 'has-modify': row['modifyFlag'] == 1}">
            {{row['modifyFlag'] == 1 ? '有修改': '无修改'}}
          </div>
        </div>`
      }
    },


    /**
     * 描述：按钮处理
     * @param{event}  event:{btnList:要处理的按钮组；row:行数据,column:列数据；{isActiveByRow}:是否为编辑态；scope:表格对象；setBtnItemProp:设置指定按钮属性{id,prop};callback:回调函数，返回按钮数组} return:

     */
    btn_render_v5ozz4ur: function (event) {
      event.btnList.forEach(ele => {
        if (ele.id == 'train' && event.row.aiStudyFlag != 3 && event.row.modifyFlag != 1) {
          ele.disabled = 1
        }
      })
    },
    reloadData() {
      window.$EventBus.$emit(this.globalVars.emitModelName)
      this.$page("adv-table_e9vp8qyz").reload()
    },


    /**
     * 描述：训练
     * @param{event}  {row}:对应的行数据;{col}:对应的列数据;{btn}:点击的按钮；{scope}:表格对象 return:

     */
    action_ev_2xot2xvo: function (event) {
      var self = this;
      let title = `确认训练此模型吗？`
      this.$FormConfirm({
        title: '训练提示',
        iconColor: "#165dff",
        message: title,
        confirmText: '确认',
        cancelText: '取消',
        confirmType: 'primary'
      }).then(res => {
        this.trainModel(event.row)
      })
    },
    trainModel(item) {
      this.$request({
        method: 'post',
        url: '/plugins/dataqueryai/model/deployTopicModel',
        data: item
      }).then(res => {
        if (res.code == 0) {
          this.$message.success('训练成功')
          this.reloadData()
        } else {
          this.$message.error(res.msg || '训练失败')
        }
      })
    },

    /**
     * 描述：删除
     * @param{event}  {row}:对应的行数据;{col}:对应的列数据;{btn}:点击的按钮；{scope}:表格对象 return:

     */
    action_ev_lljwcj1q: function (event) {
      var self = this;
      let title = `确认删除此模型吗？`
      this.$FormConfirm({
        title: '训练提示',
        message: title,
        confirmText: '确认',
        cancelText: '取消',
        confirmType: 'danger'
      }).then(res => {
        this.deleteModel(event.row)
      })
    },
    deleteModel(item) {
      this.$request({
        method: 'post',
        url: '/plugins/dataqueryai/model/deleteTopicModel',
        data: item
      }).then(res => {
        if (res.code == 0) {
          this.$message.success('删除成功')
          this.globalVars.currentTopic.currentNum--
          this.globalVars.currentTopicJson = JSON.stringify(this.globalVars.currentTopic)
          this.reloadData()
        } else {
          this.$message.error(res.msg || '删除失败')
        }
      })
    },



    /**
     * 描述：确认
     * @param{event}  事件相关参数 return:
     * @param{closeNext}  需要调用该方法才能关闭弹窗 return:

     */
    action_ev_lm4tg0n7: function (event, closeNext) {
      var self = this;
      this.$FormConfirm({
        title: '部署提示',
        iconColor: "#165dff",
        message: `确认部署吗`,
        confirmText: '确认',
        confirmType: 'primary',
        cancelText: '取消'
      }).then(res => {
        this.deployTopicModel(closeNext)
      }).catch(e => {

      })
    },
    deployTopicModel(closeNext) {
      this.$request({
        "url": "/plugins/dataqueryai/model/deployTopicModel",
        "method": "post",
        data: this.globalVars.currentRow
      }).then(res => {
        if (res.code == 0) {
          closeNext()
          this.$message.success('部署成功')
        } else {
          this.$message.error(res.msg || '部署失败')
        }
      })
    },
    setFormVal(data) {
      this.$setVal("data-form_f1v738jg", window._.cloneDeep(data))
      let bmList = data.modelSynonyms.split('、')
      bmList = bmList.filter(ele => ele)
      this.globalVars.selectBMList = bmList.map(ele => {
        return {
          value: ele,
          label: ele
        }
      })
      this.globalVars.selectBMListValue = bmList
      console.log("bmList:", this.globalVars.selectBMListValue)
    },
    editForm() {
      this.globalVars.isEdit = true
      this.globalVars.$modelDialog.$setComProps("data-form_f1v738jg", {
        readonly: false
      })
    },

    setDialogBtn(num) {
      this.$page("dialog_8edu61ev").$setBtnProp('confirm_5k9vum5r', { disabled: num })
    },

    /**
     * 描述：查看
     * @param{event}  {row}:对应的行数据;{col}:对应的列数据;{btn}:点击的按钮；{scope}:表格对象 return:

     */
    action_ev_dpih4d5i: function (event) {
      this.globalVars.currentRow = window._.cloneDeep(event.row)
      this.globalVars.$modelDialog = this.$pageDialog({ key: 'dialog_8edu61ev', params: {}, winParams: {} })
      if (event.row.modifyFlag != 1 && event.row.aiStudyFlag != 3) {
        /**如果当前行不允许训练 */
        this.setDialogBtn(1)
      }
      setTimeout(() => {
        this.setFormVal(this.globalVars.currentRow)
      })
    },
    saveForm(type) {
      this.globalVars.isEdit = false
      this.globalVars.$modelDialog.$setComProps("data-form_f1v738jg", {
        readonly: true
      })
      if (type == 'cancel') {
        this.setFormVal(this.globalVars.currentRow)
        return
      }
      let formData = this.$getVal("data-form_f1v738jg")
      console.log("formData:", formData)
      let submitData = {
        ...formData,
        modelSynonyms: this.globalVars.selectBMListValue.join("、"),
      }
      this.$request({
        "url": "/plugins/dataqueryai/model/modifyTopicModel",
        "method": "post",
        "data": submitData
      }).then(res => {
        if (res.code == 0) {
          this.globalVars.currentRow = window._.cloneDeep(submitData)
          this.setFormVal(this.globalVars.currentRow)
          this.$message.success('模型信息保存成功')
          this.$page("adv-table_e9vp8qyz").reload()
          this.setDialogBtn(0)
        } else {
          this.$message.error(res.msg || '模型信息保存失败')
        }
      })
    },

    setRowData() {

    },
    /**
     * 描述：编辑
     * @param{event}  {row}:对应的行数据;{col}:对应的列数据;{btn}:点击的按钮；{scope}:表格对象 return:

     */
    action_ev_cmbrxu6t: function (event) {
      console.log("event:",event.scope.rowIndex)
      this.action_ev_7la3spoj()
      // if (this.globalVars.isActive) {
      //   this.$message.error('请先保存正在编辑的行')
      //   return
      // }
      this.globalVars.activeRowIndex = event.scope.rowIndex
      this.globalVars.isActive = true
      this.globalVars.currentRowData = window._.cloneDeep(event.row)
      this.$page("adv-table_eibjqrms").xeGrid.setActiveRow(event.row);
      let columnSynonyms = event.row.columnSynonyms?.split('、') || []
      columnSynonyms = columnSynonyms.filter(ele => ele)
      this.globalVars.selectTableBMList = columnSynonyms.map(ele => {
        return {
          value: ele,
          label: ele
        }
      })
      this.globalVars.selectTableBMListValue = columnSynonyms
      this.globalVars.indexRow = event.row;
      this.globalVars.dictValue = event.row.searchStrategy
    },
    getLabel(value) {
      let oF = this.globalVars.dictList.find(ele => ele.value == value)
      if (oF) {
        return oF.label
      }
      return '-'
    },
    cancelEdit(event) {
      event.row = window._.cloneDeep(this.globalVars.currentRowData)
    },
    /**
     * 描述：保存
     * @param{event}  {row}:对应的行数据;{col}:对应的列数据;{btn}:点击的按钮；{scope}:表格对象 return:

     */
    action_ev_hqv6hq8p: async function (event) {
      var self = this;
      var rowData = event.row;
      var modelcolnumId = rowData.id;
      var isValidate = await self.$page("adv-table_eibjqrms").xeGrid.validate(rowData);
      if (isValidate) {
        return;
      }
      let submitData = {
        ...rowData,
        columnSynonyms: this.globalVars.selectTableBMListValue.join("、"),
        searchStrategy: this.globalVars.dictValue
      }
      this.$request({
        method: "post",
        url: "/plugins/dataqueryai/model/modifyTopicModelColumn",
        data: submitData
      }).then(res => {
        if (res.code == 0) {
          this.globalVars.currentRowData = window._.cloneDeep(submitData)
          this.$message.success('保存成功')
          this.$page("adv-table_eibjqrms").xeGrid.clearActived();
          this.globalVars.isActive = false
          this.$page("adv-table_eibjqrms").reload()
          this.$page("adv-table_e9vp8qyz").reload()
          this.setDialogBtn(0)
        } else {
          this.$message.error(res.msg || '保存失败')
        }
      })
      console.log("rowData:", rowData)
    },
    /**
     * 描述：取消
     * @param{event}  {row}:对应的行数据;{col}:对应的列数据;{btn}:点击的按钮；{scope}:表格对象 return:

     */
    action_ev_7la3spoj: function (event) {
      var self = this;
      if(this.globalVars.activeRowIndex < 0){
        return
      }
      this.$set(this.$page('adv-table_eibjqrms').tableData[this.globalVars.activeRowIndex], 'columnSemantics', this.globalVars.currentRowData.columnSemantics)
      let columnSynonyms = this.globalVars.currentRowData.columnSynonyms?.split('、') || []
      columnSynonyms = columnSynonyms.filter(ele => ele)
      this.globalVars.selectTableBMList = columnSynonyms.map(ele => {
        return {
          value: ele,
          label: ele
        }
      })
      this.globalVars.selectTableBMListValue = columnSynonyms
      this.globalVars.dictValue = this.globalVars.currentRowData.searchStrategy
      this.globalVars.activeRowIndex = -1
      this.globalVars.isActive = false
      this.$page("adv-table_eibjqrms").xeGrid.clearActived();
    },
    /**
     * 描述：编辑前列干预
     * @param{event}  event:{ row, rowIndex, column, columnIndex },返回boolean控制对应单元格是否可编辑 return:

     */
    edit_func_6nvn86u4: function (event) {
      var field = event.column.field;
      let canEditFields = ['columnSynonyms', 'columnSemantics', 'searchStrategy']
      if (canEditFields.includes(field)) {
        return true
      } else {
        return false
      }
    },

    /**
     * 描述：取消
     * @param{event}  {row}:对应的行数据;{col}:对应的列数据;{btn}:点击的按钮；{scope}:表格对象 return:

     */
    action_ev_a2dhgti2: function (event) {
      var self = this;
      // this.globalVars.currentRowData = window._.cloneDeep(event.row)
      this.$page("adv-table_eibjqrms").xeGrid.clearActived();
    },


    /**
     * 描述：训练所有未学习表
     * @param{event}  {btn}:点击的按钮; return:

     */
    action_ev_hx4u69ho: function (event) {
      var self = this;
      this.$request({
        url: '/plugins/dataqueryai/topic/trainUnStudyTopicModel',
        method: "post",
        data: {
          topicId: this.globalVars.currentTopic.id
        }
      }).then(res => {
        if (res.code == 0) {
          this.$message.success('已开始学习，请耐心等待')
          window.$EventBus.$emit('refresh-topic', 'subjectModeldetail')
        } else {
          this.$message.error(res.msg || '学习失败')
        }
      })
    },


    /**
     * 描述：模型详情弹窗销毁
     * @param{event}  name:事件名称,dialog:当前弹窗对象 return:

     */
    destroy_dialog_tnq0rp8k: function (event) {
      var self = this;
      this.globalVars.isActive = false
      this.globalVars.isEdit = false
    },


  }
})