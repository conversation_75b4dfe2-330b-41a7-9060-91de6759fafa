define(function (require) {
  return {
    /**
     * 监听指定对象的值发生变化，然后执行特定方法
     */
    pageWatch: {
      //  'globalVars.name':function(newVal,oldVal){
      //      console.log('值改变',newVal,oldVal)
      //  }
    },
    /**
     * 监听指定对象的值发生变化，然后执行特定方法
     */
    pageComputed: {
      // getNewName:function(){
      //   return this.globalVars.name+'新的'
      //}
    },
    // var1:"变量",//响应式变量，该变量不能加到组件参数内,但是写到组件模板里
    /**
     * 响应式变量，当值变化时可以影响所有绑定的值,建议把需要绑定到组件参数里的变量申明到这里面
     */
    globalVars: {
      $dialog: {}
      // name:'一个变量' //响应式变量name,组件内使用时{{globalVars.name}}
    },
    /**
     * 页面被重新激活时调用
     */
    pageActivated: function () {
      //console.log('页面激活')  
    },
    /**
     * 页面失去激活被缓存时调用
     */
    pageDeactivated: function () {
      //console.log('页面失活')  
    },
    /**
     * 固定方法，页面js初始化完成后调用,当前能修改js变量，修改组件初始化属性或者设置组件默认值
     */
    pageCreated: function () {
      //console.log('页面js初始化完成后调用')
    },
    /**
     * 固定方法，页面准备完成后调用，当前可以操作组件属性，调用未隐藏组件实例方法
     */
    pageReady: function () {
      //console.log('页面准备完成后调用')
    },
    /**
     * 固定方法，页面销毁前调用
     */
    pageDestroy: function () {
      //console.log('页面销毁前调用')
    },

    /**
     * 描述：新增
     * @param{event}  {btn}:点击的按钮; return:

     */
    action_ev_qt2zq9nw: function (event) {
      var self = this;
      this.globalVars.$dialog = this.$pageDialog({ key: 'dialog_5iyvrl7h', params: { title: '新增' }, winParams: { title: '新增提示词' } })
      // console.log("globalVars.$dialog:",this.globalVars.$dialog.$comMethod("data-form_ubqat815", resetFields))
      // this.$page("data-form_ubqat815").resetFields()
    },

    action_ev_edit: function (event) {
      var self = this;
      console.log("event.row:",event.row)
      this.$setVal("data-form_ubqat815", event.row)
      this.globalVars.$dialog = this.$pageDialog({ key: 'dialog_5iyvrl7h', params: { title: '新增' }, winParams: { title: '编辑提示词' } })
    },

    /**
     * 描述：确认
     * @param{event}  事件相关参数 return:
     * @param{closeNext}  需要调用该方法才能关闭弹窗 return:

     */
    action_ev_c2zlngyr: function (event, closeNext) {
      let formData = this.$getVal("data-form_ubqat815"), submitData = {}
      let url = '/plugins/dataqueryai/bgKnowledge/add'
      submitData.keyword = formData.keyword
      submitData.explain = formData.explain
      if (formData.id) {
        url = '/plugins/dataqueryai/bgKnowledge/update'
        submitData.id = formData.id
      }
      this.$request({
        url: url,
        method: 'post',
        data: submitData
      }).then(res => {
        console.log("res:", res)
        if (res.code == 0) {
          this.$message.success('新增成功')
          closeNext()
          this.$page('adv-table_tki6o41u').reload()
        } else {
          this.$message.eror(res.msg || '新增失败')
        }
      }).catch(e => {

      })

    },
    deleteWord(item) {
      this.$request({
        url: '/plugins/dataqueryai/bgKnowledge/delete',
        method: 'post',
        data: {
          id: item.id
        }
      }).then(res => {
        if (res.code == 0) {
          this.$message.success('删除成功')
          this.$page('adv-table_tki6o41u').reload()
        } else {
          this.$message.eror(res.msg || '删除失败')
        }
      }).catch(e => {

      })
    },
    /**
     * 描述：删除
     * @param{event}  {row}:对应的行数据;{col}:对应的列数据;{btn}:点击的按钮；{scope}:表格对象 return:

     */
    action_ev_ow0oarn1: function (event) {
      this.$FormConfirm({
        title: '删除提示',
        message: `确认删除 ${event.row.keyword} 吗`,
        confirmText: '确认',
        confirmType: 'danger',
        cancelText: '取消'
      }).then(res => {
        this.deleteWord(event.row)
      }).catch(e => {

      })
    },

  }
})