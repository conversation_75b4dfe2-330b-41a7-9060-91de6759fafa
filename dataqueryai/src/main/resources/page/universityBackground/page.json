{"list": [{"isRoot": true, "type": "row", "comType": "page", "__tree_label": "页面", "__tree_node_key": "6599939628662372", "key": "page_16swhiz2", "uuid": "uuid_ya7z62xt", "children": [{"__tree_node_key": "886950577196115", "key": "row_waozrkby", "__tree_label": "区域容器", "comType": "row", "type": "row", "isCover": false, "children": [{"__tree_node_key": "9010931268798832", "key": "col_2gz92fj9", "__tree_label": "区块容器", "comType": "col", "type": "col", "isCover": false, "children": [{"uuid": "uuid_w9d8azo6", "key": "title_0rme55or", "com": "res-title", "comType": "title", "icon": "pm-icon-title", "comClassify": "com", "dataType": "string", "options": {"readonly": false, "showIcon": "false", "icon": "", "AfterIcon": "", "title": "高校行业背景知识", "titleSize": 16, "desc": "本页面所添加信息，会在提问触发关键词后，优先以背景知识解释内容进行回答。请以本校实际情况填写", "bindCom": {}, "hidden": false, "anchorLevel": 1, "enabledAnchor": false, "undefined": "", "afterIcon": "adiconfont admin-icon-Frame-25"}, "events": {"mounted": {"tip": "组件挂载"}, "beforeDestroy": {"tip": "组件销毁"}}, "name": "标题", "isCover": false, "dataOptions": {"remote": false, "remoteType": "dataset", "remoteModel": "", "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}}, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "res-title", "pageCode": "universityBackground"}], "options": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "pageCode": "universityBackground", "uuid": "uuid_txmajyah"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "0 0 0 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": false, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false, "bgType": "bgcolor", "backgroundColor": "", "borderRadius": 0, "borderWidth": 1, "borderPos": "borderBottom", "borderStyle": "solid", "borderColor": "#F0F0F0"}, "showTitle": false, "title": "容器标题", "layout": "grid", "pageCode": "universityBackground", "uuid": "uuid_8i22pov6"}, {"__tree_node_key": "47076555833152955", "key": "row_kmt6b9qc", "__tree_label": "区域容器", "comType": "row", "type": "row", "isCover": false, "children": [{"__tree_node_key": "29319487419176715", "key": "col_z7dn1web", "__tree_label": "区块容器", "comType": "col", "type": "col", "isCover": false, "children": [{"uuid": "uuid_aa0461ai", "key": "adv-search_2k8ikm9q", "com": "adv-search", "comType": "adv-search", "icon": "pm-icon-advancedSearch", "comClassify": "model", "dataType": "object", "options": {"readonly": false, "interacTable": "adv-table_tki6o41u", "limit": 2, "keyWordsLabel": "关键字/背景知识解释", "hideKeyWords": false, "comLinkEnabled": true, "labelWidth": 150, "keyWordsHighlight": true, "size": "", "itemWidth": 285, "singleItemWidth": 600, "tileItemWidth": 320, "searchTime": "instant", "conditionType": "", "hidden": false, "readyDoSearch": false, "hideNewFields": false, "closeModelSort": false, "showAdv": false, "fullscreen": false, "isCondition": false, "isMore": false, "beforeRender": {}, "showHidden": true, "appendToBody": true, "undefined": "", "beforeSearch": {}}, "events": {"mounted": {"tip": "组件挂载"}, "inited": {"tip": "模型和数据获取完成"}, "search": {"tip": "搜索之前，参数为querySetting(同步)"}, "before-reset": {"tip": "重置之前(同步)"}, "async-before-reset": {"tip": "重置之前(异步，参数为回调函数，执行查询需要调用回调)"}, "reset": {"tip": "重置后"}, "adv-reset": {"tip": "高级筛选重置后(需开启高级筛选功能)"}, "collapse": {"tip": "收起条件后"}, "expand": {"tip": "展开条件后"}, "item-change": {"tip": "每一项的值触发change后"}, "dict-loaded": {"tip": "每当有某一项的字典数据请求完成后"}, "dict-change": {"tip": "字典项控件的值发生改变时触发"}, "beforeDestroy": {"tip": "组件销毁"}}, "dataOptions": {"remote": true, "remoteType": "model", "remoteModel": {"actionType": "search", "id": "dataqueryai-dataqueryaibgknowledge", "url": "/admin/model/design/dataqueryai/perm/dataqueryaibgknowledge", "modelCascades": "", "modelName": "dataqueryaibgknowledge", "modelApp": "dataqueryai"}, "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "prefix": "search", "dataSource": {"type": "in", "url": "", "code": "", "params": {}}, "columnsModel": [{"name": "keyword"}, {"name": "explain"}, {"name": "id"}, {"name": "createBy"}, {"name": "createTime"}, {"name": "updateBy"}, {"name": "updateTime"}], "modelConfig": {"keyword": {"placeholder": "请输入", "search.omitted": 0, "fuzzySearch": 1}, "explain": {"placeholder": "请输入", "search.omitted": 0, "fuzzySearch": 1}}}, "name": "高级搜索", "isCover": false, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "adv-search", "pageCode": "universityBackground"}, {"uuid": "uuid_1hkbxynd", "key": "adv-table_tki6o41u", "com": "adv-vxe-table", "comType": "adv-table", "icon": "pm-icon-adv-table", "comClassify": "model", "dataType": "object", "options": {"readonly": false, "customClass": "", "labelWidth": 100, "bindPager": "", "bindComEmpty": {}, "bindComTop": {}, "bindComBottom": {}, "hidden": false, "editable": false, "hideNewFields": false, "closeModelSort": false, "autoLoad": true, "searchReserveData": false, "localDataSort": false, "scheme": true, "comLinkEnabled": true, "appendToBody": true, "tableConfig": {"border": "default", "seq": {"enabled": false, "seqTitle": "序号", "seqWidth": 60, "align": "center", "headerAlign": "center"}, "rowSort": {"enabled": false, "handle": "", "fixed": "left"}, "selectType": {"type": "", "checkTitle": "", "checkWidth": 40, "labelField": "", "reserve": true, "highlight": true, "range": true, "visibleMethodFunc": {}, "checkMethodFunc": {}, "align": "center", "headerAlign": "center"}, "showHeaderOverflow": true, "showOverflow": true, "fullHeight": false, "height": 0, "minHeight": 0, "maxHeight": 1000, "stripe": false, "emptyText": "暂无数据", "align": "left", "header-align": "left", "rowId": "id", "groupField": "tableColumnGroup", "groupEnabled": false, "beforeRenderFunc": {}, "spanMethodFunc": {}, "dataLoadFunc": {}}, "rowConfig": {"isHover": true, "isCurrent": false, "height": 46}, "statConfig": {"isShowStat": false, "statConfigList": []}, "reportConfig": {"reportText": "导出报表", "reportList": []}, "batchDownloadConfig": {"btnText": "批量下载", "btnConfigList": []}, "columnConfig": {"resizable": true, "minWidth": 80, "width": 0, "isHover": true, "isCurrent": false}, "flowBtnConfig": {"labelWidth": 120, "beforeRender": {}, "limit": 30, "fileSize": 100, "labelPosition": "left", "isTable": false, "listType": "text", "undefined": ""}, "customConfig": {"storage": true, "checkMethodFunc": {}}, "operationConfig": {"enabled": true, "position": "right", "tip": "", "title": "操作", "width": 100, "schema": "text", "buttonList": [{"label": "编辑", "id": "1vbde01p", "uuid": "ui3cfkgs", "type": "primary", "func": {"name": "action_ev_edit", "params": [{"name": "event", "des": "{row}:对应的行数据;{col}:对应的列数据;{btn}:点击的按钮；{scope}:表格对象"}], "enName": "编辑"}, "children": []}, {"label": "删除", "id": "v6qp2ews", "uuid": "5ys1ajdo", "type": "primary", "func": {"name": "action_ev_ow0oarn1", "enName": "删除"}, "children": []}], "buttonEditList": [], "renderFunc": {}, "template": "", "beforeDialog": {}}, "pagerConfig": {"pageStyle": "normal", "enabled": true, "pageSize": 20, "background": false, "pagerCount": 7, "hideOnSinglePage": false, "position": "right", "pageSizes": [5, 10, 20, 50, 100], "layouts": ["prev", "pager", "next", "sizes", "jumper"], "undefined": ""}, "toolbarConfig": {"flow": false, "import": false, "export": false, "print": false, "enabled": true, "zoom": false, "custom": false, "sort": false, "report": false, "batchDownload": false, "exportOptions": {"tooltip": {}}, "printOptions": {"tooltip": {}}, "zoomOptions": {"tooltip": {}}, "customOptions": {"tooltip": {}}, "importOptions": {"tooltip": {}}, "reportOptions": {"tooltip": {}}, "leftButtonList": {"schema": "button", "buttonList": [{"label": "新增", "id": "0yl3yfu5", "uuid": "j8wsyrjb", "type": "primary", "func": {"name": "action_ev_qt2zq9nw", "params": [{"name": "event", "des": "{btn}:点击的按钮;"}], "enName": "新增"}, "children": []}], "renderFunc": {}, "beforeDialog": {}}, "rightButtonList": {"schema": "text", "buttonList": [], "renderFunc": {}}}, "exportConfig": {"filename": "table_1742798579938", "dateFormat": "", "sheetName": "sheet", "type": "xlsx", "showOneClick": true, "showCustomClick": true, "oneClickBtnText": "一键导出", "customBtnText": "自定义导出", "types": ["xlsx"], "translateDictFlag": true, "apiUrl": ""}, "treeConfig": {"lazy": false, "transform": false, "parentField": "parentId", "hasChildField": "<PERSON><PERSON><PERSON><PERSON>", "expandAll": false, "accordion": false, "trigger": "default", "indent": 20, "treeNodeField": ""}, "editConfig": {"trigger": "click", "mode": "row", "showStatus": true, "showAsterisk": true, "autoClear": true, "beforeActiveEditMethodFunc": {}, "beforeEditMethodFunc": {}}, "editRules": {}, "importConfig": {"config": "file", "filename": "模板文件", "dateFormat": "", "isGenerateZdb": true, "customParam": ""}, "undefined": ""}, "dataOptions": {"remote": true, "remoteType": "model", "remoteModel": {"actionType": "table", "id": "dataqueryai-dataqueryaibgknowledge", "url": "/admin/model/design/dataqueryai/perm/dataqueryaibgknowledge", "modelCascades": "", "modelName": "dataqueryaibgknowledge", "modelApp": "dataqueryai"}, "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "prefix": "grid", "columnsModel": [{"name": "keyword"}, {"name": "explain"}, {"name": "id"}, {"name": "createBy"}, {"name": "createTime"}, {"name": "updateBy"}, {"name": "updateTime"}], "dataSource": {"type": "custom", "url": "/plugins/dataqueryai/bgKnowledge/list", "code": "", "dataPath": "rows", "params": {}}, "modelConfig": {}}, "events": {"mounted": {"tip": "组件挂载"}, "model-inited": {"tip": "模型获取完成"}, "reload-data": {"tip": "数据获取完成"}, "operate-click": {"tip": "操作列按钮点击"}, "radio-change": {"tip": "单选切换"}, "checkbox-change": {"tip": "多选点击"}, "checkbox-all": {"tip": "全选点击"}, "cell-click": {"tip": "单元格点击"}, "scroll": {"tip": "表格滚动"}, "page-change": {"tip": "切换当前页面"}, "size-change": {"tip": "每页数量变化"}, "zoom": {"tip": "表格切换最大化"}, "edit-closed": {"tip": "退出当前编辑"}, "row-sort-change": {"tip": "行拖动排序"}, "export-complete": {"tip": "导出完成"}, "startAndTakeUserTask": {"tip": "流程启动"}, "agree": {"tip": "流程同意"}, "startAndSaveDraft": {"tip": "流程保存草稿"}, "afterClick": {"tip": "按钮事件后触发后回调"}, "flowButtonSucceed": {"tip": "流程按钮执行成功后"}, "beforeDestroy": {"tip": "组件销毁"}}, "name": "高级表格", "isCover": false, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "adv-vxe-table", "pageCode": "universityBackground"}], "options": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "pageCode": "universityBackground", "uuid": "uuid_y97xmyuw"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "0 0 0 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": false, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false, "bgType": "bgcolor", "backgroundColor": "", "borderRadius": 0}, "showTitle": false, "title": "容器标题", "layout": "grid", "pageCode": "universityBackground", "uuid": "uuid_ixclat5j"}], "options": {"marginTop": 0, "marginBottom": 12, "marginLeft": 0, "marginRight": 0, "widthType": "0", "maxWidth": 0, "minWidth": 0, "width": "260px"}, "pageCode": "universityBackground"}], "leftList": [{"isRoot": true, "type": "row", "comType": "page", "__tree_label": "页面", "__tree_node_key": "9647981514438351", "key": "page_bl98i20h", "uuid": "uuid_j9kkvfvq", "children": [], "pageCode": "universityBackground"}], "rightList": [{"isRoot": true, "type": "row", "comType": "page", "__tree_label": "页面", "__tree_node_key": "7700164086361754", "key": "page_8vb4ajn9", "uuid": "uuid_64nyeqyb", "children": [], "pageCode": "universityBackground"}], "config": {"dataSource": [], "platform": "pc", "layout": "default", "leftWidth": 260, "rightWidth": 260, "foldingSwitchTop": 50, "foldingSwitch": false}, "dialogJson": [{"__tree_node_key": "7483266395950612", "key": "dialog_5iyvrl7h", "__tree_label": "区域容器", "comType": "row", "type": "row", "children": [{"__tree_node_key": "47521491619144607", "key": "col_0dv0kkfe", "__tree_label": "区块容器", "comType": "col", "type": "col", "children": [{"__tree_node_key": "35073516980192965", "key": "row_ldvir1qs", "__tree_label": "区域容器", "comType": "row", "type": "row", "children": [{"__tree_node_key": "10120481412534588", "key": "col_0cq7xced", "__tree_label": "区块容器", "comType": "col", "type": "col", "children": [{"uuid": "uuid_3h8oiu9d", "key": "data-form_ubqat815", "com": "data-form", "comType": "data-form", "icon": "pm-icon-mode-form", "comClassify": "model", "dataType": "object", "options": {"readonly": false, "titleSize": 16, "showIcon": "true", "width": 0, "formWidth": 0, "isVisualLoading": false, "openGroup": true, "showLabel": true, "labelWidth": 120, "labelPosition": "left", "labelShowType": "default", "tableForm": false, "column": 1, "autoLoad": false, "comLinkEnabled": true, "size": "", "hidden": false, "disabled": false, "groupType": "default", "hideNewFields": false, "closeModelSort": false, "isPrint": false, "validateGroup": "", "beforeRender": {}, "beforeRules": {}, "tipRight": false, "anchorLevel": 1, "enabledAnchor": false, "labelSup": "", "disDictRequest": false, "appendToBody": true, "undefined": "", "modelSort": {}}, "events": {"mounted": {"tip": "组件挂载"}, "inited": {"tip": "模型和数据获取完成"}, "dict-loaded": {"tip": "每当有某一项的字典数据请求完成后"}, "item-change": {"tip": "每一项的值触发change后"}, "dict-change": {"tip": "字典项控件的值发生改变时触发"}, "beforeDestroy": {"tip": "组件销毁"}}, "dataOptions": {"remote": true, "remoteType": "model", "remoteModel": {"actionType": "form", "id": "dataqueryai-dataqueryaibgknowledge", "url": "/admin/model/design/dataqueryai/perm/dataqueryaibgknowledge", "modelCascades": "", "modelName": "dataqueryaibgknowledge", "modelApp": "dataqueryai"}, "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "prefix": "form", "dataSource": {"type": "in", "url": "", "code": "", "dataPath": "", "params": {}}, "columnsModel": [{"name": "keyword"}, {"name": "explain"}, {"name": "id"}, {"name": "createBy"}, {"name": "createTime"}, {"name": "updateBy"}, {"name": "updateTime"}], "modelConfig": {"keyword": {"form.required": 1, "placeholder": "请输入", "form.omitted": 0, "form.JSONParam": {"showWordLimit": true}}, "explain": {"form.required": 1, "placeholder": "请输入", "form.omitted": 0, "form.JSONParam": {"showWordLimit": true}}}}, "name": "模型表单", "isCover": false, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "data-form", "pageCode": "universityBackground"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "events": {"mounted": "", "destroy": ""}, "pageCode": "universityBackground", "uuid": "uuid_4zy0gkgo"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "showTitle": false, "title": "容器标题", "events": {"mounted": "", "destroy": ""}, "layout": "grid", "pageCode": "universityBackground", "uuid": "uuid_eb64u4pm"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "0 2px 12px 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "events": {"mounted": "", "destroy": ""}, "pageCode": "universityBackground", "uuid": "uuid_fdjkabp5"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "0 2px 12px 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "showTitle": true, "title": "行业背景提示词", "events": {"mounted": {}, "destroy": {}}, "layout": "grid", "sign": "dialog", "btnsPosition": "right", "frameType": "1", "widthType": "pixel", "percentWidth": "50%", "size": "medium", "pixelWidth": "784", "commonOptions": {"fullscreen": "false", "closeOnClickModal": "false"}, "drawerOptions": {"frameDirection": "rtl", "wrapperClosable": "true"}, "btnOptions": [{"label": "取消", "id": "cancel_syy4qjur", "type": "", "func": {"name": "", "params": {}}}, {"label": "确认", "id": "confirm_r2nlpy1o", "type": "primary", "func": {"name": "action_ev_c2zlngyr", "params": [{"name": "event", "des": "事件相关参数"}, {"name": "closeNext", "des": "需要调用该方法才能关闭弹窗"}], "enName": "确认"}}], "btnRenderFunc": {}, "pageCode": "universityBackground", "uuid": "uuid_tlqviskc", "resetValues": true}], "suspendJson": []}