define(function (require) {
  return {
    /**
     * 监听指定对象的值发生变化，然后执行特定方法
     */
    pageWatch: {
      //  'globalVars.name':function(newVal,oldVal){
      //      console.log('值改变',newVal,oldVal)
      //  }
    },
    /**
     * 监听指定对象的值发生变化，然后执行特定方法
     */
    pageComputed: {
      // getNewName:function(){
      //   return this.globalVars.name+'新的'
      //}
    },
    // var1:"变量",//响应式变量，该变量不能加到组件参数内,但是写到组件模板里
    /**
     * 响应式变量，当值变化时可以影响所有绑定的值,建议把需要绑定到组件参数里的变量申明到这里面
     */
    globalVars: {
      searchKey: '',
      currentRow: {},
      roleList: [],
      allRoleList: [],
      checkRoleId: '',
      isIndeterminate: false,
      checkedFields: [],
      checkAll: false,
      perData: {},
      fieldList: [],
      fieldPerList: [
        { label: '全部不可见', value: "0" },
        { label: '全部可见', value: "1" },
        { label: '部分可见', value: "2" }
      ],
      dataPerList: [
        { label: '全部', value: "1" },
        { label: '本学院', value: "2" },
        { label: '个人', value: "3" }
      ],
      ruleForm: {
        roleId: ''
      },
      rules: {
        roleId: [
          { required: true, message: '请选择角色', trigger: 'change' }
        ]
      },
      subjectList: []
      // name:'一个变量' //响应式变量name,组件内使用时{{globalVars.name}}
    },
    /**
     * 页面被重新激活时调用
     */
    pageActivated: function () {
      //console.log('页面激活')  
    },
    /**
     * 页面失去激活被缓存时调用
     */
    pageDeactivated: function () {
      //console.log('页面失活')  
    },
    /**
     * 固定方法，页面js初始化完成后调用,当前能修改js变量，修改组件初始化属性或者设置组件默认值
     */
    pageCreated: function () {
      //console.log('页面js初始化完成后调用')
      
    },
    /**
     * 固定方法，页面准备完成后调用，当前可以操作组件属性，调用未隐藏组件实例方法
     */
    pageReady: function () {
      //console.log('页面准备完成后调用')
      this.getRoleList()
    },
    /**
     * 固定方法，页面销毁前调用
     */
    pageDestroy: function () {
      //console.log('页面销毁前调用')
    },
    getFieldLabel(label) {
      let oF = this.globalVars.fieldPerList.find(ele => ele.value == label)
      return oF ? oF.label : '-'
    },
    getDataPerLabel(label) {
      let oF = this.globalVars.dataPerList.find(ele => ele.value == label)
      return oF ? oF.label : '-'
    },
    getImg(img) {
      return window.$GLOBAL_CONFIG.resRoot + '/szsz_da/dataqueryai/' + img
    },
    getRoleList() {
      this.$request({
        method: "post",
        url: "/toolpub/userGroup/appAdmin/dataqueryai"
      }).then(res => {
        if (res.code == 0) {
          this.globalVars.allRoleList = window._.cloneDeep(res.data || [])
          this.globalVars.roleList = window._.cloneDeep(res.data || [])
          this.globalVars.checkRoleId = this.globalVars.roleList[0]?.roleId
          this.$page("adv-table_id4l8dds").reload()
          this.globalVars.ruleForm.roleId = this.globalVars.roleList[1]?.roleId
        } else {
          this.$message.error(res.msg || '角色请求失败')
        }
      })
    },
    searchRole() {
      this.globalVars.roleList = this.globalVars.allRoleList.filter(ele => ele.roleName.includes(this.globalVars.searchKey.trim()))
     if (this.globalVars.roleList.length > 0) {
        this.checkRole(this.globalVars.roleList[0])
      }
    },
    checkRole(item) {
      this.globalVars.checkRoleId = item.roleId
      this.$page("adv-table_id4l8dds").reload()
      this.globalVars.ruleForm.roleId = this.globalVars.roleList.filter(ele => ele.roleId != item.roleId)[0]?.roleId || ''
    },
    saveRolePermission(closeNext) {
      let submitData = {
        ...this.globalVars.currentRow, 
        ...this.globalVars.perData,
        modelRoleId: this.globalVars.checkRoleId
      }
      this.$request({
        method: "post",
        url: "/plugins/dataqueryai/model/role/saveModelRole",
        data: submitData
      }).then(res => {
        if (res.code == 0) {
          this.$message.success('保存成功')
          this.$page("adv-table_id4l8dds").reload()
          closeNext()
        } else {
          this.$message.error(res.msg || '保存失败')
        }
      })
    },
    action_ev_ll6ymchs(event) {
      this.globalVars.currentRow = event.row
      this.$request({
        method: "post",
        url: "/plugins/dataqueryai/model/role/getModelDetail",
        data: event.row
      }).then(res => {
        if (res.code == 0) {
          this.globalVars.perData = res.data
          this.remoteData2CheckedFields()
          this.handleCheckedFieldsChange()
          this.globalVars.$perDialig = this.$pageDialog({ key: 'dialog_8u96fy1r', params: {}, winParams: {} })
        } else {
          this.$message.error(res.msg || '权限请求失败')
        }
      })
    },
    remoteData2CheckedFields() {
      this.globalVars.checkedFields = []
      this.globalVars.perData.columns.forEach(ele => {
        if (ele.columnAllowed == "1") {
          this.globalVars.checkedFields.push(ele.modelColumnId)
        }
      })
    },
    checkedFields2RemoveData() {
      this.globalVars.perData.columns.forEach(ele => {
        if (this.globalVars.checkedFields.includes(ele.modelColumnId)) {
          ele.columnAllowed = "1"
        } else {
          ele.columnAllowed = "0"
        }
      })
    },
    handleCheckAllChange(val) {
      console.log("val:", val)
      if (val) {
        this.globalVars.checkedFields = this.globalVars.perData.columns.reduce((a, b) => {
          return a.concat(b.modelColumnId)
        }, [])
      } else {
        this.globalVars.checkedFields = []
      }
      this.globalVars.isIndeterminate = false
    },
    handleCheckedFieldsChange() {
      if (this.globalVars.checkedFields.length > 0 && this.globalVars.checkedFields.length == this.globalVars.perData.columns.length) {
        this.globalVars.isIndeterminate = false
        this.globalVars.checkAll = true
      }
      if (this.globalVars.checkedFields.length != this.globalVars.perData.columns.length) {
        this.globalVars.checkAll = false
        if (this.globalVars.checkedFields.length == 0) {
          this.globalVars.isIndeterminate = false
        } else {
          this.globalVars.isIndeterminate = true
        }
      }
      console.log("this.globalVars.checkAll:", this.globalVars.checkAll)
      console.log("this.globalVars.isIndeterminate:", this.globalVars.isIndeterminate)

    },


    /**
     * 描述：确认
     * @param{event}  事件相关参数 return:
     * @param{closeNext}  需要调用该方法才能关闭弹窗 return:

     */
    action_ev_tvtu6bjh: function (event, closeNext) {
      var self = this;
      console.log("this.globalVars.perData:", this.globalVars.perData)
      console.log("this.globalVars.checkedFields:", this.globalVars.checkedFields)
      this.checkedFields2RemoveData()
      this.saveRolePermission(closeNext)
    },


    /**
     * 描述：一键复制
     * @param{event}  {btn}:点击的按钮; return:

     */
    action_ev_eka9vx91: function (event) {
      var self = this;
      this.$pageDialog({ key: 'dialog_3aau9vml', params: {}, winParams: {} })
    },


    /**
     * 描述：一键开启
     * @param{event}  {btn}:点击的按钮; return:

     */
    action_ev_y576pm1k: function (event) {
      var self = this;
      this.$FormConfirm({
        title: '一键开启提示',
        message: '确认一键开启所有模型权限吗？',
        confirmText: '确认',
        confirmType: 'primary',
        cancelText: '取消'
      }).then(res => {
        this.openAllPer()
      })
    },
    openAllPer() {
      this.$request({
        url: '/plugins/dataqueryai/model/role/enbaleModels?roleId=' + this.globalVars.checkRoleId,
        method: 'post',
      }).then(res => {
        if (res.code == 0) {
          this.$message.success('一键开启成功')
          this.$page("adv-table_id4l8dds").reload()
        } else {
          this.$message.error(res.msg || '一键开启失败')
        }
      })
    },


    /**
     * 描述：确认
     * @param{event}  事件相关参数 return:
     * @param{closeNext}  需要调用该方法才能关闭弹窗 return:

     */
    action_ev_12fvlr0r: function (event, closeNext) {
      var self = this;
      this.$request({
        url: `/plugins/dataqueryai/model/role/copyRoleModels?targetRoleId=${this.globalVars.checkRoleId}&fromRoleId=${this.globalVars.ruleForm.roleId}`,
        method: 'post',
      }).then(res => {
        if (res.code == 0) {
          this.$message.success('一键复制成功')
          closeNext()
          this.$page("adv-table_id4l8dds").reload()
        } else {
          this.$message.error(res.msg || '一键复制失败')
        }
      })
      
    },

  }
})