.generate-page-modelPermissionContent{
    .model-list-container{
        .model-title{
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 36px;
            .title{
                font-size: 14px;
                font-weight: 700;
                line-height: normal;
                letter-spacing: normal;
                color: rgba(0, 0, 0, 0.85);
            }
        }
        .model-search{
            display: flex;
            position: relative;
            margin-top: 8px;
            .el-input__inner{
                padding-right: 20px;
            }
            .el-input__suffix{
                right: 30px;
            }
            .el-button{
                position: absolute;
                right: 10px;
                color: #4e5969 !important;
            }
        }
        .role-container{
            margin-top: 20px;
            .role-content{
                display: grid;
                row-gap: 8px;
            }
            .role-list{
                display: flex;
                align-items: center;
                padding: 10px 8px;
                cursor: pointer;
                &.role-list-active{
                    background: #FAFAFA;
                    .role-label{
                        color: #165dff;
                    }
                }
                .role-img{
                    width: 12px;
                    height: 12px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-right: 12px;
                    img{
                        display: block;
                        max-width: 100%;
                        max-height: 100%;
                    }
                }
                .role-label{
                    flex: 1;
                    width: 0;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }
        }
    }
}
.dialog_8u96fy1r{
    .permission-container{
        display: grid;
        row-gap: 12px;
        .permission-content{
            background: #F7F8FA;
            border-radius: 4px;
            padding: 16px;
        }
        .permission-top{
            justify-content: space-between;
            display: flex;
            .permission-see{
                flex-shrink: 0;
            }
        }
        .permission-center{
            display: flex;
            justify-content: space-between;
            .permission-range{
                display: flex;
                width: calc(50% - 6px);
                align-items: center;
                margin-right: 12px;
                justify-content: space-between;
                &:last-child{
                    margin-left: 0px;
                }
                .el-select{
                    margin-left: 8px;
                    width: 100px;
                }
            }
        }
        .permission-bottom{
            .permission-title{
                margin-bottom: 12px;
            }
            .el-checkbox-group{
                display: flex;
                flex-wrap: wrap;
                .el-checkbox{
                    width: calc(50% - 6px);
                    margin-right: 12px;
                    margin-bottom: 6px;
                    display: flex;
                    align-items: center;
                    .el-checkbox__label{
                        display: block;
                        text-overflow: ellipsis;
                        overflow: hidden;
                        white-space: nowrap;
                    }
                    &:nth-child(2n){
                        margin-right: 0px;
                    }
                }
            }
        }
    }
}

.dialog_3aau9vml{
    .copy-container{
        .copy-content{
            border-radius: 4px;
            padding: 8px 16px;
            background: rgba(22, 93, 255, 0.1);
            display: flex;
            margin-bottom: 30px;
            .el-icon{
                align-self: flex-start;
                margin-right: 12px;
                i{
                    color: #165dff;
                }
            }
            .copy-tip-content{
                .tip{
                    margin-bottom: 8px;
                    font-size: 14px;
                    color: #3D3D3D;
                    .blue{
                        color: #165dff;
                    }
                    &.tip2{
                        margin-bottom: 0px;
                    }
                }
            }
        }
    }
}