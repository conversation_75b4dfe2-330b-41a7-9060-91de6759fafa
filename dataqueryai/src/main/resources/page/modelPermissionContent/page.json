{"list": [{"isRoot": true, "type": "row", "comType": "page", "__tree_label": "页面", "__tree_node_key": "974071779217659", "key": "page_l4yx6czq", "uuid": "uuid_hpa1sm6r", "children": [{"__tree_node_key": "6776934618161874", "key": "row_9d632u7y", "__tree_label": "区域容器", "comType": "row", "type": "row", "isCover": false, "children": [{"__tree_node_key": "8732490486858062", "key": "col_29whoqjg", "__tree_label": "区块容器", "comType": "col", "type": "col", "isCover": false, "children": [{"uuid": "uuid_3jdylx79", "key": "adv-search_13b1l4mm", "com": "adv-search", "comType": "adv-search", "icon": "pm-icon-advancedSearch", "comClassify": "model", "dataType": "object", "options": {"readonly": false, "interacTable": "adv-table_id4l8dds", "limit": 2, "keyWordsLabel": "关键字", "hideKeyWords": false, "comLinkEnabled": true, "labelWidth": 100, "keyWordsHighlight": true, "size": "", "itemWidth": 285, "singleItemWidth": 400, "tileItemWidth": 320, "searchTime": "", "conditionType": "", "hidden": false, "readyDoSearch": false, "hideNewFields": false, "closeModelSort": false, "showAdv": false, "fullscreen": false, "isCondition": false, "isMore": false, "beforeRender": {}, "showHidden": true, "appendToBody": true, "undefined": "", "beforeSearch": {}}, "events": {"mounted": {"tip": "组件挂载"}, "inited": {"tip": "模型和数据获取完成"}, "search": {"tip": "搜索之前，参数为querySetting(同步)"}, "before-reset": {"tip": "重置之前(同步)"}, "async-before-reset": {"tip": "重置之前(异步，参数为回调函数，执行查询需要调用回调)"}, "reset": {"tip": "重置后"}, "adv-reset": {"tip": "高级筛选重置后(需开启高级筛选功能)"}, "collapse": {"tip": "收起条件后"}, "expand": {"tip": "展开条件后"}, "item-change": {"tip": "每一项的值触发change后"}, "dict-loaded": {"tip": "每当有某一项的字典数据请求完成后"}, "dict-change": {"tip": "字典项控件的值发生改变时触发"}, "beforeDestroy": {"tip": "组件销毁"}}, "dataOptions": {"remote": true, "remoteType": "model", "remoteModel": {"actionType": "search", "id": "dataqueryai-dataqueryaimodelrolebo", "url": "/admin/model/design/dataqueryai/perm/dataqueryaimodelrolebo", "modelCascades": "", "modelName": "dataqueryaimodelrolebo", "modelApp": "dataqueryai"}, "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "prefix": "search", "dataSource": {"type": "in", "url": "", "code": "", "params": {}}, "columnsModel": [{"name": "topicName"}, {"name": "modelName"}, {"name": "id"}, {"name": "userRoleId"}, {"name": "topicModelId"}, {"name": "visibleRole"}, {"name": "columnRole"}, {"name": "rowRole"}, {"name": "rowRoleRefColumnDbname"}, {"name": "modelId"}, {"name": "modelTable"}, {"name": "topicId"}], "modelConfig": {"topicName": {"placeholder": "请输入", "search.omitted": 0, "search.xtype": "multi-select", "search.JSONParam": {"dictParams": {"url": "/plugins/dataqueryai/topic/list", "dataPath": "rows", "querySetting": [], "label": "topicName", "value": "topicName", "children": "children"}}}, "id": {"search.hidden": 1}, "userRoleId": {"search.hidden": 1}, "topicModelId": {"search.hidden": 1}, "visibleRole": {"search.hidden": 1}, "columnRole": {"search.hidden": 1}, "rowRole": {"search.hidden": 1}, "rowRoleRefColumnDbname": {"search.hidden": 1}, "modelId": {"search.hidden": 1}, "modelTable": {"search.hidden": 1}, "topicId": {"placeholder": "请输入", "search.omitted": 0, "search.hidden": 1}}}, "name": "高级搜索", "isCover": false, "interactList": [], "comStyles": {"marginBottom": "16"}, "__tree_label": "adv-search", "pageCode": "modelPermissionContent"}, {"uuid": "uuid_28jlpyq5", "key": "adv-table_id4l8dds", "com": "adv-vxe-table", "comType": "adv-table", "icon": "pm-icon-adv-table", "comClassify": "model", "dataType": "object", "options": {"readonly": false, "customClass": "", "labelWidth": 100, "bindPager": "", "bindComEmpty": {}, "bindComTop": {}, "bindComBottom": {}, "hidden": false, "editable": false, "hideNewFields": false, "closeModelSort": false, "autoLoad": false, "searchReserveData": false, "localDataSort": false, "scheme": true, "comLinkEnabled": true, "appendToBody": true, "tableConfig": {"border": "default", "seq": {"enabled": false, "seqTitle": "序号", "seqWidth": 60, "align": "center", "headerAlign": "center"}, "rowSort": {"enabled": false, "handle": "", "fixed": "left"}, "selectType": {"type": "", "checkTitle": "", "checkWidth": 40, "labelField": "", "reserve": true, "highlight": true, "range": true, "visibleMethodFunc": {}, "checkMethodFunc": {}, "align": "center", "headerAlign": "center"}, "showHeaderOverflow": true, "showOverflow": true, "fullHeight": false, "height": 0, "minHeight": 0, "maxHeight": 700, "stripe": false, "emptyText": "暂无数据", "align": "left", "header-align": "left", "rowId": "id", "groupField": "tableColumnGroup", "groupEnabled": false, "beforeRenderFunc": {}, "spanMethodFunc": {}, "dataLoadFunc": {}}, "rowConfig": {"isHover": true, "isCurrent": false, "height": 46}, "statConfig": {"isShowStat": false, "statConfigList": []}, "reportConfig": {"reportText": "导出报表", "reportList": []}, "batchDownloadConfig": {"btnText": "批量下载", "btnConfigList": []}, "columnConfig": {"resizable": true, "minWidth": 80, "width": 0, "isHover": true, "isCurrent": false}, "flowBtnConfig": {"labelWidth": 120, "beforeRender": {}, "limit": 30, "fileSize": 100, "labelPosition": "left", "isTable": false, "listType": "text", "undefined": ""}, "customConfig": {"storage": true, "checkMethodFunc": {}}, "operationConfig": {"enabled": true, "position": "right", "tip": "", "title": "操作", "width": 100, "schema": "text", "buttonList": [{"label": "授权", "id": "kvxh28at", "uuid": "j5evz5hf", "type": "primary", "func": {"name": "action_ev_ll6ymchs", "enName": "授权", "params": [{"name": "event", "des": "{row}:对应的行数据;{col}:对应的列数据;{btn}:点击的按钮；{scope}:表格对象"}]}, "children": [], "icon": "adiconfont admin-icon-Frame1"}], "buttonEditList": [], "renderFunc": {}, "template": "", "beforeDialog": {}}, "pagerConfig": {"pageStyle": "normal", "enabled": true, "pageSize": 20, "background": false, "pagerCount": 7, "hideOnSinglePage": false, "position": "right", "pageSizes": [5, 10, 20, 50, 100], "layouts": ["prev", "pager", "next", "sizes", "jumper"], "undefined": ""}, "toolbarConfig": {"flow": false, "import": false, "export": false, "print": false, "enabled": true, "zoom": false, "custom": false, "sort": false, "report": false, "batchDownload": false, "exportOptions": {"tooltip": {}}, "printOptions": {"tooltip": {}}, "zoomOptions": {"tooltip": {}}, "customOptions": {"tooltip": {}}, "importOptions": {"tooltip": {}}, "reportOptions": {"tooltip": {}}, "leftButtonList": {"schema": "text", "buttonList": [], "renderFunc": {}, "beforeDialog": {}}, "rightButtonList": {"schema": "button", "buttonList": [{"label": "一键复制", "id": "5tt9yvxh", "uuid": "g87rmqre", "type": "", "func": {"name": "action_ev_eka9vx91", "params": [{"name": "event", "des": "{btn}:点击的按钮;"}], "enName": "一键复制"}, "children": []}, {"label": "一键开启", "id": "4gswh3rg", "uuid": "5kg4i1pu", "type": "primary", "func": {"name": "action_ev_y576pm1k", "enName": "一键开启"}, "children": []}], "renderFunc": {}}}, "exportConfig": {"filename": "table_1742952667343", "dateFormat": "", "sheetName": "sheet", "type": "xlsx", "showOneClick": true, "showCustomClick": true, "oneClickBtnText": "一键导出", "customBtnText": "自定义导出", "types": ["xlsx"], "translateDictFlag": true, "apiUrl": ""}, "treeConfig": {"lazy": false, "transform": false, "parentField": "parentId", "hasChildField": "<PERSON><PERSON><PERSON><PERSON>", "expandAll": false, "accordion": false, "trigger": "default", "indent": 20, "treeNodeField": ""}, "editConfig": {"trigger": "click", "mode": "row", "showStatus": true, "showAsterisk": true, "autoClear": true, "beforeActiveEditMethodFunc": {}, "beforeEditMethodFunc": {}}, "editRules": {}, "importConfig": {"config": "file", "filename": "模板文件", "dateFormat": "", "isGenerateZdb": true, "customParam": ""}, "undefined": ""}, "dataOptions": {"remote": true, "remoteType": "model", "remoteModel": {"actionType": "table", "id": "dataqueryai-dataqueryaimodelrolebo", "url": "/admin/model/design/dataqueryai/perm/dataqueryaimodelrolebo", "modelCascades": "", "modelName": "dataqueryaimodelrolebo", "modelApp": "dataqueryai"}, "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "prefix": "grid", "columnsModel": [{"name": "topicName"}, {"name": "modelName"}, {"name": "columnRole"}, {"name": "rowRole"}, {"name": "id"}, {"name": "userRoleId"}, {"name": "topicModelId"}, {"name": "visibleRole"}, {"name": "rowRoleRefColumnDbname"}, {"name": "modelId"}, {"name": "modelTable"}, {"name": "topicId"}], "dataSource": {"type": "custom", "url": "/plugins/dataqueryai/model/role/queryModles", "code": "", "dataPath": "rows", "params": {"roleId": "{vars.checkRoleId}"}}, "modelConfig": {"topicName": {"placeholder": "请输入", "grid.width": 0, "grid.omitted": 0, "grid.sortable": 0}, "columnRole": {"placeholder": "请输入", "grid.width": 0, "grid.omitted": 0, "grid.sortable": 0, "grid.template": "<span>{{page.getFieldLabel(row[field])}}</span>", "caption": "字段权限"}, "rowRole": {"placeholder": "请输入", "grid.width": 0, "grid.omitted": 0, "grid.sortable": 0, "grid.template": "<span>{{page.getDataPerLabel(row[field])}}</span>", "caption": "数据行权限"}, "id": {"grid.hidden": 1}, "userRoleId": {"grid.hidden": 1}, "topicModelId": {"grid.hidden": 1}, "visibleRole": {"grid.hidden": 1}, "rowRoleRefColumnDbname": {"grid.hidden": 1}, "modelId": {"grid.hidden": 1}, "modelTable": {"grid.hidden": 1}, "topicId": {"placeholder": "请输入", "grid.width": 0, "grid.omitted": 0, "grid.sortable": 0, "grid.hidden": 1}}}, "events": {"mounted": {"tip": "组件挂载"}, "model-inited": {"tip": "模型获取完成"}, "reload-data": {"tip": "数据获取完成"}, "operate-click": {"tip": "操作列按钮点击"}, "radio-change": {"tip": "单选切换"}, "checkbox-change": {"tip": "多选点击"}, "checkbox-all": {"tip": "全选点击"}, "cell-click": {"tip": "单元格点击"}, "scroll": {"tip": "表格滚动"}, "page-change": {"tip": "切换当前页面"}, "size-change": {"tip": "每页数量变化"}, "zoom": {"tip": "表格切换最大化"}, "edit-closed": {"tip": "退出当前编辑"}, "row-sort-change": {"tip": "行拖动排序"}, "export-complete": {"tip": "导出完成"}, "startAndTakeUserTask": {"tip": "流程启动"}, "agree": {"tip": "流程同意"}, "startAndSaveDraft": {"tip": "流程保存草稿"}, "afterClick": {"tip": "按钮事件后触发后回调"}, "flowButtonSucceed": {"tip": "流程按钮执行成功后"}, "beforeDestroy": {"tip": "组件销毁"}}, "name": "高级表格", "isCover": false, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "adv-vxe-table", "pageCode": "modelPermissionContent"}], "options": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "pageCode": "modelPermissionContent", "uuid": "uuid_ofxl2gj2"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "0 0 0 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": false, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false, "bgType": "bgcolor", "backgroundColor": "", "borderRadius": 0}, "showTitle": false, "title": "容器标题", "layout": "grid", "pageCode": "modelPermissionContent", "uuid": "uuid_p2mlpckl"}], "options": {"marginTop": 0, "marginBottom": 12, "marginLeft": 0, "marginRight": 0, "widthType": "0", "maxWidth": 0, "minWidth": 0, "width": "260px"}, "pageCode": "modelPermissionContent"}], "leftList": [{"isRoot": true, "type": "row", "comType": "page", "__tree_label": "页面", "__tree_node_key": "472969294255275", "key": "page_t4k968sj", "uuid": "uuid_9ker4mj8", "children": [{"__tree_node_key": "5431184994534972", "key": "row_ws45dsgb", "__tree_label": "区域容器", "comType": "row", "type": "row", "isCover": false, "children": [{"__tree_node_key": "36227424825420673", "key": "col_irsh7c4w", "__tree_label": "区块容器", "comType": "col", "type": "col", "isCover": false, "children": [{"uuid": "uuid_kz3cv0rf", "key": "component_soghbcem", "com": "res-com-template", "comType": "component", "icon": "pm-icon-vue", "options": {"readonly": false, "customClass": "", "labelWidth": 100, "isLabelWidth": false, "hidden": false, "template": "<div class=\"model-list-container\">\r\n    <div class=\"model-title\">\r\n        <div class=\"title\">\r\n            用户组列表\r\n        </div>\r\n    </div>\r\n    <div class=\"model-search\">\r\n        <el-input @keyup.enter.native=\"page.searchRole\" clearable @clear=\"page.searchRole\" placeholder=\"请输入\"\r\n            v-model=\"globalVars.searchKey\">\r\n        </el-input>\r\n        <el-button type=\"text\" @click=\"page.searchRole\" icon=\"el-icon-search\"></el-button>\r\n    </div>\r\n    <div class=\"role-container\">\r\n        <div class=\"role-content\">\r\n            <div v-for=\"item in globalVars.roleList\" @click=\"page.checkRole(item)\"\r\n                :class=\"['role-list', { 'role-list-active': globalVars.checkRoleId == item.roleId }]\">\r\n                <div class=\"role-img\">\r\n                    <img :src=\"page.getImg('fold.png')\" />\r\n                </div>\r\n                <div class=\"role-label\" :title=\"item.roleName\">\r\n                    {{item.roleName}}\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>", "required": false, "pattern": "", "validator": ""}, "dataOptions": {"remote": true, "remoteType": "dataset", "remoteModel": "", "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "dataSource": {"type": "custom", "params": {}, "code": "", "url": ""}}, "events": {"mounted": {"tip": "组件挂载"}, "change": {"tip": "内部绑定的表单组件值发生变化(是否触发根据内部手写的组件有关)"}, "beforeDestroy": {"tip": "组件销毁"}}, "name": "vue模板", "isCover": false, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "res-com-template", "pageCode": "modelPermissionContent"}], "options": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "pageCode": "modelPermissionContent", "uuid": "uuid_vw9uryqq"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "0 0 0 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": false, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false, "bgType": "bgcolor", "backgroundColor": "", "borderRadius": 0}, "showTitle": false, "title": "容器标题", "layout": "grid", "pageCode": "modelPermissionContent", "uuid": "uuid_ei9zfo8c"}], "pageCode": "modelPermissionContent"}], "rightList": [{"isRoot": true, "type": "row", "comType": "page", "__tree_label": "页面", "__tree_node_key": "12837121330931", "key": "page_hbdr6pg4", "uuid": "uuid_qjyxn4s6", "children": [], "pageCode": "modelPermissionContent"}], "config": {"dataSource": [], "platform": "pc", "layout": "left", "leftWidth": 260, "rightWidth": 260, "foldingSwitchTop": 50, "foldingSwitch": false}, "dialogJson": [{"__tree_node_key": "6758577625052606", "key": "dialog_8u96fy1r", "__tree_label": "区域容器", "comType": "row", "type": "row", "children": [{"__tree_node_key": "7496579410531004", "key": "col_ya49hn2l", "__tree_label": "区块容器", "comType": "col", "type": "col", "children": [{"__tree_node_key": "1720008130139159", "key": "row_tgsf57nv", "__tree_label": "区域容器", "comType": "row", "type": "row", "children": [{"__tree_node_key": "7812048230424078", "key": "col_o0zke81d", "__tree_label": "区块容器", "comType": "col", "type": "col", "children": [{"uuid": "uuid_k689dgio", "key": "component_o15hip0b", "com": "res-com-template", "comType": "component", "icon": "pm-icon-vue", "options": {"readonly": false, "customClass": "", "labelWidth": 100, "isLabelWidth": false, "hidden": false, "template": "<div class=\"permission-container\">\r\n    <div class=\"permission-content permission-top\">\r\n        <div class=\"top-title\">\r\n            {{globalVars.perData.modelName}}\r\n        </div>\r\n        <div class=\"permission-see\">\r\n            <span>是否可见</span>\r\n            <el-switch active-value=\"1\" inactive-value=\"0\" v-model=\"globalVars.perData.visibleRole\">\r\n            </el-switch>\r\n        </div>\r\n    </div>\r\n    <div class=\"permission-content permission-center\" v-if=\"globalVars.perData.visibleRole == 1\">\r\n        <div class=\"permission-range\">\r\n            <span class=\"permission-seerange\">可查数据范围</span>\r\n            <el-select v-model=\"globalVars.perData.rowRole\" placeholder=\"请选择\">\r\n                <el-option v-for=\"item in globalVars.dataPerList\" :key=\"item.value\" :label=\"item.label\"\r\n                    :value=\"item.value\">\r\n                </el-option>\r\n            </el-select>\r\n        </div>\r\n        <div class=\"permission-range\" v-if=\"globalVars.perData.rowRole != 1\">\r\n            <span class=\"permission-rolerange\">角色范围字段</span>\r\n            <el-select v-model=\"globalVars.perData.rowRoleRefColumnDbname\" placeholder=\"请选择\">\r\n                <el-option v-for=\"item in globalVars.perData.columns\" :key=\"item.columnDbname\"\r\n                    :label=\"item.columnLabel\" :value=\"item.columnDbname\">\r\n                </el-option>\r\n            </el-select>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"permission-content permission-bottom\" v-if=\"globalVars.perData.visibleRole == 1\">\r\n        <div class=\"permission-title\">\r\n            可查字段范围\r\n        </div>\r\n        <div class=\"permission-range\">\r\n            <div class=\"permission-field\">\r\n                <el-checkbox :indeterminate=\"globalVars.isIndeterminate\" v-model=\"globalVars.checkAll\"\r\n                    @change=\"page.handleCheckAllChange\">全选</el-checkbox>\r\n                <div style=\"margin: 6px 0;\"></div>\r\n                <el-checkbox-group v-model=\"globalVars.checkedFields\" @change=\"page.handleCheckedFieldsChange\">\r\n                    <el-checkbox v-for=\"item in globalVars.perData.columns\" :label=\"item.modelColumnId\"\r\n                        :key=\"item.modelColumnId\" :title=\"item.columnLabel\">{{item.columnLabel}}</el-checkbox>\r\n                </el-checkbox-group>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>", "required": false, "pattern": "", "validator": ""}, "dataOptions": {"remote": true, "remoteType": "dataset", "remoteModel": "", "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "dataSource": {"type": "custom", "params": {}, "code": "", "url": ""}}, "events": {"mounted": {"tip": "组件挂载"}, "change": {"tip": "内部绑定的表单组件值发生变化(是否触发根据内部手写的组件有关)"}, "beforeDestroy": {"tip": "组件销毁"}}, "name": "vue模板", "isCover": false, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "res-com-template", "pageCode": "modelPermissionContent"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "events": {"mounted": "", "destroy": ""}, "pageCode": "modelPermissionContent", "uuid": "uuid_g5b4r8tt"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "showTitle": false, "title": "容器标题", "events": {"mounted": "", "destroy": ""}, "layout": "grid", "pageCode": "modelPermissionContent", "uuid": "uuid_pk86skmo"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "0 2px 12px 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "events": {"mounted": "", "destroy": ""}, "pageCode": "modelPermissionContent", "uuid": "uuid_9ubohbzp"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "0 2px 12px 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "showTitle": true, "title": "模型授权", "events": {"mounted": {}, "destroy": {}}, "layout": "grid", "sign": "dialog", "btnsPosition": "right", "frameType": "2", "widthType": "pixel", "percentWidth": "50%", "size": "medium", "pixelWidth": "484", "commonOptions": {"fullscreen": "false", "closeOnClickModal": "false"}, "drawerOptions": {"frameDirection": "rtl", "wrapperClosable": false}, "btnOptions": [{"label": "取消", "id": "cancel_u4z8x5l1", "type": "", "func": {"name": "", "params": {}}}, {"label": "确认", "id": "confirm_qsa1vedj", "type": "primary", "func": {"name": "action_ev_tvtu6bjh", "params": [{"name": "event", "des": "事件相关参数"}, {"name": "closeNext", "des": "需要调用该方法才能关闭弹窗"}], "enName": "确认"}}], "btnRenderFunc": {}, "windowsize": "484", "pageCode": "modelPermissionContent", "uuid": "uuid_652mzd1q"}, {"__tree_node_key": "6795060305707701", "key": "dialog_3aau9vml", "__tree_label": "区域容器", "comType": "row", "type": "row", "children": [{"__tree_node_key": "41218710029740624", "key": "col_9wptuljg", "__tree_label": "区块容器", "comType": "col", "type": "col", "children": [{"__tree_node_key": "09582051900401489", "key": "row_o3zs2i58", "__tree_label": "区域容器", "comType": "row", "type": "row", "children": [{"__tree_node_key": "6201459188137377", "key": "col_btd0dqkm", "__tree_label": "区块容器", "comType": "col", "type": "col", "children": [{"uuid": "uuid_ouklrinq", "key": "component_pqpkvkqw", "com": "res-com-template", "comType": "component", "icon": "pm-icon-vue", "options": {"readonly": false, "customClass": "", "labelWidth": 100, "isLabelWidth": false, "hidden": false, "template": "<div class=\"copy-container\">\r\n    <div class=\"copy-content\">\r\n        <div class=\"el-icon\">\r\n            <i class=\"el-icon-info\"></i>\r\n        </div>\r\n        <div class=\"copy-tip-content\">\r\n            <div class=\"tip tip1\">\r\n                1.通过<span class=\"blue\">复制其他用户组权限</span>快捷配置当前用户组权限；\r\n            </div>\r\n            <div class=\"tip tip2\">\r\n                2.复制后，当前模型的权限将完全被替代，<span class=\"blue\">不可恢复</span>，请谨慎操作。\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <div class=\"\">\r\n        <el-form :model=\"globalVars.ruleForm\" :rules=\"globalVars.rules\" label-width=\"100px\">\r\n            <el-form-item label=\"用户组\" prop=\"roleId\">\r\n                <el-select v-model=\"globalVars.ruleForm.roleId\" placeholder=\"请选择活动区域\">\r\n                    <el-option :disabled=\"item.roleId == globalVars.checkRoleId\" v-for=\"item in globalVars.roleList\"\r\n                        :label=\"item.roleName\" :value=\"item.roleId\">\r\n                    </el-option>\r\n                </el-select>\r\n            </el-form-item>\r\n        </el-form>\r\n    </div>\r\n</div>", "required": false, "pattern": "", "validator": ""}, "dataOptions": {"remote": false, "remoteType": "dataset", "remoteModel": "", "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "dataSource": {"type": "custom", "params": {}, "code": "", "url": ""}}, "events": {"mounted": {"tip": "组件挂载"}, "change": {"tip": "内部绑定的表单组件值发生变化(是否触发根据内部手写的组件有关)"}, "beforeDestroy": {"tip": "组件销毁"}}, "name": "vue模板", "isCover": false, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "res-com-template", "pageCode": "modelPermissionContent"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "events": {"mounted": "", "destroy": ""}, "pageCode": "modelPermissionContent", "uuid": "uuid_uuwzio0z"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "showTitle": false, "title": "容器标题", "events": {"mounted": "", "destroy": ""}, "layout": "grid", "pageCode": "modelPermissionContent", "uuid": "uuid_7h2x7sx4"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "0 2px 12px 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "events": {"mounted": "", "destroy": ""}, "pageCode": "modelPermissionContent", "uuid": "uuid_nogmlj92"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "0 2px 12px 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "showTitle": true, "title": "一键复制", "events": {"mounted": {}, "destroy": {}}, "layout": "grid", "sign": "dialog", "btnsPosition": "right", "frameType": "1", "widthType": "pixel", "percentWidth": "50%", "size": "medium", "pixelWidth": "484", "commonOptions": {"fullscreen": "false", "closeOnClickModal": "false"}, "drawerOptions": {"frameDirection": "rtl", "wrapperClosable": "true"}, "btnOptions": [{"label": "取消", "id": "cancel_ofz3ik7d", "type": "", "func": {"name": "", "params": {}}}, {"label": "确认", "id": "confirm_1uxa6cw7", "type": "primary", "func": {"name": "action_ev_12fvlr0r", "params": [{"name": "event", "des": "事件相关参数"}, {"name": "closeNext", "des": "需要调用该方法才能关闭弹窗"}], "enName": "确认"}}], "btnRenderFunc": {}, "pageCode": "modelPermissionContent", "uuid": "uuid_761pznd7", "windowsize": "484"}], "suspendJson": []}