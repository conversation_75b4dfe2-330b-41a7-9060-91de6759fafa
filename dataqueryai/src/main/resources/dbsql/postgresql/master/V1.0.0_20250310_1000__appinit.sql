-- 菜单权限
-- 功能权限
-- 初始化角色
delete from lowcode_role where role_id = 'role-daadmin';
insert into lowcode_role (ROLE_ID, ROLE_NAME, ROLE_CODE, APP_DOMAIN, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME,ROLE_TYPE,DATA_ROLE_ID)
values ('role-daadmin', '信息中心管理员', '10001','da', 'lowcode', NULL, 'lowcode', NULL,'static','10001');

-- 初始化角色菜单权限
-- 信息中心管理员
DELETE FROM LOWCODE_ROLE_MENU WHERE ROLE_ID='role-daadmin' AND MENU_ID='dataqueryai_group';
DELETE FROM LOWCODE_ROLE_MENU WHERE ROLE_ID='role-daadmin' AND MENU_ID='dataqueryai';
DELETE FROM LOWCODE_ROLE_MENU WHERE ROLE_ID='role-daadmin' AND MENU_ID='dataqueryai-zhcx';
DELETE FROM LOWCODE_ROLE_MENU WHERE ROLE_ID='role-daadmin' AND MENU_ID='dataqueryai-mxxl';
DELETE FROM LOWCODE_ROLE_MENU WHERE ROLE_ID='role-daadmin' AND MENU_ID='dataqueryai-appUserGroupAuth';
DELETE FROM LOWCODE_ROLE_MENU WHERE ROLE_ID='role-daadmin' AND MENU_ID='dataqueryai-qxgl';
DELETE FROM LOWCODE_ROLE_MENU WHERE ROLE_ID='role-daadmin' AND MENU_ID='dataqueryai-universityBackground';
DELETE FROM LOWCODE_ROLE_MENU WHERE ROLE_ID='role-daadmin' AND MENU_ID='dataqueryai-chatHiastory';
DELETE FROM LOWCODE_ROLE_MENU WHERE ROLE_ID='role-daadmin' AND MENU_ID='dataqueryai-modelPermission';
DELETE FROM LOWCODE_ROLE_MENU WHERE ROLE_ID='role-daadmin' AND MENU_ID='dataqueryai-recommendQuestion';
DELETE FROM LOWCODE_ROLE_MENU WHERE ROLE_ID='role-daadmin' AND MENU_ID='dataqueryai-modelSubjectArea';
DELETE FROM LOWCODE_ROLE_MENU WHERE ROLE_ID='role-daadmin' AND MENU_ID='dataqueryai-chatDbConfigContainer';
INSERT INTO LOWCODE_ROLE_MENU(ROLE_ID, MENU_ID) VALUES('role-daadmin','dataqueryai_group');
INSERT INTO LOWCODE_ROLE_MENU(ROLE_ID, MENU_ID) VALUES('role-daadmin','dataqueryai');
INSERT INTO LOWCODE_ROLE_MENU(ROLE_ID, MENU_ID) VALUES('role-daadmin','dataqueryai-zhcx');
INSERT INTO LOWCODE_ROLE_MENU(ROLE_ID, MENU_ID) VALUES('role-daadmin','dataqueryai-mxxl');
INSERT INTO LOWCODE_ROLE_MENU(ROLE_ID, MENU_ID) VALUES('role-daadmin','dataqueryai-appUserGroupAuth');
INSERT INTO LOWCODE_ROLE_MENU(ROLE_ID, MENU_ID) VALUES('role-daadmin','dataqueryai-qxgl');
INSERT INTO LOWCODE_ROLE_MENU(ROLE_ID, MENU_ID) VALUES('role-daadmin','dataqueryai-universityBackground');
INSERT INTO LOWCODE_ROLE_MENU(ROLE_ID, MENU_ID) VALUES('role-daadmin','dataqueryai-chatHiastory');
INSERT INTO LOWCODE_ROLE_MENU(ROLE_ID, MENU_ID) VALUES('role-daadmin','dataqueryai-modelPermission');
INSERT INTO LOWCODE_ROLE_MENU(ROLE_ID, MENU_ID) VALUES('role-daadmin','dataqueryai-recommendQuestion');
INSERT INTO LOWCODE_ROLE_MENU(ROLE_ID, MENU_ID) VALUES('role-daadmin','dataqueryai-modelSubjectArea');
INSERT INTO LOWCODE_ROLE_MENU(ROLE_ID, MENU_ID) VALUES('role-daadmin','dataqueryai-chatDbConfigContainer');

-- 院领导(部门正职)
DELETE FROM LOWCODE_ROLE_MENU WHERE ROLE_ID='bmzz20006' AND MENU_ID='dataqueryai_group';
DELETE FROM LOWCODE_ROLE_MENU WHERE ROLE_ID='bmzz20006' AND MENU_ID='dataqueryai';
DELETE FROM LOWCODE_ROLE_MENU WHERE ROLE_ID='bmzz20006' AND MENU_ID='dataqueryai-zhcx';
DELETE FROM LOWCODE_ROLE_MENU WHERE ROLE_ID='bmzz20006' AND MENU_ID='dataqueryai-chatHiastory';
DELETE FROM LOWCODE_ROLE_MENU WHERE ROLE_ID='bmzz20006' AND MENU_ID='dataqueryai-recommendQuestion';
INSERT INTO LOWCODE_ROLE_MENU(ROLE_ID, MENU_ID) VALUES('bmzz20006','dataqueryai_group');
INSERT INTO LOWCODE_ROLE_MENU(ROLE_ID, MENU_ID) VALUES('bmzz20006','dataqueryai');
INSERT INTO LOWCODE_ROLE_MENU(ROLE_ID, MENU_ID) VALUES('bmzz20006','dataqueryai-zhcx');
INSERT INTO LOWCODE_ROLE_MENU(ROLE_ID, MENU_ID) VALUES('bmzz20006','dataqueryai-chatHiastory');
INSERT INTO LOWCODE_ROLE_MENU(ROLE_ID, MENU_ID) VALUES('bmzz20006','dataqueryai-recommendQuestion');

-- 院领导(部门副职)
DELETE FROM LOWCODE_ROLE_MENU WHERE ROLE_ID='bmfz20006' AND MENU_ID='dataqueryai_group';
DELETE FROM LOWCODE_ROLE_MENU WHERE ROLE_ID='bmfz20006' AND MENU_ID='dataqueryai';
DELETE FROM LOWCODE_ROLE_MENU WHERE ROLE_ID='bmfz20006' AND MENU_ID='dataqueryai-zhcx';
DELETE FROM LOWCODE_ROLE_MENU WHERE ROLE_ID='bmfz20006' AND MENU_ID='dataqueryai-chatHiastory';
DELETE FROM LOWCODE_ROLE_MENU WHERE ROLE_ID='bmfz20006' AND MENU_ID='dataqueryai-recommendQuestion';
INSERT INTO LOWCODE_ROLE_MENU(ROLE_ID, MENU_ID) VALUES('bmfz20006','dataqueryai_group');
INSERT INTO LOWCODE_ROLE_MENU(ROLE_ID, MENU_ID) VALUES('bmfz20006','dataqueryai');
INSERT INTO LOWCODE_ROLE_MENU(ROLE_ID, MENU_ID) VALUES('bmfz20006','dataqueryai-zhcx');
INSERT INTO LOWCODE_ROLE_MENU(ROLE_ID, MENU_ID) VALUES('bmfz20006','dataqueryai-chatHiastory');
INSERT INTO LOWCODE_ROLE_MENU(ROLE_ID, MENU_ID) VALUES('bmfz20006','dataqueryai-recommendQuestion');

-- 院领导(部门秘书)
DELETE FROM LOWCODE_ROLE_MENU WHERE ROLE_ID='bmms20006' AND MENU_ID='dataqueryai_group';
DELETE FROM LOWCODE_ROLE_MENU WHERE ROLE_ID='bmms20006' AND MENU_ID='dataqueryai';
DELETE FROM LOWCODE_ROLE_MENU WHERE ROLE_ID='bmms20006' AND MENU_ID='dataqueryai-zhcx';
DELETE FROM LOWCODE_ROLE_MENU WHERE ROLE_ID='bmms20006' AND MENU_ID='dataqueryai-chatHiastory';
DELETE FROM LOWCODE_ROLE_MENU WHERE ROLE_ID='bmms20006' AND MENU_ID='dataqueryai-recommendQuestion';
INSERT INTO LOWCODE_ROLE_MENU(ROLE_ID, MENU_ID) VALUES('bmms20006','dataqueryai_group');
INSERT INTO LOWCODE_ROLE_MENU(ROLE_ID, MENU_ID) VALUES('bmms20006','dataqueryai');
INSERT INTO LOWCODE_ROLE_MENU(ROLE_ID, MENU_ID) VALUES('bmms20006','dataqueryai-zhcx');
INSERT INTO LOWCODE_ROLE_MENU(ROLE_ID, MENU_ID) VALUES('bmms20006','dataqueryai-chatHiastory');
INSERT INTO LOWCODE_ROLE_MENU(ROLE_ID, MENU_ID) VALUES('bmms20006','dataqueryai-recommendQuestion');

--教师个人
DELETE FROM LOWCODE_ROLE_MENU WHERE ROLE_ID='teacher80001' AND MENU_ID='dataqueryai_group';
DELETE FROM LOWCODE_ROLE_MENU WHERE ROLE_ID='teacher80001' AND MENU_ID='dataqueryai';
DELETE FROM LOWCODE_ROLE_MENU WHERE ROLE_ID='teacher80001' AND MENU_ID='dataqueryai-zhcx';
INSERT INTO LOWCODE_ROLE_MENU(ROLE_ID, MENU_ID) VALUES('teacher80001','dataqueryai_group');
INSERT INTO LOWCODE_ROLE_MENU(ROLE_ID, MENU_ID) VALUES('teacher80001','dataqueryai');
INSERT INTO LOWCODE_ROLE_MENU(ROLE_ID, MENU_ID) VALUES('teacher80001','dataqueryai-zhcx');

--背景知识
delete from T_DQAI_BG_KNOWLEDGE where id in ('516916176987754578', '516916176987754579', '516916176987754580', '516916176987754581');
INSERT INTO T_DQAI_BG_KNOWLEDGE(ID, KEYWORD, EXPLAIN, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)VALUES('516916176987754578', 'T级论文', '论文认定级别为T1、T2或者T3', 'lowcodeadmin', NULL, 'lowcodeadmin', NULL);
INSERT INTO T_DQAI_BG_KNOWLEDGE(ID, KEYWORD, EXPLAIN, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)VALUES('516916176987754579', '少数民族', '除汉族以外民族', 'lowcodeadmin', NULL, 'lowcodeadmin', NULL);
INSERT INTO T_DQAI_BG_KNOWLEDGE(ID, KEYWORD, EXPLAIN, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)VALUES('516916176987754580', '教师', '指教职工，不特指专任教师', 'lowcodeadmin', NULL, 'lowcodeadmin', NULL);
INSERT INTO T_DQAI_BG_KNOWLEDGE(ID, KEYWORD, EXPLAIN, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)VALUES('516916176987754581', '博士', '最高学历为博士研究生毕业', 'lowcodeadmin', NULL, 'lowcodeadmin', NULL);

--同步指标模型表
delete from T_MODELTOOL_APP where id in ('dataqueryai_kyfx', 'dataqueryai_aiwrs');
INSERT INTO T_MODELTOOL_APP(ID, APP_CODE, APP_NAME, APP_ID, APP_SECRET, MARKET_CODE, CDSP_DOMAIN, SCHEDULER_CRON, WRITE_STRATEGY_CODE, GEN_DELETE_SQL, DRIVER_CLASS_NAME, DB_TYPE, URL, USER_NAME, PASS_WORD, ENABLE_STATUS, REMARKS)VALUES('dataqueryai_kyfx', 'dataqueryai', 'AI问数', '1906515643795914754', '8bf127b2e45742d5836127aca5a45398', 'KYFX', 'http://172.16.7.40:8201/cdsp', '0 0 1 ? * * *', 'truncateAdd', '0', 'oracle.jdbc.driver.OracleDriver', 'Oracle', '****************************************', 'XXXX', 'XXXX', '0', NULL);
INSERT INTO T_MODELTOOL_APP(ID, APP_CODE, APP_NAME, APP_ID, APP_SECRET, MARKET_CODE, CDSP_DOMAIN, SCHEDULER_CRON, WRITE_STRATEGY_CODE, GEN_DELETE_SQL, DRIVER_CLASS_NAME, DB_TYPE, URL, USER_NAME, PASS_WORD, ENABLE_STATUS, REMARKS)VALUES('dataqueryai_aiwrs', 'dataqueryai', 'AI问数', '1906515643795914754', '8bf127b2e45742d5836127aca5a45398', 'AIWRS', 'http://172.16.7.40:8201/cdsp', '0 0 1 ? * * *', 'truncateAdd', '0', 'oracle.jdbc.driver.OracleDriver', 'Oracle', '****************************************', 'XXXX', 'XXXX', '0', NULL);
