--同步指标模型表
delete from T_MODELTOOL_APP where id in ('dataqueryai_kyfx','dataqueryai_aiwky','dataqueryai_aiwrs');
INSERT INTO T_MODELTOOL_APP(ID, APP_CODE, APP_NAME, APP_ID, APP_SECRET, MARKET_CODE, CDSP_DOMAIN, SCHEDULER_CRON, WRITE_STRATEGY_CODE, GEN_DELETE_SQL, DRIVER_CLASS_NAME, DB_TYPE, URL, USER_NAME, PASS_WORD, ENABLE_STATUS, REMARKS,INIT_ENABLE)VALUES('dataqueryai_aiwky', 'dataqueryai', 'AI问数', '1906515643795914754', '8bf127b2e45742d5836127aca5a45398', 'AIWKY', 'http://172.16.7.40:8201/cdsp', '0 0 1 ? * * *', 'truncateAdd', '1', 'oracle.jdbc.driver.OracleDriver', 'Oracle', '****************************************', 'XXXX', 'XXXX', '0', NULL, '1');
INSERT INTO T_MODELTOOL_APP(ID, APP_CODE, APP_NAME, APP_ID, APP_SECRET, MARKET_CODE, CDSP_DOMAIN, SCHEDULER_CRON, WRITE_STRATEGY_CODE, GEN_DELETE_SQL, DRIVER_CLASS_NAME, DB_TYPE, URL, USER_NAME, PASS_WORD, ENABLE_STATUS, REMARKS,INIT_ENABLE)VALUES('dataqueryai_aiwrs', 'dataqueryai', 'AI问数', '1906515643795914754', '8bf127b2e45742d5836127aca5a45398', 'AIWRS', 'http://172.16.7.40:8201/cdsp', '0 0 1 ? * * *', 'truncateAdd', '1', 'oracle.jdbc.driver.OracleDriver', 'Oracle', '****************************************', 'XXXX', 'XXXX', '0', NULL, '1');
