(() => {
    "use strict";
    var e = {
        928: function (e, t, n) {
            var r = this && this.__rest || function (e, t) {
                var n = {};
                for (var r in e) Object.prototype.hasOwnProperty.call(e, r) && t.indexOf(r) < 0 && (n[r] = e[r]);
                if (null != e && "function" == typeof Object.getOwnPropertySymbols) {
                    var o = 0;
                    for (r = Object.getOwnPropertySymbols(e); o < r.length; o++) t.indexOf(r[o]) < 0 && Object.prototype.propertyIsEnumerable.call(e, r[o]) && (n[r[o]] = e[r[o]])
                }
                return n
            };
            Object.defineProperty(t, "__esModule", {value: !0}), t.fetchEventSource = t.EventStreamContentType = void 0;
            const o = n(443);
            t.EventStreamContentType = "text/event-stream";
            const a = "last-event-id";

            function s(e) {
                const n = e.headers.get("content-type");
                if (!(null == n ? void 0 : n.startsWith(t.EventStreamContentType))) throw new Error(`Expected content-type to be ${t.EventStreamContentType}, Actual: ${n}`)
            }

            t.fetchEventSource = function (e, n) {
                var {
                        signal: c,
                        headers: i,
                        onopen: d,
                        onmessage: l,
                        onclose: u,
                        onerror: v,
                        openWhenHidden: f,
                        fetch: y
                    } = n,
                    p = r(n, ["signal", "headers", "onopen", "onmessage", "onclose", "onerror", "openWhenHidden", "fetch"]);
                return new Promise(((n, r) => {
                    const g = Object.assign({}, i);
                    let b;

                    function h() {
                        b.abort(), document.hidden || j()
                    }

                    g.accept || (g.accept = t.EventStreamContentType), f || document.addEventListener("visibilitychange", h);
                    let w = 1e3, m = 0;

                    function E() {
                        document.removeEventListener("visibilitychange", h), window.clearTimeout(m), b.abort()
                    }

                    null == c || c.addEventListener("abort", (() => {
                        E(), n()
                    }));
                    const O = null != y ? y : window.fetch, S = null != d ? d : s;

                    async function j() {
                        var t;
                        b = new AbortController;
                        try {
                            const t = await O(e, Object.assign(Object.assign({}, p), {headers: g, signal: b.signal}));
                            await S(t), await (0, o.getBytes)(t.body, (0, o.getLines)((0, o.getMessages)((e => {
                                e ? g[a] = e : delete g[a]
                            }), (e => {
                                w = e
                            }), l))), null == u || u(), E(), n()
                        } catch (e) {
                            if (!b.signal.aborted) try {
                                const n = null !== (t = null == v ? void 0 : v(e)) && void 0 !== t ? t : w;
                                window.clearTimeout(m), m = window.setTimeout(j, n)
                            } catch (e) {
                                E(), r(e)
                            }
                        }
                    }

                    j()
                }))
            }
        }, 443: (e, t) => {
            Object.defineProperty(t, "__esModule", {value: !0}), t.getMessages = t.getLines = t.getBytes = void 0, t.getBytes = async function (e, t) {
                const n = e.getReader();
                let r;
                for (; !(r = await n.read()).done;) t(r.value)
            }, t.getLines = function (e) {
                let t, n, r, o = !1;
                return function (a) {
                    void 0 === t ? (t = a, n = 0, r = -1) : t = function (e, t) {
                        const n = new Uint8Array(e.length + t.length);
                        return n.set(e), n.set(t, e.length), n
                    }(t, a);
                    const s = t.length;
                    let c = 0;
                    for (; n < s;) {
                        o && (10 === t[n] && (c = ++n), o = !1);
                        let a = -1;
                        for (; n < s && -1 === a; ++n) switch (t[n]) {
                            case 58:
                                -1 === r && (r = n - c);
                                break;
                            case 13:
                                o = !0;
                            case 10:
                                a = n
                        }
                        if (-1 === a) break;
                        e(t.subarray(c, a), r), c = n, r = -1
                    }
                    c === s ? t = void 0 : 0 !== c && (t = t.subarray(c), n -= c)
                }
            }, t.getMessages = function (e, t, n) {
                let r = {data: "", event: "", id: "", retry: void 0};
                const o = new TextDecoder;
                return function (a, s) {
                    if (0 === a.length) null == n || n(r), r = {
                        data: "",
                        event: "",
                        id: "",
                        retry: void 0
                    }; else if (s > 0) {
                        const n = o.decode(a.subarray(0, s)), c = s + (32 === a[s + 1] ? 2 : 1),
                            i = o.decode(a.subarray(c));
                        switch (n) {
                            case"data":
                                r.data = r.data ? r.data + "\n" + i : i;
                                break;
                            case"event":
                                r.event = i;
                                break;
                            case"id":
                                e(r.id = i);
                                break;
                            case"retry":
                                const n = parseInt(i, 10);
                                isNaN(n) || t(r.retry = n)
                        }
                    }
                }
            }
        }
    }, t = {};

    function n(r) {
        var o = t[r];
        if (void 0 !== o) return o.exports;
        var a = t[r] = {exports: {}};
        return e[r].call(a.exports, a, a.exports, n), a.exports
    }

    var r = {};
    (() => {
        var e = r;
        Object.defineProperty(e, "__esModule", {value: !0}), e.EventStreamContentType = e.fetchEventSource = void 0;
        var t = n(928);
        Object.defineProperty(e, "fetchEventSource", {
            enumerable: !0, get: function () {
                return t.fetchEventSource
            }
        }), Object.defineProperty(e, "EventStreamContentType", {
            enumerable: !0, get: function () {
                return t.EventStreamContentType
            }
        })
    })(), window.fetchEventSource = r
})();