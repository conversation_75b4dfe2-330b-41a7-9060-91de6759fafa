<?xml version="1.0" encoding="UTF-8"?>

<group id="bkjyjxzlbzpt" name="教育教学质量监测" showOrder="0" menuHidden="1">
    <routerParams>{ "openUrl": "/app/bkteachingeval/#/bkteachingeval" }</routerParams>
    <app id="bkteachingeval" name="教育教学质量监测">
        <!--菜单（目录）-->
        <menus id="bkteachingeval-drilldownlist" formRouterName="drilldownlist" name="列表" showOrder="40"
               menuHidden="1">
            <!--菜单（页面）-->
            <menu bindType="default" formRouterName="teacherlist" id="bkteachingeval-teacherlist"
                  menuPattern="pc"
                  name="教职工基本信息"
                  showOrder="1"
                  menuHidden="1">
                <routerParams>{ "appCode": "bkteachingeval", "pageCode": "teacherlist"}</routerParams>
                <!-- 权限字-表单 -->
                <permCode name="教职工基本信息" permCode="formteacherlist" permCodeId="bkteachingeval-teacherlist"
                          permCodeType="0"/>
                <!-- 权限字-片段 -->
                <permCode name="教职工基本信息" permCode="formteacherlist:fragmentteacherlist"
                          permCodeId="fragmentbkteachingeval-teacherlist"
                          permCodeType="1">
                    <perm name="数据更新时间接口" url="/jwdatapub/dataUpdateTime/**"/>
                    <perm name="字典集合" url="/admin/dict/list"/>
                    <perm name="查询级联表格平铺接口" url="/eda/bkteachingeval/pagehierarchy/jzgjcxxlb"/>
                    <perm name="页面数据展示" url="/eda/bkteachingeval/page/jzgjcxxlb"/>
                    <perm name="页面数据导出" url="/eda/bkteachingeval/export/jzgjcxxlb"/>
                    <perm name="页面导出文件下载" url="/admin/file/bkteachingeval/download"/>
                </permCode>
                <btn id="bkteachingeval-teacherlist-show" menuPattern="pc" name="显示">
                    <permCode name="显示" permCode="formteacherlist:fragmentteacherlist:show"
                              permCodeId="bkteachingeval-teacherlist-show"/>
                </btn>
            </menu>

            <!--菜单（页面）-->
            <menu bindType="default" formRouterName="studentlist" id="bkteachingeval-studentlist"
                  menuPattern="pc"
                  name="学生基本信息"
                  showOrder="2"
                  menuHidden="1">
                <routerParams>{ "appCode": "bkteachingeval", "pageCode": "studentlist"}</routerParams>
                <!-- 权限字-表单 -->
                <permCode name="学生基本信息" permCode="formstudentlist" permCodeId="bkteachingeval-studentlist"
                          permCodeType="0"/>
                <!-- 权限字-片段 -->
                <permCode name="学生基本信息" permCode="formstudentlist:fragmentstudentlist"
                          permCodeId="fragmentbkteachingeval-studentlist"
                          permCodeType="1">
                    <perm name="数据更新时间接口" url="/jwdatapub/dataUpdateTime/**"/>
                    <perm name="字典集合" url="/admin/dict/list"/>
                    <perm name="查询级联表格平铺接口" url="/eda/bkteachingeval/pagehierarchy/bksxxlb"/>
                    <perm name="页面数据展示" url="/eda/bkteachingeval/page/bksxxlb"/>
                    <perm name="页面数据导出" url="/eda/bkteachingeval/export/bksxxlb"/>
                    <perm name="页面导出文件下载" url="/admin/file/bkteachingeval/download"/>
                </permCode>
                <btn id="bkteachingeval-studentlist-show" menuPattern="pc" name="显示">
                    <permCode name="显示" permCode="formstudentlist:fragmentstudentlist:show"
                              permCodeId="bkteachingeval-studentlist-show"/>
                </btn>
            </menu>

            <!--菜单（页面）-->
            <menu bindType="default" formRouterName="courselist" id="bkteachingeval-courselist"
                  menuPattern="pc"
                  name="课程基本信息"
                  showOrder="3"
                  menuHidden="1">
                <routerParams>{ "appCode": "bkteachingeval", "pageCode": "courselist"}</routerParams>
                <!-- 权限字-表单 -->
                <permCode name="课程基本信息" permCode="formcourselist" permCodeId="bkteachingeval-courselist"
                          permCodeType="0"/>
                <!-- 权限字-片段 -->
                <permCode name="课程基本信息" permCode="formcourselist:fragmentcourselist"
                          permCodeId="fragmentbkteachingeval-courselist"
                          permCodeType="1">
                    <perm name="数据更新时间接口" url="/jwdatapub/dataUpdateTime/**"/>
                    <perm name="字典集合" url="/admin/dict/list"/>
                    <perm name="查询级联表格平铺接口" url="/eda/bkteachingeval/pagehierarchy/kcjcxxlb"/>
                    <perm name="页面数据展示" url="/eda/bkteachingeval/page/kcjcxxlb"/>
                    <perm name="页面数据导出" url="/eda/bkteachingeval/export/kcjcxxlb"/>
                    <perm name="页面导出文件下载" url="/admin/file/bkteachingeval/download"/>
                </permCode>
                <btn id="bkteachingeval-courselist-show" menuPattern="pc" name="显示">
                    <permCode name="显示" permCode="formcourselist:fragmentcourselist:show"
                              permCodeId="bkteachingeval-courselist-show"/>
                </btn>
            </menu>

            <!--菜单（页面）-->
            <menu bindType="default" formRouterName="majorlist" id="bkteachingeval-majorlist"
                  menuPattern="pc"
                  name="专业基本信息"
                  showOrder="4"
                  menuHidden="1">
                <routerParams>{ "appCode": "bkteachingeval", "pageCode": "majorlist"}</routerParams>
                <!-- 权限字-表单 -->
                <permCode name="专业基本信息" permCode="formmajorlist" permCodeId="bkteachingeval-majorlist"
                          permCodeType="0"/>
                <!-- 权限字-片段 -->
                <permCode name="专业基本信息" permCode="formmajorlist:fragmentmajorlist"
                          permCodeId="fragmentbkteachingeval-majorlist"
                          permCodeType="1">
                    <perm name="数据更新时间接口" url="/jwdatapub/dataUpdateTime/**"/>
                    <perm name="字典集合" url="/admin/dict/list"/>
                    <perm name="查询级联表格平铺接口" url="/eda/bkteachingeval/pagehierarchy/zyjbxxlb"/>
                    <perm name="页面数据展示" url="/eda/bkteachingeval/page/zyjbxxlb"/>
                    <perm name="页面数据导出" url="/eda/bkteachingeval/export/zyjbxxlb"/>
                    <perm name="页面导出文件下载" url="/admin/file/bkteachingeval/download"/>
                </permCode>
                <btn id="bkteachingeval-majorlist-show" menuPattern="pc" name="显示">
                    <permCode name="显示" permCode="formmajorlist:fragmentmajorlist:show"
                              permCodeId="bkteachingeval-majorlist-show"/>
                </btn>
            </menu>

            <!--菜单（页面）-->
            <menu bindType="default" formRouterName="wpjsxxlist" id="bkteachingeval-wpjsxxlist"
                  menuPattern="pc"
                  name="外聘教师信息列表"
                  showOrder="5"
                  menuHidden="1">
                <routerParams>{ "appCode": "bkteachingeval", "pageCode": "wpjsxxlist"}</routerParams>
                <!-- 权限字-表单 -->
                <permCode name="外聘教师信息列表" permCode="formwpjsxxlist" permCodeId="bkteachingeval-wpjsxxlist"
                          permCodeType="0"/>
                <!-- 权限字-片段 -->
                <permCode name="外聘教师信息列表" permCode="formwpjsxxlist:fragmentwpjsxxlist"
                          permCodeId="fragmentbkteachingeval-wpjsxxlist"
                          permCodeType="1">
                    <perm name="数据更新时间接口" url="/jwdatapub/dataUpdateTime/**"/>
                    <perm name="字典集合" url="/admin/dict/list"/>
                    <perm name="查询级联表格平铺接口" url="/eda/bkteachingeval/pagehierarchy/wpjsxxlb"/>
                    <perm name="页面数据展示" url="/eda/bkteachingeval/page/wpjsxxlb"/>
                    <perm name="页面数据导出" url="/eda/bkteachingeval/export/wpjsxxlb"/>
                    <perm name="页面导出文件下载" url="/admin/file/bkteachingeval/download"/>
                </permCode>
                <btn id="bkteachingeval-wpjsxxlist-show" menuPattern="pc" name="显示">
                    <permCode name="显示" permCode="formwpjsxxlist:fragmentwpjsxxlist:show"
                              permCodeId="bkteachingeval-wpjsxxlist-show"/>
                </btn>
            </menu>

            <!--菜单（页面）-->
            <menu bindType="default" formRouterName="pjxxlb" id="bkteachingeval-pjxxlb"
                  menuPattern="pc"
                  name="评教信息列表"
                  showOrder="6"
                  menuHidden="1">
                <routerParams>{ "appCode": "bkteachingeval", "pageCode": "pjxxlb"}</routerParams>
                <!-- 权限字-表单 -->
                <permCode name="评教信息列表" permCode="formpjxxlb" permCodeId="bkteachingeval-pjxxlb"
                          permCodeType="0"/>
                <!-- 权限字-片段 -->
                <permCode name="评教信息列表" permCode="formpjxxlb:fragmentpjxxlb"
                          permCodeId="fragmentbkteachingeval-pjxxlb"
                          permCodeType="1">
                    <perm name="数据更新时间接口" url="/jwdatapub/dataUpdateTime/**"/>
                    <perm name="字典集合" url="/admin/dict/list"/>
                    <perm name="查询级联表格平铺接口" url="/eda/bkteachingeval/pagehierarchy/pjxxlb"/>
                    <perm name="页面数据展示" url="/eda/bkteachingeval/page/pjxxlb"/>
                    <perm name="页面数据导出" url="/eda/bkteachingeval/export/pjxxlb"/>
                    <perm name="页面导出文件下载" url="/admin/file/bkteachingeval/download"/>
                </permCode>
                <btn id="bkteachingeval-pjxxlb-show" menuPattern="pc" name="显示">
                    <permCode name="显示" permCode="formpjxxlb:fragmentpjxxlb:show"
                              permCodeId="bkteachingeval-pjxxlb-show"/>
                </btn>
            </menu>

            <!--菜单（页面）-->
            <menu bindType="default" formRouterName="bdspjxxlb" id="bkteachingeval-bdspjxxlb"
                  menuPattern="pc"
                  name="班导师评教信息列表"
                  showOrder="7"
                  menuHidden="1">
                <routerParams>{ "appCode": "bkteachingeval", "pageCode": "bdspjxxlb"}</routerParams>
                <!-- 权限字-表单 -->
                <permCode name="班导师评教信息列表" permCode="formbdspjxxlb" permCodeId="bkteachingeval-bdspjxxlb"
                          permCodeType="0"/>
                <!-- 权限字-片段 -->
                <permCode name="班导师评教信息列表" permCode="formbdspjxxlb:fragmentbdspjxxlb"
                          permCodeId="fragmentbkteachingeval-bdspjxxlb"
                          permCodeType="1">
                    <perm name="数据更新时间接口" url="/jwdatapub/dataUpdateTime/**"/>
                    <perm name="字典集合" url="/admin/dict/list"/>
                    <perm name="查询级联表格平铺接口" url="/eda/bkteachingeval/pagehierarchy/bdspjxxlb"/>
                    <perm name="页面数据展示" url="/eda/bkteachingeval/page/bdspjxxlb"/>
                    <perm name="页面数据导出" url="/eda/bkteachingeval/export/bdspjxxlb"/>
                    <perm name="页面导出文件下载" url="/admin/file/bkteachingeval/download"/>
                </permCode>
                <btn id="bkteachingeval-bdspjxxlb-show" menuPattern="pc" name="显示">
                    <permCode name="显示" permCode="formbdspjxxlb:fragmentbdspjxxlb:show"
                              permCodeId="bkteachingeval-bdspjxxlb-show"/>
                </btn>
            </menu>

            <!--菜单（页面）-->
            <menu bindType="default" formRouterName="tkxxlb" id="bkteachingeval-tkxxlb"
                  menuPattern="pc"
                  name="听课信息列表"
                  showOrder="8"
                  menuHidden="1">
                <routerParams>{ "appCode": "bkteachingeval", "pageCode": "tkxxlb"}</routerParams>
                <!-- 权限字-表单 -->
                <permCode name="听课信息列表" permCode="formtkxxlb" permCodeId="bkteachingeval-tkxxlb"
                          permCodeType="0"/>
                <!-- 权限字-片段 -->
                <permCode name="听课信息列表" permCode="formtkxxlb:fragmenttkxxlb"
                          permCodeId="fragmentbkteachingeval-tkxxlb"
                          permCodeType="1">
                    <perm name="数据更新时间接口" url="/jwdatapub/dataUpdateTime/**"/>
                    <perm name="字典集合" url="/admin/dict/list"/>
                    <perm name="查询级联表格平铺接口" url="/eda/bkteachingeval/pagehierarchy/tkxxlb"/>
                    <perm name="页面数据展示" url="/eda/bkteachingeval/page/tkxxlb"/>
                    <perm name="页面数据导出" url="/eda/bkteachingeval/export/tkxxlb"/>
                    <perm name="页面导出文件下载" url="/admin/file/bkteachingeval/download"/>
                </permCode>
                <btn id="bkteachingeval-tkxxlb-show" menuPattern="pc" name="显示">
                    <permCode name="显示" permCode="formtkxxlb:fragmenttkxxlb:show"
                              permCodeId="bkteachingeval-tkxxlb-show"/>
                </btn>
            </menu>

            <!--菜单（页面）-->
            <menu bindType="default" formRouterName="kkmcjcxxlb" id="bkteachingeval-kkmcjcxxlb"
                  menuPattern="pc"
                  name="开课基础信息列表"
                  showOrder="9"
                  menuHidden="1">
                <routerParams>{ "appCode": "bkteachingeval", "pageCode": "kkmcjcxxlb"}</routerParams>
                <!-- 权限字-表单 -->
                <permCode name="开课基础信息列表" permCode="formkkmcjcxxlb" permCodeId="bkteachingeval-kkmcjcxxlb"
                          permCodeType="0"/>
                <!-- 权限字-片段 -->
                <permCode name="开课基础信息列表" permCode="formkkmcjcxxlb:fragmentkkmcjcxxlb"
                          permCodeId="fragmentbkteachingeval-kkmcjcxxlb"
                          permCodeType="1">
                    <perm name="数据更新时间接口" url="/jwdatapub/dataUpdateTime/**"/>
                    <perm name="字典集合" url="/admin/dict/list"/>
                    <perm name="查询级联表格平铺接口" url="/eda/bkteachingeval/pagehierarchy/kkmcjcxxlb"/>
                    <perm name="页面数据展示" url="/eda/bkteachingeval/page/kkmcjcxxlb"/>
                    <perm name="页面数据导出" url="/eda/bkteachingeval/export/kkmcjcxxlb"/>
                    <perm name="页面导出文件下载" url="/admin/file/bkteachingeval/download"/>
                </permCode>
                <btn id="bkteachingeval-kkmcjcxxlb-show" menuPattern="pc" name="显示">
                    <permCode name="显示" permCode="formkkmcjcxxlb:fragmentkkmcjcxxlb:show"
                              permCodeId="bkteachingeval-kkmcjcxxlb-show"/>
                </btn>
            </menu>

            <!--菜单（页面）-->
            <menu bindType="default" formRouterName="gccrcjcxxlist" id="bkteachingeval-gccrcjcxxlist"
                  menuPattern="pc"
                  name="高层次人才基础信息列表"
                  showOrder="10"
                  menuHidden="1">
                <routerParams>{ "appCode": "bkteachingeval", "pageCode": "gccrcjcxxlist"}</routerParams>
                <!-- 权限字-表单 -->
                <permCode name="高层次人才基础信息列表" permCode="formgccrcjcxxlist"
                          permCodeId="bkteachingeval-gccrcjcxxlist"
                          permCodeType="0"/>
                <!-- 权限字-片段 -->
                <permCode name="高层次人才基础信息列表" permCode="formgccrcjcxxlist:fragmentgccrcjcxxlist"
                          permCodeId="fragmentbkteachingeval-gccrcjcxxlist"
                          permCodeType="1">
                    <perm name="数据更新时间接口" url="/jwdatapub/dataUpdateTime/**"/>
                    <perm name="字典集合" url="/admin/dict/list"/>
                    <perm name="查询级联表格平铺接口" url="/eda/bkteachingeval/pagehierarchy/gccrcjcxxlb"/>
                    <perm name="页面数据展示" url="/eda/bkteachingeval/page/gccrcjcxxlb"/>
                    <perm name="页面数据导出" url="/eda/bkteachingeval/export/gccrcjcxxlb"/>
                    <perm name="页面导出文件下载" url="/admin/file/bkteachingeval/download"/>
                </permCode>
                <btn id="bkteachingeval-gccrcjcxxlist-show" menuPattern="pc" name="显示">
                    <permCode name="显示" permCode="formgccrcjcxxlist:fragmentgccrcjcxxlist:show"
                              permCodeId="bkteachingeval-gccrcjcxxlist-show"/>
                </btn>
            </menu>

            <!--菜单（页面）-->
            <menu bindType="default" formRouterName="sysbjcxxlblist" id="bkteachingeval-sysbjcxxlblist"
                  menuPattern="pc"
                  name="实验设备基础信息列表"
                  showOrder="11"
                  menuHidden="1">
                <routerParams>{ "appCode": "bkteachingeval", "pageCode": "sysbjcxxlblist"}</routerParams>
                <!-- 权限字-表单 -->
                <permCode name="实验设备基础信息列表" permCode="formsysbjcxxlblist"
                          permCodeId="bkteachingeval-sysbjcxxlblist"
                          permCodeType="0"/>
                <!-- 权限字-片段 -->
                <permCode name="实验设备基础信息列表" permCode="formsysbjcxxlblist:fragmentsysbjcxxlblist"
                          permCodeId="fragmentbkteachingeval-sysbjcxxlblist"
                          permCodeType="1">
                    <perm name="数据更新时间接口" url="/jwdatapub/dataUpdateTime/**"/>
                    <perm name="字典集合" url="/admin/dict/list"/>
                    <perm name="查询级联表格平铺接口" url="/eda/bkteachingeval/pagehierarchy/sysbjcxxlb"/>
                    <perm name="页面数据展示" url="/eda/bkteachingeval/page/sysbjcxxlb"/>
                    <perm name="页面数据导出" url="/eda/bkteachingeval/export/sysbjcxxlb"/>
                    <perm name="页面导出文件下载" url="/admin/file/bkteachingeval/download"/>
                </permCode>
                <btn id="bkteachingeval-sysbjcxxlblist-show" menuPattern="pc" name="显示">
                    <permCode name="显示" permCode="formsysbjcxxlblist:fragmentsysbjcxxlblist:show"
                              permCodeId="bkteachingeval-sysbjcxxlblist-show"/>
                </btn>
            </menu>

            <!--菜单（页面）-->
            <menu bindType="default" formRouterName="syjxzxjcxxlblist" id="bkteachingeval-syjxzxjcxxlblist"
                  menuPattern="pc"
                  name="实验教学中心基础信息列表"
                  showOrder="12"
                  menuHidden="1">
                <routerParams>{ "appCode": "bkteachingeval", "pageCode": "syjxzxjcxxlblist"}</routerParams>
                <!-- 权限字-表单 -->
                <permCode name="实验教学中心基础信息列表" permCode="formsyjxzxjcxxlblist"
                          permCodeId="bkteachingeval-syjxzxjcxxlblist"
                          permCodeType="0"/>
                <!-- 权限字-片段 -->
                <permCode name="实验教学中心基础信息列表" permCode="formsyjxzxjcxxlblist:fragmentsyjxzxjcxxlblist"
                          permCodeId="fragmentbkteachingeval-syjxzxjcxxlblist"
                          permCodeType="1">
                    <perm name="数据更新时间接口" url="/jwdatapub/dataUpdateTime/**"/>
                    <perm name="字典集合" url="/admin/dict/list"/>
                    <perm name="查询级联表格平铺接口" url="/eda/bkteachingeval/pagehierarchy/syjxzxjcxxlb"/>
                    <perm name="页面数据展示" url="/eda/bkteachingeval/page/syjxzxjcxxlb"/>
                    <perm name="页面数据导出" url="/eda/bkteachingeval/export/syjxzxjcxxlb"/>
                    <perm name="页面导出文件下载" url="/admin/file/bkteachingeval/download"/>
                </permCode>
                <btn id="bkteachingeval-syjxzxjcxxlblist-show" menuPattern="pc" name="显示">
                    <permCode name="显示" permCode="formsyjxzxjcxxlblist:fragmentsyjxzxjcxxlblist:show"
                              permCodeId="bkteachingeval-syjxzxjcxxlblist-show"/>
                </btn>
            </menu>

            <!--菜单（页面）-->
            <menu bindType="default" formRouterName="sxjdjcxxlblist" id="bkteachingeval-sxjdjcxxlblist"
                  menuPattern="pc"
                  name="实习基地基础信息列表"
                  showOrder="13"
                  menuHidden="1">
                <routerParams>{ "appCode": "bkteachingeval", "pageCode": "sxjdjcxxlblist"}</routerParams>
                <!-- 权限字-表单 -->
                <permCode name="实习基地基础信息列表" permCode="formsxjdjcxxlblist"
                          permCodeId="bkteachingeval-sxjdjcxxlblist"
                          permCodeType="0"/>
                <!-- 权限字-片段 -->
                <permCode name="实习基地基础信息列表" permCode="formsxjdjcxxlblist:fragmentsxjdjcxxlblist"
                          permCodeId="fragmentbkteachingeval-sxjdjcxxlblist"
                          permCodeType="1">
                    <perm name="数据更新时间接口" url="/jwdatapub/dataUpdateTime/**"/>
                    <perm name="字典集合" url="/admin/dict/list"/>
                    <perm name="查询级联表格平铺接口" url="/eda/bkteachingeval/pagehierarchy/sxjdjcxxlb"/>
                    <perm name="页面数据展示" url="/eda/bkteachingeval/page/sxjdjcxxlb"/>
                    <perm name="页面数据导出" url="/eda/bkteachingeval/export/sxjdjcxxlb"/>
                    <perm name="页面导出文件下载" url="/admin/file/bkteachingeval/download"/>
                </permCode>
                <btn id="bkteachingeval-sxjdjcxxlblist-show" menuPattern="pc" name="显示">
                    <permCode name="显示" permCode="formsxjdjcxxlblist:fragmentsxjdjcxxlblist:show"
                              permCodeId="bkteachingeval-sxjdjcxxlblist-show"/>
                </btn>
            </menu>

            <!--菜单（页面）-->
            <menu bindType="default" formRouterName="yszyjbxxlblist" id="bkteachingeval-yszyjbxxlblist"
                  menuPattern="pc"
                  name="优势专业基本信息列表"
                  showOrder="14"
                  menuHidden="1">
                <routerParams>{ "appCode": "bkteachingeval", "pageCode": "yszyjbxxlblist"}</routerParams>
                <!-- 权限字-表单 -->
                <permCode name="优势专业基本信息列表" permCode="formyszyjbxxlblist"
                          permCodeId="bkteachingeval-yszyjbxxlblist"
                          permCodeType="0"/>
                <!-- 权限字-片段 -->
                <permCode name="优势专业基本信息列表" permCode="formyszyjbxxlblist:fragmentyszyjbxxlblist"
                          permCodeId="fragmentbkteachingeval-yszyjbxxlblist"
                          permCodeType="1">
                    <perm name="数据更新时间接口" url="/jwdatapub/dataUpdateTime/**"/>
                    <perm name="字典集合" url="/admin/dict/list"/>
                    <perm name="查询级联表格平铺接口" url="/eda/bkteachingeval/find/yszyjbxxlb/yszyjbxxlb"/>
                    <perm name="页面数据展示" url="/eda/bkteachingeval/page/yszyjbxxlb"/>
                    <perm name="页面数据导出" url="/eda/bkteachingeval/export/yszyjbxxlb"/>
                    <perm name="页面导出文件下载" url="/admin/file/bkteachingeval/download"/>
                </permCode>
                <btn id="bkteachingeval-yszyjbxxlblist-show" menuPattern="pc" name="显示">
                    <permCode name="显示" permCode="formyszyjbxxlblist:fragmentyszyjbxxlblist:show"
                              permCodeId="bkteachingeval-yszyjbxxlblist-show"/>
                </btn>
            </menu>

            <!--菜单（页面）-->
            <menu bindType="default" formRouterName="tszyjbxxlblist" id="bkteachingeval-tszyjbxxlblist"
                  menuPattern="pc"
                  name="特色专业基本信息列表"
                  showOrder="15"
                  menuHidden="1">
                <routerParams>{ "appCode": "bkteachingeval", "pageCode": "tszyjbxxlblist"}</routerParams>
                <!-- 权限字-表单 -->
                <permCode name="特色专业基本信息列表" permCode="formtszyjbxxlblist"
                          permCodeId="bkteachingeval-tszyjbxxlblist"
                          permCodeType="0"/>
                <!-- 权限字-片段 -->
                <permCode name="特色专业基本信息列表" permCode="formtszyjbxxlblist:fragmenttszyjbxxlblist"
                          permCodeId="fragmentbkteachingeval-tszyjbxxlblist"
                          permCodeType="1">
                    <perm name="数据更新时间接口" url="/jwdatapub/dataUpdateTime/**"/>
                    <perm name="字典集合" url="/admin/dict/list"/>
                    <perm name="查询级联表格平铺接口" url="/eda/bkteachingeval/find/tszyjbxxlb/tszyjbxxlb"/>
                    <perm name="页面数据展示" url="/eda/bkteachingeval/page/tszyjbxxlb"/>
                    <perm name="页面数据导出" url="/eda/bkteachingeval/export/tszyjbxxlb"/>
                    <perm name="页面导出文件下载" url="/admin/file/bkteachingeval/download"/>
                </permCode>
                <btn id="bkteachingeval-tszyjbxxlblist-show" menuPattern="pc" name="显示">
                    <permCode name="显示" permCode="formtszyjbxxlblist:fragmenttszyjbxxlblist:show"
                              permCodeId="bkteachingeval-tszyjbxxlblist-show"/>
                </btn>
            </menu>

            <!--菜单（页面）-->
            <menu bindType="default" formRouterName="jcjcxxlblist" id="bkteachingeval-jcjcxxlblist"
                  menuPattern="pc"
                  name="教材基础信息列表"
                  showOrder="16"
                  menuHidden="1">
                <routerParams>{ "appCode": "bkteachingeval", "pageCode": "jcjcxxlblist"}</routerParams>
                <!-- 权限字-表单 -->
                <permCode name="教材基础信息列表" permCode="formjcjcxxlblist"
                          permCodeId="bkteachingeval-jcjcxxlblist"
                          permCodeType="0"/>
                <!-- 权限字-片段 -->
                <permCode name="教材基础信息列表" permCode="formjcjcxxlblist:fragmentjcjcxxlblist"
                          permCodeId="fragmentbkteachingeval-jcjcxxlblist"
                          permCodeType="1">
                    <perm name="数据更新时间接口" url="/jwdatapub/dataUpdateTime/**"/>
                    <perm name="字典集合" url="/admin/dict/list"/>
                    <perm name="查询级联表格平铺接口" url="/eda/bkteachingeval/find/jcjcxxlb/jcjcxxlb"/>
                    <perm name="页面数据展示" url="/eda/bkteachingeval/page/jcjcxxlb"/>
                    <perm name="页面数据导出" url="/eda/bkteachingeval/export/jcjcxxlb"/>
                    <perm name="页面导出文件下载" url="/admin/file/bkteachingeval/download"/>
                </permCode>
                <btn id="bkteachingeval-jcjcxxlblist-show" menuPattern="pc" name="显示">
                    <permCode name="显示" permCode="formjcjcxxlblist:fragmentjcjcxxlblist:show"
                              permCodeId="bkteachingeval-jcjcxxlblist-show"/>
                </btn>
            </menu>

            <menu bindType="default" formRouterName="xyssblblist" id="bkteachingeval-xyssblblist"
                  menuPattern="pc"
                  name="学院生师比列表"
                  showOrder="17"
                  menuHidden="1">
                <routerParams>{ "appCode": "bkteachingeval", "pageCode": "xyssblblist"}</routerParams>
                <!-- 权限字-表单 -->
                <permCode name="学院生师比列表" permCode="formxyssblblist" permCodeId="bkteachingeval-xyssblblist"
                          permCodeType="0"/>
                <!-- 权限字-片段 -->
                <permCode name="学院生师比列表" permCode="formxyssblblist:fragmentxyssblblist"
                          permCodeId="fragmentbkteachingeval-xyssblblist"
                          permCodeType="1">
                    <perm name="数据更新时间接口" url="/jwdatapub/dataUpdateTime/**"/>
                    <perm name="字典集合" url="/admin/dict/list"/>
                    <perm name="查询级联表格平铺接口" url="/eda/bkteachingeval/find/xyssblb/xyssblb"/>
                    <perm name="页面数据展示" url="/eda/bkteachingeval/page/xyssblb"/>
                    <perm name="页面数据导出" url="/eda/bkteachingeval/export/xyssblb"/>
                    <perm name="页面导出文件下载" url="/admin/file/bkteachingeval/download"/>
                </permCode>
                <btn id="bkteachingeval-xyssblblist-show" menuPattern="pc" name="显示">
                    <permCode name="显示" permCode="formxyssblblist:fragmentxyssblblist:show"
                              permCodeId="bkteachingeval-xyssblblist-show"/>
                </btn>
            </menu>
            <menu bindType="default" formRouterName="xywskjssllblist" id="bkteachingeval-xywskjssllblist"
                  menuPattern="pc"
                  name="学院未上课教授数量列表"
                  showOrder="18"
                  menuHidden="1">
                <routerParams>{ "appCode": "bkteachingeval", "pageCode": "xywskjssllblist"}</routerParams>
                <!-- 权限字-表单 -->
                <permCode name="学院未上课教授数量列表" permCode="formxywskjssllblist"
                          permCodeId="bkteachingeval-xywskjssllblist"
                          permCodeType="0"/>
                <!-- 权限字-片段 -->
                <permCode name="学院未上课教授数量列表" permCode="formxywskjssllblist:fragmentxywskjssllblist"
                          permCodeId="fragmentbkteachingeval-xywskjssllblist"
                          permCodeType="1">
                    <perm name="数据更新时间接口" url="/jwdatapub/dataUpdateTime/**"/>
                    <perm name="字典集合" url="/admin/dict/list"/>
                    <perm name="查询级联表格平铺接口" url="/eda/bkteachingeval/find/xywskjssllb/xywskjssllb"/>
                    <perm name="页面数据展示" url="/eda/bkteachingeval/page/xywskjssllb"/>
                    <perm name="页面数据导出" url="/eda/bkteachingeval/export/xywskjssllb"/>
                    <perm name="页面导出文件下载" url="/admin/file/bkteachingeval/download"/>
                </permCode>
                <btn id="bkteachingeval-xywskjssllblist-show" menuPattern="pc" name="显示">
                    <permCode name="显示" permCode="formxywskjssllblist:fragmentxywskjssllblist:show"
                              permCodeId="bkteachingeval-xywskjssllblist-show"/>
                </btn>
            </menu>
            <menu bindType="default" formRouterName="xywskgccrcsllblist" id="bkteachingeval-xywskgccrcsllblist"
                  menuPattern="pc"
                  name="学院未上课高层次人才数量列表"
                  showOrder="19"
                  menuHidden="1">
                <routerParams>{ "appCode": "bkteachingeval", "pageCode": "xywskgccrcsllblist"}</routerParams>
                <!-- 权限字-表单 -->
                <permCode name="学院未上课高层次人才数量列表" permCode="formxywskgccrcsllblist"
                          permCodeId="bkteachingeval-xywskgccrcsllblist"
                          permCodeType="0"/>
                <!-- 权限字-片段 -->
                <permCode name="学院未上课高层次人才数量列表"
                          permCode="formxywskgccrcsllblist:fragmentxywskgccrcsllblist"
                          permCodeId="fragmentbkteachingeval-xywskgccrcsllblist"
                          permCodeType="1">
                    <perm name="数据更新时间接口" url="/jwdatapub/dataUpdateTime/**"/>
                    <perm name="字典集合" url="/admin/dict/list"/>
                    <perm name="查询级联表格平铺接口" url="/eda/bkteachingeval/find/xywskgccrcsllb/xywskgccrcsllb"/>
                    <perm name="页面数据展示" url="/eda/bkteachingeval/page/xywskgccrcsllb"/>
                    <perm name="页面数据导出" url="/eda/bkteachingeval/export/xywskgccrcsllb"/>
                    <perm name="页面导出文件下载" url="/admin/file/bkteachingeval/download"/>
                </permCode>
                <btn id="bkteachingeval-xywskgccrcsllblist-show" menuPattern="pc" name="显示">
                    <permCode name="显示" permCode="formxywskgccrcsllblist:fragmentxywskgccrcsllblist:show"
                              permCodeId="bkteachingeval-xywskgccrcsllblist-show"/>
                </btn>
            </menu>
            <menu bindType="default" formRouterName="xyllkdyqxjzlblist" id="bkteachingeval-xyllkdyqxjzlblist"
                  menuPattern="pc"
                  name="学院理论课低于全校均值列表"
                  showOrder="20"
                  menuHidden="1">
                <routerParams>{ "appCode": "bkteachingeval", "pageCode": "xyllkdyqxjzlblist"}</routerParams>
                <!-- 权限字-表单 -->
                <permCode name="学院理论课低于全校均值列表" permCode="formxyllkdyqxjzlblist"
                          permCodeId="bkteachingeval-xyllkdyqxjzlblist"
                          permCodeType="0"/>
                <!-- 权限字-片段 -->
                <permCode name="学院理论课低于全校均值列表"
                          permCode="formxyllkdyqxjzlblist:fragmentxyllkdyqxjzlblist"
                          permCodeId="fragmentbkteachingeval-xyllkdyqxjzlblist"
                          permCodeType="1">
                    <perm name="数据更新时间接口" url="/jwdatapub/dataUpdateTime/**"/>
                    <perm name="字典集合" url="/admin/dict/list"/>
                    <perm name="查询级联表格平铺接口" url="/eda/bkteachingeval/find/xykcfldyqxjzlb/xykcfldyqxjzlb"/>
                    <perm name="页面数据展示" url="/eda/bkteachingeval/page/xykcfldyqxjzlb"/>
                    <perm name="页面数据导出" url="/eda/bkteachingeval/export/xykcfldyqxjzlb"/>
                    <perm name="页面导出文件下载" url="/admin/file/bkteachingeval/download"/>
                </permCode>
                <btn id="bkteachingeval-xyllkdyqxjzlblist-show" menuPattern="pc" name="显示">
                    <permCode name="显示" permCode="formxyllkdyqxjzlblist:fragmentxyllkdyqxjzlblist:show"
                              permCodeId="bkteachingeval-xyllkdyqxjzlblist-show"/>
                </btn>
            </menu>
            <menu bindType="default" formRouterName="xydlsykdyqxjzlblist" id="bkteachingeval-xydlsykdyqxjzlblist"
                  menuPattern="pc"
                  name="学院独立实验课低于全校均值列表"
                  showOrder="21"
                  menuHidden="1">
                <routerParams>{ "appCode": "bkteachingeval", "pageCode": "xydlsykdyqxjzlblist"}</routerParams>
                <!-- 权限字-表单 -->
                <permCode name="学院独立实验课低于全校均值列表" permCode="formxydlsykdyqxjzlblist"
                          permCodeId="bkteachingeval-xydlsykdyqxjzlblist"
                          permCodeType="0"/>
                <!-- 权限字-片段 -->
                <permCode name="学院独立实验课低于全校均值列表"
                          permCode="formxydlsykdyqxjzlblist:fragmentxydlsykdyqxjzlblist"
                          permCodeId="fragmentbkteachingeval-xydlsykdyqxjzlblist"
                          permCodeType="1">
                    <perm name="数据更新时间接口" url="/jwdatapub/dataUpdateTime/**"/>
                    <perm name="字典集合" url="/admin/dict/list"/>
                    <perm name="查询级联表格平铺接口" url="/eda/bkteachingeval/find/xykcfldyqxjzlb/xykcfldyqxjzlb"/>
                    <perm name="页面数据展示" url="/eda/bkteachingeval/page/xykcfldyqxjzlb"/>
                    <perm name="页面数据导出" url="/eda/bkteachingeval/export/xykcfldyqxjzlb"/>
                    <perm name="页面导出文件下载" url="/admin/file/bkteachingeval/download"/>
                </permCode>
                <btn id="bkteachingeval-xydlsykdyqxjzlblist-show" menuPattern="pc" name="显示">
                    <permCode name="显示" permCode="formxydlsykdyqxjzlblist:fragmentxydlsykdyqxjzlblist:show"
                              permCodeId="bkteachingeval-xydlsykdyqxjzlblist-show"/>
                </btn>
            </menu>
            <menu bindType="default" formRouterName="xygzkclblist" id="bkteachingeval-xygzkclblist"
                  menuPattern="pc"
                  name="学院关注课程列表"
                  showOrder="22"
                  menuHidden="1">
                <routerParams>{ "appCode": "bkteachingeval", "pageCode": "xygzkclblist"}</routerParams>
                <!-- 权限字-表单 -->
                <permCode name="学院关注课程列表" permCode="formxygzkclblist"
                          permCodeId="bkteachingeval-xygzkclblist"
                          permCodeType="0"/>
                <!-- 权限字-片段 -->
                <permCode name="学院关注课程列表" permCode="formxygzkclblist:fragmentxygzkclblist"
                          permCodeId="fragmentbkteachingeval-xygzkclblist"
                          permCodeType="1">
                    <perm name="数据更新时间接口" url="/jwdatapub/dataUpdateTime/**"/>
                    <perm name="字典集合" url="/admin/dict/list"/>
                    <perm name="查询级联表格平铺接口" url="/eda/bkteachingeval/find/xygzkclb/xygzkclb"/>
                    <perm name="页面数据展示" url="/eda/bkteachingeval/page/xygzkclb"/>
                    <perm name="页面数据导出" url="/eda/bkteachingeval/export/xygzkclb"/>
                    <perm name="页面导出文件下载" url="/admin/file/bkteachingeval/download"/>
                </permCode>
                <btn id="bkteachingeval-xygzkclblist-show" menuPattern="pc" name="显示">
                    <permCode name="显示" permCode="formxygzkclblist:fragmentxygzkclblist:show"
                              permCodeId="bkteachingeval-xygzkclblist-show"/>
                </btn>
            </menu>
            <menu bindType="default" formRouterName="xnxybdscpjfdblblist" id="bkteachingeval-xnxybdscpjfdblblist"
                  menuPattern="pc"
                  name="学年学院班导师测评均分对比列表"
                  showOrder="23"
                  menuHidden="1">
                <routerParams>{ "appCode": "bkteachingeval", "pageCode": "xnxybdscpjfdblblist"}</routerParams>
                <!-- 权限字-表单 -->
                <permCode name="学年学院班导师测评均分对比列表" permCode="formxnxybdscpjfdblblist"
                          permCodeId="bkteachingeval-xnxybdscpjfdblblist"
                          permCodeType="0"/>
                <!-- 权限字-片段 -->
                <permCode name="学年学院班导师测评均分对比列表"
                          permCode="formxnxybdscpjfdblblist:fragmentxnxybdscpjfdblblist"
                          permCodeId="fragmentbkteachingeval-xnxybdscpjfdblblist"
                          permCodeType="1">
                    <perm name="数据更新时间接口" url="/jwdatapub/dataUpdateTime/**"/>
                    <perm name="字典集合" url="/admin/dict/list"/>
                    <perm name="查询级联表格平铺接口"
                          url="/eda/bkteachingeval/find/xnxybdscpjfdblb/xnxybdscpjfdblb"/>
                    <perm name="页面数据展示" url="/eda/bkteachingeval/page/xnxybdscpjfdblb"/>
                    <perm name="页面数据导出" url="/eda/bkteachingeval/export/xnxybdscpjfdblb"/>
                    <perm name="页面导出文件下载" url="/admin/file/bkteachingeval/download"/>
                </permCode>
                <btn id="bkteachingeval-xnxybdscpjfdblblist-show" menuPattern="pc" name="显示">
                    <permCode name="显示" permCode="formxnxybdscpjfdblblist:fragmentxnxybdscpjfdblblist:show"
                              permCodeId="bkteachingeval-xnxybdscpjfdblblist-show"/>
                </btn>
            </menu>
            <menu bindType="default" formRouterName="xnxyddtkjfdblblist" id="bkteachingeval-xnxyddtkjfdblblist"
                  menuPattern="pc"
                  name="学年学院督导听课均分对比列表"
                  showOrder="24"
                  menuHidden="1">
                <routerParams>{ "appCode": "bkteachingeval", "pageCode": "xnxyddtkjfdblblist"}</routerParams>
                <!-- 权限字-表单 -->
                <permCode name="学年学院督导听课均分对比列表" permCode="formxnxyddtkjfdblblist"
                          permCodeId="bkteachingeval-xnxyddtkjfdblblist"
                          permCodeType="0"/>
                <!-- 权限字-片段 -->
                <permCode name="学年学院督导听课均分对比列表"
                          permCode="formxnxyddtkjfdblblist:fragmentxnxyddtkjfdblblist"
                          permCodeId="fragmentbkteachingeval-xnxyddtkjfdblblist"
                          permCodeType="1">
                    <perm name="数据更新时间接口" url="/jwdatapub/dataUpdateTime/**"/>
                    <perm name="字典集合" url="/admin/dict/list"/>
                    <perm name="查询级联表格平铺接口" url="/eda/bkteachingeval/find/xnxyddtkjfdblb/xnxyddtkjfdblb"/>
                    <perm name="页面数据展示" url="/eda/bkteachingeval/page/xnxyddtkjfdblb"/>
                    <perm name="页面数据导出" url="/eda/bkteachingeval/export/xnxyddtkjfdblb"/>
                    <perm name="页面导出文件下载" url="/admin/file/bkteachingeval/download"/>
                </permCode>
                <btn id="bkteachingeval-xnxyddtkjfdblblist-show" menuPattern="pc" name="显示">
                    <permCode name="显示" permCode="formxnxyddtkjfdblblist:fragmentxnxyddtkjfdblblist:show"
                              permCodeId="bkteachingeval-xnxyddtkjfdblblist-show"/>
                </btn>
            </menu>
        </menus>

    </app>
</group>