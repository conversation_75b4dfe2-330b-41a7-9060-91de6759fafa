spring:
  resources:
    static-locations: classpath:page,classpath:static
  thymeleaf:
    prefix: templates
    suffix: .html
    mode: html
    encoding: utf-8
    cache: false
    templateResolverOrder: 1
  flyway:
    enabled: true
    locations: classpath:dbsql/
    table: lowcode_schema_bkteachingeval
    baseline-on-migrate: true
context:
  listener:
    classes: com.wisedu.lowcode4j.app.common.listener.MyListener
nutz:
  dao:
    enabled: true
    datasource: master
    create: true #是否自动建表 默认true
    migration: true #是否自动变更 默认true
    add-column: true # 是否添加列 默认true
    delete-column: false # 是否删除列 默认true
    foce-create: false # 是否删表重建，注意此功能会删除全部表及数据，一般应用于demo或测试 默认false
    check-index: false # 是否检查索引 默认true
    basepackage: # 相关实体所在包
      - com.wisedu.lowcode4j.app.bkteachingeval.po
      - com.wisedu.lowcode4j.app.bkteachingeval.hxzljk.po
      - com.wisedu.lowcode4j.app.bkteachingeval.dwymb.po
      - com.wisedu.lowcode4j.app.bkteachingeval.szdw.po
      - com.wisedu.lowcode4j.app.bkteachingeval.jxzy.po
      - com.wisedu.lowcode4j.app.bkteachingeval.zsqk.po
      - com.wisedu.lowcode4j.app.bkteachingeval.pygc.po
      - com.wisedu.lowcode4j.app.bkteachingeval.jyyfz.po
      - com.wisedu.lowcode4j.app.bkteachingeval.zljkpg.po
      - com.wisedu.lowcode4j.app.bkteachingeval.cgyts.po
      - com.wisedu.lowcode4j.app.bkteachingeval.zlhx.po
    popackage: # 数据模型所在包
      - com.wisedu.lowcode4j.app.bkteachingeval.po
      - com.wisedu.lowcode4j.app.bkteachingeval.hxzljk.po
      - com.wisedu.lowcode4j.app.bkteachingeval.dwymb.po
      - com.wisedu.lowcode4j.app.bkteachingeval.szdw.po
      - com.wisedu.lowcode4j.app.bkteachingeval.jxzy.po
      - com.wisedu.lowcode4j.app.bkteachingeval.zsqk.po
      - com.wisedu.lowcode4j.app.bkteachingeval.pygc.po
      - com.wisedu.lowcode4j.app.bkteachingeval.jyyfz.po
      - com.wisedu.lowcode4j.app.bkteachingeval.zljkpg.po
      - com.wisedu.lowcode4j.app.bkteachingeval.cgyts.po
      - com.wisedu.lowcode4j.app.bkteachingeval.zlhx.po
    bopackage: # 业务模型所在包
      - com.wisedu.lowcode4j.app.bkteachingeval.bo
      - com.wisedu.lowcode4j.app.bkteachingeval.hxzljk.bo
      - com.wisedu.lowcode4j.app.bkteachingeval.dwymb.bo
      - com.wisedu.lowcode4j.app.bkteachingeval.szdw.bo
      - com.wisedu.lowcode4j.app.bkteachingeval.jxzy.bo
      - com.wisedu.lowcode4j.app.bkteachingeval.zsqk.bo
      - com.wisedu.lowcode4j.app.bkteachingeval.pygc.bo
      - com.wisedu.lowcode4j.app.bkteachingeval.jyyfz.bo
      - com.wisedu.lowcode4j.app.bkteachingeval.zljkpg.bo
      - com.wisedu.lowcode4j.app.bkteachingeval.cgyts.bo
      - com.wisedu.lowcode4j.app.bkteachingeval.zlhx.bo
dict:
  packages:
    - com.wisedu.lowcode4j.app.bkteachingeval.constant
    - com.wisedu.lowcode4j.app.bkteachingeval.hxzljk.constant
    - com.wisedu.lowcode4j.app.bkteachingeval.dwymb.constant
    - com.wisedu.lowcode4j.app.bkteachingeval.szdw.constant
    - com.wisedu.lowcode4j.app.bkteachingeval.jxzy.constant
    - com.wisedu.lowcode4j.app.bkteachingeval.zsqk.constant
    - com.wisedu.lowcode4j.app.bkteachingeval.pygc.constant
    - com.wisedu.lowcode4j.app.bkteachingeval.jyyfz.constant
    - com.wisedu.lowcode4j.app.bkteachingeval.zljkpg.constant
    - com.wisedu.lowcode4j.app.bkteachingeval.cgyts.constant
    - com.wisedu.lowcode4j.app.bkteachingeval.zlhx.constant
bkteachingeval:
  indicator:
    data:
      init:
        cron: 0 0 4 * * ?