class commonFunJs{
    constructor(){
        try{
            window.addEventListener('message', this.setFrameParams, false);
        }catch(e){
            console.log("e:",e)
        }
    }
    addListener(){
        window.addEventListener('message', this.setFrameParams, false);
    }
    destroyListener(){
        console.log("commonjs销毁lis")
        window.removeEventListener('message', this.setFrameParams, false);
    }
    setFrameParams = (e) => {
        let self = this
        if (e.origin === 'http://127.0.0.1:8002') {
            /**判断消息来自的域，暂时用不到 */
        }
        if(e.data.messageType == 'frreportOpenDialog'){
            /*这里可以写业务逻辑*/
            self.openDrawer(e.data)
        }
    }
    resetSearch(dialog){
        let dialogGlobalVars = dialog.dialog.$refs['generate-page'].page.globalVars
        let keyArr = JSON.parse(JSON.stringify(dialogGlobalVars.keyArr)), lastValueArr = []
        keyArr.forEach(ele=>{
            if(!ele.hidden) {
                ele.value = ''
                ele.valueTemp = ''
            }else{
                ele.valueTemp = dialogGlobalVars.keyMap[ele.valueKey]
                ele.value = dialogGlobalVars.keyMap[ele.valueKey]
                let key = `${ele.valueKey}`
                outPage.globalVars.pluginObject[key] = ele.valueTemp;
                if(Object.prototype.toString.call(ele.valueTemp) === '[object Array]'){
                    /**如果是数组，则将value改为逗号分割的字符串 */
                    ele.value = ele.valueTemp.join(',')
                }
            }

            lastValueArr.push(ele)
        })
        let iframeWindow = window.frames.frreport
        iframeWindow.postMessage({
            valueArr: lastValueArr,
            type: 'chartFilterCondition',
            filterBtnKey: `${outPage.globalVars.filterObject.filterPage}`
        }, '*');
        dialog.dialog.closeWindow()
    }
    openDrawer(data) {
        let self = this, keyArr = data.keyArr;
        outPage.globalVars.filterObject = data
        let buttonList = [
            {
                "label": "搜索",
                "id": "olfitp9i",
                "uuid": "cnis5t8j",
                "type": "primary",
                "func": self.confirmDialog,
                "children": []
            },
            {
                "label": "重置",
                "id": "btn_1712828434650",
                "uuid": "btn_1712828434650",
                "type": "",
                "func": self.resetSearch
            },
            {
                "label": "取消",
                "id": "btn_1712828434650",
                "uuid": "btn_1712828434650",
                "type": "",
                "func": {}
            },
        ]
        outPage.$openPage(
            {
                appCode:"bkteachingeval",
                pageCode:"drawerdialog",
                pageParams: {
                    keyArr,
                    btnObject: data,
                    pluginObject: outPage.globalVars.pluginObject
                }
            },
            {
                type: '2', winParams: {
                    withHeader: false
                }
            }).bindBtns(buttonList)
    }
    confirmDialog(dialog){
        let dialogGlobalVars = dialog.dialog.$refs['generate-page'].page.globalVars
        let iframeWindow = window.frames.frreport
        let keyArr = JSON.parse(JSON.stringify(dialogGlobalVars.keyArr)), lastValueArr = []
        keyArr.forEach(ele=>{
            if(!ele.hidden){
                // 空值也需要回传，重新查询时刻清空筛选条件用
                // if(dialogGlobalVars.keyMap[ele.valueKey]){
                ele.valueTemp = dialogGlobalVars.keyMap[ele.valueKey]
                ele.value = dialogGlobalVars.keyMap[ele.valueKey]
                // let key = `${outPage.globalVars.filterPage}_${ele.valueKey}`
                let key = `${ele.valueKey}`
                outPage.globalVars.pluginObject[key] = ele.valueTemp
                if(Object.prototype.toString.call(ele.valueTemp) == '[object Array]'){
                    /**如果是数组，则将value改为逗号分割的字符串 */
                    ele.value = ele.valueTemp.join(',')
                }
                lastValueArr.push(ele)
                // }
            }
        })
        iframeWindow.postMessage({
            valueArr: lastValueArr,
            type: 'chartFilterCondition',
            filterBtnKey: `${outPage.globalVars.filterObject.filterPage}`
        }, '*');
        dialog.dialog.closeWindow()
    }
}
let commonFun = new commonFunJs()
return commonFun
