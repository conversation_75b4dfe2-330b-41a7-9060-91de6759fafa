<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">  
  <parent> 
    <artifactId>app-bkteachingeval</artifactId>  
    <groupId>com.wisedu.lowcode4j</groupId>  
    <version>1.0.0</version> 
  </parent>  
  <modelVersion>4.0.0</modelVersion>  
  <artifactId>${app.name}</artifactId>  
  <packaging>jar</packaging>  
  <version>${app.version}</version>  
  <properties> 
    <app.version>1.0.0</app.version>  
    <app.name>bkteachingeval</app.name> 
  </properties>  
  <dependencies> 
    <dependency> 
      <groupId>com.wisedu.lowcode4j</groupId>  
      <artifactId>bkteachingeval-common</artifactId>  
      <version>1.0.0</version> 
    </dependency>  
    <dependency> 
      <groupId>com.wisedu.lowcode4j</groupId>  
      <artifactId>liteflow-common</artifactId>  
      <scope>provided</scope> 
    </dependency>  
    <dependency> 
      <groupId>com.wisedu.lowcode4j</groupId>  
      <artifactId>db-common</artifactId>  
      <scope>provided</scope> 
    </dependency>  
    <dependency> 
      <groupId>com.wisedu.lowcode4j</groupId>  
      <artifactId>model-common</artifactId>  
      <scope>provided</scope> 
    </dependency>  
    <dependency> 
      <groupId>com.wisedu.lowcode4j</groupId>  
      <artifactId>lowcode4j-common</artifactId>  
      <scope>provided</scope> 
    </dependency>  
    <dependency> 
      <groupId>com.wisedu.lowcode4j</groupId>  
      <artifactId>job-common</artifactId>  
      <scope>provided</scope> 
    </dependency>  
    <dependency> 
      <groupId>com.wisedu.lowcode4j</groupId>  
      <artifactId>redis-common</artifactId>  
      <scope>provided</scope> 
    </dependency>  
    <dependency> 
      <groupId>com.wisedu.lowcode4j</groupId>  
      <artifactId>validator-common</artifactId>  
      <scope>provided</scope> 
    </dependency>  
    <dependency> 
      <groupId>com.wisedu.lowcode4j</groupId>  
      <artifactId>datafilter-common</artifactId>  
      <scope>provided</scope> 
    </dependency>  
    <dependency> 
      <groupId>com.wisedu.lowcode4j</groupId>  
      <artifactId>flow-common</artifactId>  
      <scope>provided</scope> 
    </dependency>  
    <dependency> 
      <groupId>com.wisedu.lowcode4j</groupId>  
      <artifactId>dict-common</artifactId>  
      <scope>provided</scope> 
    </dependency>  
    <dependency> 
      <groupId>com.wisedu.lowcode4j</groupId>  
      <artifactId>file-common</artifactId>  
      <scope>provided</scope> 
    </dependency>  
    <dependency> 
      <groupId>com.wisedu.lowcode4j</groupId>  
      <artifactId>log-common</artifactId>  
      <scope>provided</scope> 
    </dependency>  
    <dependency> 
      <groupId>com.wisedu.lowcode4j</groupId>  
      <artifactId>message-common</artifactId>  
      <scope>provided</scope> 
    </dependency>  
    <dependency> 
      <groupId>com.wisedu.lowcode4j</groupId>  
      <artifactId>development-common</artifactId>  
      <scope>provided</scope> 
    </dependency>  
    <dependency> 
      <groupId>com.wisedu.lowcode4j</groupId>  
      <artifactId>bkteachingeval-hxzljk</artifactId>  
      <version>1.0.0</version> 
    </dependency>  
    <dependency> 
      <groupId>com.wisedu.lowcode4j</groupId>  
      <artifactId>bkteachingeval-dwymb</artifactId>  
      <version>1.0.0</version> 
    </dependency>  
    <dependency> 
      <groupId>com.wisedu.lowcode4j</groupId>  
      <artifactId>bkteachingeval-szdw</artifactId>  
      <version>1.0.0</version> 
    </dependency>  
    <dependency> 
      <groupId>com.wisedu.lowcode4j</groupId>  
      <artifactId>bkteachingeval-jxzy</artifactId>  
      <version>1.0.0</version> 
    </dependency>  
    <dependency> 
      <groupId>com.wisedu.lowcode4j</groupId>  
      <artifactId>bkteachingeval-zsqk</artifactId>  
      <version>1.0.0</version> 
    </dependency>  
    <dependency> 
      <groupId>com.wisedu.lowcode4j</groupId>  
      <artifactId>bkteachingeval-pygc</artifactId>  
      <version>1.0.0</version> 
    </dependency>  
    <dependency> 
      <groupId>com.wisedu.lowcode4j</groupId>  
      <artifactId>bkteachingeval-jyyfz</artifactId>  
      <version>1.0.0</version> 
    </dependency>  
    <dependency> 
      <groupId>com.wisedu.lowcode4j</groupId>  
      <artifactId>bkteachingeval-zljkpg</artifactId>  
      <version>1.0.0</version> 
    </dependency>  
    <dependency> 
      <groupId>com.wisedu.lowcode4j</groupId>  
      <artifactId>bkteachingeval-cgyts</artifactId>  
      <version>1.0.0</version> 
    </dependency>  
    <dependency> 
      <groupId>com.wisedu.lowcode4j</groupId>  
      <artifactId>bkteachingeval-zlhx</artifactId>  
      <version>1.0.0</version> 
    </dependency>
  </dependencies>  
  <build> 
    <plugins> 
      <!--把当前项目所有的依赖打包到target目录下的lib文件夹下-->  
      <plugin> 
        <groupId>com.gitee.starblues</groupId>  
        <artifactId>spring-brick-maven-packager</artifactId>  
        <version>${spring-brick.version}</version>  
        <configuration> 
          <mode>${plugin.build.mode}</mode>  
          <pluginInfo> 
            <id>${app.name}</id>  
            <bootstrapClass>com.wisedu.lowcode4j.app.bkteachingeval.Application</bootstrapClass>  
            <version>${app.version}</version>  
            <requires>1.0.0</requires>  
            <provider>hj</provider>  
            <description>本科教育教学质量保障平台</description>  
            <configFileName>${app.name}.yml</configFileName> 
          </pluginInfo>  
          <prodConfig> 
            <packageType>jar-outer</packageType>  
            <fileName>${app.name}-${app.version}</fileName>  
            <libDir>~/apps-lib</libDir> 
          </prodConfig>  
          <loadMainResourcePattern> 
            <includes> 
              <include>org.flywaydb.core.**</include>  
              <include>com.baomidou.dynamic.datasource.**</include>  
              <include>cn.hutool.**</include>  
              <include>org.sagacity.sqltoy.**</include>  
              <include>org.nutz.**</include>  
              <include>com.wisedu.lowcode4j.common.**</include>  
              <include>com.wisedu.lowcode4j.main.constant.**</include>  
              <include>com.wisedu.lowcode4j.common.flow.po.**</include>  
              <include>com.github.benmanes.caffeine.**</include>  
              <include>ch.qos.logback.**</include>  
              <include>org.apache.logging.slf4j.**</include>  
              <include>org.apache.commons.lang3.**</include>  
              <include>org.apache.commons.collections4.**</include>  
              <include>org.apache.commons.io.**</include>  
              <include>org.apache.commons.codec.**</include>  
              <include>com.google.common.**</include>  
              <include>com.yomahub.liteflow.**</include>  
              <include>cn.dev33.satoken.**</include>  
              <include>com.alibaba.fastjson.**</include>  
              <include>io.swagger.annotations.**</include>  
              <include>com.xxl.job.**</include>  
              <include>org.flowable.**</include>  
              <include>org.yaml.snakeyaml.**</include> 
            </includes> 
          </loadMainResourcePattern>  
          <excludes> 
            <exclude> 
              <groupId>ch.qos.logback</groupId>  
              <artifactId>logback-core</artifactId> 
            </exclude>  
            <exclude> 
              <groupId>ch.qos.logback</groupId>  
              <artifactId>logback-classic</artifactId> 
            </exclude>  
            <exclude> 
              <groupId>org.apache.logging.log4j</groupId>  
              <artifactId>log4j-api</artifactId> 
            </exclude>  
            <exclude> 
              <groupId>org.apache.logging.log4j</groupId>  
              <artifactId>log4j-to-slf4j</artifactId> 
            </exclude>  
            <exclude> 
              <groupId>org.slf4j</groupId>  
              <artifactId>jul-to-slf4j</artifactId> 
            </exclude>  
            <exclude> 
              <groupId>org.slf4j</groupId>  
              <artifactId>slf4j-api</artifactId> 
            </exclude>  
            <exclude> 
              <groupId>com.xuxueli</groupId>  
              <artifactId>xxl-job-core</artifactId> 
            </exclude>  
            <exclude> 
              <groupId>com.sagframe</groupId>  
              <artifactId>sagacity-sqltoy</artifactId> 
            </exclude>  
            <exclude> 
              <groupId>org.nutz</groupId>  
              <artifactId>nutz</artifactId> 
            </exclude>  
            <exclude> 
              <groupId>cn.hutool</groupId>  
              <artifactId>hutool-core</artifactId> 
            </exclude>  
            <exclude> 
              <groupId>cn.hutool</groupId>  
              <artifactId>hutool-http</artifactId> 
            </exclude>  
            <exclude> 
              <groupId>io.swagger</groupId>  
              <artifactId>swagger-annotations</artifactId> 
            </exclude>  
            <exclude> 
              <groupId>org.yaml</groupId>  
              <artifactId>snakeyaml</artifactId> 
            </exclude> 
          </excludes>  
          <includeSystemScope>true</includeSystemScope> 
        </configuration>  
        <executions> 
          <execution> 
            <goals> 
              <goal>repackage</goal> 
            </goals> 
          </execution> 
        </executions> 
      </plugin> 
    </plugins> 
  </build> 
</project>
