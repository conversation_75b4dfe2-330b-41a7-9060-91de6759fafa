package com.wisedu.lowcode4j.app.qualityperformance.bo;

import com.alibaba.fastjson.annotation.JSONField;
import com.wisedu.lowcode4j.common.core.annotation.ColumnDefine;
import com.wisedu.lowcode4j.common.core.annotation.ModelDefine;
import com.wisedu.lowcode4j.common.core.constant.ApplicationConstant;
import com.wisedu.lowcode4j.common.core.constant.DictCacheType;
import com.wisedu.lowcode4j.common.core.model.BaseBizModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Comment;
import org.sagacity.sqltoy.config.annotation.Translate;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "bksxxlb", description = "本科生信息列表")
@Comment("本科生信息列表")
@ModelDefine(renderType="table")
//end_dynamic_declare
@Data
public class Bksxxlb extends BaseBizModel {

    private static final long serialVersionUID = 4431474721041953813L;

    //region start_dynamic_column
    @ApiModelProperty(name = "xh", value = "学号" ,notes = "2024-11-11 15:25:46")
    @Comment("学号")
    @Column(value = "xh")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String xh;

    @ApiModelProperty(name = "tjnf", value = "统计年份" ,notes = "2024-11-11 15:26:02")
    @Comment("统计年份")
    @Column(value = "tjnf")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=13,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String tjnf;

    @ApiModelProperty(name = "xsxm", value = "姓名" ,notes = "2025-02-20 09:48:48")
    @Comment("姓名")
    @Column(value = "xsxm")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=1,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String xsxm;

    @ApiModelProperty(name = "xb", value = "性别" ,notes = "2025-02-20 09:49:24")
    @Comment("性别")
    @Column(value = "xb")
    @ColumnDefine(columnDisplay=true,columnDict="XB",columnReadonly=false,orderIndex=2,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String xb;

    @ApiModelProperty(name = "nj", value = "年级" ,notes = "2025-02-20 09:49:54")
    @Comment("年级")
    @Column(value = "nj")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=3,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String nj;

    @ApiModelProperty(name = "xy", value = "学院" ,notes = "2025-02-20 09:50:36")
    @Comment("学院")
    @Column(value = "xy")
    @ColumnDefine(columnDisplay=true,columnDict="ZZJG",columnReadonly=false,orderIndex=4,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String xy;

    @ApiModelProperty(name = "xnzydldm", value = "校内专业（大类）代码" ,notes = "2025-02-20 09:51:16")
    @Comment("校内专业（大类）代码")
    @Column(value = "xnzydldm")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=5,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String xnzydldm;

    @ApiModelProperty(name = "xnzydlmc", value = "校内专业（大类）名称" ,notes = "2025-02-20 09:51:33")
    @Comment("校内专业（大类）名称")
    @Column(value = "xnzydlmc")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=6,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String xnzydlmc;

    @ApiModelProperty(name = "xjzt", value = "学籍状态" ,notes = "2025-02-20 09:52:52")
    @Comment("学籍状态")
    @Column(value = "xjzt")
    @ColumnDefine(columnDisplay=true,columnDict="XJZT",columnReadonly=false,orderIndex=12,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String xjzt;

    @ApiModelProperty(name = "bndgpa", value = "本年度GPA" ,notes = "2025-02-20 09:54:35")
    @Comment("本年度GPA")
    @Column(value = "bndgpa")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=8,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private Double bndgpa;

    @ApiModelProperty(name = "bndsjsxcs", value = "本年度实践实习次数" ,notes = "2025-02-20 09:55:10")
    @Comment("本年度实践实习次数")
    @Column(value = "bndsjsxcs")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=9,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private Integer bndsjsxcs;

    @ApiModelProperty(name = "bndxscgsl", value = "本年度学术成果数量" ,notes = "2025-02-20 09:55:50")
    @Comment("本年度学术成果数量")
    @Column(value = "bndxscgsl")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=10,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private Integer bndxscgsl;

    @ApiModelProperty(name = "bndrych", value = "荣誉称号" ,notes = "2025-02-20 09:56:21")
    @Comment("荣誉称号")
    @Column(value = "bndrych")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=11,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String bndrych;

    @ApiModelProperty(name = "xzb", value = "班级" ,notes = "2025-02-20 09:52:15")
    @Comment("班级")
    @Column(value = "xzb")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=7,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String xzb;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "xbName", value = "性别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "xb", cacheType = "XB", split=",")
    @JSONField(name = "xb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String xbName;

    @ApiModelProperty(name = "xyName", value = "学院名称")
    @Translate(cacheName = "ZZJG", keyField = "xy", cacheType = "", split=",")
    @JSONField(name = "xy"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String xyName;

    @ApiModelProperty(name = "xjztName", value = "学籍状态名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "xjzt", cacheType = "XJZT", split=",")
    @JSONField(name = "xjzt"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String xjztName;

	//endregion end_dynamic_dict_column

}
