package com.wisedu.lowcode4j.app.qualityperformance.bo;

import com.alibaba.fastjson.annotation.JSONField;
import com.wisedu.lowcode4j.common.core.annotation.ColumnDefine;
import com.wisedu.lowcode4j.common.core.annotation.ModelDefine;
import com.wisedu.lowcode4j.common.core.constant.ApplicationConstant;
import com.wisedu.lowcode4j.common.core.constant.DictCacheType;
import com.wisedu.lowcode4j.common.core.model.BaseBizModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Comment;
import org.sagacity.sqltoy.config.annotation.Translate;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgjcxxlb", description = "教职工基本信息列表")
@Comment("教职工基本信息列表")
@ModelDefine(renderType="table")
//end_dynamic_declare
@Data
public class Jzgjcxxlb extends BaseBizModel {

    private static final long serialVersionUID = 4431474721041959901L;

    //region start_dynamic_column
    @ApiModelProperty(name = "jzgh", value = "教职工号" ,notes = "2024-11-06 11:14:24")
    @Comment("教职工号")
    @Column(value = "jzgh")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String jzgh;

    @ApiModelProperty(name = "bndjxzdcs", value = "本年度教学指导次数" ,notes = "2025-02-19 15:12:59")
    @Comment("本年度教学指导次数")
    @Column(value = "bndjxzdcs")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=8,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private Integer bndjxzdcs;

    @ApiModelProperty(name = "bndxspjjf", value = "本年度学生评教均分" ,notes = "2025-02-19 15:14:17")
    @Comment("本年度学生评教均分")
    @Column(value = "bndxspjjf")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=9,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private Double bndxspjjf;

    @ApiModelProperty(name = "bndjxgzl", value = "本年度教学工作量" ,notes = "2025-02-19 15:12:19")
    @Comment("本年度教学工作量")
    @Column(value = "bndjxgzl")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=7,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private Double bndjxgzl;

    @ApiModelProperty(name = "bndzhpjjf", value = "本年度综合评价均分" ,notes = "2025-02-19 15:14:53")
    @Comment("本年度综合评价均分")
    @Column(value = "bndzhpjjf")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=10,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private Double bndzhpjjf;

    @ApiModelProperty(name = "bndjxcg", value = "本年度教学成果" ,notes = "2025-02-19 15:15:28")
    @Comment("本年度教学成果")
    @Column(value = "bndjxcg")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=11,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private Integer bndjxcg;

    @ApiModelProperty(name = "xm", value = "姓名" ,notes = "2025-02-19 16:23:59")
    @Comment("姓名")
    @Column(value = "xm")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=1,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String xm;

    @ApiModelProperty(name = "xb", value = "性别" ,notes = "2025-02-19 16:24:49")
    @Comment("性别")
    @Column(value = "xb")
    @ColumnDefine(columnDisplay=true,columnDict="XB",columnReadonly=false,orderIndex=2,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String xb;

    @ApiModelProperty(name = "tjnf", value = "统计年份" ,notes = "2024-11-06 11:14:48")
    @Comment("统计年份")
    @Column(value = "tjnf")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=16,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String tjnf;

    @ApiModelProperty(name = "zyjszc", value = "专业技术职称" ,notes = "2025-02-19 16:27:26")
    @Comment("专业技术职称")
    @Column(value = "zyjszc")
    @ColumnDefine(columnDisplay=true,columnDict="ZC",columnReadonly=false,orderIndex=6,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String zyjszc;

    @ApiModelProperty(name = "dwh", value = "单位号" ,notes = "2025-02-19 16:25:38")
    @Comment("单位号")
    @Column(value = "dwh")
    @ColumnDefine(columnDisplay=true,columnDict="ZZJG",columnReadonly=false,orderIndex=3,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String dwh;

    @ApiModelProperty(name = "zgxw", value = "最高学位" ,notes = "2025-02-19 16:29:02")
    @Comment("最高学位")
    @Column(value = "zgxw")
    @ColumnDefine(columnDisplay=true,columnDict="ZGXW",columnReadonly=false,orderIndex=5,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String zgxw;

    @ApiModelProperty(name = "rzzt", value = "任职状态" ,notes = "2025-02-19 16:30:43")
    @Comment("任职状态")
    @Column(value = "rzzt")
    @ColumnDefine(columnDisplay=true,columnDict="rz_status",columnReadonly=false,orderIndex=13,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String rzzt;

    @ApiModelProperty(name = "rxsj", value = "入校时间" ,notes = "2025-02-19 16:31:13")
    @Comment("入校时间")
    @Column(value = "rxsj")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=14,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String rxsj;

    @ApiModelProperty(name = "xymc", value = "学缘" ,notes = "2025-02-19 16:33:20")
    @Comment("学缘")
    @Column(value = "xymc")
    @ColumnDefine(columnDisplay=true,columnDict="XY",columnReadonly=false,orderIndex=15,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String xymc;

    @ApiModelProperty(name = "rjzydm", value = "任教专业代码" ,notes = "2025-02-19 16:34:40")
    @Comment("任教专业代码")
    @Column(value = "rjzydm")
    @ColumnDefine(columnDisplay=true,columnDict="major",columnReadonly=false,orderIndex=4,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String rjzydm;

    @ApiModelProperty(name = "bndrych", value = "荣誉称号" ,notes = "2025-02-19 15:57:34")
    @Comment("荣誉称号")
    @Column(value = "bndrych")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=12,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String bndrych;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "xbName", value = "性别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "xb", cacheType = "XB", split=",")
    @JSONField(name = "xb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String xbName;

    @ApiModelProperty(name = "zyjszcName", value = "专业技术职称名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "zyjszc", cacheType = "ZC", split=",")
    @JSONField(name = "zyjszc"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String zyjszcName;

    @ApiModelProperty(name = "dwhName", value = "单位号名称")
    @Translate(cacheName = "ZZJG", keyField = "dwh", cacheType = "", split=",")
    @JSONField(name = "dwh"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String dwhName;

    @ApiModelProperty(name = "zgxwName", value = "最高学位名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "zgxw", cacheType = "ZGXW", split=",")
    @JSONField(name = "zgxw"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String zgxwName;

    @ApiModelProperty(name = "rzztName", value = "任职状态名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "rzzt", cacheType = "rz_status", split=",")
    @JSONField(name = "rzzt"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String rzztName;

    @ApiModelProperty(name = "xymcName", value = "学缘名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "xymc", cacheType = "XY", split=",")
    @JSONField(name = "xymc"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String xymcName;

    @ApiModelProperty(name = "rjzydmName", value = "任教专业代码名称")
    @Translate(cacheName = "major", keyField = "rjzydm", cacheType = "", split=",")
    @JSONField(name = "rjzydm"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String rjzydmName;

	//endregion end_dynamic_dict_column

}
