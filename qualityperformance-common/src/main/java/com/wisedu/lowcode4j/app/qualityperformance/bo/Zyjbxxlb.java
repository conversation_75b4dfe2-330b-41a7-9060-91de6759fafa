package com.wisedu.lowcode4j.app.qualityperformance.bo;

import com.alibaba.fastjson.annotation.JSONField;
import com.wisedu.lowcode4j.common.core.annotation.ColumnDefine;
import com.wisedu.lowcode4j.common.core.annotation.ModelDefine;
import com.wisedu.lowcode4j.common.core.constant.ApplicationConstant;
import com.wisedu.lowcode4j.common.core.constant.DictCacheType;
import com.wisedu.lowcode4j.common.core.model.BaseBizModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Comment;
import org.sagacity.sqltoy.config.annotation.Translate;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "zyjbxxlb", description = "专业基本信息列表")
@Comment("专业基本信息列表")
@ModelDefine(renderType="table")
//end_dynamic_declare
@Data
public class Zyjbxxlb extends BaseBizModel {

    private static final long serialVersionUID = 4431474721041951765L;

    //region start_dynamic_column
    @ApiModelProperty(name = "tjnf", value = "统计年份" ,notes = "2024-11-11 10:46:32")
    @Comment("统计年份")
    @Column(value = "tjnf", width = 128)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=17,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String tjnf;

    @ApiModelProperty(name = "xnzydm", value = "校内专业代码" ,notes = "2024-11-11 10:46:18")
    @Comment("校内专业代码")
    @Column(value = "xnzydm", width = 128)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String xnzydm;

    @ApiModelProperty(name = "xnzymc", value = "校内专业名称" ,notes = "2025-02-20 10:59:43")
    @Comment("校内专业名称")
    @Column(value = "xnzymc")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=1,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String xnzymc;

    @ApiModelProperty(name = "zydm", value = "专业代码" ,notes = "2024-11-11 10:46:18")
    @Comment("专业代码")
    @Column(value = "zydm", width = 128)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=2,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zydm;

    @ApiModelProperty(name = "zymc", value = "专业名称" ,notes = "2025-02-20 11:00:11")
    @Comment("专业名称")
    @Column(value = "zymc")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=3,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String zymc;

    @ApiModelProperty(name = "ssdwh", value = "所属单位号" ,notes = "2025-02-20 11:00:50")
    @Comment("所属单位号")
    @Column(value = "ssdwh")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=4,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String ssdwh;

    @ApiModelProperty(name = "ssdwmc", value = "所属单位名称" ,notes = "2025-02-20 11:01:02")
    @Comment("所属单位名称")
    @Column(value = "ssdwmc")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=5,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String ssdwmc;

    @ApiModelProperty(name = "szzynf", value = "专业设置年份" ,notes = "2025-02-20 11:01:29")
    @Comment("专业设置年份")
    @Column(value = "szzynf")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=6,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String szzynf;

    @ApiModelProperty(name = "xz", value = "学制" ,notes = "2025-02-20 11:01:57")
    @Comment("学制")
    @Column(value = "xz")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=7,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private Double xz;

    @ApiModelProperty(name = "yxxynx", value = "允许修业年限" ,notes = "2025-02-20 11:02:35")
    @Comment("允许修业年限")
    @Column(value = "yxxynx")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=8,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private Double yxxynx;

    @ApiModelProperty(name = "zylx", value = "专业类型" ,notes = "2025-02-20 11:09:11")
    @Comment("专业类型")
    @Column(value = "zylx")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=15,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String zylx;

    @ApiModelProperty(name = "syxwml", value = "授予学位门类" ,notes = "2025-02-20 11:02:49")
    @Comment("授予学位门类")
    @Column(value = "syxwml")
    @ColumnDefine(columnDisplay=true,columnDict="XKML",columnReadonly=false,orderIndex=9,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select")
    private String syxwml;

    @ApiModelProperty(name = "zszt", value = "招生状态" ,notes = "2025-02-20 11:03:09")
    @Comment("招生状态")
    @Column(value = "zszt")
    @ColumnDefine(columnDisplay=true,columnDict="ZSZT",columnReadonly=false,orderIndex=16,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select")
    private String zszt;

    @ApiModelProperty(name = "bndssb", value = "生师比" ,notes = "2025-02-20 13:16:52")
    @Comment("生师比")
    @Column(value = "bndssb")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=10,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private Double bndssb;

    @ApiModelProperty(name = "bndzrjssl", value = "专任教师数量" ,notes = "2025-02-20 13:17:25")
    @Comment("专任教师数量")
    @Column(value = "bndzrjssl")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=11,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private Integer bndzrjssl;

    @ApiModelProperty(name = "bndzxssl", value = "在校生数量" ,notes = "2025-02-20 13:18:06")
    @Comment("在校生数量")
    @Column(value = "bndzxssl")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=12,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private Integer bndzxssl;

    @ApiModelProperty(name = "bndzykcsl", value = "专业课程数量" ,notes = "2025-02-20 13:18:37")
    @Comment("专业课程数量")
    @Column(value = "bndzykcsl")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=13,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private Integer bndzykcsl;

    @ApiModelProperty(name = "bndjdsl", value = "基地数量" ,notes = "2025-02-20 13:19:03")
    @Comment("基地数量")
    @Column(value = "bndjdsl")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=14,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private Integer bndjdsl;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "syxwmlName", value = "授予学位门类名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "syxwml", cacheType = "XKML", split=",")
    @JSONField(name = "syxwml"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String syxwmlName;

    @ApiModelProperty(name = "zsztName", value = "招生状态名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "zszt", cacheType = "ZSZT", split=",")
    @JSONField(name = "zszt"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String zsztName;

	//endregion end_dynamic_dict_column

}
