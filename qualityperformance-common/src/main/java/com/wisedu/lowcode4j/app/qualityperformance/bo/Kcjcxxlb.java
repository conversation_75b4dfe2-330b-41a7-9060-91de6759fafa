package com.wisedu.lowcode4j.app.qualityperformance.bo;

import com.alibaba.fastjson.annotation.JSONField;
import com.wisedu.lowcode4j.common.core.annotation.ColumnDefine;
import com.wisedu.lowcode4j.common.core.annotation.ModelDefine;
import com.wisedu.lowcode4j.common.core.constant.ApplicationConstant;
import com.wisedu.lowcode4j.common.core.constant.DictCacheType;
import com.wisedu.lowcode4j.common.core.model.BaseBizModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Comment;
import org.sagacity.sqltoy.config.annotation.Translate;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "kcjcxxlb", description = "课程基础信息列表")
@Comment("课程基础信息列表")
@ModelDefine(renderType="table")
//end_dynamic_declare
@Data
public class Kcjcxxlb extends BaseBizModel {

    private static final long serialVersionUID = 4431474721041957980L;

    //region start_dynamic_column
    @ApiModelProperty(name = "kch", value = "课程号" ,notes = "2024-11-08 16:18:54")
    @Comment("课程号")
    @Column(value = "kch")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String kch;

    @ApiModelProperty(name = "tjnf", value = "统计年份" ,notes = "2024-11-08 16:19:10")
    @Comment("统计年份")
    @Column(value = "tjnf")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=14,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String tjnf;

    @ApiModelProperty(name = "kcm", value = "课程名" ,notes = "2025-02-20 14:44:33")
    @Comment("课程名")
    @Column(value = "kcm")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=1,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String kcm;

    @ApiModelProperty(name = "ywkcm", value = "英文课程名" ,notes = "2025-02-20 14:44:51")
    @Comment("英文课程名")
    @Column(value = "ywkcm")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=2,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String ywkcm;

    @ApiModelProperty(name = "kcjb", value = "课程级别" ,notes = "2025-02-20 14:45:38")
    @Comment("课程级别")
    @Column(value = "kcjb")
    @ColumnDefine(columnDisplay=true,columnDict="XMJB",columnReadonly=false,orderIndex=3,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select")
    private String kcjb;

    @ApiModelProperty(name = "jxfsdm", value = "教学方式" ,notes = "2025-02-20 14:48:05")
    @Comment("教学方式")
    @Column(value = "jxfsdm")
    @ColumnDefine(columnDisplay=true,columnDict="JXFS",columnReadonly=false,orderIndex=4,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select")
    private String jxfsdm;

    @ApiModelProperty(name = "kkdw", value = "开课单位" ,notes = "2025-02-20 14:48:41")
    @Comment("开课单位")
    @Column(value = "kkdw")
    @ColumnDefine(columnDisplay=true,columnDict="ZZJG",columnReadonly=false,orderIndex=5,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select")
    private String kkdw;

    @ApiModelProperty(name = "sfqywkc", value = "是否全英文授课" ,notes = "2025-02-20 14:49:41")
    @Comment("是否全英文授课")
    @Column(value = "sfqywkc")
    @ColumnDefine(columnDisplay=true,columnDict="SFBZ",columnReadonly=false,orderIndex=10,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select")
    private String sfqywkc;

    @ApiModelProperty(name = "kcbq", value = "课程标签" ,notes = "2025-02-20 14:50:03")
    @Comment("课程标签")
    @Column(value = "kcbq")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=11,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String kcbq;

    @ApiModelProperty(name = "kcfzr", value = "课程负责人" ,notes = "2025-02-20 14:50:23")
    @Comment("课程负责人")
    @Column(value = "kcfzr")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=12,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String kcfzr;

    @ApiModelProperty(name = "sfszkc", value = "是否思政课程" ,notes = "2025-02-20 14:51:04")
    @Comment("是否思政课程")
    @Column(value = "sfszkc")
    @ColumnDefine(columnDisplay=true,columnDict="SFBZ",columnReadonly=false,orderIndex=13,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select")
    private String sfszkc;

    @ApiModelProperty(name = "bndkkmcs", value = "本年度开课门次数" ,notes = "2025-02-20 14:51:45")
    @Comment("本年度开课门次数")
    @Column(value = "bndkkmcs")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=6,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private Integer bndkkmcs;

    @ApiModelProperty(name = "bndskrs", value = "本年度上课人数" ,notes = "2025-02-20 14:52:15")
    @Comment("本年度上课人数")
    @Column(value = "bndskrs")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=7,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private Integer bndskrs;

    @ApiModelProperty(name = "bndkcpjcj", value = "本年度课程平均成绩" ,notes = "2025-02-20 14:53:29")
    @Comment("本年度课程平均成绩")
    @Column(value = "bndkcpjcj")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=8,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private Double bndkcpjcj;

    @ApiModelProperty(name = "bndzysl", value = "本年度资源数量" ,notes = "2025-02-20 14:53:58")
    @Comment("本年度资源数量")
    @Column(value = "bndzysl")
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=9,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private Integer bndzysl;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "kcjbName", value = "课程级别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "kcjb", cacheType = "XMJB", split=",")
    @JSONField(name = "kcjb"+ ApplicationConstant.DICT_NAME_SUFFIX)
    private String kcjbName;

    @ApiModelProperty(name = "jxfsdmName", value = "教学方式名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "jxfsdm", cacheType = "JXFS", split=",")
    @JSONField(name = "jxfsdm"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String jxfsdmName;

    @ApiModelProperty(name = "kkdwName", value = "开课单位名称")
    @Translate(cacheName = "ZZJG", keyField = "kkdw", cacheType = "", split=",")
    @JSONField(name = "kkdw"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String kkdwName;

    @ApiModelProperty(name = "sfqywkcName", value = "是否全英文授课名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "sfqywkc", cacheType = "SFBZ", split=",")
    @JSONField(name = "sfqywkc"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String sfqywkcName;

    @ApiModelProperty(name = "sfszkcName", value = "是否思政课程名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "sfszkc", cacheType = "SFBZ", split=",")
    @JSONField(name = "sfszkc"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String sfszkcName;

	//endregion end_dynamic_dict_column

}
