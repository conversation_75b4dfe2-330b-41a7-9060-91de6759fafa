<?xml version="1.0" encoding="utf-8"?>
<sqltoy xmlns="http://www.sagframe.com/schema/sqltoy"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.sagframe.com/schema/sqltoy http://www.sagframe.com/schema/sqltoy/sqltoy.xsd">

    <sql id="page_zyjbxxlb_zyjbxxlb" masterTableAlias="INS_ZYJBQK">
        <value>
            <![CDATA[
                select * from (
                    select
                        zyjbqk.xnzydm,
                        zyjbqk.xnzymc ,
                        zyjbqk.zydm,
                        zyjbqk.zymc,
                        zyjbqk.ssdwh,
                        zyjbqk.ssdwmc,
                        zyjbqk.szzynf,
                        zyjbqk.xz,
                        zyjbqk.yxxynx,
                        zyjbqk.syxwml,
                        nvl(zyssb.ssb, 0) as bndssb,
                        nvl(zrjssl.zrjss, 0) as bndzrjssl,
                        nvl(zxssl.xssl, 0) as bndzxssl,
                        nvl(zykcsl.kcsl, 0) as bndzykcsl,
                        nvl(zyjdsl.jdsl, 0) as bndjdsl,
                        nvl(yszylx.zylx, '暂无') as zylx,
                        zyjbqk.zszt,
                        vzyjbqk.tjnf
                    from INS_ZYJBQK zyjbqk
                    left join V_INS_ZYJBQK vzyjbqk on zyjbqk.xnzydm = vzyjbqk.xnzydm and zyjbqk.zydm = vzyjbqk.zydm
                    -- 本年度生师比
                    left join INS_SI_MAJ_0001 zyssb on zyjbqk.xnzydm = zyssb.zydm and vzyjbqk.tjnf = zyssb.tjnf
                    -- 本年度专任教师数量
                    left join INS_SI_MAJ_0049 zrjssl on zyjbqk.xnzydm = zrjssl.zydm and vzyjbqk.tjnf = zrjssl.tjnf
                    -- 本年度在校生数量
                    left join (
                        select tjnf, xnzydldm as zydm, count(xh) as xssl
                        from INS_BKSJBQK
                        where xjzt like '%在校%'
                        group by tjnf, xnzydldm, xnzydlmc
                    ) zxssl on zyjbqk.xnzydm = zxssl.zydm and vzyjbqk.tjnf = zxssl.tjnf
                    -- 本年度专业课程数量
                    left join INS_SI_MAJ_0022 zykcsl on zyjbqk.xnzydm = zykcsl.zydm and vzyjbqk.tjnf = substr(zykcsl.xn, 6, 4)
                    -- 本年度基地数量
                    left join INS_SI_MAJ_0026 zyjdsl on zyjbqk.xnzydm = zyjdsl.zydm and vzyjbqk.tjnf = zyjdsl.tjnf
                    -- 专业类型
                    left join (
                        select xnzydm, tjnf, listagg(zylx, ',') within group (order by zylx) as zylx
                        from (select distinct xnzydm, tjnf, zylx from INS_YSZYQK)
                        group by xnzydm, tjnf
                    ) yszylx on zyjbqk.xnzydm = yszylx.xnzydm and vzyjbqk.tjnf = yszylx.tjnf
                ) INS_ZYJBQK
                WHERE 1 = 1
                #datascope#
                #[ AND @blank(:querySetting) (${querySetting})]
                ORDER BY #[@blank(:orderBy) @value(:orderBy), ] tjnf desc, ssdwh, xnzydm
            ]]>
        </value>
    </sql>
</sqltoy>
