<?xml version="1.0" encoding="utf-8"?>
<sqltoy xmlns="http://www.sagframe.com/schema/sqltoy"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.sagframe.com/schema/sqltoy http://www.sagframe.com/schema/sqltoy/sqltoy.xsd">

    <sql id="page_bksxxlb_bksxxlb" masterTableAlias="INS_BKSJBQK">
        <value>
            <![CDATA[
                select * from (
                    select
                        bksjbqk.xh,
                        bksjbqk.xsxm ,
                        bksjbqk.xb,
                        bksjbqk.nj,
                        bksjbqk.xy,
                        bksjbqk.xnzydldm,
                        bksjbqk.xnzydlmc,
                        bksjbqk.xzb,
                        nvl(xsgpa.jd, 0) as bndgpa,
                        nvl(sjsxcs.sumcs, 0) as bndsjsxcs,
                        nvl(xscgsl.sumcs, 0) as bndxscgsl,
                        nvl(xsrych.rychm, '暂无称号') as bndrych,
                        bksjbqk.xjzt,
                        bksjbqk.tjnf
                    from INS_BKSJBQK bksjbqk
                    -- 本年度GPA
                    left join INS_SI_UND_0020 xsgpa on bksjbqk.xh = xsgpa.xh and bksjbqk.tjnf = substr(xsgpa.xn, 6, 4)
                    -- 本年度实践实习次数
                    left join (
                        with xkjsb as (
                            select tjnf, xh, count(*) jsjlcs
                            from INS_XSHSJJYSGLJSJLQK
                            group by tjnf, xh
                        ),
                        dcxmb as (
                            select tjnf, xh, count(*) cxcycs
                            from INS_XSCJDXSCXCYXLJHQK
                            group by tjnf, xh
                        ),
                        sxhd as (
                            select substr (b.xnbm, 6, 4) tjnf, xh, count(*) sxcs
                            from ABD_UND_SXJBXX a
                            left join V_INS_XNXQ b on a.xnxq = b.xnxqbm
                            group by b.xnbm, xh
                        ),
                        tybsb as (
                            select tjnf, xh, count(*) tybsjlcs
                            from INS_TYLXSHZYBSJLQK
                            group by tjnf, xh
                        ),
                        ysbsb as (
                            select tjnf, xh, count(*) ysbsjlcs
                            from INS_YSLXSHZYBSJLQK
                            group by tjnf, xh
                        )
                        select a.tjnf, a.xh, a.xnzydldm zydm,
                            nvl (b.jsjlcs, 0) + nvl (c.cxcycs, 0) + nvl (d.sxcs, 0) + nvl (e.tybsjlcs, 0) + nvl (f.ysbsjlcs, 0) sumcs
                        from INS_BKSJBQK a
                        left join xkjsb b on a.tjnf = b.tjnf and a.xh = b.xh
                        left join dcxmb c on a.tjnf = c.tjnf and a.xh = c.xh
                        left join sxhd d on a.tjnf = d.tjnf and a.xh = d.xh
                        left join tybsb e on a.tjnf = e.tjnf and a.xh = e.xh
                        left join ysbsb f on a.tjnf = f.tjnf and a.xh = f.xh
                    ) sjsxcs on bksjbqk.xh = sjsxcs.xh and bksjbqk.tjnf = sjsxcs.tjnf
                    -- 本年度学术成果数量
                    left join (
                        with lwb as (
                            select tjnf, xh, count(*) fblwcs
                            from INS_XSFBXSLWQK
                            group by tjnf, xh
                        ),
                        zlb as (
                            select tjnf, xh, count(*) zlcs
                            from INS_XSZLSQQK
                            group by tjnf, xh
                        ),
                        czbyb as (
                            select tjnf, xh, count(*) czzbcs
                            from INS_XSCZBYDDBXZP
                            group by tjnf, xh
                        )
                        select a.tjnf, a.xh, a.xnzydldm zydm,
                            nvl (b.fblwcs, 0) + nvl (c.zlcs, 0) + nvl (d.czzbcs, 0) sumcs
                        from INS_BKSJBQK a
                        left join lwb b on a.tjnf = b.tjnf and a.xh = b.xh
                        left join zlb c on a.tjnf = c.tjnf and a.xh = c.xh
                        left join czbyb d on a.tjnf = d.tjnf and a.xh = d.xh
                    ) xscgsl on bksjbqk.xh = xscgsl.xh and bksjbqk.tjnf = xscgsl.tjnf
                    -- 荣誉称号
                    left join (
                        select xh, tjnf, listagg(rychm, ',') within group (order by rychm) as rychm
                        from (select distinct xh, tjnf, rychm from ABD_UND_XSGRRY)
                        group by xh, tjnf
                    ) xsrych on bksjbqk.xh = xsrych.xh and bksjbqk.tjnf = xsrych.tjnf
                ) INS_BKSJBQK
                WHERE 1 = 1
                #datascope#
                #[ AND @blank(:querySetting) (${querySetting})]
                ORDER BY #[@blank(:orderBy) @value(:orderBy), ] tjnf desc, nj desc, xy, xnzydldm, xzb, xh
            ]]>
        </value>
    </sql>
</sqltoy>
