<?xml version="1.0" encoding="utf-8"?>
<sqltoy xmlns="http://www.sagframe.com/schema/sqltoy"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.sagframe.com/schema/sqltoy http://www.sagframe.com/schema/sqltoy/sqltoy.xsd">

    <sql id="page_kcjcxxlb_kcjcxxlb" masterTableAlias="ABD_COU_BKKCJBXX">
        <value>
            <![CDATA[
                select * from (
                    select
                        bkkcjbxx.kch,
                        bkkcjbxx.kcm ,
                        bkkcjbxx.ywkcm,
                        bkkcjbxx.kcjb,
                        bkkcjbxx.jxfsdm,
                        bkkcjbxx.kkdw,
                        nvl(kckkmcs.kkmcs, 0) as bndkkmcs,
                        nvl(kcskrs.skrs, 0) as bndskrs,
                        nvl(kcpjcj.cj, 0) as bndkcpjcj,
                        nvl(kczysl.sl, 0) as bnd<PERSON><PERSON>l,
                        bkkcjbxx.sfqywkc,
                        bkkcjbxx.kcbq,
                        bkkcjbxx.kcfzr,
                        bkkcjbxx.sfszkc,
                        bkkcjbxx.tjnf
                    from ABD_COU_BKKCJBXX bkkcjbxx
                    -- 本年度开课门次数
                    left join INS_SI_COU_0007 kckkmcs on bkkcjbxx.kch = kckkmcs.kcdm and bkkcjbxx.tjnf = substr(kckkmcs.xn, 6, 4)
                    -- 本年度上课人数
                    left join INS_SI_COU_0010 kcskrs on bkkcjbxx.kch = kcskrs.kcdm and bkkcjbxx.tjnf = substr(kcskrs.xn, 6, 4)
                    -- 本年度课程平均成绩
                    left join INS_SI_COU_0011 kcpjcj on bkkcjbxx.kch = kcpjcj.kcdm and bkkcjbxx.tjnf = substr(kcpjcj.xn, 6, 4)
                    -- 本年度资源数量
                    left join INS_SI_COU_0013 kczysl on bkkcjbxx.kch = kczysl.kcdm and bkkcjbxx.tjnf = kczysl.tjnf
                ) ABD_COU_BKKCJBXX
                WHERE 1 = 1
                #datascope#
                #[ AND @blank(:querySetting) (${querySetting})]
                ORDER BY #[@blank(:orderBy) @value(:orderBy), ] tjnf desc, kkdw, kch
            ]]>
        </value>
    </sql>
</sqltoy>
