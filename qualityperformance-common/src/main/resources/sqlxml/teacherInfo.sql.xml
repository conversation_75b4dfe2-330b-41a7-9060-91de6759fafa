<?xml version="1.0" encoding="utf-8"?>
<sqltoy xmlns="http://www.sagframe.com/schema/sqltoy"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.sagframe.com/schema/sqltoy http://www.sagframe.com/schema/sqltoy/sqltoy.xsd">

    <sql id="page_jzgjcxxlb_jzgjcxxlb" masterTableAlias="INS_JZGJBXX">
        <value>
            <![CDATA[
                select * from (
                    select
                        jzgjbxx.jzgh,
                        jzgjbxx.xm,
                        jzgjbxx.xb,
                        jzgjbxx.dwh,
                        jzgqtxx.rjzydm,
                        jzgjbxx.zgxw,
                        jzgjbxx.zyjszc,
                        nvl(jxgzl.gzl, 0) as bndjxgzl,
                        nvl(jxzdcs.zdcs, 0) as bndjxzdcs,
                        nvl(xspjjf.fs, 0) as bndxspjjf,
                        nvl(zhpjjf.fs, 0) as bndzhpjjf,
                        nvl(jxcg.cgsl, 0) as bndjxcg,
                        nvl(jsrych.rychm, '暂无称号') as bndrych,
                        jzgjbxx.rzzt,
                        jzgjbxx.rxsj,
                        jzgjbxx.xymc,
                        jzgjbxx.tjnf
                    from INS_JZGJBXX jzgjbxx
                    left join INS_JZGQTXX jzgqtxx
                        on jzgjbxx.jzgh = jzgqtxx.gh and jzgjbxx.tjnf = jzgqtxx.tjnf
                    -- 本年度教学工作量
                    left join (
                        select a.zgh, b.tjnf, sum(a.gzl) as gzl
                        from ABD_TEA_JZGJXGZL a
                        left join V_INS_XNXQ b on a.xnxq = b.xnxqbm
                        where a.xnxq is not null
                        group by a.zgh, b.tjnf
                    ) jxgzl on jzgjbxx.jzgh = jxgzl.zgh and jzgjbxx.tjnf = jxgzl.tjnf
                    -- 本年度教学指导次数
                    left join INS_SI_TEA_0061 jxzdcs on jzgjbxx.jzgh = jxzdcs.zgh and jzgjbxx.tjnf = jxzdcs.tjnf
                    -- 本年度学生评教均分
                    left join INS_SI_TEA_0063 xspjjf on jzgjbxx.jzgh = xspjjf.zgh and jzgjbxx.tjnf = substr(xspjjf.xn, 6, 4)
                    -- 本年度综合评价均分
                    left join INS_SI_TEA_0064 zhpjjf on jzgjbxx.jzgh = zhpjjf.zgh and jzgjbxx.tjnf = substr(zhpjjf.xn, 6, 4)
                    -- 本年度教学成果
                    left join INS_SI_TEA_0062 jxcg on jzgjbxx.jzgh = jxcg.zgh and jzgjbxx.tjnf = jxcg.tjnf
                    -- 荣誉称号
                    left join (
                        select gh, tjnf, listagg(rychm, ',') within group (order by rychm) as rychm
                        from (select distinct gh, tjnf, rychm from ABD_TEA_JSGRRY)
                        group by gh, tjnf
                    ) jsrych on jzgjbxx.jzgh = jsrych.gh and jzgjbxx.tjnf = jsrych.tjnf
                ) INS_JZGJBXX
                WHERE 1 = 1
                #datascope#
                #[ AND @blank(:querySetting) (${querySetting})]
                ORDER BY #[@blank(:orderBy) @value(:orderBy), ] tjnf desc, dwh, jzgh
            ]]>
        </value>
    </sql>
</sqltoy>
