<?xml version="1.0" encoding="UTF-8"?>
<app id="qualityperformance" name="质量表现" groupId="qualityperformancegroup">
    <!--菜单（目录）-->
    <menus id="qualityperformance-ztbx" formRouterName="ztbx" name="主体表现" showOrder="1">
        <!--菜单（页面）-->
        <menu bindType="default" formRouterName="teacherlist" id="qualityperformance-teacherlist"
              menuPattern="pc"
              name="教职工信息"
              showOrder="1">
            <routerParams>{ "appCode": "qualityperformance", "pageCode": "teacherlist"}</routerParams>
            <!-- 权限字-表单 -->
            <permCode name="教职工信息" permCode="formteacherlist" permCodeId="qualityperformance-teacherlist"
                      permCodeType="0"/>
            <!-- 权限字-片段 -->
            <permCode name="教职工信息" permCode="formteacherlist:fragmentteacherlist"
                      permCodeId="fragmentqualityperformance-teacherlist"
                      permCodeType="1">
                <perm name="查询级联表格平铺接口" url="/eda/qualityperformance/find/jzgjcxxlb/jzgjcxxlb"/>
                <perm name="页面数据展示" url="/eda/qualityperformance/page/jzgjcxxlb"/>
                <perm name="页面数据导出" url="/eda/qualityperformance/export/jzgjcxxlb"/>
                <perm name="页面导出文件下载" url="/admin/file/qualityperformance/download"/>
            </permCode>
            <btn id="qualityperformance-teacherlist-show" menuPattern="pc" name="显示">
                <permCode name="显示" permCode="formteacherlist:fragmentteacherlist:show"
                          permCodeId="qualityperformance-teacherlist-show"/>
            </btn>
        </menu>

        <!--菜单（页面）-->
        <menu bindType="default" formRouterName="studentlist" id="qualityperformance-studentlist"
              menuPattern="pc"
              name="学生信息"
              showOrder="2">
            <routerParams>{ "appCode": "qualityperformance", "pageCode": "studentlist"}</routerParams>
            <!-- 权限字-表单 -->
            <permCode name="学生信息" permCode="formstudentlist" permCodeId="qualityperformance-studentlist"
                      permCodeType="0"/>
            <!-- 权限字-片段 -->
            <permCode name="学生信息" permCode="formstudentlist:fragmentstudentlist"
                      permCodeId="fragmentqualityperformance-studentlist"
                      permCodeType="1">
                <perm name="查询级联表格平铺接口" url="/eda/qualityperformance/find/bksxxlb/bksxxlb"/>
                <perm name="页面数据展示" url="/eda/qualityperformance/page/bksxxlb"/>
                <perm name="页面数据导出" url="/eda/qualityperformance/export/bksxxlb"/>
                <perm name="页面导出文件下载" url="/admin/file/qualityperformance/download"/>
            </permCode>
            <btn id="qualityperformance-studentlist-show" menuPattern="pc" name="显示">
                <permCode name="显示" permCode="formstudentlist:fragmentstudentlist:show"
                          permCodeId="qualityperformance-studentlist-show"/>
            </btn>
        </menu>

        <!--菜单（页面）-->
        <menu bindType="default" formRouterName="courselist" id="qualityperformance-courselist"
              menuPattern="pc"
              name="课程信息"
              showOrder="3">
            <routerParams>{ "appCode": "qualityperformance", "pageCode": "courselist"}</routerParams>
            <!-- 权限字-表单 -->
            <permCode name="课程信息" permCode="formcourselist" permCodeId="qualityperformance-courselist"
                      permCodeType="0"/>
            <!-- 权限字-片段 -->
            <permCode name="课程信息" permCode="formcourselist:fragmentcourselist"
                      permCodeId="fragmentqualityperformance-courselist"
                      permCodeType="1">
                <perm name="查询级联表格平铺接口" url="/eda/qualityperformance/find/kcjcxxlb/kcjcxxlb"/>
                <perm name="页面数据展示" url="/eda/qualityperformance/page/kcjcxxlb"/>
                <perm name="页面数据导出" url="/eda/qualityperformance/export/kcjcxxlb"/>
                <perm name="页面导出文件下载" url="/admin/file/qualityperformance/download"/>
            </permCode>
            <btn id="qualityperformance-courselist-show" menuPattern="pc" name="显示">
                <permCode name="显示" permCode="formcourselist:fragmentcourselist:show"
                          permCodeId="qualityperformance-courselist-show"/>
            </btn>
        </menu>

        <!--菜单（页面）-->
        <menu bindType="default" formRouterName="majorlist" id="qualityperformance-majorlist"
              menuPattern="pc"
              name="专业信息"
              showOrder="4">
            <routerParams>{ "appCode": "qualityperformance", "pageCode": "majorlist"}</routerParams>
            <!-- 权限字-表单 -->
            <permCode name="专业信息" permCode="formmajorlist" permCodeId="qualityperformance-majorlist"
                      permCodeType="0"/>
            <!-- 权限字-片段 -->
            <permCode name="专业信息" permCode="formmajorlist:fragmentmajorlist"
                      permCodeId="fragmentqualityperformance-majorlist"
                      permCodeType="1">
                <perm name="查询级联表格平铺接口" url="/eda/qualityperformance/find/zyjbxxlb/zyjbxxlb"/>
                <perm name="页面数据展示" url="/eda/qualityperformance/page/zyjbxxlb"/>
                <perm name="页面数据导出" url="/eda/qualityperformance/export/zyjbxxlb"/>
                <perm name="页面导出文件下载" url="/admin/file/qualityperformance/download"/>
            </permCode>
            <btn id="qualityperformance-majorlist-show" menuPattern="pc" name="显示">
                <permCode name="显示" permCode="formmajorlist:fragmentmajorlist:show"
                          permCodeId="qualityperformance-majorlist-show"/>
            </btn>
        </menu>

        <!--菜单（页面）-->
        <menu bindType="default" formRouterName="jshx" id="qualityperformance-jshx"
              menuPattern="pc"
              name="教师表现"
              showOrder="11"
              menuHidden="1">
            <routerParams>{ "appCode": "qualityperformance", "pageCode": "jshx"}</routerParams>
            <!-- 权限字-表单 -->
            <permCode name="教师表现" permCode="formjshx" permCodeId="qualityperformance-jshx"
                      permCodeType="0"/>
            <!-- 权限字-片段 -->
            <permCode name="教师表现" permCode="formjshx:fragmentjshx"
                      permCodeId="fragmentqualityperformance-jshx"
                      permCodeType="1">
                <perm name="帆软代理服务接口" url="/toolpub/proxy/**"/>
                <perm name="获取res静态文件" url="/staticFile/getStaticFile"/>
                <perm name="刷选框下拉接口" url="/plugins/qualityperformance/select/**"/>
            </permCode>
            <btn id="qualityperformance-jshx-show" menuPattern="pc" name="显示">
                <permCode name="显示" permCode="formjshx:fragmentjshx:show"
                          permCodeId="qualityperformance-jshx-show"/>
            </btn>
        </menu>

        <!--菜单（页面）-->
        <menu bindType="default" formRouterName="zyhx" id="qualityperformance-zyhx"
              menuPattern="pc"
              name="专业表现"
              showOrder="12"
              menuHidden="1">
            <routerParams>{ "appCode": "qualityperformance", "pageCode": "zyhx"}</routerParams>
            <!-- 权限字-表单 -->
            <permCode name="专业表现" permCode="formzyhx" permCodeId="qualityperformance-zyhx"
                      permCodeType="0"/>
            <!-- 权限字-片段 -->
            <permCode name="专业表现" permCode="formzyhx:fragmentzyhx"
                      permCodeId="fragmentqualityperformance-zyhx"
                      permCodeType="1">
                <perm name="帆软代理服务接口" url="/toolpub/proxy/**"/>
                <perm name="获取res静态文件" url="/staticFile/getStaticFile"/>
                <perm name="刷选框下拉接口" url="/plugins/qualityperformance/select/**"/>
            </permCode>
            <btn id="qualityperformance-zyhx-show" menuPattern="pc" name="显示">
                <permCode name="显示" permCode="formzyhx:fragmentzyhx:show"
                          permCodeId="qualityperformance-zyhx-show"/>
            </btn>
        </menu>

        <!--菜单（页面）-->
        <menu bindType="default" formRouterName="kchx" id="qualityperformance-kchx"
              menuPattern="pc"
              name="课程表现"
              showOrder="13"
              menuHidden="1">
            <routerParams>{ "appCode": "qualityperformance", "pageCode": "kchx"}</routerParams>
            <!-- 权限字-表单 -->
            <permCode name="课程表现" permCode="formkchx" permCodeId="qualityperformance-kchx"
                      permCodeType="0"/>
            <!-- 权限字-片段 -->
            <permCode name="课程表现" permCode="formkchx:fragmentkchx"
                      permCodeId="fragmentqualityperformance-kchx"
                      permCodeType="1">
                <perm name="帆软代理服务接口" url="/toolpub/proxy/**"/>
                <perm name="获取res静态文件" url="/staticFile/getStaticFile"/>
                <perm name="刷选框下拉接口" url="/plugins/qualityperformance/select/**"/>
            </permCode>
            <btn id="qualityperformance-kchx-show" menuPattern="pc" name="显示">
                <permCode name="显示" permCode="formkchx:fragmentkchx:show"
                          permCodeId="qualityperformance-kchx-show"/>
            </btn>
        </menu>

        <!--菜单（页面）-->
        <menu bindType="default" formRouterName="xshx" id="qualityperformance-xshx"
              menuPattern="pc"
              name="学生表现"
              showOrder="14"
              menuHidden="1">
            <routerParams>{ "appCode": "qualityperformance", "pageCode": "xshx"}</routerParams>
            <!-- 权限字-表单 -->
            <permCode name="学生表现" permCode="formxshx" permCodeId="qualityperformance-xshx"
                      permCodeType="0"/>
            <!-- 权限字-片段 -->
            <permCode name="学生表现" permCode="formxshx:fragmentxshx"
                      permCodeId="fragmentqualityperformance-xshx"
                      permCodeType="1">
                <perm name="帆软代理服务接口" url="/toolpub/proxy/**"/>
                <perm name="获取res静态文件" url="/staticFile/getStaticFile"/>
                <perm name="刷选框下拉接口" url="/plugins/qualityperformance/select/**"/>
            </permCode>
            <btn id="qualityperformance-xshx-show" menuPattern="pc" name="显示">
                <permCode name="显示" permCode="formxshx:fragmentxshx:show"
                          permCodeId="qualityperformance-xshx-show"/>
            </btn>
        </menu>
    </menus>
</app>