define(function (require) {
  return {
    /**
     * 监听指定对象的值发生变化，然后执行特定方法
     */
    pageWatch: {
      'globalVars.name': function (newVal, oldVal) {
        //console.log('值改变',newVal,oldVal)
      }
    },
    /**
     * 监听指定对象的值发生变化，然后执行特定方法
     */
    pageComputed: {
      getNewName: function () {
        return this.globalVars.name + '新的'
      }
    },
    var1: "变量",//响应式变量，该变量不能加到组件参数内,但是写到组件模板里
    /**
     * 响应式变量，当值变化时可以影响所有绑定的值,建议把需要绑定到组件参数里的变量申明到这里面
     */
    globalVars: {
      name: '一个变量', //响应式变量name,组件内使用时{{globalVars.name}}
      dicCaches: {},
      // url搜索参数缓存，目前重置搜索条件时使用
      urlParamCaches: {},

      // ********************配置项********************
      // url 传入的参数无需进行 name -> code 转换的字段
      urlParamNoNeedName2CodeFileds: [

      ],
      // 搜索时的参数无需进行 code -> name 转换的字段
      searchParamNoNeedCode2nameFileds: [
        'xy'
      ]
      // *********************************************
    },
    /**
     * 页面被重新激活时调用
     */
    pageActivated: function () {
      window.addEventListener('message', this.setCommonFilterConditions, false);
    },
    /**
     * 页面失去激活被缓存时调用
     */
    pageDeactivated: function () {
      window.removeEventListener('message', this.setCommonFilterConditions, false);
    },
    /**
     * 固定方法，页面js初始化完成后调用,当前能修改js变量，修改组件初始化属性或者设置组件默认值
     */
    pageCreated: function () {
      window.addEventListener('message', this.setCommonFilterConditions, false);
    },
    setCommonFilterConditions: function (e) {
      if (e.origin === 'http://127.0.0.1:8002') {
        /**判断消息来自的域，暂时用不到 */
      }
      if (e.data.type == 'commonFilter') {
        /*这里可以写业务逻辑*/
        sessionStorage.setItem('pluginCommonFilterCondition', JSON.stringify(e.data.valueArr));
        this.setCacheQueryConditions();
        setTimeout(() => {
          this.$page('adv-search_utx4qjkq').doSearch();
        }, 0);
      }
    },
    // 设置缓存中的查询条件
    setCacheQueryConditions: function () {
      /*默认都加载公共筛选*/
      let dataStr = sessionStorage.getItem("pluginCommonFilterCondition");
      let sessionCommonFilterConditionData = dataStr ? JSON.parse(dataStr) : [];
      sessionCommonFilterConditionData.forEach((item, index) => {
        if (item.valueKey && item.valueKey === 'tjnf' && item.value) {
          this.$setModelVals('adv-search_utx4qjkq', {
            ['tjnf']: item.value
          });
        }
      });
    },
    /**
     * 固定方法，页面准备完成后调用，当前可以操作组件属性，调用未隐藏组件实例方法
     */
    pageReady: function () {
      //console.log('页面准备完成后调用')
    },
    /**
     * 固定方法，页面销毁前调用
     */
    pageDestroy: function () {
      window.removeEventListener('message', this.setCommonFilterConditions, false);
    },

    /**
     * 描述：mounted
     * @param{event}  内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组) return:

     */
    adv_search_utx4qjkq_mounted: async function (event) {
      this.setCacheQueryConditions();
      this.$nextTick(() => {
        setTimeout(() => {
          this.$page('adv-search_utx4qjkq').doSearch();
        }, 0);
      });
    },

    /**
     * 描述：search
     * @param{event}  内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组) return:

     */
    adv_search_utx4qjkq_search: function (event) {
      const queryParams = event.evArgs[0];

      // 搜索条件里是否有“统计年份”字段
      let queryHasTjnf = false;
      queryParams.forEach(item => {
        // 判断是否为“tjnf” 字段
        if (!queryHasTjnf && item.name.indexOf('tjnf') >= 0) {
          queryHasTjnf = true;
        }

        // 判断是否为下拉
        if (event.$com.formItemVm[item.name] && event.$com.formItemVm[item.name].isSelect) {
          // 判断是否需要进行转换
          const fieldName = item.name.split('.')[1] || item.name;
          if (!this.globalVars.searchParamNoNeedCode2nameFileds.includes(fieldName)) {
            const display = event.$com.getDictDisplay(item.name);
            if (display) {
              item.value = display.split('、').join(',');
            } else {
              // 此时下拉数据源还未就绪，从缓存中取
              const dicCode = event.$com.formItemVm[item.name]._props.dataOptions.remoteDict.dictCode;
              const dicData = this.globalVars.dicCaches[dicCode];

              const values = item.value.split(',');
              const convertValues = [];

              values.forEach(value => {
                for (let i = 0; i < dicData.length; i++) {
                  const option = dicData[i];
                  if (option.itemId === value) {
                    convertValues.push(option.itemName);
                    break;
                  }
                }
              });

              item.value = convertValues.join(',');
            }
          }
        }
      });

      // 判断模型里是否有“统计年份”字段，如有且搜索条件中不带，则默认将“统计年份”置为当前年份
      let tjnfFieldName = '';
      Object.keys(event.$com.formItemVm).forEach(key => {
        if (key.indexOf('tjnf') >= 0 && key.length > tjnfFieldName.length) {
          tjnfFieldName = key;
        }
      });

      if (tjnfFieldName && !queryHasTjnf) {
        const currentYear = this.getDefaultTjnf() + '';
        this.$setModelVals('adv-search_utx4qjkq', {
          [tjnfFieldName]: currentYear
        });

        queryParams.push({
          builder: 'include',
          linkOpt: 'and',
          name: tjnfFieldName,
          value: currentYear
        })
      }
    },

    // 获取默认统计年份
    getDefaultTjnf: function () {
      // 当年8月31日（含）之前算前一年，之后算当年
      const currentDate = new Date();
      if (currentDate.getMonth() <= 7) {// getMonth 返回值 0-11
        return currentDate.getFullYear() - 1;
      } else {
        return currentDate.getFullYear();
      }
    },

    /**
     * 描述：学生画像
     * @param{event}  {row}:对应的行数据;{col}:对应的列数据;{btn}:点击的按钮；{scope}:表格对象 return:

     */
    action_ev_ia10z40t: function (event) {
      var self = this;

      // 跳转至“学生画像”
      const params = {
        xh: event.row.xh,
        tjnf: event.row.tjnf,
        timestamp: Date.now()
      };
      this.$router.push({
        name: 'qualityperformance-xshx',
        query: {
          parentMenuId: this.$route.query?.parentMenuId || this.$route.name,
          params: encodeURIComponent(JSON.stringify(params))
        }
      });

    },

    /**
     * 描述：预处理字段显隐
     * @param {modelItem} 对应模型信息，可以修改模型内容；返回一个模型信息
     */
    before_render_w6hpl66w: function (modelItem) {
      var self = this;

      if (!modelItem['search.hidden'] && modelItem.xtype === "number-range") {
        const fieldName = modelItem.name.split('.')[1] || modelItem.name;
        const urlParams = this.$pageRoute.merge.params ? JSON.parse(decodeURIComponent(this.$pageRoute.merge.params)) : {};
        if (urlParams[fieldName] !== undefined && urlParams[fieldName] != null) {
          modelItem['defaultVal'] = urlParams[fieldName].split(',');
        }
      }
    },


    /**
     * 描述：before-reset
     * @param{event}  内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组) return:

     */
    adv_search_utx4qjkq_before_reset: function (event) {
      var self = this;
      this.setCacheQueryConditions();
      this.$setModelVals('adv-search_utx4qjkq', this.globalVars.urlParamCaches);
    },


  }
})