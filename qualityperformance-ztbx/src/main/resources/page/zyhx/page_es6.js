define(function (require) {
  return {
    /**
     * 监听指定对象的值发生变化，然后执行特定方法
     */
    pageWatch: {
      'globalVars.name': function (newVal, oldVal) {
        console.log('值改变', newVal, oldVal)
      }
    },
    /**
     * 监听指定对象的值发生变化，然后执行特定方法
     */
    pageComputed: {
      getNewName: function () {
        return this.globalVars.name + '新的'
      },
      iframeSrc() {
        const urlParams = {};
        const paramStr = window.location.href.split('?')[1];
        const paramPairs = paramStr.split('&');
        paramPairs.forEach(pair => {
          const key = pair.split('=')[0];
          const value = pair.split('=')[1];
          urlParams[key] = value;
        });

        return `${window?.$GLOBAL_CONFIG?.apiPath}toolpub/proxy/fanruan/decision/view/form?modulecode=zlhx_zyhx_zyhx&appcode=qualityperformance&${this.jsonToQueryString(JSON.parse(decodeURIComponent(decodeURIComponent(urlParams['params']))))}`;
      }
    },
    var1: "变量",//响应式变量，该变量不能加到组件参数内,但是写到组件模板里
    /**
     * 响应式变量，当值变化时可以影响所有绑定的值,建议把需要绑定到组件参数里的变量申明到这里面
     */
    globalVars: {
      name: '一个变量', //响应式变量name,组件内使用时{{globalVars.name}}
      drawerInstence: null,
      remoteFunction: null,
      frUrl: '',
      pluginObject: {}, //勿删公共js要用
      filterObject: {}, //勿删公共js要用
    },
    /**
     * 固定方法，页面js初始化完成后调用,当前能修改js变量，修改组件初始化属性或者设置组件默认值
     */
    pageCreated: function () {
      this.$request({
        method: 'post',
        url: `/staticFile/getStaticFile`, // 后续接口url按valueKey配置
        data: {
          fileUrl: 'qcet/commonjs/commonJs.js'
        }
      }).then(res => {
        let text = res.data
        let commonJ = new Function('outPage', text)
        let remoteJsFunc = commonJ(this)
        this.globalVars.remoteFunction = remoteJsFunc
      });
    },
    /**
     * 定义一个函数，将嵌套对象转换为GET请求参数的格式
     */
    jsonToQueryString(obj, parentKey = '') {
      const parts = [];
      for (let key in obj) {
        if (!obj.hasOwnProperty(key)) continue; // 确保key是对象自身的属性，而不是继承来的
        let value = obj[key];
        let newKey = parentKey ? `${parentKey}[${key}]` : key; // 如果有父级key，则使用带有方括号的形式
        if (typeof value === 'object') {
          parts.push(objectToQueryString(value, newKey)); // 递归处理嵌套对象
        } else {
          parts.push(encodeURIComponent(newKey) + '=' + encodeURIComponent(value)); // 处理普通的键值对
        }
      }
      return parts.join('&'); // 将处理后的键值对用&连接起来
    },

    pageActivated() {
      this.globalVars.remoteFunction.addListener()
    },
    pageDeactivated() {
      this.globalVars.remoteFunction.destroyListener()
    },
    pageDestroy: function () {
      this.globalVars.remoteFunction.destroyListener()
    },
    /**
     * 描述：
     * @param{event}  内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组) return:undefined

     */
    ev_click_r46v1zwh: function (event) {


    },

  }
})
