{"updateTime": 1740366934262, "list": [{"isRoot": true, "type": "row", "comType": "page", "__tree_label": "页面", "__tree_node_key": "9586543514099766", "key": "page_dm0cctur", "uuid": "uuid_cuu6a801", "children": [{"__tree_node_key": "4621054388477934", "key": "row_ivzn092a", "__tree_label": "区域容器", "comType": "row", "type": "row", "isCover": false, "children": [{"__tree_node_key": "595928328556413", "key": "col_wo50i2uk", "__tree_label": "区块容器", "comType": "col", "type": "col", "isCover": false, "children": [{"uuid": "uuid_jgxpipe3", "key": "adv-search_utx4qjkq", "com": "adv-search", "comType": "adv-search", "icon": "pm-icon-advancedSearch", "comClassify": "model", "dataType": "object", "options": {"readonly": false, "interacTable": "adv-table_hd433oll", "limit": 2, "keyWordsLabel": "关键字", "hideKeyWords": false, "comLinkEnabled": true, "labelWidth": 100, "keyWordsHighlight": true, "size": "", "itemWidth": 380, "singleItemWidth": 400, "tileItemWidth": 320, "searchTime": "", "conditionType": "", "hidden": false, "readyDoSearch": false, "hideNewFields": false, "showAdv": false, "fullscreen": false, "isCondition": false, "isMore": true, "beforeRender": {"name": "before_render_w6hpl66w", "enName": "预处理字段显隐", "params": [{"name": "modelItem", "des": "对应模型信息，可以修改模型内容；返回一个模型信息"}]}, "showHidden": true, "appendToBody": true, "undefined": "", "beforeSearch": {}}, "events": {"mounted": {"name": "adv_search_utx4qjkq_mounted", "params": [{"name": "event", "des": "内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组)"}]}, "inited": {"name": "", "tip": "模型和数据获取完成"}, "search": {"name": "adv_search_utx4qjkq_search", "tip": "搜索之前，参数为querySetting(同步)"}, "before-reset": {"name": "adv_search_utx4qjkq_before_reset", "enName": "before-reset", "tip": "重置之前(同步)"}, "async-before-reset": {"tip": "重置之前(异步，参数为回调函数，执行查询需要调用回调)"}, "reset": {"tip": "重置后"}, "adv-reset": {"tip": "高级筛选重置后(需开启高级筛选功能)"}, "collapse": {"tip": "收起条件后"}, "expand": {"tip": "展开条件后"}, "item-change": {"tip": "每一项的值触发change后"}, "beforeDestroy": {}}, "dataOptions": {"remote": true, "remoteType": "model", "remoteModel": {"actionType": "search", "id": "qualityperformance-jzgjcxxlb", "url": "/admin/model/design/qualityperformance/perm/jzgjcxxlb", "modelCascades": "", "modelName": "jzgjcxxlb", "modelApp": "qualityperformance"}, "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "prefix": "search", "dataSource": {"type": "in", "url": "", "code": "", "params": {}}, "columnsModel": [{"name": "jzgh"}, {"name": "xm"}, {"name": "xb"}, {"name": "dwh"}, {"name": "rjzydm"}, {"name": "zgxw"}, {"name": "zyjszc"}, {"name": "bndjxgzl"}, {"name": "bndjxzdcs"}, {"name": "bndxspjjf"}, {"name": "bndzhpjjf"}, {"name": "bndjxcg"}, {"name": "b<PERSON><PERSON><PERSON>"}, {"name": "rzzt"}, {"name": "rxsj"}, {"name": "xymc"}, {"name": "tjnf"}], "modelConfig": {"dwh": {"placeholder": "请选择", "caption": "单位名称"}, "rjzydm": {"placeholder": "请选择", "caption": "任教专业", "search.JSONParam": {"comLink": [{"field": "dwh", "paramName": "pid"}]}}, "bndjxgzl": {"search.xtype": "number-range", "start-placeholder": "请输入", "end-placeholder": "请输入"}, "bndjxzdcs": {"search.xtype": "number-range", "start-placeholder": "请输入", "end-placeholder": "请输入"}, "bndxspjjf": {"search.xtype": "number-range", "start-placeholder": "请输入", "end-placeholder": "请输入"}, "bndzhpjjf": {"search.xtype": "number-range", "start-placeholder": "请输入", "end-placeholder": "请输入"}, "bndjxcg": {"search.xtype": "number-range", "start-placeholder": "请输入", "end-placeholder": "请输入"}, "rzzt": {"placeholder": "请选择"}, "rxsj": {"search.xtype": "datetime-range", "start-placeholder": "开始时间", "end-placeholder": "结束时间"}, "tjnf": {"placeholder": "请输入", "search.xtype": "number"}}}, "name": "高级搜索", "isCover": false, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "adv-search", "pageCode": "teacherlist"}, {"uuid": "uuid_pg72sxdg", "key": "adv-table_hd433oll", "com": "adv-vxe-table", "comType": "adv-table", "icon": "pm-icon-adv-table", "comClassify": "model", "dataType": "object", "options": {"readonly": false, "customClass": "", "labelWidth": 100, "bindPager": "", "bindComEmpty": {}, "bindComTop": {}, "bindComBottom": {}, "hidden": false, "editable": false, "hideNewFields": false, "autoLoad": false, "searchReserveData": false, "localDataSort": false, "scheme": true, "comLinkEnabled": true, "appendToBody": true, "tableConfig": {"border": "default", "seq": {"enabled": false, "seqTitle": "序号", "seqWidth": 60, "align": "center", "headerAlign": "center"}, "rowSort": {"enabled": false, "handle": "", "fixed": "left"}, "selectType": {"type": "", "checkTitle": "", "checkWidth": 40, "labelField": "", "reserve": true, "highlight": true, "range": true, "visibleMethodFunc": {}, "checkMethodFunc": {}, "align": "center", "headerAlign": "center"}, "showHeaderOverflow": false, "showOverflow": false, "fullHeight": true, "height": 0, "minHeight": 0, "maxHeight": 1000, "stripe": false, "emptyText": "暂无数据", "align": "left", "header-align": "left", "rowId": "id", "groupField": "tableColumnGroup", "groupEnabled": false, "beforeRenderFunc": {"name": "before_render_e47ndzmc", "enName": "模型预处理", "params": [{"name": "modelItem", "des": "对应列模型，可以修改模型数据；返回一个新的列模型"}]}, "spanMethodFunc": {}, "dataLoadFunc": {}}, "rowConfig": {"isHover": true, "isCurrent": false, "height": 46}, "reportConfig": {"reportText": "导出报表", "reportList": []}, "batchDownloadConfig": {"btnText": "批量下载", "btnConfigList": []}, "columnConfig": {"resizable": true, "minWidth": 120, "width": 0, "isHover": true, "isCurrent": false}, "flowBtnConfig": {"labelWidth": 120, "beforeRender": {}, "limit": 30, "fileSize": 100, "labelPosition": "left", "isTable": false, "listType": "text", "undefined": ""}, "customConfig": {"storage": true, "checkMethodFunc": {}}, "operationConfig": {"enabled": true, "position": "right", "title": "操作", "width": 100, "schema": "text", "buttonList": [{"label": "教师表现", "id": "21knd<PERSON><PERSON>", "uuid": "xqkarctl", "type": "primary", "func": {"name": "action_ev_td9lwjqp", "params": [{"name": "event", "des": "{row}:对应的行数据;{col}:对应的列数据;{btn}:点击的按钮；{scope}:表格对象"}], "enName": "教师表现"}, "children": [], "trackingEnabled": false}], "buttonEditList": [], "renderFunc": {}, "template": "", "beforeDialog": {}}, "pagerConfig": {"pageStyle": "normal", "enabled": true, "pageSize": 20, "background": false, "pagerCount": 7, "hideOnSinglePage": false, "position": "right", "pageSizes": [5, 10, 20, 50, 100], "layouts": ["prev", "pager", "next", "sizes", "jumper"], "undefined": ""}, "toolbarConfig": {"displayType": "flat", "flow": false, "import": false, "export": true, "print": false, "enabled": true, "zoom": true, "custom": true, "sort": false, "report": false, "batchDownload": false, "exportOptions": {"tooltip": {}}, "printOptions": {"tooltip": {}}, "zoomOptions": {"tooltip": {}}, "customOptions": {"tooltip": {}}, "importOptions": {"tooltip": {}}, "reportOptions": {"tooltip": {}}, "leftButtonList": {"schema": "text", "buttonList": [], "renderFunc": {}, "beforeDialog": {}}, "rightButtonList": {"schema": "text", "buttonList": [], "renderFunc": {}}}, "exportConfig": {"filename": "教职工信息", "sheetName": "sheet", "type": "xlsx", "showOneClick": true, "showCustomClick": false, "oneClickBtnText": "导出", "customBtnText": "自定义导出", "types": ["xlsx"], "translateDictFlag": true}, "treeConfig": {"lazy": false, "transform": true, "parentField": "parentId", "hasChildField": "<PERSON><PERSON><PERSON><PERSON>", "expandAll": false, "accordion": false, "trigger": "default", "indent": 20, "treeNodeField": ""}, "editConfig": {"trigger": "click", "mode": "row", "showStatus": true, "showAsterisk": true, "autoClear": true, "beforeActiveEditMethodFunc": {}, "beforeEditMethodFunc": {}}, "editRules": {}, "importConfig": {"config": "file", "filename": "模板文件", "isGenerateZdb": true, "customParam": ""}, "secureEnabled": false}, "dataOptions": {"remote": true, "remoteType": "model", "remoteModel": {"actionType": "table", "id": "qualityperformance-jzgjcxxlb", "url": "/admin/model/design/qualityperformance/perm/jzgjcxxlb", "modelCascades": "", "modelName": "jzgjcxxlb", "modelApp": "qualityperformance"}, "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "prefix": "grid", "columnsModel": [{"name": "jzgh"}, {"name": "xm"}, {"name": "xb"}, {"name": "dwh"}, {"name": "rjzydm"}, {"name": "zgxw"}, {"name": "zyjszc"}, {"name": "bndjxgzl"}, {"name": "bndjxzdcs"}, {"name": "bndxspjjf"}, {"name": "bndzhpjjf"}, {"name": "bndjxcg"}, {"name": "b<PERSON><PERSON><PERSON>"}, {"name": "rzzt"}, {"name": "rxsj"}, {"name": "xymc"}, {"name": "tjnf"}], "dataSource": {"type": "in", "url": "/eda/{modelApp}/find/{model}/{model}", "code": "table_sql", "dataPath": "rows", "params": {"skglskjs": "{vars.skglskjs}", "skglgccrc": "{vars.skglgccrc}", "skglwskjs": "{vars.skglwskjs}", "sfszkjs": "{vars.sfszkjs}", "sfszkjsnld": "{vars.sfszkjsnld}", "szkjsrzlx": "{vars.szkjsrzlx}", "sfzzfdy": "{vars.sfzzfdy}", "sfglry": "{vars.sfglry}", "sfbds": "{vars.sfbds}", "rych": "{vars.rych}", "jxjsxmlx": "{vars.jxjsxmlx}", "sfyrychjs": "{vars.s<PERSON>js}", "sfxjjs": "{vars.sfxjjs}"}}, "modelConfig": {"jzgh": {"grid.sortable": 1}, "xm": {"grid.sortable": 1}, "xb": {"grid.sortable": 1}, "dwh": {"placeholder": "请选择", "caption": "单位名称", "grid.sortable": 1}, "rjzydm": {"placeholder": "请选择", "caption": "任教专业", "grid.sortable": 1}, "zgxw": {"grid.sortable": 1}, "zyjszc": {"grid.sortable": 1}, "bndjxgzl": {"placeholder": "请输入", "grid.sortable": 1}, "bndjxzdcs": {"grid.sortable": 1}, "bndxspjjf": {"grid.sortable": 1}, "bndzhpjjf": {"grid.sortable": 1}, "bndjxcg": {"grid.sortable": 1}, "bndrych": {"grid.sortable": 1}, "rzzt": {"grid.sortable": 1}, "rxsj": {"grid.sortable": 1}, "xymc": {"grid.sortable": 1}, "tjnf": {"grid.hidden": 1, "grid.sortable": 1, "grid.omitted": 1}}}, "events": {"mounted": {"params": [{"name": "event", "des": "内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组)"}]}, "model-inited": {"name": "adv_table_hd433oll_model_inited", "enName": "model-inited", "tip": "模型获取完成"}, "reload-data": {"tip": "数据获取完成"}, "operate-click": {"tip": "操作列按钮点击"}, "radio-change": {"tip": "单选切换"}, "checkbox-change": {"tip": "多选点击"}, "checkbox-all": {"tip": "全选点击"}, "cell-click": {"name": "adv_table_hd433oll_cell_click", "enName": "cell-click", "tip": "单元格点击"}, "scroll": {"tip": "表格滚动"}, "page-change": {"tip": "切换当前页面"}, "size-change": {"tip": "每页数量变化"}, "zoom": {"tip": "表格切换最大化"}, "edit-closed": {"tip": "退出当前编辑"}, "row-sort-change": {"tip": "行拖动排序"}, "startAndTakeUserTask": {"tip": "流程启动"}, "agree": {"tip": "流程同意"}, "startAndSaveDraft": {"tip": "流程保存草稿"}, "afterClick": {"tip": "按钮事件后触发后回调"}, "flowButtonSucceed": {"tip": "流程按钮执行成功后"}, "beforeDestroy": {}}, "name": "高级表格", "isCover": false, "interactList": [], "comStyles": {"marginTop": "16", "marginBottom": "0"}, "__tree_label": "adv-vxe-table", "pageCode": "teacherlist"}], "options": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "pageCode": "teacherlist", "uuid": "uuid_ncg7at7x"}], "options": {"marginTop": 0, "marginBottom": 12, "marginLeft": 0, "marginRight": 0, "boxShadow": "0 0 0 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": false, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false, "bgType": "bgcolor", "backgroundColor": "", "borderRadius": 0}, "showTitle": false, "title": "容器标题", "layout": "grid", "pageCode": "teacherlist", "uuid": "uuid_w17nuax9"}], "options": {"marginTop": 0, "marginBottom": 12, "marginLeft": 0, "marginRight": 0, "widthType": "0", "maxWidth": 0, "minWidth": 0, "width": "260px"}, "pageCode": "teacherlist"}], "leftList": [{"isRoot": true, "type": "row", "comType": "page", "__tree_label": "页面", "__tree_node_key": "5176130895709821", "key": "page_sl0vzdai", "uuid": "uuid_ioyb1k7m", "children": [], "pageCode": "teacherlist"}], "rightList": [{"isRoot": true, "type": "row", "comType": "page", "__tree_label": "页面", "__tree_node_key": "8343652356070699", "key": "page_mujdm5jq", "uuid": "uuid_5wpt0a0o", "children": [], "pageCode": "teacherlist"}], "config": {"dataSource": [], "platform": "pc", "layout": "default", "leftWidth": 260, "rightWidth": 260}, "dialogJson": [], "suspendJson": []}