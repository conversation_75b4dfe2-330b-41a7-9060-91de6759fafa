.generate-page-teacherlist>.pm-page-body {
    height: 100% !important;
}

.generate-page-teacherlist>.pm-page-body>.page-body-main>.res-scroll>.res-scroll-body {
    height: 100%;
    overflow: auto;
}

.generate-page-teacherlist>.pm-page-body>.page-body-main>.res-scroll>.res-scroll-body .generate-layout {
    display: flex;
    flex-flow: column;
}

.generate-page-teacherlist>.pm-page-body>.page-body-main>.res-scroll>.res-scroll-body .generate-layout .row_ivzn092a {
    flex: 1 1 auto;
    height: 0;
}

.generate-page-teacherlist>.pm-page-body>.page-body-main>.res-scroll>.res-scroll-body .generate-layout .row_ivzn092a .row_ivzn092a_box {
    height: 100%;
}

.generate-page-teacherlist>.pm-page-body>.page-body-main>.res-scroll>.res-scroll-body .generate-layout .row_ivzn092a .row_ivzn092a_box .col_wo50i2uk {
    height: 100%;
    display: flex;
    flex-flow: column;
}

.generate-page-teacherlist>.pm-page-body>.page-body-main>.res-scroll>.res-scroll-body .generate-layout .row_ivzn092a .row_ivzn092a_box .col_wo50i2uk .adv-vxe-table {
    /** height: calc(100% - 118px); **/
    overflow: hidden;
    height: 100%;
    flex: 1 1 auto;
    padding-bottom: 3px;
}

.field-clickable {
  cursor: pointer;
}

.field-clickable .cell-item {
  color: #165dff !important;
}