package com.wisedu.lowcode4j.app.qualitycompareanalys;

import com.gitee.starblues.bootstrap.SpringPluginBootstrap;
import com.gitee.starblues.bootstrap.coexist.CoexistAllowAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.validation.ValidationAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;

/**
 * 基本插件
 * <AUTHOR>
 * @version 1.0
 * @since 2021-08-01
 */
@SpringBootApplication(exclude = {FlywayAutoConfiguration.class, DataSourceAutoConfiguration.class,ValidationAutoConfiguration.class})
@ComponentScan(value={"com.wisedu.lowcode4j.app.qualitycompareanalys","com.wisedu.lowcode4j.app.*.api"})
public class Application extends SpringPluginBootstrap {
    public static void main(String[] args) {
        new Application().run(args);
    }

    @Override
    protected void configCoexistAllowAutoConfiguration(CoexistAllowAutoConfiguration c) {
        c.add("com.wisedu.lowcode4j.app.db.config.SqlToyConfig");
        c.add("com.wisedu.lowcode4j.app.db.config.NutzDbConfig");
        c.add("com.wisedu.lowcode4j.app.db.config.FlywayConfig");
        c.add("com.wisedu.lowcode4j.app.file.config.ExportConfig");
        c.add("com.wisedu.lowcode4j.app.liteflow.config.AppLiteflowConfig");
        c.add("com.wisedu.lowcode4j.app.log.config.LogConfig");
        c.add("com.wisedu.lowcode4j.app.validator.config.ValidatorConfig");
        c.add("com.wisedu.lowcode4j.app.development.config.AppPageConfig");
        c.add("com.wisedu.lowcode4j.app.flow.config.AppFlowConfig");
        c.add("com.wisedu.lowcode4j.app.datafilter.config.DataFilterConfig");
        c.add("com.wisedu.lowcode4j.app.dict.config.DictConfig");
        c.add("com.wisedu.lowcode4j.app.model.config.ModelConfig");
        c.add("com.wisedu.lowcode4j.app.common.listener.MyPluginCloseListener");
        c.add("com.wisedu.lowcode4j.app.common.interceptor.MyInterceptorRegister");
    }
}
