package com.wisedu.lowcode4j.app.qualitycompareanalys;

import com.gitee.starblues.core.PluginExtensionInfo;
import com.wisedu.lowcode4j.common.core.constant.*;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
/**
 * 扩展插件信息
 * <AUTHOR>
 * @date 2023/3/9 10:11 下午
 */
@Component
public class AppExtensionInfo implements PluginExtensionInfo {

    private Map<String, Object> map = new HashMap<>(16);

    @PostConstruct
    public void init(){
        map.put(ApplicationConstant.PLUGIN_APP_ORDER_KEY, "5");
        map.put(ApplicationConstant.PLUGIN_APP_THEME_KEY, "qcet-compare-analysis");
        map.put(ApplicationConstant.PLUGIN_APP_DOMAIN_KEY, "ea");
        map.put(ApplicationConstant.PLUGIN_APP_GROUP_KEY, "rsfw");
        map.put(ApplicationConstant.PLUGIN_APP_PATTERN_KEY, "pc");
        map.put(ApplicationConstant.PLUGIN_APP_PERMIT_EXT_FLAG_KEY, "1");
    }

    @Override
    public Map<String, Object> extensionInfo() {
        return map;
    }

}
