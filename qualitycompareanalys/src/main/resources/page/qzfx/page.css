.res-scroll-body {
  height: 100%;
}

.pm-item-empty_o3bdhrcm {
  height: calc(100vh - 205px) !important;
  background: #fff;
  border-radius: 8px;
}

.tabs_o79uy8p8 {
  #tab-qzcy_tab {
    font-size: 16px !important;
  }

  .el-tabs__item {
    font-size: 16px !important;
  }
}

.generate-page-qzfx {
  .inside-dialog .dialog-body {
    overflow-y: hidden !important;
  }

  .input_qzmc {
    width: 300px;
  }

  .component_54hx98ml {
    display: block;
    width: 100%;
  }


  /* 容器 */
  .container {
    display: grid;
    /* 最小宽度为200px，自动适应宽度 repeat(auto-fit, minmax(336px, 1fr)); */
    grid-template-columns: repeat(4, minmax(250px, 25%));

    /* 在分辨率小于 768px 时，设置最小宽度为 200px */
    /*@media screen and (min-width: 768px) {
      grid-template-columns: repeat(auto-fit, minmax(336px, 336px));
    }

    @media screen and (min-width: 1200px) {
      grid-template-columns: repeat(auto-fit, minmax(336px, 1fr));
    }

    @media screen and (min-width: 1680px) {
      grid-template-columns: repeat(auto-fit, minmax(336px, 1fr));
    }*/

    /* 卡片之间的间隔 */
    grid-gap: 16px;
    overflow-y: auto;
    max-height: calc(100vh - 186px);

    /* 卡片 */
    .item {
      border-radius: 8px;
      box-shadow: none;
      height: auto !important;
      background: #ffffff;

      &:hover {
        box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.08);
      }

      /* 卡片上半部分 */
      .item-top {
        padding-left: 20px;
        padding-top: 20px;
        padding-right: 20px;

        /* 第一行 */
        .div1 {
          overflow: hidden;
          margin-bottom: 15px;

          /* 图标 */
          .icon {
            float: left;
            box-sizing: border-box;
            font-size: 32px;
            border-radius: 16px;
            flex-shrink: 0;
            height: 48px;
            width: 48px;
            text-align: center;
            line-height: 48px;
            /*color: white;*/
          }

          /* 群组名称、教师数量 */
          .qzmc-jscount-div {
            float: left;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            padding-left: 10px;
            gap: 10px;
            align-items: start;

            .qzmc {
              font-size: 16px;
              color: #1D2129;
              font-weight: 700
            }

            .jscount {
              padding-top: 5px;
              padding-bottom: 5px;
              padding-left: 0px;
              padding-right: 10px;
              color: #86909C;
            }

            .jscount-dengyu0 {}

            .jscount-dayu0 {}
          }

          .xtyz {
            float: right;
            background: #f2f3f5;
            padding-left: 8px;
            padding-right: 8px;
            padding-top: 5px;
            padding-bottom: 5px;
            border-radius: 3px;
          }
        }

        .div2 {
          box-sizing: border-box;
          display: flex;
          margin-bottom: 15px;

          .label {
            color: #86909C;
            font-size: 14px;
          }
        }

        .div3 {
          box-sizing: border-box;
          display: flex;
          align-items: center;
          margin-bottom: 15px;

          .label {
            color: #86909C;
            font-size: 14px;
          }
        }
      }

      /* 卡片下半部分 */
      .item-down {
        .btn-list {
          border-top: 1px #edeff2 solid;
          cursor: pointer;
          display: flex;

          .btn-text {
            flex-basis: 0;
            flex-grow: 1;
            padding: 10px;
            box-sizing: border-box;
            /* 让宽度包含边框和填充 */
            display: flex;
            align-items: center;
            /* 垂直居中 */
            justify-content: center;
            /* 水平居中 */
            font-size: 14px;
            font-weight: 500;
            font-style: normal;

            /* 如果有disabled */
            &.disabled {
              cursor: not-allowed;
              opacity: 0.4;
            }

            /* 如果有disabled悬浮样式 */
            &.disabled:hover {
              color: #4e5969;
            }
          }

          .btn-text:hover {
            color: #165DFF;
          }

          .btn-zj-border {
            border-left: 1px #edeff2 solid;
            border-right: 1px #edeff2 solid;
          }
        }
      }
    }
  }

  .inside-dialog {
    padding: 0px 16px;
  }

  .inside-dialog .dialog-body {
    margin: 0px;
  }

  .inside-dialog-model {
    border-radius: 8px;
  }

  .inside-dialog-model .dialog-header {
    margin-bottom: 16px;
  }

  .tabs_o79uy8p8 {
    .el-tabs__item {
      height: 36px !important;
      line-height: 36px !important;
    }
  }
}

.pm-item-page-runtime_n3rv42qk {
  height: calc(100vh - 335px);

  iframe {
    height: calc(100vh - 400px) !important;
  }
}


.pm-item-data-form_zd_qzfx {
  .item--readonly {
    height: 32px !important;
    line-height: 32px !important;
  }
}

.title_1usp1yha {
  font-weight: 700;
}

.title_t91ydy6q {
  font-weight: 700;
}

/* 群组详情右上角的按钮组 */
.pm-item-buttons_xdkru0jb {
  position: fixed;
  top: 108px;
  right: 32px;
  z-index: 100;
}

/* 群组详情-群组概述-基本信息 */
.data-form_zd_qzfx {
  .label-text {
    color: #86909C;
  }
}

.btn-delete {
  background: red !important;
  border-color: red !important;
}

.radio_0ik3w8l4 {
  margin-left: 16px !important;
}

.button_82nbz4ol {
  margin-top: -10px;
}

/* 群组详情顶部提示 */
.qzxq-top {
  overflow: hidden;
  margin-bottom: 12px;

  /* 图标 */
  .icon {
    float: left;
    box-sizing: border-box;
    font-size: 32px;
    height: 48px;
    width: 48px;
    text-align: center;
    line-height: 48px;
    border-radius: 16px;
    color: white;
  }

  /* 群组名称、教师数量 */
  .qzmc-jscount-div {
    float: left;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    padding-left: 10px;
    gap: 10px;

    .qzmc {
      font-size: 16px;
      color: #1D2129;
      font-weight: 700
    }

    .jscount {
      padding-top: 5px;
      padding-bottom: 5px;
      padding-left: 0px;
      padding-right: 10px;
    }

    .jscount-dengyu0 {}

    .jscount-dayu0 {}
  }
}

/* 群组成员Tab容器内 */
/*.row_0tqxj85o_box {
  max-height: 600px;
  overflow: auto;
}*/

/* 群组成员列表容器 */
.qzxq-qzcy-container {
  max-height: calc(100vh - 455px);
  overflow-y: auto;
  overflow-x: hidden;
  /* 最小宽度为200px，自动适应宽度 */
  /* grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); */
  /* 卡片之间的间隔 */

  /* 卡片 */
  .item:hover {
    border: 1px #165DFF solid;
    cursor: pointer;

  }

  .item:hover .btn-text {
    /* 悬停时的样式 */
    color: #165DFF !important;
    /* 文字颜色变蓝 */
  }

  .true_container {
    display: flex;
    flex-wrap: wrap;

    .item {
      &:nth-child(4n) {
        /* margin-right: 0px;*/
      }
    }
  }

  .item {
    border-radius: 8px;
    border: 1px solid #DFE2E8;
    background: #FFF;
    height: 150px;
    width: calc(25% - 12px);
    margin-right: 8px;
    margin-left: 8px;
    margin-bottom: 16px;

    /* 卡片上半部分 */
    .item-top {
      padding-left: 20px;
      /* padding-top: 20px; */
      padding-right: 20px;

      /* 第一行 */
      .div1 {
        overflow: hidden;
        margin-bottom: 16px;

        /* 图标 */
        .icon {
          margin-top: 15px;
          float: right;
          box-sizing: border-box;
          font-size: 12px;
          text-align: center;
          padding: 8px;
          border-radius: 56px;
          display: flex;
          color: white;
          width: 40px;
          height: 40px;
          flex-shrink: 0;
          fill: #165DFF;
          background: #165DFF;
          align-items: center;
          justify-content: center;
        }

        /* 群组名称、教师数量 */
        .qzmc-jscount-div {
          float: left;
          width: 75%;
          padding-top: 20px;
          box-sizing: border-box;
          display: flex;
          flex-direction: column;
          padding-left: 0px;
          /* gap: 10px; */
          line-height: 20px;

          .qzmc {
            width: 170px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            color: #000;
            font-family: "Microsoft YaHei UI";
            font-size: 16px;
            font-style: normal;
            font-weight: 700;
            /* line-height: 24px; */
            margin-bottom: 10px;
          }

          .zgh-tb {
            display: inline-block;
            vertical-align: middle;
            color: #86909C;
          }

          .zgh-dw {
            font-weight: 400;
            font-style: normal;
            font-size: 14px;
            color: #86909C;
            max-width: 155px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            font-family: "Microsoft YaHei UI";
            line-height: 22px;
            min-height: 22px;
            flex: 1;
            display: inline-block;
            vertical-align: middle;
            margin-left: 8px;

          }
        }
      }
    }

    /* 卡片下半部分 */
    .item-down {
      .btn-list {
        border-top: 1px #edeff2 solid;
        display: flex;
        padding-left: 20px;
        padding-right: 20px;

        .btn-text {
          color: #4E5969;
          text-align: center;
          font-family: "Microsoft YaHei UI";
          line-height: 38px;
          height: 38px;
          flex-basis: 0;
          flex-grow: 1;
          padding: 0;
          box-sizing: border-box;
          /* 让宽度包含边框和填充 */
          display: flex;
          /* 垂直居中 */
          align-items: center;
          /* 水平居左 */
          justify-content: flex-start;
          font-size: 14px;
          font-weight: 400;
          font-style: normal;


        }


      }
    }
  }
}

body {
  .dialog_qzfx {
    .select-person {
      padding: 0px !important;
    }
  }
}

.dialog_qzfx {
  .we-select-person_yg8h5s1f {
    /*border: 1px rgb(237, 239, 242) solid;*/
    padding-left: 10px;
    padding-right: 10px;
    border-radius: 5px;
  }
}

.dialog_qzxq {
  .el-pagination {
    position: static;
    margin: 0;
    padding: 2px 0px;
    float: right;

    * {
      font-size: 14px;
      min-width: 32px;
      height: 32px;
      line-height: 32px;

    }

    .btn-prev {
      padding-right: 8px;
    }

    .btn-next {
      padding-left: 8px;

    }

    .el-pagination__jump {
      margin-left: 0;
    }

    .el-pagination__total {
      position: absolute;
      left: 0;

    }

    .el-pagination__sizes .el-input .el-input__inner {
      padding-left: 12px;
    }

    .el-pager li {
      &:last-child {
        margin-right: 0px;
      }

      margin-right: 8px;
    }

    .el-pager li.active {
      border-radius: 2px;
      /*background: @color-primary;*/
      background: #165dff;
      color: #fff;
    }
  }
}

/* 群组分析弹窗-导入相关 */
.condition_upload_qzfx_container {
  .template_file {
    color: #86909C;
    font-size: 12px;
    margin-left: 10px;

    .el-button {
      font-size: 12px !important;
    }
  }

  .person_container {
    width: 100%;
    border: 1px solid rgba(237, 239, 242, 1);

    .person_top {
      display: flex;
      height: 48px;
      align-items: center;
      padding: 0 16px;

      .person_title {
        color: #1D2129;
        font-size: 14px;
        font-weight: 700;
      }

      .person_result {
        display: flex;
        flex: 1;
        justify-content: flex-end;

        .result {
          margin-left: 35px;
          font-size: 14px;
          display: flex;
          align-items: center;
          color: #86909C;
        }
      }
    }

    .person_content {
      border-top: 1px solid rgba(237, 239, 242, 1);
    }

    .el-upload {
      display: block;
      width: 100%;
      height: 100%;

      .el-upload-dragger {
        width: 100%;
        border: none;
        background: #F7F8FA;
      }
    }

    .el-button {
      font-size: 14px !important;
    }

    .el-upload__text {
      div {
        font-size: 12px;
        height: 20px;
        line-height: 20px;

        .el-link {
          font-size: 12px;
        }

        &.limit {
          margin-top: 2px;
          color: #86909C;
        }
      }
    }

    .el-loading-text {
      color: #86909C !important;
      font-size: 14px;
    }
  }

  .split_div {
    height: 16px;
    width: 100%;
  }

  .components_adv_search_select_data {
    .search-body {
      .el-form-item__label {
        padding-right: 12px !important;
      }
    }
  }
}

/* 条件预览 */
.dialog_tjyl {
  .teacher_content_gthx {
    display: flex;
    align-items: center;
    width: 100%;

    .teacher_avater {
      width: 50px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      overflow: hidden;
      background: #165DFF;
      color: #fff;
      margin-right: 8px;

      img {
        max-width: 100%;
        max-height: 100%;
      }

      span {
        display: block;
        text-align: center;
        font-size: 16px;
        font-weight: bold;
      }
    }

    .teacher_info {
      text-align: left;

      .teacher_name {
        color: #1D2129;
        height: 26px;
        line-height: 26px;
        font-size: 16px;
        font-weight: 700;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .teacher_gh {
        line-height: 24px;
        height: 24px;
        color: #86909C;
      }
    }
  }
}

.generate-page-qzfx {
  .el-page-header__content {
    span {
      font-weight: bold;
    }
  }
}