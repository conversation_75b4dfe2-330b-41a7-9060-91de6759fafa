define(function (require) {
  return {
    /**
     * 监听指定对象的值发生变化，然后执行特定方法
     */
    pageWatch: {
      'globalVars.name'(newVal, oldVal) {
        console.log('值改变', newVal, oldVal);
      },
    },
    /**
     * 监听指定对象的值发生变化，然后执行特定方法
     */
    pageComputed: {
      getNewName() {
        return this.globalVars.name + '新的';
      },
    },
    var1: '变量', //响应式变量，该变量不能加到组件参数内,但是写到组件模板里
    dialog: {
      dialog_qzfx: 'dialog_qzfx',
      dialog_qzxq: 'dialog_qzxq',
      dialog_tjyl: 'dialog_tjyl',
    },
    /**
     * 响应式变量，当值变化时可以影响所有绑定的值,建议把需要绑定到组件参数里的变量申明到这里面
     */
    globalVars: {
      isSelColumn: 1,
      // self.$message.success('删除成功');
      // self.$message.warning('删除成功');
      // self.$message.error('删除成功');
      qzfxview: {
        colors: [
          'linear-gradient(180deg, #165DFF 0%, #4080FF 100%)',
          'linear-gradient(180deg, #FF7D00 0%, #FF9A2E 100%)',
          'linear-gradient(180deg, #14C9C9 0%, #37D4CF 100%)',
          'linear-gradient(180deg, #EC38A4 0%, #FA64B9 100%)',
          'linear-gradient(180deg, #00B42A 0%, #23C343 100%)',
          'linear-gradient(180deg, #7A2EE5 0%, #9D57F2 100%)',
          'linear-gradient(180deg, #3491FA 0%, #57A9FB 100%)',
          'linear-gradient(180deg, #F7BA1E 0%, #F9CC45 100%)',
        ],
        loading: true,
        // tableData: [
        //   {
        //     qzmc: '全体教师',
        //     teacherCount: 1105,
        //     qztb: 'el-icon-platform-eleme',
        //     cyqz: '1',
        //     fxkb: '1',
        //     xtyz: '1',
        //   }
        // ]
        tableData: [],
      },
      // 群组分析
      qzfx: {
        // 编辑=true，新增=false
        edit: false,
        // 群组图标
        qztb: {
          pageIcon: '',
          pageIconColor: '',
          customPageIcon: '',
          pageIconBgColor: '',
        },
        // 编辑或新增群组分析时生成的查询条件放这里
        tjQuerySettingJson: [],
        tjQuerySettingJsonStr: '[]',
        // 模型级联数据
        cascader: null,
        // 选人组件
        selectUserList: [],
        // 导入页面的参数
        dr: {
          logId: '',
          querySetting: [],
          // 导入上传
          fillUserType: 'teacher',
          condition: false,
          uploadUrl: '', //'http://127.0.0.1:8282/lowcode/admin/file/main/preupload',
          axiosInstence: {},
          hasPersonList: false,
          acceptType: ['.xlsx'],
          limitSize: 500,
          logId: '',
          uploadLoading: false,
          headers: {},
          templateKey: {
            teacher: 'teacherQzfx',
          },
          // downloadKey: {
          //   student: 'bizstudent',
          //   teacher: 'bizteacher'
          // },
          urlObj: {
            // student: '/admin/api/query/main_appSetting_reviewFillConditionStudent',
            // teacher: '/admin/api/query/main_appSetting_reviewFillConditionTeacher',
            // 首次导入预览
            scdryulan: '/plugins/portrait/qzfx/previewImportTeacher',
            // 后面再编辑预览调用这个接口
            editdryulan: '/plugins/portrait/qzfx/previewEditImportTeacher',
          },
          columns: {
            // student: ["xh:学号:false", "studentName:姓名:false", "academy:院系:true", "majorCode:专业:true"],
            // teacher: ["zgh:职工号:false", "teacherName:姓名:false", "departmentCode:所在单位:true"],
            teacher: [
              'zgh:职工号:false',
              'xm:姓名:false',
              'ssjgdm:所在单位:true',
            ],
          },
          newRequestHeaders: {
            'Content-Type': 'application/x-www-form-urlencoded',
            // "Authorization_lowcode": getToken()
          },
          batchId: '',
          templateBatchId: '',
          // 导入查询
          tableParams: {},
          pagerConfig: {
            enabled: true,
            pageSize: 10,
            background: false,
            pagerCount: 10,
            hideOnSinglePage: true,
            position: 'right',
            pageSizes: [5, 10, 20, 50, 100],
            layouts: ['prev', 'pager', 'next', 'sizes', 'total', 'jumper'],
          },
          tableDataConfig: {
            type: 'custom',
            url: '',
            code: 'table',
            dataPath: 'rows',
            params: {
              fillConditionType: '{page.tableParams.fillConditionType}',
              fillCondition: '{page.tableParams.fillCondition}',
              field: 'page',
            },
            // 表格第一次加载会走这个
            querySetting: [],
          },
          modelParams: {
            actionType: 'table',
            id: '',
            url: '',
            modelCascades: '',
            modelName: 'jzgemptymodel',
            modelApp: 'portrait',
            params: { modelId: 'main-abdteajzgjbxx' },
          },
          tableConfig: {
            // "beforeRenderFunc": this.modelEdit,
            maxHeight: 440,
            'header-align': 'left',
            align: 'left',
          },
          columnsModelObj: {
            student: [
              {
                name: 'xh',
                fuzzySearch: 1,
              },
              {
                name: 'studentName',
                fuzzySearch: 1,
              },
              {
                name: 'academy',
                caption: '院系',
              },
              {
                name: 'majorCode',
                caption: '专业',
              },
            ],
            teacher: [
              {
                name: 'zgh',
                fuzzySearch: 1,
              },
              {
                name: 'xm',
                fuzzySearch: 1,
              },
              {
                name: 'ssjgdm',
                caption: '所在单位',
              },
            ],
          },
          keyWordsLabelObj: {
            teacher: '职工号/姓名',
          },
          showList: false,
          columnsModel: [],
          showModel: [],
          searchQuerySetting: {},
          searchProps: {
            limit: 2,
            hideKeyWords: true,
            labelWidth: 100,
            hidden: false,
            readyDoSearch: true,
            size: 'mini',
            modelParams: {
              actionType: 'search',
              id: '',
              url: '',
              modelCascades: '',
              modelName: 'jzgemptymodel',
              modelApp: 'portrait',
              params: { modelId: 'main-abdteajzgjbxx' },
            },
          },
        },
      },
      // 群组详情弹窗
      qzxq: {
        formData: {},
        // formData: {
        //   id: '',
        //   qzmc: '专任教师4',
        //   teacherCount: 1105,
        //   qztb: 'el-icon-s-tools',
        //   cyqz: '1',
        //   fxkb: '0',
        //   xtyz: '0',
        // },
        qzgs: {
          tjQuerySettingJson: [],
        },
        qzcy: {
          // 分页
          // 当前页
          pageNo: 1,
          pageSize: 8,
          // 总页数
          total: 0,
          loading: true,
          tableData: [],
        },
      },
    },

    pageCreated() {
      console.log('页面js初始化完成后调用');
      this.setMediaCss();
    },

    pageReady() {
      console.log('页面准备完成后调用');
      this.globalVars.qzfx.dr.uploadUrl = this.getUploadActionUrl(
        '/admin/file/main/preupload',
      );
      this.getQzfxGroupList();
      window.addEventListener('message', this.onPostMessage);
    },

    pageDestroy() {
      console.log('页面销毁前调用');
      window.removeEventListener('message', this.onPostMessage);
    },

    pageActivated() {
      window.addEventListener('message', this.onPostMessage);
    },

    pageDeactivated() {
      window.removeEventListener('message', this.onPostMessage);
    },

    getUploadActionUrl(url) {
      if (url != null && url[0] === '/') {
        url = url.substr(1);
      }
      let BASE_URL = `${window?.$GLOBAL_CONFIG?.apiPath}`;
      return BASE_URL + url;
    },
    /**
     * 打开弹窗
     */
    showDialog(dialogKey, title, type = 1, params = {}) {
      // dialog_qzfx 群组分析新增/编辑弹窗
      // dialog_qzxq 群组详情
      // 获取dialog params self.$getDialog("dialog_dhy6o24n").dialogParams.hasAuth;
      this.$pageDialog({
        key: dialogKey,
        type: type,
        params: params,
        winParams: {
          title: title,
        },
      });
    },
    /**
     * 获取弹窗参数
     * self.getDialogParams(self.dialog.dialog_qzxq,'id')
     */
    getDialogParams(dialogKey, paramsKey) {
      return this.$getDialog(dialogKey).dialogParams[paramsKey];
    },
    /**
     * 关闭弹窗
     */
    closeDialog(dialogKey) {
      var dialog = this.$getDialog(dialogKey);
      dialog.closeWindow();
    },

    /**
     * 清空表单
     */
    clearQzfxForm() {
      var self = this;
      self.$setVal('data-form_qzfx', {});
      self.$page('data-form_qzfx')?.clearValidate();
      self.$setVal('radio_0ik3w8l4', '');
      // 条件
      self.globalVars.qzfx.tjQuerySettingJson = [];
      self.globalVars.qzfx.tjQuerySettingJsonStr = '[]';
      // 条件预览
      // 清空选人组件
      self.globalVars.qzfx.selectUserList = [];
      self.$setVal('we-select-person_yg8h5s1f', []);
      // 清空导入
      self.globalVars.qzfx.dr.condition = false;
      self.globalVars.qzfx.dr.logId = '';
      self.globalVars.qzfx.dr.uploadLoading = false;
      // 图标初始化
      self.globalVars.qzfx.qztb.pageIconColor = '';
      self.globalVars.qzfx.qztb.customPageIcon = '';
      self.globalVars.qzfx.qztb.pageIconBgColor = '';
    },
    clearQzxqForm() {
      var self = this;
      self.$setVal('data-form_zd_qzfx', {});
      self.$page('data-form_zd_qzfx')?.clearValidate();
      self.$page('adv-search_pp1qj0kk')?.doReset();
    },

    /**
     * 把数值转换成1,105这种格式
     */
    formatNumber(num) {
      if (!num) {
        return '0';
      }
      let str = num.toString().split('').reverse().join('');
      let formattedStr = '';
      for (let i = 0; i < str.length; i++) {
        if (i % 3 === 0 && i !== 0) {
          formattedStr += ',';
        }
        formattedStr += str[i];
      }
      formattedStr = formattedStr.split('').reverse().join('');
      return formattedStr;
    },

    renderJsCount(num) {
      return {
        'jscount-dengyu0': num === 0,
        'jscount-dayu0': num > 0,
      };
    },

    /**
     * 获取群组分析列表
     */
    getQzfxGroupList() {
      var self = this;
      // input_qzmc
      let params = {
        querySetting: [
          {
            name: 'qzmc',
            value: this.$getVal('fuzzy-search_tib2i70n'),
            linkOpt: 'and',
            builder: 'include',
          },
        ],
      };
      this.globalVars.qzfxview.loading = true;
      let url = '/plugins/portrait/qzfx/getList';
      this.$request({
        method: 'post',
        url: url,
        data: params,
      })
        .then(res => {
          if (res.data.length > 0) {
            self.globalVars.qzfxview.tableData = res.data;
            self.$setComsProps({
              // 空状态-隐藏
              empty_o3bdhrcm: {
                hidden: 1,
              },
            });
          } else {
            self.globalVars.qzfxview.tableData = [];
            self.$setComsProps({
              // 空状态-显示
              empty_o3bdhrcm: {
                hidden: 0,
              },
            });
          }
          this.globalVars.qzfxview.loading = false;
        })
        .catch(rej => {
          this.globalVars.qzfxview.loading = false;
        });
    },

    /**
     * 描述：新增群组
     * @param{event}  内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组) return:undefined

     */
    ev_click_m87k3xfd(event) {
      var self = this;
      self.showDialog(self.dialog.dialog_qzfx, '新增群组');
      self.globalVars.qzfx.edit = false;
      self.$setVal('radio_0ik3w8l4', 'tj');
      // 设置导入初始化数据
      self.globalVars.qzfx.dr.templateBatchId =
        self.globalVars.qzfx.dr.batchId = new Date().getTime();
    },
    genIcon(qztb) {
      // pageIcon: '',
      // pageIconColor: '',
      // customPageIcon: '',
      // pageIconBgColor: '',
      var self = this;
      let tb = JSON.parse(qztb);
      // let html = `"<i class='icon ${tb.pageIcon}' style='background:${tb.pageIconBgColor}'></i>"`
      let html = `<span class="adiconfont icon ${tb.pageIcon}"style="background:linear-gradient(180deg, ${tb.pageIconColor} 0%, ${tb.pageIconBgColor} 100%);color:white"></span>`;
      return html;
    },
    getQzxqFormData(qzid, func) {
      var self = this;
      // 查询群组分析信息
      self
        .$request({
          method: 'post',
          url: '/plugins/portrait/qzfx/getById?qzid=' + qzid,
        })
        .then(function (res) {
          self.globalVars.qzxq.formData = res.data;
          // 刷新图标
          self.setIconData(res.data.qztb);
          // 系统预置的不让删除、不让编辑
          if (res.data.xtyz === '1') {
            self.$getDialog(self.dialog.dialog_qzxq)?.$setComsProps({
              buttons_xdkru0jb: { 'buttonList.0.hidden': true },
            });
            self.$getDialog(self.dialog.dialog_qzxq)?.$setComsProps({
              buttons_xdkru0jb: { 'buttonList.2.hidden': true },
            });
          }
          // 回调方法
          if (func) {
            func(self.globalVars.qzxq.formData);
          }
        })
        ['catch'](function (err) {
          self.$message.error('操作错误:' + err.message);
        });
    },
    /**
     * 打开群组详情弹窗
     */
    openQzxq(qzid) {
      var self = this;
      self.getQzxqFormData(qzid, formData => {
        // 群组详情
        self.showDialog(self.dialog.dialog_qzxq, '', 3, formData);
        self.$setVal('data-form_zd_qzfx', formData);
      });
    },
    /**
     * 打开群组分析的tab页
     */
    openTabQzfx(qzid) {
      var self = this;
      self.getQzxqFormData(qzid, formData => {
        self.showDialog(
          self.dialog.dialog_qzxq,
          '群组详情-群组分析',
          3,
          formData,
        );
        self.$setVal('data-form_zd_qzfx', formData);
        self
          .$getDialog(self.dialog.dialog_qzxq)
          .$setComProps('tabs_o79uy8p8', { activeIndex: 2 });
      });
    },
    /**
     * 打开群组分析的编辑窗口
     */
    openEditQzfx(item) {
      let qzid = item.id;
      let xtyz = item.xtyz;
      // if (xtyz === "1") return;
      var self = this;
      // self.showDialog(self.dialog.dialog_tjyl, '预览', 1)
      // self.$getDialog("dialog_tjyl").$setComProps("adv-table_94qqaomu", {
      //   'extQuerySetting': [
      //     [],
      //     []
      //   ]
      // })
      // self.$comMethod("adv-table_94qqaomu", "reload")
      // return

      // 查询群组分析信息
      self
        .$request({
          method: 'post',
          url: '/plugins/portrait/qzfx/getById?qzid=' + qzid,
        })
        .then(function (res) {
          self.showDialog(self.dialog.dialog_qzfx, '编辑群组', 1, res.data);
          let qzid = res.data.id;
          self.globalVars.qzfx.edit = true;
          self.$setVal('data-form_qzfx', res.data);
          // 图标回显
          self.setIconData(res.data.qztb);
          // 圈群方式回显
          self.$setVal('radio_0ik3w8l4', res.data.qqfs);
          if (res.data.qqfs === 'tj') {
            // 条件回显
            self.globalVars.qzfx.tjQuerySettingJson = JSON.parse(
              res.data.tj,
            )[0];
          } else if (res.data.qqfs === 'xz') {
            // 直接选择回显
            self.setSelectUserList(qzid);
          } else if (res.data.qqfs === 'dr') {
            // 导入回显
            // 设置导入初始化数据
            self.globalVars.qzfx.dr.templateBatchId =
              self.globalVars.qzfx.dr.batchId = new Date().getTime();
            // 导入预览的接口调整
            self.globalVars.qzfx.dr.tableDataConfig.url =
              self.globalVars.qzfx.dr.urlObj['editdryulan'] + '/' + qzid; //'?qzid=' + qzid
            self.globalVars.qzfx.dr.modelParams.url =
              '/admin/model/design/portrait/perm/jzgemptymodel';
            self.globalVars.qzfx.dr.modelParams.id = 'portrait-jzgemptymodel';
            self.globalVars.qzfx.dr.modelParams.modelName = 'jzgemptymodel';
            self.globalVars.qzfx.dr.searchProps.modelParams.id = `portrait-jzgemptymodel`;
            self.globalVars.qzfx.dr.searchProps.modelParams.url = `/admin/model/design/portrait/perm/jzgemptymodel`;

            // 加载表格组件
            self.globalVars.qzfx.dr.condition = true;
            // 预览导入
            self.globalVars.qzfx.dr.showList = true;
            // 初始化配置
            self.initTableConfig();
            // self.changeFillUserType(self.globalVars.qzfx.dr.fillUserType, logId)

            self.globalVars.qzfx.dr.columnsModelObj[
              self.globalVars.qzfx.dr.fillUserType
            ].forEach(ele => {
              self.globalVars.qzfx.dr.showModel.push(ele.name);
            });
            self.globalVars.qzfx.dr.columnsModel =
              self.globalVars.qzfx.dr.columnsModelObj[
                self.globalVars.qzfx.dr.fillUserType
              ];
            self.globalVars.qzfx.dr.modelParams.modelApp = 'portrait';
            // 调接口表格
            self.dataReload();
          }
        })
        ['catch'](function (err) {
          self.$message.error('操作错误:' + err.message);
        });
    },
    // 选人组件回显
    setSelectUserList(qzid) {
      var self = this;
      let url = '/plugins/portrait/qzfx/getSelectUserListByQzid?qzid=' + qzid;
      this.$request({
        method: 'post',
        url: url,
      })
        .then(res => {
          let ids = res.data.map(x => x.zgh);
          self.$setVal('we-select-person_yg8h5s1f', ids);
        })
        .catch(rej => {});
    },
    setIconData(qztb) {
      var self = this;
      self.globalVars.qzfx.qztb = JSON.parse(qztb);
      console.log('回显图标', self.globalVars.qzfx.qztb);
    },
    /**
     * 查看个人画像
     */
    openGrhx(item) {
      var self = this;
      // let urlData = this.$router.resolve({
      //   name: `portrait-gthx`,
      //   query: {
      //     zgh: zgh
      //   }
      // })
      // window.open(urlData.href, "_blank")
      this.$openPage({
        appCode: 'portrait',
        pageCode: 'hxzl',
        title: '画像总览',
        pageParams: { zgh: item.zgh, xm: item.xm },
      });
    },

    /**
     * 重载群组详情-群组概述
     */
    reloadQzxqQzgs(formData) {
      var self = this;
      self.$setVal('data-form_zd_qzfx', formData);
      self.globalVars.qzxq.formData = formData;
      console.log('表单保存数据', self.$getVal('data-form_zd_qzfx'));
      if (formData.qqfs === 'tj') {
        self.$getDialog(self.dialog.dialog_qzxq)?.$setComsProps({
          // 是否展示群组成员条件
          col_346eiwsm: {
            hidden: 0,
          },
        });
        // 展示条件查看组件
        self.getTjCascaderData(() => {
          self.globalVars.qzxq.qzgs.tjQuerySettingJson = JSON.parse(
            formData.tj,
          )[0];
          console.log(
            '群组概述查看条件',
            self.globalVars.qzxq.qzgs.tjQuerySettingJson,
          );
        });
      } else {
        self.$getDialog(self.dialog.dialog_qzxq)?.$setComsProps({
          // 是否展示群组成员条件
          col_346eiwsm: {
            hidden: 1,
          },
        });
      }
    },

    /**
     * 描述：群组详情-tab页change
     * @param{event}  内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组) return:undefined

     */
    ev_tabChange_yz0wszjg(event) {
      var self = this;
      console.log('tabchanges', event);
      let qzid = self.getDialogParams(self.dialog.dialog_qzxq, 'id');
      if (event.evArgs[0].name === 'qzgs_tab') {
        // 群组概述
        // 查询表单
        self.getQzxqFormData(qzid, formData => {
          self.reloadQzxqQzgs(formData);
        });
      } else if (event.evArgs[0].name === 'qzcy_tab') {
        // 群组成员
        self.$page('adv-search_pp1qj0kk').doSearch();
      } else if (event.evArgs[0].name === 'qzfx_tab') {
        // 群组分析
        self.getQzxqFormData(qzid, formData => {
          if (formData.fxkb === '1') {
            self.$page('dialog_qzxq').$setComsProps({
              empty_la0tq1bj: {
                hidden: 1,
              },
              'page-runtime_n3rv42qk': {
                hidden: 0,
              },
            });
          } else {
            self.$page('dialog_qzxq').$setComsProps({
              empty_la0tq1bj: {
                hidden: 0,
              },
              'page-runtime_n3rv42qk': {
                hidden: 1,
              },
            });
          }
        });
      }
    },

    /**
     * 描述：比对详情弹窗-删除
     * @param{event}  {btn}:点击的按钮; return:undefined

     */
    action_ev_w4lej0nb(event) {
      var self = this;
      let qzid = self.getDialogParams(self.dialog.dialog_qzxq, 'id');
      self
        .$resConfirm(
          '确认删除此群组吗？<br>删除后，将同步删除群组信息、所有分析模板，请谨慎操作。',
          '删除提示',
          {
            confirmButtonText: '删除',
            cancelButtonText: '取消',
            confirmButtonClass: 'btn-delete',
            dangerouslyUseHTMLString: true,
            type: 'warning',
          },
        )
        .then(function () {
          self
            .$request({
              method: 'post',
              url: '/plugins/portrait/qzfx/delById?qzid=' + qzid,
            })
            .then(function (res) {
              if (res.code === '0') {
                self.$message.success('删除成功');
                self.closeDialog(self.dialog.dialog_qzxq);
                self.getQzfxGroupList();
              } else {
                self.$message.error(res.msg);
              }
            })
            ['catch'](function (err) {
              self.$message.error('操作错误:' + err.message);
            });
        })
        ['catch'](function () {
          // 已取消
        });
    },

    /**
     * 描述：比对详情弹窗-立即对比
     * @param{event}  {btn}:点击的按钮; return:undefined

     */
    action_ev_e19osncq(event) {
      var self = this;
      let urlData = this.$router.push({
        name: `portrait-hxdb`,
        query: {
          qzid: self.globalVars.qzxq.formData.id,
        },
      });
      // window.open(urlData.href, "_blank")
      // this.$openPage({ appCode: 'portrait', pageCode: 'hxdb', title: '画像对比', pageParams: { 'qzid': self.globalVars.qzxq.formData.id } })
    },

    /**
     * 描述：比对详情弹窗-编辑
     * @param{event}  {btn}:点击的按钮; return:undefined

     */
    action_ev_7efdfz1j(event) {
      var self = this;
      let qzid = self.getDialogParams(self.dialog.dialog_qzxq, 'id');
      let data = self.$getDialog(self.dialog.dialog_qzxq).dialogParams;
      self.openEditQzfx(data);
    },

    handleSizeChange(val) {
      var self = this;
      console.log(`每页 ${val} 条`);
      this.globalVars.qzxq.qzcy.pageSize = val;
      self.$page('adv-search_pp1qj0kk').doSearch();
    },
    handleCurrentChange(val) {
      var self = this;
      console.log(`当前页: ${val}`);
      this.globalVars.qzxq.qzcy.pageNo = val;
      self.$page('adv-search_pp1qj0kk').doSearch();
    },

    /**
     * 描述：列表页-输入群组名称-查询
     * @param{event}  内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组) return:undefined

     */
    ev_enter_az0gvczs(event) {
      var self = this;
      this.getQzfxGroupList();
    },

    /**
     * 渲染群组成员单选按钮change事件
     */
    renderQzfxQzcyForm(type) {
      var self = this;
      self.$setHide({
        col_z637jskk: type !== 'tj',
        col_i6kp93mc: type !== 'xz',
        col_zus02oip: type !== 'dr',
      });
    },

    /**
     * 描述：圈群方式change
     * @param{event}  内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组) return:undefined

     */
    ev_value_change_jh8dnxm9(event) {
      var self = this;
      console.log('change', event.evArgs[1]);
      let type = event.evArgs[1];
      self.renderQzfxQzcyForm(type);
    },

    /**
     * 描述：群组分析弹窗-保存
     * @param{event}  事件相关参数 return:undefined
     * @param{closeNext}  需要调用该方法才能关闭弹窗 return:undefined

     */
    action_ev_ubv5gn3t(event, closeNext) {
      var self = this;
      self.saveQzfx();
    },

    getTjQuerySetting() {
      var self = this;
      return new Promise((resolve, reject) => {
        self
          .$page('component_eom74ped')
          .$refs.advFilter2Ref.getQuerySetting()
          .then(res => {
            console.log('条件获取成功', res);
            resolve(res);
          })
          .catch(err => {
            console.log('条件获取失败', err);
            reject([]);
          });
      });
    },

    saveQzfx(type) {
      var self = this;
      // 塞图标
      // self.$getVal("data-form_qzfx").qztb = JSON.stringify(self.globalVars.qzfx.qztb);
      self
        .$page('data-form_qzfx')
        .validateFormValue()
        .then(async function (obj) {
          if (!obj) {
            return;
          }
          let rqdata = self.$getVal('data-form_qzfx');
          // 判断是否选择了圈群方式
          let qqfs = self.$getVal('radio_0ik3w8l4');
          if (!qqfs) {
            self.$message.warning('请选择圈群方式');
            return;
          }
          if (qqfs === 'tj') {
            let tjQuerySetting = await self.getTjQuerySetting();
            if (tjQuerySetting.length <= 0) {
              self.$message.warning('请设置筛选条件');
              return;
            }
            // 在包一层数据，拼接成and (sql)，编辑时也需要取
            rqdata.tj = JSON.stringify([tjQuerySetting]);
          }
          if (qqfs === 'xz') {
            let selectUserList = self.globalVars.qzfx.selectUserList;
            if (selectUserList.length <= 0) {
              self.$message.warning('请选择人员');
              return;
            }
            rqdata.selectUserList = selectUserList;
          }
          if (qqfs === 'dr') {
            let logId = self.globalVars.qzfx.dr.logId;
            if (!self.globalVars.qzfx.edit && !logId) {
              // 新增时必须导入人员
              self.$message.warning('请导入人员');
              return;
            }
            rqdata.logId = logId;
          }
          let tb;
          if (self.globalVars.qzfx.edit) {
            // 编辑
            tb = JSON.parse(rqdata.qztb).pageIcon;
          } else {
            // 新增
            tb = self.$getVal('data-form_qzfx').qztb;
            // tb=self.globalVars.qzfx.qztb.pageIcon
          }
          self.globalVars.qzfx.qztb.pageIcon = tb;
          // self.$getVal("data-form_qzfx").qztb = JSON.stringify(self.globalVars.qzfx.qztb);
          // self.$getVal("data-form_qzfx").qqfs = qqfs
          rqdata.qqfs = qqfs;
          var url = '/plugins/portrait/qzfx/saveOrUpdate';
          self
            .$request({
              method: 'post',
              url: url,
              data: {
                ...rqdata,
                qztb: JSON.stringify(self.globalVars.qzfx.qztb),
              },
            })
            .then(function (res) {
              if (res.code === '0') {
                self.$message.success('保存成功');
                self.closeDialog(self.dialog.dialog_qzfx);
                // 重载列表
                self.getQzfxGroupList();
                if (type === 'ljfx') {
                  self.globalVars.qzxq.formData = res.data;
                  self.openTabQzfx(res.data.id);
                }
                self.getQzxqFormData(res.data.id, formData => {
                  self.reloadQzxqQzgs(formData);
                });
              } else {
                self.$message.error(res.msg);
              }
            })
            .catch(function (err) {
              console.log(err);
              self.$message.error(err.message);
            });
        })
        .catch(function (err) {
          console.log(err);
        });
    },
    /**
     * 描述：群组分析弹窗-保存并立即分析
     * @param{event}  事件相关参数 return:undefined
     * @param{closeNext}  需要调用该方法才能关闭弹窗 return:undefined

     */
    action_ev_z0jno3qe(event, closeNext) {
      var self = this;
      self.saveQzfx('ljfx');
    },

    /**
     * 描述：群组分析弹窗-卸载
     * @param{event}  name:事件名称,dialog:当前弹窗对象 return:undefined

     */
    destroy_dialog_sppn93ai(event) {
      var self = this;
      self.clearQzfxForm();
    },

    /**
     * 描述：群组详情弹窗-卸载
     * @param{event}  name:事件名称,dialog:当前弹窗对象 return:undefined

     */
    destroy_dialog_54eyxi9y(event) {
      var self = this;
      self.clearQzxqForm();
    },

    /**
     * 常用群组change
     */
    cyqzChange(val, qzid) {
      var self = this;
      self
        .$request({
          method: 'post',
          url:
            '/plugins/portrait/qzfx/cyqzChange?qzid=' + qzid + '&cyqz=' + val,
        })
        .then(function (res) {
          if (res.code === '0') {
            self.$message.success('操作成功');
            self.getQzfxGroupList();
          } else {
            self.$message.error(res.msg);
          }
        })
        ['catch'](function (err) {
          self.$message.error('操作错误:' + err.message);
        });
    },

    /**
     * 描述：群组分析弹窗-设置条件-预览
     * @param{event}  内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组) return:undefined

     */
    ev_click_ci8d1g6a: async function (event) {
      var self = this;
      self.globalVars.qzfx.tjQuerySettingJson = await self.getTjQuerySetting();
      // tjQuerySettingJsonStr 给条件预览的表格空模型使用，渲染列用的
      self.globalVars.qzfx.tjQuerySettingJsonStr = JSON.stringify(
        self.globalVars.qzfx.tjQuerySettingJson,
      );
      console.log(
        '群组分析-条件预览-条件设计器',
        self.globalVars.qzfx.tjQuerySettingJson,
      );
      if (self.globalVars.qzfx.tjQuerySettingJson.length <= 0) {
        self.$message.warning('请设置筛选条件');
        return;
      }
      self.showDialog(
        self.dialog.dialog_tjyl,
        '预览',
        1,
        self.globalVars.qzfx.tjQuerySettingJson,
      );
    },

    /**
     * 描述：群组详情-群组成员-高级搜索-搜索
     * @param{event}  内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组) return:undefined

     */
    ev_search_cqsx1ig5(event) {
      var self = this;
      // 条件选择器的querySetting
      let querySetting1 = [];
      // 高级搜索组件的querySetting
      let querySetting2 = event.evArgs[0];
      console.log('dosearch querySetting2', querySetting2);
      let formData = self.globalVars.qzxq.formData;
      let qqfs = formData.qqfs;
      let qzid = formData.id;
      // 全部qb也不传qzid
      if (qqfs === 'tj') {
        // 拼接条件设计器的
        querySetting1 = JSON.parse(formData.tj)[0];
        // params.push([
        //   {
        //     "name": "zgh",
        //     "value": "",
        //     "builder": "include",
        //     "caption": "教职工号",
        //     "builder_display": "包含",
        //     "linkOpt": "or"
        //   },
        //   {
        //     "name": "teacherName",
        //     "value": "",
        //     "builder": "include",
        //     "caption": "姓名",
        //     "builder_display": "包含",
        //     "linkOpt": "or"
        //   }
        // ])
      }
      // else if (qqfs === 'xz' || qqfs === 'dr') {
      //   // 圈群方式=直接选择和导入，根据群组id查询教职工
      //   qzid = formData.id
      // }
      self.globalVars.qzxq.qzcy.tableData = [];
      let queryParams = {
        pageNo: self.globalVars.qzxq.qzcy.pageNo,
        pageSize: self.globalVars.qzxq.qzcy.pageSize,
        querySetting: [querySetting1, querySetting2],
        // "querySetting": querySetting1,
        order: 'zgh asc',
      };
      this.globalVars.qzxq.qzcy.loading = true;
      let url =
        '/plugins/portrait/qzfx/getQzcyPageList?qzid=' + qzid + '&qqfs=' + qqfs;
      this.$request({
        method: 'post',
        url: url,
        data: queryParams,
      })
        .then(res => {
          self.globalVars.qzxq.qzcy.total = res.data.totalSize;
          let rows = res.data.rows;
          if (rows.length > 0) {
            self.globalVars.qzxq.qzcy.tableData = rows;
            self.$getDialog(self.dialog.dialog_qzxq)?.$setComsProps({
              // 空状态-隐藏
              empty_px0a6w2e: {
                hidden: 1,
              },
            });
          } else {
            self.globalVars.qzxq.qzcy.tableData = [];
            self.$getDialog(self.dialog.dialog_qzxq)?.$setComsProps({
              // 空状态-显示
              empty_px0a6w2e: {
                hidden: 0,
              },
            });
          }
          this.globalVars.qzxq.qzcy.loading = false;
        })
        .catch(rej => {
          this.globalVars.qzxq.qzcy.loading = false;
        });

      // this.$request('qzfx_page_reviewTeacher', { params: queryParams }).then(res => {
      //   self.globalVars.qzxq.qzcy.total = res.data.data.page.recordCount
      //   let rows = res.data.data.page.rows
      //   if (rows.length > 0) {
      //     self.globalVars.qzxq.qzcy.tableData = rows
      //     self.$getDialog(self.dialog.dialog_qzxq).$setComsProps({
      //       // 空状态-隐藏
      //       empty_px0a6w2e: {
      //         hidden: 1
      //       }
      //     })
      //   } else {
      //     self.globalVars.qzxq.qzcy.tableData = []
      //     self.$getDialog(self.dialog.dialog_qzxq).$setComsProps({
      //       // 空状态-显示
      //       empty_px0a6w2e: {
      //         hidden: 0
      //       }
      //     })
      //   }
      //   this.globalVars.qzxq.qzcy.loading = false
      // }).catch(err => {
      //   console.log('err')
      //   this.globalVars.qzxq.qzcy.loading = false
      // })
    },

    /**
     * 描述：群组详情-群组成员-高级搜索-重置
     * @param{event}  内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组) return:undefined

     */
    ev_reset_65sxg50h(event) {
      var self = this;
      let params = event.evArgs[0];
      console.log('重置', params);
    },

    /**
     * 描述：群组详情-群组成员-高级搜索-itemchange
     * @param{event}  内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组) return:undefined

     */
    ev_item_change_p41964ht(event) {
      var self = this;
      console.log('群组成员搜索', event);
      // 高级搜索表单变化之后从第一页开始
      self.globalVars.qzxq.qzcy.pageNo = 1;
    },

    /**
     * 描述：群组分析-条件预览-高级搜索
     * @param{event}  内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组) return:undefined

     */
    ev_search_aawxneyj: async function (event) {
      var self = this;
      let querySetting2 = event.evArgs[0];
      console.log('群组分析-条件预览-高级搜索 querySetting2', querySetting2);
      let queryParams = {
        extQuerySetting: [
          self.globalVars.qzfx.tjQuerySettingJson,
          querySetting2,
        ],
      };
      console.log('设置dialog_tjyl', queryParams);
      // 把表格的分页清理掉
      self
        .$getDialog('dialog_tjyl')
        .$setComProps('adv-table_94qqaomu', queryParams);
      self.$nextTick(() => {
        // self.$comMethod("adv-table_94qqaomu", "reload", { pageNo: 1 })
        self.$comMethod('adv-table_94qqaomu', 'reload', null, 1);
      });

      // self.$getDialog("dialog_tjyl").$setComProps("adv-table_94qqaomu", {
      //   'extQuerySetting': [{
      //     "name": "sjbdId",
      //     "value": '1',
      //     "linkOpt": "and",
      //     "builder": "include"
      //   }]
      // })

      // self.$page('adv-table_94qqaomu').querySetting = [
      //   querySetting1,
      //   querySetting2
      // ];
      // self.$page('adv-table_94qqaomu').search();
    },

    /* ------------------------------------------ 导入相关 ------------------------------------------ */
    /**下载模板文件 */
    downLoadTemplate() {
      this.$request({
        method: 'post',
        url: '/admin/imexport/portrait/importTemplate',
        params: {
          config:
            this.globalVars.qzfx.dr.templateKey[
              this.globalVars.qzfx.dr.fillUserType
            ],
          templateName: '导入群组人员',
          isGenerateZdb: false,
        },
      })
        .then(res => {
          console.log('res:', res);
          let fileId = res.datas.importTemplate.attachment;
          this.downloadFile({ fileId });
        })
        .catch(rej => {
          console.log('rej:', rej);
        });
    },
    /**下载成功数据 */
    downloadSuccessFile() {
      var self = this;
      let logId = this.globalVars.qzfx.dr.logId;
      let url;
      if (logId) {
        // logid有值说明是导入之后预览，这个走临时表预览
        url = this.globalVars.qzfx.dr.urlObj['scdryulan'] + '/' + logId; //+ '?logId=' + logId
      } else {
        url =
          this.globalVars.qzfx.dr.urlObj['editdryulan'] +
          '/' +
          self.getDialogParams(self.dialog.dialog_qzfx, 'id'); //'?qzid=' + self.getDialogParams(self.dialog.dialog_qzfx, 'id')
      }
      // portrait
      this.$request({
        method: 'post',
        url: '/eda/portrait/export/abdteajzgjbxx',
        timeout: 0,
        data: {
          url: url,
          colnames:
            this.globalVars.qzfx.dr.columns[
              this.globalVars.qzfx.dr.fillUserType
            ],
          filename: '成功数据',
          sheetname: '成功数据',
          fileType: 'xlsx',
          queryModel: {
            order: '',
            querySetting: [],
            params: {
              field: 'page',
              fillCondition: this.globalVars.qzfx.dr.condition,
              fillConditionType: 'import',
              logId: logId,
            },
            pageSize: 99999,
          },
        },
      })
        .then(res => {
          let fileId = res.data.attachment;
          this.downloadFile({ fileId });
        })
        .catch(rej => {
          console.log('rej:', rej);
        });
    },
    /**下载结果 */
    downloadResult(logId) {
      this.$request({
        method: 'post',
        url: '/admin/imexport/portrait/getImportResult',
        params: {
          logId: logId,
          exeType: -1,
          filename: '失败数据',
        },
      })
        .then(res => {
          let fileId = res.datas.attachment;
          this.downloadFile({ fileId });
        })
        .catch(rej => {
          console.log('rej:', rej);
        });

      // DataIndexController.getImportResult(this, qs.stringify({
      //   logId: logId,
      //   exeType: -1,
      //   filename: '失败数据'
      // }), { headers: this.globalVars.qzfx.dr.newRequestHeaders }).then(res => {
      //   let fileId = res.datas.attachment
      //   this.downloadFile({ fileId })
      // }).catch(rej => {
      //   console.log("rej:", rej)
      // })
    },
    downloadFile(params) {
      this.$getFile('/admin/file/main/download', { fileId: params.fileId });
    },
    /**重新上传 */
    reUpload() {
      document.querySelector('.upload_person_file .el-upload__input').click();
    },
    importCheckPersonData(logId) {
      return this.$request({
        method: 'post',
        url: '/admin/imexport/portrait/checkData',
        timeout: 0,
        params: {
          logId: logId,
          importStrategyType: 'add',
          config:
            this.globalVars.qzfx.dr.templateKey[
              this.globalVars.qzfx.dr.fillUserType
            ],
          customParam: JSON.stringify({
            batchId: this.globalVars.qzfx.dr.batchId,
          }),
        },
      });

      // return DataIndexController.importCheckPersonData(this, qs.stringify({
      //   logId: logId,
      //   importStrategyType: 'add',
      //   config: this.globalVars.qzfx.dr.templateKey[this.globalVars.qzfx.dr.fillUserType],
      //   customParam: JSON.stringify({
      //     "batchId": this.globalVars.qzfx.dr.batchId
      //   }),
      // }), { headers: this.globalVars.qzfx.dr.newRequestHeaders, timeout: 0 })
    },
    preImport(data) {
      let fileId = data?.uploadFileInfos[0]?.fileId;

      return this.$request({
        method: 'post',
        url: '/admin/imexport/portrait/preImport',
        timeout: 0,
        params: {
          attachment: fileId,
          config:
            this.globalVars.qzfx.dr.templateKey[
              this.globalVars.qzfx.dr.fillUserType
            ],
          customParam: JSON.stringify({
            batchId: this.globalVars.qzfx.dr.batchId,
          }),
        },
      });

      // return DataIndexController.preImport(this, qs.stringify({
      //   attachment: fileId,
      //   config: this.globalVars.qzfx.dr.templateKey[this.globalVars.qzfx.dr.fillUserType],
      //   customParam: JSON.stringify({
      //     "batchId": this.globalVars.qzfx.dr.batchId
      //   }),
      // }), { headers: this.globalVars.qzfx.dr.newRequestHeaders, timeout: 0 })
    },
    performImport(logId) {
      return this.$request({
        method: 'post',
        url: '/admin/imexport/portrait/performImport',
        timeout: 0,
        params: {
          logId: logId,
          importStrategyType: 'add',
          config:
            this.globalVars.qzfx.dr.templateKey[
              this.globalVars.qzfx.dr.fillUserType
            ],
          customParam: JSON.stringify({
            batchId: this.globalVars.qzfx.dr.batchId,
          }),
        },
      });

      // return DataIndexController.performImport(this, qs.stringify({
      //   logId: logId,
      //   importStrategyType: 'add',
      //   config: this.globalVars.qzfx.dr.templateKey[this.globalVars.qzfx.dr.fillUserType],
      //   customParam: JSON.stringify({
      //     "batchId": this.globalVars.qzfx.dr.batchId
      //   }),
      // }), { headers: this.globalVars.qzfx.dr.newRequestHeaders, timeout: 0 })
    },
    hasFailDataEvent(data, logId) {
      /**弹窗提示失败数据 */
      let totalNum = 0;
      for (let key in data) {
        totalNum += data[key];
      }
      // 我这个不是导入到xml设置的模型表里，只是借用模型，去导入另一个表，另一个表里的数据本身就有重复的，插入是根据群组id来导入
      let title = `共解析出${totalNum}条数据，已成功导入${data.ignoreNumber}条，失败导入${data.errorNumber}条。`;
      this.$resConfirm(title, '导入提示', {
        confirmButtonText: '下载失败数据',
        type: 'warning',
        confirmButtonType: 'primary',
        dangerouslyUseHTMLString: true,
        center: false,
      })
        .then(res => {
          this.downloadResult(logId);
        })
        .catch(rej => {});
    },
    handleAvatarSuccess: async function (uploadRes, file) {
      /**设置默认图片 */
      try {
        this.globalVars.qzfx.dr.uploadLoading = false;
        if (uploadRes.code == 0) {
          this.globalVars.qzfx.dr.batchId = uploadRes.data.batchId;
          let preImportRes = await this.preImport(uploadRes.data);
          if (preImportRes.code != 0) {
            this.$message.error(preImportRes.msg);
            return;
          }
          let logId = preImportRes.datas.preImport.LOGID;
          this.globalVars.qzfx.dr.logId = logId;
          let checkDataRes = await this.importCheckPersonData(logId);
          if (checkDataRes.code != 0) {
            this.$message.error(checkDataRes.msg);
            return;
          }
          let performImportRes = await this.performImport(logId);
          if (performImportRes.code != 0) {
            this.$message.error(performImportRes.msg);
            return;
          }
          if (performImportRes.datas.performImport.errorNumber > 0) {
            this.hasFailDataEvent(performImportRes.datas.performImport, logId);
          }

          this.$message.success('导入成功');
          this.globalVars.qzfx.dr.uploadLoading = false;
          // this.$emit("input", this.globalVars.qzfx.dr.batchId)
          this.globalVars.qzfx.dr.tableParams.fillCondition =
            this.globalVars.qzfx.dr.batchId;
          // 加载表格组件
          this.globalVars.qzfx.dr.condition = true;
          // 预览导入
          this.globalVars.qzfx.dr.showList = true;
          // 初始化配置
          this.initTableConfig();
          this.changeFillUserType(this.globalVars.qzfx.dr.fillUserType, logId);
          // 调接口表格
          this.dataReload();
        }
      } catch (e) {
        if (typeof e && !e.code) {
          /**如果不是是调借口报错抛出的异常，就提示导入失败 */
          this.$message.error('导入失败');
        }
        console.log('上传报错：', e);
        this.globalVars.qzfx.dr.uploadLoading = false;
      }
      this.updateBatchId();
    },
    updateBatchId() {
      this.globalVars.qzfx.dr.templateBatchId = new Date().getTime();
    },
    beforeAvatarUpload(file) {
      const isXLSX = file.type.includes(
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
      const isLt = file.size / 1024 / 1024 < this.globalVars.qzfx.dr.limitSize;

      if (!isXLSX) {
        this.$message.error('只能上传xlsx文件');
      }
      if (!isLt) {
        this.$message.error(
          `上传图标大小不能超过 ${this.globalVars.qzfx.dr.limitSize}MB!`,
        );
      }
      let bool = isXLSX && isLt;
      if (bool) {
        this.globalVars.qzfx.dr.uploadLoading = true;
      }
      return isXLSX && isLt;
    },

    /* ------------------------------------------ 导入预览相关 ------------------------------------------ */
    /**初始化模型查询条件和数据查询地址 */
    changeFillUserType(val, logId) {
      this.globalVars.qzfx.dr.columnsModelObj[val].forEach(ele => {
        this.globalVars.qzfx.dr.showModel.push(ele.name);
      });
      this.globalVars.qzfx.dr.columnsModel =
        this.globalVars.qzfx.dr.columnsModelObj[val];
      if (val == 'teacher') {
        this.globalVars.qzfx.dr.tableDataConfig.url =
          this.globalVars.qzfx.dr.urlObj['scdryulan'] + '/' + logId; //'?logId=' + logId
        this.globalVars.qzfx.dr.modelParams.url =
          '/admin/model/design/portrait/perm/jzgemptymodel';
        this.globalVars.qzfx.dr.modelParams.id = 'portrait-jzgemptymodel';
        this.globalVars.qzfx.dr.modelParams.modelName = 'jzgemptymodel';
        this.globalVars.qzfx.dr.searchProps.modelParams.id = `portrait-jzgemptymodel`;
        this.globalVars.qzfx.dr.searchProps.modelParams.url = `/admin/model/design/portrait/perm/jzgemptymodel`;
      }
      this.globalVars.qzfx.dr.modelParams.modelApp = 'portrait';
    },
    /**初始化数据查询条件，数据查询地址在切换填报人员时调用 changeFillUserType 方法中修改 */
    initTableConfig() {
      if (
        this.globalVars.qzfx.dr.tableParams.fillConditionType == 'querySetting'
      ) {
        /**如果是条件配置的预览 */
        this.globalVars.qzfx.dr.tableDataConfig.querySetting =
          this.globalVars.qzfx.dr.tableParams.querySetting;
      } else {
        this.globalVars.qzfx.dr.tableDataConfig.querySetting = [];
      }
      // this.tableDataConfig.params.fillConditionType = this.tableParams.fillConditionType
      // this.tableDataConfig.params.fillCondition = this.tableParams.fillCondition
    },
    /**重新加载数据 */
    dataReload() {
      var self = this;
      setTimeout(() => {
        this.$nextTick(() => {
          console.log(
            '组件',
            self.$page('component_61s68ng6').$refs.table_preview,
          );
          this.$page('component_61s68ng6').$refs['table_preview'].reload();
        });
      }, 200);
    },
    /**表格表头的模型干预 */
    modelEdit(item) {
      if (!this.globalVars.qzfx.dr.showModel.includes(item.name)) {
        item['grid.hidden'] = 1;
      }
    },
    /**搜索条件的模型干预 */
    beforeRender(val) {
      val.hidden = 0;
      if (!this.globalVars.qzfx.dr.showModel.includes(val.name)) {
        val.hidden = 1;
      }
    },
    search(val) {
      var self = this;
      let logId = this.globalVars.qzfx.dr.logId;
      let querySetting = [];
      if (val) {
        val.forEach(function (item) {
          querySetting.push(item);
        });
      }
      querySetting.push({
        name: 'logId',
        value: logId,
        linkOpt: 'and',
        builder: 'equal',
        caption: '日志id',
        builder_display: '等于',
      });
      self
        .$getDialog('dialog_qzfx')
        .$setComProps('table_preview', querySetting);
      // this.globalVars.qzfx.dr.tableDataConfig.url = '/plugins/portrait/qzfx/previewImportTeacher' + '?logId=' + logId
      this.globalVars.qzfx.dr.querySetting = querySetting;
      this.$nextTick(() => {
        this.$page('component_61s68ng6').$refs['table_preview'].search(val);
      });
    },

    /**
     * 描述：选人组件选择事件
     * @param{event}  内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组) return:undefined

     */
    ev_getselected_uug8iu8o(event) {
      var self = this;
      console.log('选人组件', event);
      self.globalVars.qzfx.selectUserList = event.evArgs[0].map(x => x.userId);
    },

    /**
     * 描述：群组分析-弹窗-加载完成
     * @param{event}  name:事件名称,dialog:当前弹窗对象 return:undefined

     */
    mounted_dialog_6rm9x8ih(event) {
      var self = this;
      self.getTjCascaderData();
      this.$nextTick(() => {
        console.log('zujian:', this.$page('component_61s68ng6'));
        this.$page('component_61s68ng6').$refs.adv_search?.doReset();
      });
    },

    /**
     * 获取模型级联树
     */
    getTjCascaderData(func) {
      let url = '/plugins/portrait/qzfx/getAppModels';
      this.$request({
        method: 'post',
        url: url,
      })
        .then(res => {
          this.globalVars.qzfx.cascader = res.data;
          console.log('级联', this.globalVars.qzfx.cascader);
          if (func) {
            func();
          }
        })
        .catch(rej => {});
    },
    /**
     * 描述：条件查询预览-弹窗
     * @param{event}  name:事件名称,dialog:当前弹窗对象 return:undefined

     */
    mounted_dialog_2n0ywzz6: async function (event) {
      var self = this;
      // 清空条件预览高级搜索
      self.$page('adv-search_dv5tpccj')?.doReset();
    },

    /**
     * 描述：条件查询预览-弹窗
     * @param{modelItem}  对应列模型，可以修改模型数据；返回一个新的列模型 return:undefined

     */
    before_render_o1hs8a49(modelItem) {
      console.log('modelItem:', modelItem);
      var self = this;
      // if ((modelItem['grid.hidden'] !== undefined && modelItem['grid.hidden'] === 0) || (modelItem['grid.hidden'] === undefined && modelItem['hidden'] === 0)) {
      //   self.globalVars.$index++
      // }
      if (modelItem.name == 'ssjg') {
        modelItem.hidden = 1;
      }
      if (self.globalVars.isSelColumn == 1) {
        if (modelItem.name == 'zgh' || modelItem.name == 'xm') {
          modelItem.hidden = 1;
        }
      }
      if (modelItem.name == 'txCustom') {
        modelItem['fixed'] = 'left';
        if (self.globalVars.isSelColumn == 0) {
          modelItem.caption = '头像';
          modelItem['grid.template'] = `
                    <div class="teacher_content_gthx">
                      <div class="teacher_avater">
                        <img v-if="row['avatar']" :src="row['avatar']">
                        <span v-else>{{row['xm'].slice(-2)}}</span>
                      </div>
                    </div>`;
        } else {
          modelItem.caption = '教师';
          modelItem['grid.template'] = `
                    <div class="teacher_content_gthx">
                      <div class="teacher_avater">
                        <img v-if="row['avatar']" :src="row['avatar']">
                        <span v-else>{{row['xm'].slice(-2)}}</span>
                      </div>
                      <div class="teacher_info">
                        <div class="teacher_name">
                          {{row['xm']}}
                        </div>
                        <div class="teacher_gh">
                          {{row['zgh']}}
                        </div>
                      </div>
                    </div>`;
        }
      }
    },

    /**
     * 描述：条件设计器change
     * @param{event}  内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组) return:undefined

     */
    ev_change_y8xokxx6(event) {
      var self = this;
      console.log('条件设计器change', event);
    },

    /**
     * 描述：群组详情-弹窗-加载完成
     * @param{event}  name:事件名称,dialog:当前弹窗对象 return:undefined

     */
    mounted_dialog_z8s5nvgr(event) {
      var self = this;
      // 群组成员高级搜索clear
      self.$page('adv-search_pp1qj0kk')?.doReset();
    },

    /**
     * 描述：条件预览-弹窗-卸载
     * @param{event}  name:事件名称,dialog:当前弹窗对象 return:undefined

     */
    destroy_dialog_a9rigz2d(event) {
      var self = this;
      self.$page('adv-search_dv5tpccj')?.doReset();
    },

    setCssStr(minWidth, width, mediaStr = 'min-width', startNum) {
      return `
        @media screen and (${mediaStr}: ${minWidth}px) {
            /* 当屏幕宽度小于或等于 1560px 或 设备是横向显示时，应用此样式 */
            /* .qzxq-qzcy-container {
              .true_container{
                .item{
                  width: ${width};
                }
              }
            } */
            .qzxq-qzcy-container .true_container .item{
              width: ${width};
            }
           /* .qzxq-qzcy-container .true_container .item:nth-child(${startNum}n){
              margin-right: 0px;
            }*/
          }
      `;
    },
    setMediaCss() {
      let self = this;
      let css = '',
        startWidth = 1366,
        step = 296,
        cssNum = 15,
        startNum = 4;
      for (let i = 0; i <= cssNum; i++) {
        /**m每增加一个单位的step，每行数量减1 */
        let widthStr = `calc(${self.keepOneDecimalDown(
          100 / (startNum + i),
        )}% - 16px)`;
        if (i == cssNum) {
          widthStr = '204px';
        }
        if (i == 0) {
          /**第一个长度需要设置最大宽度 */
          let startWidthStr = self.setCssStr(
            startWidth,
            widthStr,
            'max-width',
            startNum + i,
          );
          css = css + startWidthStr;
        }
        let currCss = self.setCssStr(
          startWidth + step * i,
          widthStr,
          'min-width',
          startNum + i,
        );
        css = css + currCss;
      }
      const style = document.createElement('style');
      style.id = 'customCssmedia';
      style.appendChild(document.createTextNode(css));
      document.head.appendChild(style);
    },
    keepOneDecimalDown(num) {
      return Math.floor(num * 10) / 10;
    },
  };
});
