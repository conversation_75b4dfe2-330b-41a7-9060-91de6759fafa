{"list": [{"isRoot": true, "type": "row", "comType": "page", "__tree_label": "页面", "__tree_node_key": "5990334779183806", "key": "page_w2dp1ixa", "uuid": "uuid_u3gvrzsh", "children": [{"__tree_node_key": "5012176969809021", "key": "row_wt7wequ9", "__tree_label": "区域容器", "comType": "row", "type": "row", "isCover": false, "children": [{"__tree_node_key": "2057175601722916", "key": "col_94vog49g", "__tree_label": "区块容器", "comType": "col", "type": "col", "isCover": false, "children": [{"__tree_node_key": "6045347578689413", "key": "row_lt5zj5ni", "__tree_label": "区域容器", "comType": "row", "type": "row", "isCover": false, "children": [{"__tree_node_key": "17797136750325482", "key": "col_e3rjgvpa", "__tree_label": "区块容器", "comType": "col", "type": "col", "isCover": false, "children": [{"uuid": "uuid_bm5egj7n", "key": "button_1mawn2uw", "com": "res-button", "comType": "button", "icon": "pm-icon-btns", "comClassify": "form", "options": {"readonly": false, "size": "", "customClass": "", "disabled": false, "labelWidth": 100, "isLabelWidth": false, "hideLabel": true, "hidden": false, "dataBind": false, "type": "primary", "plain": false, "round": false, "circle": false, "buttonName": "新增群组", "noMoreClick": true, "dashed": false, "icon": "el-icon-plus"}, "events": {"mounted": {"params": [{"name": "event", "des": "内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组)"}]}, "beforeDestroy": {}, "click": {"name": "ev_click_m87k3xfd", "enName": "新增群组"}}, "name": "按钮", "isCover": false, "dataOptions": {"remote": false, "remoteType": "dataset", "remoteModel": "", "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}}, "interactList": [], "comStyles": {"display": "inline-block", "marginBottom": "16", "float": "left"}, "__tree_label": "res-button", "pageCode": "qzfx"}, {"uuid": "uuid_m8sbs6vf", "key": "fuzzy-search_tib2i70n", "com": "fuzzy-search", "comType": "fuzzy-search", "icon": "pm-icon-adv-search", "comClassify": "model", "options": {"readonly": false, "size": "small", "width": 0, "mode": "", "hotMax": 5, "modeType": "", "round": false, "isSearchBtn": true, "isIcon": true, "btnColor": "light", "interacTable": "", "hidden": false, "readyDoSearch": false, "undefined": ""}, "events": {"mounted": {"params": [{"name": "event", "des": "内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组)"}]}, "beforeDestroy": {}, "search": {"name": "ev_enter_az0gvczs", "enName": ""}}, "dataOptions": {"remote": false, "remoteType": "model", "remoteModel": {"actionType": "search"}, "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "prefix": "search", "dataSource": {"type": "in", "url": "", "code": "", "params": {}}}, "name": "模糊搜索", "isCover": false, "interactList": [], "comStyles": {"marginBottom": "0", "float": "right"}, "__tree_label": "fuzzy-search", "pageCode": "qzfx"}], "options": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "uuid": "uuid_8syydfbx", "pageCode": "qzfx"}], "options": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "showTitle": false, "title": "容器标题", "layout": "grid", "uuid": "uuid_fjvdbl7b", "pageCode": "qzfx"}, {"__tree_node_key": "944124670675291", "key": "row_jpve95kv", "__tree_label": "区域容器", "comType": "row", "type": "row", "isCover": false, "children": [{"__tree_node_key": "9959685525514408", "key": "col_f1poquag", "__tree_label": "区块容器", "comType": "col", "type": "col", "isCover": false, "children": [{"uuid": "uuid_y2h9dcy1", "key": "component_rz4il1ja", "com": "res-com-template", "comType": "component", "icon": "pm-icon-vue", "options": {"readonly": false, "customClass": "", "labelWidth": 100, "isLabelWidth": false, "hidden": false, "template": "<div class=\"container\" v-loading=\"globalVars.qzfxview.loading\" element-loading-text=\"拼命加载中\"\r\n  element-loading-spinner=\"el-icon-loading\" element-loading-background=\"white\">\r\n  <div class=\"item\" v-for=\"(item,index) in globalVars.qzfxview.tableData\">\r\n    <div class=\"item-top\">\r\n      <div class=\"div1\">\r\n        <div v-html=\"page.genIcon(item.qztb)\"></div>\r\n        <!-- <span class=\"adiconfont icon admin-icon-Filters-\"style=\"background:red\"></span> -->\r\n        <!-- <i class=\"admin-icon-Filters-\" class=\"icon\" :style=\"{background:globalVars.qzfxview.colors[index%globalVars.qzfxview.colors.length]}\"></i> -->\r\n        <!-- <i :class=\"item.qztb\" class=\"icon\" :style=\"{background:globalVars.qzfxview.colors[index%globalVars.qzfxview.colors.length]}\"></i> -->\r\n        <div class=\"qzmc-jscount-div\">\r\n          <span class=\"qzmc\">{{item.qzmc}}</span>\r\n          <span class=\"jscount\" :class=\"page.renderJsCount(item.teacherCount)\">{{page.formatNumber(item.teacherCount)}} 位教师</span>\r\n        </div>\r\n        <span v-if=\"item.xtyz==='1'\" class=\"xtyz\">系统</span>\r\n      </div>\r\n      <div class=\"div2\">\r\n        <span class=\"label\">分析看板：</span><span>{{item.fxkb==='1'?'已配置':'未配置'}}</span>\r\n      </div>\r\n      <div class=\"div3\">\r\n        <span class=\"label\">常用群组：</span>\r\n        <res-switch v-model=\"item.cyqz\" active-text=\"\" inactive-text=\"\" active-color=\"#165dff\" inactive-color=\"#C9CDD4\"\r\n          active-value=\"1\" inactive-value=\"0\" @change=\"(val)=>page.cyqzChange(val,item.id)\"\r\n          :disabled=\"item.fxkb==='0'||item.xtyz==='1'\">\r\n        </res-switch>\r\n      </div>\r\n    </div>\r\n    <div class=\"item-down\">\r\n      <div class=\"btn-list\">\r\n        <div class=\"btn-text\" @click=\"page.openTabQzfx(item.id,index)\">分析</div>\r\n        <div class=\"btn-text btn-zj-border\" @click=\"page.openQzxq(item.id,index)\">详情</div>\r\n        <div class=\"btn-text\" @click=\"page.openEditQzfx(item)\">编辑</div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>", "required": false, "pattern": "", "validator": ""}, "dataOptions": {"remote": true, "remoteType": "dataset", "remoteModel": "", "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "dataSource": {"type": "custom", "params": {}, "code": "", "url": ""}}, "events": {"mounted": "", "beforeDestroy": "", "click": "", "change": "", "input": ""}, "name": "vue模板", "isCover": false, "interactList": [], "comStyles": {"marginTop": "0", "marginBottom": "0"}, "__tree_label": "res-com-template", "pageCode": "qzfx"}], "options": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "uuid": "uuid_2cnq5jyy", "pageCode": "qzfx"}], "options": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false, "bgType": "bgcolor", "backgroundColor": "", "borderRadius": 0}, "showTitle": false, "title": "容器标题", "layout": "grid", "uuid": "uuid_kytp4d5j", "pageCode": "qzfx"}, {"uuid": "uuid_j9921arc", "key": "empty_o3bdhrcm", "com": "res-empty", "comType": "empty", "icon": "pm-icon-upload", "comClassify": "com", "dataType": "string", "options": {"readonly": false, "img": "noData", "title": "暂无数据", "desc": "", "btnType": "btn", "getFileUrl": "", "bindCom": {}, "buttonList": [], "hidden": true}, "events": {"mounted": "", "beforeDestroy": ""}, "name": "空状态", "isCover": false, "dataOptions": {"remote": false, "remoteType": "dataset", "remoteModel": "", "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}}, "interactList": [], "comStyles": {"marginTop": "0", "marginBottom": "0"}, "__tree_label": "res-empty", "pageCode": "qzfx"}], "options": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "uuid": "uuid_999t5nih", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 12, "marginLeft": 0, "marginRight": 0, "boxShadow": "0 0 0 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": false, "paddingTop": "0", "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false, "bgType": "bgcolor", "backgroundColor": "", "borderRadius": 0}, "showTitle": false, "title": "容器标题", "layout": "grid", "uuid": "uuid_knwtdz9q", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 12, "marginLeft": 0, "marginRight": 0, "widthType": "0", "maxWidth": 0, "minWidth": 0, "width": "260px"}, "pageCode": "qzfx"}], "leftList": [{"isRoot": true, "type": "row", "comType": "page", "__tree_label": "页面", "__tree_node_key": "03154181182718685", "key": "page_prxg6tjt", "uuid": "uuid_en1nwhqr", "children": [], "pageCode": "qzfx"}], "rightList": [{"isRoot": true, "type": "row", "comType": "page", "__tree_label": "页面", "__tree_node_key": "021982485371449645", "key": "page_cks5prmx", "uuid": "uuid_tnb6u9pl", "children": [], "pageCode": "qzfx"}], "config": {"dataSource": [{"type": "fetch", "isInit": true, "isEncrypt": false, "options": {"source": "api", "params": {}, "method": "POST", "isCors": true, "timeout": 5000, "headers": {}, "uri": "portrait_qzfx_page_reviewTeacher"}, "id": "api_0l7kqm1l", "name": "qzfx_page_reviewTeacher", "label": "群组分析-page-预览教师", "responseFunc": "", "requestFunc": "", "errorFunc": ""}], "platform": "pc", "layout": "default", "leftWidth": 260, "rightWidth": 260}, "dialogJson": [{"__tree_node_key": "02869296807792665", "key": "dialog_qzfx", "__tree_label": "区域容器", "comType": "row", "type": "row", "children": [{"__tree_node_key": "40640575278978464", "key": "col_9bzudcue", "__tree_label": "区块容器", "comType": "col", "type": "col", "children": [{"__tree_node_key": "7902581782831639", "key": "row_j8tli9xd", "__tree_label": "区域容器", "comType": "row", "type": "row", "children": [{"__tree_node_key": "13614708892846683", "key": "col_b40qtt53", "__tree_label": "区块容器", "comType": "col", "type": "col", "children": [{"uuid": "uuid_d40i99oo", "key": "title_1usp1yha", "com": "res-title", "comType": "title", "icon": "pm-icon-title", "comClassify": "com", "dataType": "string", "options": {"readonly": false, "showIcon": true, "icon": "", "AfterIcon": "", "title": "基本信息", "titleSize": 16, "desc": "", "bindCom": {}, "hidden": false, "anchorLevel": 1, "enabledAnchor": false, "undefined": ""}, "events": {"mounted": "", "beforeDestroy": ""}, "name": "基本信息", "isCover": false, "dataOptions": {"remote": true, "remoteType": "dataset", "remoteModel": "", "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "dataSource": {"type": "", "url": "", "code": "", "params": {}, "querySetting": []}}, "interactList": [], "comStyles": {"marginBottom": "15"}, "__tree_label": "res-title", "pageCode": "qzfx"}, {"uuid": "uuid_l3fxat7d", "key": "data-form_qzfx", "com": "data-form", "comType": "data-form", "icon": "pm-icon-mode-form", "comClassify": "model", "dataType": "object", "options": {"readonly": false, "width": 0, "formWidth": 0, "openGroup": true, "showLabel": true, "labelWidth": 70, "labelPosition": "left", "labelShowType": "default", "tableForm": false, "column": 2, "autoLoad": false, "size": "", "hidden": false, "disabled": false, "groupType": "default", "hideNewFields": true, "isPrint": false, "validateGroup": "", "beforeRender": {}, "beforeRules": {}, "tipRight": false, "anchorLevel": 1, "enabledAnchor": false, "undefined": "", "comLinkEnabled": true, "titleSize": 16, "showIcon": "true", "labelSup": "", "modelSort": {}, "disDictRequest": false}, "events": {"mounted": "", "beforeDestroy": "", "inited": "", "item-change": "", "dict-loaded": "", "dict-change": ""}, "dataOptions": {"remote": true, "remoteType": "model", "remoteModel": {"actionType": "form", "id": "portrait-portraitgroup", "url": "/admin/model/design/portrait/perm/portraitgroup", "modelCascades": "", "modelName": "portraitgroup", "modelApp": "portrait"}, "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "prefix": "form", "dataSource": {"type": "in", "url": "", "code": "", "dataPath": "", "params": {}}, "columnsModel": [{"name": "qzmc"}, {"name": "qztb"}, {"name": "qzms"}, {"name": "qqfs"}, {"name": "tj"}, {"name": "cyqz"}, {"name": "fxkb"}, {"name": "xtyz"}, {"name": "id"}, {"name": "createBy"}, {"name": "createTime"}, {"name": "updateBy"}, {"name": "updateTime"}, {"name": "teacherCount"}, {"name": "sql"}, {"name": "logId"}], "modelConfig": {"qzmc": {"form.comName": "", "placeholder": "请输入", "start-placeholder": "", "end-placeholder": "", "form.hidden": 0, "form.omitted": 0, "form.readonly": 0}, "qztb": {"form.comName": "", "form.hidden": 0, "form.omitted": 0, "form.readonly": 0, "placeholder": "请输入", "start-placeholder": "", "end-placeholder": "", "form.template_edit": "<template v-if=\"globalVars.qzfx.edit\">\r\n    <!-- 编辑 -->\r\n    <!-- <i class=\"el-icon-delete\"></i> -->\r\n    <!-- <div v-html=\"page.genIcon(value)\">\r\n    </div> -->\r\n    <!-- <template v-html=\"page.genIcon(value)\">\r\n    </template> -->\r\n    <select-icon-pop iconSize=\"40\" :icon.sync=\"globalVars.qzfx.qztb.pageIcon\" :iconColor.sync=\"globalVars.qzfx.qztb.pageIconColor\"\r\n        :customIcon.sync=\"globalVars.qzfx.qztb.customPageIcon\" :iconBgColor.sync=\"globalVars.qzfx.qztb.pageIconBgColor\"\r\n        :isCustom=\"false\">\r\n    </select-icon-pop>\r\n</template>\r\n<template v-else>\r\n    <!-- 新增 -->\r\n    <select-icon-pop iconSize=\"40\" iconSize=\"40\" :icon.sync=\"value\" :iconColor.sync=\"globalVars.qzfx.qztb.pageIconColor\"\r\n        :customIcon.sync=\"globalVars.qzfx.qztb.customPageIcon\" :iconBgColor.sync=\"globalVars.qzfx.qztb.pageIconBgColor\"\r\n        :isCustom=\"false\">\r\n    </select-icon-pop>\r\n</template>", "form.template": ""}, "qzms": {"form.comName": "", "form.xtype": "textarea", "form.hidden": 0, "form.omitted": 0, "form.readonly": 0, "form.JSONParam": {"isOver": true}, "placeholder": "请输入", "start-placeholder": "", "end-placeholder": ""}, "qqfs": {"form.comName": "", "form.hidden": 1}, "tj": {"form.comName": "", "form.hidden": 1}, "cyqz": {"form.comName": "", "form.hidden": 1}, "fxkb": {"form.comName": "", "form.hidden": 1}, "xtyz": {"form.comName": "", "form.hidden": 1}, "id": {"form.comName": ""}, "createBy": {"form.comName": ""}, "createTime": {"form.comName": ""}, "updateBy": {"form.comName": ""}, "updateTime": {"form.comName": ""}, "teacherCount": {"form.comName": "", "form.hidden": 1, "form.omitted": 0, "form.readonly": 0}, "sql": {"form.comName": "", "placeholder": "请输入", "start-placeholder": "", "end-placeholder": "", "form.hidden": 1, "form.omitted": 0, "form.readonly": 0}, "logId": {"form.comName": "", "placeholder": "请输入", "start-placeholder": "", "end-placeholder": "", "form.hidden": 1, "form.omitted": 0, "form.readonly": 0}}}, "name": "模型表单", "isCover": false, "interactList": [], "comStyles": {"marginBottom": "15"}, "__tree_label": "data-form", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "events": {"mounted": "", "destroy": ""}, "uuid": "uuid_gip35oge", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false, "bgType": "bgcolor", "backgroundColor": "", "borderRadius": 0}, "showTitle": false, "title": "容器标题", "events": {"mounted": "", "destroy": ""}, "layout": "grid", "uuid": "uuid_exx9b1vu", "pageCode": "qzfx"}, {"__tree_node_key": "48711711866344864", "key": "row_9ulwhsx6", "__tree_label": "区域容器", "comType": "row", "type": "row", "children": [{"__tree_node_key": "45639206235255037", "key": "col_43gq7bsa", "__tree_label": "区块容器", "comType": "col", "type": "col", "children": [{"__tree_node_key": "7754230467989764", "key": "row_0supaals", "__tree_label": "区域容器", "comType": "row", "type": "row", "children": [{"__tree_node_key": "5649057277301845", "key": "col_taqiwdbk", "__tree_label": "区块容器", "comType": "col", "type": "col", "children": [{"uuid": "uuid_239o6sqs", "key": "title_t91ydy6q", "com": "res-title", "comType": "title", "icon": "pm-icon-title", "comClassify": "com", "dataType": "string", "options": {"readonly": false, "showIcon": true, "icon": "", "AfterIcon": "", "title": "群组成员", "titleSize": 16, "desc": "", "bindCom": {}, "hidden": false, "anchorLevel": 1, "enabledAnchor": false, "undefined": ""}, "events": {"mounted": "", "beforeDestroy": ""}, "name": "群组成员", "isCover": false, "dataOptions": {"remote": false, "remoteType": "dataset", "remoteModel": "", "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}}, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "res-title", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": "20", "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "events": {"mounted": "", "destroy": ""}, "uuid": "uuid_jlkmj0l3", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false, "bgType": "bgcolor", "backgroundColor": "", "borderRadius": 0}, "showTitle": false, "title": "容器标题", "events": {"mounted": "", "destroy": ""}, "layout": "grid", "uuid": "uuid_rh7eofzb", "pageCode": "qzfx"}, {"__tree_node_key": "5855382219234657", "key": "row_8fxmduo8", "__tree_label": "区域容器", "comType": "row", "type": "row", "children": [{"__tree_node_key": "765774660610044", "key": "col_ty0fb1oy", "__tree_label": "区块容器", "comType": "col", "type": "col", "children": [{"uuid": "uuid_b6e9cglb", "key": "text_p6juvfzm", "com": "res-text", "comType": "text", "icon": "pm-icon-text", "comClassify": "form", "options": {"readonly": false, "textContent": "*", "textType": "text", "customClass": "", "labelWidth": 100, "isLabelWidth": false, "hidden": false, "required": false, "description": "", "comStyle": {"fontSize": "14px", "backgroundColor": "", "color": "red", "marginLeft": "2px"}}, "events": {"mounted": "", "beforeDestroy": ""}, "name": "文字", "isCover": false, "dataOptions": {"remote": false, "remoteType": "dataset", "remoteModel": "", "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}}, "interactList": [], "comStyles": {"display": "inline-block", "marginBottom": "0", "marginLeft": "", "paddingLeft": "0", "left": "10"}, "__tree_label": "res-text", "pageCode": "qzfx"}, {"uuid": "uuid_6gjkzhau", "key": "text_fg7370b8", "com": "res-text", "comType": "text", "icon": "pm-icon-text", "comClassify": "form", "options": {"readonly": false, "textContent": "圈群方式", "textType": "text", "customClass": "", "labelWidth": 100, "isLabelWidth": false, "hidden": false, "required": false, "description": "", "comStyle": {"fontSize": "14px", "backgroundColor": "", "color": "", "marginLeft": "2px"}}, "events": {"mounted": "", "beforeDestroy": ""}, "name": "文字", "isCover": false, "dataOptions": {"remote": false, "remoteType": "dataset", "remoteModel": "", "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}}, "interactList": [], "comStyles": {"display": "inline-block", "marginBottom": "0", "marginLeft": ""}, "__tree_label": "res-text", "pageCode": "qzfx"}, {"uuid": "uuid_32n7whzb", "key": "radio_0ik3w8l4", "com": "res-radio", "comType": "radio", "icon": "pm-icon-radiobox", "comClassify": "form", "dataType": "string", "options": {"readonly": false, "initWidth": 200, "layout": "", "autoLoad": true, "size": "", "defaultValue": "", "showLabel": false, "border": false, "isButton": false, "styleType": "style1", "enableAll": false, "options": [{"value": "qb", "label": "全部人员"}, {"value": "tj", "label": "设置条件"}, {"value": "xz", "label": "直接选择"}, {"value": "dr", "label": "导入"}], "valueKey": "value", "labelKey": "label", "required": false, "requiredMessage": "", "validatorCheck": false, "validator": "", "width": 0, "customClass": "", "labelWidth": 100, "isLabelWidth": false, "hidden": false, "disabled": false, "description": ""}, "dataOptions": {"remote": true, "remoteType": "dict", "remoteModel": "", "remoteDict": {"dictCode": "", "params": {}, "querySetting": [], "fields": {"label": "itemName", "value": "itemId"}}, "dataSource": {"type": "", "url": "", "code": "", "params": {}, "querySetting": []}}, "events": {"mounted": {"params": [{"name": "event", "des": "内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组)"}]}, "beforeDestroy": {}, "change": {"name": ""}, "value-change": {"name": "ev_value_change_jh8dnxm9", "enName": ""}}, "name": "单选框组", "isCover": false, "interactList": [], "comStyles": {"display": "inline-block", "marginBottom": "0", "marginLeft": "", "paddingLeft": "0"}, "__tree_label": "res-radio", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": "20", "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "events": {"mounted": "", "destroy": ""}, "uuid": "uuid_wpstjr5y", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false, "bgType": "bgcolor", "backgroundColor": "", "borderRadius": 0}, "showTitle": false, "title": "容器标题", "events": {"mounted": "", "destroy": ""}, "layout": "grid", "uuid": "uuid_52lmb8lk", "pageCode": "qzfx"}, {"__tree_node_key": "26188395919180163", "key": "row_aw6l7m1y", "__tree_label": "区域容器", "comType": "row", "type": "row", "children": [{"__tree_node_key": "37384187143871106", "key": "col_z637jskk", "__tree_label": "区块容器", "comType": "col", "type": "col", "children": [{"__tree_node_key": "9470952885105735", "key": "row_gl5keu6c", "__tree_label": "区域容器", "comType": "row", "type": "row", "children": [{"__tree_node_key": "5241205197235967", "key": "col_pl6yfm9c", "__tree_label": "区块容器", "comType": "col", "type": "col", "children": [{"uuid": "uuid_j1sqpbjw", "key": "text_jbeoi6vh", "com": "res-text", "comType": "text", "icon": "pm-icon-text", "comClassify": "form", "options": {"readonly": false, "textContent": "*", "textType": "text", "customClass": "", "labelWidth": 100, "isLabelWidth": false, "hidden": false, "required": false, "description": "", "comStyle": {"fontSize": "14px", "backgroundColor": "", "color": "red", "marginLeft": "2px"}}, "events": {"mounted": "", "beforeDestroy": ""}, "name": "文字", "isCover": false, "dataOptions": {"remote": false, "remoteType": "dataset", "remoteModel": "", "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}}, "interactList": [], "comStyles": {"marginBottom": "0", "float": "left"}, "__tree_label": "res-text", "pageCode": "qzfx"}, {"uuid": "uuid_b9m0rpya", "key": "text_h5zhod3k", "com": "res-text", "comType": "text", "icon": "pm-icon-text", "comClassify": "form", "options": {"readonly": false, "textContent": "设置条件", "textType": "text", "customClass": "", "labelWidth": 100, "isLabelWidth": false, "hidden": false, "required": false, "description": "", "comStyle": {"fontSize": "14px", "backgroundColor": "", "color": "", "marginLeft": "2px"}}, "events": {"mounted": "", "beforeDestroy": ""}, "name": "文字", "isCover": false, "dataOptions": {"remote": false, "remoteType": "dataset", "remoteModel": "", "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}}, "interactList": [], "comStyles": {"marginBottom": "0", "float": "left"}, "__tree_label": "res-text", "pageCode": "qzfx"}, {"uuid": "uuid_z9pnavd5", "key": "button_82nbz4ol", "com": "res-button", "comType": "button", "icon": "pm-icon-btns", "comClassify": "form", "options": {"readonly": false, "size": "", "customClass": "", "disabled": false, "labelWidth": 100, "isLabelWidth": false, "hideLabel": true, "hidden": false, "dataBind": false, "type": "text", "plain": false, "round": false, "circle": false, "buttonName": "预览", "noMoreClick": true, "dashed": false}, "events": {"mounted": {"params": [{"name": "event", "des": "内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组)"}]}, "beforeDestroy": {}, "click": {"name": "ev_click_ci8d1g6a", "enName": "群组分析弹窗-设置条件-预览"}}, "name": "按钮", "isCover": false, "dataOptions": {"remote": false, "remoteType": "dataset", "remoteModel": "", "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}}, "interactList": [], "comStyles": {"marginBottom": "0", "float": "right"}, "__tree_label": "res-button", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "events": {"mounted": "", "destroy": ""}, "uuid": "uuid_7zwseqqa", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "showTitle": false, "title": "容器标题", "events": {"mounted": "", "destroy": ""}, "layout": "grid", "uuid": "uuid_g54s8b3d", "pageCode": "qzfx"}, {"__tree_node_key": "9888703547797622", "key": "row_57lcthpi", "__tree_label": "区域容器", "comType": "row", "type": "row", "children": [{"__tree_node_key": "4731120947675649", "key": "col_0o2v3aa8", "__tree_label": "区块容器", "comType": "col", "type": "col", "children": [{"uuid": "uuid_90qybvfh", "key": "component_eom74ped", "com": "res-com-template", "comType": "component", "icon": "pm-icon-vue", "options": {"readonly": false, "customClass": "", "labelWidth": 100, "isLabelWidth": false, "hidden": false, "template": "<!-- 条件编辑或新增 -->\r\n<div>\r\n    <adv-filter-2 ref=\"advFilter2Ref\" :cascader=\"globalVars.qzfx.cascader\"\r\n        :advQuerySetting=\"globalVars.qzfx.tjQuerySettingJson\" />\r\n</div>", "required": false, "pattern": "", "validator": ""}, "dataOptions": {"remote": true, "remoteType": "dataset", "remoteModel": "", "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "dataSource": {"type": "custom", "params": {}, "code": "", "url": ""}}, "events": {"mounted": {"params": [{"name": "event", "des": "内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组)"}]}, "beforeDestroy": {}, "click": {}, "change": {"name": "ev_change_y8xokxx6", "enName": "条件设计器change"}, "input": {}}, "name": "vue模板", "isCover": false, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "res-com-template", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "events": {"mounted": "", "destroy": ""}, "uuid": "uuid_p14j2r4b", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "showTitle": false, "title": "容器标题", "events": {"mounted": "", "destroy": ""}, "layout": "grid", "uuid": "uuid_e1cydyzd", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "events": {"mounted": "", "destroy": ""}, "uuid": "uuid_1qlur96n", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "showTitle": false, "title": "容器标题", "events": {"mounted": "", "destroy": ""}, "layout": "grid", "uuid": "uuid_y1o9a31y", "pageCode": "qzfx"}, {"__tree_node_key": "8018762894911744", "key": "row_5po5gyz9", "__tree_label": "区域容器", "comType": "row", "type": "row", "children": [{"__tree_node_key": "7257536806423239", "key": "col_i6kp93mc", "__tree_label": "区块容器", "comType": "col", "type": "col", "children": [{"uuid": "uuid_sta7vdvl", "key": "we-select-person_yg8h5s1f", "com": "we-select-person", "comType": "we-select-person", "comClassify": "com", "icon": "pm-icon-step", "options": {"readonly": false, "pageSize": 10, "showSelect": true, "collapseTags": true, "selectWidth": "300px", "dialogTitle": "选择人员", "dialogWidth": "90%", "type": "checkbox", "showDepartment": true, "withoutDialog": true, "comHeight": "25rem", "pagerCount": 5, "small": false, "layout": "total, prev, pager, next", "dataLoadFunc": {}}, "events": {"mounted": {"params": [{"name": "event", "des": "内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组)"}]}, "beforeDestroy": {}, "getselected": {"name": "ev_getselected_uug8iu8o", "enName": "选人组件选择事件"}, "getSelectUserid": ""}, "dataOptions": {"remote": true, "remoteType": "", "remoteModel": "", "remoteDict": {"dictCode": "", "params": {}, "querySetting": []}, "dataSource": {"type": "custom", "url": "/plugins/portrait/qzfx/getLastTeacher", "code": "", "params": {}, "querySetting": []}, "dataProps": {"tableth": {"userName": "姓名", "userId": "工号", "deptName": "组织部门"}, "tabletdkey": {"userName": "showName", "userId": "userId", "deptName": "deptIdDictMap.name"}}}, "name": "选人", "isCover": false, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "we-select-person", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "events": {"mounted": "", "destroy": ""}, "uuid": "uuid_pcp8hibf", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false, "bgType": "bgcolor", "backgroundColor": "", "borderRadius": 0}, "showTitle": false, "title": "容器标题", "events": {"mounted": "", "destroy": ""}, "layout": "grid", "uuid": "uuid_zein75e5", "pageCode": "qzfx"}, {"__tree_node_key": "5803602066589042", "key": "row_060u7p9o", "__tree_label": "区域容器", "comType": "row", "type": "row", "children": [{"__tree_node_key": "12319182488621627", "key": "col_zus02oip", "__tree_label": "区块容器", "comType": "col", "type": "col", "children": [{"uuid": "uuid_6jh8bb6q", "key": "component_61s68ng6", "com": "res-com-template", "comType": "component", "icon": "pm-icon-vue", "options": {"readonly": false, "customClass": "", "labelWidth": 100, "isLabelWidth": false, "hidden": false, "template": "<!-- 导入 -->\r\n<div class=\"condition_upload_qzfx_container\">\r\n  <!-- 为了保证数据顺利导入，请下载 -->\r\n  <div class=\"person_container\">\r\n    <div class=\"person_top\">\r\n      <div>\r\n        <span class=\"person_title\">\r\n          人员列表\r\n        </span>\r\n        <span class=\"template_file\">\r\n          为了保证数据顺利导入，请下载 <el-button type=\"text\" @click=\"page.downLoadTemplate\">模板文件</el-button>\r\n        </span>\r\n      </div>\r\n      <!-- 加载 {{globalVars.qzfx.dr.uploadLoading}} -->\r\n      <!-- <div class=\"person_title\" @click=\"page.importCheckPersonData\">\r\n        人员列表\r\n        <div class=\"template_file\">\r\n        </div>\r\n      </div> -->\r\n      <div class=\"person_result\">\r\n        <div class=\"result\" v-if=\"globalVars.qzfx.dr.fillUserType == 'teacher'\">\r\n          <el-button type=\"text\" @click=\"page.downloadSuccessFile\">下载</el-button>\r\n          <el-button type=\"text\" @click=\"page.reUpload\">重新上传</el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"person_content\" v-loading=\"globalVars.qzfx.dr.uploadLoading\" element-loading-text=\"导入中，请耐心等待...\">\r\n      <div v-show=\"!globalVars.qzfx.dr.condition\">\r\n        <el-upload :on-success=\"page.handleAvatarSuccess\" :before-upload=\"page.beforeAvatarUpload\"\r\n          class=\"upload_person_file\" drag :show-file-list=\"false\" :data=\"{\r\n              batchId: globalVars.qzfx.dr.templateBatchId,\r\n            }\" name=\"uploadFiles\" :headers=\"globalVars.qzfx.dr.headers\" :action=\"globalVars.qzfx.dr.uploadUrl\"\r\n          :accept=\"globalVars.qzfx.dr.acceptType.join()\" multiple>\r\n          <i class=\"el-icon-upload\"></i>\r\n          <div class=\"el-upload__text\">\r\n            <div>\r\n              将文件拖到此处，或<el-link type=\"text\">点击上传</el-link>\r\n            </div>\r\n            <div class=\"limit\">只能上传不超过{{globalVars.qzfx.dr.limitSize}}MB的 .xlsx 文件</div>\r\n          </div>\r\n        </el-upload>\r\n      </div>\r\n      <div v-if=\"globalVars.qzfx.dr.condition\">\r\n        <div class=\"components_adv_search_select_data\" v-if=\"globalVars.qzfx.dr.showList\">\r\n          <adv-search ref=\"adv_search\" @search=\"page.search\" comKey=\"adv_search_select_data\" :page=\"this\"\r\n            :keyWordsLabel=\"globalVars.qzfx.dr.keyWordsLabelObj[globalVars.qzfx.dr.fillUserType]\" :hideKeyWords=\"false\"\r\n            v-model=\"globalVars.qzfx.dr.searchQuerySetting\" searchTime=\"instant\"\r\n            v-bind=\"globalVars.qzfx.dr.searchProps\" :columnsModel=\"globalVars.qzfx.dr.columnsModel\"\r\n            :beforeRender=\"page.beforeRender\"></adv-search>\r\n          <div class=\"split_div\"></div>\r\n          <adv-vxe-table autoLoad :comKey=\"new Date().getTime()\" app-code=\"portrait\" ref=\"table_preview\" :page=\"this\"\r\n            :data-source=\"globalVars.qzfx.dr.tableDataConfig\"\r\n            :table-config=\"{...globalVars.qzfx.dr.tableConfig, beforeRenderFunc:page.modelEdit}\"\r\n            :model-params=\"globalVars.qzfx.dr.modelParams\" :columnsModel=\"globalVars.qzfx.dr.columnsModel\"\r\n            :toolbarConfig=\"{enabled: false}\" :pagerConfig=\"globalVars.qzfx.dr.pagerConfig\" :autoLoad='false'\r\n            :extQuerySetting=\"globalVars.qzfx.dr.querySetting\">\r\n            <!-- :dataOptions=\" {dataSource:{dataPath:'datas.previewData.rows'}} -->\r\n          </adv-vxe-table>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>", "required": false, "pattern": "", "validator": ""}, "dataOptions": {"remote": true, "remoteType": "dataset", "remoteModel": "", "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "dataSource": {"type": "custom", "params": {}, "code": "", "url": ""}}, "events": {"mounted": "", "beforeDestroy": "", "click": "", "change": "", "input": ""}, "name": "vue模板", "isCover": false, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "res-com-template", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "events": {"mounted": "", "destroy": ""}, "uuid": "uuid_5fklu9ul", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "showTitle": false, "title": "容器标题", "events": {"mounted": "", "destroy": ""}, "layout": "grid", "uuid": "uuid_ss8vtq8q", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "events": {"mounted": "", "destroy": ""}, "uuid": "uuid_okv9gzpj", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false, "bgType": "bgcolor", "backgroundColor": ""}, "showTitle": false, "title": "容器标题", "events": {"mounted": "", "destroy": ""}, "layout": "grid", "uuid": "uuid_mbxkatdf", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "0 2px 12px 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "events": {"mounted": "", "destroy": ""}, "uuid": "uuid_5y3ykprc", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "0 2px 12px 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "showTitle": true, "title": "群组", "events": {"mounted": {"name": "mounted_dialog_6rm9x8ih", "enName": "群组分析-弹窗-加载完成", "params": [{"name": "event", "des": "name:事件名称,dialog:当前弹窗对象"}]}, "destroy": {"name": "destroy_dialog_sppn93ai", "enName": "群组分析弹窗-卸载", "params": [{"name": "event", "des": "name:事件名称,dialog:当前弹窗对象"}]}}, "layout": "grid", "sign": "dialog", "btnsPosition": "right", "frameType": "1", "widthType": "pixel", "percentWidth": "50%", "size": "medium", "pixelWidth": "1100", "commonOptions": {"fullscreen": "false", "closeOnClickModal": "false"}, "drawerOptions": {"frameDirection": "rtl", "wrapperClosable": "true"}, "btnOptions": [{"label": "取消", "id": "cancel_6n7uvqw9", "type": "", "func": {"name": "", "params": {}}}, {"label": "保存", "id": "confirm_y88s7y2j", "type": "", "func": {"name": "action_ev_ubv5gn3t", "params": [{"name": "event", "des": "事件相关参数"}, {"name": "closeNext", "des": "需要调用该方法才能关闭弹窗"}], "enName": "群组分析弹窗-保存"}}, {"label": "保存并立即分析", "id": "a8wsbgbi", "uuid": "cu6vpyal", "type": "primary", "func": {"name": "action_ev_z0jno3qe", "enName": "群组分析弹窗-保存并立即分析"}, "children": []}], "btnRenderFunc": {}, "uuid": "uuid_crtozjxi", "pageCode": "qzfx"}, {"__tree_node_key": "1050651509799223", "key": "dialog_qzxq", "__tree_label": "区域容器", "comType": "row", "type": "row", "children": [{"__tree_node_key": "66351788770489", "key": "col_yhtclta8", "__tree_label": "区块容器", "comType": "col", "type": "col", "children": [{"__tree_node_key": "8716011218542208", "key": "row_o5bdzete", "__tree_label": "区域容器", "comType": "row", "type": "row", "children": [{"__tree_node_key": "2920044147702181", "key": "col_6t645kbe", "__tree_label": "区块容器", "comType": "col", "type": "col", "children": [{"uuid": "uuid_z1wrxmzn", "key": "buttons_xdkru0jb", "com": "res-buttons", "comType": "buttons", "icon": "pm-icon-btns", "comClassify": "form", "options": {"readonly": false, "size": "", "schema": "button", "noMoreClick": true, "buttonList": [{"label": "删除", "id": "btn_1708311438576", "uuid": "btn_1708311438576", "type": "danger", "func": {"name": "action_ev_w4lej0nb", "enName": "比对详情弹窗-删除", "params": [{"name": "event", "des": "{btn}:点击的按钮;"}]}}, {"label": "立即对比", "id": "b8<PERSON><PERSON><PERSON>", "uuid": "g1wqy68w", "type": "", "func": {"name": "action_ev_e19osncq", "enName": "比对详情-立即对比"}, "children": []}, {"label": "编辑", "id": "ts6p7ddq", "uuid": "i22vgpl5", "type": "primary", "func": {"name": "action_ev_7efdfz1j", "enName": "比对详情弹窗-编辑"}, "children": []}], "renderFunc": {}, "hideIcon": false}, "events": {"mounted": "", "beforeDestroy": "", "click": ""}, "name": "按钮组", "isCover": false, "dataOptions": {"remote": true, "remoteType": "dataset", "remoteModel": "", "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "dataSource": {"type": "", "url": "", "code": "", "params": {}, "querySetting": []}}, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "res-buttons", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "events": {"mounted": "", "destroy": ""}, "uuid": "uuid_2ythel8t", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false, "bgType": "bgcolor", "backgroundColor": "", "borderRadius": 0}, "showTitle": false, "title": "容器标题", "events": {"mounted": "", "destroy": ""}, "layout": "grid", "uuid": "uuid_kc0uhpfh", "pageCode": "qzfx"}, {"__tree_node_key": "9058035069787627", "key": "row_9gqqx1dr", "__tree_label": "区域容器", "comType": "row", "type": "row", "children": [{"__tree_node_key": "7526783567127537", "key": "col_o9x45bgk", "__tree_label": "区块容器", "comType": "col", "type": "col", "children": [{"uuid": "uuid_l3u5jsff", "key": "component_bixal49r", "com": "res-com-template", "comType": "component", "icon": "pm-icon-vue", "options": {"readonly": false, "customClass": "", "labelWidth": 100, "isLabelWidth": false, "hidden": false, "template": "<div class=\"qzxq-top\">\r\n    <div v-html=\"page.genIcon(globalVars.qzxq.formData.qztb)\"></div>\r\n    <!-- <i :class=\"globalVars.qzxq.formData.qztb\" class=\"icon\" :style=\"{background:globalVars.qzfxview.colors[globalVars.qzxq.formData.index%globalVars.qzfxview.colors.length]}\"></i> -->\r\n    <div class=\"qzmc-jscount-div\">\r\n        <span class=\"qzmc\">{{globalVars.qzxq.formData.qzmc}}</span>\r\n        <span class=\"jscount\" :class=\"page.renderJsCount(globalVars.qzxq.formData.teacherCount)\">{{page.formatNumber(globalVars.qzxq.formData.teacherCount)}} 位教师</span>\r\n    </div>\r\n</div>", "required": false, "pattern": "", "validator": ""}, "dataOptions": {"remote": true, "remoteType": "dataset", "remoteModel": "", "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "dataSource": {"type": "custom", "params": {}, "code": "", "url": ""}}, "events": {"mounted": "", "beforeDestroy": "", "click": "", "change": "", "input": ""}, "name": "vue模板", "isCover": false, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "res-com-template", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "events": {"mounted": "", "destroy": ""}, "uuid": "uuid_wmn7vsjl", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false, "bgType": "bgcolor", "backgroundColor": "", "borderRadius": 0}, "showTitle": false, "title": "容器标题", "events": {"mounted": "", "destroy": ""}, "layout": "grid", "uuid": "uuid_g8rk3qxo", "pageCode": "qzfx"}, {"__tree_node_key": "1430037492087255", "key": "row_546gt7ot", "__tree_label": "区域容器", "comType": "row", "type": "row", "children": [{"__tree_node_key": "8644599824384966", "key": "col_mmhrbve8", "__tree_label": "区块容器", "comType": "col", "type": "col", "children": [{"uuid": "uuid_d4w81al7", "key": "tabs_o79uy8p8", "com": "res-tabs", "comType": "tabs", "comClassify": "layout", "icon": "pm-icon-tabs", "options": {"readonly": false, "type": "", "tabPosition": "top", "customClass": "", "hidden": false, "activeIndex": 0, "paddingContent": 16, "show-tab": true, "hideContainer": false, "lazy": true, "initedTriggerUnchange": false, "titleUsageScenarios": "", "contentPosition": "", "undefined": "", "headerUsageScenariosLeft": "line", "formHeightLeft": 40, "showBgColorLeft": false, "contentPositionLeft": "", "headerUsageScenariosRight": "line", "formHeightRight": 40, "showBgColorRight": false, "contentPositionRight": ""}, "children": [{"id": "tab_1", "title": "群组概述", "name": "qzgs_tab", "children": [{"isRoot": true, "type": "row", "comType": "page", "key": "page_li60unjq", "__tree_label": "页面", "__tree_node_key": "16640152584992696", "children": [{"__tree_node_key": "5441872545650241", "key": "row_4ni9ijzb", "__tree_label": "区域容器", "comType": "row", "type": "row", "isCover": false, "children": [{"__tree_node_key": "05838591541176186", "key": "col_t0b7stqr", "__tree_label": "区块容器", "comType": "col", "type": "col", "isCover": false, "children": [{"__tree_node_key": "09624731040765178", "key": "row_nxp9v8ty", "__tree_label": "区域容器", "comType": "row", "type": "row", "children": [{"__tree_node_key": "3638669685618454", "key": "col_ec2euae3", "__tree_label": "区块容器", "comType": "col", "type": "col", "children": [{"uuid": "uuid_slz0z7ol", "key": "title_4vz212vu", "com": "res-title", "comType": "title", "icon": "pm-icon-title", "comClassify": "com", "dataType": "string", "options": {"readonly": false, "showIcon": true, "icon": "", "AfterIcon": "", "title": "群组信息", "titleSize": 16, "desc": "", "bindCom": {}, "hidden": false, "anchorLevel": 1, "enabledAnchor": false, "undefined": ""}, "events": {"mounted": "", "beforeDestroy": ""}, "name": "群组信息", "isCover": false, "dataOptions": {"remote": false, "remoteType": "dataset", "remoteModel": "", "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}}, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "res-title", "pageCode": "qzfx"}, {"uuid": "uuid_y9mmcprl", "key": "data-form_zd_qzfx", "com": "data-form", "comType": "data-form", "icon": "pm-icon-mode-form", "comClassify": "model", "dataType": "object", "options": {"readonly": true, "width": 0, "formWidth": 0, "openGroup": true, "showLabel": true, "labelWidth": 110, "labelPosition": "left", "labelShowType": "default", "tableForm": false, "column": 3, "autoLoad": false, "size": "", "hidden": false, "disabled": false, "groupType": "default", "hideNewFields": true, "isPrint": false, "validateGroup": "", "beforeRender": {}, "beforeRules": {}, "tipRight": false, "anchorLevel": 1, "enabledAnchor": false, "undefined": "", "comLinkEnabled": true, "titleSize": 16, "showIcon": "true", "labelSup": "", "disDictRequest": false, "modelSort": {}}, "events": {"mounted": "", "beforeDestroy": "", "inited": "", "item-change": "", "dict-loaded": "", "dict-change": ""}, "dataOptions": {"remote": true, "remoteType": "model", "remoteModel": {"actionType": "form", "id": "portrait-portraitgroup", "url": "/admin/model/design/portrait/perm/portraitgroup", "modelCascades": "", "modelName": "portraitgroup", "modelApp": "portrait"}, "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "prefix": "form", "dataSource": {"type": "in", "url": "", "code": "", "dataPath": "", "params": {}}, "columnsModel": [{"name": "qztb", "originalCaption": "群组图标", "form.comName": "", "form.hidden": 1, "form.omitted": 0, "form.readonly": 0, "form.required": 1, "placeholder": "请输入", "start-placeholder": "", "end-placeholder": ""}, {"name": "qzmc", "originalCaption": "群组名称", "form.comName": "", "form.required": 1, "caption": "群组名称", "placeholder": "请输入", "start-placeholder": "", "end-placeholder": "", "form.hidden": 0, "form.omitted": 0, "form.readonly": 0}, {"name": "id", "originalCaption": "ID", "form.comName": "", "form.omitted": 0, "form.readonly": 0, "form.required": 0, "caption": "群组ID", "form.hidden": 0, "placeholder": "请选择", "start-placeholder": "", "end-placeholder": ""}, {"name": "teacherCount", "originalCaption": "教师数量", "form.comName": "", "form.hidden": 0, "form.omitted": 0, "form.readonly": 0, "form.required": 0, "caption": "教师数量", "placeholder": "请输入", "start-placeholder": "", "end-placeholder": ""}, {"name": "createBy", "originalCaption": "创建人", "form.comName": "", "form.hidden": 0, "caption": "创建人", "placeholder": "请选择", "start-placeholder": "", "end-placeholder": "", "form.omitted": 0, "form.readonly": 0, "form.required": 0}, {"name": "createTime", "originalCaption": "创建时间", "form.comName": "", "form.hidden": 0, "caption": "创建时间", "placeholder": "请选择", "start-placeholder": "", "end-placeholder": "", "form.omitted": 0, "form.readonly": 0, "form.required": 0}, {"name": "updateTime", "originalCaption": "更新时间", "form.comName": "", "caption": "最后修改时间", "form.hidden": 0, "form.xtype": "text", "form.JSONParam": {"width": "200px"}, "placeholder": "请输入", "start-placeholder": "", "end-placeholder": "", "form.omitted": 0, "form.readonly": 0, "form.required": 0}, {"name": "qzms", "originalCaption": "群组描述", "form.comName": "", "form.JSONParam": {"isOver": true}, "caption": "群组描述", "form.hidden": 0, "form.omitted": 0, "form.readonly": 0, "form.required": 0}, {"name": "qqfs", "originalCaption": "圈群方式，tj 设置条件 xz 直接选择 导入 dr", "form.comName": "", "caption": "圈群方式", "form.xtype": "text", "placeholder": "请输入", "start-placeholder": "", "end-placeholder": "", "form.hidden": 0, "form.omitted": 0, "form.readonly": 0, "form.required": 0}, {"name": "tj", "originalCaption": "条件", "form.comName": "", "form.hidden": 1}, {"name": "cyqz", "originalCaption": "常用群组：1 常用群组，0 非常用群组", "form.comName": "", "form.hidden": 1, "placeholder": "请选择", "start-placeholder": "", "end-placeholder": "", "form.omitted": 0, "form.readonly": 0, "form.required": 0}, {"name": "fxkb", "originalCaption": "分析看板：1 已配置 0 未配置", "form.comName": "", "form.hidden": 1}, {"name": "xtyz", "originalCaption": "系统预置：1 系统预置，0 非系统预置", "form.comName": "", "form.hidden": 1}, {"name": "updateBy", "originalCaption": "更新人", "form.comName": ""}, {"name": "sql", "originalCaption": "sql片段", "form.comName": "", "placeholder": "请输入", "start-placeholder": "", "end-placeholder": "", "form.hidden": 1, "form.omitted": 0, "form.readonly": 0, "form.required": 0}, {"name": "logId", "originalCaption": "excel导入记录id", "form.comName": "", "placeholder": "请输入", "start-placeholder": "", "end-placeholder": "", "form.hidden": 1, "form.omitted": 0, "form.readonly": 0, "form.required": 0}]}, "name": "模型表单", "isCover": false, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "data-form", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "events": {"mounted": "", "destroy": ""}, "uuid": "uuid_yhdxgsmc", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false, "bgType": "bgcolor", "backgroundColor": "", "borderRadius": 0}, "showTitle": false, "title": "容器标题", "events": {"mounted": "", "destroy": ""}, "layout": "grid", "uuid": "uuid_b92exmvf", "pageCode": "qzfx"}, {"__tree_node_key": "9506338248869628", "key": "row_wffwydji", "__tree_label": "区域容器", "comType": "row", "type": "row", "children": [{"__tree_node_key": "9129242044237043", "key": "col_346eiwsm", "__tree_label": "区块容器", "comType": "col", "type": "col", "children": [{"uuid": "uuid_pj2elu9b", "key": "title_bkcbuf2c", "com": "res-title", "comType": "title", "icon": "pm-icon-title", "comClassify": "com", "dataType": "string", "options": {"readonly": false, "showIcon": true, "icon": "", "AfterIcon": "", "title": "群组成员条件", "titleSize": 16, "desc": "", "bindCom": {}, "hidden": false, "anchorLevel": 1, "enabledAnchor": false, "undefined": ""}, "events": {"mounted": "", "beforeDestroy": ""}, "name": "群组成员条件", "isCover": false, "dataOptions": {"remote": false, "remoteType": "dataset", "remoteModel": "", "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}}, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "res-title", "pageCode": "qzfx"}, {"uuid": "uuid_xbr1i32d", "key": "component_mt4qmvip", "com": "res-com-template", "comType": "component", "icon": "pm-icon-vue", "options": {"readonly": false, "customClass": "", "labelWidth": 100, "isLabelWidth": false, "hidden": false, "template": "<!-- 条件查看 -->\r\n<div>\r\n    <adv-filter-2 ref=\"advFilter2RefRead\" :cascader=\"globalVars.qzfx.cascader\" type=\"read\"\r\n        :advQuerySetting=\"globalVars.qzxq.qzgs.tjQuerySettingJson\" />\r\n</div>", "required": false, "pattern": "", "validator": ""}, "dataOptions": {"remote": false, "remoteType": "dataset", "remoteModel": "", "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "dataSource": {"type": "custom", "params": {}, "code": "", "url": ""}}, "events": {"mounted": "", "beforeDestroy": "", "click": "", "change": "", "input": ""}, "name": "vue模板", "isCover": false, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "res-com-template", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "events": {"mounted": "", "destroy": ""}, "uuid": "uuid_i53qjt9i", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false, "bgType": "bgcolor", "backgroundColor": "", "borderRadius": 0}, "showTitle": false, "title": "容器标题", "events": {"mounted": "", "destroy": ""}, "layout": "grid", "uuid": "uuid_w6g9vdy7", "pageCode": "qzfx"}], "options": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "uuid": "uuid_n05z8c8m", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": "0", "marginLeft": 0, "marginRight": 0, "boxShadow": "0 0 0 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": false, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "borderColor": "#dddddd", "borwidth": 1, "borderStyle": "solid", "hidden": false, "bgType": "bgcolor", "backgroundColor": "", "borderRadius": 0}, "showTitle": false, "title": "容器标题", "layout": "grid", "uuid": "uuid_ox1uwvjc", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 8, "marginLeft": 0, "marginRight": 0}, "uuid": "uuid_nzsxsxbg", "pageCode": "qzfx"}], "__tree_label": "tab_1", "uuid": "uuid_thxmvr39", "comStyles": {}}, {"title": "群组成员", "id": "cuwjkfjp", "name": "qzcy_tab", "children": [{"isRoot": true, "type": "row", "comType": "page", "key": "page_i329qyrj", "__tree_label": "页面", "__tree_node_key": "5641604113955885", "children": [{"__tree_node_key": "1198778626713739", "key": "row_0tqxj85o", "__tree_label": "区域容器", "comType": "row", "type": "row", "isCover": false, "children": [{"__tree_node_key": "8189922744464178", "key": "col_laq7wahs", "__tree_label": "区块容器", "comType": "col", "type": "col", "isCover": false, "children": [{"uuid": "uuid_n39451j9", "key": "adv-search_pp1qj0kk", "com": "adv-search", "comType": "adv-search", "icon": "pm-icon-adv-search", "comClassify": "model", "dataType": "object", "options": {"readonly": false, "interacTable": "", "limit": 2, "keyWordsLabel": "职工号/姓名", "hideKeyWords": false, "labelWidth": 100, "keyWordsHighlight": true, "size": "", "itemWidth": 360, "singleItemWidth": 400, "tileItemWidth": 320, "isMore": false, "searchTime": "instant", "conditionType": "", "hidden": false, "readyDoSearch": true, "hideNewFields": true, "showAdv": false, "fullscreen": false, "isCondition": false, "beforeRender": {}, "showHidden": true, "undefined": "", "beforeSearch": {}, "comLinkEnabled": true}, "events": {"mounted": {"params": [{"name": "event", "des": "内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组)"}]}, "beforeDestroy": {}, "search": {"name": "ev_search_cqsx1ig5"}, "collapse": {}, "expand": {}, "reset": {"name": "ev_reset_65sxg50h"}, "item-change": {"name": "ev_item_change_p41964ht", "enName": "群组详情-群组成员-高级搜索-itemchange"}, "inited": {}, "before-reset": "", "adv-reset": "", "async-before-reset": ""}, "dataOptions": {"remote": true, "remoteType": "model", "remoteModel": {"actionType": "search", "id": "portrait-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "/admin/model/design/portrait/perm/jzgemptymodel", "modelCascades": "", "modelName": "jzgemptymodel", "modelApp": "portrait", "params": {"modelId": "main-abdteajzgjbxx"}}, "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "prefix": "search", "dataSource": {"type": "in", "url": "", "code": "", "params": {}}, "columnsModel": [{"name": "zgh"}, {"name": "xm"}, {"name": "ssjgdm"}], "modelConfig": {"zgh": {"search.comName": "", "placeholder": "请输入", "start-placeholder": "", "end-placeholder": "", "search.omitted": 0, "fuzzySearch": 1}, "xm": {"search.comName": "", "placeholder": "请输入", "start-placeholder": "", "end-placeholder": "", "search.omitted": 0, "fuzzySearch": 1}, "ssjgdm": {"search.comName": ""}}}, "name": "高级搜索", "isCover": false, "interactList": [], "comStyles": {"marginBottom": "16"}, "__tree_label": "adv-search", "pageCode": "qzfx"}, {"uuid": "uuid_strm5y3a", "key": "component_54hx98ml", "com": "res-com-template", "comType": "component", "icon": "pm-icon-vue", "options": {"readonly": false, "customClass": "", "labelWidth": 100, "isLabelWidth": false, "hidden": false, "template": "<div class=\"qzxq-qzcy-container\" v-loading=\"globalVars.qzxq.qzcy.loading\" element-loading-text=\"拼命加载中\"\r\n  element-loading-spinner=\"el-icon-loading\" element-loading-background=\"white\">\r\n  <div class=\"true_container\">\r\n    <div class=\"item\" v-for=\"(item,index) in globalVars.qzxq.qzcy.tableData\" @click=\"page.openGrhx(item)\">\r\n      <div class=\"item-top\">\r\n        <div class=\"div1\">\r\n          <div class=\"qzmc-jscount-div\">\r\n            <span class=\"qzmc\" :title=\"item.xm\">{{item.xm}}</span>\r\n            <div>\r\n              <i v-if=\"item.zgh\" class=\"el-icon-user-solid zgh-tb\"></i>\r\n              <span class=\"zgh-dw\" :title=\"item.zgh\">{{item.zgh}}</span>\r\n            </div>\r\n            <div>\r\n              <i v-if=\"item.ssjg\" class=\"el-icon-s-home zgh-tb\"></i>\r\n              <span class=\"zgh-dw\" :title=\"item.ssjg\">{{item.ssjg}}</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"icon\">\r\n            {{item?.xm?.substring(Math.max(item?.xm?.length - 2, 0))}}\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"item-down\">\r\n        <div class=\"btn-list\">\r\n          <div class=\"btn-text\">查看画像</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n<!-- 分页 -->\r\n<el-pagination style=\"margin-top:16px\" @size-change=\"page.handleSizeChange\" @current-change=\"page.handleCurrentChange\"\r\n  :current-page=\"globalVars.qzxq.qzcy.pageNo\" :page-sizes=\"[8, 20, 30, 40]\" :page-size=\"globalVars.qzxq.qzcy.pageSize\"\r\n  layout=\"total, prev, pager, next, sizes, jumper\" :total=\"globalVars.qzxq.qzcy.total\" hide-on-single-page=\"true\">\r\n</el-pagination>", "required": false, "pattern": "", "validator": ""}, "dataOptions": {"remote": true, "remoteType": "dataset", "remoteModel": "", "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "dataSource": {"type": "custom", "params": {}, "code": "", "url": ""}}, "events": {"mounted": "", "beforeDestroy": "", "click": "", "change": "", "input": ""}, "name": "vue模板", "isCover": false, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "res-com-template", "pageCode": "qzfx"}, {"uuid": "uuid_mfhjneeo", "key": "empty_px0a6w2e", "com": "res-empty", "comType": "empty", "icon": "pm-icon-upload", "comClassify": "com", "dataType": "string", "options": {"readonly": false, "img": "noData", "title": "暂无数据", "desc": "暂无数据", "btnType": "btn", "getFileUrl": "", "bindCom": {}, "buttonList": [], "hidden": true}, "events": {"mounted": "", "beforeDestroy": ""}, "name": "空状态", "isCover": false, "dataOptions": {"remote": true, "remoteType": "dataset", "remoteModel": "", "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "dataSource": {"type": "", "url": "", "code": "", "params": {}, "querySetting": []}}, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "res-empty", "pageCode": "qzfx"}], "options": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "uuid": "uuid_1b3iufme", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 8, "marginLeft": 0, "marginRight": 0, "boxShadow": "0 0 0 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": false, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "borderColor": "#dddddd", "borwidth": 1, "borderStyle": "solid", "hidden": false, "bgType": "bgcolor", "backgroundColor": "", "borderRadius": 0}, "showTitle": false, "title": "容器标题", "layout": "grid", "uuid": "uuid_0ltq1asp", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 8, "marginLeft": 0, "marginRight": 0}, "uuid": "uuid_714oam2s", "pageCode": "qzfx"}], "__tree_label": "tabssx3sf5rr", "uuid": "uuid_25cgrotj", "comStyles": {}}, {"title": "群组分析", "id": "vxpacqia", "name": "qzfx_tab", "children": [{"isRoot": true, "type": "row", "comType": "page", "key": "page_i329qyrj", "__tree_label": "页面", "__tree_node_key": "5641604113955885", "children": [{"__tree_node_key": "43472574481651294", "key": "row_1kfo5pah", "__tree_label": "区域容器", "comType": "row", "type": "row", "isCover": false, "children": [{"__tree_node_key": "30707561917569404", "key": "col_1xe7npgs", "__tree_label": "区块容器", "comType": "col", "type": "col", "isCover": false, "children": [{"uuid": "uuid_s2n550vp", "key": "page-runtime_n3rv42qk", "com": "page-runtime", "comType": "page-runtime", "icon": "pm-icon-page-runtime", "dataType": "object", "options": {"readonly": false, "customClass": "", "labelWidth": 100, "isLabelWidth": false, "hidden": true, "pageInfo": {"pageCode": "qzhx", "pageType": "default", "appCode": "portrait", "label": "群组画像", "value": "qzhx"}, "values": {}, "pageParams": {"qzid": "{vars.qzxq.formData.id}"}, "isProdEnv": true, "appCode": "portrait", "inlinePage": true}, "events": {"mounted": "", "beforeDestroy": "", "change": "", "pageBeforeDestroy": "", "pageReady": ""}, "name": "页面", "isCover": false, "dataOptions": {"remote": true, "remoteType": "dataset", "remoteModel": "", "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "dataSource": {"type": "", "url": "", "code": "", "params": {}, "querySetting": []}}, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "page-runtime", "pageCode": "qzfx"}, {"uuid": "uuid_svmwtikn", "key": "empty_la0tq1bj", "com": "res-empty", "comType": "empty", "icon": "pm-icon-upload", "comClassify": "com", "dataType": "string", "options": {"readonly": false, "img": "listEmpty", "title": "暂无数据", "desc": "请联系管理员定制看板后，再查看结果", "btnType": "btn", "getFileUrl": "", "bindCom": {}, "buttonList": [], "hidden": true}, "events": {"mounted": "", "beforeDestroy": ""}, "name": "空状态", "isCover": false, "dataOptions": {"remote": true, "remoteType": "dataset", "remoteModel": "", "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "dataSource": {"type": "", "url": "", "code": "", "params": {}, "querySetting": []}}, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "res-empty", "pageCode": "qzfx"}], "options": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "uuid": "uuid_vyfhgsy3", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 8, "marginLeft": 0, "marginRight": 0, "boxShadow": "0 0 0 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": false, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "borderColor": "#dddddd", "borwidth": 1, "borderStyle": "solid", "hidden": false, "bgType": "bgcolor", "backgroundColor": "", "borderRadius": 0}, "showTitle": false, "title": "容器标题", "layout": "grid", "uuid": "uuid_sx52zhg9", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 8, "marginLeft": 0, "marginRight": 0}, "uuid": "uuid_ez8he8h5", "pageCode": "qzfx"}], "__tree_label": "tabs6nnd9q0k", "uuid": "uuid_9tye3f58"}], "events": {"mounted": {"params": [{"name": "event", "des": "内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组)"}]}, "beforeDestroy": {}, "tabChange": {"name": "ev_tabChange_yz0wszjg", "enName": "群组详情-tab页change", "tip": "切换tab时触发"}}, "dataOptions": {"remote": true, "remoteType": "dataset", "remoteModel": "", "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "dataSource": {"type": "", "url": "", "code": "", "params": {}, "querySetting": []}}, "name": "标签页", "isCover": false, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "res-tabs", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "events": {"mounted": "", "destroy": ""}, "uuid": "uuid_r69tfgdf", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false, "bgType": "bgcolor", "backgroundColor": "", "borderRadius": 0}, "showTitle": false, "title": "容器标题", "events": {"mounted": "", "destroy": ""}, "layout": "grid", "uuid": "uuid_1mq6to4n", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "0 2px 12px 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "events": {"mounted": "", "destroy": ""}, "uuid": "uuid_879psmfa", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "0 2px 12px 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "showTitle": true, "title": "群组详情", "events": {"mounted": {"name": "mounted_dialog_z8s5nvgr", "enName": "群组详情-弹窗-加载完成", "params": [{"name": "event", "des": "name:事件名称,dialog:当前弹窗对象"}]}, "destroy": {"name": "destroy_dialog_54eyxi9y", "enName": "群组详情弹窗-卸载", "params": [{"name": "event", "des": "name:事件名称,dialog:当前弹窗对象"}]}}, "layout": "grid", "sign": "dialog", "btnsPosition": "right", "frameType": "1", "widthType": "pixel", "percentWidth": "50%", "size": "medium", "pixelWidth": "700", "commonOptions": {"fullscreen": "false", "closeOnClickModal": "false"}, "drawerOptions": {"frameDirection": "rtl", "wrapperClosable": "true"}, "btnOptions": [{"label": "取消", "id": "cancel_6n7uvqw9", "type": "", "func": {"name": "", "params": {}}}, {"label": "确认", "id": "confirm_y88s7y2j", "type": "primary", "func": {"name": "", "params": {}}}], "btnRenderFunc": {}, "uuid": "uuid_59jnw1dj", "hideAction": true, "pageCode": "qzfx"}, {"__tree_node_key": "8254525871529865", "key": "dialog_tjyl", "__tree_label": "区域容器", "comType": "row", "type": "row", "children": [{"__tree_node_key": "2809123831310478", "key": "col_o6rrq3ye", "__tree_label": "区块容器", "comType": "col", "type": "col", "children": [{"__tree_node_key": "8864976545256775", "key": "row_hw3ylj1b", "__tree_label": "区域容器", "comType": "row", "type": "row", "children": [{"__tree_node_key": "8795704754826696", "key": "col_hx8cmtjn", "__tree_label": "区块容器", "comType": "col", "type": "col", "children": [{"uuid": "uuid_g28jveuj", "key": "adv-search_dv5tpccj", "com": "adv-search", "comType": "adv-search", "icon": "pm-icon-adv-search", "comClassify": "model", "dataType": "object", "options": {"readonly": false, "interacTable": "", "limit": 2, "keyWordsLabel": "职工号/姓名", "hideKeyWords": false, "labelWidth": 100, "keyWordsHighlight": true, "size": "", "itemWidth": 340, "singleItemWidth": 400, "tileItemWidth": 320, "isMore": false, "searchTime": "instant", "conditionType": "", "hidden": false, "readyDoSearch": true, "hideNewFields": true, "showAdv": false, "fullscreen": false, "isCondition": false, "beforeRender": {}, "showHidden": true, "undefined": "", "beforeSearch": {}, "comLinkEnabled": true}, "events": {"mounted": {"params": [{"name": "event", "des": "内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组)"}]}, "beforeDestroy": {}, "search": {"name": "ev_search_aawxneyj", "enName": "群组分析-条件预览-高级搜索"}, "collapse": {}, "expand": {}, "reset": {}, "item-change": {}, "inited": {}, "before-reset": "", "adv-reset": "", "async-before-reset": ""}, "dataOptions": {"remote": true, "remoteType": "model", "remoteModel": {"actionType": "search", "id": "portrait-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "/admin/model/design/portrait/perm/jzgemptymodel", "modelCascades": "", "modelName": "jzgemptymodel", "modelApp": "portrait", "params": {"modelId": "main-abdteajzgjbxx"}}, "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "prefix": "search", "dataSource": {"type": "in", "url": "", "code": "", "params": {}}, "columnsModel": [{"name": "zgh"}, {"name": "xm"}, {"name": "ssjgdm"}], "modelConfig": {"zgh": {"search.comName": "", "placeholder": "请输入", "start-placeholder": "", "end-placeholder": "", "search.omitted": 0, "fuzzySearch": 1}, "xm": {"search.comName": "", "placeholder": "请输入", "start-placeholder": "", "end-placeholder": "", "search.omitted": 0, "fuzzySearch": 1}, "ssjgdm": {"search.comName": ""}}}, "name": "高级搜索", "isCover": false, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "adv-search", "pageCode": "qzfx"}, {"uuid": "uuid_ervdkoln", "key": "adv-table_94qqaomu", "com": "adv-vxe-table", "comType": "adv-table", "icon": "pm-icon-adv-table", "comClassify": "model", "dataType": "object", "options": {"readonly": false, "customClass": "", "labelWidth": 100, "bindComEmpty": {}, "bindComTop": {}, "bindComBottom": {}, "hidden": false, "editable": false, "hideNewFields": false, "autoLoad": false, "searchReserveData": false, "localDataSort": false, "scheme": true, "tableConfig": {"border": "default", "seq": {"enabled": false, "seqTitle": "序号", "seqWidth": 60, "align": "center", "headerAlign": "center"}, "rowSort": {"enabled": false, "handle": "", "fixed": "left"}, "selectType": {"type": "", "checkTitle": "选择", "checkWidth": 100, "labelField": "", "reserve": true, "highlight": true, "range": true, "visibleMethodFunc": {}, "checkMethodFunc": {}, "align": "center", "headerAlign": "center"}, "showHeaderOverflow": true, "showOverflow": true, "keepSource": true, "height": 0, "minHeight": 298, "maxHeight": 548, "stripe": false, "emptyText": "暂无数据", "align": "left", "header-align": "left", "rowId": "id", "groupField": "tableColumnGroup", "groupEnabled": false, "beforeRenderFunc": {"name": "before_render_o1hs8a49", "enName": "条件查询预览-弹窗", "params": [{"name": "modelItem", "des": "对应列模型，可以修改模型数据；返回一个新的列模型"}]}, "spanMethodFunc": {}}, "rowConfig": {"isHover": true, "isCurrent": false, "height": 66}, "reportConfig": {"reportText": "导出报表", "reportList": []}, "columnConfig": {"resizable": true, "minWidth": 0, "width": 0, "isHover": true, "isCurrent": false}, "customConfig": {"storage": true, "checkMethodFunc": {}}, "operationConfig": {"enabled": false, "position": "left", "title": "操作", "width": 100, "schema": "text", "buttonList": [], "buttonEditList": [], "renderFunc": {}, "template": "", "showOverflow": false, "beforeDialog": {}}, "pagerConfig": {"enabled": true, "pageSize": 20, "background": false, "pagerCount": 7, "hideOnSinglePage": false, "position": "right", "pageSizes": [10, 20, 50, 100], "layouts": ["prev", "pager", "next", "sizes", "total", "jumper"], "pageStyle": "normal", "undefined": ""}, "toolbarConfig": {"import": false, "export": false, "print": false, "enabled": false, "zoom": false, "custom": false, "sort": false, "report": false, "exportOptions": {"tooltip": {}}, "printOptions": {"tooltip": {}}, "zoomOptions": {"tooltip": {}}, "customOptions": {"tooltip": {}}, "importOptions": {"tooltip": {}}, "reportOptions": {"tooltip": {}}, "leftButtonList": {"schema": "text", "buttonList": [], "renderFunc": {}, "beforeDialog": {}}, "rightButtonList": {"schema": "text", "buttonList": [], "renderFunc": {}}, "flow": false, "batchDownload": false}, "exportConfig": {"filename": "table_1708409644036", "sheetName": "sheet", "type": "xlsx", "showConfig": true, "types": ["xlsx"], "exportColumnFilter": {}, "showOneClick": true, "showCustomClick": true, "oneClickBtnText": "一键导出", "customBtnText": "自定义导出", "translateDictFlag": true}, "treeConfig": {"lazy": false, "transform": false, "parentField": "parentId", "hasChildField": "<PERSON><PERSON><PERSON><PERSON>", "expandAll": false, "accordion": false, "trigger": "default", "indent": 20, "treeNodeField": ""}, "editConfig": {"trigger": "click", "mode": "row", "showStatus": true, "showAsterisk": true, "autoClear": true, "beforeActiveEditMethodFunc": {}, "beforeEditMethodFunc": {}}, "editRules": {}, "importConfig": {"config": "file", "filename": "模板文件", "isGenerateZdb": true, "customParam": ""}, "batchDownload": {"btnConfigList": []}, "bindPager": "", "comLinkEnabled": true, "batchDownloadConfig": {"btnText": "批量下载", "btnConfigList": []}, "flowConfig": {"placeholder": "", "isOnlyRead": false, "formComKey": "", "processDefinitionKey": "", "statMachId": "", "getButtonUrl": "", "customParams": {"processDefinitionKey": "{vars.flowParams.processDefinitionKey}", "processInstanceId": "{vars.flowParams.processInstanceId}", "processDefinitionId": "{vars.flowParams.processDefinitionId}", "taskId": "{vars.flowParams.taskId}", "wid": "{vars.flowParams.wid}", "viewData": "{vars.flowParams.viewData}", "operStatus": "{vars.flowParams.operStatus}"}, "isAutoSelect": true, "openDefaultOpinion": true, "showOldOpinion": false, "isTable": false, "labelPosition": "left", "labelWidth": 100}, "flowBtnConfig": {"beforeRender": {}, "undefined": ""}}, "dataOptions": {"remote": true, "remoteType": "model", "remoteModel": {"actionType": "table", "id": "portrait-<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "/admin/model/design/portrait/perm/emptymanymodel", "modelCascades": "", "modelName": "emptymanymodel", "modelApp": "portrait", "params": {"querySettingJsonStr": "{globalVars.qzfx.tjQuerySettingJsonStr}"}}, "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "prefix": "grid", "columnsModel": [], "dataSource": {"type": "custom", "url": "/plugins/portrait/qzfx/getQzcyPageList?qqfs=tj", "code": "", "dataPath": "rows", "params": {}, "querySetting": {}}}, "events": {"mounted": "", "beforeDestroy": "", "reload-data": "", "operate-click": "", "radio-change": "", "checkbox-change": "", "checkbox-all": "", "cell-click": "", "scroll": "", "page-change": "", "size-change": "", "zoom": "", "edit-closed": "", "row-sort-change": "", "startAndTakeUserTask": "", "agree": "", "startAndSaveDraft": "", "afterClick": "", "flowButtonSucceed": ""}, "name": "高级表格", "isCover": false, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "adv-vxe-table", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "events": {"mounted": "", "destroy": ""}, "uuid": "uuid_o9hia34i", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "showTitle": false, "title": "容器标题", "events": {"mounted": "", "destroy": ""}, "layout": "grid", "uuid": "uuid_sanzoetn", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "0 2px 12px 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "events": {"mounted": "", "destroy": ""}, "uuid": "uuid_sigh7w55", "pageCode": "qzfx"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "0 2px 12px 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "showTitle": true, "title": "条件查询预览", "events": {"mounted": {"name": "mounted_dialog_2n0ywzz6", "enName": "条件查询预览-弹窗", "params": [{"name": "event", "des": "name:事件名称,dialog:当前弹窗对象"}]}, "destroy": {"name": "destroy_dialog_a9rigz2d", "enName": "条件预览-弹窗-卸载", "params": [{"name": "event", "des": "name:事件名称,dialog:当前弹窗对象"}]}}, "layout": "grid", "sign": "dialog", "btnsPosition": "right", "frameType": "1", "widthType": "pixel", "percentWidth": "50%", "size": "medium", "pixelWidth": "1000", "commonOptions": {"fullscreen": "false", "closeOnClickModal": "false"}, "drawerOptions": {"frameDirection": "rtl", "wrapperClosable": "true"}, "btnOptions": [{"label": "取消", "id": "cancel_al5e0txl", "type": "", "func": {"name": "", "params": {}}}, {"label": "确认", "id": "confirm_hupilcoc", "type": "primary", "func": {"name": "", "params": {}}}], "btnRenderFunc": {}, "hideAction": true, "uuid": "uuid_fc6xqw2p", "pageCode": "qzfx"}], "suspendJson": []}