package com.wisedu.lowcode4j.main.teapub.vo;

import com.wisedu.lowcode4j.common.core.constant.*;

import org.sagacity.sqltoy.config.annotation.*;

import io.swagger.annotations.*;

import lombok.Data;
import com.alibaba.fastjson.annotation.*;

import com.wisedu.lowcode4j.main.teapub.po.Jzgkhjl;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgkhjlVo", description = "考核记录Vo")
//end_dynamic_declare
@Data
public class JzgkhjlVo extends Jzgkhjl {

    private static final long serialVersionUID = 4431474721041954239L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "khdwName", value = "考核单位名称")
    @Translate(cacheName = "biz_department", keyField = "khdw", cacheType = "")
    @JSONField(name = "khdw"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String khdwName;

    @ApiModelProperty(name = "khlbName", value = "考核类别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "khlb", cacheType = "KHLB")
    @JSONField(name = "khlb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String khlbName;

    @ApiModelProperty(name = "khjlName", value = "考核结论名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "khjl", cacheType = "KHJL")
    @JSONField(name = "khjl"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String khjlName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
