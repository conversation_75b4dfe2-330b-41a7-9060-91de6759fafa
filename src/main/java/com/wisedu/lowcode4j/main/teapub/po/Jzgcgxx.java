package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.main.teapub.vo.JzgcgxxVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgcgxx", description = "出国信息")
@Comment("出国信息")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_BIZ_TEACHER_CGXX", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_BIZ_TEACHER_CGXX", unique = false)
})
@Table(value = "BIZ_TEACHER_CGXX")
@SqlToyEntity
@ModelExt(extClass = JzgcgxxVo.class )
@ModelDefine(orderIndex = 13,modelClassify="rsxx", renderType="table")
//end_dynamic_declare
@Data
public class Jzgcgxx extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041956743L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-09 14:59:07")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=1,columnHidden=false,columnXtype="text",formRequired=false)
    private String zgh;

    @ApiModelProperty(name = "sqdwywmc", value = "所去单位英文名称" ,notes = "2023-11-09 15:02:27")
    @Comment("所去单位英文名称")
    @Column(value = "sqdwywmc",type = ColType.AUTO, width = 540,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=3,columnHidden=false,columnXtype="text",formRequired=false)
    private String sqdwywmc;

    @ApiModelProperty(name = "sqdwzwmc", value = "所去单位中文名称" ,notes = "2023-11-09 15:03:36")
    @Comment("所去单位中文名称")
    @Column(value = "sqdwzwmc",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=4,columnHidden=false,columnXtype="text",formRequired=false)
    private String sqdwzwmc;

    @ApiModelProperty(name = "tzmc", value = "团组名称" ,notes = "2023-11-09 15:04:12")
    @Comment("团组名称")
    @Column(value = "tzmc",type = ColType.AUTO, width = 120,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=5,columnHidden=false,columnXtype="text",formRequired=false)
    private String tzmc;

    @ApiModelProperty(name = "spdw", value = "审批单位" ,notes = "2023-11-09 15:07:00")
    @Comment("审批单位")
    @Column(value = "spdw",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=7,columnHidden=false,columnXtype="text",formRequired=false)
    private String spdw;

    @ApiModelProperty(name = "sprq", value = "审批日期" ,notes = "2023-11-09 15:09:51")
    @Comment("审批日期")
    @Column(value = "sprq",type = ColType.AUTO, width = 0,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=8,columnHidden=false,columnXtype="date-local",formRequired=false)
    private Date sprq;

    @ApiModelProperty(name = "spwh", value = "审批文号" ,notes = "2023-11-09 15:10:52")
    @Comment("审批文号")
    @Column(value = "spwh",type = ColType.AUTO, width = 90,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=9,columnHidden=false,columnXtype="text",formRequired=false)
    private String spwh;

    @ApiModelProperty(name = "xxgznr", value = "学习工作内容" ,notes = "2023-11-09 15:13:26")
    @Comment("学习工作内容")
    @Column(value = "xxgznr",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=11,columnHidden=false,columnXtype="text",formRequired=false)
    private String xxgznr;

    @ApiModelProperty(name = "hzhhtxzh", value = "护照号或通行证号" ,notes = "2023-11-09 15:17:57")
    @Comment("护照号或通行证号")
    @Column(value = "hzhhtxzh",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=12,columnHidden=false,columnXtype="text",formRequired=false)
    private String hzhhtxzh;

    @ApiModelProperty(name = "cgrq", value = "出国（境）日期" ,notes = "2023-11-09 15:18:42")
    @Comment("出国（境）日期")
    @Column(value = "cgrq",type = ColType.AUTO, width = 0,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=13,columnHidden=false,columnXtype="date-local",formRequired=false)
    private Date cgrq;

    @ApiModelProperty(name = "hgrq", value = "回国日期" ,notes = "2023-11-09 15:19:30")
    @Comment("回国日期")
    @Column(value = "hgrq",type = ColType.AUTO, width = 0,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=14,columnHidden=false,columnXtype="date-local",formRequired=false)
    private Date hgrq;

    @ApiModelProperty(name = "xxgzcj", value = "学习工作成绩" ,notes = "2023-11-09 15:20:03")
    @Comment("学习工作成绩")
    @Column(value = "xxgzcj",type = ColType.AUTO, width = 3000,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=15,columnHidden=false,columnXtype="text",formRequired=false)
    private String xxgzcj;

    @ApiModelProperty(name = "xxcdfy", value = "学校承担费用" ,notes = "2023-11-09 15:20:47")
    @Comment("学校承担费用")
    @Column(value = "xxcdfy",type = ColType.AUTO, precision = 4, width = 19,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=16,columnHidden=false,columnXtype="number",formRequired=false)
    private Double xxcdfy;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-09 15:20:58")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "cggb", value = "出国（境）国别" ,notes = "2023-11-09 15:01:51")
    @Comment("出国（境）国别")
    @Column(value = "cggb",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="GJHDQ",columnReadonly=false,fuzzySearch=false,orderIndex=2,columnHidden=false,columnXtype="select",formRequired=false)
    private String cggb;

    @ApiModelProperty(name = "jfly", value = "经费来源" ,notes = "2023-11-09 15:05:56")
    @Comment("经费来源")
    @Column(value = "jfly",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="CGJJFLY",columnReadonly=false,fuzzySearch=false,orderIndex=6,columnHidden=false,columnXtype="select",formRequired=false)
    private String jfly;

    @ApiModelProperty(name = "cgmd", value = "出国（境）目的" ,notes = "2023-11-09 15:12:42")
    @Comment("出国（境）目的")
    @Column(value = "cgmd",type = ColType.AUTO, width = 128,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="CGMD",columnReadonly=false,fuzzySearch=false,orderIndex=10,columnHidden=false,columnXtype="select",formRequired=false)
    private String cgmd;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=17,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
