package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.main.teapub.vo.JzgkyhjVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgkyhj", description = "科研获奖")
@Comment("科研获奖")
@ModelDefine(modelClassify="kyxx",renderType="table")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_biz_teacher_kyhj", unique = false),
})
@Table(value = "BIZ_TEACHER_KYHJ")
@SqlToyEntity
@ModelExt(extClass = JzgkyhjVo.class)
//end_dynamic_declare
@Data
public class Jzgkyhj extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041956658L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2024-05-30 16:51:15")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=1,fuzzySearch=false,columnHidden=false,columnXtype="text",tableReadonly=false,tableHidden=false,tableRequired=false,formReadonly=false,formHidden=false,formRequired=false,searchReadonly=false,searchHidden=false,searchRequired=false)
    private String zgh;

    @ApiModelProperty(name = "hjcglx", value = "获奖成果类型" ,notes = "2024-05-30 16:50:31")
    @Comment("获奖成果类型")
    @Column(value = "hjcglx",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="KYCGLX",columnReadonly=false,orderIndex=2,fuzzySearch=false,columnHidden=false,columnXtype="select")
    private String hjcglx;

    @ApiModelProperty(name = "kycgbh", value = "获奖成果编号" ,notes = "2024-05-30 16:52:02")
    @Comment("获奖成果编号")
    @Column(value = "kycgbh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=3,fuzzySearch=false,columnHidden=false,columnXtype="text",tableReadonly=false,tableHidden=false,tableRequired=false,formReadonly=false,formHidden=false,formRequired=false,searchReadonly=false,searchHidden=false,searchRequired=false)
    private String kycgbh;

    @ApiModelProperty(name = "kycgmc", value = "获奖成果名称" ,notes = "2024-05-30 16:52:36")
    @Comment("获奖成果名称")
    @Column(value = "kycgmc",type = ColType.AUTO, width = 900,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=4,fuzzySearch=false,columnHidden=false,columnXtype="text",tableReadonly=false,tableHidden=false,tableRequired=false,formReadonly=false,formHidden=false,formRequired=false,searchReadonly=false,searchHidden=false,searchRequired=false)
    private String kycgmc;

    @ApiModelProperty(name = "jlmc", value = "奖励名称" ,notes = "2024-05-30 16:52:58")
    @Comment("奖励名称")
    @Column(value = "jlmc",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=5,fuzzySearch=false,columnHidden=false,columnXtype="text",tableReadonly=false,tableHidden=false,tableRequired=false,formReadonly=false,formHidden=false,formRequired=false,searchReadonly=false,searchHidden=false,searchRequired=false)
    private String jlmc;

    @ApiModelProperty(name = "hjlb", value = "获奖类别" ,notes = "2024-05-30 16:53:44")
    @Comment("获奖类别")
    @Column(value = "hjlb",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="CGHJLB",columnReadonly=false,orderIndex=6,fuzzySearch=false,columnHidden=false,columnXtype="select",tableReadonly=false,tableHidden=false,tableRequired=false,formReadonly=false,formHidden=false,formRequired=false,searchReadonly=false,searchHidden=false,searchRequired=false)
    private String hjlb;

    @ApiModelProperty(name = "hjjb", value = "获奖级别" ,notes = "2024-05-30 16:54:21")
    @Comment("获奖级别")
    @Column(value = "hjjb",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JB",columnReadonly=false,orderIndex=7,fuzzySearch=false,columnHidden=false,columnXtype="select",tableReadonly=false,tableHidden=false,tableRequired=false,formReadonly=false,formHidden=false,formRequired=false,searchReadonly=false,searchHidden=false,searchRequired=false)
    private String hjjb;

    @ApiModelProperty(name = "hjdj", value = "获奖等级" ,notes = "2024-05-30 16:54:50")
    @Comment("获奖等级")
    @Column(value = "hjdj",type = ColType.AUTO, width = 128,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JLDJ",columnReadonly=false,orderIndex=8,fuzzySearch=false,columnHidden=false,columnXtype="select",tableReadonly=false,tableHidden=false,tableRequired=false,formReadonly=false,formHidden=false,formRequired=false,searchReadonly=false,searchHidden=false,searchRequired=false)
    private String hjdj;

    @ApiModelProperty(name = "hjrq", value = "获奖日期" ,notes = "2024-05-30 16:59:09")
    @Comment("获奖日期")
    @Column(value = "hjrq",type = ColType.AUTO, width = 0,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=9,fuzzySearch=false,columnHidden=false,columnXtype="date-local",tableReadonly=false,tableHidden=false,tableRequired=false,formReadonly=false,formHidden=false,formRequired=false,searchReadonly=false,searchHidden=false,searchXtype="date-range",searchRequired=false)
    private Date hjrq;

    @ApiModelProperty(name = "hjje", value = "获奖金额" ,notes = "2024-05-30 17:01:41")
    @Comment("获奖金额")
    @Column(value = "hjje",type = ColType.AUTO, precision = 4, width = 19,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=10,fuzzySearch=false,columnHidden=false,columnXtype="number",tableReadonly=false,tableHidden=false,tableRequired=false,formReadonly=false,formHidden=false,formRequired=false,searchReadonly=false,searchHidden=false,searchXtype="number-range",searchRequired=false)
    private Double hjje;

    @ApiModelProperty(name = "dwpm", value = "单位排名" ,notes = "2024-05-30 17:02:06")
    @Comment("单位排名")
    @Column(value = "dwpm",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=11,fuzzySearch=false,columnHidden=false,columnXtype="text",tableReadonly=false,tableHidden=false,tableRequired=false,formReadonly=false,formHidden=false,formRequired=false,searchReadonly=false,searchHidden=false,searchRequired=false)
    private String dwpm;

    @ApiModelProperty(name = "smsx", value = "署名顺序" ,notes = "2024-05-30 17:02:28")
    @Comment("署名顺序")
    @Column(value = "smsx",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=12,fuzzySearch=false,columnHidden=false,columnXtype="text",tableReadonly=false,tableHidden=false,tableRequired=false,formReadonly=false,formHidden=false,formRequired=false,searchReadonly=false,searchHidden=false,searchRequired=false)
    private String smsx;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2024-05-31 16:47:59")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=13,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2024-06-19 16:29:59")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=14,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
