package com.wisedu.lowcode4j.main.teapub.vo;

import io.swagger.annotations.*;

import lombok.Data;

import com.wisedu.lowcode4j.main.teapub.po.Jzgjxpjjg;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgjxpjjgVo", description = "教学评价结果Vo")
//end_dynamic_declare
@Data
public class JzgjxpjjgVo extends Jzgjxpjjg {

    private static final long serialVersionUID = 4431474721041955716L;

    //region start_dynamic_dict_column
	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
