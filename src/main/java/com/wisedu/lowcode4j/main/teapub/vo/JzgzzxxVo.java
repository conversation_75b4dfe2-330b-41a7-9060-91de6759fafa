package com.wisedu.lowcode4j.main.teapub.vo;

import com.wisedu.lowcode4j.common.core.constant.*;

import org.sagacity.sqltoy.config.annotation.*;

import io.swagger.annotations.*;

import lombok.Data;
import com.alibaba.fastjson.annotation.*;

import com.wisedu.lowcode4j.main.teapub.po.Jzgzzxx;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgzzxxVo", description = "资质信息Vo")
//end_dynamic_declare
@Data
public class JzgzzxxVo extends Jzgzzxx {

    private static final long serialVersionUID = 4431474721041954373L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "zzlbName", value = "资质类别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "zzlb", cacheType = "JZGZZLB")
    @JSONField(name = "zzlb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String zzlbName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
