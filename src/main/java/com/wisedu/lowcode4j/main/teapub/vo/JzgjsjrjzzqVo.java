package com.wisedu.lowcode4j.main.teapub.vo;

import io.swagger.annotations.*;

import lombok.Data;

import com.wisedu.lowcode4j.main.teapub.po.Jzgjsjrjzzq;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgjsjrjzzqVo", description = "计算机软件著作权Vo")
//end_dynamic_declare
@Data
public class JzgjsjrjzzqVo extends Jzgjsjrjzzq {

    private static final long serialVersionUID = 4431474721041957524L;

    //region start_dynamic_dict_column
	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
