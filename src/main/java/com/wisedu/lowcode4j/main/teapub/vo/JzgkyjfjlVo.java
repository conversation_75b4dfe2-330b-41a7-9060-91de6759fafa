package com.wisedu.lowcode4j.main.teapub.vo;

import com.wisedu.lowcode4j.common.core.constant.*;

import org.sagacity.sqltoy.config.annotation.*;

import io.swagger.annotations.*;

import lombok.Data;
import com.alibaba.fastjson.annotation.*;

import com.wisedu.lowcode4j.main.teapub.po.Jzgkyjfjl;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgkyjfjlVo", description = "科研经费记录Vo")
//end_dynamic_declare
@Data
public class JzgkyjfjlVo extends Jzgkyjfjl {

    private static final long serialVersionUID = 4431474721041954512L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "xmlxName", value = "项目类型名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "xmlx", cacheType = "XMLX")
    @JSONField(name = "xmlx"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String xmlxName;

    @ApiModelProperty(name = "xmlbName", value = "项目类别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "xmlb", cacheType = "XMFL")
    @JSONField(name = "xmlb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String xmlbName;

    @ApiModelProperty(name = "kjxmlbName", value = "科技项目类别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "kjxmlb", cacheType = "KJXMLB")
    @JSONField(name = "kjxmlb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String kjxmlbName;

    @ApiModelProperty(name = "xmlyName", value = "项目来源名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "xmly", cacheType = "XMLY")
    @JSONField(name = "xmly"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String xmlyName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
