package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.main.teapub.vo.JzgkyjfjlVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgkyjfjl", description = "科研经费记录")
@Comment("科研经费记录")
@ModelDefine(modelClassify="kyxx",renderType="table")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_biz_teacher_kyjfjl", unique = false),
})
@Table(value = "BIZ_TEACHER_KYJFJL")
@SqlToyEntity
@ModelExt(extClass = JzgkyjfjlVo.class)
//end_dynamic_declare
@Data
public class Jzgkyjfjl extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041958887L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2024-05-30 17:08:11")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=1,fuzzySearch=false,columnHidden=false,columnXtype="text",tableReadonly=false,tableHidden=false,tableRequired=false,formReadonly=false,formHidden=false,formRequired=false,searchReadonly=false,searchHidden=false,searchRequired=false)
    private String zgh;

    @ApiModelProperty(name = "xmbh", value = "项目编号" ,notes = "2024-05-30 17:09:15")
    @Comment("项目编号")
    @Column(value = "xmbh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=2,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String xmbh;

    @ApiModelProperty(name = "xmmc", value = "项目名称" ,notes = "2024-05-30 17:09:38")
    @Comment("项目名称")
    @Column(value = "xmmc",type = ColType.AUTO, width = 900,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=3,fuzzySearch=false,columnHidden=false,columnXtype="text",tableReadonly=false,tableHidden=false,tableRequired=false,formReadonly=false,formHidden=false,formRequired=false,searchReadonly=false,searchHidden=false,searchRequired=false)
    private String xmmc;

    @ApiModelProperty(name = "xmlx", value = "项目类型" ,notes = "2024-05-30 17:11:32")
    @Comment("项目类型")
    @Column(value = "xmlx",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="XMLX",columnReadonly=false,orderIndex=4,fuzzySearch=false,columnHidden=false,columnXtype="select",tableReadonly=false,tableHidden=false,tableRequired=false,formReadonly=false,formHidden=false,formRequired=false,searchReadonly=false,searchHidden=false,searchRequired=false)
    private String xmlx;

    @ApiModelProperty(name = "xmlb", value = "项目类别" ,notes = "2024-05-30 17:10:47")
    @Comment("项目类别")
    @Column(value = "xmlb",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="XMFL",columnReadonly=false,orderIndex=5,fuzzySearch=false,columnHidden=false,columnXtype="select",tableReadonly=false,tableHidden=false,tableRequired=false,formReadonly=false,formHidden=false,formRequired=false,searchReadonly=false,searchHidden=false,searchRequired=false)
    private String xmlb;

    @ApiModelProperty(name = "kjxmlb", value = "科技项目类别" ,notes = "2024-05-30 17:12:18")
    @Comment("科技项目类别")
    @Column(value = "kjxmlb",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="KJXMLB",columnReadonly=false,orderIndex=6,fuzzySearch=false,columnHidden=false,columnXtype="select")
    private String kjxmlb;

    @ApiModelProperty(name = "xmly", value = "项目来源" ,notes = "2024-05-30 17:12:53")
    @Comment("项目来源")
    @Column(value = "xmly",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="XMLY",columnReadonly=false,orderIndex=7,fuzzySearch=false,columnHidden=false,columnXtype="select",tableReadonly=false,tableHidden=false,tableRequired=false,formReadonly=false,formHidden=false,formRequired=false,searchReadonly=false,searchHidden=false,searchRequired=false)
    private String xmly;

    @ApiModelProperty(name = "xmjfze", value = "项目经费总额" ,notes = "2024-05-30 17:13:35")
    @Comment("项目经费总额")
    @Column(value = "xmjfze",type = ColType.AUTO, precision = 4, width = 19,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=8,fuzzySearch=false,columnHidden=false,columnXtype="number",tableReadonly=false,tableHidden=false,tableRequired=false,formReadonly=false,formHidden=false,formRequired=false,searchReadonly=false,searchHidden=false,searchXtype="number-range",searchRequired=false)
    private Double xmjfze;

    @ApiModelProperty(name = "lxjf", value = "留校经费" ,notes = "2024-05-30 17:14:08")
    @Comment("留校经费")
    @Column(value = "lxjf",type = ColType.AUTO, precision = 4, width = 19,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=9,fuzzySearch=false,columnHidden=false,columnXtype="number",tableReadonly=false,tableHidden=false,tableRequired=false,formReadonly=false,formHidden=false,formRequired=false,searchReadonly=false,searchHidden=false,searchXtype="number-range",searchRequired=false)
    private Double lxjf;

    @ApiModelProperty(name = "wbjf", value = "外拨经费" ,notes = "2024-05-30 17:14:43")
    @Comment("外拨经费")
    @Column(value = "wbjf",type = ColType.AUTO, precision = 4, width = 19,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=10,fuzzySearch=false,columnHidden=false,columnXtype="number",tableReadonly=false,tableHidden=false,tableRequired=false,formReadonly=false,formHidden=false,formRequired=false,searchReadonly=false,searchHidden=false,searchXtype="number-range",searchRequired=false)
    private Double wbjf;

    @ApiModelProperty(name = "dzjf", value = "到账经费" ,notes = "2024-05-30 17:15:13")
    @Comment("到账经费")
    @Column(value = "dzjf",type = ColType.AUTO, precision = 4, width = 19,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=11,fuzzySearch=false,columnHidden=false,columnXtype="number",tableReadonly=false,tableHidden=false,tableRequired=false,formReadonly=false,formHidden=false,formRequired=false,searchReadonly=false,searchHidden=false,searchXtype="number-range",searchRequired=false)
    private Double dzjf;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2024-05-31 16:47:59")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=12,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2024-06-19 16:29:59")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=13,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
