package com.wisedu.lowcode4j.main.teapub.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.wisedu.lowcode4j.common.core.constant.ApplicationConstant;
import com.wisedu.lowcode4j.common.core.constant.DictCacheType;
import com.wisedu.lowcode4j.main.teapub.po.Jzgjxcg;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.sagacity.sqltoy.config.annotation.Translate;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgjxcgVo", description = "教学成果Vo")
//end_dynamic_declare
@Data
public class JzgjxcgVo extends Jzgjxcg {

    private static final long serialVersionUID = 4431474721041956362L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "xkmlName", value = "学科门类名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "xkml", cacheType = "XWSYHRCPYXKML")
    @JSONField(name = "xkml"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String xkmlName;

    @ApiModelProperty(name = "yjxkName", value = "一级学科名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "yjxk", cacheType = "XWSYHRCPYXKML")
    @JSONField(name = "yjxk"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String yjxkName;

    @ApiModelProperty(name = "jsName", value = "角色名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "js", cacheType = "JS")
    @JSONField(name = "js"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String jsName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
