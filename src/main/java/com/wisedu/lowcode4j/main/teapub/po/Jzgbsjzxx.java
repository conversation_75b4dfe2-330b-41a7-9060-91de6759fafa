package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.main.teapub.vo.JzgbsjzxxVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgbsjzxx", description = "比赛兼职信息")
@Comment("比赛兼职信息")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_BIZ_TEACHER_BSJZXX", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_BIZ_TEACHER_BSJZXX", unique = false)
})
@Table(value = "BIZ_TEACHER_BSJZXX")
@SqlToyEntity
@ModelExt(extClass = JzgbsjzxxVo.class )
@ModelDefine(orderIndex = 9,modelClassify="rsxx", renderType="table")
//end_dynamic_declare
@Data
public class Jzgbsjzxx extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041952183L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-09 10:17:10")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=1,columnHidden=false,columnXtype="text",formRequired=false)
    private String zgh;

    @ApiModelProperty(name = "bsmc", value = "比赛名称" ,notes = "2023-11-09 10:17:38")
    @Comment("比赛名称")
    @Column(value = "bsmc",type = ColType.AUTO, width = 540,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=2,columnHidden=false,columnXtype="text",formRequired=false)
    private String bsmc;

    @ApiModelProperty(name = "bsqsrq", value = "比赛起始日期" ,notes = "2023-11-09 10:18:38")
    @Comment("比赛起始日期")
    @Column(value = "bsqsrq",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=6,columnHidden=false,columnXtype="date-local",formRequired=false)
    private String bsqsrq;

    @ApiModelProperty(name = "bsjsrq", value = "比赛结束日期" ,notes = "2023-11-09 10:18:53")
    @Comment("比赛结束日期")
    @Column(value = "bsjsrq",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=7,columnHidden=false,columnXtype="date-local",formRequired=false)
    private String bsjsrq;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-09 10:19:08")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "bslx", value = "比赛类型" ,notes = "2023-11-09 10:17:55")
    @Comment("比赛类型")
    @Column(value = "bslx",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="BSLX",columnReadonly=false,fuzzySearch=false,orderIndex=3,columnHidden=false,columnXtype="select",formRequired=false)
    private String bslx;

    @ApiModelProperty(name = "bsjb", value = "比赛级别" ,notes = "2023-11-09 10:18:10")
    @Comment("比赛级别")
    @Column(value = "bsjb",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JB",columnReadonly=false,fuzzySearch=false,orderIndex=4,columnHidden=false,columnXtype="select",formRequired=false)
    private String bsjb;

    @ApiModelProperty(name = "drzw", value = "担任职务" ,notes = "2023-11-09 10:18:21")
    @Comment("担任职务")
    @Column(value = "drzw",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="BSZW",columnReadonly=false,fuzzySearch=false,orderIndex=5,columnHidden=false,columnXtype="select",formRequired=false)
    private String drzw;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=8,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
