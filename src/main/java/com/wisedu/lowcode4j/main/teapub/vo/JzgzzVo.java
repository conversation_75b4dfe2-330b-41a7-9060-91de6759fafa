package com.wisedu.lowcode4j.main.teapub.vo;

import com.wisedu.lowcode4j.common.core.constant.*;

import org.sagacity.sqltoy.config.annotation.*;

import io.swagger.annotations.*;

import lombok.Data;
import com.alibaba.fastjson.annotation.*;

import com.wisedu.lowcode4j.main.teapub.po.Jzgzz;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgzzVo", description = "著作Vo")
//end_dynamic_declare
@Data
public class JzgzzVo extends Jzgzz {

    private static final long serialVersionUID = 4431474721041958862L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "cbsjbName", value = "出版社级别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "cbsjb", cacheType = "CBSJB")
    @JSONField(name = "cbsjb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String cbsjbName;

    @ApiModelProperty(name = "cbsszgjdqName", value = "出版社所在国家地区名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "cbsszgjdq", cacheType = "GJHDQ")
    @JSONField(name = "cbsszgjdq"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String cbsszgjdqName;

    @ApiModelProperty(name = "yzName", value = "语种名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "yz", cacheType = "YZ")
    @JSONField(name = "yz"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String yzName;

    @ApiModelProperty(name = "zgyzName", value = "中国语种名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "zgyz", cacheType = "ZGYZ")
    @JSONField(name = "zgyz"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String zgyzName;

    @ApiModelProperty(name = "lzlbName", value = "论著类别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "lzlb", cacheType = "LZLB")
    @JSONField(name = "lzlb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String lzlbName;

    @ApiModelProperty(name = "sfsbName", value = "是否首版名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "sfsb", cacheType = "SFBZ")
    @JSONField(name = "sfsb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String sfsbName;

    @ApiModelProperty(name = "jsName", value = "角色名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "js", cacheType = "JS")
    @JSONField(name = "js"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String jsName;

    @ApiModelProperty(name = "sfdyzzName", value = "是否第一作者名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "sfdyzz", cacheType = "SFBZ")
    @JSONField(name = "sfdyzz"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String sfdyzzName;

    @ApiModelProperty(name = "sfbxdyzzName", value = "是否本校第一作者名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "sfbxdyzz", cacheType = "SFBZ")
    @JSONField(name = "sfbxdyzz"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String sfbxdyzzName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
