package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.main.teapub.vo.JzgkchjVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgkchj", description = "课程获奖")
@Comment("课程获奖")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_biz_teacher_kchj", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_biz_teacher_kchj", unique = false)
})
@Table(value = "BIZ_TEACHER_KCHJ")
@SqlToyEntity
@ModelExt(extClass = JzgkchjVo.class )
@ModelDefine(orderIndex = 2,modelClassify="jyjx", renderType="table")
//end_dynamic_declare
@Data
public class Jzgkchj extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041958428L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-27 16:39:08")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=1,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zgh;

    @ApiModelProperty(name = "kcbm", value = "课程编码" ,notes = "2023-11-27 16:39:33")
    @Comment("课程编码")
    @Column(value = "kcbm",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=2,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String kcbm;

    @ApiModelProperty(name = "kcmc", value = "课程名称" ,notes = "2023-11-27 16:39:59")
    @Comment("课程名称")
    @Column(value = "kcmc",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=3,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String kcmc;

    @ApiModelProperty(name = "kcywm", value = "课程英文名" ,notes = "2023-11-27 16:40:27")
    @Comment("课程英文名")
    @Column(value = "kcywm",type = ColType.AUTO, width = 1500,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=4,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String kcywm;

    @ApiModelProperty(name = "kclx", value = "课程类型" ,notes = "2023-11-27 16:42:46")
    @Comment("课程类型")
    @Column(value = "kclx",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="KCLX",columnReadonly=false,orderIndex=5,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String kclx;

    @ApiModelProperty(name = "kcjb", value = "课程级别" ,notes = "2023-11-27 16:48:43")
    @Comment("课程级别")
    @Column(value = "kcjb",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="KCJB",columnReadonly=false,orderIndex=6,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String kcjb;

    @ApiModelProperty(name = "sfzjr", value = "是否主讲人" ,notes = "2023-11-27 16:50:41")
    @Comment("是否主讲人")
    @Column(value = "sfzjr",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SFBZ",columnReadonly=false,orderIndex=7,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String sfzjr;

    @ApiModelProperty(name = "jlmc", value = "奖励名称" ,notes = "2023-11-27 16:51:11")
    @Comment("奖励名称")
    @Column(value = "jlmc",type = ColType.AUTO, width = 540,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=8,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String jlmc;

    @ApiModelProperty(name = "jljb", value = "奖励级别" ,notes = "2023-11-27 16:51:54")
    @Comment("奖励级别")
    @Column(value = "jljb",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JB",columnReadonly=false,orderIndex=9,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String jljb;

    @ApiModelProperty(name = "jldj", value = "奖励等级" ,notes = "2023-11-27 16:52:39")
    @Comment("奖励等级")
    @Column(value = "jldj",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JLDJ",columnReadonly=false,orderIndex=10,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String jldj;

    @ApiModelProperty(name = "bjdw", value = "颁奖单位" ,notes = "2023-11-27 16:53:03")
    @Comment("颁奖单位")
    @Column(value = "bjdw",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=11,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String bjdw;

    @ApiModelProperty(name = "bjrq", value = "颁奖日期" ,notes = "2023-11-27 16:53:38")
    @Comment("颁奖日期")
    @Column(value = "bjrq",type = ColType.AUTO, width = 0,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=12,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private Date bjrq;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-27 17:29:22")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=13,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=14,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
