package com.wisedu.lowcode4j.main.teapub.vo;

import com.wisedu.lowcode4j.common.core.constant.*;

import org.sagacity.sqltoy.config.annotation.*;

import io.swagger.annotations.*;

import lombok.Data;
import com.alibaba.fastjson.annotation.*;

import com.wisedu.lowcode4j.main.teapub.po.Jzgjljryxx;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgjljryxxVo", description = "奖励及荣誉信息Vo")
//end_dynamic_declare
@Data
public class JzgjljryxxVo extends Jzgjljryxx {

    private static final long serialVersionUID = 4431474721041952122L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "jljbName", value = "奖励级别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "jljb", cacheType = "JB")
    @JSONField(name = "jljb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String jljbName;

    @ApiModelProperty(name = "rychName", value = "荣誉称号名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "rych", cacheType = "RYCH")
    @JSONField(name = "rych"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String rychName;

    @ApiModelProperty(name = "jldjName", value = "奖励等级名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "jldj", cacheType = "JLDJ")
    @JSONField(name = "jldj"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String jldjName;

    @ApiModelProperty(name = "jllbName", value = "奖励类别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "jllb", cacheType = "JSHJLB")
    @JSONField(name = "jllb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String jllbName;

    @ApiModelProperty(name = "jlfsName", value = "奖励方式名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "jlfs", cacheType = "JLFS")
    @JSONField(name = "jlfs"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String jlfsName;

    @ApiModelProperty(name = "jljsName", value = "获奖角色名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "jljs", cacheType = "JS")
    @JSONField(name = "jljs"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String jljsName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
