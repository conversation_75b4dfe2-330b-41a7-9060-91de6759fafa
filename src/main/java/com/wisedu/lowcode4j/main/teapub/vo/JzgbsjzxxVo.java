package com.wisedu.lowcode4j.main.teapub.vo;

import com.wisedu.lowcode4j.common.core.constant.*;

import org.sagacity.sqltoy.config.annotation.*;

import io.swagger.annotations.*;

import lombok.Data;
import com.alibaba.fastjson.annotation.*;

import com.wisedu.lowcode4j.main.teapub.po.Jzgbsjzxx;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgbsjzxxVo", description = "比赛兼职信息Vo")
//end_dynamic_declare
@Data
public class JzgbsjzxxVo extends Jzgbsjzxx {

    private static final long serialVersionUID = 4431474721041956284L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "bslxName", value = "比赛类型名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "bslx", cacheType = "BSLX")
    @JSONField(name = "bslx"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String bslxName;

    @ApiModelProperty(name = "bsjbName", value = "比赛级别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "bsjb", cacheType = "JB")
    @JSONField(name = "bsjb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String bsjbName;

    @ApiModelProperty(name = "drzwName", value = "担任职务名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "drzw", cacheType = "BSZW")
    @JSONField(name = "drzw"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String drzwName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
