package com.wisedu.lowcode4j.main.teapub.vo;

import com.wisedu.lowcode4j.common.core.constant.*;

import org.sagacity.sqltoy.config.annotation.*;

import io.swagger.annotations.*;

import lombok.Data;
import com.alibaba.fastjson.annotation.*;

import com.wisedu.lowcode4j.main.teapub.po.Jzgxsqkjzxx;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgxsqkjzxxVo", description = "学术期刊兼职信息Vo")
//end_dynamic_declare
@Data
public class JzgxsqkjzxxVo extends Jzgxsqkjzxx {

    private static final long serialVersionUID = 4431474721041956343L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "kwjbName", value = "刊物级别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "kwjb", cacheType = "KWJB")
    @JSONField(name = "kwjb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String kwjbName;

    @ApiModelProperty(name = "gjdqName", value = "国家地区名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "gjdq", cacheType = "GJHDQ")
    @JSONField(name = "gjdq"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String gjdqName;

    @ApiModelProperty(name = "dzzwName", value = "担任职位名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "dzzw", cacheType = "JS")
    @JSONField(name = "dzzw"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String dzzwName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
