package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.main.teapub.vo.JzggnjxVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzggnjx", description = "国内进修")
@Comment("国内进修")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_BIZ_TEACHER_GNJX", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_BIZ_TEACHER_GNJX", unique = false)
})
@Table(value = "BIZ_TEACHER_GNJX")
@SqlToyEntity
@ModelExt(extClass = JzggnjxVo.class )
@ModelDefine(orderIndex = 14,modelClassify="rsxx", renderType="table")
//end_dynamic_declare
@Data
public class Jzggnjx extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041955583L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-09 13:39:19")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=1,columnHidden=false,columnXtype="text",formRequired=false)
    private String zgh;

    @ApiModelProperty(name = "jxbmc", value = "进修班名称" ,notes = "2023-11-09 14:42:15")
    @Comment("进修班名称")
    @Column(value = "jxbmc",type = ColType.AUTO, width = 240,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=3,columnHidden=false,columnXtype="text",formRequired=false)
    private String jxbmc;

    @ApiModelProperty(name = "jxzy", value = "进修专业" ,notes = "2023-11-09 14:43:31")
    @Comment("进修专业")
    @Column(value = "jxzy",type = ColType.AUTO, width = 240,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=5,columnHidden=false,columnXtype="text",formRequired=false)
    private String jxzy;

    @ApiModelProperty(name = "jxfx", value = "进修方向" ,notes = "2023-11-09 14:44:03")
    @Comment("进修方向")
    @Column(value = "jxfx",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=6,columnHidden=false,columnXtype="text",formRequired=false)
    private String jxfx;

    @ApiModelProperty(name = "kssj", value = "开始时间" ,notes = "2023-11-09 14:44:27")
    @Comment("开始时间")
    @Column(value = "kssj",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=7,columnHidden=false,columnXtype="date-local",formRequired=false)
    private String kssj;

    @ApiModelProperty(name = "jssj", value = "结束时间" ,notes = "2023-11-09 14:44:49")
    @Comment("结束时间")
    @Column(value = "jssj",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=8,columnHidden=false,columnXtype="date-local",formRequired=false)
    private String jssj;

    @ApiModelProperty(name = "zbdw", value = "主办单位" ,notes = "2023-11-09 14:45:14")
    @Comment("主办单位")
    @Column(value = "zbdw",type = ColType.AUTO, width = 540,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=9,columnHidden=false,columnXtype="text",formRequired=false)
    private String zbdw;

    @ApiModelProperty(name = "zbdwxz", value = "主办单位性质" ,notes = "2023-11-09 14:46:28")
    @Comment("主办单位性质")
    @Column(value = "zbdwxz",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SHDWXZ",columnReadonly=false,orderIndex=10,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String zbdwxz;

    @ApiModelProperty(name = "zxdw", value = "在学单位" ,notes = "2023-11-09 14:47:04")
    @Comment("在学单位")
    @Column(value = "zxdw",type = ColType.AUTO, width = 540,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=11,columnHidden=false,columnXtype="text",formRequired=false)
    private String zxdw;

    @ApiModelProperty(name = "jxxs", value = "进修学时" ,notes = "2023-11-09 14:51:30")
    @Comment("进修学时")
    @Column(value = "jxxs",type = ColType.AUTO, precision = 2, width = 19,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=14,columnHidden=false,columnXtype="number",formRequired=false)
    private String jxxs;

    @ApiModelProperty(name = "zfy", value = "总费用" ,notes = "2023-11-09 14:53:39")
    @Comment("总费用")
    @Column(value = "zfy",type = ColType.AUTO, precision = 4, width = 19,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=15,columnHidden=false,columnXtype="number",formRequired=false)
    private Double zfy;

    @ApiModelProperty(name = "grcdfy", value = "个人承担费用" ,notes = "2023-11-09 14:54:51")
    @Comment("个人承担费用")
    @Column(value = "grcdfy",type = ColType.AUTO, precision = 4, width = 19,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=16,columnHidden=false,columnXtype="number",formRequired=false)
    private Double grcdfy;

    @ApiModelProperty(name = "xxcdfy", value = "学校承担费用" ,notes = "2023-11-09 14:55:23")
    @Comment("学校承担费用")
    @Column(value = "xxcdfy",type = ColType.AUTO, precision = 4, width = 19,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=17,columnHidden=false,columnXtype="number",formRequired=false)
    private Double xxcdfy;

    @ApiModelProperty(name = "zzfs", value = "资助方式" ,notes = "2023-11-09 14:56:08")
    @Comment("资助方式")
    @Column(value = "zzfs",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=18,columnHidden=false,columnXtype="text",formRequired=false)
    private String zzfs;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-09 14:56:25")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "jxxz", value = "进修性质" ,notes = "2023-11-09 14:41:47")
    @Comment("进修性质")
    @Column(value = "jxxz",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="PX、JXBLB",columnReadonly=false,fuzzySearch=false,orderIndex=2,columnHidden=false,columnXtype="select",formRequired=false)
    private String jxxz;

    @ApiModelProperty(name = "xxfs", value = "学习方式" ,notes = "2023-11-09 14:43:04")
    @Comment("学习方式")
    @Column(value = "xxfs",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JYLB",columnReadonly=false,fuzzySearch=false,orderIndex=4,columnHidden=false,columnXtype="select",formRequired=false)
    private String xxfs;

    @ApiModelProperty(name = "zxdwlb", value = "在学单位类别" ,notes = "2023-11-09 14:48:07")
    @Comment("在学单位类别")
    @Column(value = "zxdwlb",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="ZXDWLB",columnReadonly=false,fuzzySearch=false,orderIndex=12,columnHidden=false,columnXtype="select",formRequired=false)
    private String zxdwlb;

    @ApiModelProperty(name = "jxjg", value = "进修结果" ,notes = "2023-11-09 14:50:00")
    @Comment("进修结果")
    @Column(value = "jxjg",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JYPXJG",columnReadonly=false,fuzzySearch=false,orderIndex=13,columnHidden=false,columnXtype="select",formRequired=false)
    private String jxjg;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=19,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
