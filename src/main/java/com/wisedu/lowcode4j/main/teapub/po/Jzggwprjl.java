package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.main.teapub.vo.JzggwprjlVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzggwprjl", description = "岗位聘任记录")
@Comment("岗位聘任记录")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_BIZ_TEACHER_GWPRJL", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_BIZ_TEACHER_GWPRJL", unique = false)
})
@Table(value = "BIZ_TEACHER_GWPRJL")
@SqlToyEntity
@ModelExt(extClass = JzggwprjlVo.class )
@ModelDefine(orderIndex = 18,modelClassify="rsxx", renderType="table")
//end_dynamic_declare
@Data
public class Jzggwprjl extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041951771L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-09 10:55:40")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=1,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zgh;

    @ApiModelProperty(name = "gwmc", value = "岗位名称" ,notes = "2023-11-09 10:56:13")
    @Comment("岗位名称")
    @Column(value = "gwmc",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=2,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String gwmc;

    @ApiModelProperty(name = "prqsrq", value = "聘任起始日期" ,notes = "2023-11-09 10:56:56")
    @Comment("聘任起始日期")
    @Column(value = "prqsrq",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=3,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private String prqsrq;

    @ApiModelProperty(name = "przzrq", value = "聘任终止日期" ,notes = "2023-11-09 10:57:38")
    @Comment("聘任终止日期")
    @Column(value = "przzrq",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=4,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private String przzrq;

    @ApiModelProperty(name = "sjprjsrq", value = "实际聘任结束日期" ,notes = "2023-11-09 10:58:19")
    @Comment("实际聘任结束日期")
    @Column(value = "sjprjsrq",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=5,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private String sjprjsrq;

    @ApiModelProperty(name = "sfzr", value = "是否在任" ,notes = "2023-11-09 10:58:50")
    @Comment("是否在任")
    @Column(value = "sfzr",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SFBZ",orderIndex=6,columnReadonly=false,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String sfzr;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-09 14:38:35")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=7,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=8,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
