package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.main.teapub.vo.JzggbzwxxVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzggbzwxx", description = "干部职务信息")
@Comment("干部职务信息")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_BIZ_TEACHER_GBZWXX", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_BIZ_TEACHER_GBZWXX", unique = false)
})
@Table(value = "BIZ_TEACHER_GBZWXX")
@SqlToyEntity
@ModelExt(extClass = JzggbzwxxVo.class )
@ModelDefine(orderIndex = 21,modelClassify="rsxx", renderType="table")
//end_dynamic_declare
@Data
public class Jzggbzwxx extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041956025L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-09 09:52:53")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=1,columnHidden=false,columnXtype="text",formRequired=false)
    private String zgh;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-09 09:53:43")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "zwmc", value = "职务名称" ,notes = "2023-11-09 09:56:25")
    @Comment("职务名称")
    @Column(value = "zwmc",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=2,columnHidden=false,columnXtype="text",formRequired=false)
    private String zwmc;

    @ApiModelProperty(name = "rzny", value = "任职年月" ,notes = "2023-11-09 10:03:05")
    @Comment("任职年月")
    @Column(value = "rzny",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=6,columnHidden=false,columnXtype="date-ym",columnFormat="yyyy-MM",formRequired=false)
    private String rzny;

    @ApiModelProperty(name = "rzqx", value = "任职期限" ,notes = "2023-11-09 10:07:08")
    @Comment("任职期限")
    @Column(value = "rzqx",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=8,columnHidden=false,columnXtype="text",formRequired=false)
    private String rzqx;

    @ApiModelProperty(name = "sfzr", value = "是否在任" ,notes = "2023-11-09 10:08:26")
    @Comment("是否在任")
    @Column(value = "sfzr",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SFBZ",columnReadonly=false,fuzzySearch=false,orderIndex=9,columnHidden=false,columnXtype="select",formRequired=false)
    private String sfzr;

    @ApiModelProperty(name = "sfzz", value = "是否主职" ,notes = "2023-11-09 10:09:08")
    @Comment("是否主职")
    @Column(value = "sfzz",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SFBZ",columnReadonly=false,fuzzySearch=false,orderIndex=10,columnHidden=false,columnXtype="select",formRequired=false)
    private String sfzz;

    @ApiModelProperty(name = "mzrq", value = "免职日期" ,notes = "2023-11-09 10:09:59")
    @Comment("免职日期")
    @Column(value = "mzrq",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=11,columnHidden=false,columnXtype="date-local",formRequired=false)
    private String mzrq;

    @ApiModelProperty(name = "zwlb", value = "职务类别" ,notes = "2023-11-09 09:59:04")
    @Comment("职务类别")
    @Column(value = "zwlb",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="ZWLB",columnReadonly=false,fuzzySearch=false,orderIndex=3,columnHidden=false,columnXtype="select",formRequired=false)
    private String zwlb;

    @ApiModelProperty(name = "zwjb", value = "职务级别" ,notes = "2023-11-09 10:00:00")
    @Comment("职务级别")
    @Column(value = "zwjb",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="ZWJB",columnReadonly=false,fuzzySearch=false,orderIndex=4,columnHidden=false,columnXtype="select",formRequired=false)
    private String zwjb;

    @ApiModelProperty(name = "rzdw", value = "任职单位" ,notes = "2023-11-09 10:01:49")
    @Comment("任职单位")
    @Column(value = "rzdw",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="biz_department",columnReadonly=false,fuzzySearch=false,orderIndex=5,columnHidden=false,columnXtype="select",formRequired=false)
    private String rzdw;

    @ApiModelProperty(name = "rzfs", value = "任职方式" ,notes = "2023-11-09 10:06:03")
    @Comment("任职方式")
    @Column(value = "rzfs",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="RZFS",columnReadonly=false,fuzzySearch=false,orderIndex=7,columnHidden=false,columnXtype="select",formRequired=false)
    private String rzfs;

    @ApiModelProperty(name = "mzfs", value = "免职方式" ,notes = "2023-11-09 10:13:37")
    @Comment("免职方式")
    @Column(value = "mzfs",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="MZFS",columnReadonly=false,fuzzySearch=false,orderIndex=12,columnHidden=false,columnXtype="select",formRequired=false)
    private String mzfs;

    @ApiModelProperty(name = "mzyy", value = "免职原因" ,notes = "2023-11-09 10:14:10")
    @Comment("免职原因")
    @Column(value = "mzyy",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="MZ、CZYY",columnReadonly=false,fuzzySearch=false,orderIndex=13,columnHidden=false,columnXtype="select",formRequired=false)
    private String mzyy;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=14,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
