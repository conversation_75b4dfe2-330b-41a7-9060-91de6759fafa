package com.wisedu.lowcode4j.main.teapub.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.wisedu.lowcode4j.common.core.annotation.ColumnDefine;
import com.wisedu.lowcode4j.common.core.constant.ApplicationConstant;
import com.wisedu.lowcode4j.common.core.constant.DictCacheType;
import com.wisedu.lowcode4j.main.teapub.po.ExtTeacher;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Comment;
import org.sagacity.sqltoy.config.annotation.Translate;

import java.util.Date;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "bizteacherVo", description = "教职工基本信息Vo")
//end_dynamic_declare
@Data
public class ExtTeacherVo extends ExtTeacher {

    private static final long serialVersionUID = 4431474721041958446L;

    //region start_dynamic_dict_column
	//endregion end_dynamic_dict_column

    //region start_dynamic_column
    @ApiModelProperty(name = "extZghCopy", value = "教职工号" ,notes = "2023-09-13 17:07:19")
    @Comment("教职工号")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="zgh",columnGroup="人事信息",columnReadonly=false,orderIndex=1,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String extZghCopy;

    @ApiModelProperty(name = "extTeacherNameCopy", value = "姓名" ,notes = "2023-09-13 17:07:19")
    @Comment("姓名")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="teacherName",columnGroup="个人信息",columnReadonly=false,orderIndex=1,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String extTeacherNameCopy;

    @ApiModelProperty(name = "extEnglishNameCopy", value = "姓名拼音" ,notes = "2023-09-13 17:07:19")
    @Comment("姓名拼音")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="englishName",columnGroup="个人信息",columnReadonly=false,orderIndex=2,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String extEnglishNameCopy;

    @ApiModelProperty(name = "extBirthDateCopy", value = "出生日期" ,notes = "2023-09-13 17:07:19")
    @Comment("出生日期")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="birthDate",columnGroup="个人信息",columnReadonly=false,orderIndex=11,fuzzySearch=false,columnHidden=false,columnFormat="yyyy-MM-dd",columnXtype="date-local",formRequired=false)
    private Date extBirthDateCopy;

    @ApiModelProperty(name = "extIdcardTypeCodeCopy", value = "身份证件类型" ,notes = "2023-09-13 17:07:19")
    @Comment("身份证件类型")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="idcardTypeCode",columnDict="SFZJLX",columnGroup="个人信息",columnReadonly=false,orderIndex=16,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String extIdcardTypeCodeCopy;

    @ApiModelProperty(name = "extIdcardNumberCopy", value = "身份证件号" ,notes = "2023-09-13 17:07:19")
    @Comment("身份证件号")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="idcardNumber",columnGroup="个人信息",columnReadonly=false,orderIndex=17,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String extIdcardNumberCopy;

    @ApiModelProperty(name = "extMobileCopy", value = "手机号码" ,notes = "2023-09-13 17:07:19")
    @Comment("手机号码")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="mobile",columnGroup="联系方式",columnReadonly=false,orderIndex=20,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String extMobileCopy;

    @ApiModelProperty(name = "extEmailCopy", value = "电子信箱" ,notes = "2023-09-13 17:07:19")
    @Comment("电子信箱")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="email",columnGroup="联系方式",columnReadonly=false,orderIndex=21,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String extEmailCopy;

    @ApiModelProperty(name = "extStatusCopy", value = "教职工当前状态" ,notes = "2023-09-13 17:07:19")
    @Comment("教职工当前状态")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="status",columnDict="JZGDQZT",columnGroup="用工信息",columnReadonly=false,orderIndex=53,fuzzySearch=false,columnHidden=false,columnXtype="select-tree",formRequired=false,formJsonparam="{\"multiple\":false}")
    private String extStatusCopy;

    @ApiModelProperty(name = "extGenderCodeCopy", value = "性别" ,notes = "2023-09-13 17:07:19")
    @Comment("性别")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="genderCode",columnDict="XB",columnGroup="个人信息",columnReadonly=false,orderIndex=4,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String extGenderCodeCopy;

    @ApiModelProperty(name = "extProfessionalPositionCopy", value = "专业技术职务" ,notes = "2023-09-13 17:07:19")
    @Comment("专业技术职务")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="professionalPosition",columnDict="ZYJSZW",columnGroup="用工信息",columnReadonly=false,orderIndex=56,fuzzySearch=false,columnHidden=false,columnXtype="select-tree",formRequired=false,formJsonparam="{\"multiple\":false,\"checkStrictly\":true}")
    private String extProfessionalPositionCopy;

    @ApiModelProperty(name = "extNationCodeCopy", value = "民族" ,notes = "2023-09-13 17:07:19")
    @Comment("民族")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="nationCode",columnDict="MZ",columnGroup="个人信息",columnReadonly=false,orderIndex=15,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String extNationCodeCopy;

    @ApiModelProperty(name = "extPoliticalCodeCopy", value = "政治面貌" ,notes = "2023-09-13 17:07:19")
    @Comment("政治面貌")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="politicalCode",columnDict="ZZMM",columnGroup="个人信息",columnReadonly=false,orderIndex=8,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String extPoliticalCodeCopy;

    @ApiModelProperty(name = "extOriginalBirthPlaceCopy", value = "籍贯" ,notes = "2023-09-13 17:07:19")
    @Comment("籍贯")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="originalBirthPlace",columnDict="XZQH",columnGroup="个人信息",columnReadonly=false,orderIndex=13,fuzzySearch=false,columnHidden=false,columnXtype="select-tree",formRequired=false,formJsonparam="{\"multiple\":false,\"checkStrictly\":true}")
    private String extOriginalBirthPlaceCopy;

    @ApiModelProperty(name = "extDepartmentCodeCopy", value = "所属机构" ,notes = "2023-09-13 17:07:19")
    @Comment("所属机构")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="departmentCode",columnDict="biz_department",columnGroup="用工信息",columnReadonly=false,orderIndex=31,fuzzySearch=false,columnHidden=false,columnXtype="select-tree",formRequired=false,formJsonparam="{\"multiple\":false,\"checkStrictly\":true}")
    private String extDepartmentCodeCopy;

    @ApiModelProperty(name = "extCensusRegisterCopy", value = "户口所在地" ,notes = "2023-09-13 17:07:19")
    @Comment("户口所在地")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="censusRegister",columnDict="XZQH",columnGroup="个人信息",columnReadonly=false,orderIndex=9,fuzzySearch=false,columnHidden=false,columnXtype="select-tree",formRequired=false,formJsonparam="{\"multiple\":false,\"checkStrictly\":true}")
    private String extCensusRegisterCopy;

    @ApiModelProperty(name = "extTeacherCategoryCopy", value = "教职工类别" ,notes = "2023-09-13 17:07:19")
    @Comment("教职工类别")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="teacherCategory",columnDict="JZGLB",columnGroup="用工信息",columnReadonly=false,orderIndex=32,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String extTeacherCategoryCopy;

    @ApiModelProperty(name = "extIsAdvisorCopy", value = "是否导师" ,notes = "2023-09-13 17:07:19")
    @Comment("是否导师")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="isAdvisor",columnDict="SFBZ",columnGroup="用工信息",columnReadonly=false,orderIndex=41,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String extIsAdvisorCopy;

    @ApiModelProperty(name = "extIsInstructorCopy", value = "是否辅导员" ,notes = "2023-09-13 17:07:19")
    @Comment("是否辅导员")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="isInstructor",columnDict="SFBZ",columnGroup="用工信息",columnReadonly=false,orderIndex=44,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String extIsInstructorCopy;

    @ApiModelProperty(name = "extBloodTypeCodeCopy", value = "血型" ,notes = "2023-09-13 17:07:19")
    @Comment("血型")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="bloodTypeCode",columnDict="XX",columnGroup="个人信息",columnReadonly=false,orderIndex=10,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String extBloodTypeCodeCopy;

    @ApiModelProperty(name = "extHealthCopy", value = "健康状况" ,notes = "2023-09-13 17:07:19")
    @Comment("健康状况")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="health",columnDict="JKZK",columnGroup="个人信息",columnReadonly=false,orderIndex=19,fuzzySearch=false,columnHidden=false,columnXtype="select-tree",formRequired=false)
    private String extHealthCopy;

    @ApiModelProperty(name = "extBankCardNumberCopy", value = "银行卡号" ,notes = "2023-09-13 17:07:19")
    @Comment("银行卡号")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="bankCardNumber",columnGroup="账号信息",columnReadonly=false,orderIndex=74,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String extBankCardNumberCopy;

    @ApiModelProperty(name = "extBankNameCopy", value = "银行卡开户行" ,notes = "2023-09-13 17:07:19")
    @Comment("银行卡开户行")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="bankName",columnGroup="账号信息",columnReadonly=false,orderIndex=75,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String extBankNameCopy;

    @ApiModelProperty(name = "extCardNumberCopy", value = "一卡通卡号" ,notes = "2023-09-13 17:07:19")
    @Comment("一卡通卡号")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="cardNumber",columnGroup="账号信息",columnReadonly=false,orderIndex=76,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String extCardNumberCopy;

    @ApiModelProperty(name = "extNetAccountCopy", value = "网络账号" ,notes = "2023-09-13 17:07:19")
    @Comment("网络账号")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="netAccount",columnGroup="账号信息",columnReadonly=false,orderIndex=77,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String extNetAccountCopy;

    @ApiModelProperty(name = "extUrgentMobileCopy", value = "紧急联系人手机号码" ,notes = "2023-09-13 17:07:19")
    @Comment("紧急联系人手机号码")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="urgentMobile",columnGroup="联系方式",columnReadonly=false,orderIndex=29,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String extUrgentMobileCopy;

    @ApiModelProperty(name = "extHouseAddressCopy", value = "家庭地址" ,notes = "2023-09-13 17:07:19")
    @Comment("家庭地址")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="houseAddress",columnGroup="联系方式",columnReadonly=false,orderIndex=25,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String extHouseAddressCopy;

    @ApiModelProperty(name = "extEmailAccountCopy", value = "邮箱账号" ,notes = "2023-09-13 17:07:19")
    @Comment("邮箱账号")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="emailAccount",columnGroup="账号信息",columnReadonly=false,orderIndex=78,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String extEmailAccountCopy;

    @ApiModelProperty(name = "extTelephoneCopy", value = "联系电话" ,notes = "2023-09-13 17:07:19")
    @Comment("联系电话")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="telephone",columnGroup="联系方式",columnReadonly=false,orderIndex=22,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String extTelephoneCopy;

    @ApiModelProperty(name = "extConcatInSchoolCopy", value = "在校通讯地址" ,notes = "2023-09-13 17:07:19")
    @Comment("在校通讯地址")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="concatInSchool",columnGroup="联系方式",columnReadonly=false,orderIndex=23,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String extConcatInSchoolCopy;

    @ApiModelProperty(name = "extPostcodeInSchoolCopy", value = "在校邮政编码" ,notes = "2023-09-13 17:07:19")
    @Comment("在校邮政编码")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="postcodeInSchool",columnGroup="联系方式",columnReadonly=false,orderIndex=24,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String extPostcodeInSchoolCopy;

    @ApiModelProperty(name = "extHousePostcodeCopy", value = "家庭邮政编码" ,notes = "2023-09-13 17:07:19")
    @Comment("家庭邮政编码")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="housePostcode",columnGroup="联系方式",columnReadonly=false,orderIndex=26,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String extHousePostcodeCopy;

    @ApiModelProperty(name = "extHouseTelephoneCopy", value = "家庭电话" ,notes = "2023-09-13 17:07:19")
    @Comment("家庭电话")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="houseTelephone",columnGroup="联系方式",columnReadonly=false,orderIndex=27,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String extHouseTelephoneCopy;

    @ApiModelProperty(name = "extCountryCodeCopy", value = "国家地区" ,notes = "2023-09-13 17:07:19")
    @Comment("国家地区")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="countryCode",columnDict="GJHDQ",columnGroup="个人信息",columnReadonly=false,orderIndex=14,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String extCountryCodeCopy;

    @ApiModelProperty(name = "extReligionCodeCopy", value = "宗教信仰" ,notes = "2023-09-13 17:07:19")
    @Comment("宗教信仰")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="religionCode",columnDict="ZJXY",columnGroup="个人信息",columnReadonly=false,orderIndex=7,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String extReligionCodeCopy;

    @ApiModelProperty(name = "extBirthPlaceCopy", value = "出生地" ,notes = "2023-09-13 17:07:19")
    @Comment("出生地")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="birthPlace",columnDict="XZQH",columnGroup="个人信息",columnReadonly=false,orderIndex=12,fuzzySearch=false,columnHidden=false,columnXtype="select-tree",formRequired=false,formJsonparam="{\"multiple\":false,\"checkStrictly\":true}")
    private String extBirthPlaceCopy;

    @ApiModelProperty(name = "extMaritalCodeCopy", value = "婚姻状况" ,notes = "2023-09-13 17:07:19")
    @Comment("婚姻状况")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="maritalCode",columnDict="HYZK",columnGroup="个人信息",columnReadonly=false,orderIndex=18,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String extMaritalCodeCopy;

    @ApiModelProperty(name = "extGatqCopy", value = "港澳台侨外" ,notes = "2023-09-13 17:07:19")
    @Comment("港澳台侨外")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="gatq",columnDict="GATQW",columnGroup="个人信息",columnReadonly=false,orderIndex=6,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String extGatqCopy;

    @ApiModelProperty(name = "extIsParttimeCopy", value = "是否兼职教师" ,notes = "2023-09-13 17:07:19")
    @Comment("是否兼职教师")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="isParttime",columnDict="SFBZ",columnGroup="用工信息",columnReadonly=false,orderIndex=39,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String extIsParttimeCopy;

    @ApiModelProperty(name = "extUrgentConcatCopy", value = "紧急联系人姓名" ,notes = "2023-09-13 17:07:19")
    @Comment("紧急联系人姓名")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="urgentConcat",columnGroup="联系方式",columnReadonly=false,orderIndex=28,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String extUrgentConcatCopy;

    @ApiModelProperty(name = "extPictureCopy", value = "照片" ,notes = "2023-09-13 17:07:19")
    @Comment("照片")
    @Column
    @ColumnDefine(columnDisplay=true,extendJavaName="picture",columnGroup="照片",columnReadonly=false,orderIndex=200,fuzzySearch=false,columnHidden=false,columnXtype="uploadfile",formRequired=false,formJsonparam="{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\",\"list-type\":\"picture\"}")
    private String extPictureCopy;

	//endregion end_dynamic_column

    @ApiModelProperty(name = "genderCodeName", value = "性别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "genderCode", cacheType = "XB")
    @JSONField(name = "genderCode"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String genderCodeName;

    @ApiModelProperty(name = "nationCodeName", value = "民族名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "nationCode", cacheType = "MZ")
    @JSONField(name = "nationCode"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String nationCodeName;

    @ApiModelProperty(name = "politicalCodeName", value = "政治面貌名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "politicalCode", cacheType = "ZZMM")
    @JSONField(name = "politicalCode"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String politicalCodeName;

    @ApiModelProperty(name = "gatqName", value = "港澳台侨外名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "gatq", cacheType = "GATQW")
    @JSONField(name = "gatq"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String gatqName;

    @ApiModelProperty(name = "religionCodeName", value = "宗教信仰名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "religionCode", cacheType = "ZJXY")
    @JSONField(name = "religionCode"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String religionCodeName;

    @ApiModelProperty(name = "healthName", value = "健康状况名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "health", cacheType = "JKZK")
    @JSONField(name = "health"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String healthName;

    @ApiModelProperty(name = "teacherCategoryName", value = "教职工类别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "teacherCategory", cacheType = "JZGLB")
    @JSONField(name = "teacherCategory"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String teacherCategoryName;

    @ApiModelProperty(name = "sourceName", value = "教职工来源名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "source", cacheType = "GXJZGLY")
    @JSONField(name = "source"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String sourceName;

    @ApiModelProperty(name = "categoryName", value = "编制类别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "category", cacheType = "BZLX")
    @JSONField(name = "category"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String categoryName;

    @ApiModelProperty(name = "parttimeCategoryName", value = "兼职教师聘任类别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "parttimeCategory", cacheType = "JZJSPRLB")
    @JSONField(name = "parttimeCategory"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String parttimeCategoryName;

    @ApiModelProperty(name = "isAdvisorName", value = "是否导师名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "isAdvisor", cacheType = "SFBZ")
    @JSONField(name = "isAdvisor"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String isAdvisorName;

    @ApiModelProperty(name = "advisorCategoryName", value = "导师类别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "advisorCategory", cacheType = "DSLB")
    @JSONField(name = "advisorCategory"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String advisorCategoryName;

    @ApiModelProperty(name = "isInstructorName", value = "是否辅导员名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "isInstructor", cacheType = "SFBZ")
    @JSONField(name = "isInstructor"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String isInstructorName;

    @ApiModelProperty(name = "instructorCategoryName", value = "辅导员类别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "instructorCategory", cacheType = "FDYLB")
    @JSONField(name = "instructorCategory"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String instructorCategoryName;

    @ApiModelProperty(name = "personIdentityName", value = "个人身份名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "personIdentity", cacheType = "GRSF")
    @JSONField(name = "personIdentity"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String personIdentityName;

    @ApiModelProperty(name = "isDoubleTeachName", value = "是否双师型教师名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "isDoubleTeach", cacheType = "SFBZ")
    @JSONField(name = "isDoubleTeach"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String isDoubleTeachName;

    @ApiModelProperty(name = "isDoubleShoulderName", value = "是否双肩挑名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "isDoubleShoulder", cacheType = "SFBZ")
    @JSONField(name = "isDoubleShoulder"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String isDoubleShoulderName;

    @ApiModelProperty(name = "professionalPositionLevelName", value = "专业技术职务级别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "professionalPositionLevel", cacheType = "ZWJB")
    @JSONField(name = "professionalPositionLevel"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String professionalPositionLevelName;

    @ApiModelProperty(name = "isProfessionalPostName", value = "是否专业技术岗位名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "isProfessionalPost", cacheType = "SFBZ")
    @JSONField(name = "isProfessionalPost"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String isProfessionalPostName;

    @ApiModelProperty(name = "professionalPostLevelName", value = "专业技术岗位等级名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "professionalPostLevel", cacheType = "SYDWZYJSRYGWDJ")
    @JSONField(name = "professionalPostLevel"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String professionalPostLevelName;

    @ApiModelProperty(name = "isManagePostName", value = "是否管理岗位名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "isManagePost", cacheType = "SFBZ")
    @JSONField(name = "isManagePost"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String isManagePostName;

    @ApiModelProperty(name = "managePostLevelName", value = "管理岗位等级名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "managePostLevel", cacheType = "SYDWZYJSRYGWDJ")
    @JSONField(name = "managePostLevel"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String managePostLevelName;

    @ApiModelProperty(name = "isRearservicePostName", value = "是否工勤岗位名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "isRearservicePost", cacheType = "SFBZ")
    @JSONField(name = "isRearservicePost"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String isRearservicePostName;

    @ApiModelProperty(name = "rearservicePostLevelName", value = "工勤岗位等级名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "rearservicePostLevel", cacheType = "SYDWZYJSRYGWDJ")
    @JSONField(name = "rearservicePostLevel"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String rearservicePostLevelName;

    @ApiModelProperty(name = "mainPostTypeName", value = "主要岗位类型名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "mainPostType", cacheType = "GWLX")
    @JSONField(name = "mainPostType"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String mainPostTypeName;

    @ApiModelProperty(name = "cadrePostName", value = "干部职务名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "cadrePost", cacheType = "GBZWMC")
    @JSONField(name = "cadrePost"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String cadrePostName;

    @ApiModelProperty(name = "cadrePostLevelName", value = "干部职务级别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "cadrePostLevel", cacheType = "ZWJB")
    @JSONField(name = "cadrePostLevel"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String cadrePostLevelName;

    @ApiModelProperty(name = "lastDegreeName", value = "最高学位名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "lastDegree", cacheType = "ZHRMGHGXW")
    @JSONField(name = "lastDegree"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String lastDegreeName;

    @ApiModelProperty(name = "fromCurrentSchoolName", value = "是否本校毕业名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "fromCurrentSchool", cacheType = "SFBZ")
    @JSONField(name = "fromCurrentSchool"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String fromCurrentSchoolName;

    @ApiModelProperty(name = "lastGraduationSchoolTypeName", value = "最高学历毕业院校类型名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "lastGraduationSchoolType", cacheType = "GXLB")
    @JSONField(name = "lastGraduationSchoolType"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String lastGraduationSchoolTypeName;

    @ApiModelProperty(name = "subjectDivisionName", value = "学科门类名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "subjectDivision", cacheType = "XWSYHRCPYXKML")
    @JSONField(name = "subjectDivision"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String subjectDivisionName;

    @ApiModelProperty(name = "firstSubjectName", value = "一级学科名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "firstSubject", cacheType = "XWSYHRCPYXKML")
    @JSONField(name = "firstSubject"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String firstSubjectName;

    @ApiModelProperty(name = "secondSubjectName", value = "二级学科名称")
    @Translate(cacheName = "xbzd_ejxk_zdy", keyField = "secondSubject", cacheType = "")
    @JSONField(name = "secondSubject"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String secondSubjectName;

    @ApiModelProperty(name = "employmentFormName", value = "用人方式名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "employmentForm", cacheType = "YRFS")
    @JSONField(name = "employmentForm"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String employmentFormName;

    @ApiModelProperty(name = "statusName", value = "教职工当前状态名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "status", cacheType = "JZGDQZT")
    @JSONField(name = "status"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String statusName;

    @ApiModelProperty(name = "lastEducationName", value = "最高学历名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "lastEducation", cacheType = "XL")
    @JSONField(name = "lastEducation"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String lastEducationName;

    @ApiModelProperty(name = "censusRegisterName", value = "户口所在地名称")
    @Translate(split = ",", cacheName = "XZQH_SV", keyField = "censusRegister", cacheType = "XZQH")
    @JSONField(name = "censusRegister"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String censusRegisterName;

    @ApiModelProperty(name = "originalBirthPlaceName", value = "籍贯名称")
    @Translate(split = ",", cacheName = "XZQH_SV", keyField = "originalBirthPlace", cacheType = "XZQH")
    @JSONField(name = "originalBirthPlace"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String originalBirthPlaceName;

    @ApiModelProperty(name = "bloodTypeCodeName", value = "血型名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "bloodTypeCode", cacheType = "XX")
    @JSONField(name = "bloodTypeCode"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String bloodTypeCodeName;

    @ApiModelProperty(name = "countryCodeName", value = "国家地区名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "countryCode", cacheType = "GJHDQ")
    @JSONField(name = "countryCode"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String countryCodeName;

    @ApiModelProperty(name = "maritalCodeName", value = "婚姻状况名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "maritalCode", cacheType = "HYZK")
    @JSONField(name = "maritalCode"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String maritalCodeName;

    @ApiModelProperty(name = "professionalPositionName", value = "专业技术职务名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "professionalPosition", cacheType = "ZYJSZW")
    @JSONField(name = "professionalPosition"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String professionalPositionName;

    @ApiModelProperty(name = "idcardTypeCodeName", value = "身份证件类型名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "idcardTypeCode", cacheType = "SFZJLX")
    @JSONField(name = "idcardTypeCode"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String idcardTypeCodeName;

    @ApiModelProperty(name = "isParttimeName", value = "是否兼职教师名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "isParttime", cacheType = "SFBZ")
    @JSONField(name = "isParttime"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String isParttimeName;

}
