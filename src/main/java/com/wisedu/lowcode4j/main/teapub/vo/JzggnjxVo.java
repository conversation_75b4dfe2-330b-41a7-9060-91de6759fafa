package com.wisedu.lowcode4j.main.teapub.vo;

import com.wisedu.lowcode4j.common.core.constant.*;

import org.sagacity.sqltoy.config.annotation.*;

import io.swagger.annotations.*;

import lombok.Data;
import com.alibaba.fastjson.annotation.*;

import com.wisedu.lowcode4j.main.teapub.po.Jzggnjx;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzggnjxVo", description = "国内进修Vo")
//end_dynamic_declare
@Data
public class JzggnjxVo extends Jzggnjx {

    private static final long serialVersionUID = 4431474721041955720L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "zbdwxzName", value = "主办单位性质名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "zbdwxz", cacheType = "SHDWXZ")
    @JSONField(name = "zbdwxz"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String zbdwxzName;

    @ApiModelProperty(name = "jxxzName", value = "进修性质名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "jxxz", cacheType = "PXJXBLB")
    @JSONField(name = "jxxz"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String jxxzName;

    @ApiModelProperty(name = "xxfsName", value = "学习方式名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "xxfs", cacheType = "JYLB")
    @JSONField(name = "xxfs"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String xxfsName;

    @ApiModelProperty(name = "zxdwlbName", value = "在学单位类别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "zxdwlb", cacheType = "ZXDWLB")
    @JSONField(name = "zxdwlb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String zxdwlbName;

    @ApiModelProperty(name = "jxjgName", value = "进修结果名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "jxjg", cacheType = "JYPXJG")
    @JSONField(name = "jxjg"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String jxjgName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
