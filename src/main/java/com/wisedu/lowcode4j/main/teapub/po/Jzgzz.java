package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.main.teapub.vo.JzgzzVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgzz", description = "著作")
@Comment("著作")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_BIZ_TEACHER_ZZ", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_biz_teacher_zz", unique = false)
})
@Table(value = "BIZ_TEACHER_ZZ")
@SqlToyEntity
@ModelExt(extClass = JzgzzVo.class )
@ModelDefine(orderIndex = 5,modelClassify="kyxx", renderType="table")
//end_dynamic_declare
@Data
public class Jzgzz extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041952887L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-28 20:01:41")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=1,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zgh;

    @ApiModelProperty(name = "zzbh", value = "著作编号" ,notes = "2023-11-28 20:01:57")
    @Comment("著作编号")
    @Column(value = "zzbh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=2,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zzbh;

    @ApiModelProperty(name = "zzzwmc", value = "著作中文名称" ,notes = "2023-11-28 20:01:50")
    @Comment("著作中文名称")
    @Column(value = "zzzwmc",type = ColType.AUTO, width = 360,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=3,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zzzwmc;

    @ApiModelProperty(name = "zzywmc", value = "著作英文名称" ,notes = "2023-11-28 20:02:09")
    @Comment("著作英文名称")
    @Column(value = "zzywmc",type = ColType.AUTO, width = 600,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=4,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zzywmc;

    @ApiModelProperty(name = "cbsmc", value = "出版社名称" ,notes = "2023-11-28 20:02:08")
    @Comment("出版社名称")
    @Column(value = "cbsmc",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=5,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String cbsmc;

    @ApiModelProperty(name = "cbrq", value = "出版日期" ,notes = "2023-11-28 20:01:53")
    @Comment("出版日期")
    @Column(value = "cbrq",type = ColType.AUTO, width = 0,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=8,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private Date cbrq;

    @ApiModelProperty(name = "isbnh", value = "ISBN号" ,notes = "2023-11-28 20:02:08")
    @Comment("ISBN号")
    @Column(value = "isbnh",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=9,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String isbnh;

    @ApiModelProperty(name = "zs", value = "字数" ,notes = "2023-11-28 20:01:50")
    @Comment("字数")
    @Column(value = "zs",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=12,fuzzySearch=false,columnHidden=false,columnXtype="number",formRequired=false)
    private Long zs;

    @ApiModelProperty(name = "ciph", value = "CIP号" ,notes = "2023-11-28 20:01:50")
    @Comment("CIP号")
    @Column(value = "ciph",type = ColType.AUTO, width = 600,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=14,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String ciph;

    @ApiModelProperty(name = "bc", value = "版次" ,notes = "2023-11-28 20:02:07")
    @Comment("版次")
    @Column(value = "bc",type = ColType.AUTO, width = 90,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=16,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String bc;

    @ApiModelProperty(name = "xxsm", value = "学校署名" ,notes = "2023-11-28 20:02:09")
    @Comment("学校署名")
    @Column(value = "xxsm",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=17,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String xxsm;

    @ApiModelProperty(name = "dyzz", value = "第一作者" ,notes = "2023-11-28 20:01:41")
    @Comment("第一作者")
    @Column(value = "dyzz",type = ColType.AUTO, width = 240,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=18,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String dyzz;

    @ApiModelProperty(name = "zzjj", value = "著作简介" ,notes = "2023-11-28 20:01:54")
    @Comment("著作简介")
    @Column(value = "zzjj",type = ColType.AUTO, width = 3000,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=19,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zzjj;

    @ApiModelProperty(name = "smsx", value = "署名顺序" ,notes = "2023-11-28 20:01:52")
    @Comment("署名顺序")
    @Column(value = "smsx",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=21,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String smsx;

    @ApiModelProperty(name = "gxl", value = "贡献率" ,notes = "2023-11-28 20:01:44")
    @Comment("贡献率")
    @Column(value = "gxl",type = ColType.AUTO, precision = 4, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=22,fuzzySearch=false,columnHidden=false,columnXtype="number",formRequired=false)
    private Integer gxl;

    @ApiModelProperty(name = "zxzs", value = "撰写字数" ,notes = "2023-11-28 20:02:04")
    @Comment("撰写字数")
    @Column(value = "zxzs",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=23,fuzzySearch=false,columnHidden=false,columnXtype="number",formRequired=false)
    private Long zxzs;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-28 20:01:52")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=26,fuzzySearch=false,columnXtype="text",columnHidden=true,formRequired=false)
    private String zt;

    @ApiModelProperty(name = "cbsjb", value = "出版社级别" ,notes = "2023-11-28 20:01:51")
    @Comment("出版社级别")
    @Column(value = "cbsjb",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="CBSJB",columnReadonly=false,orderIndex=6,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String cbsjb;

    @ApiModelProperty(name = "cbsszgjdq", value = "出版社所在国家地区" ,notes = "2023-11-28 20:01:57")
    @Comment("出版社所在国家地区")
    @Column(value = "cbsszgjdq",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="GJHDQ",columnReadonly=false,orderIndex=7,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String cbsszgjdq;

    @ApiModelProperty(name = "yz", value = "语种" ,notes = "2023-11-28 20:02:03")
    @Comment("语种")
    @Column(value = "yz",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="YZ",orderIndex=10,columnReadonly=false,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String yz;

    @ApiModelProperty(name = "zgyz", value = "中国语种" ,notes = "2023-11-28 20:01:46")
    @Comment("中国语种")
    @Column(value = "zgyz",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="ZGYZ",columnReadonly=false,orderIndex=11,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String zgyz;

    @ApiModelProperty(name = "lzlb", value = "论著类别" ,notes = "2023-11-28 20:01:45")
    @Comment("论著类别")
    @Column(value = "lzlb",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="LZLB",columnReadonly=false,orderIndex=13,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String lzlb;

    @ApiModelProperty(name = "sfsb", value = "是否首版" ,notes = "2023-11-28 20:01:48")
    @Comment("是否首版")
    @Column(value = "sfsb",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SFBZ",columnReadonly=false,orderIndex=15,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String sfsb;

    @ApiModelProperty(name = "js", value = "角色" ,notes = "2023-11-28 20:02:09")
    @Comment("角色")
    @Column(value = "js",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JS",columnReadonly=false,orderIndex=20,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String js;

    @ApiModelProperty(name = "sfdyzz", value = "是否第一作者" ,notes = "2023-11-28 20:01:43")
    @Comment("是否第一作者")
    @Column(value = "sfdyzz",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SFBZ",columnReadonly=false,orderIndex=24,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String sfdyzz;

    @ApiModelProperty(name = "sfbxdyzz", value = "是否本校第一作者" ,notes = "2023-11-28 20:02:03")
    @Comment("是否本校第一作者")
    @Column(value = "sfbxdyzz",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SFBZ",columnReadonly=false,orderIndex=25,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String sfbxdyzz;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=26,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
