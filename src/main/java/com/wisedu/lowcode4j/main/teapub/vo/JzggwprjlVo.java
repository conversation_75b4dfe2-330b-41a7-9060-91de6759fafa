package com.wisedu.lowcode4j.main.teapub.vo;

import com.wisedu.lowcode4j.common.core.constant.*;

import org.sagacity.sqltoy.config.annotation.*;

import io.swagger.annotations.*;

import lombok.Data;
import com.alibaba.fastjson.annotation.*;

import com.wisedu.lowcode4j.main.teapub.po.Jzggwprjl;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzggwprjlVo", description = "岗位聘任记录Vo")
//end_dynamic_declare
@Data
public class JzggwprjlVo extends Jzggwprjl {

    private static final long serialVersionUID = 4431474721041957507L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "sfzrName", value = "是否在任名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "sfzr", cacheType = "SFBZ")
    @JSONField(name = "sfzr"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String sfzrName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
