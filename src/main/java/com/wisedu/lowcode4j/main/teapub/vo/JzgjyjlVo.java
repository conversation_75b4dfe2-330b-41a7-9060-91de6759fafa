package com.wisedu.lowcode4j.main.teapub.vo;

import com.wisedu.lowcode4j.common.core.constant.*;

import org.sagacity.sqltoy.config.annotation.*;

import io.swagger.annotations.*;

import lombok.Data;
import com.alibaba.fastjson.annotation.*;

import com.wisedu.lowcode4j.main.teapub.po.Jzgjyjl;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgjyjlVo", description = "教育经历Vo")
//end_dynamic_declare
@Data
public class JzgjyjlVo extends Jzgjyjl {

    private static final long serialVersionUID = 4431474721041956084L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "xlName", value = "学历名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "xl", cacheType = "XL")
    @JSONField(name = "xl"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String xlName;

    @ApiModelProperty(name = "xwName", value = "学位名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "xw", cacheType = "ZHRMGHGXW")
    @JSONField(name = "xw"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String xwName;

    @ApiModelProperty(name = "xwsygjName", value = "学位授予国家名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "xwsygj", cacheType = "GJHDQ")
    @JSONField(name = "xwsygj"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String xwsygjName;

    @ApiModelProperty(name = "gxlbName", value = "高校类别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "gxlb", cacheType = "GXLB")
    @JSONField(name = "gxlb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String gxlbName;

    @ApiModelProperty(name = "sfzgxlName", value = "是否最高学历名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "sfzgxl", cacheType = "SFBZ")
    @JSONField(name = "sfzgxl"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String sfzgxlName;

    @ApiModelProperty(name = "sfdyxlName", value = "是否第一学历名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "sfdyxl", cacheType = "SFBZ")
    @JSONField(name = "sfdyxl"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String sfdyxlName;

    @ApiModelProperty(name = "sfhwjlName", value = "是否海外经历名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "sfhwjl", cacheType = "SFBZ")
    @JSONField(name = "sfhwjl"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String sfhwjlName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
