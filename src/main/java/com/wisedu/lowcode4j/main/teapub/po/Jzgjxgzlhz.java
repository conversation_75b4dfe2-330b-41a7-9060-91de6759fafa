package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.main.teapub.vo.JzgjxgzlhzVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgjxgzlhz", description = "教学工作量汇总")
@Comment("教学工作量汇总")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_BIZ_TEACHER_JXGZLHZ", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_BIZ_TEACHER_JXGZLHZ", unique = false)
})
@Table(value = "BIZ_TEACHER_JXGZLHZ")
@SqlToyEntity
@ModelExt(extClass = JzgjxgzlhzVo.class )
@ModelDefine(orderIndex = 12,modelClassify="jyjx", renderType="table")
//end_dynamic_declare
@Data
public class Jzgjxgzlhz extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041953644L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-09 19:08:35")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=1,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zgh;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-09 19:08:48")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "xnxq", value = "学年学期" ,notes = "2023-11-09 19:09:19")
    @Comment("学年学期")
    @Column(value = "xnxq",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=2,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String xnxq;

    @ApiModelProperty(name = "gzl", value = "工作量" ,notes = "2023-11-09 19:09:51")
    @Comment("工作量")
    @Column(value = "gzl",type = ColType.AUTO, precision = 2, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=3,fuzzySearch=false,columnHidden=false,columnXtype="number",formRequired=false)
    private Double gzl;

    @ApiModelProperty(name = "gzllx", value = "工作量类型" ,notes = "2023-11-09 19:10:11")
    @Comment("工作量类型")
    @Column(value = "gzllx",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="GZLLX",orderIndex=4,columnReadonly=false,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String gzllx;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=5,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
