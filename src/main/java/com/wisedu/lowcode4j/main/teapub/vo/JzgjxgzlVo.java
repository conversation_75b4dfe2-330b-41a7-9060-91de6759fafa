package com.wisedu.lowcode4j.main.teapub.vo;

import com.wisedu.lowcode4j.common.core.constant.*;

import org.sagacity.sqltoy.config.annotation.*;

import io.swagger.annotations.*;

import lombok.Data;
import com.alibaba.fastjson.annotation.*;

import com.wisedu.lowcode4j.main.teapub.po.Jzgjxgzl;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgjxgzlVo", description = "教学工作量Vo")
//end_dynamic_declare
@Data
public class JzgjxgzlVo extends Jzgjxgzl {

    private static final long serialVersionUID = 4431474721041958281L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "gzllxName", value = "工作量类型名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "gzllx", cacheType = "GZLLX")
    @JSONField(name = "gzllx"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String gzllxName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
