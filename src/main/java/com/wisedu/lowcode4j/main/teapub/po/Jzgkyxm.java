package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.main.teapub.vo.JzgkyxmVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgkyxm", description = "科研项目")
@Comment("科研项目")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_biz_teacher_kyxm", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_biz_teacher_kyxm", unique = false)
})
@Table(value = "BIZ_TEACHER_KYXM")
@SqlToyEntity
@ModelExt(extClass = JzgkyxmVo.class )
@ModelDefine(orderIndex = 1,modelClassify="kyxx", renderType="table")
//end_dynamic_declare
@Data
public class Jzgkyxm extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041954643L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-27 20:38:24")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=1,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zgh;

    @ApiModelProperty(name = "xmbh", value = "项目编号" ,notes = "2023-11-27 20:38:47")
    @Comment("项目编号")
    @Column(value = "xmbh",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=2,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String xmbh;

    @ApiModelProperty(name = "xmmc", value = "项目名称" ,notes = "2023-11-27 20:39:35")
    @Comment("项目名称")
    @Column(value = "xmmc",type = ColType.AUTO, width = 900,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=3,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String xmmc;

    @ApiModelProperty(name = "xmlb", value = "项目类别" ,notes = "2023-11-27 20:40:32")
    @Comment("项目类别")
    @Column(value = "xmlb",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="XMFL",columnReadonly=false,orderIndex=4,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String xmlb;

    @ApiModelProperty(name = "xmlx", value = "项目类型" ,notes = "2023-11-27 20:41:07")
    @Comment("项目类型")
    @Column(value = "xmlx",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="XMLX",columnReadonly=false,orderIndex=5,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String xmlx;

    @ApiModelProperty(name = "kjxmlb", value = "科技项目类别" ,notes = "2023-11-27 20:41:55")
    @Comment("科技项目类别")
    @Column(value = "kjxmlb",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="KJXMLB",columnReadonly=false,orderIndex=6,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String kjxmlb;

    @ApiModelProperty(name = "xmjb", value = "项目级别" ,notes = "2023-11-27 20:42:46")
    @Comment("项目级别")
    @Column(value = "xmjb",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JB",columnReadonly=false,orderIndex=7,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String xmjb;

    @ApiModelProperty(name = "xmfzr", value = "项目负责人" ,notes = "2023-11-27 20:43:12")
    @Comment("项目负责人")
    @Column(value = "xmfzr",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=8,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String xmfzr;

    @ApiModelProperty(name = "xmpzh", value = "项目批准号" ,notes = "2023-11-27 20:44:45")
    @Comment("项目批准号")
    @Column(value = "xmpzh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=9,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String xmpzh;

    @ApiModelProperty(name = "lxrq", value = "立项日期" ,notes = "2023-11-27 20:45:09")
    @Comment("立项日期")
    @Column(value = "lxrq",type = ColType.AUTO, width = 0,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=10,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private Date lxrq;

    @ApiModelProperty(name = "ksrq", value = "开始日期" ,notes = "2023-11-27 20:45:29")
    @Comment("开始日期")
    @Column(value = "ksrq",type = ColType.AUTO, width = 0,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=11,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private Date ksrq;

    @ApiModelProperty(name = "jhwcrq", value = "计划完成日期" ,notes = "2023-11-27 20:45:52")
    @Comment("计划完成日期")
    @Column(value = "jhwcrq",type = ColType.AUTO, width = 0,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=12,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private Date jhwcrq;

    @ApiModelProperty(name = "jxrq", value = "结项日期" ,notes = "2023-11-27 20:46:11")
    @Comment("结项日期")
    @Column(value = "jxrq",type = ColType.AUTO, width = 0,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=13,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private Date jxrq;

    @ApiModelProperty(name = "xmly", value = "项目来源" ,notes = "2023-11-27 20:46:47")
    @Comment("项目来源")
    @Column(value = "xmly",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="XMLY",columnReadonly=false,orderIndex=14,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String xmly;

    @ApiModelProperty(name = "xmlydw", value = "项目来源单位" ,notes = "2023-11-27 20:47:26")
    @Comment("项目来源单位")
    @Column(value = "xmlydw",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=15,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String xmlydw;

    @ApiModelProperty(name = "xmzxzt", value = "项目执行状态" ,notes = "2023-11-27 20:48:06")
    @Comment("项目执行状态")
    @Column(value = "xmzxzt",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="XMZXZT",columnReadonly=false,orderIndex=16,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String xmzxzt;

    @ApiModelProperty(name = "js", value = "角色" ,notes = "2023-11-27 20:48:43")
    @Comment("角色")
    @Column(value = "js",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JS",columnReadonly=false,orderIndex=17,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String js;

    @ApiModelProperty(name = "gxl", value = "贡献率" ,notes = "2023-11-27 20:49:18")
    @Comment("贡献率")
    @Column(value = "gxl",type = ColType.AUTO, precision = 4, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=18,fuzzySearch=false,columnHidden=false,columnXtype="number",formRequired=false)
    private Double gxl;

    @ApiModelProperty(name = "smsx", value = "署名顺序" ,notes = "2023-11-27 20:49:40")
    @Comment("署名顺序")
    @Column(value = "smsx",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=19,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String smsx;

    @ApiModelProperty(name = "cyrq", value = "参与日期" ,notes = "2023-11-27 20:50:03")
    @Comment("参与日期")
    @Column(value = "cyrq",type = ColType.AUTO, width = 0,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=20,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private Date cyrq;

    @ApiModelProperty(name = "cyzt", value = "参与状态" ,notes = "2023-11-27 20:50:26")
    @Comment("参与状态")
    @Column(value = "cyzt",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=21,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String cyzt;

    @ApiModelProperty(name = "sfxmfzr", value = "是否项目负责人" ,notes = "2023-11-27 20:52:13")
    @Comment("是否项目负责人")
    @Column(value = "sfxmfzr",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SFBZ",columnReadonly=false,orderIndex=22,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String sfxmfzr;

    @ApiModelProperty(name = "ssktbm", value = "所属课题编码" ,notes = "2023-11-27 20:52:41")
    @Comment("所属课题编码")
    @Column(value = "ssktbm",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=23,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String ssktbm;

    @ApiModelProperty(name = "ssktmc", value = "所属课题名称" ,notes = "2023-11-27 20:53:14")
    @Comment("所属课题名称")
    @Column(value = "ssktmc",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=24,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String ssktmc;

    @ApiModelProperty(name = "jfze", value = "经费总额" ,notes = "2023-11-27 20:53:48")
    @Comment("经费总额")
    @Column(value = "jfze",type = ColType.AUTO, precision = 4, width = 19,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=25,fuzzySearch=false,columnHidden=false,columnXtype="number",formRequired=false)
    private Double jfze;

    @ApiModelProperty(name = "grkzpjf", value = "个人可支配经费" ,notes = "2023-11-27 20:54:18")
    @Comment("个人可支配经费")
    @Column(value = "grkzpjf",type = ColType.AUTO, precision = 4, width = 19,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=26,fuzzySearch=false,columnHidden=false,columnXtype="number",formRequired=false)
    private Double grkzpjf;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-27 20:54:30")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=27,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=28,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
