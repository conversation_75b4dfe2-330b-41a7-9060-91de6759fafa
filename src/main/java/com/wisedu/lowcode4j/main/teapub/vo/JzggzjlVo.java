package com.wisedu.lowcode4j.main.teapub.vo;

import com.wisedu.lowcode4j.common.core.constant.*;

import org.sagacity.sqltoy.config.annotation.*;

import io.swagger.annotations.*;

import lombok.Data;
import com.alibaba.fastjson.annotation.*;

import com.wisedu.lowcode4j.main.teapub.po.Jzggzjl;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzggzjlVo", description = "工作经历Vo")
//end_dynamic_declare
@Data
public class JzggzjlVo extends Jzggzjl {

    private static final long serialVersionUID = 4431474721041955874L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "gjdqdmName", value = "国家地区名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "gjdqdm", cacheType = "GJHDQ")
    @JSONField(name = "gjdqdm"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String gjdqdmName;

    @ApiModelProperty(name = "crzyjszwdmName", value = "曾任专业技术职务名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "crzyjszwdm", cacheType = "ZYJSZW")
    @JSONField(name = "crzyjszwdm"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String crzyjszwdmName;

    @ApiModelProperty(name = "sfhwjlName", value = "是否海外经历名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "sfhwjl", cacheType = "SFBZ")
    @JSONField(name = "sfhwjl"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String sfhwjlName;

    @ApiModelProperty(name = "dwxzlbdmName", value = "单位性质类别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "dwxzlbdm", cacheType = "SHDWXZ")
    @JSONField(name = "dwxzlbdm"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String dwxzlbdmName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
