package com.wisedu.lowcode4j.main.teapub.vo;

import com.wisedu.lowcode4j.common.core.constant.*;

import org.sagacity.sqltoy.config.annotation.*;

import io.swagger.annotations.*;

import lombok.Data;
import com.alibaba.fastjson.annotation.*;

import com.wisedu.lowcode4j.main.teapub.po.Jzgcgxx;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgcgxxVo", description = "出国信息Vo")
//end_dynamic_declare
@Data
public class JzgcgxxVo extends Jzgcgxx {

    private static final long serialVersionUID = 4431474721041958846L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "cggbName", value = "出国（境）国别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "cggb", cacheType = "gjdq")
    @JSONField(name = "cggb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String cggbName;

    @ApiModelProperty(name = "jflyName", value = "经费来源名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "jfly", cacheType = "CGJJFLY")
    @JSONField(name = "jfly"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String jflyName;

    @ApiModelProperty(name = "cgmdName", value = "出国（境）目的名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "cgmd", cacheType = "CGMD")
    @JSONField(name = "cgmd"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String cgmdName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
