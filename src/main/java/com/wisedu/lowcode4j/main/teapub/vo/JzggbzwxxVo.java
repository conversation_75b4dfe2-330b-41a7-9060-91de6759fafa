package com.wisedu.lowcode4j.main.teapub.vo;

import com.wisedu.lowcode4j.common.core.constant.*;

import org.sagacity.sqltoy.config.annotation.*;

import io.swagger.annotations.*;

import lombok.Data;
import com.alibaba.fastjson.annotation.*;

import com.wisedu.lowcode4j.main.teapub.po.Jzggbzwxx;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzggbzwxxVo", description = "干部职务信息Vo")
//end_dynamic_declare
@Data
public class JzggbzwxxVo extends Jzggbzwxx {

    private static final long serialVersionUID = 4431474721041956178L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "sfzrName", value = "是否在任名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "sfzr", cacheType = "SFBZ")
    @JSONField(name = "sfzr"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String sfzrName;

    @ApiModelProperty(name = "sfzzName", value = "是否主职名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "sfzz", cacheType = "SFBZ")
    @JSONField(name = "sfzz"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String sfzzName;

    @ApiModelProperty(name = "zwlbName", value = "职务类别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "zwlb", cacheType = "ZWLB")
    @JSONField(name = "zwlb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String zwlbName;

    @ApiModelProperty(name = "zwjbName", value = "职务级别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "zwjb", cacheType = "ZWJB")
    @JSONField(name = "zwjb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String zwjbName;

    @ApiModelProperty(name = "rzdwName", value = "任职单位名称")
    @Translate(cacheName = "biz_department", keyField = "rzdw", cacheType = "")
    @JSONField(name = "rzdw"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String rzdwName;

    @ApiModelProperty(name = "rzfsName", value = "任职方式名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "rzfs", cacheType = "RZFS")
    @JSONField(name = "rzfs"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String rzfsName;

    @ApiModelProperty(name = "mzfsName", value = "免职方式名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "mzfs", cacheType = "MZFS")
    @JSONField(name = "mzfs"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String mzfsName;

    @ApiModelProperty(name = "mzyyName", value = "免职原因名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "mzyy", cacheType = "MZCZYY")
    @JSONField(name = "mzyy"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String mzyyName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
