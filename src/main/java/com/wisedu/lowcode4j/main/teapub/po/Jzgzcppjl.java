package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.main.teapub.vo.JzgzcppjlVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgzcppjl", description = "职称评聘记录")
@Comment("职称评聘记录")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_BIZ_TEACHER_ZCPPJL", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_BIZ_TEACHER_ZCPPJL", unique = false)
})
@Table(value = "BIZ_TEACHER_ZCPPJL")
@SqlToyEntity
@ModelExt(extClass = JzgzcppjlVo.class )
@ModelDefine(orderIndex = 20,modelClassify="rsxx", renderType="table")
//end_dynamic_declare
@Data
public class Jzgzcppjl extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041957807L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-09 10:18:57")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=1,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zgh;

    @ApiModelProperty(name = "zyjszw", value = "专业技术职务" ,notes = "2023-11-09 10:19:51")
    @Comment("专业技术职务")
    @Column(value = "zyjszw",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="ZYJSZW",columnReadonly=false,orderIndex=2,fuzzySearch=false,columnHidden=false,columnXtype="select-tree",formRequired=false,formJsonparam="{\"multiple\":false,\"checkStrictly\":true}")
    private String zyjszw;

    @ApiModelProperty(name = "pdrq", value = "评定日期" ,notes = "2023-11-09 10:21:43")
    @Comment("评定日期")
    @Column(value = "pdrq",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=4,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private String pdrq;

    @ApiModelProperty(name = "psdw", value = "评审单位" ,notes = "2023-11-09 10:22:20")
    @Comment("评审单位")
    @Column(value = "psdw",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=5,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String psdw;

    @ApiModelProperty(name = "przyjszw", value = "聘任专业技术职务" ,notes = "2023-11-09 10:23:40")
    @Comment("聘任专业技术职务")
    @Column(value = "przyjszw",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="ZYJSZW",columnReadonly=false,orderIndex=6,fuzzySearch=false,columnHidden=false,columnXtype="select-tree",formRequired=false,formJsonparam="{\"multiple\":false,\"checkStrictly\":true}")
    private String przyjszw;

    @ApiModelProperty(name = "prqsrq", value = "聘任起始日期" ,notes = "2023-11-09 10:25:56")
    @Comment("聘任起始日期")
    @Column(value = "prqsrq",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=8,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private String prqsrq;

    @ApiModelProperty(name = "przzrq", value = "聘任终止日期" ,notes = "2023-11-09 10:27:12")
    @Comment("聘任终止日期")
    @Column(value = "przzrq",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=9,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private String przzrq;

    @ApiModelProperty(name = "pzwh", value = "批准文号" ,notes = "2023-11-09 10:33:28")
    @Comment("批准文号")
    @Column(value = "pzwh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=12,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String pzwh;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-09 14:37:18")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=13,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "zyjszwjb", value = "专业技术职务级别" ,notes = "2023-11-09 10:20:56")
    @Comment("专业技术职务级别")
    @Column(value = "zyjszwjb",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="ZWJB",columnReadonly=false,orderIndex=3,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String zyjszwjb;

    @ApiModelProperty(name = "prdw", value = "聘任单位" ,notes = "2023-11-09 10:29:26")
    @Comment("聘任单位")
    @Column(value = "prdw",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="biz_department",columnReadonly=false,orderIndex=10,fuzzySearch=false,columnHidden=false,columnXtype="select-tree",formRequired=false)
    private String prdw;

    @ApiModelProperty(name = "przt", value = "聘任状态" ,notes = "2023-11-09 10:32:09")
    @Comment("聘任状态")
    @Column(value = "przt",type = ColType.AUTO, width = 128,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="PRQK",columnReadonly=false,orderIndex=11,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String przt;

    @ApiModelProperty(name = "przyjszwjb", value = "聘任专业技术职务级别" ,notes = "2023-11-09 10:25:03")
    @Comment("聘任专业技术职务级别")
    @Column(value = "przyjszwjb",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="ZWJB",columnReadonly=false,orderIndex=7,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String przyjszwjb;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=13,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
