package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.main.teapub.vo.JzgjljryxxVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgjljryxx", description = "奖励及荣誉信息")
@Comment("奖励及荣誉信息")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_BIZ_TEACHER_JLJRYXX", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_BIZ_TEACHER_JLJRYXX", unique = false)
})
@Table(value = "BIZ_TEACHER_JLJRYXX")
@SqlToyEntity
@ModelExt(extClass = JzgjljryxxVo.class )
@ModelDefine(orderIndex = 10,modelClassify="rsxx", renderType="table")
//end_dynamic_declare
@Data
public class Jzgjljryxx extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041958817L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-09 15:42:52")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=1,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zgh;

    @ApiModelProperty(name = "jlmc", value = "奖励名称" ,notes = "2023-11-09 15:44:20")
    @Comment("奖励名称")
    @Column(value = "jlmc",type = ColType.AUTO, width = 540,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=2,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String jlmc;

    @ApiModelProperty(name = "jlrq", value = "奖励日期" ,notes = "2023-11-09 15:45:46")
    @Comment("奖励日期")
    @Column(value = "jlrq",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=4,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private String jlrq;

    @ApiModelProperty(name = "jljb", value = "奖励级别" ,notes = "2023-11-09 15:46:34")
    @Comment("奖励级别")
    @Column(value = "jljb",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JB",orderIndex=5,columnReadonly=false,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String jljb;

    @ApiModelProperty(name = "jldw", value = "奖励单位" ,notes = "2023-11-09 15:48:28")
    @Comment("奖励单位")
    @Column(value = "jldw",type = ColType.AUTO, width = 540,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=8,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String jldw;

    @ApiModelProperty(name = "jlxm", value = "奖励项目" ,notes = "2023-11-09 15:49:01")
    @Comment("奖励项目")
    @Column(value = "jlxm",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=9,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String jlxm;

    @ApiModelProperty(name = "jlyy", value = "奖励原因" ,notes = "2023-11-09 15:50:00")
    @Comment("奖励原因")
    @Column(value = "jlyy",type = ColType.AUTO, width = 600,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=10,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String jlyy;

    @ApiModelProperty(name = "brpm", value = "本人排名" ,notes = "2023-11-09 15:51:29")
    @Comment("本人排名")
    @Column(value = "brpm",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=12,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String brpm;

    @ApiModelProperty(name = "hjzsbh", value = "获奖证书编号" ,notes = "2023-11-09 15:51:53")
    @Comment("获奖证书编号")
    @Column(value = "hjzsbh",type = ColType.AUTO, width = 150,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=13,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String hjzsbh;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-09 15:52:07")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "rych", value = "荣誉称号" ,notes = "2023-11-09 15:45:13")
    @Comment("荣誉称号")
    @Column(value = "rych",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="RYCH",orderIndex=3,columnReadonly=false,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String rych;

    @ApiModelProperty(name = "jldj", value = "奖励等级" ,notes = "2023-11-09 15:47:25")
    @Comment("奖励等级")
    @Column(value = "jldj",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JLDJ",orderIndex=6,columnReadonly=false,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String jldj;

    @ApiModelProperty(name = "jllb", value = "奖励类别" ,notes = "2023-11-09 15:48:06")
    @Comment("奖励类别")
    @Column(value = "jllb",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JSHJLB",orderIndex=7,columnReadonly=false,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String jllb;

    @ApiModelProperty(name = "jlfs", value = "奖励方式" ,notes = "2023-11-09 15:49:37")
    @Comment("奖励方式")
    @Column(value = "jlfs",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JLFS",columnReadonly=false,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String jlfs;

    @ApiModelProperty(name = "jljs", value = "获奖角色" ,notes = "2023-11-09 15:51:07")
    @Comment("获奖角色")
    @Column(value = "jljs",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JS",orderIndex=11,columnReadonly=false,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String jljs;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=14,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
