package com.wisedu.lowcode4j.main.teapub.vo;

import com.wisedu.lowcode4j.common.core.constant.*;

import org.sagacity.sqltoy.config.annotation.*;

import io.swagger.annotations.*;

import lombok.Data;
import com.alibaba.fastjson.annotation.*;

import com.wisedu.lowcode4j.main.teapub.po.Jzgshjzxx;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgshjzxxVo", description = "社会兼职信息Vo")
//end_dynamic_declare
@Data
public class JzgshjzxxVo extends Jzgshjzxx {

    private static final long serialVersionUID = 4431474721041954637L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "shjzfwName", value = "社会兼职服务名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "shjzfw", cacheType = "SHJZ")
    @JSONField(name = "shjzfw"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String shjzfwName;

    @ApiModelProperty(name = "czyyName", value = "辞职原因名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "czyy", cacheType = "CQJZYY")
    @JSONField(name = "czyy"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String czyyName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
