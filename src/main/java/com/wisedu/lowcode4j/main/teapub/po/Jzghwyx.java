package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.main.teapub.vo.JzghwyxVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzghwyx", description = "海外研修")
@Comment("海外研修")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_BIZ_TEACHER_HWYX", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_BIZ_TEACHER_HWYX", unique = false)
})
@Table(value = "BIZ_TEACHER_HWYX")
@SqlToyEntity
@ModelExt(extClass = JzghwyxVo.class )
@ModelDefine(orderIndex = 16,modelClassify="rsxx", renderType="table")
//end_dynamic_declare
@Data
public class Jzghwyx extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041955843L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-09 11:23:12")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=1,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zgh;

    @ApiModelProperty(name = "ksrq", value = "开始日期" ,notes = "2023-11-09 11:24:48")
    @Comment("开始日期")
    @Column(value = "ksrq",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=2,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private String ksrq;

    @ApiModelProperty(name = "jsrq", value = "结束日期" ,notes = "2023-11-09 11:25:10")
    @Comment("结束日期")
    @Column(value = "jsrq",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=3,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private String jsrq;

    @ApiModelProperty(name = "gjdq", value = "国家地区" ,notes = "2023-11-09 13:12:19")
    @Comment("国家地区")
    @Column(value = "gjdq",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="GJHDQ",orderIndex=4,columnReadonly=false,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String gjdq;

    @ApiModelProperty(name = "yxfxjl", value = "研修访学机构" ,notes = "2023-11-09 13:13:45")
    @Comment("研修访学机构")
    @Column(value = "yxfxjl",type = ColType.AUTO, width = 540,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=5,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String yxfxjl;

    @ApiModelProperty(name = "xmmc", value = "项目名称" ,notes = "2023-11-09 13:14:38")
    @Comment("项目名称")
    @Column(value = "xmmc",type = ColType.AUTO, width = 900,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=6,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String xmmc;

    @ApiModelProperty(name = "xmzzdw", value = "项目组织单位" ,notes = "2023-11-09 13:16:12")
    @Comment("项目组织单位")
    @Column(value = "xmzzdw",type = ColType.AUTO, width = 540,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=7,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String xmzzdw;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-09 14:39:34")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=8,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=9,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
