package com.wisedu.lowcode4j.main.teapub.vo;

import com.wisedu.lowcode4j.main.teapub.po.Jzgjtcy;
import org.sagacity.sqltoy.config.annotation.Translate;

import com.alibaba.fastjson.annotation.JSONField;
import com.wisedu.lowcode4j.common.core.constant.ApplicationConstant;
import com.wisedu.lowcode4j.common.core.constant.DictCacheType;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgjtcyVo", description = "家庭成员Vo")
//end_dynamic_declare
@Data
public class JzgjtcyVo extends Jzgjtcy {

    private static final long serialVersionUID = 4431474721041952615L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "cygxName", value = "成员关系名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "cygx", cacheType = "JTGX")
    @JSONField(name = "cygx"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String cygxName;

    @ApiModelProperty(name = "mzName", value = "民族名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "mz", cacheType = "MZ")
    @JSONField(name = "mz"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String mzName;

    @ApiModelProperty(name = "jkzkName", value = "健康状况名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "jkzk", cacheType = "JKZK")
    @JSONField(name = "jkzk"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String jkzkName;

    @ApiModelProperty(name = "zyjszwName", value = "专业技术职务名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "zyjszw", cacheType = "ZYJSZW")
    @JSONField(name = "zyjszw"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String zyjszwName;

    @ApiModelProperty(name = "gjName", value = "国籍名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "gj", cacheType = "GJHDQ")
    @JSONField(name = "gj"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String gjName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
