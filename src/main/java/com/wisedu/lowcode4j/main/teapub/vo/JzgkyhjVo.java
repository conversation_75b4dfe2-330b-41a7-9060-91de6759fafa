package com.wisedu.lowcode4j.main.teapub.vo;

import com.wisedu.lowcode4j.common.core.constant.*;

import org.sagacity.sqltoy.config.annotation.*;

import io.swagger.annotations.*;

import lombok.Data;
import com.alibaba.fastjson.annotation.*;

import com.wisedu.lowcode4j.main.teapub.po.Jzgkyhj;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgkyhjVo", description = "科研获奖Vo")
//end_dynamic_declare
@Data
public class JzgkyhjVo extends Jzgkyhj {

    private static final long serialVersionUID = 4431474721041959120L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "hjcglxName", value = "获奖成果类型名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "hjcglx", cacheType = "KYCGLX")
    @JSONField(name = "hjcglx"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String hjcglxName;

    @ApiModelProperty(name = "hjlbName", value = "获奖类别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "hjlb", cacheType = "CGHJLB")
    @JSONField(name = "hjlb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String hjlbName;

    @ApiModelProperty(name = "hjjbName", value = "获奖级别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "hjjb", cacheType = "JB")
    @JSONField(name = "hjjb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String hjjbName;

    @ApiModelProperty(name = "hjdjName", value = "获奖等级名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "hjdj", cacheType = "JLDJ")
    @JSONField(name = "hjdj"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String hjdjName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
