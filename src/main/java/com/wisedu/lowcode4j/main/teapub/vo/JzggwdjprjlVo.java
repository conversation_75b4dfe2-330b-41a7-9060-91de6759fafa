package com.wisedu.lowcode4j.main.teapub.vo;

import com.wisedu.lowcode4j.common.core.constant.*;

import org.sagacity.sqltoy.config.annotation.*;

import io.swagger.annotations.*;

import lombok.Data;
import com.alibaba.fastjson.annotation.*;

import com.wisedu.lowcode4j.main.teapub.po.Jzggwdjprjl;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzggwdjprjlVo", description = "岗位等级聘任记录Vo")
//end_dynamic_declare
@Data
public class JzggwdjprjlVo extends Jzggwdjprjl {

    private static final long serialVersionUID = 4431474721041951999L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "gwlxName", value = "岗位类型名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "gwlx", cacheType = "GWLX")
    @JSONField(name = "gwlx"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String gwlxName;

    @ApiModelProperty(name = "gwdjName", value = "岗位等级名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "gwdj", cacheType = "SYDWZYJSRYGWDJ")
    @JSONField(name = "gwdj"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String gwdjName;

    @ApiModelProperty(name = "spdwName", value = "受聘单位名称")
    @Translate(cacheName = "biz_department", keyField = "spdw", cacheType = "")
    @JSONField(name = "spdw"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String spdwName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
