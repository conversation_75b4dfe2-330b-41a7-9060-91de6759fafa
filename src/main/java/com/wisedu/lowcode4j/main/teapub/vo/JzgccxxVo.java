package com.wisedu.lowcode4j.main.teapub.vo;

import com.wisedu.lowcode4j.common.core.constant.*;

import org.sagacity.sqltoy.config.annotation.*;

import io.swagger.annotations.*;

import lombok.Data;
import com.alibaba.fastjson.annotation.*;

import com.wisedu.lowcode4j.main.teapub.po.Jzgccxx;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgccxxVo", description = "惩处信息Vo")
//end_dynamic_declare
@Data
public class JzgccxxVo extends Jzgccxx {

    private static final long serialVersionUID = 4431474721041959972L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "jlcfName", value = "纪律处分名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "jlcf", cacheType = "JLCF")
    @JSONField(name = "jlcf"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String jlcfName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
