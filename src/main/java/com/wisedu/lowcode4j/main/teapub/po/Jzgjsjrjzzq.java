package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.main.teapub.vo.JzgjsjrjzzqVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgjsjrjzzq", description = "计算机软件著作权")
@Comment("计算机软件著作权")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_biz_teacher_jsjrjzzq", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_biz_teacher_jsjrjzzq", unique = false)
})
@Table(value = "BIZ_TEACHER_JSJRJZZQ")
@SqlToyEntity
@ModelExt(extClass = JzgjsjrjzzqVo.class )
@ModelDefine(orderIndex = 2,modelClassify="kyxx", renderType="table")
//end_dynamic_declare
@Data
public class Jzgjsjrjzzq extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041954029L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-27 20:57:00")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=1,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zgh;

    @ApiModelProperty(name = "rjzzmc", value = "软件著作名称" ,notes = "2023-11-27 20:57:25")
    @Comment("软件著作名称")
    @Column(value = "rjzzmc",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=2,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String rjzzmc;

    @ApiModelProperty(name = "zsh", value = "证书号" ,notes = "2023-11-27 20:57:42")
    @Comment("证书号")
    @Column(value = "zsh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=3,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zsh;

    @ApiModelProperty(name = "djh", value = "登记号" ,notes = "2023-11-27 20:58:22")
    @Comment("登记号")
    @Column(value = "djh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=4,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String djh;

    @ApiModelProperty(name = "zzqr", value = "著作权人" ,notes = "2023-11-27 20:58:56")
    @Comment("著作权人")
    @Column(value = "zzqr",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=5,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zzqr;

    @ApiModelProperty(name = "smsx", value = "署名顺序" ,notes = "2023-11-27 20:59:29")
    @Comment("署名顺序")
    @Column(value = "smsx",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=6,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String smsx;

    @ApiModelProperty(name = "scfbrq", value = "首次发表日期" ,notes = "2023-11-27 20:59:56")
    @Comment("首次发表日期")
    @Column(value = "scfbrq",type = ColType.AUTO, width = 0,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=7,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private Date scfbrq;

    @ApiModelProperty(name = "zsqdrq", value = "证书取得日期" ,notes = "2023-11-27 21:00:26")
    @Comment("证书取得日期")
    @Column(value = "zsqdrq",type = ColType.AUTO, width = 0,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=8,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private Date zsqdrq;

    @ApiModelProperty(name = "zsbfjg", value = "证书颁发机构" ,notes = "2023-11-27 21:00:52")
    @Comment("证书颁发机构")
    @Column(value = "zsbfjg",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=9,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zsbfjg;

    @ApiModelProperty(name = "qlqdfs", value = "权利取得方式" ,notes = "2023-11-27 21:01:19")
    @Comment("权利取得方式")
    @Column(value = "qlqdfs",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=10,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String qlqdfs;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-27 21:01:35")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=11,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
