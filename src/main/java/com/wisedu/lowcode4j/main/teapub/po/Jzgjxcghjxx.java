package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.main.teapub.vo.JzgjxcghjxxVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgjxcghjxx", description = "教学成果获奖信息")
@Comment("教学成果获奖信息")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_biz_teacher_jxcghjxx", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_biz_teacher_jxcghjxx", unique = false)
})
@Table(value = "BIZ_TEACHER_JXCGHJXX")
@SqlToyEntity
@ModelExt(extClass = JzgjxcghjxxVo.class )
@ModelDefine(orderIndex = 7,modelClassify="jyjx", renderType="table")
//end_dynamic_declare
@Data
public class Jzgjxcghjxx extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041959743L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-27 18:17:42")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=1,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zgh;

    @ApiModelProperty(name = "jxcgbm", value = "教学成果编码" ,notes = "2023-11-27 18:21:22")
    @Comment("教学成果编码")
    @Column(value = "jxcgbm",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=2,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String jxcgbm;

    @ApiModelProperty(name = "jxcgmc", value = "教学成果名称" ,notes = "2023-11-27 18:21:50")
    @Comment("教学成果名称")
    @Column(value = "jxcgmc",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=3,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String jxcgmc;

    @ApiModelProperty(name = "js", value = "角色" ,notes = "2023-11-27 18:22:22")
    @Comment("角色")
    @Column(value = "js",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JS",orderIndex=4,columnReadonly=false,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String js;

    @ApiModelProperty(name = "brwc", value = "本人位次" ,notes = "2023-11-27 18:22:43")
    @Comment("本人位次")
    @Column(value = "brwc",type = ColType.AUTO, width = 90,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=5,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String brwc;

    @ApiModelProperty(name = "jxmc", value = "奖项名称" ,notes = "2023-11-27 18:23:00")
    @Comment("奖项名称")
    @Column(value = "jxmc",type = ColType.AUTO, width = 540,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=6,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String jxmc;

    @ApiModelProperty(name = "jljb", value = "奖励级别" ,notes = "2023-11-27 18:23:45")
    @Comment("奖励级别")
    @Column(value = "jljb",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JB",orderIndex=7,columnReadonly=false,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String jljb;

    @ApiModelProperty(name = "jldj", value = "奖励等级" ,notes = "2023-11-27 18:24:12")
    @Comment("奖励等级")
    @Column(value = "jldj",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JLDJ",orderIndex=8,columnReadonly=false,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String jldj;

    @ApiModelProperty(name = "bjdw", value = "颁奖单位" ,notes = "2023-11-27 18:24:32")
    @Comment("颁奖单位")
    @Column(value = "bjdw",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=9,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String bjdw;

    @ApiModelProperty(name = "bjzsh", value = "颁奖证书号" ,notes = "2023-11-27 18:24:53")
    @Comment("颁奖证书号")
    @Column(value = "bjzsh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=10,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String bjzsh;

    @ApiModelProperty(name = "hjrq", value = "获奖日期" ,notes = "2023-11-27 18:25:16")
    @Comment("获奖日期")
    @Column(value = "hjrq",type = ColType.AUTO, width = 0,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=11,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private Date hjrq;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-27 18:25:31")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=12,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
