package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.main.teapub.vo.JzgxnddVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgxndd", description = "校内调动")
@Comment("校内调动")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_BIZ_TEACHER_XNDD", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_BIZ_TEACHER_XNDD", unique = false)
})
@Table(value = "BIZ_TEACHER_XNDD")
@SqlToyEntity
@ModelExt(extClass = JzgxnddVo.class )
@ModelDefine(orderIndex = 17,modelClassify="rsxx", renderType="table")
//end_dynamic_declare
@Data
public class Jzgxndd extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041953826L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-09 11:13:58")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=1,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zgh;

    @ApiModelProperty(name = "ddrq", value = "调动日期" ,notes = "2023-11-09 11:15:36")
    @Comment("调动日期")
    @Column(value = "ddrq",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=4,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private String ddrq;

    @ApiModelProperty(name = "ddwh", value = "调动文号" ,notes = "2023-11-09 11:16:06")
    @Comment("调动文号")
    @Column(value = "ddwh",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=5,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String ddwh;

    @ApiModelProperty(name = "ddyy", value = "调动原因" ,notes = "2023-11-09 11:16:39")
    @Comment("调动原因")
    @Column(value = "ddyy",type = ColType.AUTO, width = 3000,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=6,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String ddyy;

    @ApiModelProperty(name = "sfzg", value = "是否转岗" ,notes = "2023-11-09 11:17:16")
    @Comment("是否转岗")
    @Column(value = "sfzg",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SFBZ",columnReadonly=false,orderIndex=7,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String sfzg;

    @ApiModelProperty(name = "dcgw", value = "调出岗位" ,notes = "2023-11-09 11:17:48")
    @Comment("调出岗位")
    @Column(value = "dcgw",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=8,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String dcgw;

    @ApiModelProperty(name = "drgw", value = "调入岗位" ,notes = "2023-11-09 11:18:12")
    @Comment("调入岗位")
    @Column(value = "drgw",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=9,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String drgw;

    @ApiModelProperty(name = "sm", value = "说明" ,notes = "2023-11-09 11:18:53")
    @Comment("说明")
    @Column(value = "sm",type = ColType.AUTO, width = 3000,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=10,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String sm;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-09 14:39:01")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=11,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "dcdw", value = "调出单位" ,notes = "2023-11-09 11:14:38")
    @Comment("调出单位")
    @Column(value = "dcdw",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="biz_department",columnReadonly=false,orderIndex=2,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String dcdw;

    @ApiModelProperty(name = "drdw", value = "调入单位" ,notes = "2023-11-09 11:15:03")
    @Comment("调入单位")
    @Column(value = "drdw",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="biz_department",columnReadonly=false,orderIndex=3,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String drdw;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=12,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
