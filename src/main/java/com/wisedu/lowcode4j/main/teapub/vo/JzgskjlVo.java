package com.wisedu.lowcode4j.main.teapub.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.wisedu.lowcode4j.common.core.constant.ApplicationConstant;
import com.wisedu.lowcode4j.common.core.constant.DictCacheType;
import com.wisedu.lowcode4j.main.teapub.po.Jzgskjl;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.sagacity.sqltoy.config.annotation.Translate;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgskjlVo", description = "授课记录Vo")
//end_dynamic_declare
@Data
public class JzgskjlVo extends Jzgskjl {

    private static final long serialVersionUID = 4431474721041957578L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "kclxName", value = "课程类型名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "kclx", cacheType = "KCLX")
    @JSONField(name = "kclx"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String kclxName;

    @ApiModelProperty(name = "kcjbName", value = "课程级别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "kcjb", cacheType = "KCJB")
    @JSONField(name = "kcjb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String kcjbName;

    @ApiModelProperty(name = "skfsName", value = "授课方式名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "skfs", cacheType = "SKFS")
    @JSONField(name = "skfs"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String skfsName;

    @ApiModelProperty(name = "skyzName", value = "授课语种名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "skyz", cacheType = "YZ")
    @JSONField(name = "skyz"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String skyzName;

    @ApiModelProperty(name = "sfxgxkName", value = "是否校公选课名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "sfxgxk", cacheType = "SFBZ")
    @JSONField(name = "sfxgxk"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String sfxgxkName;

    @ApiModelProperty(name = "sfsyskName", value = "是否双语授课名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "sfsysk", cacheType = "SFBZ")
    @JSONField(name = "sfsysk"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String sfsyskName;

    @ApiModelProperty(name = "sfqywskName", value = "是否全英文授课名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "sfqywsk", cacheType = "SFBZ")
    @JSONField(name = "sfqywsk"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String sfqywskName;

    @ApiModelProperty(name = "sfzjrName", value = "是否主讲人名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "sfzjr", cacheType = "SFBZ")
    @JSONField(name = "sfzjr"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String sfzjrName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
