package com.wisedu.lowcode4j.main.teapub.vo;

import com.wisedu.lowcode4j.common.core.constant.*;

import org.sagacity.sqltoy.config.annotation.*;

import io.swagger.annotations.*;

import lombok.Data;
import com.alibaba.fastjson.annotation.*;

import com.wisedu.lowcode4j.main.teapub.po.Jzgbshxx;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgbshxxVo", description = "博士后信息Vo")
//end_dynamic_declare
@Data
public class JzgbshxxVo extends Jzgbshxx {

    private static final long serialVersionUID = 4431474721041952546L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "zlxsName", value = "招录形式名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "zlxs", cacheType = "BSZLXS")
    @JSONField(name = "zlxs"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String zlxsName;

    @ApiModelProperty(name = "bshdqztName", value = "博士后当前状态名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "bshdqzt", cacheType = "BSHDQZT")
    @JSONField(name = "bshdqzt"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String bshdqztName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
