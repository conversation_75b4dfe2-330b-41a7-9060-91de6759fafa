package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.main.teapub.vo.JzggnpxVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzggnpx", description = "国内培训")
@Comment("国内培训")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_BIZ_TEACHER_GNPX", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_BIZ_TEACHER_GNPX", unique = false)
})
@Table(value = "BIZ_TEACHER_GNPX")
@SqlToyEntity
@ModelExt(extClass = JzggnpxVo.class )
@ModelDefine(orderIndex = 15,modelClassify="rsxx", renderType="table")
//end_dynamic_declare
@Data
public class Jzggnpx extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041957224L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-09 13:24:26")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=1,columnHidden=false,columnXtype="text",formRequired=false)
    private String zgh;

    @ApiModelProperty(name = "pxxmmc", value = "培训项目名称" ,notes = "2023-11-09 13:27:53")
    @Comment("培训项目名称")
    @Column(value = "pxxmmc",type = ColType.AUTO, width = 900,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=2,columnHidden=false,columnXtype="text",formRequired=false)
    private String pxxmmc;

    @ApiModelProperty(name = "pxjgmc", value = "培训机构名称" ,notes = "2023-11-09 13:28:28")
    @Comment("培训机构名称")
    @Column(value = "pxjgmc",type = ColType.AUTO, width = 540,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=3,columnHidden=false,columnXtype="text",formRequired=false)
    private String pxjgmc;

    @ApiModelProperty(name = "pxjb", value = "培训级别" ,notes = "2023-11-09 13:31:42")
    @Comment("培训级别")
    @Column(value = "pxjb",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JB",columnReadonly=false,fuzzySearch=false,orderIndex=4,columnHidden=false,columnXtype="select",formRequired=false)
    private String pxjb;

    @ApiModelProperty(name = "pxhdxs", value = "培训获得学时" ,notes = "2023-11-09 13:35:25")
    @Comment("培训获得学时")
    @Column(value = "pxhdxs",type = ColType.AUTO, precision = 2, width = 19,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=6,columnHidden=false,columnXtype="number",formRequired=false)
    private String pxhdxs;

    @ApiModelProperty(name = "pxnd", value = "培训年度" ,notes = "2023-11-09 13:36:06")
    @Comment("培训年度")
    @Column(value = "pxnd",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=7,columnHidden=false,columnFormat = "yyyy",columnXtype = "date-local",columnJsonparam="{\"type\":\"yyyy\",\"format\":\"yyyy\",\"value-format\":\"yyyy\",\"pattern\":\"yyyy\"}",formRequired=false)
    private String pxnd;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-09 14:39:59")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "pxfs", value = "培训方式" ,notes = "2023-11-09 13:33:43")
    @Comment("培训方式")
    @Column(value = "pxfs",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="PXFS",columnReadonly=false,fuzzySearch=false,orderIndex=5,columnHidden=false,columnXtype="select",formRequired=false)
    private String pxfs;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=8,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
