package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.common.core.annotation.ModelDefine;
import com.wisedu.lowcode4j.main.teapub.vo.JzgjtcyVo;
import org.nutz.dao.entity.annotation.*;
import org.sagacity.sqltoy.config.annotation.SqlToyEntity;

import com.wisedu.lowcode4j.common.core.annotation.ColumnDefine;
import com.wisedu.lowcode4j.common.core.annotation.ModelExt;
import com.wisedu.lowcode4j.common.core.model.BaseDataModel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgjtcy", description = "家庭成员")
@Comment("家庭成员")
@ModelDefine(orderIndex = 2,modelClassify="jbxx",renderType="table")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_BIZ_TEACHER_JTCY", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_BIZ_TEACHER_JTCY", unique = false),
})
@Table(value = "BIZ_TEACHER_JTCY")
@SqlToyEntity
@ModelExt(extClass = JzgjtcyVo.class)
//end_dynamic_declare
@Data
public class Jzgjtcy extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041954846L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-07 20:30:50")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-07 18:15:06")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=1,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=true)
    private String zgh;

    @ApiModelProperty(name = "cyxm", value = "成员姓名" ,notes = "2023-11-07 18:15:26")
    @Comment("成员姓名")
    @Column(value = "cyxm",type = ColType.AUTO, width = 720,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=2,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String cyxm;

    @ApiModelProperty(name = "cygx", value = "成员关系" ,notes = "2023-11-07 18:15:55")
    @Comment("成员关系")
    @Column(value = "cygx",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JTGX",columnReadonly=false,orderIndex=3,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String cygx;

    @ApiModelProperty(name = "csny", value = "出生年月" ,notes = "2023-11-07 18:16:25")
    @Comment("出生年月")
    @Column(value = "csny",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=4,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="date-ym",columnFormat="yyyy-MM",formRequired=false)
    private String csny;

    @ApiModelProperty(name = "mz", value = "民族" ,notes = "2023-11-07 18:19:28")
    @Comment("民族")
    @Column(value = "mz",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="MZ",columnReadonly=false,orderIndex=5,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String mz;

    @ApiModelProperty(name = "gj", value = "国籍" ,notes = "2023-11-07 18:20:37")
    @Comment("国籍")
    @Column(value = "gj",type = ColType.AUTO, width = 128,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="GJHDQ",columnReadonly=false,orderIndex=6,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String gj;

    @ApiModelProperty(name = "jkzk", value = "健康状况" ,notes = "2023-11-07 18:21:04")
    @Comment("健康状况")
    @Column(value = "jkzk",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JKZK",columnReadonly=false,orderIndex=7,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String jkzk;

    @ApiModelProperty(name = "gzdw", value = "工作单位" ,notes = "2023-11-07 18:23:11")
    @Comment("工作单位")
    @Column(value = "gzdw",type = ColType.AUTO, width = 1800,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=8,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String gzdw;

    @ApiModelProperty(name = "zwjb", value = "职务级别" ,notes = "2023-11-07 18:23:40")
    @Comment("职务级别")
    @Column(value = "zwjb",type = ColType.AUTO, width = 540,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=9,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zwjb;

    @ApiModelProperty(name = "zyjszw", value = "专业技术职务" ,notes = "2023-11-07 18:24:05")
    @Comment("专业技术职务")
    @Column(value = "zyjszw",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="ZYJSZW",columnReadonly=false,orderIndex=10,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select-tree",formRequired=false,formJsonparam="{\"multiple\":false,\"checkStrictly\":true}")
    private String zyjszw;

    @ApiModelProperty(name = "dh", value = "电话" ,notes = "2023-11-07 18:24:25")
    @Comment("电话")
    @Column(value = "dh",type = ColType.AUTO, width = 120,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=11,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String dh;

    @ApiModelProperty(name = "dzxx", value = "电子信箱" ,notes = "2023-11-07 18:24:45")
    @Comment("电子信箱")
    @Column(value = "dzxx",type = ColType.AUTO, width = 120,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=12,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String dzxx;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=13,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="uploadfile",formRequired=false,formJsonparam="{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
