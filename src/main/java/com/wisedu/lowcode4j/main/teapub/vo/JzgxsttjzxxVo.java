package com.wisedu.lowcode4j.main.teapub.vo;

import com.wisedu.lowcode4j.common.core.constant.*;

import org.sagacity.sqltoy.config.annotation.*;

import io.swagger.annotations.*;

import lombok.Data;
import com.alibaba.fastjson.annotation.*;

import com.wisedu.lowcode4j.main.teapub.po.Jzgxsttjzxx;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgxsttjzxxVo", description = "学术团体兼职信息Vo")
//end_dynamic_declare
@Data
public class JzgxsttjzxxVo extends Jzgxsttjzxx {

    private static final long serialVersionUID = 4431474721041959572L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "xsttjbName", value = "学术团体级别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "xsttjb", cacheType = "XSTTJB")
    @JSONField(name = "xsttjb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String xsttjbName;

    @ApiModelProperty(name = "zzlxName", value = "组织类型名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "zzlx", cacheType = "XSZZLX")
    @JSONField(name = "zzlx"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String zzlxName;

    @ApiModelProperty(name = "jzzwjbName", value = "兼职职务级别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "jzzwjb", cacheType = "ZWJB")
    @JSONField(name = "jzzwjb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String jzzwjbName;

    @ApiModelProperty(name = "ccyyName", value = "辞职原因名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "ccyy", cacheType = "CQJZYY")
    @JSONField(name = "ccyy"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String ccyyName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
