package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.common.core.annotation.ColumnDefine;
import com.wisedu.lowcode4j.common.core.annotation.ModelDefine;
import com.wisedu.lowcode4j.common.core.annotation.ModelExt;
import com.wisedu.lowcode4j.common.core.model.BaseDataModel;
import com.wisedu.lowcode4j.main.teapub.vo.JzgskjlVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nutz.dao.entity.annotation.*;
import org.sagacity.sqltoy.config.annotation.SqlToyEntity;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgskjl", description = "授课记录")
@Comment("授课记录")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_biz_teacher_skjl", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_biz_teacher_skjl", unique = false)
})
@Table(value = "BIZ_TEACHER_SKJL")
@SqlToyEntity
@ModelExt(extClass = JzgskjlVo.class )
@ModelDefine(orderIndex = 1,modelClassify="jyjx", renderType="table")
//end_dynamic_declare
@Data
public class Jzgskjl extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041957221L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-27 16:55:47")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=1,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zgh;

    @ApiModelProperty(name = "kcbm", value = "课程编码" ,notes = "2023-11-27 16:56:09")
    @Comment("课程编码")
    @Column(value = "kcbm",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=2,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String kcbm;

    @ApiModelProperty(name = "kcmc", value = "课程名称" ,notes = "2023-11-27 16:56:30")
    @Comment("课程名称")
    @Column(value = "kcmc",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=3,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String kcmc;

    @ApiModelProperty(name = "kcywm", value = "课程英文名" ,notes = "2023-11-27 16:56:58")
    @Comment("课程英文名")
    @Column(value = "kcywm",type = ColType.AUTO, width = 1500,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=4,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String kcywm;

    @ApiModelProperty(name = "kclx", value = "课程类型" ,notes = "2023-11-27 16:59:02")
    @Comment("课程类型")
    @Column(value = "kclx",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="KCLX",columnReadonly=false,orderIndex=6,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String kclx;

    @ApiModelProperty(name = "kcjb", value = "课程级别" ,notes = "2023-11-27 16:59:38")
    @Comment("课程级别")
    @Column(value = "kcjb",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="KCJB",columnReadonly=false,orderIndex=7,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String kcjb;

    @ApiModelProperty(name = "skfs", value = "授课方式" ,notes = "2023-11-27 17:00:12")
    @Comment("授课方式")
    @Column(value = "skfs",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SKFS",columnReadonly=false,orderIndex=8,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String skfs;

    @ApiModelProperty(name = "skyz", value = "授课语种" ,notes = "2023-11-27 17:00:59")
    @Comment("授课语种")
    @Column(value = "skyz",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="YZ",columnReadonly=false,orderIndex=9,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String skyz;

    @ApiModelProperty(name = "sfxgxk", value = "是否校公选课" ,notes = "2023-11-27 17:01:37")
    @Comment("是否校公选课")
    @Column(value = "sfxgxk",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SFBZ",columnReadonly=false,orderIndex=10,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String sfxgxk;

    @ApiModelProperty(name = "sfsysk", value = "是否双语授课" ,notes = "2023-11-27 17:02:07")
    @Comment("是否双语授课")
    @Column(value = "sfsysk",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SFBZ",columnReadonly=false,orderIndex=11,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String sfsysk;

    @ApiModelProperty(name = "sfqywsk", value = "是否全英文授课" ,notes = "2023-11-27 17:02:38")
    @Comment("是否全英文授课")
    @Column(value = "sfqywsk",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SFBZ",columnReadonly=false,orderIndex=12,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String sfqywsk;

    @ApiModelProperty(name = "zwjc", value = "中文教材" ,notes = "2023-11-27 17:03:06")
    @Comment("中文教材")
    @Column(value = "zwjc",type = ColType.AUTO, width = 3000,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=13,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zwjc;

    @ApiModelProperty(name = "zwcks", value = "中文参考书" ,notes = "2023-11-27 17:03:45")
    @Comment("中文参考书")
    @Column(value = "zwcks",type = ColType.AUTO, width = 3000,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=14,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zwcks;

    @ApiModelProperty(name = "wwjc", value = "外文教材" ,notes = "2023-11-27 17:04:09")
    @Comment("外文教材")
    @Column(value = "wwjc",type = ColType.AUTO, width = 3000,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=15,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String wwjc;

    @ApiModelProperty(name = "wwcks", value = "外文参考书" ,notes = "2023-11-27 17:04:28")
    @Comment("外文参考书")
    @Column(value = "wwcks",type = ColType.AUTO, width = 3000,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=16,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String wwcks;

    @ApiModelProperty(name = "kczwjs", value = "课程中文介绍" ,notes = "2023-11-27 17:04:53")
    @Comment("课程中文介绍")
    @Column(value = "kczwjs",type = ColType.AUTO, width = 3000,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=17,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String kczwjs;

    @ApiModelProperty(name = "kcywjs", value = "课程英文介绍" ,notes = "2023-11-27 17:05:15")
    @Comment("课程英文介绍")
    @Column(value = "kcywjs",type = ColType.AUTO, width = 3000,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=18,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String kcywjs;

    @ApiModelProperty(name = "sfzjr", value = "是否主讲人" ,notes = "2023-11-27 17:05:45")
    @Comment("是否主讲人")
    @Column(value = "sfzjr",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SFBZ",columnReadonly=false,orderIndex=19,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String sfzjr;

    @ApiModelProperty(name = "xnxq", value = "学年学期" ,notes = "2023-11-27 17:06:07")
    @Comment("学年学期")
    @Column(value = "xnxq",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=20,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String xnxq;

    @ApiModelProperty(name = "krl", value = "课容量" ,notes = "2023-11-27 17:07:08")
    @Comment("课容量")
    @Column(value = "krl",type = ColType.AUTO, width = 8,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=21,fuzzySearch=false,columnHidden=false,columnXtype="number",formRequired=false)
    private Integer krl;

    @ApiModelProperty(name = "xkzrs", value = "选课总人数" ,notes = "2023-11-27 17:07:38")
    @Comment("选课总人数")
    @Column(value = "xkzrs",type = ColType.AUTO, width = 8,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=22,fuzzySearch=false,columnHidden=false,columnXtype="number",formRequired=false)
    private Integer xkzrs;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-27 17:30:00")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=23,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "kcfzr", value = "课程负责人" ,notes = "2023-11-27 16:57:49")
    @Comment("课程负责人")
    @Column(value = "kcfzr",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=5,fuzzySearch=false,columnHidden=false,columnJsonparam="{\"dictParams\":{\"label\":\"teacherName\",\"value\":\"zgh\",\"url\":\"/plugins/grsjzx/citeTable/getTeacherTable\",\"dataPath\":\"rows\"},\"columns\":[{\"name\":\"userName\",\"label\":\"姓名\"}, {\"name\":\"zgh\",\"label\":\"工号\"}],\"filterProps\":{\"filterable\":true,\"remote\":true},\"multiple\":false,\"pagerConfig\":{\"pageSizes\":[5, 10, 20, 50, 100],\"background\":false,\"pagerCount\":7,\"hideOnSinglePage\":false,\"pageSize\":5,\"position\":\"right\",\"layouts\":[\"prev\", \"pager\", \"next\", \"sizes\", \"total\"],\"enabled\":true}}",columnXtype="text",formRequired=false)
    private String kcfzr;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=24,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
