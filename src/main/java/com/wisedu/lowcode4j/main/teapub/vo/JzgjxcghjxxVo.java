package com.wisedu.lowcode4j.main.teapub.vo;

import com.wisedu.lowcode4j.common.core.constant.*;

import org.sagacity.sqltoy.config.annotation.*;

import io.swagger.annotations.*;

import lombok.Data;
import com.alibaba.fastjson.annotation.*;

import com.wisedu.lowcode4j.main.teapub.po.Jzgjxcghjxx;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgjxcghjxxVo", description = "教学成果获奖信息Vo")
//end_dynamic_declare
@Data
public class JzgjxcghjxxVo extends Jzgjxcghjxx {

    private static final long serialVersionUID = 4431474721041951286L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "jsName", value = "角色名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "js", cacheType = "JS")
    @JSONField(name = "js"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String jsName;

    @ApiModelProperty(name = "jljbName", value = "奖励级别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "jljb", cacheType = "JB")
    @JSONField(name = "jljb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String jljbName;

    @ApiModelProperty(name = "jldjName", value = "奖励等级名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "jldj", cacheType = "JLDJ")
    @JSONField(name = "jldj"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String jldjName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
