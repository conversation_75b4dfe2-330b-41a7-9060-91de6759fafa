package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.common.core.annotation.ColumnDefine;
import com.wisedu.lowcode4j.common.core.annotation.ModelDefine;
import com.wisedu.lowcode4j.common.core.annotation.ModelExt;
import com.wisedu.lowcode4j.common.core.constant.ApplicationConstant;
import com.wisedu.lowcode4j.common.model.Processor;
import com.wisedu.lowcode4j.common.validator.annotation.ModelValid;
import com.wisedu.lowcode4j.main.bizpub.vo.TeacherVo;
import com.wisedu.lowcode4j.main.teapub.vo.ExtTeacherVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nutz.dao.entity.annotation.*;
import org.sagacity.sqltoy.config.annotation.Secure;
import org.sagacity.sqltoy.config.annotation.SecureConfig;
import org.sagacity.sqltoy.config.annotation.SqlToyEntity;

import java.util.Date;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "bizteacher", description = "教职工基本信息")
@Comment("教职工")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_zgh_BIZ_TEACHER", unique = true),
        @Index(fields = {"departmentCode"}, name = "idx_dept_BIZ_TEACHER", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_BIZ_TEACHER", unique = false)
})
@ModelValid.List({
})
@Table(value = "BIZ_TEACHER")
@SqlToyEntity
@ModelExt(extClass = ExtTeacherVo.class ,custom=ApplicationConstant.YES)
@Processor(processorBean="teacherBizProcessor")
@ModelDefine(orderIndex = 1,modelClassify="jbxx", renderType="form")
//end_dynamic_declare
@Data
public class ExtTeacher extends TeacherVo {

    private static final long serialVersionUID = 4431474721041958431L;

    //region start_dynamic_column
    @ApiModelProperty(name = "source", value = "教职工来源" ,notes = "2023-09-26 09:51:45")
    @Comment("教职工来源")
    @Column(value = "source",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="GXJZGLY",columnGroup="用工信息",columnReadonly=false,orderIndex=33,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String source;

    @ApiModelProperty(name = "beginTeachDate", value = "从教年月" ,notes = "2023-09-26 09:51:51")
    @Comment("从教年月")
    @Column(value = "begin_teach_date",type = ColType.AUTO, width = 18,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnGroup="用工信息",columnReadonly=false,orderIndex=36,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="date-ym",columnFormat="yyyy-MM",formRequired=false)
    private String beginTeachDate;

    @ApiModelProperty(name = "arriveDate", value = "来校年月" ,notes = "2023-09-26 09:51:40")
    @Comment("来校年月")
    @Column(value = "arrive_date",type = ColType.AUTO, width = 18,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnGroup="用工信息",columnReadonly=false,orderIndex=37,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="date-ym",columnFormat="yyyy-MM",formRequired=false)
    private String arriveDate;

    @ApiModelProperty(name = "category", value = "编制类别" ,notes = "2023-09-26 09:51:45")
    @Comment("编制类别")
    @Column(value = "category",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="BZLX",columnGroup="用工信息",columnReadonly=false,orderIndex=34,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String category;

    @ApiModelProperty(name = "leaveDate", value = "离校日期" ,notes = "2023-09-26 09:51:40")
    @Comment("离校日期")
    @Column(value = "leave_date",type = ColType.AUTO, width = 0,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnGroup="用工信息",columnReadonly=false,orderIndex=54,columnLinkSelect=true,fuzzySearch=false,columnPattern="yyyy-MM-dd",columnHidden=false,columnFormat="yyyy-MM-dd",columnXtype="date-local",formRequired=false)
    private Date leaveDate;

    @ApiModelProperty(name = "employmentForm", value = "用人方式" ,notes = "2023-09-26 09:51:33")
    @Comment("用人方式")
    @Column(value = "employment_form",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="YRFS",columnGroup="用工信息",columnReadonly=false,orderIndex=35,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String employmentForm;

    @ApiModelProperty(name = "parttimeCategory", value = "兼职教师聘任类别" ,notes = "2023-09-26 09:51:31")
    @Comment("兼职教师聘任类别")
    @Column(value = "parttime_category",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JZJSPRLB",columnGroup="用工信息",columnReadonly=false,orderIndex=40,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String parttimeCategory;

    @ApiModelProperty(name = "advisorCategory", value = "导师类别" ,notes = "2023-09-26 09:51:50")
    @Comment("导师类别")
    @Column(value = "advisor_category",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="DSLB",columnGroup="用工信息",columnReadonly=false,orderIndex=42,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String advisorCategory;

    @ApiModelProperty(name = "advisorDate", value = "导师聘任年月" ,notes = "2023-09-26 09:51:33")
    @Comment("导师聘任年月")
    @Column(value = "advisor_date",type = ColType.AUTO, width = 18,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnGroup="用工信息",columnReadonly=false,orderIndex=43,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="date-ym",columnFormat="yyyy-MM",formRequired=false)
    private String advisorDate;

    @ApiModelProperty(name = "instructorCategory", value = "辅导员类别" ,notes = "2023-09-26 09:51:53")
    @Comment("辅导员类别")
    @Column(value = "instructor_category",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="FDYLB",columnGroup="用工信息",columnReadonly=false,orderIndex=45,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String instructorCategory;

    @ApiModelProperty(name = "personIdentity", value = "个人身份" ,notes = "2023-09-26 09:51:42")
    @Comment("个人身份")
    @Column(value = "person_identity",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="GRSF",columnGroup="用工信息",columnReadonly=false,orderIndex=46,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String personIdentity;

    @ApiModelProperty(name = "isDoubleTeach", value = "是否双师型教师" ,notes = "2023-09-26 09:51:39")
    @Comment("是否双师型教师")
    @Column(value = "is_double_teach",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SFBZ",columnGroup="用工信息",columnReadonly=false,orderIndex=47,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String isDoubleTeach;

    @ApiModelProperty(name = "isDoubleShoulder", value = "是否双肩挑" ,notes = "2023-09-26 09:51:31")
    @Comment("是否双肩挑")
    @Column(value = "is_double_shoulder",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SFBZ",columnGroup="用工信息",columnReadonly=false,orderIndex=48,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String isDoubleShoulder;

    @ApiModelProperty(name = "subjectDivision", value = "学科门类" ,notes = "2023-09-26 09:51:51")
    @Comment("学科门类")
    @Column(value = "subject_division",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="XWSYHRCPYXKML",columnGroup="用工信息",columnReadonly=false,orderIndex=49,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select-tree",formRequired=false,formJsonparam="{\"multiple\":false,\"checkStrictly\":true}")
    private String subjectDivision;

    @ApiModelProperty(name = "firstSubject", value = "一级学科" ,notes = "2023-09-26 09:51:53")
    @Comment("一级学科")
    @Column(value = "first_subject",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="XWSYHRCPYXKML",columnGroup="用工信息",columnReadonly=false,orderIndex=50,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select-tree",formRequired=false,formJsonparam="{\"multiple\":false,\"checkStrictly\":true}")
    private String firstSubject;

    @ApiModelProperty(name = "secondSubject", value = "二级学科" ,notes = "2023-09-26 09:51:32")
    @Comment("二级学科")
    @Column(value = "second_subject",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="XWSYHRCPYXKML",columnGroup="用工信息",columnReadonly=false,orderIndex=51,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select-tree",formRequired=false,formJsonparam="{\"multiple\":false,\"checkStrictly\":true}")
    private String secondSubject;

    @ApiModelProperty(name = "researchDirection", value = "研究方向" ,notes = "2023-09-26 09:51:45")
    @Comment("研究方向")
    @Column(value = "research_direction",type = ColType.AUTO, width = 3000,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnGroup="用工信息",columnReadonly=false,orderIndex=52,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String researchDirection;

    @ApiModelProperty(name = "expectedRetirementDate", value = "预计退休日期" ,notes = "2023-09-26 09:51:34")
    @Comment("预计退休日期")
    @Column(value = "expected_retirement_date",type = ColType.AUTO, width = 0,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnGroup="用工信息",columnReadonly=false,orderIndex=55,columnLinkSelect=true,fuzzySearch=false,columnPattern="yyyy-MM-dd",columnHidden=false,columnFormat="yyyy-MM-dd",columnXtype="date-local",formRequired=false)
    private Date expectedRetirementDate;

    @ApiModelProperty(name = "professionalPositionLevel", value = "专业技术职务级别" ,notes = "2023-09-26 09:51:51")
    @Comment("专业技术职务级别")
    @Column(value = "professional_position_level",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="ZWJB",columnGroup="用工信息",columnReadonly=false,orderIndex=57,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String professionalPositionLevel;

    @ApiModelProperty(name = "isProfessionalPost", value = "是否专业技术岗位" ,notes = "2023-09-26 09:51:44")
    @Comment("是否专业技术岗位")
    @Column(value = "is_professional_post",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SFBZ",columnGroup="用工信息",columnReadonly=false,orderIndex=58,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String isProfessionalPost;

    @ApiModelProperty(name = "professionalPostLevel", value = "专业技术岗位等级" ,notes = "2023-09-26 09:51:40")
    @Comment("专业技术岗位等级")
    @Column(value = "professional_post_level",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SYDWZYJSRYGWDJ",columnGroup="用工信息",columnReadonly=false,orderIndex=59,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String professionalPostLevel;

    @ApiModelProperty(name = "isManagePost", value = "是否管理岗位" ,notes = "2023-09-26 09:51:38")
    @Comment("是否管理岗位")
    @Column(value = "is_manage_post",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SFBZ",columnGroup="用工信息",columnReadonly=false,orderIndex=60,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String isManagePost;

    @ApiModelProperty(name = "managePostLevel", value = "管理岗位等级" ,notes = "2023-09-26 09:51:47")
    @Comment("管理岗位等级")
    @Column(value = "manage_post_level",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SYDWZYJSRYGWDJ",columnGroup="用工信息",columnReadonly=false,orderIndex=61,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String managePostLevel;

    @ApiModelProperty(name = "isRearservicePost", value = "是否工勤岗位" ,notes = "2023-09-26 09:51:32")
    @Comment("是否工勤岗位")
    @Column(value = "is_rearservice_post",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SFBZ",columnGroup="用工信息",columnReadonly=false,orderIndex=62,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String isRearservicePost;

    @ApiModelProperty(name = "rearservicePostLevel", value = "工勤岗位等级" ,notes = "2023-09-26 09:51:44")
    @Comment("工勤岗位等级")
    @Column(value = "rearservice_post_level",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SYDWZYJSRYGWDJ",columnGroup="用工信息",columnReadonly=false,orderIndex=63,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String rearservicePostLevel;

    @ApiModelProperty(name = "mainPostType", value = "主要岗位类型" ,notes = "2023-09-26 09:51:48")
    @Comment("主要岗位类型")
    @Column(value = "main_post_type",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="GWLX",columnGroup="用工信息",columnReadonly=false,orderIndex=64,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String mainPostType;

    @ApiModelProperty(name = "cadrePost", value = "干部职务" ,notes = "2023-09-26 09:51:40")
    @Comment("干部职务")
    @Column(value = "cadre_post",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="GBZWMC",columnGroup="用工信息",columnReadonly=false,orderIndex=66,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String cadrePost;

    @ApiModelProperty(name = "cadrePostLevel", value = "干部职务级别" ,notes = "2023-09-26 09:51:41")
    @Comment("干部职务级别")
    @Column(value = "cadre_post_level",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="ZWJB",columnGroup="用工信息",columnReadonly=false,orderIndex=67,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String cadrePostLevel;

    @ApiModelProperty(name = "postName", value = "岗位名称" ,notes = "2023-09-26 09:51:45")
    @Comment("岗位名称")
    @Column(value = "post_name",type = ColType.AUTO, width = 1800,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnGroup="用工信息",columnReadonly=false,orderIndex=65,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String postName;

    @ApiModelProperty(name = "lastDegree", value = "最高学位" ,notes = "2023-09-26 09:51:29")
    @Comment("最高学位")
    @Column(value = "last_degree",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="ZHRMGHGXW",columnGroup="资历信息",columnReadonly=false,orderIndex=69,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select-tree",formRequired=false,formJsonparam="{\"multiple\":false,\"checkStrictly\":true}")
    private String lastDegree;

    @ApiModelProperty(name = "lastEducation", value = "最高学历" ,notes = "2023-09-26 09:51:36")
    @Comment("最高学历")
    @Column(value = "last_education",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="XL",columnGroup="资历信息",columnReadonly=false,orderIndex=68,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false,formJsonparam="{\"multiple\":false}")
    private String lastEducation;

    @ApiModelProperty(name = "lastGraduationSchool", value = "最高学历毕业院校" ,notes = "2023-09-26 09:51:39")
    @Comment("最高学历毕业院校")
    @Column(value = "last_graduation_school",type = ColType.AUTO, width = 540,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnGroup="资历信息",columnReadonly=false,orderIndex=70,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String lastGraduationSchool;

    @ApiModelProperty(name = "fromCurrentSchool", value = "是否本校毕业" ,notes = "2023-09-26 09:51:43")
    @Comment("是否本校毕业")
    @Column(value = "from_current_school",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SFBZ",columnGroup="资历信息",columnReadonly=false,orderIndex=71,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String fromCurrentSchool;

    @ApiModelProperty(name = "lastGraduationSchoolType", value = "最高学历毕业院校类型" ,notes = "2023-09-26 09:51:39")
    @Comment("最高学历毕业院校类型")
    @Column(value = "last_graduation_school_type",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="GXLB",columnGroup="资历信息",columnReadonly=false,orderIndex=72,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String lastGraduationSchoolType;

    @ApiModelProperty(name = "overseasExperience", value = "海外经历" ,notes = "2023-09-26 09:51:41")
    @Comment("海外经历")
    @Column(value = "overseas_experience",type = ColType.AUTO, width = 900,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnGroup="资历信息",columnReadonly=false,orderIndex=73,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String overseasExperience;

    @ApiModelProperty(name = "beginWorkDate", value = "参加工作年月" ,notes = "2023-09-26 09:51:29")
    @Comment("参加工作年月")
    @Column(value = "begin_work_date",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnGroup="个人信息",columnReadonly=false,orderIndex=9,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="date-ym",columnFormat="yyyy-MM",formRequired=false)
    private String beginWorkDate;

    @ApiModelProperty(name = "archiveNumber", value = "档案编号" ,notes = "2023-09-26 09:51:49")
    @Comment("档案编号")
    @Column(value = "archive_number",type = ColType.AUTO, width = 540,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnGroup="用工信息",columnReadonly=false,orderIndex=38,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String archiveNumber;

    @ApiModelProperty(name = "usedName", value = "曾用名" ,notes = "2023-09-26 09:51:39")
    @Comment("曾用名")
    @Column(value = "used_name",type = ColType.AUTO, width = 720,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnGroup="个人信息",columnReadonly=false,orderIndex=3,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String usedName;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2024-01-08 14:15:26")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnGroup="附件",columnReadonly=false,orderIndex=80,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="uploadfile",formRequired=false,formJsonparam="{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2024-01-08 14:11:59")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=79,columnLinkSelect=true,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "age", value = "年龄" ,notes = "2024-05-30 15:38:59")
    @Comment("年龄")
    @Column(value = "age",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnGroup="个人信息",columnReadonly=false,orderIndex=5,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="number",formRequired=false)
    private Integer age;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
