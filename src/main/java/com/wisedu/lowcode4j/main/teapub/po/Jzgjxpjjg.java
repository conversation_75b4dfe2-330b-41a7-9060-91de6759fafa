package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.main.teapub.vo.JzgjxpjjgVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgjxpjjg", description = "教学评价结果")
@Comment("教学评价结果")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_BIZ_TEACHER_JXPJJG", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_BIZ_TEACHER_JXPJJG", unique = false)
})
@Table(value = "BIZ_TEACHER_JXPJJG")
@SqlToyEntity
@ModelExt(extClass = JzgjxpjjgVo.class )
@ModelDefine(orderIndex = 4,modelClassify="jyjx", renderType="table")
//end_dynamic_declare
@Data
public class Jzgjxpjjg extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041957933L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-09 19:10:59")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=1,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zgh;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-09 19:11:08")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "xnxq", value = "学年学期" ,notes = "2023-11-09 19:11:43")
    @Comment("学年学期")
    @Column(value = "xnxq",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=2,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String xnxq;

    @ApiModelProperty(name = "pjrq", value = "评教日期" ,notes = "2023-11-09 19:12:06")
    @Comment("评教日期")
    @Column(value = "pjrq",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=3,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private String pjrq;

    @ApiModelProperty(name = "xspjcj", value = "学生评教成绩" ,notes = "2023-11-09 19:12:40")
    @Comment("学生评教成绩")
    @Column(value = "xspjcj",type = ColType.AUTO, precision = 2, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=4,fuzzySearch=false,columnHidden=false,columnXtype="number",formRequired=false)
    private Double xspjcj;

    @ApiModelProperty(name = "jspjcj", value = "教师评教成绩" ,notes = "2023-11-09 19:12:56")
    @Comment("教师评教成绩")
    @Column(value = "jspjcj",type = ColType.AUTO, precision = 2, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=5,fuzzySearch=false,columnHidden=false,columnXtype="number",formRequired=false)
    private Double jspjcj;

    @ApiModelProperty(name = "ddpjcj", value = "督导评教成绩" ,notes = "2023-11-09 19:13:10")
    @Comment("督导评教成绩")
    @Column(value = "ddpjcj",type = ColType.AUTO, precision = 2, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=6,fuzzySearch=false,columnHidden=false,columnXtype="number",formRequired=false)
    private Double ddpjcj;

    @ApiModelProperty(name = "xypjcj", value = "学院评教成绩" ,notes = "2023-11-09 19:13:36")
    @Comment("学院评教成绩")
    @Column(value = "xypjcj",type = ColType.AUTO, precision = 2, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=7,fuzzySearch=false,columnHidden=false,columnXtype="number",formRequired=false)
    private Double xypjcj;

    @ApiModelProperty(name = "gcpjcj", value = "过程评教成绩" ,notes = "2023-11-09 19:14:04")
    @Comment("过程评教成绩")
    @Column(value = "gcpjcj",type = ColType.AUTO, precision = 2, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=8,fuzzySearch=false,columnHidden=false,columnXtype="number",formRequired=false)
    private Double gcpjcj;

    @ApiModelProperty(name = "zcj", value = "总成绩" ,notes = "2023-11-09 19:14:22")
    @Comment("总成绩")
    @Column(value = "zcj",type = ColType.AUTO, precision = 2, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=9,fuzzySearch=false,columnHidden=false,columnXtype="number",formRequired=false)
    private Double zcj;

    @ApiModelProperty(name = "pjdj", value = "评教等级" ,notes = "2023-11-09 19:14:36")
    @Comment("评教等级")
    @Column(value = "pjdj",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=10,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String pjdj;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=11,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
