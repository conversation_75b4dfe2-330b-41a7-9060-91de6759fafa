package com.wisedu.lowcode4j.main.teapub.vo;

import com.wisedu.lowcode4j.common.core.constant.*;

import org.sagacity.sqltoy.config.annotation.*;

import io.swagger.annotations.*;

import lombok.Data;
import com.alibaba.fastjson.annotation.*;

import com.wisedu.lowcode4j.main.teapub.po.Jzggnpx;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzggnpxVo", description = "国内培训Vo")
//end_dynamic_declare
@Data
public class JzggnpxVo extends Jzggnpx {

    private static final long serialVersionUID = 4431474721041958428L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "pxjbName", value = "培训级别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "pxjb", cacheType = "JB")
    @JSONField(name = "pxjb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String pxjbName;

    @ApiModelProperty(name = "pxfsName", value = "培训方式名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "pxfs", cacheType = "PXFS")
    @JSONField(name = "pxfs"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String pxfsName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
