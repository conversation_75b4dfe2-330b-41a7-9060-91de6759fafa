package com.wisedu.lowcode4j.main.teapub.vo;

import com.wisedu.lowcode4j.common.core.constant.*;

import org.sagacity.sqltoy.config.annotation.*;

import io.swagger.annotations.*;

import lombok.Data;
import com.alibaba.fastjson.annotation.*;

import com.wisedu.lowcode4j.main.teapub.po.Jzgzcppjl;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgzcppjlVo", description = "职称评聘记录Vo")
//end_dynamic_declare
@Data
public class JzgzcppjlVo extends Jzgzcppjl {

    private static final long serialVersionUID = 4431474721041958807L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "zyjszwName", value = "专业技术职务名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "zyjszw", cacheType = "ZYJSZW")
    @JSONField(name = "zyjszw"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String zyjszwName;

    @ApiModelProperty(name = "przyjszwName", value = "聘任专业技术职务名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "przyjszw", cacheType = "ZYJSZW")
    @JSONField(name = "przyjszw"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String przyjszwName;

    @ApiModelProperty(name = "zyjszwjbName", value = "专业技术职务级别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "zyjszwjb", cacheType = "ZWJB")
    @JSONField(name = "zyjszwjb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String zyjszwjbName;

    @ApiModelProperty(name = "prdwName", value = "聘任单位名称")
    @Translate(cacheName = "biz_department", keyField = "prdw", cacheType = "")
    @JSONField(name = "prdw"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String prdwName;

    @ApiModelProperty(name = "prztName", value = "聘任状态名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "przt", cacheType = "PRQK")
    @JSONField(name = "przt"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String prztName;

    @ApiModelProperty(name = "przyjszwjbName", value = "聘任专业技术职务级别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "przyjszwjb", cacheType = "ZWJB")
    @JSONField(name = "przyjszwjb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String przyjszwjbName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
