package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.common.core.annotation.ModelDefine;
import org.nutz.dao.entity.annotation.*;
import org.sagacity.sqltoy.config.annotation.SqlToyEntity;

import com.wisedu.lowcode4j.common.core.annotation.ColumnDefine;
import com.wisedu.lowcode4j.common.core.annotation.ModelExt;
import com.wisedu.lowcode4j.common.core.model.BaseDataModel;
import com.wisedu.lowcode4j.main.teapub.vo.JzggccrcxxVo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzggccrcxx", description = "高层次人才信息")
@Comment("高层次人才信息")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_BIZ_TEACHER_GCCRCXX", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_BIZ_TEACHER_GCCRCXX", unique = false)
})
@Table(value = "BIZ_TEACHER_GCCRCXX")
@SqlToyEntity
@ModelExt(extClass = JzggccrcxxVo.class )
@ModelDefine(orderIndex = 5,modelClassify="rsxx", renderType="table")
//end_dynamic_declare
@Data
public class Jzggccrcxx extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041956637L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-08 15:49:44")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=1,columnHidden=false,columnXtype="text",formRequired=false)
    private String zgh;

    @ApiModelProperty(name = "pzdw", value = "批准单位" ,notes = "2023-11-08 15:51:39")
    @Comment("批准单位")
    @Column(value = "pzdw",type = ColType.AUTO, width = 900,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=3,columnHidden=false,columnXtype="text",formRequired=false)
    private String pzdw;

    @ApiModelProperty(name = "pzny", value = "批准年月" ,notes = "2023-11-08 15:52:17")
    @Comment("批准年月")
    @Column(value = "pzny",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=5,columnHidden=false,columnXtype="date-ym",columnFormat="yyyy-MM",formRequired=false)
    private String pzny;

    @ApiModelProperty(name = "xsdy", value = "享受待遇" ,notes = "2023-11-08 15:52:32")
    @Comment("享受待遇")
    @Column(value = "xsdy",type = ColType.AUTO, width = 1800,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=6,columnHidden=false,columnXtype="text",formRequired=false)
    private String xsdy;

    @ApiModelProperty(name = "zt", value = "zt" ,notes = "2023-11-08 15:52:40")
    @Comment("zt")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "rclb", value = "人才类别" ,notes = "2023-11-08 15:51:13")
    @Comment("人才类别")
    @Column(value = "rclb",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="ZJLB",columnReadonly=false,fuzzySearch=false,orderIndex=2,columnHidden=false,columnXtype="select",formRequired=false)
    private String rclb;

    @ApiModelProperty(name = "pzdwjb", value = "批准单位级别" ,notes = "2023-11-08 15:51:58")
    @Comment("批准单位级别")
    @Column(value = "pzdwjb",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="DWJB",columnReadonly=false,fuzzySearch=false,orderIndex=4,columnHidden=false,columnXtype="select",formRequired=false)
    private String pzdwjb;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=7,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
