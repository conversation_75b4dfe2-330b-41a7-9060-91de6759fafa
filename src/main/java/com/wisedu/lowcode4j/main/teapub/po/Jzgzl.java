package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.common.core.annotation.ColumnDefine;
import com.wisedu.lowcode4j.common.core.annotation.ModelDefine;
import com.wisedu.lowcode4j.common.core.annotation.ModelExt;
import com.wisedu.lowcode4j.common.core.model.BaseDataModel;
import com.wisedu.lowcode4j.main.teapub.vo.JzgzlVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nutz.dao.entity.annotation.*;
import org.sagacity.sqltoy.config.annotation.SqlToyEntity;

import java.util.Date;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgzl", description = "专利")
@Comment("专利")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_biz_teacher_zl", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_biz_teacher_zl", unique = false)
})
@Table(value = "BIZ_TEACHER_ZL")
@SqlToyEntity
@ModelExt(extClass = JzgzlVo.class )
@ModelDefine(orderIndex = 4,modelClassify="kyxx", renderType="table")
//end_dynamic_declare
@Data
public class Jzgzl extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041955105L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-28 09:30:42")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=1,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zgh;

    @ApiModelProperty(name = "zlmc", value = "专利名称" ,notes = "2023-11-28 09:31:24")
    @Comment("专利名称")
    @Column(value = "zlmc",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=2,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zlmc;

    @ApiModelProperty(name = "zlsqbh", value = "专利申请编号" ,notes = "2023-11-28 09:31:53")
    @Comment("专利申请编号")
    @Column(value = "zlsqbh",type = ColType.AUTO, width = 90,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=3,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zlsqbh;

    @ApiModelProperty(name = "zllx", value = "专利类型" ,notes = "2023-11-28 09:32:37")
    @Comment("专利类型")
    @Column(value = "zllx",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="ZLLX",columnReadonly=false,orderIndex=4,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String zllx;

    @ApiModelProperty(name = "zlzt", value = "专利状态" ,notes = "2023-11-28 09:33:20")
    @Comment("专利状态")
    @Column(value = "zlzt",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="ZLFLZT",columnReadonly=false,orderIndex=5,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String zlzt;

    @ApiModelProperty(name = "sqr", value = "申请人" ,notes = "2023-11-28 09:33:41")
    @Comment("申请人")
    @Column(value = "sqr",type = ColType.AUTO, width = 240,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=6,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String sqr;

    @ApiModelProperty(name = "sqrq", value = "申请日期" ,notes = "2023-11-28 09:34:10")
    @Comment("申请日期")
    @Column(value = "sqrq",type = ColType.AUTO, width = 0,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=7,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private Date sqrq;

    @ApiModelProperty(name = "dyfmr", value = "第一发明人" ,notes = "2023-11-28 09:34:32")
    @Comment("第一发明人")
    @Column(value = "dyfmr",type = ColType.AUTO, width = 240,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=8,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String dyfmr;

    @ApiModelProperty(name = "zlqr", value = "专利权人" ,notes = "2023-11-28 09:34:51")
    @Comment("专利权人")
    @Column(value = "zlqr",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=9,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zlqr;

    @ApiModelProperty(name = "zlsqggh", value = "专利授权公告号" ,notes = "2023-11-28 09:35:16")
    @Comment("专利授权公告号")
    @Column(value = "zlsqggh",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=10,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zlsqggh;

    @ApiModelProperty(name = "zlsqggrq", value = "专利授权公告日期" ,notes = "2023-11-28 09:35:49")
    @Comment("专利授权公告日期")
    @Column(value = "zlsqggrq",type = ColType.AUTO, width = 0,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=11,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private Date zlsqggrq;

    @ApiModelProperty(name = "sfbxzl", value = "是否本校专利" ,notes = "2023-11-28 09:36:30")
    @Comment("是否本校专利")
    @Column(value = "sfbxzl",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SFBZ",columnReadonly=false,orderIndex=12,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String sfbxzl;

    @ApiModelProperty(name = "sfpctzl", value = "是否PCT专利" ,notes = "2023-11-28 09:37:15")
    @Comment("是否PCT专利")
    @Column(value = "sfpctzl",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SFBZ",columnReadonly=false,orderIndex=13,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String sfpctzl;

    @ApiModelProperty(name = "zlgj", value = "专利国家" ,notes = "2023-11-28 09:37:46")
    @Comment("专利国家")
    @Column(value = "zlgj",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="GJHDQ",columnReadonly=false,orderIndex=17,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String zlgj;

    @ApiModelProperty(name = "flh", value = "分类号" ,notes = "2023-11-28 09:38:04")
    @Comment("分类号")
    @Column(value = "flh",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=14,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String flh;

    @ApiModelProperty(name = "js", value = "角色" ,notes = "2023-11-28 09:38:50")
    @Comment("角色")
    @Column(value = "js",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JS",columnReadonly=false,orderIndex=15,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String js;

    @ApiModelProperty(name = "gxl", value = "贡献率" ,notes = "2023-11-28 09:39:26")
    @Comment("贡献率")
    @Column(value = "gxl",type = ColType.AUTO, precision = 4, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=16,fuzzySearch=false,columnHidden=false,columnXtype="number",formRequired=false)
    private Double gxl;

    @ApiModelProperty(name = "fmrpm", value = "发明人排名" ,notes = "2023-11-28 09:40:16")
    @Comment("发明人排名")
    @Column(value = "fmrpm",type = ColType.AUTO, width = 5,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=18,fuzzySearch=false,columnHidden=false,columnXtype="number",formRequired=false)
    private Integer fmrpm;

    @ApiModelProperty(name = "sfbxdyfmr", value = "是否本校第一发明人" ,notes = "2023-11-28 09:41:19")
    @Comment("是否本校第一发明人")
    @Column(value = "sfbxdyfmr",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SFBZ",columnReadonly=false,orderIndex=19,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String sfbxdyfmr;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-28 09:41:45")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=20,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=21,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
