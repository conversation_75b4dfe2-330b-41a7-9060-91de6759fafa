package com.wisedu.lowcode4j.main.teapub.vo;

import com.wisedu.lowcode4j.common.core.constant.*;

import org.sagacity.sqltoy.config.annotation.*;

import io.swagger.annotations.*;

import lombok.Data;
import com.alibaba.fastjson.annotation.*;

import com.wisedu.lowcode4j.main.teapub.po.Jzgxndd;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgxnddVo", description = "校内调动Vo")
//end_dynamic_declare
@Data
public class JzgxnddVo extends Jzgxndd {

    private static final long serialVersionUID = 4431474721041959385L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "sfzgName", value = "是否转岗名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "sfzg", cacheType = "SFBZ")
    @JSONField(name = "sfzg"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String sfzgName;

    @ApiModelProperty(name = "dcdwName", value = "调出单位名称")
    @Translate(cacheName = "biz_department", keyField = "dcdw", cacheType = "")
    @JSONField(name = "dcdw"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String dcdwName;

    @ApiModelProperty(name = "drdwName", value = "调入单位名称")
    @Translate(cacheName = "biz_department", keyField = "drdw", cacheType = "")
    @JSONField(name = "drdw"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String drdwName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
