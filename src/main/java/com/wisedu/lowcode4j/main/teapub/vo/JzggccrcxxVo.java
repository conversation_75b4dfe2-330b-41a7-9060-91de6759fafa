package com.wisedu.lowcode4j.main.teapub.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.wisedu.lowcode4j.common.core.constant.ApplicationConstant;
import com.wisedu.lowcode4j.common.core.constant.DictCacheType;
import com.wisedu.lowcode4j.main.teapub.po.Jzggccrcxx;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.sagacity.sqltoy.config.annotation.Translate;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzggccrcxxVo", description = "高层次人才信息Vo")
//end_dynamic_declare
@Data
public class JzggccrcxxVo extends Jzggccrcxx {

    private static final long serialVersionUID = 4431474721041958212L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "rclbName", value = "人才类别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "rclb", cacheType = "ZJLB")
    @JSONField(name = "rclb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String rclbName;

    @ApiModelProperty(name = "pzdwjbName", value = "批准单位级别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "pzdwjb", cacheType = "DWJB")
    @JSONField(name = "pzdwjb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String pzdwjbName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
