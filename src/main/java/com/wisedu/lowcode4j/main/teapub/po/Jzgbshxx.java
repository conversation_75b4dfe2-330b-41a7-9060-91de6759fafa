package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.main.teapub.vo.JzgbshxxVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgbshxx", description = "博士后信息")
@Comment("博士后信息")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_BIZ_TEACHER_BSHXX", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_BIZ_TEACHER_BSHXX", unique = false)
})
@Table(value = "BIZ_TEACHER_BSHXX")
@SqlToyEntity
@ModelExt(extClass = JzgbshxxVo.class )
@ModelDefine(orderIndex = 4,modelClassify="rsxx", renderType="form")
//end_dynamic_declare
@Data
public class Jzgbshxx extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041957329L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-08 15:38:07")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=1,columnHidden=false,columnXtype="text",formRequired=false)
    private String zgh;

    @ApiModelProperty(name = "bshqgtybh", value = "博士后全国统一编号" ,notes = "2023-11-08 15:38:52")
    @Comment("博士后全国统一编号")
    @Column(value = "bshqgtybh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=2,columnHidden=false,columnXtype="text",formRequired=false)
    private String bshqgtybh;

    @ApiModelProperty(name = "ldz", value = "流动站" ,notes = "2023-11-08 15:39:19")
    @Comment("流动站")
    @Column(value = "ldz",type = ColType.AUTO, width = 900,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=3,columnHidden=false,columnXtype="text",formRequired=false)
    private String ldz;

    @ApiModelProperty(name = "jzrq", value = "进站日期" ,notes = "2023-11-08 15:39:40")
    @Comment("进站日期")
    @Column(value = "jzrq",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=4,columnHidden=false,columnXtype="date-local",formRequired=false)
    private String jzrq;

    @ApiModelProperty(name = "bdrq", value = "报到日期" ,notes = "2023-11-08 15:39:58")
    @Comment("报到日期")
    @Column(value = "bdrq",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=5,columnHidden=false,columnXtype="date-local",formRequired=false)
    private String bdrq;

    @ApiModelProperty(name = "lhpyqy", value = "联合培养企业" ,notes = "2023-11-08 15:40:52")
    @Comment("联合培养企业")
    @Column(value = "lhpyqy",type = ColType.AUTO, width = 1800,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=7,columnHidden=false,columnXtype="text",formRequired=false)
    private String lhpyqy;

    @ApiModelProperty(name = "lhpyfjnrq", value = "联合培养费缴纳日期" ,notes = "2023-11-08 15:41:20")
    @Comment("联合培养费缴纳日期")
    @Column(value = "lhpyfjnrq",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=8,columnHidden=false,columnFormat="yyyy-MM-dd",columnXtype="date-local",formRequired=false)
    private String lhpyfjnrq;

    @ApiModelProperty(name = "lhpyfje", value = "联合培养费金额" ,notes = "2023-11-08 15:41:53")
    @Comment("联合培养费金额")
    @Column(value = "lhpyfje",type = ColType.AUTO, precision = 4, width = 19,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=9,columnHidden=false,columnXtype="number",formRequired=false)
    private String lhpyfje;

    @ApiModelProperty(name = "czrq", value = "出站日期" ,notes = "2023-11-08 15:42:09")
    @Comment("出站日期")
    @Column(value = "czrq",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=10,columnHidden=false,columnXtype="date-local",formRequired=false)
    private String czrq;

    @ApiModelProperty(name = "tzrq", value = "退站日期" ,notes = "2023-11-08 15:42:31")
    @Comment("退站日期")
    @Column(value = "tzrq",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=11,columnHidden=false,columnXtype="date-local",formRequired=false)
    private String tzrq;

    @ApiModelProperty(name = "yqhczrq", value = "延期后出站日期" ,notes = "2023-11-08 15:42:59")
    @Comment("延期后出站日期")
    @Column(value = "yqhczrq",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=12,columnHidden=false,columnXtype="date-local",formRequired=false)
    private String yqhczrq;

    @ApiModelProperty(name = "hzdszgh", value = "合作导师职工号" ,notes = "2023-11-08 15:43:42")
    @Comment("合作导师职工号")
    @Column(value = "hzdszgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=14,columnHidden=false,columnXtype="text",formRequired=false)
    private String hzdszgh;

    @ApiModelProperty(name = "czqx", value = "出站去向" ,notes = "2023-11-08 15:44:01")
    @Comment("出站去向")
    @Column(value = "czqx",type = ColType.AUTO, width = 900,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=15,columnHidden=false,columnXtype="text",formRequired=false)
    private String czqx;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-08 15:44:31")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "zlxs", value = "招录形式" ,notes = "2023-11-08 15:40:29")
    @Comment("招录形式")
    @Column(value = "zlxs",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="BSZLXS",columnReadonly=false,fuzzySearch=false,orderIndex=6,columnHidden=false,columnXtype="select",formRequired=false)
    private String zlxs;

    @ApiModelProperty(name = "bshdqzt", value = "博士后当前状态" ,notes = "2023-11-08 15:43:24")
    @Comment("博士后当前状态")
    @Column(value = "bshdqzt",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="BSHDQZT",columnReadonly=false,fuzzySearch=false,orderIndex=13,columnHidden=false,columnXtype="select",formRequired=false)
    private String bshdqzt;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=16,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
