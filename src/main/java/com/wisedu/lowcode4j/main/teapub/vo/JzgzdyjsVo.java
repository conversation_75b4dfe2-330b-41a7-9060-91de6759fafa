package com.wisedu.lowcode4j.main.teapub.vo;

import com.wisedu.lowcode4j.common.core.constant.*;

import org.sagacity.sqltoy.config.annotation.*;

import io.swagger.annotations.*;

import lombok.Data;
import com.alibaba.fastjson.annotation.*;

import com.wisedu.lowcode4j.main.teapub.po.Jzgzdyjs;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgzdyjsVo", description = "指导研究生Vo")
//end_dynamic_declare
@Data
public class JzgzdyjsVo extends Jzgzdyjs {

    private static final long serialVersionUID = 4431474721041958352L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "yjslxName", value = "研究生类型名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "yjslx", cacheType = "PYFALX")
    @JSONField(name = "yjslx"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String yjslxName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
