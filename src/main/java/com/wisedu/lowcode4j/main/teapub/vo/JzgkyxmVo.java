package com.wisedu.lowcode4j.main.teapub.vo;

import com.wisedu.lowcode4j.common.core.constant.*;

import org.sagacity.sqltoy.config.annotation.*;

import io.swagger.annotations.*;

import lombok.Data;
import com.alibaba.fastjson.annotation.*;

import com.wisedu.lowcode4j.main.teapub.po.Jzgkyxm;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgkyxmVo", description = "科研项目Vo")
//end_dynamic_declare
@Data
public class JzgkyxmVo extends Jzgkyxm {

    private static final long serialVersionUID = 4431474721041958958L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "xmlbName", value = "项目类别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "xmlb", cacheType = "XMFL")
    @JSONField(name = "xmlb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String xmlbName;

    @ApiModelProperty(name = "xmlxName", value = "项目类型名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "xmlx", cacheType = "XMLX")
    @JSONField(name = "xmlx"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String xmlxName;

    @ApiModelProperty(name = "kjxmlbName", value = "科技项目类别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "kjxmlb", cacheType = "KJXMLB")
    @JSONField(name = "kjxmlb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String kjxmlbName;

    @ApiModelProperty(name = "xmjbName", value = "项目级别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "xmjb", cacheType = "JB")
    @JSONField(name = "xmjb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String xmjbName;

    @ApiModelProperty(name = "xmlyName", value = "项目来源名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "xmly", cacheType = "XMLY")
    @JSONField(name = "xmly"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String xmlyName;

    @ApiModelProperty(name = "xmzxztName", value = "项目执行状态名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "xmzxzt", cacheType = "XMZXZT")
    @JSONField(name = "xmzxzt"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String xmzxztName;

    @ApiModelProperty(name = "jsName", value = "角色名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "js", cacheType = "JS")
    @JSONField(name = "js"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String jsName;

    @ApiModelProperty(name = "sfxmfzrName", value = "是否项目负责人名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "sfxmfzr", cacheType = "SFBZ")
    @JSONField(name = "sfxmfzr"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String sfxmfzrName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
