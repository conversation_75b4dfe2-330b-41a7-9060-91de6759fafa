package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.common.core.annotation.ColumnDefine;
import com.wisedu.lowcode4j.common.core.annotation.ModelDefine;
import com.wisedu.lowcode4j.common.core.annotation.ModelExt;
import com.wisedu.lowcode4j.common.core.model.BaseDataModel;
import com.wisedu.lowcode4j.main.teapub.vo.JzgjgxmVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nutz.dao.entity.annotation.*;
import org.sagacity.sqltoy.config.annotation.SqlToyEntity;

import java.util.Date;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgjgxm", description = "教改项目")
@Comment("教改项目")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_biz_teacher_jgxm", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_biz_teacher_jgxm", unique = false)
})
@Table(value = "BIZ_TEACHER_JGXM")
@SqlToyEntity
@ModelExt(extClass = JzgjgxmVo.class )
@ModelDefine(orderIndex = 5,modelClassify="jyjx", renderType="table")
//end_dynamic_declare
@Data
public class Jzgjgxm extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041956829L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-27 17:46:50")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=1,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zgh;

    @ApiModelProperty(name = "xmbh", value = "项目编号" ,notes = "2023-11-27 17:47:23")
    @Comment("项目编号")
    @Column(value = "xmbh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=2,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String xmbh;

    @ApiModelProperty(name = "xmmc", value = "项目名称" ,notes = "2023-11-27 17:47:45")
    @Comment("项目名称")
    @Column(value = "xmmc",type = ColType.AUTO, width = 900,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=3,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String xmmc;

    @ApiModelProperty(name = "xmjb", value = "项目级别" ,notes = "2023-11-27 17:48:28")
    @Comment("项目级别")
    @Column(value = "xmjb",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JB",orderIndex=4,columnReadonly=false,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String xmjb;

    @ApiModelProperty(name = "xmlx", value = "项目类型" ,notes = "2023-11-27 17:50:59")
    @Comment("项目类型")
    @Column(value = "xmlx",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="XMLX",orderIndex=5,columnReadonly=false,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String xmlx;

    @ApiModelProperty(name = "qtdw", value = "牵头单位" ,notes = "2023-11-27 17:51:23")
    @Comment("牵头单位")
    @Column(value = "qtdw",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=6,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String qtdw;

    @ApiModelProperty(name = "ssdw", value = "所属单位" ,notes = "2023-11-27 17:52:31")
    @Comment("所属单位")
    @Column(value = "ssdw",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="biz_department",orderIndex=7,columnReadonly=false,fuzzySearch=false,columnHidden=false,columnXtype="select-tree",formRequired=false,formJsonparam="{\"multiple\":false,\"checkStrictly\":true}")
    private String ssdw;

    @ApiModelProperty(name = "xmfzrxm", value = "项目负责人姓名" ,notes = "2023-11-27 17:54:53")
    @Comment("项目负责人姓名")
    @Column(value = "xmfzrxm",type = ColType.AUTO, width = 240,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=9,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String xmfzrxm;

    @ApiModelProperty(name = "lxrq", value = "立项日期" ,notes = "2023-11-27 17:55:25")
    @Comment("立项日期")
    @Column(value = "lxrq",type = ColType.AUTO, width = 0,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=10,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private Date lxrq;

    @ApiModelProperty(name = "ksrq", value = "开始日期" ,notes = "2023-11-27 17:55:50")
    @Comment("开始日期")
    @Column(value = "ksrq",type = ColType.AUTO, width = 0,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=11,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private Date ksrq;

    @ApiModelProperty(name = "jxrq", value = "结项日期" ,notes = "2023-11-27 17:56:14")
    @Comment("结项日期")
    @Column(value = "jxrq",type = ColType.AUTO, width = 0,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=12,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private Date jxrq;

    @ApiModelProperty(name = "xmjf", value = "项目经费" ,notes = "2023-11-27 17:56:57")
    @Comment("项目经费")
    @Column(value = "xmjf",type = ColType.AUTO, precision = 4, width = 19,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=13,fuzzySearch=false,columnHidden=false,columnXtype="number",formRequired=false)
    private Double xmjf;

    @ApiModelProperty(name = "xmzt", value = "项目状态" ,notes = "2023-11-27 17:58:21")
    @Comment("项目状态")
    @Column(value = "xmzt",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="XMZXZT",orderIndex=14,columnReadonly=false,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String xmzt;

    @ApiModelProperty(name = "xmly", value = "项目来源" ,notes = "2023-11-27 17:59:02")
    @Comment("项目来源")
    @Column(value = "xmly",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="XMLY",orderIndex=15,columnReadonly=false,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String xmly;

    @ApiModelProperty(name = "brcyqk", value = "本人参与情况" ,notes = "2023-11-27 17:59:34")
    @Comment("本人参与情况")
    @Column(value = "brcyqk",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=16,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String brcyqk;

    @ApiModelProperty(name = "bxcyqk", value = "本校参与情况" ,notes = "2023-11-27 18:00:06")
    @Comment("本校参与情况")
    @Column(value = "bxcyqk",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=17,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String bxcyqk;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-27 18:00:21")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "bxfzr", value = "本校负责人" ,notes = "2023-11-28 16:29:11")
    @Comment("本校负责人")
    @Column(value = "bxfzr",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=8,fuzzySearch=false,columnHidden=false,columnJsonparam="{\"dictParams\":{\"label\":\"teacherName\",\"value\":\"zgh\",\"url\":\"/plugins/grsjzx/citeTable/getTeacherTable\",\"dataPath\":\"rows\"},\"columns\":[{\"name\":\"userName\",\"label\":\"姓名\"}, {\"name\":\"zgh\",\"label\":\"工号\"}],\"filterProps\":{\"filterable\":true,\"remote\":true},\"multiple\":false,\"pagerConfig\":{\"pageSizes\":[5, 10, 20, 50, 100],\"background\":false,\"pagerCount\":7,\"hideOnSinglePage\":false,\"pageSize\":5,\"position\":\"right\",\"layouts\":[\"prev\", \"pager\", \"next\", \"sizes\", \"total\"],\"enabled\":true}}",columnXtype="text",formRequired=false)
    private String bxfzr;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=18,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
