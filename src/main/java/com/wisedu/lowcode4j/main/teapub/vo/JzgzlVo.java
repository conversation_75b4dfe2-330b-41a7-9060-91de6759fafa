package com.wisedu.lowcode4j.main.teapub.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.wisedu.lowcode4j.common.core.constant.ApplicationConstant;
import com.wisedu.lowcode4j.common.core.constant.DictCacheType;
import com.wisedu.lowcode4j.main.teapub.po.Jzgzl;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.sagacity.sqltoy.config.annotation.Translate;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgzlVo", description = "专利Vo")
//end_dynamic_declare
@Data
public class JzgzlVo extends Jzgzl {

    private static final long serialVersionUID = 4431474721041958488L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "zllxName", value = "专利类型名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "zllx", cacheType = "ZLLX")
    @JSONField(name = "zllx"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String zllxName;

    @ApiModelProperty(name = "zlztName", value = "专利状态名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "zlzt", cacheType = "ZLFLZT")
    @JSONField(name = "zlzt"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String zlztName;

    @ApiModelProperty(name = "sfbxzlName", value = "是否本校专利名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "sfbxzl", cacheType = "SFBZ")
    @JSONField(name = "sfbxzl"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String sfbxzlName;

    @ApiModelProperty(name = "sfpctzlName", value = "是否PCT专利名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "sfpctzl", cacheType = "SFBZ")
    @JSONField(name = "sfpctzl"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String sfpctzlName;

    @ApiModelProperty(name = "zlgjName", value = "专利国家名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "zlgj", cacheType = "GJHDQ")
    @JSONField(name = "zlgj"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String zlgjName;

    @ApiModelProperty(name = "jsName", value = "角色名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "js", cacheType = "JS")
    @JSONField(name = "js"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String jsName;

    @ApiModelProperty(name = "sfbxdyfmrName", value = "是否本校第一发明人名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "sfbxdyfmr", cacheType = "SFBZ")
    @JSONField(name = "sfbxdyfmr"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String sfbxdyfmrName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
