package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.main.teapub.vo.JzggwdjprjlVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzggwdjprjl", description = "岗位等级聘任记录")
@Comment("岗位等级聘任记录")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_BIZ_TEACHER_GWDJPRJL", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_BIZ_TEACHER_GWDJPRJL", unique = false)
})
@Table(value = "BIZ_TEACHER_GWDJPRJL")
@SqlToyEntity
@ModelExt(extClass = JzggwdjprjlVo.class )
@ModelDefine(orderIndex = 19,modelClassify="rsxx", renderType="table")
//end_dynamic_declare
@Data
public class Jzggwdjprjl extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041952936L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-09 11:05:34")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=1,columnHidden=false,columnXtype="text",formRequired=false)
    private String zgh;

    @ApiModelProperty(name = "prqsrq", value = "聘任起始日期" ,notes = "2023-11-09 11:10:30")
    @Comment("聘任起始日期")
    @Column(value = "prqsrq",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=5,columnHidden=false,columnXtype="date-local",formRequired=false)
    private String prqsrq;

    @ApiModelProperty(name = "przzrq", value = "聘任终止日期" ,notes = "2023-11-09 11:11:00")
    @Comment("聘任终止日期")
    @Column(value = "przzrq",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=6,columnHidden=false,columnXtype="date-local",formRequired=false)
    private String przzrq;

    @ApiModelProperty(name = "pywh", value = "聘用文号" ,notes = "2023-11-09 11:11:23")
    @Comment("聘用文号")
    @Column(value = "pywh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=7,columnHidden=false,columnXtype="text",formRequired=false)
    private String pywh;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-09 14:37:55")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "gwlx", value = "岗位类型" ,notes = "2023-11-09 11:06:13")
    @Comment("岗位类型")
    @Column(value = "gwlx",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="GWLX",columnReadonly=false,fuzzySearch=false,orderIndex=2,columnHidden=false,columnXtype="select",formRequired=false)
    private String gwlx;

    @ApiModelProperty(name = "gwdj", value = "岗位等级" ,notes = "2023-11-09 11:07:01")
    @Comment("岗位等级")
    @Column(value = "gwdj",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SYDWZYJSRYGWDJ",columnReadonly=false,fuzzySearch=false,orderIndex=3,columnHidden=false,columnXtype="select",formRequired=false)
    private String gwdj;

    @ApiModelProperty(name = "spdw", value = "受聘单位" ,notes = "2023-11-09 11:09:21")
    @Comment("受聘单位")
    @Column(value = "spdw",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="biz_department",columnReadonly=false,fuzzySearch=false,orderIndex=4,columnHidden=false,columnXtype="select",formRequired=false)
    private String spdw;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=8,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
