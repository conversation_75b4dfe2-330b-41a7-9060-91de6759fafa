package com.wisedu.lowcode4j.main.teapub.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.wisedu.lowcode4j.common.core.constant.ApplicationConstant;
import com.wisedu.lowcode4j.common.core.constant.DictCacheType;
import com.wisedu.lowcode4j.main.teapub.po.Jzglw;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.sagacity.sqltoy.config.annotation.Translate;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzglwVo", description = "论文Vo")
//end_dynamic_declare
@Data
public class JzglwVo extends Jzglw {

    private static final long serialVersionUID = 4431474721041952426L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "lwlxName", value = "论文类型名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "lwlx", cacheType = "LWFBLX")
    @JSONField(name = "lwlx"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String lwlxName;

    @ApiModelProperty(name = "lwfblxName", value = "论文发表类型名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "lwfblx", cacheType = "LWFBLX")
    @JSONField(name = "lwfblx"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String lwfblxName;

    @ApiModelProperty(name = "hygjdqName", value = "会议国家地区名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "hygjdq", cacheType = "GJHDQ")
    @JSONField(name = "hygjdq"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String hygjdqName;

    @ApiModelProperty(name = "kwjbName", value = "刊物级别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "kwjb", cacheType = "KWJB")
    @JSONField(name = "kwjb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String kwjbName;

    @ApiModelProperty(name = "yzName", value = "语种名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "yz", cacheType = "YZ")
    @JSONField(name = "yz"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String yzName;

    @ApiModelProperty(name = "jsName", value = "角色名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "js", cacheType = "JS")
    @JSONField(name = "js"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String jsName;

    @ApiModelProperty(name = "sftxzzName", value = "是否通讯作者名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "sftxzz", cacheType = "SFBZ")
    @JSONField(name = "sftxzz"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String sftxzzName;

    @ApiModelProperty(name = "sfdyzzName", value = "是否第一作者名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "sfdyzz", cacheType = "SFBZ")
    @JSONField(name = "sfdyzz"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String sfdyzzName;

    @ApiModelProperty(name = "sfbxdyzzName", value = "是否本校第一作者名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "sfbxdyzz", cacheType = "SFBZ")
    @JSONField(name = "sfbxdyzz"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String sfbxdyzzName;

    @ApiModelProperty(name = "hydjName", value = "会议等级名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "hydj", cacheType = "XSHYDJ")
    @JSONField(name = "hydj"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String hydjName;

    @ApiModelProperty(name = "qklbName", value = "期刊类别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "qklb", cacheType = "XKFL")
    @JSONField(name = "qklb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String qklbName;

    @ApiModelProperty(name = "jsfqName", value = "检索分区名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "jsfq", cacheType = "JSFQ")
    @JSONField(name = "jsfq"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String jsfqName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
