package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.main.teapub.vo.JzgkhjlVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgkhjl", description = "考核记录")
@Comment("考核记录")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_BIZ_TEACHER_KHJL", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_BIZ_TEACHER_KHJL", unique = false)
})
@Table(value = "BIZ_TEACHER_KHJL")
@SqlToyEntity
@ModelExt(extClass = JzgkhjlVo.class )
@ModelDefine(orderIndex = 12,modelClassify="rsxx", renderType="table")
//end_dynamic_declare
@Data
public class Jzgkhjl extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041953458L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-09 15:23:52")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=1,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zgh;

    @ApiModelProperty(name = "khmc", value = "考核名称" ,notes = "2023-11-09 15:25:18")
    @Comment("考核名称")
    @Column(value = "khmc",type = ColType.AUTO, width = 600,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=3,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String khmc;

    @ApiModelProperty(name = "khnf", value = "考核年份" ,notes = "2023-11-09 15:26:32")
    @Comment("考核年份")
    @Column(value = "khnf",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=5,fuzzySearch=false,columnHidden=false,columnFormat = "yyyy",columnXtype = "date-local",columnJsonparam="{\"type\":\"yyyy\",\"format\":\"yyyy\",\"value-format\":\"yyyy\",\"pattern\":\"yyyy\"}",formRequired=false)
    private String khnf;

    @ApiModelProperty(name = "khrq", value = "考核日期" ,notes = "2023-11-09 15:27:55")
    @Comment("考核日期")
    @Column(value = "khrq",type = ColType.AUTO, width = 0,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=7,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private Date khrq;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-09 15:28:09")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=8,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "khdw", value = "考核单位" ,notes = "2023-11-09 15:24:52")
    @Comment("考核单位")
    @Column(value = "khdw",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="biz_department",columnReadonly=false,orderIndex=2,fuzzySearch=false,columnHidden=false,columnXtype="select-tree",formRequired=false)
    private String khdw;

    @ApiModelProperty(name = "khlb", value = "考核类别" ,notes = "2023-11-09 15:26:08")
    @Comment("考核类别")
    @Column(value = "khlb",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="KHLB",columnReadonly=false,orderIndex=4,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String khlb;

    @ApiModelProperty(name = "khjl", value = "考核结论" ,notes = "2023-11-09 15:27:05")
    @Comment("考核结论")
    @Column(value = "khjl",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="KHJL",columnReadonly=false,orderIndex=6,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String khjl;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=9,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
