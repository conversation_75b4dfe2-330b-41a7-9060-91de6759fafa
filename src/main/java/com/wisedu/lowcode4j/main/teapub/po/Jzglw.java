package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.common.core.annotation.ColumnDefine;
import com.wisedu.lowcode4j.common.core.annotation.ModelDefine;
import com.wisedu.lowcode4j.common.core.annotation.ModelExt;
import com.wisedu.lowcode4j.common.core.model.BaseDataModel;
import com.wisedu.lowcode4j.main.teapub.vo.JzglwVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nutz.dao.entity.annotation.*;
import org.sagacity.sqltoy.config.annotation.SqlToyEntity;

import java.util.Date;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzglw", description = "论文")
@Comment("论文")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_biz_teacher_lw", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_biz_teacher_lw", unique = false)
})
@Table(value = "BIZ_TEACHER_LW")
@SqlToyEntity
@ModelExt(extClass = JzglwVo.class )
@ModelDefine(orderIndex = 3,modelClassify="kyxx", renderType="table")
//end_dynamic_declare
@Data
public class Jzglw extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041956440L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-28 09:01:52")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=1,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zgh;

    @ApiModelProperty(name = "lwbh", value = "论文编码" ,notes = "2023-11-28 09:02:14")
    @Comment("论文编码")
    @Column(value = "lwbh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=2,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String lwbh;

    @ApiModelProperty(name = "lwzwmc", value = "论文中文名称" ,notes = "2023-11-28 09:03:17")
    @Comment("论文中文名称")
    @Column(value = "lwzwmc",type = ColType.AUTO, width = 360,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=3,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String lwzwmc;

    @ApiModelProperty(name = "lwywmc", value = "论文英文名称" ,notes = "2023-11-28 09:03:40")
    @Comment("论文英文名称")
    @Column(value = "lwywmc",type = ColType.AUTO, width = 600,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=4,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String lwywmc;

    @ApiModelProperty(name = "lwlx", value = "论文类型" ,notes = "2023-11-28 09:04:38")
    @Comment("论文类型")
    @Column(value = "lwlx",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="LWFBLX",columnReadonly=false,orderIndex=5,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String lwlx;

    @ApiModelProperty(name = "sllb", value = "收录类别" ,notes = "2023-11-28 09:05:00")
    @Comment("收录类别")
    @Column(value = "sllb",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=6,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String sllb;

    @ApiModelProperty(name = "txzz", value = "通讯作者" ,notes = "2023-11-28 09:05:37")
    @Comment("通讯作者")
    @Column(value = "txzz",type = ColType.AUTO, width = 240,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=8,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String txzz;

    @ApiModelProperty(name = "xxsm", value = "学校署名" ,notes = "2023-11-28 09:05:57")
    @Comment("学校署名")
    @Column(value = "xxsm",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=9,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String xxsm;

    @ApiModelProperty(name = "lwfblx", value = "论文发表类型" ,notes = "2023-11-28 09:06:51")
    @Comment("论文发表类型")
    @Column(value = "lwfblx",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="LWFBLX",columnReadonly=false,orderIndex=10,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String lwfblx;

    @ApiModelProperty(name = "hymc", value = "会议名称" ,notes = "2023-11-28 09:09:56")
    @Comment("会议名称")
    @Column(value = "hymc",type = ColType.AUTO, width = 600,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=11,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String hymc;

    @ApiModelProperty(name = "hyywmc", value = "会议英文名称" ,notes = "2023-11-28 09:10:23")
    @Comment("会议英文名称")
    @Column(value = "hyywmc",type = ColType.AUTO, width = 600,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=12,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String hyywmc;

    @ApiModelProperty(name = "hydd", value = "会议地点" ,notes = "2023-11-28 09:10:42")
    @Comment("会议地点")
    @Column(value = "hydd",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=13,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String hydd;

    @ApiModelProperty(name = "hygjdq", value = "会议国家地区" ,notes = "2023-11-28 09:12:24")
    @Comment("会议国家地区")
    @Column(value = "hygjdq",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="GJHDQ",columnReadonly=false,orderIndex=14,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String hygjdq;

    @ApiModelProperty(name = "hyzbdw", value = "会议主办单位" ,notes = "2023-11-28 09:12:51")
    @Comment("会议主办单位")
    @Column(value = "hyzbdw",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=15,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String hyzbdw;

    @ApiModelProperty(name = "lwjmc", value = "论文集名称" ,notes = "2023-11-28 09:14:04")
    @Comment("论文集名称")
    @Column(value = "lwjmc",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=17,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String lwjmc;

    @ApiModelProperty(name = "kwjb", value = "刊物级别" ,notes = "2023-11-28 09:14:44")
    @Comment("刊物级别")
    @Column(value = "kwjb",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="KWJB",columnReadonly=false,orderIndex=18,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String kwjb;

    @ApiModelProperty(name = "kwmc", value = "刊物名称" ,notes = "2023-11-28 09:16:37")
    @Comment("刊物名称")
    @Column(value = "kwmc",type = ColType.AUTO, width = 600,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=19,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String kwmc;

    @ApiModelProperty(name = "issnh", value = "issn号" ,notes = "2023-11-28 09:17:14")
    @Comment("issn号")
    @Column(value = "issnh",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=20,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String issnh;

    @ApiModelProperty(name = "cnh", value = "cn号" ,notes = "2023-11-28 09:17:31")
    @Comment("cn号")
    @Column(value = "cnh",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=21,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String cnh;

    @ApiModelProperty(name = "doi", value = "doi" ,notes = "2023-11-28 09:17:50")
    @Comment("doi")
    @Column(value = "doi",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=22,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String doi;

    @ApiModelProperty(name = "nh", value = "年号" ,notes = "2023-11-28 09:18:06")
    @Comment("年号")
    @Column(value = "nh",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=23,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String nh;

    @ApiModelProperty(name = "jqy", value = "卷期页" ,notes = "2023-11-28 09:18:29")
    @Comment("卷期页")
    @Column(value = "jqy",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=24,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String jqy;

    @ApiModelProperty(name = "lwzs", value = "论文字数" ,notes = "2023-11-28 09:21:15")
    @Comment("论文字数")
    @Column(value = "lwzs",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=27,fuzzySearch=false,columnHidden=false,columnXtype="number",formRequired=false)
    private Long lwzs;

    @ApiModelProperty(name = "yz", value = "语种" ,notes = "2023-11-28 09:21:53")
    @Comment("语种")
    @Column(value = "yz",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="YZ",columnReadonly=false,orderIndex=28,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String yz;

    @ApiModelProperty(name = "fbrq", value = "发表日期" ,notes = "2023-11-28 09:22:20")
    @Comment("发表日期")
    @Column(value = "fbrq",type = ColType.AUTO, width = 0,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=29,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private Date fbrq;

    @ApiModelProperty(name = "lwrdjb", value = "论文认定级别" ,notes = "2023-11-28 09:22:50")
    @Comment("论文认定级别")
    @Column(value = "lwrdjb",type = ColType.AUTO, width = 150,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=30,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String lwrdjb;

    @ApiModelProperty(name = "zy", value = "摘要" ,notes = "2023-11-28 09:23:16")
    @Comment("摘要")
    @Column(value = "zy",type = ColType.TEXT, width = 4000,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=31,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zy;

    @ApiModelProperty(name = "gjc", value = "关键词" ,notes = "2023-11-28 09:23:41")
    @Comment("关键词")
    @Column(value = "gjc",type = ColType.TEXT, width = 4000,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=32,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String gjc;

    @ApiModelProperty(name = "js", value = "角色" ,notes = "2023-11-28 09:24:14")
    @Comment("角色")
    @Column(value = "js",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JS",columnReadonly=false,orderIndex=33,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String js;

    @ApiModelProperty(name = "smsx", value = "署名顺序" ,notes = "2023-11-28 09:24:52")
    @Comment("署名顺序")
    @Column(value = "smsx",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=34,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String smsx;

    @ApiModelProperty(name = "gxl", value = "贡献率" ,notes = "2023-11-28 09:25:36")
    @Comment("贡献率")
    @Column(value = "gxl",type = ColType.AUTO, precision = 4, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=35,fuzzySearch=false,columnHidden=false,columnXtype="number",formRequired=false)
    private Double gxl;

    @ApiModelProperty(name = "sftxzz", value = "是否通讯作者" ,notes = "2023-11-28 09:26:09")
    @Comment("是否通讯作者")
    @Column(value = "sftxzz",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SFBZ",columnReadonly=false,orderIndex=36,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String sftxzz;

    @ApiModelProperty(name = "sfdyzz", value = "是否第一作者" ,notes = "2023-11-28 09:26:53")
    @Comment("是否第一作者")
    @Column(value = "sfdyzz",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SFBZ",columnReadonly=false,orderIndex=37,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String sfdyzz;

    @ApiModelProperty(name = "sfbxdyzz", value = "是否本校第一作者" ,notes = "2023-11-28 09:27:39")
    @Comment("是否本校第一作者")
    @Column(value = "sfbxdyzz",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SFBZ",columnReadonly=false,orderIndex=38,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String sfbxdyzz;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-28 09:28:04")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=39,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "dyzz", value = "第一作者" ,notes = "2023-11-28 09:05:19")
    @Comment("第一作者")
    @Column(value = "dyzz",type = ColType.AUTO, width = 240,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=7,fuzzySearch=false,columnHidden=false,columnJsonparam="{}",columnXtype="text",formRequired=false)
    private String dyzz;

    @ApiModelProperty(name = "hydj", value = "会议等级" ,notes = "2023-11-28 09:13:38")
    @Comment("会议等级")
    @Column(value = "hydj",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="XSHYDJ",columnReadonly=false,orderIndex=16,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String hydj;

    @ApiModelProperty(name = "qklb", value = "期刊类别" ,notes = "2023-11-28 09:20:47")
    @Comment("期刊类别")
    @Column(value = "qklb",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="XKFL",columnReadonly=false,orderIndex=25,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String qklb;

    @ApiModelProperty(name = "jsfq", value = "检索分区" ,notes = "2024-05-30 17:46:47")
    @Comment("检索分区")
    @Column(value = "jsfq",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JSFQ",columnReadonly=false,orderIndex=26,fuzzySearch=false,columnHidden=false,columnXtype="select-tree",formRequired=false,formJsonparam="{\"multiple\":false,\"checkStrictly\":true}")
    private String jsfq;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=40,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
