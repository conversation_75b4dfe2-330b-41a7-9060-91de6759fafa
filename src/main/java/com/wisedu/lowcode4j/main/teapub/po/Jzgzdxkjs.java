package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.main.teapub.vo.JzgzdxkjsVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgzdxkjs", description = "指导学科竞赛")
@Comment("指导学科竞赛")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_biz_teacher_zdxkjs", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_biz_teacher_zdxkjs", unique = false)
})
@Table(value = "BIZ_TEACHER_ZDXKJS")
@SqlToyEntity
@ModelExt(extClass = JzgzdxkjsVo.class )
@ModelDefine(orderIndex = 9,modelClassify="jyjx", renderType="table")
//end_dynamic_declare
@Data
public class Jzgzdxkjs extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041952871L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-27 18:41:41")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=1,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zgh;

    @ApiModelProperty(name = "jsbh", value = "竞赛编号" ,notes = "2023-11-27 18:43:02")
    @Comment("竞赛编号")
    @Column(value = "jsbh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=2,fuzzySearch=false,columnHidden=false,columnJsonparam="{\"dictParams\":{\"label\":\"showName\",\"value\":\"userId\",\"url\":\"/admin/upms/postMange/user-list\",\"dataPath\":\"rows\"},\"columns\":[{\"name\":\"showName\",\"label\":\"姓名\"}, {\"name\":\"userId\",\"label\":\"工号\"}],\"filterProps\":{\"filterable\":true,\"remote\":true},\"multiple\":false,\"pagerConfig\":{\"pageSizes\":[5, 10, 20, 50, 100],\"background\":false,\"pagerCount\":7,\"hideOnSinglePage\":false,\"pageSize\":5,\"position\":\"right\",\"layouts\":[\"prev\", \"pager\", \"next\", \"sizes\", \"total\"],\"enabled\":true}}",columnXtype="text",formRequired=false)
    private String jsbh;

    @ApiModelProperty(name = "jsmc", value = "竞赛名称" ,notes = "2023-11-27 18:43:26")
    @Comment("竞赛名称")
    @Column(value = "jsmc",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=3,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String jsmc;

    @ApiModelProperty(name = "jsxk", value = "竞赛学科" ,notes = "2023-11-27 18:44:49")
    @Comment("竞赛学科")
    @Column(value = "jsxk",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="XWSYHRCPYXKML",columnReadonly=false,orderIndex=4,fuzzySearch=false,columnHidden=false,columnXtype="select-tree",formRequired=false,formJsonparam="{\"multiple\":false,\"checkStrictly\":true}")
    private String jsxk;

    @ApiModelProperty(name = "jsjc", value = "竞赛界次" ,notes = "2023-11-27 18:45:18")
    @Comment("竞赛界次")
    @Column(value = "jsjc",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=5,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String jsjc;

    @ApiModelProperty(name = "jsjb", value = "竞赛级别" ,notes = "2023-11-27 18:45:59")
    @Comment("竞赛级别")
    @Column(value = "jsjb",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JSJB",columnReadonly=false,orderIndex=6,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String jsjb;

    @ApiModelProperty(name = "jsjf", value = "竞赛经费" ,notes = "2023-11-27 18:46:28")
    @Comment("竞赛经费")
    @Column(value = "jsjf",type = ColType.AUTO, precision = 4, width = 19,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=7,fuzzySearch=false,columnHidden=false,columnXtype="number",formRequired=false)
    private Double jsjf;

    @ApiModelProperty(name = "jsksrq", value = "竞赛开始日期" ,notes = "2023-11-27 18:46:55")
    @Comment("竞赛开始日期")
    @Column(value = "jsksrq",type = ColType.AUTO, width = 0,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=8,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private Date jsksrq;

    @ApiModelProperty(name = "jsjsrq", value = "竞赛结束日期" ,notes = "2023-11-27 18:47:23")
    @Comment("竞赛结束日期")
    @Column(value = "jsjsrq",type = ColType.AUTO, width = 0,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=9,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private Date jsjsrq;

    @ApiModelProperty(name = "tdbh", value = "团队编号" ,notes = "2023-11-27 18:48:10")
    @Comment("团队编号")
    @Column(value = "tdbh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=10,fuzzySearch=false,columnHidden=false,columnJsonparam="{\"dictParams\":{\"label\":\"showName\",\"value\":\"userId\",\"url\":\"/admin/upms/postMange/user-list\",\"dataPath\":\"rows\"},\"columns\":[{\"name\":\"showName\",\"label\":\"姓名\"}, {\"name\":\"userId\",\"label\":\"工号\"}],\"filterProps\":{\"filterable\":true,\"remote\":true},\"multiple\":false,\"pagerConfig\":{\"pageSizes\":[5, 10, 20, 50, 100],\"background\":false,\"pagerCount\":7,\"hideOnSinglePage\":false,\"pageSize\":5,\"position\":\"right\",\"layouts\":[\"prev\", \"pager\", \"next\", \"sizes\", \"total\"],\"enabled\":true}}",columnXtype="text",formRequired=false)
    private String tdbh;

    @ApiModelProperty(name = "tdmc", value = "团队名称" ,notes = "2023-11-27 18:48:38")
    @Comment("团队名称")
    @Column(value = "tdmc",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=11,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String tdmc;

    @ApiModelProperty(name = "zdgzl", value = "指导工作量" ,notes = "2023-11-27 18:49:00")
    @Comment("指导工作量")
    @Column(value = "zdgzl",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=12,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zdgzl;

    @ApiModelProperty(name = "jscj", value = "竞赛成绩" ,notes = "2023-11-27 18:49:30")
    @Comment("竞赛成绩")
    @Column(value = "jscj",type = ColType.AUTO, precision = 2, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=13,fuzzySearch=false,columnHidden=false,columnXtype="number",formRequired=false)
    private Double jscj;

    @ApiModelProperty(name = "jljb", value = "奖励级别" ,notes = "2023-11-27 18:50:00")
    @Comment("奖励级别")
    @Column(value = "jljb",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JB",columnReadonly=false,orderIndex=14,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String jljb;

    @ApiModelProperty(name = "jldj", value = "奖励等级" ,notes = "2023-11-27 18:50:23")
    @Comment("奖励等级")
    @Column(value = "jldj",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JLDJ",columnReadonly=false,orderIndex=15,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String jldj;

    @ApiModelProperty(name = "hjmc", value = "获奖名次" ,notes = "2023-11-27 18:50:42")
    @Comment("获奖名次")
    @Column(value = "hjmc",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=16,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String hjmc;

    @ApiModelProperty(name = "hjrq", value = "获奖日期" ,notes = "2023-11-27 18:51:05")
    @Comment("获奖日期")
    @Column(value = "hjrq",type = ColType.AUTO, width = 0,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=17,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private Date hjrq;

    @ApiModelProperty(name = "hjzsbh", value = "获奖证书编号" ,notes = "2023-11-27 18:51:40")
    @Comment("获奖证书编号")
    @Column(value = "hjzsbh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=18,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String hjzsbh;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-27 18:51:54")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=19,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=20,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
