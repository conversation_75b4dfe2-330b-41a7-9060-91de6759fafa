package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.main.teapub.vo.JzgccxxVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgccxx", description = "惩处信息")
@Comment("惩处信息")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_BIZ_TEACHER_CCXX", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_BIZ_TEACHER_CCXX", unique = false)
})
@Table(value = "BIZ_TEACHER_CCXX")
@SqlToyEntity
@ModelExt(extClass = JzgccxxVo.class )
@ModelDefine(orderIndex = 11,modelClassify="rsxx", renderType="table")
//end_dynamic_declare
@Data
public class Jzgccxx extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041955429L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-09 15:34:07")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=1,columnHidden=false,columnXtype="text",formRequired=false)
    private String zgh;

    @ApiModelProperty(name = "ccyy", value = "惩处原因" ,notes = "2023-11-09 15:35:43")
    @Comment("惩处原因")
    @Column(value = "ccyy",type = ColType.AUTO, width = 540,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=3,columnHidden=false,columnXtype="text",formRequired=false)
    private String ccyy;

    @ApiModelProperty(name = "ccnr", value = "惩处内容" ,notes = "2023-11-09 15:36:04")
    @Comment("惩处内容")
    @Column(value = "ccnr",type = ColType.AUTO, width = 540,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=4,columnHidden=false,columnXtype="text",formRequired=false)
    private String ccnr;

    @ApiModelProperty(name = "ccdw", value = "惩处单位" ,notes = "2023-11-09 15:36:23")
    @Comment("惩处单位")
    @Column(value = "ccdw",type = ColType.AUTO, width = 540,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=5,columnHidden=false,columnXtype="text",formRequired=false)
    private String ccdw;

    @ApiModelProperty(name = "ccwh", value = "惩处文号" ,notes = "2023-11-09 15:36:53")
    @Comment("惩处文号")
    @Column(value = "ccwh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=6,columnHidden=false,columnXtype="text",formRequired=false)
    private String ccwh;

    @ApiModelProperty(name = "ccrq", value = "惩处日期" ,notes = "2023-11-09 15:38:12")
    @Comment("惩处日期")
    @Column(value = "ccrq",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=7,columnHidden=false,columnXtype="date-local",formRequired=false)
    private String ccrq;

    @ApiModelProperty(name = "cfcxrq", value = "处分撤销日期" ,notes = "2023-11-09 15:39:40")
    @Comment("处分撤销日期")
    @Column(value = "cfcxrq",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=8,columnHidden=false,columnXtype="date-local",formRequired=false)
    private String cfcxrq;

    @ApiModelProperty(name = "cfcxwh", value = "处分撤销文号" ,notes = "2023-11-09 15:40:11")
    @Comment("处分撤销文号")
    @Column(value = "cfcxwh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=9,columnHidden=false,columnXtype="text",formRequired=false)
    private String cfcxwh;

    @ApiModelProperty(name = "cfcxyy", value = "处分撤销原因" ,notes = "2023-11-09 15:40:31")
    @Comment("处分撤销原因")
    @Column(value = "cfcxyy",type = ColType.AUTO, width = 540,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=10,columnHidden=false,columnXtype="text",formRequired=false)
    private String cfcxyy;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-09 15:40:45")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "jlcf", value = "纪律处分" ,notes = "2023-11-09 15:35:12")
    @Comment("纪律处分")
    @Column(value = "jlcf",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JLCF",columnReadonly=false,fuzzySearch=false,orderIndex=2,columnHidden=false,columnXtype="select",formRequired=false)
    private String jlcf;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=11,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
