package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.main.teapub.vo.JzgjxbshjxxVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgjxbshjxx", description = "教学比赛获奖信息")
@Comment("教学比赛获奖信息")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_biz_teacher_jxbshjxx", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_biz_teacher_jxbshjxx", unique = false)
})
@Table(value = "BIZ_TEACHER_JXBSHJXX")
@SqlToyEntity
@ModelExt(extClass = JzgjxbshjxxVo.class )
@ModelDefine(orderIndex = 8,modelClassify="jyjx", renderType="table")
//end_dynamic_declare
@Data
public class Jzgjxbshjxx extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041952979L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-27 18:29:46")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=1,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zgh;

    @ApiModelProperty(name = "bsmc", value = "比赛名称" ,notes = "2023-11-27 18:30:13")
    @Comment("比赛名称")
    @Column(value = "bsmc",type = ColType.AUTO, width = 540,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=2,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String bsmc;

    @ApiModelProperty(name = "jljb", value = "奖励级别" ,notes = "2023-11-27 18:30:43")
    @Comment("奖励级别")
    @Column(value = "jljb",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JB",orderIndex=3,columnReadonly=false,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String jljb;

    @ApiModelProperty(name = "jldj", value = "奖励等级" ,notes = "2023-11-27 18:31:07")
    @Comment("奖励等级")
    @Column(value = "jldj",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JLDJ",orderIndex=4,columnReadonly=false,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String jldj;

    @ApiModelProperty(name = "brpx", value = "本人排序" ,notes = "2023-11-27 18:38:36")
    @Comment("本人排序")
    @Column(value = "brpx",type = ColType.AUTO, width = 540,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=5,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String brpx;

    @ApiModelProperty(name = "hjrq", value = "获奖日期" ,notes = "2023-11-27 18:39:04")
    @Comment("获奖日期")
    @Column(value = "hjrq",type = ColType.AUTO, width = 0,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=6,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private Date hjrq;

    @ApiModelProperty(name = "bjdw", value = "颁奖单位" ,notes = "2023-11-27 18:39:26")
    @Comment("颁奖单位")
    @Column(value = "bjdw",type = ColType.AUTO, width = 540,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=7,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String bjdw;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-27 18:39:39")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=8,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
