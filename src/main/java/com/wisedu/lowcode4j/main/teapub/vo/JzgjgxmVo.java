package com.wisedu.lowcode4j.main.teapub.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.wisedu.lowcode4j.common.core.constant.ApplicationConstant;
import com.wisedu.lowcode4j.common.core.constant.DictCacheType;
import com.wisedu.lowcode4j.main.teapub.po.Jzgjgxm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.sagacity.sqltoy.config.annotation.Translate;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgjgxmVo", description = "教改项目Vo")
//end_dynamic_declare
@Data
public class JzgjgxmVo extends Jzgjgxm {

    private static final long serialVersionUID = 4431474721041951983L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "xmjbName", value = "项目级别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "xmjb", cacheType = "JB")
    @JSONField(name = "xmjb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String xmjbName;

    @ApiModelProperty(name = "xmlxName", value = "项目类型名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "xmlx", cacheType = "XMLX")
    @JSONField(name = "xmlx"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String xmlxName;

    @ApiModelProperty(name = "ssdwName", value = "所属单位名称")
    @Translate(cacheName = "biz_department", keyField = "ssdw", cacheType = "")
    @JSONField(name = "ssdw"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String ssdwName;

    @ApiModelProperty(name = "xmztName", value = "项目状态名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "xmzt", cacheType = "XMZXZT")
    @JSONField(name = "xmzt"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String xmztName;

    @ApiModelProperty(name = "xmlyName", value = "项目来源名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "xmly", cacheType = "XMLY")
    @JSONField(name = "xmly"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String xmlyName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
