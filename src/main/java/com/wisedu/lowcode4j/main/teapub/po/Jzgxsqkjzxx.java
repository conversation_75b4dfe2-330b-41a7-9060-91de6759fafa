package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.main.teapub.vo.JzgxsqkjzxxVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgxsqkjzxx", description = "学术期刊兼职信息")
@Comment("学术期刊兼职信息")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_BIZ_TEACHERXSQKJZXX", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_BIZ_TEACHER_XSQKJZXX", unique = false)
})
@Table(value = "BIZ_TEACHER_XSQKJZXX")
@SqlToyEntity
@ModelExt(extClass = JzgxsqkjzxxVo.class )
@ModelDefine(orderIndex = 7,modelClassify="rsxx", renderType="table")
//end_dynamic_declare
@Data
public class Jzgxsqkjzxx extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041959456L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-08 18:56:31")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=1,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zgh;

    @ApiModelProperty(name = "kwmc", value = "刊物名称" ,notes = "2023-11-08 18:56:53")
    @Comment("刊物名称")
    @Column(value = "kwmc",type = ColType.AUTO, width = 600,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=2,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String kwmc;

    @ApiModelProperty(name = "issn", value = "ISSN号" ,notes = "2023-11-08 18:58:13")
    @Comment("ISSN号")
    @Column(value = "issn",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=5,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String issn;

    @ApiModelProperty(name = "cn", value = "CN号" ,notes = "2023-11-08 18:58:30")
    @Comment("CN号")
    @Column(value = "cn",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=6,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String cn;

    @ApiModelProperty(name = "rzqsrq", value = "任职起始日期" ,notes = "2023-11-08 18:59:05")
    @Comment("任职起始日期")
    @Column(value = "rzqsrq",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=8,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private String rzqsrq;

    @ApiModelProperty(name = "rzjsrq", value = "任职结束日期" ,notes = "2023-11-08 18:59:24")
    @Comment("任职结束日期")
    @Column(value = "rzjsrq",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=9,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private String rzjsrq;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-08 18:59:37")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=10,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "kwjb", value = "刊物级别" ,notes = "2023-11-08 18:57:12")
    @Comment("刊物级别")
    @Column(value = "kwjb",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="KWJB",columnReadonly=false,orderIndex=3,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String kwjb;

    @ApiModelProperty(name = "gjdq", value = "国家地区" ,notes = "2023-11-08 18:57:47")
    @Comment("国家地区")
    @Column(value = "gjdq",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="GJHDQ",columnReadonly=false,orderIndex=4,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String gjdq;

    @ApiModelProperty(name = "dzzw", value = "担任职位" ,notes = "2023-11-08 18:58:46")
    @Comment("担任职位")
    @Column(value = "dzzw",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JS",columnReadonly=false,orderIndex=7,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String dzzw;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=11,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
