package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.main.teapub.vo.JzgxsttjzxxVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgxsttjzxx", description = "学术团体兼职信息")
@Comment("学术团体兼职信息")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_BIZ_TEACHER_XSTTJZXX", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_BIZ_TEACHER_XSTTJZXX", unique = false)
})
@Table(value = "BIZ_TEACHER_XSTTJZXX")
@SqlToyEntity
@ModelExt(extClass = JzgxsttjzxxVo.class )
@ModelDefine(orderIndex = 8,modelClassify="rsxx", renderType="table")
//end_dynamic_declare
@Data
public class Jzgxsttjzxx extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041954808L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-09 10:09:37")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=1,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zgh;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-09 10:09:50")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=12,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "xsttmc", value = "学术团体名称" ,notes = "2023-11-09 10:10:28")
    @Comment("学术团体名称")
    @Column(value = "xsttmc",type = ColType.AUTO, width = 540,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=2,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String xsttmc;

    @ApiModelProperty(name = "lshzgdwmc", value = "隶属或主管单位名称" ,notes = "2023-11-09 10:13:21")
    @Comment("隶属或主管单位名称")
    @Column(value = "lshzgdwmc",type = ColType.AUTO, width = 540,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=5,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String lshzgdwmc;

    @ApiModelProperty(name = "xsjzzw", value = "学术兼职职务" ,notes = "2023-11-09 10:13:36")
    @Comment("学术兼职职务")
    @Column(value = "xsjzzw",type = ColType.AUTO, width = 270,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=6,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String xsjzzw;

    @ApiModelProperty(name = "rzzzrq", value = "任职终止日期" ,notes = "2023-11-09 10:14:35")
    @Comment("任职终止日期")
    @Column(value = "rzzzrq",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=9,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private String rzzzrq;

    @ApiModelProperty(name = "rzqsrq", value = "任职起始日期" ,notes = "2023-11-09 10:14:13")
    @Comment("任职起始日期")
    @Column(value = "rzqsrq",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=8,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private String rzqsrq;

    @ApiModelProperty(name = "jznr", value = "兼职内容" ,notes = "2023-11-09 10:15:12")
    @Comment("兼职内容")
    @Column(value = "jznr",type = ColType.AUTO, width = 3000,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=11,fuzzySearch=false,columnHidden=false,columnXtype="textarea",formRequired=false)
    private String jznr;

    @ApiModelProperty(name = "xsttjb", value = "学术团体级别" ,notes = "2023-11-09 10:11:53")
    @Comment("学术团体级别")
    @Column(value = "xsttjb",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="XSTTJB",columnReadonly=false,orderIndex=3,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String xsttjb;

    @ApiModelProperty(name = "zzlx", value = "组织类型" ,notes = "2023-11-09 10:12:36")
    @Comment("组织类型")
    @Column(value = "zzlx",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="XSZZLX",columnReadonly=false,orderIndex=4,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String zzlx;

    @ApiModelProperty(name = "jzzwjb", value = "兼职职务级别" ,notes = "2023-11-09 10:13:50")
    @Comment("兼职职务级别")
    @Column(value = "jzzwjb",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="ZWJB",columnReadonly=false,orderIndex=7,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String jzzwjb;

    @ApiModelProperty(name = "ccyy", value = "辞职原因" ,notes = "2023-11-09 10:14:59")
    @Comment("辞职原因")
    @Column(value = "ccyy",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="CQSHJZHXSTTZWYY",columnReadonly=false,orderIndex=10,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String ccyy;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=13,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
