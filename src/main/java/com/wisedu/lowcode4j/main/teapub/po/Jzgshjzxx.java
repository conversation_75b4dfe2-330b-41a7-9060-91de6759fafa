package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.main.teapub.vo.JzgshjzxxVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgshjzxx", description = "社会兼职信息")
@Comment("社会兼职信息")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_BIZ_TEACHER_SHJZXX", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_BIZ_TEACHER_SHJZXX", unique = false)
})
@Table(value = "BIZ_TEACHER_SHJZXX")
@SqlToyEntity
@ModelExt(extClass = JzgshjzxxVo.class )
@ModelDefine(orderIndex = 6,modelClassify="rsxx", renderType="table")
//end_dynamic_declare
@Data
public class Jzgshjzxx extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041957362L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-08 18:51:35")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=1,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=true)
    private String zgh;

    @ApiModelProperty(name = "jzdw", value = "兼职单位" ,notes = "2023-11-08 18:52:14")
    @Comment("兼职单位")
    @Column(value = "jzdw",type = ColType.AUTO, width = 1800,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=2,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String jzdw;

    @ApiModelProperty(name = "jrnr", value = "兼职内容" ,notes = "2023-11-08 18:53:05")
    @Comment("兼职内容")
    @Column(value = "jrnr",type = ColType.AUTO, width = 3000,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=4,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String jrnr;

    @ApiModelProperty(name = "qsrq", value = "起始日期" ,notes = "2023-11-08 18:53:35")
    @Comment("起始日期")
    @Column(value = "qsrq",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=5,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private String qsrq;

    @ApiModelProperty(name = "zzrq", value = "终止日期" ,notes = "2023-11-08 18:54:37")
    @Comment("终止日期")
    @Column(value = "zzrq",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=6,fuzzySearch=false,columnHidden=false,columnXtype="date-local",formRequired=false)
    private String zzrq;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-08 18:55:22")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=8,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "shjzfw", value = "社会兼职服务" ,notes = "2023-11-08 18:52:46")
    @Comment("社会兼职服务")
    @Column(value = "shjzfw",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SHJZ",columnReadonly=false,orderIndex=3,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String shjzfw;

    @ApiModelProperty(name = "czyy", value = "辞职原因" ,notes = "2023-11-08 18:55:09")
    @Comment("辞职原因")
    @Column(value = "czyy",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="CQSHJZHXSTTZWYY",columnReadonly=false,orderIndex=7,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String czyy;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=9,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
