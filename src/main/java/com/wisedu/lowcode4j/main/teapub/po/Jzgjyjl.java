package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.main.teapub.vo.JzgjyjlVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgjyjl", description = "教育经历")
@Comment("教育经历")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_BIZ_TEACHER_JYJL", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_BIZ_TEACHER_JYJL", unique = false)
})
@Table(value = "BIZ_TEACHER_JYJL")
@SqlToyEntity
@ModelExt(extClass = JzgjyjlVo.class )
@ModelDefine(orderIndex = 2,modelClassify="rsxx", renderType="table")
//end_dynamic_declare
@Data
public class Jzgjyjl extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041954305L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-08 15:26:39")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=1,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=true)
    private String zgh;

    @ApiModelProperty(name = "rxny", value = "入学年月" ,notes = "2023-11-08 15:27:07")
    @Comment("入学年月")
    @Column(value = "rxny",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=2,fuzzySearch=false,columnHidden=false,columnXtype="date-ym",columnFormat="yyyy-MM",formRequired=false)
    private String rxny;

    @ApiModelProperty(name = "byny", value = "毕业年月" ,notes = "2023-11-08 15:27:30")
    @Comment("毕业年月")
    @Column(value = "byny",type = ColType.AUTO, width = 20,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=3,fuzzySearch=false,columnHidden=false,columnXtype="date-ym",columnFormat="yyyy-MM",formRequired=false)
    private String byny;

    @ApiModelProperty(name = "byyxxhdw", value = "毕肄业学校或单位" ,notes = "2023-11-08 15:28:13")
    @Comment("毕肄业学校或单位")
    @Column(value = "byyxxhdw",type = ColType.AUTO, width = 1800,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=4,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String byyxxhdw;

    @ApiModelProperty(name = "xl", value = "学历" ,notes = "2023-11-08 15:28:32")
    @Comment("学历")
    @Column(value = "xl",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="XL",columnReadonly=false,orderIndex=5,fuzzySearch=false,columnHidden=false,columnXtype="select-tree",formRequired=false)
    private String xl;

    @ApiModelProperty(name = "xw", value = "学位" ,notes = "2023-11-08 15:28:59")
    @Comment("学位")
    @Column(value = "xw",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="ZHRMGHGXW",columnReadonly=false,orderIndex=6,fuzzySearch=false,columnHidden=false,columnXtype="select-tree",formRequired=false,formJsonparam="{\"multiple\":false,\"checkStrictly\":true}")
    private String xw;

    @ApiModelProperty(name = "sxzy", value = "所学专业" ,notes = "2023-11-08 15:29:15")
    @Comment("所学专业")
    @Column(value = "sxzy",type = ColType.AUTO, width = 900,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=7,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String sxzy;

    @ApiModelProperty(name = "xz", value = "学制" ,notes = "2023-11-08 15:29:31")
    @Comment("学制")
    @Column(value = "xz",type = ColType.AUTO, width = 90,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=8,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String xz;

    @ApiModelProperty(name = "xwsygj", value = "学位授予国家" ,notes = "2023-11-08 15:29:57")
    @Comment("学位授予国家")
    @Column(value = "xwsygj",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="GJHDQ",columnReadonly=false,orderIndex=9,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String xwsygj;

    @ApiModelProperty(name = "xwsydw", value = "学位授予单位" ,notes = "2023-11-08 15:30:15")
    @Comment("学位授予单位")
    @Column(value = "xwsydw",type = ColType.AUTO, width = 1800,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=10,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String xwsydw;

    @ApiModelProperty(name = "gxlb", value = "高校类别" ,notes = "2023-11-08 15:30:41")
    @Comment("高校类别")
    @Column(value = "gxlb",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="GXLB",columnReadonly=false,orderIndex=11,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String gxlb;

    @ApiModelProperty(name = "sfzgxl", value = "是否最高学历" ,notes = "2023-11-08 15:31:04")
    @Comment("是否最高学历")
    @Column(value = "sfzgxl",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SFBZ",columnReadonly=false,orderIndex=12,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String sfzgxl;

    @ApiModelProperty(name = "sfdyxl", value = "是否第一学历" ,notes = "2024-05-30 17:41:04")
    @Comment("是否第一学历")
    @Column(value = "sfdyxl",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SFBZ",columnReadonly=false,orderIndex=13,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String sfdyxl;

    @ApiModelProperty(name = "sfhwjl", value = "是否海外经历" ,notes = "2024-05-30 17:41:04")
    @Comment("是否海外经历")
    @Column(value = "sfhwjl",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SFBZ",columnReadonly=false,orderIndex=14,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String sfhwjl;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-08 15:31:13")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=15,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=16,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

    //endregion end_dynamic_column

    //region start_dynamic_cascades
    //region end_dynamic_cascades
}
