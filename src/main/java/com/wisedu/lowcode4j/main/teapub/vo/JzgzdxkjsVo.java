package com.wisedu.lowcode4j.main.teapub.vo;

import com.wisedu.lowcode4j.common.core.constant.*;

import org.sagacity.sqltoy.config.annotation.*;

import io.swagger.annotations.*;

import lombok.Data;
import com.alibaba.fastjson.annotation.*;

import com.wisedu.lowcode4j.main.teapub.po.Jzgzdxkjs;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgzdxkjsVo", description = "指导学科竞赛Vo")
//end_dynamic_declare
@Data
public class JzgzdxkjsVo extends Jzgzdxkjs {

    private static final long serialVersionUID = 4431474721041957107L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "jsxkName", value = "竞赛学科名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "jsxk", cacheType = "XWSYHRCPYXKML")
    @JSONField(name = "jsxk"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String jsxkName;

    @ApiModelProperty(name = "jsjbName", value = "竞赛级别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "jsjb", cacheType = "JSJB")
    @JSONField(name = "jsjb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String jsjbName;

    @ApiModelProperty(name = "jljbName", value = "奖励级别名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "jljb", cacheType = "JB")
    @JSONField(name = "jljb"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String jljbName;

    @ApiModelProperty(name = "jldjName", value = "奖励等级名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "jldj", cacheType = "JLDJ")
    @JSONField(name = "jldj"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String jldjName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
