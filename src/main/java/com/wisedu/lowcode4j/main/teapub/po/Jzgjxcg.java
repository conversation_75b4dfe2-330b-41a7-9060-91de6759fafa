package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.common.core.annotation.ColumnDefine;
import com.wisedu.lowcode4j.common.core.annotation.ModelDefine;
import com.wisedu.lowcode4j.common.core.annotation.ModelExt;
import com.wisedu.lowcode4j.common.core.model.BaseDataModel;
import com.wisedu.lowcode4j.main.teapub.vo.JzgjxcgVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nutz.dao.entity.annotation.*;
import org.sagacity.sqltoy.config.annotation.SqlToyEntity;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgjxcg", description = "教学成果")
@Comment("教学成果")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_biz_teacher_jxcg", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_biz_teacher_jxcg", unique = false)
})
@Table(value = "BIZ_TEACHER_JXCG")
@SqlToyEntity
@ModelExt(extClass = JzgjxcgVo.class )
@ModelDefine(orderIndex = 6,modelClassify="jyjx", renderType="table")
//end_dynamic_declare
@Data
public class Jzgjxcg extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041956591L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-11-27 18:03:54")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=1,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zgh;

    @ApiModelProperty(name = "jxcgbm", value = "教学成果编码" ,notes = "2023-11-27 18:04:18")
    @Comment("教学成果编码")
    @Column(value = "jxcgbm",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=2,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String jxcgbm;

    @ApiModelProperty(name = "jxcgmc", value = "教学成果名称" ,notes = "2023-11-27 18:04:52")
    @Comment("教学成果名称")
    @Column(value = "jxcgmc",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=3,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String jxcgmc;

    @ApiModelProperty(name = "xkml", value = "学科门类" ,notes = "2023-11-27 18:07:03")
    @Comment("学科门类")
    @Column(value = "xkml",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="XWSYHRCPYXKML",orderIndex=4,columnReadonly=false,fuzzySearch=false,columnHidden=false,columnXtype="select-tree",formRequired=false,formJsonparam="{\"multiple\":false,\"checkStrictly\":true}")
    private String xkml;

    @ApiModelProperty(name = "yjxk", value = "一级学科" ,notes = "2023-11-27 18:08:15")
    @Comment("一级学科")
    @Column(value = "yjxk",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="XWSYHRCPYXKML",orderIndex=5,columnReadonly=false,fuzzySearch=false,columnHidden=false,columnXtype="select-tree",formRequired=false,formJsonparam="{\"multiple\":false,\"checkStrictly\":true}")
    private String yjxk;

    @ApiModelProperty(name = "zcr", value = "主持人" ,notes = "2023-11-27 18:08:37")
    @Comment("主持人")
    @Column(value = "zcr",type = ColType.AUTO, width = 240,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=6,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zcr;

    @ApiModelProperty(name = "zcdw", value = "主持单位" ,notes = "2023-11-27 18:08:57")
    @Comment("主持单位")
    @Column(value = "zcdw",type = ColType.AUTO, width = 300,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=7,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zcdw;

    @ApiModelProperty(name = "cgjj", value = "成果简介" ,notes = "2023-11-27 18:11:16")
    @Comment("成果简介")
    @Column(value = "cgjj",type = ColType.AUTO, width = 3000,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=9,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String cgjj;

    @ApiModelProperty(name = "cgcxd", value = "成果创新点" ,notes = "2023-11-27 18:11:56")
    @Comment("成果创新点")
    @Column(value = "cgcxd",type = ColType.AUTO, width = 3000,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=10,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String cgcxd;

    @ApiModelProperty(name = "xxsm", value = "学校署名" ,notes = "2023-11-27 18:12:34")
    @Comment("学校署名")
    @Column(value = "xxsm",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=11,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String xxsm;

    @ApiModelProperty(name = "js", value = "角色" ,notes = "2023-11-27 18:13:07")
    @Comment("角色")
    @Column(value = "js",type = ColType.AUTO, width = 60,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="JS",orderIndex=12,columnReadonly=false,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String js;

    @ApiModelProperty(name = "brwc", value = "本人位次" ,notes = "2023-11-27 18:13:53")
    @Comment("本人位次")
    @Column(value = "brwc",type = ColType.AUTO, width = 90,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=13,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String brwc;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-11-27 18:14:11")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "bxfzr", value = "本校负责人" ,notes = "2023-11-28 16:33:19")
    @Comment("本校负责人")
    @Column(value = "bxfzr",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=8,fuzzySearch=false,columnHidden=false,columnJsonparam="{\"dictParams\":{\"label\":\"teacherName\",\"value\":\"zgh\",\"url\":\"/plugins/grsjzx/citeTable/getTeacherTable\",\"dataPath\":\"rows\"},\"columns\":[{\"name\":\"userName\",\"label\":\"姓名\"}, {\"name\":\"zgh\",\"label\":\"工号\"}],\"filterProps\":{\"filterable\":true,\"remote\":true},\"multiple\":false,\"pagerConfig\":{\"pageSizes\":[5, 10, 20, 50, 100],\"background\":false,\"pagerCount\":7,\"hideOnSinglePage\":false,\"pageSize\":5,\"position\":\"right\",\"layouts\":[\"prev\", \"pager\", \"next\", \"sizes\", \"total\"],\"enabled\":true}}",columnXtype="text",formRequired=false)
    private String bxfzr;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,orderIndex=14,columnHidden=false,columnXtype="uploadfile",formJsonparam= "{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
