package com.wisedu.lowcode4j.main.teapub.vo;

import com.wisedu.lowcode4j.common.core.constant.*;

import org.sagacity.sqltoy.config.annotation.*;

import io.swagger.annotations.*;

import lombok.Data;
import com.alibaba.fastjson.annotation.*;

import com.wisedu.lowcode4j.main.teapub.po.Jzghwyx;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzghwyxVo", description = "海外研修Vo")
//end_dynamic_declare
@Data
public class JzghwyxVo extends Jzghwyx {

    private static final long serialVersionUID = 4431474721041952356L;

    //region start_dynamic_dict_column
    @ApiModelProperty(name = "gjdqName", value = "国家地区名称")
    @Translate(cacheName = DictCacheType.GLOBAL, keyField = "gjdq", cacheType = "GJHDQ")
    @JSONField(name = "gjdq"+ApplicationConstant.DICT_NAME_SUFFIX)
    private String gjdqName;

	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
