package com.wisedu.lowcode4j.main.teapub.po;

import com.wisedu.lowcode4j.main.teapub.vo.JzggzjlVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzggzjl", description = "工作经历")
@Comment("工作经历")
@TableIndexes({
        @Index(fields = {"zgh"}, name = "idx_BIZ_TEACHER_GZJL", unique = false),
        @Index(fields = {"zt"}, name = "idx_zt_BIZ_TEACHER_GZJL", unique = false)
})
@Table(value = "BIZ_TEACHER_GZJL")
@SqlToyEntity
@ModelExt(extClass = JzggzjlVo.class )
@ModelDefine(orderIndex = 1,modelClassify="rsxx", renderType = "table")
//end_dynamic_declare
@Data
public class Jzggzjl extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041957603L;

    //region start_dynamic_column
    @ApiModelProperty(name = "zgh", value = "教职工号" ,notes = "2023-10-13 09:42:24")
    @Comment("教职工号")
    @Column(value = "zgh",type = ColType.AUTO, width = 180,insert=true,update=true,notNull=true)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=1,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=true)
    private String zgh;

    @ApiModelProperty(name = "crdzzw", value = "曾任党政职务" ,notes = "2023-10-13 09:42:06")
    @Comment("曾任党政职务")
    @Column(value = "crdzzw",type = ColType.AUTO, width = 270,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=4,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String crdzzw;

    @ApiModelProperty(name = "zmr", value = "工作证明人" ,notes = "2023-10-13 09:42:00")
    @Comment("工作证明人")
    @Column(value = "zmr",type = ColType.AUTO, width = 270,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=6,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String zmr;

    @ApiModelProperty(name = "jzny", value = "截止年月" ,notes = "2023-10-13 09:42:18")
    @Comment("截止年月")
    @Column(value = "jzny",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=9,fuzzySearch=false,columnHidden=false,columnXtype="date-ym",columnFormat="yyyy-MM",formRequired=false)
    private String jzny;

    @ApiModelProperty(name = "szdwmc", value = "所在单位名称" ,notes = "2023-10-13 09:42:08")
    @Comment("所在单位名称")
    @Column(value = "szdwmc",type = ColType.AUTO, width = 540,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=10,fuzzySearch=false,columnHidden=false,columnXtype="text",formRequired=false)
    private String szdwmc;

    @ApiModelProperty(name = "zt", value = "状态" ,notes = "2023-10-16 16:39:53")
    @Comment("状态")
    @Column(value = "zt",type = ColType.AUTO, width = 30,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,fuzzySearch=false,columnHidden=true,columnXtype="text",formRequired=false)
    private String zt;

    @ApiModelProperty(name = "gjdqdm", value = "国家地区" ,notes = "2023-10-13 09:42:26")
    @Comment("国家地区")
    @Column(value = "gjdqdm",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="GJHDQ",columnReadonly=false,orderIndex=3,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String gjdqdm;

    @ApiModelProperty(name = "crzyjszwdm", value = "曾任专业技术职务" ,notes = "2023-10-13 09:42:10")
    @Comment("曾任专业技术职务")
    @Column(value = "crzyjszwdm",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="ZYJSZW",columnReadonly=false,orderIndex=5,fuzzySearch=false,columnHidden=false,columnXtype="select-tree",formRequired=false,formJsonparam="{\"multiple\":false,\"checkStrictly\":true}")
    private String crzyjszwdm;

    @ApiModelProperty(name = "sfhwjl", value = "是否海外经历" ,notes = "2023-10-13 09:42:15")
    @Comment("是否海外经历")
    @Column(value = "sfhwjl",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SFBZ",columnReadonly=false,orderIndex=7,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String sfhwjl;

    @ApiModelProperty(name = "dwxzlbdm", value = "单位性质类别" ,notes = "2023-10-13 09:42:14")
    @Comment("单位性质类别")
    @Column(value = "dwxzlbdm",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnDict="SHDWXZ",columnReadonly=false,orderIndex=2,fuzzySearch=false,columnHidden=false,columnXtype="select",formRequired=false)
    private String dwxzlbdm;

    @ApiModelProperty(name = "fj", value = "附件" ,notes = "2023-12-08 14:38:42")
    @Comment("附件")
    @Column(value = "fj",type = ColType.AUTO, width = 225,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=11,fuzzySearch=false,columnHidden=false,columnXtype="uploadfile",formRequired=false,formJsonparam="{\"size\":\"100\",\"limit\":\"100\",\"isOver\":\"true\"}")
    private String fj;

    @ApiModelProperty(name = "qsny", value = "起始年月" ,notes = "2023-10-13 09:42:09")
    @Comment("起始年月")
    @Column(value = "qsny",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,orderIndex=8,fuzzySearch=false,columnHidden=false,columnXtype="date-ym",columnFormat="yyyy-MM",formRequired=false)
    private String qsny;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
