package com.wisedu.lowcode4j.main.teapub.vo;

import io.swagger.annotations.*;

import lombok.Data;

import com.wisedu.lowcode4j.main.teapub.po.Jzgzdbzks;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "jzgzdbzksVo", description = "指导本专科生Vo")
//end_dynamic_declare
@Data
public class JzgzdbzksVo extends Jzgzdbzks {

    private static final long serialVersionUID = 4431474721041957405L;

    //region start_dynamic_dict_column
	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
