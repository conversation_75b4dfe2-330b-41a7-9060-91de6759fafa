-- 公共基础表中appCode属性初始化
UPDATE T_JSSJZX_SJBD SET APP_CODE = 'grsjzx' WHERE APP_CODE IS NULL;
UPDATE T_JSSJZX_YHZMXSQ SET APP_CODE = 'grsjzx' WHERE APP_CODE IS NULL;
UPDATE T_JSSJZX_BGJL SET APP_CODE = 'grsjzx' WHERE APP_CODE IS NULL;
UPDATE T_JSSJZX_BGRZ SET APP_CODE = 'grsjzx' WHERE APP_CODE IS NULL;



-- 科研经费及科研获奖记录模型授权初始化
INSERT INTO T_JSSJZX_YHZMXSQ (ID, YHZID, BM, SHLC, MXID, SFXZ, SFKJ, SFSC, SFXG, SFFJ, TSWA, SFDR, SFDC, YWXTMC, YWXTDZ) SELECT '1726642026200001245' AS ID, '1730147021694074880' AS YHZID, NULL AS BM, NULL AS SHLC, 'main-jzgkyhj' AS MXID, '0' AS SFXZ, '1' AS SFKJ, '0' AS SFSC, NULL AS SFXG, '0' AS SFFJ, NULL AS TSWA, '1' AS SFDR, '1' AS SFDC, NULL AS YWXTMC, NULL AS YWXTDZ FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZMXSQ WHERE 1 = 2) t WHERE NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.YHZID = '1730147021694074880' and a.MXID = 'main-jzgkyhj');
INSERT INTO T_JSSJZX_YHZMXSQ (ID, YHZID, BM, SHLC, MXID, SFXZ, SFKJ, SFSC, SFXG, SFFJ, TSWA, SFDR, SFDC, YWXTMC, YWXTDZ) SELECT '1726642039204001245' AS ID, '1730147021694074880' AS YHZID, NULL AS BM, NULL AS SHLC, 'main-jzgkyjfjl' AS MXID, '0' AS SFXZ, '1' AS SFKJ, '0' AS SFSC, NULL AS SFXG, '0' AS SFFJ, NULL AS TSWA, '1' AS SFDR, '1' AS SFDC, NULL AS YWXTMC, NULL AS YWXTDZ FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZMXSQ WHERE 1 = 2) t WHERE NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.YHZID = '1730147021694074880' and a.MXID = 'main-jzgkyjfjl');
INSERT INTO T_JSSJZX_YHZMXSQ (ID, YHZID, BM, SHLC, MXID, SFXZ, SFKJ, SFSC, SFXG, SFFJ, TSWA, SFDR, SFDC, YWXTMC, YWXTDZ) SELECT '1726642931036001245' AS ID, 'bmfz20006' AS YHZID, NULL AS BM, NULL AS SHLC, 'main-jzgkyhj' AS MXID, '0' AS SFXZ, '1' AS SFKJ, '0' AS SFSC, NULL AS SFXG, '0' AS SFFJ, NULL AS TSWA, '1' AS SFDR, '1' AS SFDC, NULL AS YWXTMC, NULL AS YWXTDZ FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZMXSQ WHERE 1 = 2) t WHERE NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.YHZID = 'bmfz20006' and a.MXID = 'main-jzgkyhj');
INSERT INTO T_JSSJZX_YHZMXSQ (ID, YHZID, BM, SHLC, MXID, SFXZ, SFKJ, SFSC, SFXG, SFFJ, TSWA, SFDR, SFDC, YWXTMC, YWXTDZ) SELECT '1726642942503001245' AS ID, 'bmfz20006' AS YHZID, NULL AS BM, NULL AS SHLC, 'main-jzgkyjfjl' AS MXID, '0' AS SFXZ, '1' AS SFKJ, '0' AS SFSC, NULL AS SFXG, '0' AS SFFJ, NULL AS TSWA, '1' AS SFDR, '1' AS SFDC, NULL AS YWXTMC, NULL AS YWXTDZ FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZMXSQ WHERE 1 = 2) t WHERE NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.YHZID = 'bmfz20006' and a.MXID = 'main-jzgkyjfjl');
INSERT INTO T_JSSJZX_YHZMXSQ (ID, YHZID, BM, SHLC, MXID, SFXZ, SFKJ, SFSC, SFXG, SFFJ, TSWA, SFDR, SFDC, YWXTMC, YWXTDZ) SELECT '1726642966668001245' AS ID, 'bmms20006' AS YHZID, NULL AS BM, NULL AS SHLC, 'main-jzgkyhj' AS MXID, '0' AS SFXZ, '1' AS SFKJ, '0' AS SFSC, NULL AS SFXG, '0' AS SFFJ, NULL AS TSWA, '1' AS SFDR, '1' AS SFDC, NULL AS YWXTMC, NULL AS YWXTDZ FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZMXSQ WHERE 1 = 2) t WHERE NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.YHZID = 'bmms20006' and a.MXID = 'main-jzgkyhj');
INSERT INTO T_JSSJZX_YHZMXSQ (ID, YHZID, BM, SHLC, MXID, SFXZ, SFKJ, SFSC, SFXG, SFFJ, TSWA, SFDR, SFDC, YWXTMC, YWXTDZ) SELECT '1726642957362001245' AS ID, 'bmms20006' AS YHZID, NULL AS BM, NULL AS SHLC, 'main-jzgkyjfjl' AS MXID, '0' AS SFXZ, '1' AS SFKJ, '0' AS SFSC, NULL AS SFXG, '0' AS SFFJ, NULL AS TSWA, '1' AS SFDR, '1' AS SFDC, NULL AS YWXTMC, NULL AS YWXTDZ FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZMXSQ WHERE 1 = 2) t WHERE NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.YHZID = 'bmms20006' and a.MXID = 'main-jzgkyjfjl');
INSERT INTO T_JSSJZX_YHZMXSQ (ID, YHZID, BM, SHLC, MXID, SFXZ, SFKJ, SFSC, SFXG, SFFJ, TSWA, SFDR, SFDC, YWXTMC, YWXTDZ) SELECT '1726642990149001245' AS ID, 'bmzz20006' AS YHZID, NULL AS BM, NULL AS SHLC, 'main-jzgkyhj' AS MXID, '0' AS SFXZ, '1' AS SFKJ, '0' AS SFSC, NULL AS SFXG, '0' AS SFFJ, NULL AS TSWA, '1' AS SFDR, '1' AS SFDC, NULL AS YWXTMC, NULL AS YWXTDZ FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZMXSQ WHERE 1 = 2) t WHERE NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.YHZID = 'bmzz20006' and a.MXID = 'main-jzgkyhj');
INSERT INTO T_JSSJZX_YHZMXSQ (ID, YHZID, BM, SHLC, MXID, SFXZ, SFKJ, SFSC, SFXG, SFFJ, TSWA, SFDR, SFDC, YWXTMC, YWXTDZ) SELECT '1726642982822001245' AS ID, 'bmzz20006' AS YHZID, NULL AS BM, NULL AS SHLC, 'main-jzgkyjfjl' AS MXID, '0' AS SFXZ, '1' AS SFKJ, '0' AS SFSC, NULL AS SFXG, '0' AS SFFJ, NULL AS TSWA, '1' AS SFDR, '1' AS SFDC, NULL AS YWXTMC, NULL AS YWXTDZ FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZMXSQ WHERE 1 = 2) t WHERE NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.YHZID = 'bmzz20006' and a.MXID = 'main-jzgkyjfjl');
INSERT INTO T_JSSJZX_YHZMXSQ (ID, YHZID, BM, SHLC, MXID, SFXZ, SFKJ, SFSC, SFXG, SFFJ, TSWA, SFDR, SFDC, YWXTMC, YWXTDZ) SELECT '1726643018387001245' AS ID, 'dafgxld20001' AS YHZID, NULL AS BM, NULL AS SHLC, 'main-jzgkyhj' AS MXID, '0' AS SFXZ, '1' AS SFKJ, '0' AS SFSC, NULL AS SFXG, '0' AS SFFJ, NULL AS TSWA, '0' AS SFDR, '1' AS SFDC, NULL AS YWXTMC, NULL AS YWXTDZ FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZMXSQ WHERE 1 = 2) t WHERE NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.YHZID = 'dafgxld20001' and a.MXID = 'main-jzgkyhj');
INSERT INTO T_JSSJZX_YHZMXSQ (ID, YHZID, BM, SHLC, MXID, SFXZ, SFKJ, SFSC, SFXG, SFFJ, TSWA, SFDR, SFDC, YWXTMC, YWXTDZ) SELECT '1726643010949001245' AS ID, 'dafgxld20001' AS YHZID, NULL AS BM, NULL AS SHLC, 'main-jzgkyjfjl' AS MXID, '0' AS SFXZ, '1' AS SFKJ, '0' AS SFSC, NULL AS SFXG, '0' AS SFFJ, NULL AS TSWA, '0' AS SFDR, '1' AS SFDC, NULL AS YWXTMC, NULL AS YWXTDZ FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZMXSQ WHERE 1 = 2) t WHERE NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.YHZID = 'dafgxld20001' and a.MXID = 'main-jzgkyjfjl');
INSERT INTO T_JSSJZX_YHZMXSQ (ID, YHZID, BM, SHLC, MXID, SFXZ, SFKJ, SFSC, SFXG, SFFJ, TSWA, SFDR, SFDC, YWXTMC, YWXTDZ) SELECT '1726642074628001245' AS ID, 'role-daadmin' AS YHZID, NULL AS BM, NULL AS SHLC, 'main-jzgkyhj' AS MXID, '0' AS SFXZ, '1' AS SFKJ, '0' AS SFSC, NULL AS SFXG, '0' AS SFFJ, NULL AS TSWA, '0' AS SFDR, '1' AS SFDC, NULL AS YWXTMC, NULL AS YWXTDZ FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZMXSQ WHERE 1 = 2) t WHERE NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.YHZID = 'role-daadmin' and a.MXID = 'main-jzgkyhj');
INSERT INTO T_JSSJZX_YHZMXSQ (ID, YHZID, BM, SHLC, MXID, SFXZ, SFKJ, SFSC, SFXG, SFFJ, TSWA, SFDR, SFDC, YWXTMC, YWXTDZ) SELECT '1726642063287001245' AS ID, 'role-daadmin' AS YHZID, NULL AS BM, NULL AS SHLC, 'main-jzgkyjfjl' AS MXID, '0' AS SFXZ, '1' AS SFKJ, '0' AS SFSC, NULL AS SFXG, '0' AS SFFJ, NULL AS TSWA, '0' AS SFDR, '1' AS SFDC, NULL AS YWXTMC, NULL AS YWXTDZ FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZMXSQ WHERE 1 = 2) t WHERE NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.YHZID = 'role-daadmin' and a.MXID = 'main-jzgkyjfjl');
INSERT INTO T_JSSJZX_YHZMXSQ (ID, YHZID, BM, SHLC, MXID, SFXZ, SFKJ, SFSC, SFXG, SFFJ, TSWA, SFDR, SFDC, YWXTMC, YWXTDZ) SELECT '1726641998917001245' AS ID, 'teacher80001' AS YHZID, NULL AS BM, NULL AS SHLC, 'main-jzgkyhj' AS MXID, '0' AS SFXZ, '1' AS SFKJ, '0' AS SFSC, NULL AS SFXG, '0' AS SFFJ, NULL AS TSWA, '0' AS SFDR, '1' AS SFDC, NULL AS YWXTMC, NULL AS YWXTDZ FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZMXSQ WHERE 1 = 2) t WHERE NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.YHZID = 'teacher80001' and a.MXID = 'main-jzgkyhj');
INSERT INTO T_JSSJZX_YHZMXSQ (ID, YHZID, BM, SHLC, MXID, SFXZ, SFKJ, SFSC, SFXG, SFFJ, TSWA, SFDR, SFDC, YWXTMC, YWXTDZ) SELECT '1726641979026001245' AS ID, 'teacher80001' AS YHZID, NULL AS BM, NULL AS SHLC, 'main-jzgkyjfjl' AS MXID, '0' AS SFXZ, '1' AS SFKJ, '0' AS SFSC, NULL AS SFXG, '0' AS SFFJ, NULL AS TSWA, '0' AS SFDR, '1' AS SFDC, NULL AS YWXTMC, NULL AS YWXTDZ FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZMXSQ WHERE 1 = 2) t WHERE NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.YHZID = 'teacher80001' and a.MXID = 'main-jzgkyjfjl');



-- 科研经费及科研获奖记录模型字段授权初始化
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641979133004245' AS ID, 'createBy' AS ZDID, '1726641979026001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641979026001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641979133004245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641979133005245' AS ID, 'createTime' AS ZDID, '1726641979026001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641979026001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641979133005245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641979132010245' AS ID, 'dzjf' AS ZDID, '1726641979026001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641979026001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641979132010245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641979132011245' AS ID, 'fj' AS ZDID, '1726641979026001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641979026001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641979132011245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641979133003245' AS ID, 'id' AS ZDID, '1726641979026001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641979026001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641979133003245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641979132005245' AS ID, 'kjxmlb' AS ZDID, '1726641979026001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641979026001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641979132005245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641979132008245' AS ID, 'lxjf' AS ZDID, '1726641979026001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641979026001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641979132008245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641979133006245' AS ID, 'updateBy' AS ZDID, '1726641979026001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641979026001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641979133006245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641979133007245' AS ID, 'updateTime' AS ZDID, '1726641979026001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641979026001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641979133007245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641979132009245' AS ID, 'wbjf' AS ZDID, '1726641979026001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641979026001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641979132009245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641979132001245' AS ID, 'xmbh' AS ZDID, '1726641979026001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641979026001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641979132001245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641979132007245' AS ID, 'xmjfze' AS ZDID, '1726641979026001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641979026001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641979132007245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641979132004245' AS ID, 'xmlb' AS ZDID, '1726641979026001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641979026001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641979132004245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641979132003245' AS ID, 'xmlx' AS ZDID, '1726641979026001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641979026001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641979132003245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641979132006245' AS ID, 'xmly' AS ZDID, '1726641979026001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641979026001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641979132006245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641979132002245' AS ID, 'xmmc' AS ZDID, '1726641979026001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641979026001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641979132002245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641979133001245' AS ID, 'zgh' AS ZDID, '1726641979026001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641979026001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641979133001245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641979133002245' AS ID, 'zt' AS ZDID, '1726641979026001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641979026001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641979133002245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641998969016245' AS ID, 'createBy' AS ZDID, '1726641998917001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641998917001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641998969016245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641998969017245' AS ID, 'createTime' AS ZDID, '1726641998917001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641998917001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641998969017245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641998969010245' AS ID, 'dwpm' AS ZDID, '1726641998917001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641998917001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641998969010245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641998969012245' AS ID, 'fj' AS ZDID, '1726641998917001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641998917001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641998969012245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641998969001245' AS ID, 'hjcglx' AS ZDID, '1726641998917001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641998917001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641998969001245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641998969007245' AS ID, 'hjdj' AS ZDID, '1726641998917001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641998917001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641998969007245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641998969006245' AS ID, 'hjjb' AS ZDID, '1726641998917001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641998917001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641998969006245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641998969009245' AS ID, 'hjje' AS ZDID, '1726641998917001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641998917001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641998969009245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641998969005245' AS ID, 'hjlb' AS ZDID, '1726641998917001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641998917001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641998969005245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641998969008245' AS ID, 'hjrq' AS ZDID, '1726641998917001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641998917001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641998969008245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641998969015245' AS ID, 'id' AS ZDID, '1726641998917001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641998917001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641998969015245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641998969004245' AS ID, 'jlmc' AS ZDID, '1726641998917001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641998917001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641998969004245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641998969002245' AS ID, 'kycgbh' AS ZDID, '1726641998917001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641998917001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641998969002245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641998969003245' AS ID, 'kycgmc' AS ZDID, '1726641998917001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641998917001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641998969003245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641998969011245' AS ID, 'smsx' AS ZDID, '1726641998917001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641998917001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641998969011245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641998969018245' AS ID, 'updateBy' AS ZDID, '1726641998917001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641998917001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641998969018245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641998969019245' AS ID, 'updateTime' AS ZDID, '1726641998917001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641998917001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641998969019245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641998969013245' AS ID, 'zgh' AS ZDID, '1726641998917001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641998917001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641998969013245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726641998969014245' AS ID, 'zt' AS ZDID, '1726641998917001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726641998917001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726641998969014245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642026205014245' AS ID, 'createBy' AS ZDID, '1726642026200001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642026200001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642026205014245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642026205015245' AS ID, 'createTime' AS ZDID, '1726642026200001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642026200001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642026205015245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642026205008245' AS ID, 'dwpm' AS ZDID, '1726642026200001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642026200001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642026205008245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642026205010245' AS ID, 'fj' AS ZDID, '1726642026200001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642026200001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642026205010245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642026204001245' AS ID, 'hjcglx' AS ZDID, '1726642026200001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642026200001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642026204001245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642026205005245' AS ID, 'hjdj' AS ZDID, '1726642026200001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642026200001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642026205005245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642026205004245' AS ID, 'hjjb' AS ZDID, '1726642026200001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642026200001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642026205004245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642026205007245' AS ID, 'hjje' AS ZDID, '1726642026200001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642026200001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642026205007245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642026205003245' AS ID, 'hjlb' AS ZDID, '1726642026200001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642026200001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642026205003245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642026205006245' AS ID, 'hjrq' AS ZDID, '1726642026200001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642026200001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642026205006245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642026205013245' AS ID, 'id' AS ZDID, '1726642026200001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642026200001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642026205013245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642026205002245' AS ID, 'jlmc' AS ZDID, '1726642026200001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642026200001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642026205002245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642026204002245' AS ID, 'kycgbh' AS ZDID, '1726642026200001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642026200001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642026204002245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642026205001245' AS ID, 'kycgmc' AS ZDID, '1726642026200001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642026200001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642026205001245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642026205009245' AS ID, 'smsx' AS ZDID, '1726642026200001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642026200001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642026205009245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642026205016245' AS ID, 'updateBy' AS ZDID, '1726642026200001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642026200001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642026205016245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642026205017245' AS ID, 'updateTime' AS ZDID, '1726642026200001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642026200001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642026205017245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642026205011245' AS ID, 'zgh' AS ZDID, '1726642026200001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642026200001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642026205011245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642026205012245' AS ID, 'zt' AS ZDID, '1726642026200001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642026200001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642026205012245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642039209015245' AS ID, 'createBy' AS ZDID, '1726642039204001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642039204001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642039209015245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642039209016245' AS ID, 'createTime' AS ZDID, '1726642039204001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642039204001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642039209016245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642039209010245' AS ID, 'dzjf' AS ZDID, '1726642039204001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642039204001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642039209010245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642039209011245' AS ID, 'fj' AS ZDID, '1726642039204001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642039204001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642039209011245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642039209014245' AS ID, 'id' AS ZDID, '1726642039204001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642039204001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642039209014245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642039209005245' AS ID, 'kjxmlb' AS ZDID, '1726642039204001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642039204001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642039209005245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642039209008245' AS ID, 'lxjf' AS ZDID, '1726642039204001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642039204001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642039209008245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642039209017245' AS ID, 'updateBy' AS ZDID, '1726642039204001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642039204001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642039209017245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642039209018245' AS ID, 'updateTime' AS ZDID, '1726642039204001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642039204001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642039209018245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642039209009245' AS ID, 'wbjf' AS ZDID, '1726642039204001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642039204001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642039209009245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642039209001245' AS ID, 'xmbh' AS ZDID, '1726642039204001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642039204001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642039209001245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642039209007245' AS ID, 'xmjfze' AS ZDID, '1726642039204001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642039204001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642039209007245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642039209004245' AS ID, 'xmlb' AS ZDID, '1726642039204001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642039204001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642039209004245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642039209003245' AS ID, 'xmlx' AS ZDID, '1726642039204001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642039204001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642039209003245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642039209006245' AS ID, 'xmly' AS ZDID, '1726642039204001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642039204001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642039209006245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642039209002245' AS ID, 'xmmc' AS ZDID, '1726642039204001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642039204001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642039209002245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642039209012245' AS ID, 'zgh' AS ZDID, '1726642039204001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642039204001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642039209012245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642039209013245' AS ID, 'zt' AS ZDID, '1726642039204001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642039204001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642039209013245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642063294015245' AS ID, 'createBy' AS ZDID, '1726642063287001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642063287001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642063294015245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642063294016245' AS ID, 'createTime' AS ZDID, '1726642063287001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642063287001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642063294016245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642063294010245' AS ID, 'dzjf' AS ZDID, '1726642063287001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642063287001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642063294010245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642063294011245' AS ID, 'fj' AS ZDID, '1726642063287001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642063287001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642063294011245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642063294014245' AS ID, 'id' AS ZDID, '1726642063287001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642063287001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642063294014245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642063294005245' AS ID, 'kjxmlb' AS ZDID, '1726642063287001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642063287001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642063294005245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642063294008245' AS ID, 'lxjf' AS ZDID, '1726642063287001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642063287001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642063294008245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642063294017245' AS ID, 'updateBy' AS ZDID, '1726642063287001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642063287001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642063294017245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642063294018245' AS ID, 'updateTime' AS ZDID, '1726642063287001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642063287001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642063294018245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642063294009245' AS ID, 'wbjf' AS ZDID, '1726642063287001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642063287001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642063294009245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642063294001245' AS ID, 'xmbh' AS ZDID, '1726642063287001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642063287001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642063294001245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642063294007245' AS ID, 'xmjfze' AS ZDID, '1726642063287001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642063287001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642063294007245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642063294004245' AS ID, 'xmlb' AS ZDID, '1726642063287001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642063287001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642063294004245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642063294003245' AS ID, 'xmlx' AS ZDID, '1726642063287001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642063287001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642063294003245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642063294006245' AS ID, 'xmly' AS ZDID, '1726642063287001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642063287001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642063294006245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642063294002245' AS ID, 'xmmc' AS ZDID, '1726642063287001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642063287001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642063294002245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642063294012245' AS ID, 'zgh' AS ZDID, '1726642063287001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642063287001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642063294012245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642063294013245' AS ID, 'zt' AS ZDID, '1726642063287001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642063287001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642063294013245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642074634010245' AS ID, 'createBy' AS ZDID, '1726642074628001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642074628001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642074634010245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642074634011245' AS ID, 'createTime' AS ZDID, '1726642074628001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642074628001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642074634011245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642074634004245' AS ID, 'dwpm' AS ZDID, '1726642074628001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642074628001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642074634004245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642074634006245' AS ID, 'fj' AS ZDID, '1726642074628001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642074628001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642074634006245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642074633001245' AS ID, 'hjcglx' AS ZDID, '1726642074628001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642074628001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642074633001245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642074634001245' AS ID, 'hjdj' AS ZDID, '1726642074628001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642074628001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642074634001245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642074633006245' AS ID, 'hjjb' AS ZDID, '1726642074628001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642074628001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642074633006245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642074634003245' AS ID, 'hjje' AS ZDID, '1726642074628001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642074628001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642074634003245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642074633005245' AS ID, 'hjlb' AS ZDID, '1726642074628001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642074628001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642074633005245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642074634002245' AS ID, 'hjrq' AS ZDID, '1726642074628001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642074628001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642074634002245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642074634009245' AS ID, 'id' AS ZDID, '1726642074628001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642074628001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642074634009245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642074633004245' AS ID, 'jlmc' AS ZDID, '1726642074628001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642074628001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642074633004245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642074633002245' AS ID, 'kycgbh' AS ZDID, '1726642074628001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642074628001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642074633002245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642074633003245' AS ID, 'kycgmc' AS ZDID, '1726642074628001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642074628001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642074633003245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642074634005245' AS ID, 'smsx' AS ZDID, '1726642074628001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642074628001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642074634005245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642074634012245' AS ID, 'updateBy' AS ZDID, '1726642074628001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642074628001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642074634012245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642074634013245' AS ID, 'updateTime' AS ZDID, '1726642074628001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642074628001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642074634013245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642074634007245' AS ID, 'zgh' AS ZDID, '1726642074628001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642074628001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642074634007245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642074634008245' AS ID, 'zt' AS ZDID, '1726642074628001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642074628001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642074634008245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642931047002245' AS ID, 'createBy' AS ZDID, '1726642931036001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642931036001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642931047002245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642931047003245' AS ID, 'createTime' AS ZDID, '1726642931036001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642931036001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642931047003245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642931046010245' AS ID, 'dwpm' AS ZDID, '1726642931036001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642931036001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642931046010245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642931046012245' AS ID, 'fj' AS ZDID, '1726642931036001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642931036001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642931046012245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642931046001245' AS ID, 'hjcglx' AS ZDID, '1726642931036001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642931036001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642931046001245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642931046007245' AS ID, 'hjdj' AS ZDID, '1726642931036001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642931036001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642931046007245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642931046006245' AS ID, 'hjjb' AS ZDID, '1726642931036001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642931036001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642931046006245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642931046009245' AS ID, 'hjje' AS ZDID, '1726642931036001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642931036001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642931046009245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642931046005245' AS ID, 'hjlb' AS ZDID, '1726642931036001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642931036001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642931046005245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642931046008245' AS ID, 'hjrq' AS ZDID, '1726642931036001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642931036001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642931046008245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642931047001245' AS ID, 'id' AS ZDID, '1726642931036001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642931036001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642931047001245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642931046004245' AS ID, 'jlmc' AS ZDID, '1726642931036001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642931036001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642931046004245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642931046002245' AS ID, 'kycgbh' AS ZDID, '1726642931036001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642931036001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642931046002245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642931046003245' AS ID, 'kycgmc' AS ZDID, '1726642931036001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642931036001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642931046003245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642931046011245' AS ID, 'smsx' AS ZDID, '1726642931036001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642931036001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642931046011245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642931047004245' AS ID, 'updateBy' AS ZDID, '1726642931036001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642931036001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642931047004245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642931047005245' AS ID, 'updateTime' AS ZDID, '1726642931036001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642931036001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642931047005245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642931046013245' AS ID, 'zgh' AS ZDID, '1726642931036001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642931036001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642931046013245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642931046014245' AS ID, 'zt' AS ZDID, '1726642931036001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642931036001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642931046014245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642942509015245' AS ID, 'createBy' AS ZDID, '1726642942503001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642942503001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642942509015245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642942509016245' AS ID, 'createTime' AS ZDID, '1726642942503001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642942503001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642942509016245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642942509010245' AS ID, 'dzjf' AS ZDID, '1726642942503001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642942503001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642942509010245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642942509011245' AS ID, 'fj' AS ZDID, '1726642942503001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642942503001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642942509011245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642942509014245' AS ID, 'id' AS ZDID, '1726642942503001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642942503001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642942509014245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642942509005245' AS ID, 'kjxmlb' AS ZDID, '1726642942503001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642942503001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642942509005245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642942509008245' AS ID, 'lxjf' AS ZDID, '1726642942503001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642942503001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642942509008245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642942509017245' AS ID, 'updateBy' AS ZDID, '1726642942503001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642942503001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642942509017245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642942509018245' AS ID, 'updateTime' AS ZDID, '1726642942503001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642942503001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642942509018245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642942509009245' AS ID, 'wbjf' AS ZDID, '1726642942503001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642942503001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642942509009245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642942509001245' AS ID, 'xmbh' AS ZDID, '1726642942503001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642942503001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642942509001245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642942509007245' AS ID, 'xmjfze' AS ZDID, '1726642942503001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642942503001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642942509007245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642942509004245' AS ID, 'xmlb' AS ZDID, '1726642942503001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642942503001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642942509004245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642942509003245' AS ID, 'xmlx' AS ZDID, '1726642942503001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642942503001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642942509003245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642942509006245' AS ID, 'xmly' AS ZDID, '1726642942503001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642942503001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642942509006245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642942509002245' AS ID, 'xmmc' AS ZDID, '1726642942503001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642942503001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642942509002245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642942509012245' AS ID, 'zgh' AS ZDID, '1726642942503001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642942503001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642942509012245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642942509013245' AS ID, 'zt' AS ZDID, '1726642942503001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642942503001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642942509013245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642957374015245' AS ID, 'createBy' AS ZDID, '1726642957362001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642957362001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642957374015245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642957374016245' AS ID, 'createTime' AS ZDID, '1726642957362001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642957362001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642957374016245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642957374010245' AS ID, 'dzjf' AS ZDID, '1726642957362001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642957362001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642957374010245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642957374011245' AS ID, 'fj' AS ZDID, '1726642957362001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642957362001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642957374011245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642957374014245' AS ID, 'id' AS ZDID, '1726642957362001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642957362001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642957374014245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642957374005245' AS ID, 'kjxmlb' AS ZDID, '1726642957362001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642957362001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642957374005245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642957374008245' AS ID, 'lxjf' AS ZDID, '1726642957362001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642957362001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642957374008245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642957374017245' AS ID, 'updateBy' AS ZDID, '1726642957362001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642957362001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642957374017245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642957374018245' AS ID, 'updateTime' AS ZDID, '1726642957362001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642957362001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642957374018245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642957374009245' AS ID, 'wbjf' AS ZDID, '1726642957362001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642957362001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642957374009245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642957374001245' AS ID, 'xmbh' AS ZDID, '1726642957362001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642957362001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642957374001245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642957374007245' AS ID, 'xmjfze' AS ZDID, '1726642957362001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642957362001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642957374007245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642957374004245' AS ID, 'xmlb' AS ZDID, '1726642957362001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642957362001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642957374004245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642957374003245' AS ID, 'xmlx' AS ZDID, '1726642957362001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642957362001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642957374003245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642957374006245' AS ID, 'xmly' AS ZDID, '1726642957362001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642957362001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642957374006245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642957374002245' AS ID, 'xmmc' AS ZDID, '1726642957362001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642957362001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642957374002245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642957374012245' AS ID, 'zgh' AS ZDID, '1726642957362001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642957362001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642957374012245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642957374013245' AS ID, 'zt' AS ZDID, '1726642957362001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642957362001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642957374013245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642966673016245' AS ID, 'createBy' AS ZDID, '1726642966668001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642966668001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642966673016245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642966673017245' AS ID, 'createTime' AS ZDID, '1726642966668001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642966668001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642966673017245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642966673010245' AS ID, 'dwpm' AS ZDID, '1726642966668001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642966668001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642966673010245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642966673012245' AS ID, 'fj' AS ZDID, '1726642966668001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642966668001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642966673012245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642966673001245' AS ID, 'hjcglx' AS ZDID, '1726642966668001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642966668001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642966673001245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642966673007245' AS ID, 'hjdj' AS ZDID, '1726642966668001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642966668001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642966673007245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642966673006245' AS ID, 'hjjb' AS ZDID, '1726642966668001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642966668001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642966673006245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642966673009245' AS ID, 'hjje' AS ZDID, '1726642966668001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642966668001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642966673009245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642966673005245' AS ID, 'hjlb' AS ZDID, '1726642966668001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642966668001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642966673005245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642966673008245' AS ID, 'hjrq' AS ZDID, '1726642966668001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642966668001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642966673008245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642966673015245' AS ID, 'id' AS ZDID, '1726642966668001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642966668001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642966673015245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642966673004245' AS ID, 'jlmc' AS ZDID, '1726642966668001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642966668001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642966673004245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642966673002245' AS ID, 'kycgbh' AS ZDID, '1726642966668001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642966668001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642966673002245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642966673003245' AS ID, 'kycgmc' AS ZDID, '1726642966668001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642966668001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642966673003245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642966673011245' AS ID, 'smsx' AS ZDID, '1726642966668001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642966668001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642966673011245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642966673018245' AS ID, 'updateBy' AS ZDID, '1726642966668001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642966668001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642966673018245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642966673019245' AS ID, 'updateTime' AS ZDID, '1726642966668001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642966668001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642966673019245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642966673013245' AS ID, 'zgh' AS ZDID, '1726642966668001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642966668001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642966673013245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642966673014245' AS ID, 'zt' AS ZDID, '1726642966668001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642966668001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642966673014245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642982828015245' AS ID, 'createBy' AS ZDID, '1726642982822001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642982822001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642982828015245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642982828016245' AS ID, 'createTime' AS ZDID, '1726642982822001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642982822001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642982828016245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642982828010245' AS ID, 'dzjf' AS ZDID, '1726642982822001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642982822001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642982828010245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642982828011245' AS ID, 'fj' AS ZDID, '1726642982822001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642982822001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642982828011245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642982828014245' AS ID, 'id' AS ZDID, '1726642982822001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642982822001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642982828014245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642982828005245' AS ID, 'kjxmlb' AS ZDID, '1726642982822001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642982822001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642982828005245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642982828008245' AS ID, 'lxjf' AS ZDID, '1726642982822001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642982822001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642982828008245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642982828017245' AS ID, 'updateBy' AS ZDID, '1726642982822001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642982822001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642982828017245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642982828018245' AS ID, 'updateTime' AS ZDID, '1726642982822001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642982822001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642982828018245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642982828009245' AS ID, 'wbjf' AS ZDID, '1726642982822001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642982822001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642982828009245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642982828001245' AS ID, 'xmbh' AS ZDID, '1726642982822001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642982822001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642982828001245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642982828007245' AS ID, 'xmjfze' AS ZDID, '1726642982822001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642982822001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642982828007245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642982828004245' AS ID, 'xmlb' AS ZDID, '1726642982822001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642982822001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642982828004245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642982828003245' AS ID, 'xmlx' AS ZDID, '1726642982822001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642982822001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642982828003245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642982828006245' AS ID, 'xmly' AS ZDID, '1726642982822001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642982822001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642982828006245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642982828002245' AS ID, 'xmmc' AS ZDID, '1726642982822001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642982822001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642982828002245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642982828012245' AS ID, 'zgh' AS ZDID, '1726642982822001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642982822001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642982828012245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642982828013245' AS ID, 'zt' AS ZDID, '1726642982822001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642982822001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642982828013245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642990154016245' AS ID, 'createBy' AS ZDID, '1726642990149001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642990149001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642990154016245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642990154017245' AS ID, 'createTime' AS ZDID, '1726642990149001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642990149001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642990154017245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642990154010245' AS ID, 'dwpm' AS ZDID, '1726642990149001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642990149001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642990154010245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642990154012245' AS ID, 'fj' AS ZDID, '1726642990149001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642990149001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642990154012245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642990154001245' AS ID, 'hjcglx' AS ZDID, '1726642990149001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642990149001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642990154001245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642990154007245' AS ID, 'hjdj' AS ZDID, '1726642990149001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642990149001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642990154007245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642990154006245' AS ID, 'hjjb' AS ZDID, '1726642990149001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642990149001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642990154006245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642990154009245' AS ID, 'hjje' AS ZDID, '1726642990149001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642990149001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642990154009245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642990154005245' AS ID, 'hjlb' AS ZDID, '1726642990149001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642990149001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642990154005245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642990154008245' AS ID, 'hjrq' AS ZDID, '1726642990149001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642990149001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642990154008245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642990154015245' AS ID, 'id' AS ZDID, '1726642990149001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642990149001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642990154015245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642990154004245' AS ID, 'jlmc' AS ZDID, '1726642990149001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642990149001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642990154004245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642990154002245' AS ID, 'kycgbh' AS ZDID, '1726642990149001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642990149001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642990154002245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642990154003245' AS ID, 'kycgmc' AS ZDID, '1726642990149001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642990149001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642990154003245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642990154011245' AS ID, 'smsx' AS ZDID, '1726642990149001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642990149001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642990154011245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642990154018245' AS ID, 'updateBy' AS ZDID, '1726642990149001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642990149001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642990154018245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642990154019245' AS ID, 'updateTime' AS ZDID, '1726642990149001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642990149001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642990154019245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642990154013245' AS ID, 'zgh' AS ZDID, '1726642990149001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642990149001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642990154013245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726642990154014245' AS ID, 'zt' AS ZDID, '1726642990149001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726642990149001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726642990154014245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643010954015245' AS ID, 'createBy' AS ZDID, '1726643010949001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643010949001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643010954015245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643010954016245' AS ID, 'createTime' AS ZDID, '1726643010949001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643010949001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643010954016245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643010954010245' AS ID, 'dzjf' AS ZDID, '1726643010949001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643010949001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643010954010245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643010954011245' AS ID, 'fj' AS ZDID, '1726643010949001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643010949001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643010954011245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643010954014245' AS ID, 'id' AS ZDID, '1726643010949001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643010949001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643010954014245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643010954005245' AS ID, 'kjxmlb' AS ZDID, '1726643010949001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643010949001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643010954005245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643010954008245' AS ID, 'lxjf' AS ZDID, '1726643010949001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643010949001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643010954008245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643010954017245' AS ID, 'updateBy' AS ZDID, '1726643010949001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643010949001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643010954017245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643010954018245' AS ID, 'updateTime' AS ZDID, '1726643010949001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643010949001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643010954018245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643010954009245' AS ID, 'wbjf' AS ZDID, '1726643010949001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643010949001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643010954009245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643010954001245' AS ID, 'xmbh' AS ZDID, '1726643010949001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643010949001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643010954001245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643010954007245' AS ID, 'xmjfze' AS ZDID, '1726643010949001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643010949001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643010954007245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643010954004245' AS ID, 'xmlb' AS ZDID, '1726643010949001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643010949001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643010954004245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643010954003245' AS ID, 'xmlx' AS ZDID, '1726643010949001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643010949001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643010954003245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643010954006245' AS ID, 'xmly' AS ZDID, '1726643010949001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643010949001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643010954006245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643010954002245' AS ID, 'xmmc' AS ZDID, '1726643010949001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643010949001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643010954002245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643010954012245' AS ID, 'zgh' AS ZDID, '1726643010949001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643010949001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643010954012245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643010954013245' AS ID, 'zt' AS ZDID, '1726643010949001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643010949001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643010954013245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643018392005245' AS ID, 'createBy' AS ZDID, '1726643018387001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643018387001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643018392005245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643018392006245' AS ID, 'createTime' AS ZDID, '1726643018387001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643018387001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643018392006245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643018391010245' AS ID, 'dwpm' AS ZDID, '1726643018387001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643018387001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643018391010245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643018392001245' AS ID, 'fj' AS ZDID, '1726643018387001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643018387001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643018392001245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643018391001245' AS ID, 'hjcglx' AS ZDID, '1726643018387001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643018387001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643018391001245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643018391007245' AS ID, 'hjdj' AS ZDID, '1726643018387001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643018387001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643018391007245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643018391006245' AS ID, 'hjjb' AS ZDID, '1726643018387001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643018387001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643018391006245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643018391009245' AS ID, 'hjje' AS ZDID, '1726643018387001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643018387001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643018391009245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643018391005245' AS ID, 'hjlb' AS ZDID, '1726643018387001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643018387001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643018391005245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643018391008245' AS ID, 'hjrq' AS ZDID, '1726643018387001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643018387001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643018391008245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643018392004245' AS ID, 'id' AS ZDID, '1726643018387001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643018387001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643018392004245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643018391004245' AS ID, 'jlmc' AS ZDID, '1726643018387001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643018387001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643018391004245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643018391002245' AS ID, 'kycgbh' AS ZDID, '1726643018387001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643018387001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643018391002245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643018391003245' AS ID, 'kycgmc' AS ZDID, '1726643018387001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643018387001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643018391003245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643018391011245' AS ID, 'smsx' AS ZDID, '1726643018387001245' AS MXSQID, '4' AS QXLX, '0' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643018387001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643018391011245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643018392007245' AS ID, 'updateBy' AS ZDID, '1726643018387001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643018387001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643018392007245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643018392008245' AS ID, 'updateTime' AS ZDID, '1726643018387001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643018387001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643018392008245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643018392002245' AS ID, 'zgh' AS ZDID, '1726643018387001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643018387001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643018392002245');
INSERT INTO T_JSSJZX_YHZZDSQ (ID, ZDID, MXSQID, QXLX, SFBT) SELECT '1726643018392003245' AS ID, 'zt' AS ZDID, '1726643018387001245' AS MXSQID, '1' AS QXLX, '1' AS SFBT FROM (SELECT COUNT(1) FROM T_JSSJZX_YHZZDSQ WHERE 1 = 2) t WHERE EXISTS (SELECT 1 FROM T_JSSJZX_YHZMXSQ a WHERE a.ID = '1726643018387001245') and NOT EXISTS (SELECT 1 FROM T_JSSJZX_YHZZDSQ b WHERE b.ID = '1726643018392003245');


-- 教师模型分组初始化

insert into t_szsz_modelgroup (ID, CODE, CLASSIFY_CODE, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
values ('1', 'teacher', 'rsxx', null, null, null, null);

insert into t_szsz_modelgroup (ID, CODE, CLASSIFY_CODE, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
values ('2', 'teacher', 'jbxx', null, null, null, null);

insert into t_szsz_modelgroup (ID, CODE, CLASSIFY_CODE, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
values ('3', 'teacher', 'kyxx', null, null, null, null);

insert into t_szsz_modelgroup (ID, CODE, CLASSIFY_CODE, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
values ('4', 'teacher', 'jyjx', null, null, null, null);

update LOWCODE_MENU set ROUTER_PARAMS='{"appCode":"main","pageCode":"dataModelManage","modelGroup":"teacher"}' where MENU_ID='1721706971042680832';

-- 数据权限初始化脚本
DELETE FROM LOWCODE_AUTH_DATA_PERMISSION WHERE ID='1698913733347000001481';
INSERT INTO LOWCODE_AUTH_DATA_PERMISSION
(ID, DATA_AUTH_ID, SUB_SQL, DATA_ROLE_ID, USER_TYPE, BUSINESS_DOMAIN, SQL_TYPE, DATA_AUTH_TYPE, APP, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES('1698913733347000001481', 'BIZ_TEACHER_', 'all', '10001', NULL, 'da', 'all', 'pre', NULL, NULL, NULL, NULL, NULL);


DELETE FROM LOWCODE_AUTH_DATA_PERMISSION WHERE ID='1698913733347000001482';
INSERT INTO LOWCODE_AUTH_DATA_PERMISSION
(ID, DATA_AUTH_ID, SUB_SQL, DATA_ROLE_ID, USER_TYPE, BUSINESS_DOMAIN, SQL_TYPE, DATA_AUTH_TYPE, APP, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES('1698913733347000001482', 'EXT_TEACHER_', 'all', '10001', NULL, 'da', 'all', 'pre', NULL, NULL, NULL, NULL, NULL);

DELETE FROM LOWCODE_AUTH_DATA_PERMISSION WHERE ID='1698913710802000001481';
INSERT INTO LOWCODE_AUTH_DATA_PERMISSION
(ID, DATA_AUTH_ID, SUB_SQL, DATA_ROLE_ID, USER_TYPE, BUSINESS_DOMAIN, SQL_TYPE, DATA_AUTH_TYPE, APP, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES('1698913710802000001481', 'BIZ_TEACHER', 'all', '10001', NULL, 'da', 'else', NULL, NULL, NULL, NULL, NULL, NULL);

DELETE FROM LOWCODE_AUTH_DATA_PERMISSION WHERE ID='1698997998664000001481';
INSERT INTO LOWCODE_AUTH_DATA_PERMISSION
(ID, DATA_AUTH_ID, SUB_SQL, DATA_ROLE_ID, USER_TYPE, BUSINESS_DOMAIN, SQL_TYPE, DATA_AUTH_TYPE, APP, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES('1698997998664000001481', 'BIZ_TEACHER', 'all', '10001', NULL, 'da', 'insert', NULL, NULL, NULL, NULL, NULL, NULL);

DELETE FROM LOWCODE_AUTH_DATA_PERMISSION WHERE ID='1698998019719000001482';
INSERT INTO LOWCODE_AUTH_DATA_PERMISSION
(ID, DATA_AUTH_ID, SUB_SQL, DATA_ROLE_ID, USER_TYPE, BUSINESS_DOMAIN, SQL_TYPE, DATA_AUTH_TYPE, APP, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES('1698998019719000001482', 'EXT_TEACHER_', 'all', '10001', NULL, 'da', 'insert', 'pre', NULL, NULL, NULL, NULL, NULL);

DELETE FROM LOWCODE_AUTH_DATA_PERMISSION WHERE ID='1698998019719000001481';
INSERT INTO LOWCODE_AUTH_DATA_PERMISSION
(ID, DATA_AUTH_ID, SUB_SQL, DATA_ROLE_ID, USER_TYPE, BUSINESS_DOMAIN, SQL_TYPE, DATA_AUTH_TYPE, APP, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES('1698998019719000001481', 'BIZ_TEACHER_', 'all', '10001', NULL, 'da', 'insert', 'pre', NULL, NULL, NULL, NULL, NULL);

DELETE FROM LOWCODE_AUTH_DATA_PERMISSION WHERE ID='1701221496124000001622';
INSERT INTO LOWCODE_AUTH_DATA_PERMISSION
(ID, DATA_AUTH_ID, SUB_SQL, DATA_ROLE_ID, USER_TYPE, BUSINESS_DOMAIN, SQL_TYPE, DATA_AUTH_TYPE, APP, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES('1701221496124000001622', 'BIZ_TEACHER_', 'SELECT count(1) FROM biz_department d JOIN LOWCODE_AUTH_PARTMENT_POSITION L ON (L.DEPARTMENT_ID = d.department_code OR d.owner_department = L.DEPARTMENT_ID) WHERE L.user_id = @curUserId AND L.data_role_id = ''20001'' AND L.role_id = @curRoleId AND L.department_id IN (SELECT department_code FROM BIZ_TEACHER WHERE zgh =##zgh##)', '20001', NULL, 'da', 'insert', 'pre', NULL, NULL, NULL, NULL, NULL);

DELETE FROM LOWCODE_AUTH_DATA_PERMISSION WHERE ID='1701221496124000001623';
INSERT INTO LOWCODE_AUTH_DATA_PERMISSION
(ID, DATA_AUTH_ID, SUB_SQL, DATA_ROLE_ID, USER_TYPE, BUSINESS_DOMAIN, SQL_TYPE, DATA_AUTH_TYPE, APP, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES('1701221496124000001623', 'EXT_TEACHER_', 'select count(1) from biz_department d join LOWCODE_AUTH_PARTMENT_POSITION L on (L.DEPARTMENT_ID=d.department_code or d.owner_department=L.DEPARTMENT_ID) where L.user_id=@curUserId and L.data_role_id=''20001'' and L.role_id=@curRoleId and L.department_id in (select department_code from BIZ_TEACHER where zgh=##zgh##)', '20001', NULL, 'da', 'insert', 'pre', NULL, NULL, NULL, NULL, NULL);

DELETE FROM LOWCODE_AUTH_DATA_PERMISSION WHERE ID='1701221522036000001622';
INSERT INTO LOWCODE_AUTH_DATA_PERMISSION
(ID, DATA_AUTH_ID, SUB_SQL, DATA_ROLE_ID, USER_TYPE, BUSINESS_DOMAIN, SQL_TYPE, DATA_AUTH_TYPE, APP, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES('1701221522036000001622', 'biz_teacher', 'select count(1) from (select 1 from biz_department d join LOWCODE_AUTH_PARTMENT_POSITION L on (L.DEPARTMENT_ID=d.department_code or d.owner_department=L.DEPARTMENT_ID) where L.user_id=@curUserId and L.data_role_id=''20001'' and L.role_id=@curRoleId and L.department_id=##departmentCode## union all select 1 from biz_teacher where zgh=@curUserId) t', '20001', NULL, 'da', 'insert', NULL, NULL, NULL, NULL, NULL, NULL);

DELETE FROM LOWCODE_AUTH_DATA_PERMISSION WHERE ID='1698998038827000001481';
INSERT INTO LOWCODE_AUTH_DATA_PERMISSION
(ID, DATA_AUTH_ID, SUB_SQL, DATA_ROLE_ID, USER_TYPE, BUSINESS_DOMAIN, SQL_TYPE, DATA_AUTH_TYPE, APP, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES('1698998038827000001481', 'BIZ_TEACHER', 'select 0 from t_empty', '20006', NULL, 'da', 'insert', NULL, NULL, NULL, NULL, NULL, NULL);

DELETE FROM LOWCODE_AUTH_DATA_PERMISSION WHERE ID='1698998057254000001482';
INSERT INTO LOWCODE_AUTH_DATA_PERMISSION
(ID, DATA_AUTH_ID, SUB_SQL, DATA_ROLE_ID, USER_TYPE, BUSINESS_DOMAIN, SQL_TYPE, DATA_AUTH_TYPE, APP, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES('1698998057254000001482', 'EXT_TEACHER_', 'select count(1) from LOWCODE_AUTH_PARTMENT_POSITION where user_id=@curUserId and data_role_id=''20006'' and role_id=@curRoleId and department_id in (select department_code from BIZ_TEACHER where zgh=##zgh##)', '20006', NULL, 'da', 'insert', 'pre', NULL, NULL, NULL, NULL, NULL);

DELETE FROM LOWCODE_AUTH_DATA_PERMISSION WHERE ID='1698998057254000001481';
INSERT INTO LOWCODE_AUTH_DATA_PERMISSION
(ID, DATA_AUTH_ID, SUB_SQL, DATA_ROLE_ID, USER_TYPE, BUSINESS_DOMAIN, SQL_TYPE, DATA_AUTH_TYPE, APP, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES('1698998057254000001481', 'BIZ_TEACHER_', 'select count(1) from LOWCODE_AUTH_PARTMENT_POSITION where user_id=@curUserId and data_role_id=''20006'' and role_id=@curRoleId and department_id in (select department_code from BIZ_TEACHER where zgh=##zgh##)', '20006', NULL, 'da', 'insert', 'pre', NULL, NULL, NULL, NULL, NULL);

DELETE FROM LOWCODE_AUTH_DATA_PERMISSION WHERE ID='1698991107138000001481';
INSERT INTO LOWCODE_AUTH_DATA_PERMISSION
(ID, DATA_AUTH_ID, SUB_SQL, DATA_ROLE_ID, USER_TYPE, BUSINESS_DOMAIN, SQL_TYPE, DATA_AUTH_TYPE, APP, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES('1698991107138000001481', 'BIZ_TEACHER_', '@tableName.zgh=@curUserId', '80001', NULL, 'da', 'else', 'pre', NULL, NULL, NULL, NULL, NULL);

DELETE FROM LOWCODE_AUTH_DATA_PERMISSION WHERE ID='1698991107138000001482';
INSERT INTO LOWCODE_AUTH_DATA_PERMISSION
(ID, DATA_AUTH_ID, SUB_SQL, DATA_ROLE_ID, USER_TYPE, BUSINESS_DOMAIN, SQL_TYPE, DATA_AUTH_TYPE, APP, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES('1698991107138000001482', 'EXT_TEACHER_', '@tableName.zgh=@curUserId', '80001', NULL, 'da', 'else', 'pre', NULL, NULL, NULL, NULL, NULL);

DELETE FROM LOWCODE_AUTH_DATA_PERMISSION WHERE ID='1703561062975001472';
INSERT INTO LOWCODE_AUTH_DATA_PERMISSION
(ID, DATA_AUTH_ID, SUB_SQL, DATA_ROLE_ID, USER_TYPE, BUSINESS_DOMAIN, SQL_TYPE, DATA_AUTH_TYPE, APP, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES('1703561062975001472', 'BIZ_TEACHER', '@tableName.zgh=@curUserId', '80001', NULL, 'da', 'else', NULL, NULL, NULL, NULL, NULL, NULL);

DELETE FROM LOWCODE_AUTH_DATA_PERMISSION WHERE ID='1698998101302000001481';
INSERT INTO LOWCODE_AUTH_DATA_PERMISSION
(ID, DATA_AUTH_ID, SUB_SQL, DATA_ROLE_ID, USER_TYPE, BUSINESS_DOMAIN, SQL_TYPE, DATA_AUTH_TYPE, APP, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES('1698998101302000001481', 'BIZ_TEACHER_', 'select count(1) from biz_teacher where @curUserId=##zgh## and zgh=@curUserId', '80001', NULL, 'da', 'insert', 'pre', NULL, NULL, NULL, NULL, NULL);

DELETE FROM LOWCODE_AUTH_DATA_PERMISSION WHERE ID='1698998101302000001482';
INSERT INTO LOWCODE_AUTH_DATA_PERMISSION
(ID, DATA_AUTH_ID, SUB_SQL, DATA_ROLE_ID, USER_TYPE, BUSINESS_DOMAIN, SQL_TYPE, DATA_AUTH_TYPE, APP, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES('1698998101302000001482', 'EXT_TEACHER_', 'select count(1) from biz_teacher where @curUserId=##zgh## and zgh=@curUserId', '80001', NULL, 'da', 'insert', 'pre', NULL, NULL, NULL, NULL, NULL);




DELETE FROM LOWCODE_AUTH_DATA_PERMISSION WHERE ID='1698979339762000001481';
INSERT INTO LOWCODE_AUTH_DATA_PERMISSION
(ID, DATA_AUTH_ID, SUB_SQL, DATA_ROLE_ID, USER_TYPE, BUSINESS_DOMAIN, SQL_TYPE, DATA_AUTH_TYPE, APP, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES('1698979339762000001481', 'BIZ_TEACHER_', '(EXISTS (SELECT 1 FROM BIZ_TEACHER WHERE @tableName.zgh=BIZ_TEACHER.zgh AND department_code IN (SELECT biz_department.department_code FROM biz_department JOIN LOWCODE_AUTH_PARTMENT_POSITION ON (LOWCODE_AUTH_PARTMENT_POSITION.department_id = biz_department.department_code OR biz_department.owner_department = LOWCODE_AUTH_PARTMENT_POSITION.DEPARTMENT_ID) WHERE user_id = @curUserId AND data_role_id = ''20001'' AND role_id = @curRoleId)) or @tableName.zgh=@curUserId)', '20001', NULL, 'da', 'else', 'pre', NULL, NULL, NULL, NULL, NULL);


DELETE FROM LOWCODE_AUTH_DATA_PERMISSION WHERE ID='1698979339762000001482';
INSERT INTO LOWCODE_AUTH_DATA_PERMISSION
(ID, DATA_AUTH_ID, SUB_SQL, DATA_ROLE_ID, USER_TYPE, BUSINESS_DOMAIN, SQL_TYPE, DATA_AUTH_TYPE, APP, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES('1698979339762000001482', 'EXT_TEACHER_', '(EXISTS (SELECT 1 FROM BIZ_TEACHER WHERE @tableName.zgh=BIZ_TEACHER.zgh AND department_code IN (SELECT biz_department.department_code FROM biz_department JOIN LOWCODE_AUTH_PARTMENT_POSITION ON (LOWCODE_AUTH_PARTMENT_POSITION.department_id = biz_department.department_code OR biz_department.owner_department = LOWCODE_AUTH_PARTMENT_POSITION.DEPARTMENT_ID) WHERE user_id = @curUserId AND data_role_id = ''20001'' AND role_id = @curRoleId))  or @tableName.zgh=@curUserId)', '20001', NULL, 'da', 'else', 'pre', NULL, NULL, NULL, NULL, NULL);


DELETE FROM LOWCODE_AUTH_DATA_PERMISSION WHERE ID='1701181633426000001622';
INSERT INTO LOWCODE_AUTH_DATA_PERMISSION
(ID, DATA_AUTH_ID, SUB_SQL, DATA_ROLE_ID, USER_TYPE, BUSINESS_DOMAIN, SQL_TYPE, DATA_AUTH_TYPE, APP, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES('1701181633426000001622', 'biz_teacher', '(@tableName.department_code in (select biz_department.department_code from biz_department join LOWCODE_AUTH_PARTMENT_POSITION on (LOWCODE_AUTH_PARTMENT_POSITION.department_id=biz_department.department_code or biz_department.owner_department=LOWCODE_AUTH_PARTMENT_POSITION.DEPARTMENT_ID) where user_id=@curUserId and data_role_id=''20001'' and role_id=@curRoleId) or @tableName.zgh=@curUserId)', '20001', NULL, 'da', 'else', NULL, NULL, NULL, NULL, NULL, NULL);


DELETE FROM LOWCODE_AUTH_DATA_PERMISSION WHERE ID='1698914078446000001481';
INSERT INTO LOWCODE_AUTH_DATA_PERMISSION
(ID, DATA_AUTH_ID, SUB_SQL, DATA_ROLE_ID, USER_TYPE, BUSINESS_DOMAIN, SQL_TYPE, DATA_AUTH_TYPE, APP, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES('1698914078446000001481', 'BIZ_TEACHER', '(@tableName.DEPARTMENT_CODE IN (SELECT department_id FROM LOWCODE_AUTH_PARTMENT_POSITION where user_id=@curUserId and data_role_id=''20001'' and role_id=@curRoleId) or @tableName.zgh=@curUserId)', '20006', NULL, 'da', 'else', NULL, NULL, NULL, NULL, NULL, NULL);


DELETE FROM LOWCODE_AUTH_DATA_PERMISSION WHERE ID='1701181868508000001623';
INSERT INTO LOWCODE_AUTH_DATA_PERMISSION
(ID, DATA_AUTH_ID, SUB_SQL, DATA_ROLE_ID, USER_TYPE, BUSINESS_DOMAIN, SQL_TYPE, DATA_AUTH_TYPE, APP, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES('1701181868508000001623', 'EXT_TEACHER_', '(EXISTS (SELECT 1 FROM BIZ_TEACHER WHERE @tableName.zgh=BIZ_TEACHER.zgh AND department_code IN (SELECT department_id FROM LOWCODE_AUTH_PARTMENT_POSITION WHERE user_id = @curUserId AND data_role_id = ''20006'' AND role_id = @curRoleId)) or @tableName.zgh=@curUserId)', '20006', NULL, 'da', 'else', 'pre', NULL, NULL, NULL, NULL, NULL);


DELETE FROM LOWCODE_AUTH_DATA_PERMISSION WHERE ID='1701181868508000001622';
INSERT INTO LOWCODE_AUTH_DATA_PERMISSION
(ID, DATA_AUTH_ID, SUB_SQL, DATA_ROLE_ID, USER_TYPE, BUSINESS_DOMAIN, SQL_TYPE, DATA_AUTH_TYPE, APP, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)
VALUES('1701181868508000001622', 'BIZ_TEACHER_', '(EXISTS (SELECT 1 FROM BIZ_TEACHER WHERE @tableName.zgh=BIZ_TEACHER.zgh AND department_code IN (SELECT department_id FROM LOWCODE_AUTH_PARTMENT_POSITION WHERE user_id = @curUserId AND data_role_id = ''20006'' AND role_id = @curRoleId)) or @tableName.zgh=@curUserId)', '20006', NULL, 'da', 'else', 'pre', NULL, NULL, NULL, NULL, NULL);

