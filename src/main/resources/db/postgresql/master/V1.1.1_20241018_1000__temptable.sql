-- 创建temp中间表，用作数据同步
create table biz_teacher_temp as select * from biz_teacher where 1 = 2;
create table biz_teacher_gccrcxx_temp as select * from biz_teacher_gccrcxx where 1 = 2;
create table biz_teacher_kchj_temp as select * from biz_teacher_kchj where 1 = 2;
create table biz_teacher_zdxkjs_temp as select * from biz_teacher_zdxkjs where 1 = 2;
create table biz_teacher_jxcghjxx_temp as select * from biz_teacher_jxcghjxx where 1 = 2;
create table biz_teacher_jxcg_temp as select * from biz_teacher_jxcg where 1 = 2;
create table biz_teacher_lw_temp as select * from biz_teacher_lw where 1 = 2;
create table biz_teacher_jsjrjzzq_temp as select * from biz_teacher_jsjrjzzq where 1 = 2;
create table biz_teacher_kyxm_temp as select * from biz_teacher_kyxm where 1 = 2;
create table biz_teacher_jgxm_temp as select * from biz_teacher_jgxm where 1 = 2;
create table biz_teacher_jxbshjxx_temp as select * from biz_teacher_jxbshjxx where 1 = 2;
create table biz_teacher_skjl_temp as select * from biz_teacher_skjl where 1 = 2;
create table biz_teacher_zz_temp as select * from biz_teacher_zz where 1 = 2;
create table biz_teacher_zdbzks_temp as select * from biz_teacher_zdbzks where 1 = 2;
create table biz_teacher_jxgzl_temp as select * from biz_teacher_jxgzl where 1 = 2;
create table biz_teacher_zdyjs_temp as select * from biz_teacher_zdyjs where 1 = 2;
create table biz_teacher_zl_temp as select * from biz_teacher_zl where 1 = 2;
create table biz_teacher_jtcy_temp as select * from biz_teacher_jtcy where 1 = 2;
create table biz_teacher_zzxx_temp as select * from biz_teacher_zzxx where 1 = 2;
create table biz_teacher_cgxx_temp as select * from biz_teacher_cgxx where 1 = 2;
create table biz_teacher_ccxx_temp as select * from biz_teacher_ccxx where 1 = 2;
create table biz_teacher_khjl_temp as select * from biz_teacher_khjl where 1 = 2;
create table biz_teacher_hwyx_temp as select * from biz_teacher_hwyx where 1 = 2;
create table biz_teacher_gnjx_temp as select * from biz_teacher_gnjx where 1 = 2;
create table biz_teacher_gzjl_temp as select * from biz_teacher_gzjl where 1 = 2;
create table biz_teacher_shjzxx_temp as select * from biz_teacher_shjzxx where 1 = 2;
create table biz_teacher_gnpx_temp as select * from biz_teacher_gnpx where 1 = 2;
create table biz_teacher_gwdjprjl_temp as select * from biz_teacher_gwdjprjl where 1 = 2;
create table biz_teacher_jxpjjg_temp as select * from biz_teacher_jxpjjg where 1 = 2;
create table biz_teacher_jljryxx_temp as select * from biz_teacher_jljryxx where 1 = 2;
create table biz_teacher_zcppjl_temp as select * from biz_teacher_zcppjl where 1 = 2;
create table biz_teacher_bshxx_temp as select * from biz_teacher_bshxx where 1 = 2;
create table biz_teacher_jyjl_temp as select * from biz_teacher_jyjl where 1 = 2;
create table biz_teacher_gwprjl_temp as select * from biz_teacher_gwprjl where 1 = 2;
create table biz_teacher_xndd_temp as select * from biz_teacher_xndd where 1 = 2;
create table biz_teacher_bsjzxx_temp as select * from biz_teacher_bsjzxx where 1 = 2;
create table biz_teacher_gbzwxx_temp as select * from biz_teacher_gbzwxx where 1 = 2;
create table biz_teacher_xsqkjzxx_temp as select * from biz_teacher_xsqkjzxx where 1 = 2;
create table biz_teacher_xsttjzxx_temp as select * from biz_teacher_xsttjzxx where 1 = 2;
create table biz_teacher_jxgzlhz_temp as select * from biz_teacher_jxgzlhz where 1 = 2;



comment on table biz_teacher_temp is '教师基本信息TEMP';
comment on column biz_teacher_temp.id is 'ID';
comment on column biz_teacher_temp.zgh is '教职工号';
comment on column biz_teacher_temp.teacher_name is '姓名';
comment on column biz_teacher_temp.english_name is '姓名拼音';
comment on column biz_teacher_temp.idcard_number is '身份证件号';
comment on column biz_teacher_temp.census_register is '户口所在地';
comment on column biz_teacher_temp.source is '教职工来源';
comment on column biz_teacher_temp.begin_teach_date is '从教年月';
comment on column biz_teacher_temp.arrive_date is '来校年月';
comment on column biz_teacher_temp.teacher_category is '教职工类别';
comment on column biz_teacher_temp.category is '编制类别';
comment on column biz_teacher_temp.employment_form is '用人方式';
comment on column biz_teacher_temp.parttime_category is '兼职教师聘任类别';
comment on column biz_teacher_temp.is_advisor is '是否导师';
comment on column biz_teacher_temp.advisor_category is '导师类别';
comment on column biz_teacher_temp.advisor_date is '导师聘任年月';
comment on column biz_teacher_temp.is_instructor is '是否辅导员';
comment on column biz_teacher_temp.instructor_category is '辅导员类别';
comment on column biz_teacher_temp.person_identity is '个人身份';
comment on column biz_teacher_temp.is_double_teach is '是否双师型教师';
comment on column biz_teacher_temp.is_double_shoulder is '是否双肩挑';
comment on column biz_teacher_temp.subject_division is '学科门类';
comment on column biz_teacher_temp.first_subject is '一级学科';
comment on column biz_teacher_temp.second_subject is '二级学科';
comment on column biz_teacher_temp.research_direction is '研究方向';
comment on column biz_teacher_temp.leave_date is '离校日期';
comment on column biz_teacher_temp.picture is '照片';
comment on column biz_teacher_temp.blood_type_code is '血型';
comment on column biz_teacher_temp.health is '健康状况';
comment on column biz_teacher_temp.bank_card_number is '银行卡号';
comment on column biz_teacher_temp.bank_name is '银行卡开户行';
comment on column biz_teacher_temp.card_number is '一卡通卡号';
comment on column biz_teacher_temp.net_account is '网络账号';
comment on column biz_teacher_temp.email_account is '邮箱账号';
comment on column biz_teacher_temp.mobile is '手机号码';
comment on column biz_teacher_temp.email is '电子信箱';
comment on column biz_teacher_temp.telephone is '联系电话';
comment on column biz_teacher_temp.concat_in_school is '在校通讯地址';
comment on column biz_teacher_temp.postcode_in_school is '在校邮政编码';
comment on column biz_teacher_temp.house_address is '家庭地址';
comment on column biz_teacher_temp.house_postcode is '家庭邮政编码';
comment on column biz_teacher_temp.house_telephone is '家庭电话';
comment on column biz_teacher_temp.urgent_concat is '紧急联系人姓名';
comment on column biz_teacher_temp.urgent_mobile is '紧急联系人手机号码';
comment on column biz_teacher_temp.expected_retirement_date is '预计退休日期';
comment on column biz_teacher_temp.professional_position_level is '专业技术职务级别';
comment on column biz_teacher_temp.is_professional_post is '是否专业技术岗位';
comment on column biz_teacher_temp.professional_post_level is '专业技术岗位等级';
comment on column biz_teacher_temp.is_manage_post is '是否管理岗位';
comment on column biz_teacher_temp.manage_post_level is '管理岗位等级';
comment on column biz_teacher_temp.is_rearservice_post is '是否工勤岗位';
comment on column biz_teacher_temp.rearservice_post_level is '工勤岗位等级';
comment on column biz_teacher_temp.main_post_type is '主要岗位类型';
comment on column biz_teacher_temp.cadre_post is '干部职务';
comment on column biz_teacher_temp.cadre_post_level is '干部职务级别';
comment on column biz_teacher_temp.post_name is '岗位名称';
comment on column biz_teacher_temp.last_degree is '最高学位';
comment on column biz_teacher_temp.last_education is '最高学历';
comment on column biz_teacher_temp.last_graduation_school is '最后毕业院校';
comment on column biz_teacher_temp.from_current_school is '是否本校毕业';
comment on column biz_teacher_temp.last_graduation_school_type is '最后毕业院校类型';
comment on column biz_teacher_temp.overseas_experience is '海外经历';
comment on column biz_teacher_temp.begin_work_date is '参加工作年月';
comment on column biz_teacher_temp.archive_number is '档案编号';
comment on column biz_teacher_temp.used_name is '曾用名';
comment on column biz_teacher_temp.religion_code is '宗教信仰';
comment on column biz_teacher_temp.status is '教职工当前状态';
comment on column biz_teacher_temp.birth_date is '出生日期';
comment on column biz_teacher_temp.idcard_type_code is '身份证件类型';
comment on column biz_teacher_temp.original_birth_place is '籍贯';
comment on column biz_teacher_temp.gender_code is '性别';
comment on column biz_teacher_temp.professional_position is '专业技术职务';
comment on column biz_teacher_temp.department_code is '所属机构';
comment on column biz_teacher_temp.nation_code is '民族';
comment on column biz_teacher_temp.political_code is '政治面貌';
comment on column biz_teacher_temp.country_code is '国家地区';
comment on column biz_teacher_temp.birth_place is '出生地';
comment on column biz_teacher_temp.marital_code is '婚姻状况';
comment on column biz_teacher_temp.gatq is '港澳台侨外';
comment on column biz_teacher_temp.is_parttime is '是否兼职教师';
comment on column biz_teacher_temp.create_by is '创建人';
comment on column biz_teacher_temp.create_time is '创建时间';
comment on column biz_teacher_temp.update_by is '更新人';
comment on column biz_teacher_temp.update_time is '更新时间';
comment on column biz_teacher_temp.zt is '状态';


comment on table BIZ_TEACHER_BSHXX_TEMP is '博士后信息TEMP';
comment on column BIZ_TEACHER_BSHXX_TEMP.id is 'ID';
comment on column BIZ_TEACHER_BSHXX_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_BSHXX_TEMP.bshqgtybh is '博士后全国统一编号';
comment on column BIZ_TEACHER_BSHXX_TEMP.ldz is '流动站';
comment on column BIZ_TEACHER_BSHXX_TEMP.jzrq is '进站日期';
comment on column BIZ_TEACHER_BSHXX_TEMP.bdrq is '报到日期';
comment on column BIZ_TEACHER_BSHXX_TEMP.lhpyqy is '联合培养企业';
comment on column BIZ_TEACHER_BSHXX_TEMP.lhpyfjnrq is '联合培养费缴纳日期';
comment on column BIZ_TEACHER_BSHXX_TEMP.lhpyfje is '联合培养费金额';
comment on column BIZ_TEACHER_BSHXX_TEMP.czrq is '出站日期';
comment on column BIZ_TEACHER_BSHXX_TEMP.tzrq is '退站日期';
comment on column BIZ_TEACHER_BSHXX_TEMP.yqhczrq is '延期后出站日期';
comment on column BIZ_TEACHER_BSHXX_TEMP.hzdszgh is '合作导师职工号';
comment on column BIZ_TEACHER_BSHXX_TEMP.czqx is '出站去向';
comment on column BIZ_TEACHER_BSHXX_TEMP.zt is '状态';
comment on column BIZ_TEACHER_BSHXX_TEMP.zlxs is '招录形式';
comment on column BIZ_TEACHER_BSHXX_TEMP.bshdqzt is '博士后当前状态';
comment on column BIZ_TEACHER_BSHXX_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_BSHXX_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_BSHXX_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_BSHXX_TEMP.update_time is '更新时间';


comment on table BIZ_TEACHER_BSJZXX_TEMP is '比赛兼职信息TEMP';
comment on column BIZ_TEACHER_BSJZXX_TEMP.id is 'ID';
comment on column BIZ_TEACHER_BSJZXX_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_BSJZXX_TEMP.bsmc is '比赛名称';
comment on column BIZ_TEACHER_BSJZXX_TEMP.bsqsrq is '比赛起始日期';
comment on column BIZ_TEACHER_BSJZXX_TEMP.bsjsrq is '比赛结束日期';
comment on column BIZ_TEACHER_BSJZXX_TEMP.zt is '状态';
comment on column BIZ_TEACHER_BSJZXX_TEMP.bslx is '比赛类型';
comment on column BIZ_TEACHER_BSJZXX_TEMP.bsjb is '比赛级别';
comment on column BIZ_TEACHER_BSJZXX_TEMP.drzw is '担任职务';
comment on column BIZ_TEACHER_BSJZXX_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_BSJZXX_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_BSJZXX_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_BSJZXX_TEMP.update_time is '更新时间';


comment on table BIZ_TEACHER_CCXX_TEMP is '教职工惩处信息TEMP';
comment on column BIZ_TEACHER_CCXX_TEMP.id is 'ID';
comment on column BIZ_TEACHER_CCXX_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_CCXX_TEMP.ccyy is '惩处原因';
comment on column BIZ_TEACHER_CCXX_TEMP.ccnr is '惩处内容';
comment on column BIZ_TEACHER_CCXX_TEMP.ccdw is '惩处单位';
comment on column BIZ_TEACHER_CCXX_TEMP.ccwh is '惩处文号';
comment on column BIZ_TEACHER_CCXX_TEMP.ccrq is '惩处日期';
comment on column BIZ_TEACHER_CCXX_TEMP.cfcxrq is '处分撤销日期';
comment on column BIZ_TEACHER_CCXX_TEMP.cfcxwh is '处分撤销文号';
comment on column BIZ_TEACHER_CCXX_TEMP.cfcxyy is '处分撤销原因';
comment on column BIZ_TEACHER_CCXX_TEMP.zt is '状态';
comment on column BIZ_TEACHER_CCXX_TEMP.jlcf is '纪律处分';
comment on column BIZ_TEACHER_CCXX_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_CCXX_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_CCXX_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_CCXX_TEMP.update_time is '更新时间';


comment on table BIZ_TEACHER_CGXX_TEMP is '教职工出国信息TEMP';
comment on column BIZ_TEACHER_CGXX_TEMP.id is 'ID';
comment on column BIZ_TEACHER_CGXX_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_CGXX_TEMP.sqdwywmc is '所去单位英文名称';
comment on column BIZ_TEACHER_CGXX_TEMP.sqdwzwmc is '所去单位中文名称';
comment on column BIZ_TEACHER_CGXX_TEMP.tzmc is '团组名称';
comment on column BIZ_TEACHER_CGXX_TEMP.spdw is '审批单位';
comment on column BIZ_TEACHER_CGXX_TEMP.sprq is '审批日期';
comment on column BIZ_TEACHER_CGXX_TEMP.spwh is '审批文号';
comment on column BIZ_TEACHER_CGXX_TEMP.xxgznr is '学习工作内容';
comment on column BIZ_TEACHER_CGXX_TEMP.hzhhtxzh is '护照号或通行证号';
comment on column BIZ_TEACHER_CGXX_TEMP.cgrq is '出国（境）日期';
comment on column BIZ_TEACHER_CGXX_TEMP.hgrq is '回国日期';
comment on column BIZ_TEACHER_CGXX_TEMP.xxgzcj is '学习工作成绩';
comment on column BIZ_TEACHER_CGXX_TEMP.xxcdfy is '学校承担费用';
comment on column BIZ_TEACHER_CGXX_TEMP.zt is '状态';
comment on column BIZ_TEACHER_CGXX_TEMP.cggb is '出国（境）国别';
comment on column BIZ_TEACHER_CGXX_TEMP.jfly is '经费来源';
comment on column BIZ_TEACHER_CGXX_TEMP.cgmd is '出国（境）目的';
comment on column BIZ_TEACHER_CGXX_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_CGXX_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_CGXX_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_CGXX_TEMP.update_time is '更新时间';


comment on table BIZ_TEACHER_GBZWXX_TEMP is '干部职务信息TEMP';
comment on column BIZ_TEACHER_GBZWXX_TEMP.id is 'ID';
comment on column BIZ_TEACHER_GBZWXX_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_GBZWXX_TEMP.zt is '状态';
comment on column BIZ_TEACHER_GBZWXX_TEMP.zwmc is '职务名称';
comment on column BIZ_TEACHER_GBZWXX_TEMP.rzny is '任职年月';
comment on column BIZ_TEACHER_GBZWXX_TEMP.rzqx is '任职期限';
comment on column BIZ_TEACHER_GBZWXX_TEMP.sfzr is '是否在任';
comment on column BIZ_TEACHER_GBZWXX_TEMP.sfzz is '是否主职';
comment on column BIZ_TEACHER_GBZWXX_TEMP.mzrq is '免职日期';
comment on column BIZ_TEACHER_GBZWXX_TEMP.zwlb is '职务类别';
comment on column BIZ_TEACHER_GBZWXX_TEMP.zwjb is '职务级别';
comment on column BIZ_TEACHER_GBZWXX_TEMP.rzdw is '任职单位';
comment on column BIZ_TEACHER_GBZWXX_TEMP.rzfs is '任职方式';
comment on column BIZ_TEACHER_GBZWXX_TEMP.mzfs is '免职方式';
comment on column BIZ_TEACHER_GBZWXX_TEMP.mzyy is '免职原因';
comment on column BIZ_TEACHER_GBZWXX_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_GBZWXX_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_GBZWXX_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_GBZWXX_TEMP.update_time is '更新时间';


comment on table BIZ_TEACHER_GCCRCXX_TEMP is '高层次人才信息TEMP';
comment on column BIZ_TEACHER_GCCRCXX_TEMP.id is 'ID';
comment on column BIZ_TEACHER_GCCRCXX_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_GCCRCXX_TEMP.pzdw is '批准单位';
comment on column BIZ_TEACHER_GCCRCXX_TEMP.pzny is '批准年月';
comment on column BIZ_TEACHER_GCCRCXX_TEMP.xsdy is '享受待遇';
comment on column BIZ_TEACHER_GCCRCXX_TEMP.zt is 'zt';
comment on column BIZ_TEACHER_GCCRCXX_TEMP.rclb is '人才类别';
comment on column BIZ_TEACHER_GCCRCXX_TEMP.pzdwjb is '批准单位级别';
comment on column BIZ_TEACHER_GCCRCXX_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_GCCRCXX_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_GCCRCXX_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_GCCRCXX_TEMP.update_time is '更新时间';


comment on table BIZ_TEACHER_GNJX_TEMP is '教职工国内进修TEMP';
comment on column BIZ_TEACHER_GNJX_TEMP.id is 'ID';
comment on column BIZ_TEACHER_GNJX_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_GNJX_TEMP.jxbmc is '进修班名称';
comment on column BIZ_TEACHER_GNJX_TEMP.jxzy is '进修专业';
comment on column BIZ_TEACHER_GNJX_TEMP.jxfx is '进修方向';
comment on column BIZ_TEACHER_GNJX_TEMP.kssj is '开始时间';
comment on column BIZ_TEACHER_GNJX_TEMP.jssj is '结束时间';
comment on column BIZ_TEACHER_GNJX_TEMP.zbdw is '主办单位';
comment on column BIZ_TEACHER_GNJX_TEMP.zbdwxz is '主办单位性质';
comment on column BIZ_TEACHER_GNJX_TEMP.zxdw is '在学单位';
comment on column BIZ_TEACHER_GNJX_TEMP.jxxs is '进修学时';
comment on column BIZ_TEACHER_GNJX_TEMP.zfy is '总费用';
comment on column BIZ_TEACHER_GNJX_TEMP.grcdfy is '个人承担费用';
comment on column BIZ_TEACHER_GNJX_TEMP.xxcdfy is '学校承担费用';
comment on column BIZ_TEACHER_GNJX_TEMP.zzfs is '资助方式';
comment on column BIZ_TEACHER_GNJX_TEMP.zt is '状态';
comment on column BIZ_TEACHER_GNJX_TEMP.jxxz is '进修性质';
comment on column BIZ_TEACHER_GNJX_TEMP.xxfs is '学习方式';
comment on column BIZ_TEACHER_GNJX_TEMP.zxdwlb is '在学单位类别';
comment on column BIZ_TEACHER_GNJX_TEMP.jxjg is '进修结果';
comment on column BIZ_TEACHER_GNJX_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_GNJX_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_GNJX_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_GNJX_TEMP.update_time is '更新时间';


comment on table BIZ_TEACHER_GNPX_TEMP is '教职工国内培训TEMP';
comment on column BIZ_TEACHER_GNPX_TEMP.id is 'ID';
comment on column BIZ_TEACHER_GNPX_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_GNPX_TEMP.pxxmmc is '培训项目名称';
comment on column BIZ_TEACHER_GNPX_TEMP.pxjgmc is '培训机构名称';
comment on column BIZ_TEACHER_GNPX_TEMP.pxjb is '培训级别';
comment on column BIZ_TEACHER_GNPX_TEMP.pxhdxs is '培训获得学时';
comment on column BIZ_TEACHER_GNPX_TEMP.pxnd is '培训年度';
comment on column BIZ_TEACHER_GNPX_TEMP.zt is '状态';
comment on column BIZ_TEACHER_GNPX_TEMP.pxfs is '培训方式';
comment on column BIZ_TEACHER_GNPX_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_GNPX_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_GNPX_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_GNPX_TEMP.update_time is '更新时间';


comment on table BIZ_TEACHER_GWDJPRJL_TEMP is '教职工岗位等级聘任记录TEMP';
comment on column BIZ_TEACHER_GWDJPRJL_TEMP.id is 'ID';
comment on column BIZ_TEACHER_GWDJPRJL_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_GWDJPRJL_TEMP.prqsrq is '聘任起始日期';
comment on column BIZ_TEACHER_GWDJPRJL_TEMP.przzrq is '聘任终止日期';
comment on column BIZ_TEACHER_GWDJPRJL_TEMP.pywh is '聘用文号';
comment on column BIZ_TEACHER_GWDJPRJL_TEMP.zt is '状态';
comment on column BIZ_TEACHER_GWDJPRJL_TEMP.gwlx is '岗位类型';
comment on column BIZ_TEACHER_GWDJPRJL_TEMP.gwdj is '岗位等级';
comment on column BIZ_TEACHER_GWDJPRJL_TEMP.spdw is '受聘单位';
comment on column BIZ_TEACHER_GWDJPRJL_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_GWDJPRJL_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_GWDJPRJL_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_GWDJPRJL_TEMP.update_time is '更新时间';


comment on table BIZ_TEACHER_GWPRJL_TEMP is '教职工岗位聘任记录TEMP';
comment on column BIZ_TEACHER_GWPRJL_TEMP.id is 'ID';
comment on column BIZ_TEACHER_GWPRJL_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_GWPRJL_TEMP.gwmc is '岗位名称';
comment on column BIZ_TEACHER_GWPRJL_TEMP.prqsrq is '聘任起始日期';
comment on column BIZ_TEACHER_GWPRJL_TEMP.przzrq is '聘任终止日期';
comment on column BIZ_TEACHER_GWPRJL_TEMP.sjprjsrq is '实际聘任结束日期';
comment on column BIZ_TEACHER_GWPRJL_TEMP.sfzr is '是否在任';
comment on column BIZ_TEACHER_GWPRJL_TEMP.zt is '状态';
comment on column BIZ_TEACHER_GWPRJL_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_GWPRJL_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_GWPRJL_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_GWPRJL_TEMP.update_time is '更新时间';


comment on table BIZ_TEACHER_GZJL_TEMP is '工作经历TEMP';
comment on column BIZ_TEACHER_GZJL_TEMP.id is 'ID';
comment on column BIZ_TEACHER_GZJL_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_GZJL_TEMP.crdzzw is '曾任党政职务';
comment on column BIZ_TEACHER_GZJL_TEMP.zmr is '工作证明人';
comment on column BIZ_TEACHER_GZJL_TEMP.qsny is '起始年月';
comment on column BIZ_TEACHER_GZJL_TEMP.jzny is '截止年月';
comment on column BIZ_TEACHER_GZJL_TEMP.szdwmc is '所在单位名称';
comment on column BIZ_TEACHER_GZJL_TEMP.zt is '状态';
comment on column BIZ_TEACHER_GZJL_TEMP.gjdqdm is '国家地区';
comment on column BIZ_TEACHER_GZJL_TEMP.crzyjszwdm is '曾任专业技术职务';
comment on column BIZ_TEACHER_GZJL_TEMP.sfhwjl is '是否海外经历';
comment on column BIZ_TEACHER_GZJL_TEMP.dwxzlbdm is '单位性质类别';
comment on column BIZ_TEACHER_GZJL_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_GZJL_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_GZJL_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_GZJL_TEMP.update_time is '更新时间';


comment on table BIZ_TEACHER_HWYX_TEMP is '教职工海外研修TEMP';
comment on column BIZ_TEACHER_HWYX_TEMP.id is 'ID';
comment on column BIZ_TEACHER_HWYX_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_HWYX_TEMP.ksrq is '开始日期';
comment on column BIZ_TEACHER_HWYX_TEMP.jsrq is '结束日期';
comment on column BIZ_TEACHER_HWYX_TEMP.gjdq is '国家地区';
comment on column BIZ_TEACHER_HWYX_TEMP.yxfxjl is '研修访学机构';
comment on column BIZ_TEACHER_HWYX_TEMP.xmmc is '项目名称';
comment on column BIZ_TEACHER_HWYX_TEMP.xmzzdw is '项目组织单位';
comment on column BIZ_TEACHER_HWYX_TEMP.zt is '状态';
comment on column BIZ_TEACHER_HWYX_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_HWYX_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_HWYX_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_HWYX_TEMP.update_time is '更新时间';


comment on table BIZ_TEACHER_JGXM_TEMP is '教职工教改项目TEMP';
comment on column BIZ_TEACHER_JGXM_TEMP.id is 'ID';
comment on column BIZ_TEACHER_JGXM_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_JGXM_TEMP.xmbh is '项目编号';
comment on column BIZ_TEACHER_JGXM_TEMP.xmmc is '项目名称';
comment on column BIZ_TEACHER_JGXM_TEMP.xmjb is '项目级别';
comment on column BIZ_TEACHER_JGXM_TEMP.xmlx is '项目类型';
comment on column BIZ_TEACHER_JGXM_TEMP.qtdw is '牵头单位';
comment on column BIZ_TEACHER_JGXM_TEMP.ssdw is '所属单位';
comment on column BIZ_TEACHER_JGXM_TEMP.bxfzr is '本校负责人';
comment on column BIZ_TEACHER_JGXM_TEMP.xmfzrxm is '项目负责人姓名';
comment on column BIZ_TEACHER_JGXM_TEMP.lxrq is '立项日期';
comment on column BIZ_TEACHER_JGXM_TEMP.ksrq is '开始日期';
comment on column BIZ_TEACHER_JGXM_TEMP.jxrq is '结项日期';
comment on column BIZ_TEACHER_JGXM_TEMP.xmjf is '项目经费';
comment on column BIZ_TEACHER_JGXM_TEMP.xmzt is '项目状态';
comment on column BIZ_TEACHER_JGXM_TEMP.xmly is '项目来源';
comment on column BIZ_TEACHER_JGXM_TEMP.brcyqk is '本人参与情况';
comment on column BIZ_TEACHER_JGXM_TEMP.bxcyqk is '本校参与情况';
comment on column BIZ_TEACHER_JGXM_TEMP.zt is '状态';
comment on column BIZ_TEACHER_JGXM_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_JGXM_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_JGXM_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_JGXM_TEMP.update_time is '更新时间';
comment on column BIZ_TEACHER_JGXM_TEMP.bxfzr is '本校负责人';

comment on table BIZ_TEACHER_JLJRYXX_TEMP is '教职工奖励及荣誉信息TEMP';
comment on column BIZ_TEACHER_JLJRYXX_TEMP.id is 'ID';
comment on column BIZ_TEACHER_JLJRYXX_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_JLJRYXX_TEMP.jlmc is '奖励名称';
comment on column BIZ_TEACHER_JLJRYXX_TEMP.jlrq is '奖励日期';
comment on column BIZ_TEACHER_JLJRYXX_TEMP.jljb is '奖励级别';
comment on column BIZ_TEACHER_JLJRYXX_TEMP.jldw is '奖励单位';
comment on column BIZ_TEACHER_JLJRYXX_TEMP.jlxm is '奖励项目';
comment on column BIZ_TEACHER_JLJRYXX_TEMP.jlyy is '奖励原因';
comment on column BIZ_TEACHER_JLJRYXX_TEMP.brpm is '本人排名';
comment on column BIZ_TEACHER_JLJRYXX_TEMP.hjzsbh is '获奖证书编号';
comment on column BIZ_TEACHER_JLJRYXX_TEMP.zt is '状态';
comment on column BIZ_TEACHER_JLJRYXX_TEMP.rych is '荣誉称号';
comment on column BIZ_TEACHER_JLJRYXX_TEMP.jldj is '奖励等级';
comment on column BIZ_TEACHER_JLJRYXX_TEMP.jllb is '奖励类别';
comment on column BIZ_TEACHER_JLJRYXX_TEMP.jlfs is '奖励方式';
comment on column BIZ_TEACHER_JLJRYXX_TEMP.jljs is '获奖角色';
comment on column BIZ_TEACHER_JLJRYXX_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_JLJRYXX_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_JLJRYXX_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_JLJRYXX_TEMP.update_time is '更新时间';


comment on table BIZ_TEACHER_JSJRJZZQ_TEMP is '计算机软件著作权TEMP';
comment on column BIZ_TEACHER_JSJRJZZQ_TEMP.id is 'ID';
comment on column BIZ_TEACHER_JSJRJZZQ_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_JSJRJZZQ_TEMP.rjzzmc is '软件著作名称';
comment on column BIZ_TEACHER_JSJRJZZQ_TEMP.zsh is '证书号';
comment on column BIZ_TEACHER_JSJRJZZQ_TEMP.djh is '登记号';
comment on column BIZ_TEACHER_JSJRJZZQ_TEMP.zzqr is '著作权人';
comment on column BIZ_TEACHER_JSJRJZZQ_TEMP.smsx is '署名顺序';
comment on column BIZ_TEACHER_JSJRJZZQ_TEMP.scfbrq is '首次发表日期';
comment on column BIZ_TEACHER_JSJRJZZQ_TEMP.zsqdrq is '证书取得日期';
comment on column BIZ_TEACHER_JSJRJZZQ_TEMP.zsbfjg is '证书颁发机构';
comment on column BIZ_TEACHER_JSJRJZZQ_TEMP.qlqdfs is '权利取得方式';
comment on column BIZ_TEACHER_JSJRJZZQ_TEMP.zt is '状态';
comment on column BIZ_TEACHER_JSJRJZZQ_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_JSJRJZZQ_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_JSJRJZZQ_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_JSJRJZZQ_TEMP.update_time is '更新时间';


comment on table BIZ_TEACHER_JTCY_TEMP is '家庭成员TEMP';
comment on column BIZ_TEACHER_JTCY_TEMP.id is 'ID';
comment on column BIZ_TEACHER_JTCY_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_JTCY_TEMP.cyxm is '成员姓名';
comment on column BIZ_TEACHER_JTCY_TEMP.cygx is '成员关系';
comment on column BIZ_TEACHER_JTCY_TEMP.csny is '出生年月';
comment on column BIZ_TEACHER_JTCY_TEMP.mz is '民族';
comment on column BIZ_TEACHER_JTCY_TEMP.jkzk is '健康状况';
comment on column BIZ_TEACHER_JTCY_TEMP.gzdw is '工作单位';
comment on column BIZ_TEACHER_JTCY_TEMP.zwjb is '职务级别';
comment on column BIZ_TEACHER_JTCY_TEMP.zyjszw is '专业技术职务';
comment on column BIZ_TEACHER_JTCY_TEMP.dh is '电话';
comment on column BIZ_TEACHER_JTCY_TEMP.dzxx is '电子信箱';
comment on column BIZ_TEACHER_JTCY_TEMP.zt is '状态';
comment on column BIZ_TEACHER_JTCY_TEMP.gj is '国籍';
comment on column BIZ_TEACHER_JTCY_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_JTCY_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_JTCY_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_JTCY_TEMP.update_time is '更新时间';


comment on table BIZ_TEACHER_JXBSHJXX_TEMP is '教学比赛获奖信息TEMP';
comment on column BIZ_TEACHER_JXBSHJXX_TEMP.id is 'ID';
comment on column BIZ_TEACHER_JXBSHJXX_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_JXBSHJXX_TEMP.bsmc is '比赛名称';
comment on column BIZ_TEACHER_JXBSHJXX_TEMP.jljb is '奖励级别';
comment on column BIZ_TEACHER_JXBSHJXX_TEMP.jldj is '奖励等级';
comment on column BIZ_TEACHER_JXBSHJXX_TEMP.brpx is '本人排序';
comment on column BIZ_TEACHER_JXBSHJXX_TEMP.hjrq is '获奖日期';
comment on column BIZ_TEACHER_JXBSHJXX_TEMP.bjdw is '颁奖单位';
comment on column BIZ_TEACHER_JXBSHJXX_TEMP.zt is '状态';
comment on column BIZ_TEACHER_JXBSHJXX_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_JXBSHJXX_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_JXBSHJXX_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_JXBSHJXX_TEMP.update_time is '更新时间';


comment on table BIZ_TEACHER_JXCG_TEMP is '教学成果TEMP';
comment on column BIZ_TEACHER_JXCG_TEMP.id is 'ID';
comment on column BIZ_TEACHER_JXCG_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_JXCG_TEMP.jxcgbm is '教学成果编码';
comment on column BIZ_TEACHER_JXCG_TEMP.jxcgmc is '教学成果名称';
comment on column BIZ_TEACHER_JXCG_TEMP.xkml is '学科门类';
comment on column BIZ_TEACHER_JXCG_TEMP.yjxk is '一级学科';
comment on column BIZ_TEACHER_JXCG_TEMP.zcr is '主持人';
comment on column BIZ_TEACHER_JXCG_TEMP.zcdw is '主持单位';
comment on column BIZ_TEACHER_JXCG_TEMP.bxfzr is '本校负责人';
comment on column BIZ_TEACHER_JXCG_TEMP.cgjj is '成果简介';
comment on column BIZ_TEACHER_JXCG_TEMP.cgcxd is '成果创新点';
comment on column BIZ_TEACHER_JXCG_TEMP.xxsm is '学校署名';
comment on column BIZ_TEACHER_JXCG_TEMP.js is '角色';
comment on column BIZ_TEACHER_JXCG_TEMP.brwc is '本人位次';
comment on column BIZ_TEACHER_JXCG_TEMP.zt is '状态';
comment on column BIZ_TEACHER_JXCG_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_JXCG_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_JXCG_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_JXCG_TEMP.update_time is '更新时间';
comment on column BIZ_TEACHER_JXCG_TEMP.bxfzr is '本校负责人';


comment on table BIZ_TEACHER_JXCGHJXX_TEMP is '教学成果获奖信息TEMP';
comment on column BIZ_TEACHER_JXCGHJXX_TEMP.id is 'ID';
comment on column BIZ_TEACHER_JXCGHJXX_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_JXCGHJXX_TEMP.jxcgbm is '教学成果编码';
comment on column BIZ_TEACHER_JXCGHJXX_TEMP.jxcgmc is '教学成果名称';
comment on column BIZ_TEACHER_JXCGHJXX_TEMP.js is '角色';
comment on column BIZ_TEACHER_JXCGHJXX_TEMP.brwc is '本人位次';
comment on column BIZ_TEACHER_JXCGHJXX_TEMP.jxmc is '奖项名称';
comment on column BIZ_TEACHER_JXCGHJXX_TEMP.jljb is '奖励级别';
comment on column BIZ_TEACHER_JXCGHJXX_TEMP.jldj is '奖励等级';
comment on column BIZ_TEACHER_JXCGHJXX_TEMP.bjdw is '颁奖单位';
comment on column BIZ_TEACHER_JXCGHJXX_TEMP.bjzsh is '颁奖证书号';
comment on column BIZ_TEACHER_JXCGHJXX_TEMP.hjrq is '获奖日期';
comment on column BIZ_TEACHER_JXCGHJXX_TEMP.zt is '状态';
comment on column BIZ_TEACHER_JXCGHJXX_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_JXCGHJXX_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_JXCGHJXX_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_JXCGHJXX_TEMP.update_time is '更新时间';


comment on table BIZ_TEACHER_JXGZL_TEMP is '教职工教学工作量TEMP';
comment on column BIZ_TEACHER_JXGZL_TEMP.id is 'ID';
comment on column BIZ_TEACHER_JXGZL_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_JXGZL_TEMP.xnxq is '学年学期';
comment on column BIZ_TEACHER_JXGZL_TEMP.gzl is '工作量';
comment on column BIZ_TEACHER_JXGZL_TEMP.gzllx is '工作量类型';
comment on column BIZ_TEACHER_JXGZL_TEMP.zt is '状态';
comment on column BIZ_TEACHER_JXGZL_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_JXGZL_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_JXGZL_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_JXGZL_TEMP.update_time is '更新时间';
--create index IDX_BIZ_TEACHER_JXGZL on BIZ_TEACHER_JXGZL (ZGH);

comment on table BIZ_TEACHER_JXGZLHZ_TEMP is '教学工作量汇总TEMP';
comment on column BIZ_TEACHER_JXGZLHZ_TEMP.id is 'ID';
comment on column BIZ_TEACHER_JXGZLHZ_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_JXGZLHZ_TEMP.zt is '状态';
comment on column BIZ_TEACHER_JXGZLHZ_TEMP.xnxq is '学年学期';
comment on column BIZ_TEACHER_JXGZLHZ_TEMP.gzl is '工作量';
comment on column BIZ_TEACHER_JXGZLHZ_TEMP.gzllx is '工作量类型';
comment on column BIZ_TEACHER_JXGZLHZ_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_JXGZLHZ_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_JXGZLHZ_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_JXGZLHZ_TEMP.update_time is '更新时间';

comment on table BIZ_TEACHER_JXPJJG_TEMP is '教学评价结果TEMP';
comment on column BIZ_TEACHER_JXPJJG_TEMP.id is 'ID';
comment on column BIZ_TEACHER_JXPJJG_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_JXPJJG_TEMP.zt is '状态';
comment on column BIZ_TEACHER_JXPJJG_TEMP.xnxq is '学年学期';
comment on column BIZ_TEACHER_JXPJJG_TEMP.pjrq is '评教日期';
comment on column BIZ_TEACHER_JXPJJG_TEMP.xspjcj is '学生评教成绩';
comment on column BIZ_TEACHER_JXPJJG_TEMP.jspjcj is '教师评教成绩';
comment on column BIZ_TEACHER_JXPJJG_TEMP.ddpjcj is '督导评教成绩';
comment on column BIZ_TEACHER_JXPJJG_TEMP.xypjcj is '学院评教成绩';
comment on column BIZ_TEACHER_JXPJJG_TEMP.gcpjcj is '过程评教成绩';
comment on column BIZ_TEACHER_JXPJJG_TEMP.zcj is '总成绩';
comment on column BIZ_TEACHER_JXPJJG_TEMP.pjdj is '评教等级';
comment on column BIZ_TEACHER_JXPJJG_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_JXPJJG_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_JXPJJG_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_JXPJJG_TEMP.update_time is '更新时间';

comment on table BIZ_TEACHER_JYJL_TEMP is '教育经历TEMP';
comment on column BIZ_TEACHER_JYJL_TEMP.id is 'ID';
comment on column BIZ_TEACHER_JYJL_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_JYJL_TEMP.rxny is '入学年月';
comment on column BIZ_TEACHER_JYJL_TEMP.byny is '毕业年月';
comment on column BIZ_TEACHER_JYJL_TEMP.byyxxhdw is '毕肄业学校或单位';
comment on column BIZ_TEACHER_JYJL_TEMP.xl is '学历';
comment on column BIZ_TEACHER_JYJL_TEMP.xw is '学位';
comment on column BIZ_TEACHER_JYJL_TEMP.sxzy is '所学专业';
comment on column BIZ_TEACHER_JYJL_TEMP.xz is '学制';
comment on column BIZ_TEACHER_JYJL_TEMP.xwsygj is '学位授予国家';
comment on column BIZ_TEACHER_JYJL_TEMP.xwsydw is '学位授予单位';
comment on column BIZ_TEACHER_JYJL_TEMP.gxlb is '高校类别';
comment on column BIZ_TEACHER_JYJL_TEMP.sfzgxl is '是否最高学历';
comment on column BIZ_TEACHER_JYJL_TEMP.zt is '状态';
comment on column BIZ_TEACHER_JYJL_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_JYJL_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_JYJL_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_JYJL_TEMP.update_time is '更新时间';

comment on table BIZ_TEACHER_KCHJ_TEMP is '教职工课程获奖TEMP';
comment on column BIZ_TEACHER_KCHJ_TEMP.id is 'ID';
comment on column BIZ_TEACHER_KCHJ_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_KCHJ_TEMP.kcbm is '课程编码';
comment on column BIZ_TEACHER_KCHJ_TEMP.kcmc is '课程名称';
comment on column BIZ_TEACHER_KCHJ_TEMP.kcywm is '课程英文名';
comment on column BIZ_TEACHER_KCHJ_TEMP.kclx is '课程类型';
comment on column BIZ_TEACHER_KCHJ_TEMP.kcjb is '课程级别';
comment on column BIZ_TEACHER_KCHJ_TEMP.sfzjr is '是否主讲人';
comment on column BIZ_TEACHER_KCHJ_TEMP.jlmc is '奖励名称';
comment on column BIZ_TEACHER_KCHJ_TEMP.jljb is '奖励级别';
comment on column BIZ_TEACHER_KCHJ_TEMP.jldj is '奖励等级';
comment on column BIZ_TEACHER_KCHJ_TEMP.bjdw is '颁奖单位';
comment on column BIZ_TEACHER_KCHJ_TEMP.bjrq is '颁奖日期';
comment on column BIZ_TEACHER_KCHJ_TEMP.zt is '状态';
comment on column BIZ_TEACHER_KCHJ_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_KCHJ_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_KCHJ_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_KCHJ_TEMP.update_time is '更新时间';

comment on table BIZ_TEACHER_KHJL_TEMP is '教职工考核记录TEMP';
comment on column BIZ_TEACHER_KHJL_TEMP.id is 'ID';
comment on column BIZ_TEACHER_KHJL_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_KHJL_TEMP.khmc is '考核名称';
comment on column BIZ_TEACHER_KHJL_TEMP.khnf is '考核年份';
comment on column BIZ_TEACHER_KHJL_TEMP.khrq is '考核日期';
comment on column BIZ_TEACHER_KHJL_TEMP.zt is '状态';
comment on column BIZ_TEACHER_KHJL_TEMP.khdw is '考核单位';
comment on column BIZ_TEACHER_KHJL_TEMP.khlb is '考核类别';
comment on column BIZ_TEACHER_KHJL_TEMP.khjl is '考核结论';
comment on column BIZ_TEACHER_KHJL_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_KHJL_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_KHJL_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_KHJL_TEMP.update_time is '更新时间';

comment on table BIZ_TEACHER_KYXM_TEMP is '科研项目TEMP';
comment on column BIZ_TEACHER_KYXM_TEMP.id is 'ID';
comment on column BIZ_TEACHER_KYXM_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_KYXM_TEMP.xmbh is '项目编号';
comment on column BIZ_TEACHER_KYXM_TEMP.xmmc is '项目名称';
comment on column BIZ_TEACHER_KYXM_TEMP.xmlb is '项目类别';
comment on column BIZ_TEACHER_KYXM_TEMP.xmlx is '项目类型';
comment on column BIZ_TEACHER_KYXM_TEMP.kjxmlb is '科技项目类别';
comment on column BIZ_TEACHER_KYXM_TEMP.xmjb is '项目级别';
comment on column BIZ_TEACHER_KYXM_TEMP.xmfzr is '项目负责人';
comment on column BIZ_TEACHER_KYXM_TEMP.xmpzh is '项目批准号';
comment on column BIZ_TEACHER_KYXM_TEMP.lxrq is '立项日期';
comment on column BIZ_TEACHER_KYXM_TEMP.ksrq is '开始日期';
comment on column BIZ_TEACHER_KYXM_TEMP.jhwcrq is '计划完成日期';
comment on column BIZ_TEACHER_KYXM_TEMP.jxrq is '结项日期';
comment on column BIZ_TEACHER_KYXM_TEMP.xmly is '项目来源';
comment on column BIZ_TEACHER_KYXM_TEMP.xmlydw is '项目来源单位';
comment on column BIZ_TEACHER_KYXM_TEMP.xmzxzt is '项目执行状态';
comment on column BIZ_TEACHER_KYXM_TEMP.js is '角色';
comment on column BIZ_TEACHER_KYXM_TEMP.gxl is '贡献率';
comment on column BIZ_TEACHER_KYXM_TEMP.smsx is '署名顺序';
comment on column BIZ_TEACHER_KYXM_TEMP.cyrq is '参与日期';
comment on column BIZ_TEACHER_KYXM_TEMP.cyzt is '参与状态';
comment on column BIZ_TEACHER_KYXM_TEMP.sfxmfzr is '是否项目负责人';
comment on column BIZ_TEACHER_KYXM_TEMP.ssktbm is '所属课题编码';
comment on column BIZ_TEACHER_KYXM_TEMP.ssktmc is '所属课题名称';
comment on column BIZ_TEACHER_KYXM_TEMP.jfze is '经费总额';
comment on column BIZ_TEACHER_KYXM_TEMP.grkzpjf is '个人可支配经费';
comment on column BIZ_TEACHER_KYXM_TEMP.zt is '状态';
comment on column BIZ_TEACHER_KYXM_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_KYXM_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_KYXM_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_KYXM_TEMP.update_time is '更新时间';


comment on table BIZ_TEACHER_SHJZXX_TEMP is '社会兼职信息TEMP';
comment on column BIZ_TEACHER_SHJZXX_TEMP.id is 'ID';
comment on column BIZ_TEACHER_SHJZXX_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_SHJZXX_TEMP.jzdw is '兼职单位';
comment on column BIZ_TEACHER_SHJZXX_TEMP.jrnr is '兼职内容';
comment on column BIZ_TEACHER_SHJZXX_TEMP.qsrq is '起始日期';
comment on column BIZ_TEACHER_SHJZXX_TEMP.zzrq is '终止日期';
comment on column BIZ_TEACHER_SHJZXX_TEMP.zt is '状态';
comment on column BIZ_TEACHER_SHJZXX_TEMP.shjzfw is '社会兼职服务';
comment on column BIZ_TEACHER_SHJZXX_TEMP.czyy is '辞职原因';
comment on column BIZ_TEACHER_SHJZXX_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_SHJZXX_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_SHJZXX_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_SHJZXX_TEMP.update_time is '更新时间';

comment on table BIZ_TEACHER_SKJL_TEMP is '教职工授课记录TEMP';
comment on column BIZ_TEACHER_SKJL_TEMP.id is 'ID';
comment on column BIZ_TEACHER_SKJL_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_SKJL_TEMP.kcbm is '课程编码';
comment on column BIZ_TEACHER_SKJL_TEMP.kcmc is '课程名称';
comment on column BIZ_TEACHER_SKJL_TEMP.kcywm is '课程英文名';
comment on column BIZ_TEACHER_SKJL_TEMP.kcfzr is '课程负责人';
--comment on column BIZ_TEACHER_SKJL_TEMP.kcfzrxm is '课程负责人姓名';
comment on column BIZ_TEACHER_SKJL_TEMP.kclx is '课程类型TEMP';
comment on column BIZ_TEACHER_SKJL_TEMP.kcjb is '课程级别';
comment on column BIZ_TEACHER_SKJL_TEMP.skfs is '授课方式';
comment on column BIZ_TEACHER_SKJL_TEMP.skyz is '授课语种';
comment on column BIZ_TEACHER_SKJL_TEMP.sfxgxk is '是否校公选课';
comment on column BIZ_TEACHER_SKJL_TEMP.sfsysk is '是否双语授课';
comment on column BIZ_TEACHER_SKJL_TEMP.sfqywsk is '是否全英文授课';
comment on column BIZ_TEACHER_SKJL_TEMP.zwjc is '中文教材';
comment on column BIZ_TEACHER_SKJL_TEMP.zwcks is '中文参考书';
comment on column BIZ_TEACHER_SKJL_TEMP.wwjc is '外文教材';
comment on column BIZ_TEACHER_SKJL_TEMP.wwcks is '外文参考书';
comment on column BIZ_TEACHER_SKJL_TEMP.kczwjs is '课程中文介绍';
comment on column BIZ_TEACHER_SKJL_TEMP.kcywjs is '课程英文介绍';
comment on column BIZ_TEACHER_SKJL_TEMP.sfzjr is '是否主讲人';
comment on column BIZ_TEACHER_SKJL_TEMP.xnxq is '学年学期';
comment on column BIZ_TEACHER_SKJL_TEMP.krl is '课容量';
comment on column BIZ_TEACHER_SKJL_TEMP.xkzrs is '选课总人数';
comment on column BIZ_TEACHER_SKJL_TEMP.zt is '状态';
comment on column BIZ_TEACHER_SKJL_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_SKJL_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_SKJL_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_SKJL_TEMP.update_time is '更新时间';



comment on table BIZ_TEACHER_XNDD_TEMP is '教职工校内调动TEMP';
comment on column BIZ_TEACHER_XNDD_TEMP.id is 'ID';
comment on column BIZ_TEACHER_XNDD_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_XNDD_TEMP.ddrq is '调动日期';
comment on column BIZ_TEACHER_XNDD_TEMP.ddwh is '调动文号';
comment on column BIZ_TEACHER_XNDD_TEMP.ddyy is '调动原因';
comment on column BIZ_TEACHER_XNDD_TEMP.sfzg is '是否转岗';
comment on column BIZ_TEACHER_XNDD_TEMP.dcgw is '调出岗位';
comment on column BIZ_TEACHER_XNDD_TEMP.drgw is '调入岗位';
comment on column BIZ_TEACHER_XNDD_TEMP.sm is '说明';
comment on column BIZ_TEACHER_XNDD_TEMP.zt is '状态';
comment on column BIZ_TEACHER_XNDD_TEMP.dcdw is '调出单位';
comment on column BIZ_TEACHER_XNDD_TEMP.drdw is '调入单位';
comment on column BIZ_TEACHER_XNDD_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_XNDD_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_XNDD_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_XNDD_TEMP.update_time is '更新时间';


comment on table BIZ_TEACHER_XSQKJZXX_TEMP is '学术期刊兼职信息TEMP';
comment on column BIZ_TEACHER_XSQKJZXX_TEMP.id is 'ID';
comment on column BIZ_TEACHER_XSQKJZXX_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_XSQKJZXX_TEMP.kwmc is '刊物名称';
comment on column BIZ_TEACHER_XSQKJZXX_TEMP.issn is 'ISSN号';
comment on column BIZ_TEACHER_XSQKJZXX_TEMP.cn is 'CN号';
comment on column BIZ_TEACHER_XSQKJZXX_TEMP.rzqsrq is '任职起始日期';
comment on column BIZ_TEACHER_XSQKJZXX_TEMP.rzjsrq is '任职结束日期';
comment on column BIZ_TEACHER_XSQKJZXX_TEMP.zt is '状态';
comment on column BIZ_TEACHER_XSQKJZXX_TEMP.kwjb is '刊物级别';
comment on column BIZ_TEACHER_XSQKJZXX_TEMP.gjdq is '国家地区';
comment on column BIZ_TEACHER_XSQKJZXX_TEMP.dzzw is '担任职位';
comment on column BIZ_TEACHER_XSQKJZXX_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_XSQKJZXX_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_XSQKJZXX_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_XSQKJZXX_TEMP.update_time is '更新时间';


comment on table BIZ_TEACHER_XSTTJZXX_TEMP is '学术团体兼职信息TEMP';
comment on column BIZ_TEACHER_XSTTJZXX_TEMP.id is 'ID';
comment on column BIZ_TEACHER_XSTTJZXX_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_XSTTJZXX_TEMP.zt is '状态';
comment on column BIZ_TEACHER_XSTTJZXX_TEMP.xsttmc is '学术团体名称';
comment on column BIZ_TEACHER_XSTTJZXX_TEMP.lshzgdwmc is '隶属或主管单位名称';
comment on column BIZ_TEACHER_XSTTJZXX_TEMP.xsjzzw is '学术兼职职务';
comment on column BIZ_TEACHER_XSTTJZXX_TEMP.rzzzrq is '任职终止日期';
comment on column BIZ_TEACHER_XSTTJZXX_TEMP.rzqsrq is '任职起始日期';
comment on column BIZ_TEACHER_XSTTJZXX_TEMP.jznr is '兼职内容';
comment on column BIZ_TEACHER_XSTTJZXX_TEMP.xsttjb is '学术团体级别';
comment on column BIZ_TEACHER_XSTTJZXX_TEMP.zzlx is '组织类型';
comment on column BIZ_TEACHER_XSTTJZXX_TEMP.jzzwjb is '兼职职务级别';
comment on column BIZ_TEACHER_XSTTJZXX_TEMP.ccyy is '辞职原因';
comment on column BIZ_TEACHER_XSTTJZXX_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_XSTTJZXX_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_XSTTJZXX_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_XSTTJZXX_TEMP.update_time is '更新时间';


comment on table BIZ_TEACHER_ZCPPJL_TEMP is '教职工职称评聘记录TEMP';
comment on column BIZ_TEACHER_ZCPPJL_TEMP.id is 'ID';
comment on column BIZ_TEACHER_ZCPPJL_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_ZCPPJL_TEMP.zyjszw is '专业技术职务';
comment on column BIZ_TEACHER_ZCPPJL_TEMP.pdrq is '评定日期';
comment on column BIZ_TEACHER_ZCPPJL_TEMP.psdw is '评审单位';
comment on column BIZ_TEACHER_ZCPPJL_TEMP.przyjszw is '聘任专业技术职务';
comment on column BIZ_TEACHER_ZCPPJL_TEMP.prqsrq is '聘任起始日期';
comment on column BIZ_TEACHER_ZCPPJL_TEMP.przzrq is '聘任终止日期';
comment on column BIZ_TEACHER_ZCPPJL_TEMP.pzwh is '批准文号';
comment on column BIZ_TEACHER_ZCPPJL_TEMP.zt is '状态';
comment on column BIZ_TEACHER_ZCPPJL_TEMP.zyjszwjb is '专业技术职务级别';
comment on column BIZ_TEACHER_ZCPPJL_TEMP.prdw is '聘任单位';
comment on column BIZ_TEACHER_ZCPPJL_TEMP.przt is '聘任状态';
comment on column BIZ_TEACHER_ZCPPJL_TEMP.przyjszwjb is '聘任专业技术职务级别';
comment on column BIZ_TEACHER_ZCPPJL_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_ZCPPJL_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_ZCPPJL_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_ZCPPJL_TEMP.update_time is '更新时间';



comment on table BIZ_TEACHER_ZDBZKS_TEMP is '指导本专科生TEMP';
comment on column BIZ_TEACHER_ZDBZKS_TEMP.id is 'ID';
comment on column BIZ_TEACHER_ZDBZKS_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_ZDBZKS_TEMP.xnxq is '学年学期';
comment on column BIZ_TEACHER_ZDBZKS_TEMP.xssl is '学生数量';
comment on column BIZ_TEACHER_ZDBZKS_TEMP.zdnr is '指导内容';
comment on column BIZ_TEACHER_ZDBZKS_TEMP.zt is '状态';
comment on column BIZ_TEACHER_ZDBZKS_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_ZDBZKS_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_ZDBZKS_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_ZDBZKS_TEMP.update_time is '更新时间';


comment on table BIZ_TEACHER_ZDXKJS_TEMP is '指导学科竞赛TEMP';
comment on column BIZ_TEACHER_ZDXKJS_TEMP.id is 'ID';
comment on column BIZ_TEACHER_ZDXKJS_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_ZDXKJS_TEMP.jsbh is '竞赛编号';
comment on column BIZ_TEACHER_ZDXKJS_TEMP.jsmc is '竞赛名称';
comment on column BIZ_TEACHER_ZDXKJS_TEMP.jsxk is '竞赛学科';
comment on column BIZ_TEACHER_ZDXKJS_TEMP.jsjc is '竞赛界次';
comment on column BIZ_TEACHER_ZDXKJS_TEMP.jsjb is '竞赛级别';
comment on column BIZ_TEACHER_ZDXKJS_TEMP.jsjf is '竞赛经费';
comment on column BIZ_TEACHER_ZDXKJS_TEMP.jsksrq is '竞赛开始日期';
comment on column BIZ_TEACHER_ZDXKJS_TEMP.jsjsrq is '竞赛结束日期';
comment on column BIZ_TEACHER_ZDXKJS_TEMP.tdbh is '团队编号';
comment on column BIZ_TEACHER_ZDXKJS_TEMP.tdmc is '团队名称';
comment on column BIZ_TEACHER_ZDXKJS_TEMP.zdgzl is '指导工作量';
comment on column BIZ_TEACHER_ZDXKJS_TEMP.jscj is '竞赛成绩';
comment on column BIZ_TEACHER_ZDXKJS_TEMP.jljb is '奖励级别';
comment on column BIZ_TEACHER_ZDXKJS_TEMP.jldj is '奖励等级';
comment on column BIZ_TEACHER_ZDXKJS_TEMP.hjmc is '获奖名次';
comment on column BIZ_TEACHER_ZDXKJS_TEMP.hjrq is '获奖日期';
comment on column BIZ_TEACHER_ZDXKJS_TEMP.hjzsbh is '获奖证书编号';
comment on column BIZ_TEACHER_ZDXKJS_TEMP.zt is '状态';
comment on column BIZ_TEACHER_ZDXKJS_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_ZDXKJS_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_ZDXKJS_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_ZDXKJS_TEMP.update_time is '更新时间';



comment on table BIZ_TEACHER_ZDYJS_TEMP is '指导研究生TEMP';
comment on column BIZ_TEACHER_ZDYJS_TEMP.id is 'ID';
comment on column BIZ_TEACHER_ZDYJS_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_ZDYJS_TEMP.xnxq is '学年学期';
comment on column BIZ_TEACHER_ZDYJS_TEMP.yjslx is '研究生类型';
comment on column BIZ_TEACHER_ZDYJS_TEMP.xssl is '学生数量';
comment on column BIZ_TEACHER_ZDYJS_TEMP.zt is '状态';
comment on column BIZ_TEACHER_ZDYJS_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_ZDYJS_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_ZDYJS_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_ZDYJS_TEMP.update_time is '更新时间';


comment on table BIZ_TEACHER_ZL_TEMP is '专利TEMP';
comment on column BIZ_TEACHER_ZL_TEMP.id is 'ID';
comment on column BIZ_TEACHER_ZL_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_ZL_TEMP.zlmc is '专利名称';
comment on column BIZ_TEACHER_ZL_TEMP.zlsqbh is '专利申请编号';
comment on column BIZ_TEACHER_ZL_TEMP.zllx is '专利类型';
comment on column BIZ_TEACHER_ZL_TEMP.zlzt is '专利状态';
comment on column BIZ_TEACHER_ZL_TEMP.sqr is '申请人';
comment on column BIZ_TEACHER_ZL_TEMP.sqrq is '申请日期';
comment on column BIZ_TEACHER_ZL_TEMP.dyfmr is '第一发明人';
comment on column BIZ_TEACHER_ZL_TEMP.zlqr is '专利权人';
comment on column BIZ_TEACHER_ZL_TEMP.zlsqggh is '专利授权公告号';
comment on column BIZ_TEACHER_ZL_TEMP.zlsqggrq is '专利授权公告日期';
comment on column BIZ_TEACHER_ZL_TEMP.sfbxzl is '是否本校专利';
comment on column BIZ_TEACHER_ZL_TEMP.sfpctzl is '是否PCT专利';
comment on column BIZ_TEACHER_ZL_TEMP.zlgj is '专利国家';
comment on column BIZ_TEACHER_ZL_TEMP.flh is '分类号';
comment on column BIZ_TEACHER_ZL_TEMP.js is '角色';
comment on column BIZ_TEACHER_ZL_TEMP.gxl is '贡献率';
comment on column BIZ_TEACHER_ZL_TEMP.fmrpm is '发明人排名';
comment on column BIZ_TEACHER_ZL_TEMP.sfbxdyfmr is '是否本校第一发明人';
comment on column BIZ_TEACHER_ZL_TEMP.zt is '状态';
comment on column BIZ_TEACHER_ZL_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_ZL_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_ZL_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_ZL_TEMP.update_time is '更新时间';


comment on table BIZ_TEACHER_ZZ_TEMP is '著作TEMP';
comment on column BIZ_TEACHER_ZZ_TEMP.id is 'ID';
comment on column BIZ_TEACHER_ZZ_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_ZZ_TEMP.zzbh is '著作编号';
comment on column BIZ_TEACHER_ZZ_TEMP.zzzwmc is '著作中文名称';
comment on column BIZ_TEACHER_ZZ_TEMP.zzywmc is '著作英文名称';
comment on column BIZ_TEACHER_ZZ_TEMP.cbsmc is '出版社名称';
comment on column BIZ_TEACHER_ZZ_TEMP.cbsjb is '出版社级别';
comment on column BIZ_TEACHER_ZZ_TEMP.cbsszgjdq is '出版社所在国家地区';
comment on column BIZ_TEACHER_ZZ_TEMP.cbrq is '出版日期';
comment on column BIZ_TEACHER_ZZ_TEMP.isbnh is 'ISBN号';
comment on column BIZ_TEACHER_ZZ_TEMP.yz is '语种';
comment on column BIZ_TEACHER_ZZ_TEMP.zgyz is '中国语种';
comment on column BIZ_TEACHER_ZZ_TEMP.zs is '字数';
comment on column BIZ_TEACHER_ZZ_TEMP.lzlb is '论著类别';


comment on table BIZ_TEACHER_ZZXX_TEMP is '资质信息TEMP';
comment on column BIZ_TEACHER_ZZXX_TEMP.id is 'ID';
comment on column BIZ_TEACHER_ZZXX_TEMP.zgh is '教职工号';
comment on column BIZ_TEACHER_ZZXX_TEMP.zzmc is '资质名称';
comment on column BIZ_TEACHER_ZZXX_TEMP.zzdj is '资质等级';
comment on column BIZ_TEACHER_ZZXX_TEMP.hdrq is '获得日期';
comment on column BIZ_TEACHER_ZZXX_TEMP.zt is '状态';
comment on column BIZ_TEACHER_ZZXX_TEMP.zzlb is '资质类别';
comment on column BIZ_TEACHER_ZZXX_TEMP.create_by is '创建人';
comment on column BIZ_TEACHER_ZZXX_TEMP.create_time is '创建时间';
comment on column BIZ_TEACHER_ZZXX_TEMP.update_by is '更新人';
comment on column BIZ_TEACHER_ZZXX_TEMP.update_time is '更新时间';


--temp表新增附件字段描述
comment on column biz_teacher_temp.fj is '附件';

comment on column BIZ_TEACHER_BSHXX_TEMP.fj is '附件';

comment on column BIZ_TEACHER_BSJZXX_TEMP.fj is '附件';

comment on column BIZ_TEACHER_CCXX_TEMP.fj is '附件';

comment on column BIZ_TEACHER_CGXX_TEMP.fj is '附件';

comment on column BIZ_TEACHER_GBZWXX_TEMP.fj is '附件';

comment on column BIZ_TEACHER_GCCRCXX_TEMP.fj is '附件';

comment on column BIZ_TEACHER_GNJX_TEMP.fj is '附件';

comment on column BIZ_TEACHER_GNPX_TEMP.fj is '附件';

comment on column BIZ_TEACHER_GWDJPRJL_TEMP.fj is '附件';

comment on column BIZ_TEACHER_GWPRJL_TEMP.fj is '附件';

comment on column BIZ_TEACHER_GZJL_TEMP.fj is '附件';

comment on column BIZ_TEACHER_HWYX_TEMP.fj is '附件';

comment on column BIZ_TEACHER_JGXM_TEMP.fj is '附件';

comment on column BIZ_TEACHER_JLJRYXX_TEMP.fj is '附件';

comment on column BIZ_TEACHER_JSJRJZZQ_TEMP.fj is '附件';

comment on column BIZ_TEACHER_JTCY_TEMP.fj is '附件';

comment on column BIZ_TEACHER_JXBSHJXX_TEMP.fj is '附件';

comment on column BIZ_TEACHER_JXCG_TEMP.fj is '附件';

comment on column BIZ_TEACHER_JXCGHJXX_TEMP.fj is '附件';

comment on column BIZ_TEACHER_JXGZL_TEMP.fj is '附件';

comment on column BIZ_TEACHER_JXGZLHZ_TEMP.fj is '附件';

comment on column BIZ_TEACHER_JXPJJG_TEMP.fj is '附件';

comment on column BIZ_TEACHER_JYJL_TEMP.fj is '附件';

comment on column BIZ_TEACHER_KCHJ_TEMP.fj is '附件';

comment on column BIZ_TEACHER_KHJL_TEMP.fj is '附件';

comment on column BIZ_TEACHER_KYXM_TEMP.fj is '附件';

comment on column BIZ_TEACHER_SHJZXX_TEMP.fj is '附件';

comment on column BIZ_TEACHER_SKJL_TEMP.fj is '附件';

comment on column BIZ_TEACHER_XNDD_TEMP.fj is '附件';

comment on column BIZ_TEACHER_XSQKJZXX_TEMP.fj is '附件';

comment on column BIZ_TEACHER_XSTTJZXX_TEMP.fj is '附件';

comment on column BIZ_TEACHER_ZCPPJL_TEMP.fj is '附件';

comment on column BIZ_TEACHER_ZDBZKS_TEMP.fj is '附件';

comment on column BIZ_TEACHER_ZDXKJS_TEMP.fj is '附件';

comment on column BIZ_TEACHER_ZDYJS_TEMP.fj is '附件';

comment on column BIZ_TEACHER_ZL_TEMP.fj is '附件';

comment on column BIZ_TEACHER_ZZ_TEMP.fj is '附件';

comment on column BIZ_TEACHER_ZZXX_TEMP.fj is '附件';


ALTER TABLE biz_teacher_temp ADD CONSTRAINT pk_biz_teacher_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_gccrcxx_temp ADD CONSTRAINT pk_biz_teacher_gccrcxx_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_kchj_temp ADD CONSTRAINT pk_biz_teacher_kchj_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_zdxkjs_temp ADD CONSTRAINT pk_biz_teacher_zdxkjs_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_jxcghjxx_temp ADD CONSTRAINT pk_biz_teacher_jxcghjxx_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_jxcg_temp ADD CONSTRAINT pk_biz_teacher_jxcg_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_lw_temp ADD CONSTRAINT pk_biz_teacher_lw_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_jsjrjzzq_temp ADD CONSTRAINT pk_biz_teacher_jsjrjzzq_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_kyxm_temp ADD CONSTRAINT pk_biz_teacher_kyxm_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_jgxm_temp ADD CONSTRAINT pk_biz_teacher_jgxm_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_jxbshjxx_temp ADD CONSTRAINT pk_biz_teacher_jxbshjxx_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_skjl_temp ADD CONSTRAINT pk_biz_teacher_skjl_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_zz_temp ADD CONSTRAINT pk_biz_teacher_zz_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_zdbzks_temp ADD CONSTRAINT pk_biz_teacher_zdbzks_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_jxgzl_temp ADD CONSTRAINT pk_biz_teacher_jxgzl_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_zdyjs_temp ADD CONSTRAINT pk_biz_teacher_zdyjs_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_zl_temp ADD CONSTRAINT pk_biz_teacher_zl_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_jtcy_temp ADD CONSTRAINT pk_biz_teacher_jtcy_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_zzxx_temp ADD CONSTRAINT pk_biz_teacher_zzxx_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_cgxx_temp ADD CONSTRAINT pk_biz_teacher_cgxx_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_ccxx_temp ADD CONSTRAINT pk_biz_teacher_ccxx_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_khjl_temp ADD CONSTRAINT pk_biz_teacher_khjl_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_hwyx_temp ADD CONSTRAINT pk_biz_teacher_hwyx_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_gnjx_temp ADD CONSTRAINT pk_biz_teacher_gnjx_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_gzjl_temp ADD CONSTRAINT pk_biz_teacher_gzjl_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_shjzxx_temp ADD CONSTRAINT pk_biz_teacher_shjzxx_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_gnpx_temp ADD CONSTRAINT pk_biz_teacher_gnpx_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_gwdjprjl_temp ADD CONSTRAINT pk_biz_teacher_gwdjprjl_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_jxpjjg_temp ADD CONSTRAINT pk_biz_teacher_jxpjjg_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_jljryxx_temp ADD CONSTRAINT pk_biz_teacher_jljryxx_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_zcppjl_temp ADD CONSTRAINT pk_biz_teacher_zcppjl_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_bshxx_temp ADD CONSTRAINT pk_biz_teacher_bshxx_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_jyjl_temp ADD CONSTRAINT pk_biz_teacher_jyjl_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_gwprjl_temp ADD CONSTRAINT pk_biz_teacher_gwprjl_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_xndd_temp ADD CONSTRAINT pk_biz_teacher_xndd_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_bsjzxx_temp ADD CONSTRAINT pk_biz_teacher_bsjzxx_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_gbzwxx_temp ADD CONSTRAINT pk_biz_teacher_gbzwxx_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_xsqkjzxx_temp ADD CONSTRAINT pk_biz_teacher_xsqkjzxx_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_xsttjzxx_temp ADD CONSTRAINT pk_biz_teacher_xsttjzxx_temp PRIMARY KEY (ID);
ALTER TABLE biz_teacher_jxgzlhz_temp ADD CONSTRAINT pk_biz_teacher_jxgzlhz_temp PRIMARY KEY (ID);


CREATE INDEX idx_biz_teacher_temp ON biz_teacher_temp (zgh);
CREATE INDEX idx_zt_biz_teacher_temp ON biz_teacher_temp (zt);

CREATE INDEX idx_biz_teacher_gccrcxx_temp ON biz_teacher_gccrcxx_temp (zgh);
CREATE INDEX idx_zt_biz_tea_gccrcxx_temp ON biz_teacher_gccrcxx_temp (zt);

CREATE INDEX idx_biz_teacher_kchj_temp ON biz_teacher_kchj_temp (zgh);
CREATE INDEX idx_zt_biz_teacher_kchj_temp ON biz_teacher_kchj_temp (zt);

CREATE INDEX idx_biz_teacher_zdxkjs_temp ON biz_teacher_zdxkjs_temp (zgh);
CREATE INDEX idx_zt_biz_tea_zdxkjs_temp ON biz_teacher_zdxkjs_temp (zt);

CREATE INDEX idx_biz_teacher_jxcghjxx_temp ON biz_teacher_jxcghjxx_temp (zgh);
CREATE INDEX idx_zt_biz_tea_jxcghjxx_temp ON biz_teacher_jxcghjxx_temp (zt);

CREATE INDEX idx_biz_teacher_jxcg_temp ON biz_teacher_jxcg_temp (zgh);
CREATE INDEX idx_zt_biz_teacher_jxcg_temp ON biz_teacher_jxcg_temp (zt);

CREATE INDEX idx_biz_teacher_lw_temp ON biz_teacher_lw_temp (zgh);
CREATE INDEX idx_zt_biz_teacher_lw_temp ON biz_teacher_lw_temp (zt);

CREATE INDEX idx_biz_teacher_jsjrjzzq_temp ON biz_teacher_jsjrjzzq_temp (zgh);
CREATE INDEX idx_zt_biz_tea_jsjrjzzq_temp ON biz_teacher_jsjrjzzq_temp (zt);

CREATE INDEX idx_biz_teacher_kyxm_temp ON biz_teacher_kyxm_temp (zgh);
CREATE INDEX idx_zt_biz_teacher_kyxm_temp ON biz_teacher_kyxm_temp (zt);

CREATE INDEX idx_biz_teacher_jgxm_temp ON biz_teacher_jgxm_temp (zgh);
CREATE INDEX idx_zt_biz_teacher_jgxm_temp ON biz_teacher_jgxm_temp (zt);

CREATE INDEX idx_biz_teacher_jxbshjxx_temp ON biz_teacher_jxbshjxx_temp (zgh);
CREATE INDEX idx_zt_biz_tea_jxbshjxx_temp ON biz_teacher_jxbshjxx_temp (zt);

CREATE INDEX idx_biz_teacher_skjl_temp ON biz_teacher_skjl_temp (zgh);
CREATE INDEX idx_zt_biz_teacher_skjl_temp ON biz_teacher_skjl_temp (zt);

CREATE INDEX idx_biz_teacher_zz_temp ON biz_teacher_zz_temp (zgh);
CREATE INDEX idx_zt_biz_teacher_zz_temp ON biz_teacher_zz_temp (zt);

CREATE INDEX idx_biz_teacher_zdbzks_temp ON biz_teacher_zdbzks_temp (zgh);
CREATE INDEX idx_zt_biz_tea_zdbzks_temp ON biz_teacher_zdbzks_temp (zt);

CREATE INDEX idx_biz_teacher_jxgzl_temp ON biz_teacher_jxgzl_temp (zgh);
CREATE INDEX idx_zt_biz_teacher_jxgzl_temp ON biz_teacher_jxgzl_temp (zt);

CREATE INDEX idx_biz_teacher_zdyjs_temp ON biz_teacher_zdyjs_temp (zgh);
CREATE INDEX idx_zt_biz_teacher_zdyjs_temp ON biz_teacher_zdyjs_temp (zt);

CREATE INDEX idx_biz_teacher_zl_temp ON biz_teacher_zl_temp (zgh);
CREATE INDEX idx_zt_biz_teacher_zl_temp ON biz_teacher_zl_temp (zt);

CREATE INDEX idx_biz_teacher_jtcy_temp ON biz_teacher_jtcy_temp (zgh);
CREATE INDEX idx_zt_biz_teacher_jtcy_temp ON biz_teacher_jtcy_temp (zt);

CREATE INDEX idx_biz_teacher_zzxx_temp ON biz_teacher_zzxx_temp (zgh);
CREATE INDEX idx_zt_biz_teacher_zzxx_temp ON biz_teacher_zzxx_temp (zt);

CREATE INDEX idx_biz_teacher_cgxx_temp ON biz_teacher_cgxx_temp (zgh);
CREATE INDEX idx_zt_biz_teacher_cgxx_temp ON biz_teacher_cgxx_temp (zt);

CREATE INDEX idx_biz_teacher_ccxx_temp ON biz_teacher_ccxx_temp (zgh);
CREATE INDEX idx_zt_biz_teacher_ccxx_temp ON biz_teacher_ccxx_temp (zt);

CREATE INDEX idx_biz_teacher_khjl_temp ON biz_teacher_khjl_temp (zgh);
CREATE INDEX idx_zt_biz_teacher_khjl_temp ON biz_teacher_khjl_temp (zt);

CREATE INDEX idx_biz_teacher_hwyx_temp ON biz_teacher_hwyx_temp (zgh);
CREATE INDEX idx_zt_biz_teacher_hwyx_temp ON biz_teacher_hwyx_temp (zt);

CREATE INDEX idx_biz_teacher_gnjx_temp ON biz_teacher_gnjx_temp (zgh);
CREATE INDEX idx_zt_biz_teacher_gnjx_temp ON biz_teacher_gnjx_temp (zt);

CREATE INDEX idx_biz_teacher_gzjl_temp ON biz_teacher_gzjl_temp (zgh);
CREATE INDEX idx_zt_biz_teacher_gzjl_temp ON biz_teacher_gzjl_temp (zt);

CREATE INDEX idx_biz_teacher_shjzxx_temp ON biz_teacher_shjzxx_temp (zgh);
CREATE INDEX idx_zt_biz_tea_shjzxx_temp ON biz_teacher_shjzxx_temp (zt);

CREATE INDEX idx_biz_teacher_gnpx_temp ON biz_teacher_gnpx_temp (zgh);
CREATE INDEX idx_zt_biz_teacher_gnpx_temp ON biz_teacher_gnpx_temp (zt);

CREATE INDEX idx_biz_teacher_gwdjprjl_temp ON biz_teacher_gwdjprjl_temp (zgh);
CREATE INDEX idx_zt_biz_tea_gwdjprjl_temp ON biz_teacher_gwdjprjl_temp (zt);

CREATE INDEX idx_biz_teacher_jxpjjg_temp ON biz_teacher_jxpjjg_temp (zgh);
CREATE INDEX idx_zt_biz_tea_jxpjjg_temp ON biz_teacher_jxpjjg_temp (zt);

CREATE INDEX idx_biz_teacher_jljryxx_temp ON biz_teacher_jljryxx_temp (zgh);
CREATE INDEX idx_zt_biz_tea_jljryxx_temp ON biz_teacher_jljryxx_temp (zt);

CREATE INDEX idx_biz_teacher_zcppjl_temp ON biz_teacher_zcppjl_temp (zgh);
CREATE INDEX idx_zt_biz_tea_zcppjl_temp ON biz_teacher_zcppjl_temp (zt);

CREATE INDEX idx_biz_teacher_bshxx_temp ON biz_teacher_bshxx_temp (zgh);
CREATE INDEX idx_zt_biz_teacher_bshxx_temp ON biz_teacher_bshxx_temp (zt);

CREATE INDEX idx_biz_teacher_jyjl_temp ON biz_teacher_jyjl_temp (zgh);
CREATE INDEX idx_zt_biz_teacher_jyjl_temp ON biz_teacher_jyjl_temp (zt);

CREATE INDEX idx_biz_teacher_gwprjl_temp ON biz_teacher_gwprjl_temp (zgh);
CREATE INDEX idx_zt_biz_tea_gwprjl_temp ON biz_teacher_gwprjl_temp (zt);

CREATE INDEX idx_biz_teacher_xndd_temp ON biz_teacher_xndd_temp (zgh);
CREATE INDEX idx_zt_biz_teacher_xndd_temp ON biz_teacher_xndd_temp (zt);

CREATE INDEX idx_biz_teacher_bsjzxx_temp ON biz_teacher_bsjzxx_temp (zgh);
CREATE INDEX idx_zt_biz_tea_bsjzxx_temp ON biz_teacher_bsjzxx_temp (zt);

CREATE INDEX idx_biz_teacher_gbzwxx_temp ON biz_teacher_gbzwxx_temp (zgh);
CREATE INDEX idx_zt_biz_tea_gbzwxx_temp ON biz_teacher_gbzwxx_temp (zt);

CREATE INDEX idx_biz_teacher_xsqkjzxx_temp ON biz_teacher_xsqkjzxx_temp (zgh);
CREATE INDEX idx_zt_biz_tea_xsqkjzxx_temp ON biz_teacher_xsqkjzxx_temp (zt);

CREATE INDEX idx_biz_teacher_xsttjzxx_temp ON biz_teacher_xsttjzxx_temp (zgh);
CREATE INDEX idx_zt_biz_tea_xsttjzxx_temp ON biz_teacher_xsttjzxx_temp (zt);

CREATE INDEX idx_biz_teacher_jxgzlhz_temp ON biz_teacher_jxgzlhz_temp (zgh);
CREATE INDEX idx_zt_biz_tea_jxgzlhz_temp ON biz_teacher_jxgzlhz_temp (zt);




