package com.wisedu.lowcode4j.app.qualitycompareanalys.bo;

import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;
import com.wisedu.lowcode4j.common.model.constant.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;
import org.sagacity.sqltoy.model.SecureType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;
import com.alibaba.fastjson.annotation.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */


@ApiModel(value = "qualityanalysisemptybo", description = "群体分析空模型")
@Comment("群体分析空模型")
@ModelDefine(renderType="form")
@Processor(processorBean="qualityAnalysisModelProcessor")
//end_dynamic_declare
@Data
public class QualityAnalysisEmptyBo extends BaseBizModel {

    private static final long serialVersionUID = 4431474721041954999L;

    //region start_dynamic_column
    @ApiModelProperty(name = "id", value = "id" ,notes = "2025-05-20 17:07:00")
    @Comment("id")
    @Column(value = "id", width = 128)
    @ColumnDefine(columnDisplay=false,columnHidden=true,columnXtype="text",tableHidden=true,formHidden=true,searchHidden=true)
    private String id;

    @ApiModelProperty(name = "xsxm", value = "学生姓名" ,notes = "2025-06-06 09:56:57")
    @Comment("学生姓名")
    @Column(value = "xsxm", width = 128)
    @ColumnDefine(columnXtype="text")
    private String xsxm;

    @ApiModelProperty(name = "zydmDisplay", value = "专业名称" ,notes = "2025-06-16 16:08:28")
    @Comment("专业名称")
    @Column(value = "zydm_display")
    @ColumnDefine(columnXtype="text")
    private String zydmDisplay;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades

    //region start_dynamic_dict_column
	//endregion end_dynamic_dict_column

}
