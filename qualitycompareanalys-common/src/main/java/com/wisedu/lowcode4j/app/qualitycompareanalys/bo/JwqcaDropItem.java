package com.wisedu.lowcode4j.app.qualitycompareanalys.bo;

import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;
import com.wisedu.lowcode4j.common.model.constant.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;
import org.sagacity.sqltoy.model.SecureType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;
import com.alibaba.fastjson.annotation.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */


@ApiModel(value = "jwqcadropitem", description = "对比分析下拉项")
@Comment("对比分析下拉项")
@ModelDefine(renderType="form")
//end_dynamic_declare
@Data
public class JwqcaDropItem extends BaseBizModel {

    private static final long serialVersionUID = 4431474721041955386L;

    //region start_dynamic_column
    @ApiModelProperty(name = "itemId", value = "下拉项id" ,notes = "2025-05-21 21:19:23")
    @Comment("下拉项id")
    @Column(value = "item_id", width = 128)
    @ColumnDefine(columnXtype="text")
    private String itemId;

    @ApiModelProperty(name = "itemName", value = "下拉项名称" ,notes = "2025-05-21 21:19:36")
    @Comment("下拉项名称")
    @Column(value = "item_name", width = 128)
    @ColumnDefine(columnXtype="text")
    private String itemName;

    @ApiModelProperty(name = "tjnf", value = "统计年份" ,notes = "2025-05-21 21:19:46")
    @Comment("统计年份")
    @Column(value = "tjnf", width = 128)
    @ColumnDefine(columnXtype="text")
    private String tjnf;

    @ApiModelProperty(name = "xnzydldm", value = "校内专业大类代码" ,notes = "2025-05-22 11:18:42")
    @Comment("校内专业大类代码")
    @Column(value = "xnzydldm")
    @ColumnDefine(columnXtype="text")
    private String xnzydldm;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades

    //region start_dynamic_dict_column
	//endregion end_dynamic_dict_column

}
