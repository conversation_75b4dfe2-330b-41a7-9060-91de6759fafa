package com.wisedu.lowcode4j.app.qualitycompareanalys.bo;

import com.wisedu.lowcode4j.main.qcapub.bo.type.QualityAnalysisCommon;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 学生模型数据
 * <AUTHOR>
 * @date 2025/5/19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QualityAnalysisStudentModelData extends QualityAnalysisCommon {

    /**
     * 群体名称
     */
    private String groupName;

    /**
     * 模型值
     */
    private Double value;

    /**
     * 学号
     */
    private String studentCode;

    /**
     * 专业代码
     */
    private String zydm;

    /**
     * 专业名称
     */
    private String zymc;

}
