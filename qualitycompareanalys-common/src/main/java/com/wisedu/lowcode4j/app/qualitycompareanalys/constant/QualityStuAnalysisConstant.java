package com.wisedu.lowcode4j.app.qualitycompareanalys.constant;

import com.wisedu.lowcode4j.main.qcapub.enums.QualityAnalysisModelClassify;

/**
 * @ClassName: QualityAnalysisConstant
 * @Author: leiyy
 * @Date: 2025/5/13 10:35
 * @Description: 群体对比分析常量类
 */
public class QualityStuAnalysisConstant {
    /**
     * 指标分类
     */
    public static final QualityAnalysisModelClassify MODEL_CLASSIFY = QualityAnalysisModelClassify.STUDENT_ANALYSIS;

    /**
     * SQL ID
     */
    public interface SQL_ID {
        /**
         * 学生分析-学生-指标数据
         */
        String JWQCA_XS_ZB = "jwqca_xs_zb";

        /**
         * 群体分析-学生列表
         */
        String JWQCA_QTFX_XS_LB = "page_bksxxlb_bksxxlb";
    }

    /**
     * 基础表信息
     */
    public interface BASE_TABLE_INFO {
        /**
         * 本专科生基本情况表
         */
        String BASE_TABLE_NAME = "INS_BKSJBQK";

        /**
         * 基础表时间标识字段
         */
        String BASE_TABLE_TIME_COLUMN_NAME = BASE_TABLE_NAME + ".TJNF";

        /**
         * 基础表实体标识字段
         */
        String BASE_TABLE_ENTITY_COLUMN_NAME = BASE_TABLE_NAME + ".XH";

        /**
         * 学院
         */
        String COLLEGE_COLUMN_NAME = BASE_TABLE_NAME + ".XY";

        /**
         * 校内专业代码
         */
        String MAJOR_COLUMN_NAME = BASE_TABLE_NAME + ".XNZYDLDM";

        /**
         * 年级
         */
        String GRADE_COLUMN_NAME = BASE_TABLE_NAME + ".NJ";
    }

    public interface SQL_TEMPLATE {
        /**
         * 本科生群体对比查询SQL模板
         */
        String STU_GROUP_DB_SQL_TEMPLATE = "SELECT INS_BKSJBQK.TJNF AS STATISTIC_YEAR, " +
                " ${groupColumns}, " +
                " '${modelId}' AS MODEL_ID, " +
                " '${groupName}' AS GROUP_NAME, " +
                " '${classifyCode}' AS CLASSIFY_CODE, " +
                " '${classifyName}' AS CLASSIFY_NAME, " +
                " ROUND(AVG(${modelTable}.${valueColumnName}), 2) AS VALUE, " +
                " INS_BKSJBQK.XH as ITEM_CODE, " +
                " INS_BKSJBQK.XNZYDLDM as GROUP_CODE, " +
                " INS_BKSJBQK.XNZYDLMC as ITEM_SUB_NAME" +
                " FROM INS_BKSJBQK" +
                " LEFT JOIN ${modelTable} ON ${joinColumn} = INS_BKSJBQK.TJNF AND ${modelTable}.${entityColumnName} = INS_BKSJBQK.XH " +
                " WHERE INS_BKSJBQK.TJNF = ${year} AND ${condition}" +
                " GROUP BY INS_BKSJBQK.TJNF, ${groupColumns}, INS_BKSJBQK.XH, INS_BKSJBQK.XNZYDLDM, INS_BKSJBQK.XNZYDLMC";
    }
}
