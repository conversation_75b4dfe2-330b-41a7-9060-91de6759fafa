{"list": [{"isRoot": true, "type": "row", "comType": "page", "__tree_label": "页面", "__tree_node_key": "9586543514099766", "key": "page_dm0cctur", "uuid": "uuid_cuu6a801", "children": [{"__tree_node_key": "4621054388477934", "key": "row_ivzn092a", "__tree_label": "区域容器", "comType": "row", "type": "row", "isCover": false, "children": [{"__tree_node_key": "10528622354919492", "key": "col_zmbqa9b9", "__tree_label": "区块容器", "comType": "col", "type": "col", "isCover": false, "children": [{"uuid": "uuid_jgxpipe3", "key": "adv-search_utx4qjkq", "com": "adv-search", "comType": "adv-search", "icon": "pm-icon-advancedSearch", "comClassify": "model", "dataType": "object", "options": {"readonly": false, "interacTable": "adv-table_hd433oll", "limit": 2, "keyWordsLabel": "关键字", "hideKeyWords": false, "comLinkEnabled": true, "labelWidth": 100, "keyWordsHighlight": true, "size": "", "itemWidth": 320, "singleItemWidth": 400, "tileItemWidth": 320, "searchTime": "", "conditionType": "", "hidden": false, "readyDoSearch": false, "hideNewFields": false, "showAdv": false, "fullscreen": false, "isCondition": false, "isMore": true, "beforeRender": {"name": "before_render_w6hpl66w", "enName": "", "params": [{"name": "modelItem", "des": "对应模型信息，可以修改模型内容；返回一个模型信息"}]}, "showHidden": true, "appendToBody": true, "undefined": "", "beforeSearch": {}, "closeModelSort": false}, "events": {"mounted": {"name": "adv_search_utx4qjkq_mounted", "params": [{"name": "event", "des": "内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组)"}]}, "inited": {"tip": "模型和数据获取完成"}, "search": {"name": "adv_search_utx4qjkq_search", "tip": "搜索之前，参数为querySetting(同步)"}, "before-reset": {"name": "adv_search_utx4qjkq_before_reset", "enName": "before-reset", "tip": "重置之前(同步)"}, "async-before-reset": {"tip": "重置之前(异步，参数为回调函数，执行查询需要调用回调)"}, "reset": {"tip": "重置后"}, "adv-reset": {"tip": "高级筛选重置后(需开启高级筛选功能)"}, "collapse": {"tip": "收起条件后"}, "expand": {"tip": "展开条件后"}, "item-change": {"tip": "每一项的值触发change后"}, "beforeDestroy": {}, "dict-loaded": {"tip": "每当有某一项的字典数据请求完成后"}, "dict-change": {"tip": "字典项控件的值发生改变时触发"}}, "dataOptions": {"remote": true, "remoteType": "model", "remoteModel": {"actionType": "search", "id": "majorcompareanalys-zyjbxxlb", "url": "/admin/model/design/majorcompareanalys/perm/zyjbxxlb", "modelCascades": "", "modelName": "zyjbxxlb", "modelApp": "majorcompareanalys"}, "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "prefix": "search", "dataSource": {"type": "in", "url": "", "code": "", "params": {}}, "columnsModel": [{"name": "ssdwh"}, {"name": "xnzydm"}, {"name": "xnzymc"}, {"name": "zydm"}, {"name": "zymc"}, {"name": "ssdwmc"}, {"name": "szzynf"}, {"name": "xz"}, {"name": "yxxynx"}, {"name": "syxwml"}, {"name": "bndssb"}, {"name": "bndzrjssl"}, {"name": "bndzxssl"}, {"name": "bndzykcsl"}, {"name": "bndjdsl"}, {"name": "zylx"}, {"name": "zszt"}, {"name": "tjnf"}], "modelConfig": {"ssdwh": {"placeholder": "请选择", "search.omitted": 0, "caption": "所属单位名称"}, "xnzydm": {"placeholder": "请选择", "search.omitted": 0, "search.JSONParam": {"comLink": [{"field": "ssdwh", "paramName": "pid"}]}, "caption": "校内专业名称"}, "xnzymc": {"search.hidden": 1, "search.omitted": 1, "placeholder": "请输入", "caption": "校内专业代码"}, "zydm": {"placeholder": "请选择", "search.omitted": 0, "caption": "专业名称"}, "zymc": {"search.hidden": 1, "search.omitted": 1, "placeholder": "请输入", "caption": "专业代码"}, "ssdwmc": {"search.hidden": 1, "search.omitted": 1, "placeholder": "请输入", "caption": "所属单位号"}, "tjnf": {"placeholder": "请输入", "search.omitted": 0, "search.xtype": "number"}}}, "name": "高级搜索", "isCover": false, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "adv-search", "pageCode": "majorlist"}, {"uuid": "uuid_pg72sxdg", "key": "adv-table_hd433oll", "com": "adv-vxe-table", "comType": "adv-table", "icon": "pm-icon-adv-table", "comClassify": "model", "dataType": "object", "options": {"readonly": false, "customClass": "", "labelWidth": 100, "bindPager": "", "bindComEmpty": {}, "bindComTop": {}, "bindComBottom": {}, "hidden": false, "editable": false, "hideNewFields": false, "autoLoad": false, "searchReserveData": false, "localDataSort": false, "scheme": true, "comLinkEnabled": true, "appendToBody": true, "tableConfig": {"border": "default", "seq": {"enabled": false, "seqTitle": "序号", "seqWidth": 60, "align": "center", "headerAlign": "center"}, "rowSort": {"enabled": false, "handle": "", "fixed": "left"}, "selectType": {"type": "", "checkTitle": "", "checkWidth": 40, "labelField": "", "reserve": true, "highlight": true, "range": true, "visibleMethodFunc": {}, "checkMethodFunc": {}, "align": "center", "headerAlign": "center"}, "showHeaderOverflow": false, "showOverflow": false, "fullHeight": true, "height": 0, "minHeight": 0, "maxHeight": 1000, "stripe": false, "emptyText": "暂无数据", "align": "left", "header-align": "left", "rowId": "id", "groupField": "tableColumnGroup", "groupEnabled": false, "beforeRenderFunc": {}, "spanMethodFunc": {}, "dataLoadFunc": {}}, "rowConfig": {"isHover": true, "isCurrent": false, "height": 46}, "reportConfig": {"reportText": "导出报表", "reportList": []}, "batchDownloadConfig": {"btnText": "批量下载", "btnConfigList": []}, "columnConfig": {"resizable": true, "minWidth": 0, "width": 0, "isHover": true, "isCurrent": false}, "flowBtnConfig": {"labelWidth": 120, "beforeRender": {}, "limit": 30, "fileSize": 100, "labelPosition": "left", "isTable": false, "listType": "text", "undefined": ""}, "customConfig": {"storage": true, "checkMethodFunc": {}}, "operationConfig": {"enabled": true, "position": "right", "title": "操作", "width": 100, "schema": "text", "buttonList": [{"label": "专业画像", "id": "gkd3fm1d", "uuid": "e6fb7g8y", "type": "primary", "func": {"name": "action_ev_s2bubjvx", "params": [{"name": "event", "des": "{row}:对应的行数据;{col}:对应的列数据;{btn}:点击的按钮；{scope}:表格对象"}], "enName": "专业表现"}, "children": []}], "buttonEditList": [], "renderFunc": {}, "template": "", "beforeDialog": {}, "tip": ""}, "pagerConfig": {"pageStyle": "normal", "enabled": true, "pageSize": 20, "background": false, "pagerCount": 7, "hideOnSinglePage": false, "position": "right", "pageSizes": [5, 10, 20, 50, 100], "layouts": ["prev", "pager", "next", "sizes", "jumper"], "undefined": ""}, "toolbarConfig": {"displayType": "flat", "flow": false, "import": false, "export": true, "print": false, "enabled": true, "zoom": true, "custom": true, "sort": false, "report": false, "batchDownload": false, "exportOptions": {"tooltip": {}}, "printOptions": {"tooltip": {}}, "zoomOptions": {"tooltip": {}}, "customOptions": {"tooltip": {}}, "importOptions": {"tooltip": {}}, "reportOptions": {"tooltip": {}}, "leftButtonList": {"schema": "text", "buttonList": [], "renderFunc": {}, "beforeDialog": {}}, "rightButtonList": {"schema": "text", "buttonList": [], "renderFunc": {}}}, "exportConfig": {"filename": "专业信息", "sheetName": "sheet", "type": "xlsx", "showOneClick": true, "showCustomClick": false, "oneClickBtnText": "导出", "customBtnText": "自定义导出", "types": ["xlsx"], "translateDictFlag": false, "dateFormat": "", "apiUrl": ""}, "treeConfig": {"lazy": false, "transform": false, "parentField": "parentId", "hasChildField": "<PERSON><PERSON><PERSON><PERSON>", "expandAll": false, "accordion": false, "trigger": "default", "indent": 20, "treeNodeField": ""}, "editConfig": {"trigger": "click", "mode": "row", "showStatus": true, "showAsterisk": true, "autoClear": true, "beforeActiveEditMethodFunc": {}, "beforeEditMethodFunc": {}}, "editRules": {}, "importConfig": {"config": "file", "filename": "模板文件", "isGenerateZdb": true, "customParam": "", "dateFormat": ""}, "closeModelSort": false, "statConfig": {"isShowStat": false, "statConfigList": []}, "undefined": ""}, "dataOptions": {"remote": true, "remoteType": "model", "remoteModel": {"actionType": "table", "id": "majorcompareanalys-zyjbxxlb", "url": "/admin/model/design/majorcompareanalys/perm/zyjbxxlb", "modelCascades": "", "modelName": "zyjbxxlb", "modelApp": "majorcompareanalys"}, "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "prefix": "grid", "columnsModel": [{"name": "xnzydm"}, {"name": "xnzymc"}, {"name": "zydm"}, {"name": "zymc"}, {"name": "ssdwh"}, {"name": "ssdwmc"}, {"name": "szzynf"}, {"name": "xz"}, {"name": "yxxynx"}, {"name": "syxwml"}, {"name": "bndssb"}, {"name": "bndzrjssl"}, {"name": "bndzxssl"}, {"name": "bndzykcsl"}, {"name": "bndjdsl"}, {"name": "zylx"}, {"name": "zszt"}, {"name": "tjnf"}], "dataSource": {"type": "in", "url": "/eda/{modelApp}/find/{model}/{model}", "code": "table_sql", "dataPath": "rows", "params": {}}, "modelConfig": {"xnzydm": {"placeholder": "请选择", "grid.width": 0, "grid.omitted": 0, "grid.sortable": 0, "grid.template": "<span>{{row[field]}}</span>"}, "zydm": {"placeholder": "请选择", "grid.width": 0, "grid.omitted": 0, "grid.sortable": 0, "grid.template": "<span>{{row[field]}}</span>"}, "ssdwh": {"placeholder": "请选择", "grid.width": 0, "grid.omitted": 0, "grid.sortable": 0, "grid.template": "<span>{{row[field]}}</span>"}, "syxwml": {"placeholder": "请选择", "grid.width": 0, "grid.omitted": 0, "grid.sortable": 0, "grid.template": "<span>{{row[field]}}</span>"}, "zszt": {"placeholder": "请选择", "grid.width": 0, "grid.omitted": 0, "grid.sortable": 0, "grid.template": "<span>{{row[field]}}</span>"}}}, "events": {"mounted": {"tip": "组件挂载"}, "model-inited": {"tip": "模型获取完成"}, "reload-data": {"tip": "数据获取完成"}, "operate-click": {"tip": "操作列按钮点击"}, "radio-change": {"tip": "单选切换"}, "checkbox-change": {"tip": "多选点击"}, "checkbox-all": {"tip": "全选点击"}, "cell-click": {"tip": "单元格点击"}, "scroll": {"tip": "表格滚动"}, "page-change": {"tip": "切换当前页面"}, "size-change": {"tip": "每页数量变化"}, "zoom": {"tip": "表格切换最大化"}, "edit-closed": {"tip": "退出当前编辑"}, "row-sort-change": {"tip": "行拖动排序"}, "startAndTakeUserTask": {"tip": "流程启动"}, "agree": {"tip": "流程同意"}, "startAndSaveDraft": {"tip": "流程保存草稿"}, "afterClick": {"tip": "按钮事件后触发后回调"}, "flowButtonSucceed": {"tip": "流程按钮执行成功后"}, "beforeDestroy": {"tip": "组件销毁"}, "export-complete": {"tip": "导出完成"}}, "name": "高级表格", "isCover": false, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "adv-vxe-table", "pageCode": "majorlist"}], "options": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "pageCode": "majorlist", "uuid": "uuid_o9l2nbhg"}], "options": {"marginTop": 0, "marginBottom": 12, "marginLeft": 0, "marginRight": 0, "boxShadow": "0 0 0 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": false, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false, "bgType": "bgcolor", "backgroundColor": "", "borderRadius": 0}, "showTitle": false, "title": "容器标题", "layout": "grid", "pageCode": "majorlist", "uuid": "uuid_w17nuax9"}], "options": {"marginTop": 0, "marginBottom": 12, "marginLeft": 0, "marginRight": 0, "widthType": "0", "maxWidth": 0, "minWidth": 0, "width": "260px"}, "pageCode": "majorlist"}], "leftList": [{"isRoot": true, "type": "row", "comType": "page", "__tree_label": "页面", "__tree_node_key": "5176130895709821", "key": "page_sl0vzdai", "uuid": "uuid_ioyb1k7m", "children": [], "pageCode": "majorlist"}], "rightList": [{"isRoot": true, "type": "row", "comType": "page", "__tree_label": "页面", "__tree_node_key": "8343652356070699", "key": "page_mujdm5jq", "uuid": "uuid_5wpt0a0o", "children": [], "pageCode": "majorlist"}], "config": {"dataSource": [], "platform": "pc", "layout": "default", "leftWidth": 260, "rightWidth": 260}, "dialogJson": [], "suspendJson": []}