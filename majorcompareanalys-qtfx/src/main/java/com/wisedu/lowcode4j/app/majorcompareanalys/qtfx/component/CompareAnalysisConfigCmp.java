package com.wisedu.lowcode4j.app.majorcompareanalys.qtfx.component;

import cn.hutool.core.bean.BeanUtil;
import com.gitee.starblues.bootstrap.annotation.AutowiredType;
import com.wisedu.lowcode4j.app.db.core.MyTranscational;
import com.wisedu.lowcode4j.app.majorcompareanalys.qtfx.context.CompareAnalysisConfigContext;
import com.wisedu.lowcode4j.common.core.context.QueryContext;
import com.wisedu.lowcode4j.main.qcapub.service.QualityAnalysisConfigService;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.annotation.LiteflowMethod;
import com.yomahub.liteflow.core.NodeComponent;
import com.yomahub.liteflow.enums.LiteFlowMethodEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

import static com.wisedu.lowcode4j.app.majorcompareanalys.constant.CompareAnalysisConstant.MODEL_CLASSIFY;

/**
 * @ClassName: CompareAnalysisConfigCmp
 * @Author: leiyy
 * @Date: 2025/5/13 10:23
 * @Description: 群体对比分析配置组件
 */
@Slf4j
@RequiredArgsConstructor
@LiteflowComponent(name = "群体对比分析配置组件")
public class CompareAnalysisConfigCmp {
    @Autowired
    @AutowiredType(AutowiredType.Type.MAIN)
    private QualityAnalysisConfigService qualityAnalysisConfigService;

    @LiteflowMethod(
            value = LiteFlowMethodEnum.PROCESS,
            name = "查询群体对比分析专业指标下拉",
            nodeId = "majorcompareanalys_zb_drop",
            context = {QueryContext.class})
    public void getModelDrop(NodeComponent bindCmp) {
        QueryContext queryContext = bindCmp.getContextBean(QueryContext.class);
        queryContext.setList(qualityAnalysisConfigService.getModelDrop(MODEL_CLASSIFY));
    }

    @LiteflowMethod(
            value = LiteFlowMethodEnum.PROCESS,
            name = "群体对比分析-专业对比分析详情",
            nodeId = "majorcompareanalys_config_detail",
            context = {CompareAnalysisConfigContext.class})
    public void getModelConfigDetail(NodeComponent bindCmp) {
        CompareAnalysisConfigContext queryContext = bindCmp.getContextBean(CompareAnalysisConfigContext.class);
        queryContext.setJwqcaXsQtpzVo(qualityAnalysisConfigService.getJwcaQtpz(MODEL_CLASSIFY));
        queryContext.setJwqcaXsZbwdVoList(qualityAnalysisConfigService.getJwqcaZbwdList(MODEL_CLASSIFY));
    }

    @LiteflowMethod(
            value = LiteFlowMethodEnum.PROCESS,
            name = "群体对比分析-专业对比分析上下文初始化",
            nodeId = "majorcompareanalys_config_init",
            context = {CompareAnalysisConfigContext.class})
    public void initModelConfig(NodeComponent bindCmp) {
        CompareAnalysisConfigContext compareAnalysisConfigContext = bindCmp.getContextBean(CompareAnalysisConfigContext.class);
        Map<String, Object> params = bindCmp.getRequestData();
        CompareAnalysisConfigContext configContext = BeanUtil.copyProperties(params, CompareAnalysisConfigContext.class);
        compareAnalysisConfigContext.setJwqcaXsQtpzVo(configContext.getJwqcaXsQtpzVo());
        compareAnalysisConfigContext.setJwqcaXsZbwdVoList(configContext.getJwqcaXsZbwdVoList());
    }

    @LiteflowMethod(
            value = LiteFlowMethodEnum.PROCESS,
            name = "群体对比分析-专业对比分析保存校验",
            nodeId = "majorcompareanalys_config_validate",
            context = {CompareAnalysisConfigContext.class})
    public void saveModelConfigValidate(NodeComponent bindCmp) {
        // 获取上下文
        CompareAnalysisConfigContext compareAnalysisConfigContext = bindCmp.getContextBean(CompareAnalysisConfigContext.class);
        compareAnalysisConfigContext.setModelClassify(MODEL_CLASSIFY);
        qualityAnalysisConfigService.saveModelConfigValidate(compareAnalysisConfigContext);
    }

    @MyTranscational
    @LiteflowMethod(
            value = LiteFlowMethodEnum.PROCESS,
            name = "群体对比分析-专业对比分析保存",
            nodeId = "majorcompareanalys_config_save",
            context = {CompareAnalysisConfigContext.class})
    public void saveModelConfig(NodeComponent bindCmp) {
        // 获取上下文
        CompareAnalysisConfigContext compareAnalysisConfigContext = bindCmp.getContextBean(CompareAnalysisConfigContext.class);
        compareAnalysisConfigContext.setModelClassify(MODEL_CLASSIFY);
        qualityAnalysisConfigService.saveModelConfig(compareAnalysisConfigContext);
    }
}
