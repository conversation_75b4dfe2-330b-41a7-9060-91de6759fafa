package com.wisedu.lowcode4j.app.majorcompareanalys.qtfx.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.gitee.starblues.bootstrap.annotation.AutowiredType;
import com.wisedu.lowcode4j.app.majorcompareanalys.bo.Zyjbxxlb;
import com.wisedu.lowcode4j.app.majorcompareanalys.constant.CompareAnalysisConstant;
import com.wisedu.lowcode4j.app.majorcompareanalys.qtfx.service.CompareAnalysisService;
import com.wisedu.lowcode4j.common.core.annotation.DisableDataFilter;
import com.wisedu.lowcode4j.common.core.model.QueryModel;
import com.wisedu.lowcode4j.common.db.BaseService;
import com.wisedu.lowcode4j.main.qcapub.bo.JwqcaModelConfigBo;
import com.wisedu.lowcode4j.main.qcapub.bo.type.QualityAnalysisGroupComparison;
import com.wisedu.lowcode4j.main.qcapub.enums.QualityAnalysisModelClassify;
import com.wisedu.lowcode4j.main.qcapub.service.QualityAnalysisCompareService;
import com.wisedu.lowcode4j.main.qcapub.service.QualityAnalysisConfigService;
import com.wisedu.lowcode4j.main.qcapub.service.QualityAnalysisPubService;
import com.wisedu.lowcode4j.main.qcapub.vo.JwqcaQtpzVo;
import lombok.extern.slf4j.Slf4j;
import org.sagacity.sqltoy.model.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.wisedu.lowcode4j.app.majorcompareanalys.constant.CompareAnalysisConstant.MODEL_CLASSIFY;
import static com.wisedu.lowcode4j.app.majorcompareanalys.constant.CompareAnalysisConstant.SQL_ID.JWQCA_ZY_ZB;
import static com.wisedu.lowcode4j.main.qcapub.enums.QualityAnalysisGroupType.ANALYSIS_GROUP;

/**
 * @ClassName: CompareAnalysisServiceImpl
 * @Author: leiyy
 * @Date: 2025/5/13 15:32
 * @Description: 群体对比分析专业服务实现
 */
@Service
@Slf4j
public class CompareAnalysisServiceImpl implements CompareAnalysisService {

    @Autowired
    private BaseService baseService;

    @Autowired
    @AutowiredType(AutowiredType.Type.MAIN)
    private QualityAnalysisConfigService qualityAnalysisConfigService;

    @Autowired
    @AutowiredType(AutowiredType.Type.MAIN)
    private QualityAnalysisCompareService qualityAnalysisCompareService;

    @Autowired
    @AutowiredType(AutowiredType.Type.MAIN)
    private QualityAnalysisPubService qualityAnalysisPubService;

    @Autowired
    private CompareAnalysisExtendServiceImpl compareAnalysisExtendService;

    /**
     * 查询专业分析-指标模型数据
     * @param modelId 模型ID
     * @param statisticYear 统计年份
     * @return Page<Map<String, Object>> 模型数据集合
     */

    @Override
    public Page<Map<String, Object>> queryModelData(QueryModel queryModel, String modelId, String statisticYear) {
        // 数据权限
        qualityAnalysisPubService.authModelData(modelId, MODEL_CLASSIFY);
        // 群体配置信息
        JwqcaQtpzVo jwcaXsQtpz = qualityAnalysisConfigService.getJwcaQtpz(MODEL_CLASSIFY);
        Map<String, JwqcaModelConfigBo> jwqcaModelConfig = qualityAnalysisConfigService.getJwqcaModelConfig(MODEL_CLASSIFY);
        // 根据modelId 过滤数据
        Map<String, Object> queryCondition = qualityAnalysisCompareService.buildSqlPubParams(jwqcaModelConfig.get(modelId), ANALYSIS_GROUP, statisticYear);
        // 分析群体查询条件
        String fxqtQueryCondition = compareAnalysisExtendService.buildQtQueryCondition(jwcaXsQtpz, ANALYSIS_GROUP);
        queryCondition.put("condition", fxqtQueryCondition);
        queryModel.setParams(queryCondition);
        @SuppressWarnings("unchecked")
        Page<Map<String, Object>> pageData = baseService.findPageBySql(JWQCA_ZY_ZB, queryModel, Map.class);
        List<Map<String, Object>> dataListRows = pageData.getRows();
        // 字典翻译
        if (CollUtil.isNotEmpty(dataListRows)) {
            dataListRows.forEach(row -> row.put("ZYMC", row.get("ZYMC_DISPLAY")));
            dictTranslate(modelId, dataListRows);
        }
        return pageData;
    }

    @Override
    @DisableDataFilter
    public List<Zyjbxxlb> queryQtfxZylb(QualityAnalysisModelClassify modelClassify, String statisticYear, QueryModel queryModel) {
        Map<String, List<QualityAnalysisGroupComparison>> qtMap = qualityAnalysisCompareService.queryGroupComparison(modelClassify, statisticYear);
        String qtlx = (String) queryModel.getParams().get("qtlx");
        String ssdwh = (String) queryModel.getParams().get("ssdwh");
        List<QualityAnalysisGroupComparison> qtdbBoList = qtMap.get(qtlx);
        QualityAnalysisGroupComparison qtdbBo = null;
        for (QualityAnalysisGroupComparison jwqcaQtdbBo : qtdbBoList) {
            if (ssdwh.equals(jwqcaQtdbBo.getGroupCode())) {
                qtdbBo = jwqcaQtdbBo;
                break;
            }
        }
        if (Objects.isNull(qtdbBo)) {
            return Collections.emptyList();
        }
        // 群体校内专业代码集合
        List<String> itemCodeList = qtdbBo.getGroupItemCodes();
        // 根据前端查询条件查询所有专业
        List<Zyjbxxlb> allMajor = baseService.findBySql(CompareAnalysisConstant.SQL_ID.JWQCA_QTFX_ZY_LB, queryModel, Zyjbxxlb.class);
        // 过滤出群体学生
        return allMajor.stream().filter(major -> itemCodeList.contains(major.getXnzydm())).collect(Collectors.toList());
    }

    /**
     * 字典列翻译
     * @param modelId 指标模型ID
     * @param dataListRows 数据列表
     */
    protected void dictTranslate(String modelId, List<Map<String, Object>> dataListRows) {
        qualityAnalysisPubService.dictTranslate(modelId, dataListRows);
    }
}
