package com.wisedu.lowcode4j.app.majorcompareanalys.qtfx.service.dimension;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: DimensionManager
 * @Author: leiyy
 * @Date: 2025/5/14 11:15
 * @Description: 专业对比-维度管理器
 */
@Service
public class DimensionManager {

    private final Map<String, DimensionService> dimensionServiceMap = new HashMap<>();

    @Autowired
    public DimensionManager(List<DimensionService> dimensionServiceList) {
        for (DimensionService dimensionService : dimensionServiceList) {
            dimensionServiceMap.put(dimensionService.dimension(), dimensionService);
        }
    }

    public DimensionService getDimension(String dimension) {
        return dimensionServiceMap.get(dimension);
    }
}
