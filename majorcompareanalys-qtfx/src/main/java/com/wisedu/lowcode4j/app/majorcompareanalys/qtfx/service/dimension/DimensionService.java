package com.wisedu.lowcode4j.app.majorcompareanalys.qtfx.service.dimension;

import com.wisedu.lowcode4j.main.qcapub.bo.JwqcaModelConfigBo;
import com.wisedu.lowcode4j.main.qcapub.enums.QualityAnalysisGroupType;
import com.wisedu.lowcode4j.main.qcapub.vo.JwqcaQtpzVo;

import static com.wisedu.lowcode4j.app.majorcompareanalys.constant.CompareAnalysisConstant.BASE_TABLE_INFO.COLLEGE_COLUMN_NAME;
import static com.wisedu.lowcode4j.app.majorcompareanalys.constant.CompareAnalysisConstant.BASE_TABLE_INFO.MAJOR_COLUMN_NAME;

/**
 * @InterfaceName: DimensionService
 * @Author: leiyy
 * @Date: 2025/5/14 11:07
 * @Description: 对比维度服务
 */
public interface DimensionService {
    String QUERY_COLLEGE_COLUMN_NAME = COLLEGE_COLUMN_NAME;
    String QUERY_MAJOR_COLUMN_NAME = MAJOR_COLUMN_NAME;

    /**
     * 分析方式求和，计算学校均值模板
     */
    String SUM_CALC_SCHOOL_AVG_VALUE_TEMPLATE = "SUM({0}.{1})/COUNT(DISTINCT {2})";
    /**
     * 构建专业对比-分析群体查询模型
     * @param config 专业群体对比配置
     * @return 查询条件
     */
    String buildFxqtQueryCondition(JwqcaQtpzVo config);

    /**
     * 构建专业对比-对标群体查询模型
     * @param config 专业群体对比配置
     * @return 查询条件
     */
    String buildDbqtQueryCondition(JwqcaQtpzVo config);

    /**
     * 构建查询分组字段 逗号拼接
     * @param config 专业群体对比配置
     * @return 查询分组字段
     */
    String buildQueryGroupColumn(JwqcaQtpzVo config);

    /**
     * 获取对比分析-查询分组列字段， 基于QualityAnalysisRankData统一别名
     * @param jwqcaQtpz 配置信息
     * @return String 查询分组列字段
     */
    String buildGroupSelectColumns(JwqcaQtpzVo jwqcaQtpz);

    /**
     * 构建专业群体概览查询条件
     * @param config 专业群体对比配置
     * @param groupType 质量分析群体类型
     * @return 查询条件
     */
    String buildOverviewCondition(JwqcaQtpzVo config, QualityAnalysisGroupType groupType);

    /**
     * 获取对比维度
     * @return 对比维度值
     */
    String dimension();

    /**
     * 构建专业群体对比列表名称
     * @param config 专业群体对比配置
     * @param zymc 专业名称
     * @return 专业群体对比列表名称
     */
    String buildQtdblbName(JwqcaQtpzVo config, String zymc);

    /**
     * 构建分析方式-求和的学校平均值字段
     * @param jwqcaQtpz 专业群体对比配置
     * @param modelConfigBo 模型配置
     * @return 学校平均值字段
     */
    String buildSumTypeSchoolAvgValueColumn(JwqcaQtpzVo jwqcaQtpz, JwqcaModelConfigBo modelConfigBo);
}
