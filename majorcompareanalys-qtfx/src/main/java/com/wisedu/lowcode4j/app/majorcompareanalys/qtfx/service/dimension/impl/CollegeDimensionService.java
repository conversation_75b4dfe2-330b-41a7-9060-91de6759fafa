package com.wisedu.lowcode4j.app.majorcompareanalys.qtfx.service.dimension.impl;

import cn.hutool.core.util.StrUtil;
import com.wisedu.lowcode4j.app.majorcompareanalys.qtfx.service.dimension.DimensionService;
import com.wisedu.lowcode4j.common.db.BaseService;
import com.wisedu.lowcode4j.main.qcapub.bo.JwqcaModelConfigBo;
import com.wisedu.lowcode4j.main.qcapub.bo.type.QualityAnalysisRankData;
import com.wisedu.lowcode4j.main.qcapub.constant.QualityAnalysisDimension;
import com.wisedu.lowcode4j.main.qcapub.enums.QualityAnalysisErrorCodeEnum;
import com.wisedu.lowcode4j.main.qcapub.enums.QualityAnalysisGroupType;
import com.wisedu.lowcode4j.main.qcapub.exception.QualityAnalysisBizException;
import com.wisedu.lowcode4j.main.qcapub.vo.JwqcaQtpzVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.Objects;

/**
 * @ClassName: CollegeDimensionService
 * @Author: leiyy
 * @Date: 2025/5/14 11:13
 * @Description: 学院维度服务
 */
@Component
public class CollegeDimensionService implements DimensionService {
    /**
     * 构建专业对比-分析群体查询模型
     * @param config 专业群体对比配置
     * @return 查询模型
     */
    @Override
    public String buildFxqtQueryCondition(JwqcaQtpzVo config) {
        return QUERY_COLLEGE_COLUMN_NAME + " = '" + config.getFxqtXydm() + "' ";
    }

    @Override
    public String buildDbqtQueryCondition(JwqcaQtpzVo config) {
        return QUERY_COLLEGE_COLUMN_NAME + " = '" + config.getDbqtXydm() + "' ";
    }

    @Override
    public String buildQueryGroupColumn(JwqcaQtpzVo config) {
        return QUERY_COLLEGE_COLUMN_NAME;
    }

    @Override
    public String buildGroupSelectColumns(JwqcaQtpzVo jwqcaQtpz) {
        return QUERY_COLLEGE_COLUMN_NAME + " AS XY ";
    }

    @Override
    public String buildOverviewCondition(JwqcaQtpzVo config, QualityAnalysisGroupType groupType) {
        return "1=1";
    }

    @Override
    public String dimension() {
        return QualityAnalysisDimension.COLLEGE;
    }

    @Override
    public String buildQtdblbName(JwqcaQtpzVo config, String zymc) {
        return config.getFxqtXymc() + "-" + zymc;
    }

    @Override
    public String buildSumTypeSchoolAvgValueColumn(JwqcaQtpzVo jwqcaQtpz, JwqcaModelConfigBo modelConfigBo) {
        return MessageFormat.format(SUM_CALC_SCHOOL_AVG_VALUE_TEMPLATE, modelConfigBo.getModelTable(), modelConfigBo.getValueColumnName(), QUERY_COLLEGE_COLUMN_NAME);
    }
}
