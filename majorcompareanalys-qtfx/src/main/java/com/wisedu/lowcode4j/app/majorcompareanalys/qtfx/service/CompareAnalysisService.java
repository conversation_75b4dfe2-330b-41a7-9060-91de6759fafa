package com.wisedu.lowcode4j.app.majorcompareanalys.qtfx.service;

import com.wisedu.lowcode4j.app.majorcompareanalys.bo.Zyjbxxlb;
import com.wisedu.lowcode4j.common.core.model.QueryModel;
import com.wisedu.lowcode4j.main.qcapub.enums.QualityAnalysisModelClassify;
import org.sagacity.sqltoy.model.Page;

import java.util.List;
import java.util.Map;

/**
 * @InterfaceName: CompareAnalysisService
 * @Author: leiyy
 * @Date: 2025/5/13 15:31
 * @Description: 群体对比分析服务接口
 */
public interface CompareAnalysisService {

    /**
     * 查询专业分析-指标模型数据
     * @param modelId 模型ID
     * @param statisticYear 统计年份
     * @return Page 模型数据集合
     */
    Page<Map<String, Object>> queryModelData(QueryModel queryModel, String modelId, String statisticYear);

    /**
     * 查询群体专业信息
     * @param modelClassify 指标类型 - 用于获取当前分析对象类型
     * @param statisticYear 统计年份
     * @param queryModel 查询参数
     * @return 群体专业信息
     */
    List<Zyjbxxlb> queryQtfxZylb(QualityAnalysisModelClassify modelClassify, String statisticYear, QueryModel queryModel);
}
