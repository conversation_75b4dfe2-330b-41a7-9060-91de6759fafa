package com.wisedu.lowcode4j.app.majorcompareanalys.qtfx.component;

import cn.hutool.core.map.MapUtil;
import com.gitee.starblues.bootstrap.annotation.AutowiredType;
import com.wisedu.lowcode4j.app.majorcompareanalys.bo.Zyjbxxlb;
import com.wisedu.lowcode4j.app.majorcompareanalys.qtfx.context.CompareAnalysisQueryContext;
import com.wisedu.lowcode4j.app.majorcompareanalys.qtfx.service.CompareAnalysisService;
import com.wisedu.lowcode4j.common.core.context.QueryContext;
import com.wisedu.lowcode4j.common.core.model.QueryModel;
import com.wisedu.lowcode4j.main.qcapub.bo.JwqcaGroupBo;
import com.wisedu.lowcode4j.main.qcapub.bo.type.QualityAnalysisGroupComparison;
import com.wisedu.lowcode4j.main.qcapub.constant.QualityAnalysisConstant;
import com.wisedu.lowcode4j.main.qcapub.context.JwqcaGroupComparisonContext;
import com.wisedu.lowcode4j.main.qcapub.context.JwqcaGroupContext;
import com.wisedu.lowcode4j.main.qcapub.service.QualityAnalysisCompareService;
import com.wisedu.lowcode4j.main.qcapub.service.QualityAnalysisConfigService;
import com.wisedu.lowcode4j.main.qcapub.util.QualityAnalysisUtil;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.annotation.LiteflowMethod;
import com.yomahub.liteflow.core.NodeComponent;
import com.yomahub.liteflow.enums.LiteFlowMethodEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.sagacity.sqltoy.model.Page;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

import static com.wisedu.lowcode4j.app.majorcompareanalys.constant.CompareAnalysisConstant.MODEL_CLASSIFY;
import static com.wisedu.lowcode4j.main.qcapub.enums.QualityAnalysisModelClassify.MAJOR_ANALYSIS;
import static com.wisedu.lowcode4j.main.qcapub.util.QualityAnalysisUtil.validateFrontParamsNotNull;

/**
 * @ClassName: CompareAnalysisCmp
 * @Author: leiyy
 * @Date: 2025/5/13 15:30
 * @Description: 群体对比分析组件
 */
@Slf4j
@RequiredArgsConstructor
@LiteflowComponent(name = "群体对比分析组件")
public class CompareAnalysisCmp {

    @Autowired
    private CompareAnalysisService compareAnalysisService;

    @Autowired
    @AutowiredType(AutowiredType.Type.MAIN)
    private QualityAnalysisConfigService qualityAnalysisConfigService;

    @Autowired
    @AutowiredType(AutowiredType.Type.MAIN)
    private QualityAnalysisCompareService qualityAnalysisCompareService;

    @LiteflowMethod(
            value = LiteFlowMethodEnum.PROCESS,
            name = "查询分析群体和对标群体",
            nodeId = "majorcompareanalys_fxqtanddbqt_query",
            context = {JwqcaGroupContext.class})
    public void queryFxqtAndDbqt(NodeComponent bindCmp) {
        JwqcaGroupContext queryContext = bindCmp.getContextBean(JwqcaGroupContext.class);
        Map<String, Object> params = bindCmp.getRequestData();
        // 校验前端参数
        validateFrontParamsNotNull(params, "tjnf");
        String tjnf = MapUtil.getStr(params, "tjnf");
        Map<String, JwqcaGroupBo> qtBoMap = qualityAnalysisCompareService.queryGroupInfo(MAJOR_ANALYSIS, tjnf);
        queryContext.setFxqt(qtBoMap.get(QualityAnalysisConstant.QT.JWQCA_QT_FXQT));
        queryContext.setDbqt(qtBoMap.get(QualityAnalysisConstant.QT.JWQCA_QT_DBQT));
    }

    @LiteflowMethod(
            value = LiteFlowMethodEnum.PROCESS,
            name = "查询群体分析群体对比",
            nodeId = "majorcompareanalys_qtfxqtdb_query",
            context = {JwqcaGroupComparisonContext.class})
    public void queryQtfxQtdb(NodeComponent bindCmp) {
        JwqcaGroupComparisonContext queryContext = bindCmp.getContextBean(JwqcaGroupComparisonContext.class);
        Map<String, Object> params = bindCmp.getRequestData();
        // 校验前端参数
        validateFrontParamsNotNull(params, "tjnf");
        String tjnf = MapUtil.getStr(params, "tjnf");
        Map<String, List<QualityAnalysisGroupComparison>> map = qualityAnalysisCompareService.queryGroupComparison(MAJOR_ANALYSIS, tjnf);
        queryContext.buildData(map);
    }

    @LiteflowMethod(
            value = LiteFlowMethodEnum.PROCESS,
            name = "查询群体分析专业列表",
            nodeId = "majorcompareanalys_qtfxzylb_query",
            context = {QueryContext.class})
    public void queryQtfxXslb(NodeComponent bindCmp) {
        QueryContext queryContext = bindCmp.getContextBean(QueryContext.class);
        QueryModel queryModel = queryContext.getQueryModel();
        queryModel.setModelClass(Zyjbxxlb.class);
        // 校验前端参数
        validateFrontParamsNotNull(queryModel.getParams(), "qtlx", "ssdwh", "tjnf");
        String tjnf = MapUtil.getStr(queryModel.getParams(), "tjnf");
        List<Zyjbxxlb> list = compareAnalysisService.queryQtfxZylb(MAJOR_ANALYSIS, tjnf, queryModel);
        Page page = QualityAnalysisUtil.groupComparisonPage(list, queryModel.getPageNo(), queryModel.getPageSize());
        queryContext.setPage(page);
    }

    @LiteflowMethod(
            value = LiteFlowMethodEnum.PROCESS,
            name = "查询专业群体对比-图表数据",
            nodeId = "majorcompareanalys_zyqtdbjc_query",
            context = {CompareAnalysisQueryContext.class})
    public void queryChartData(NodeComponent bindCmp) {
        CompareAnalysisQueryContext queryContext = bindCmp.getContextBean(CompareAnalysisQueryContext.class);
        // 前端传入参数
        Map<String, Object> params = bindCmp.getRequestData();
        // 校验前端参数
        validateFrontParamsNotNull(params, "tjnf");
        // 统计年份
        String statisticYear = MapUtil.getStr(params, "tjnf");
        queryContext.setAnalysisData(qualityAnalysisCompareService.queryModelIntegrationData(statisticYear, MODEL_CLASSIFY));
    }

    @LiteflowMethod(
            value = LiteFlowMethodEnum.PROCESS,
            name = "查询专业群体对比-指标列表",
            nodeId = "majorcompareanalys_zb_list",
            context = {QueryContext.class})
    public void getZbList(NodeComponent bindCmp) {
        QueryContext queryContext = bindCmp.getContextBean(QueryContext.class);
        queryContext.setList(qualityAnalysisConfigService.getJwqcaZbwdList(MODEL_CLASSIFY));
    }

    @LiteflowMethod(
            value = LiteFlowMethodEnum.PROCESS,
            nodeId = "majorcompareanalys_zb_data",
            name = "查询专业群体对比-指标数据",
            context = {QueryContext.class}
    )
    public void queryModelData(NodeComponent bindCmp) {
        // 前端传入参数
        Map<String, Object> params = bindCmp.getRequestData();
        QueryContext queryContext = bindCmp.getContextBean(QueryContext.class);
        QueryModel queryModel = queryContext.getQueryModel();
        validateFrontParamsNotNull(params, "tjnf", "modelId");
        // 统计年份
        String statisticYear = MapUtil.getStr(params, "tjnf");
        // 模型id
        String modelId = MapUtil.getStr(params, "modelId");
        queryContext.setPage(compareAnalysisService.queryModelData(queryModel, modelId, statisticYear));
    }
}
