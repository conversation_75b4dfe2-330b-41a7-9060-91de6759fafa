package com.wisedu.lowcode4j.app.majorcompareanalys.qtfx.service.dimension.impl;

import cn.hutool.core.util.StrUtil;
import com.wisedu.lowcode4j.app.majorcompareanalys.qtfx.service.dimension.DimensionService;
import com.wisedu.lowcode4j.common.db.BaseService;
import com.wisedu.lowcode4j.main.qcapub.bo.JwqcaModelConfigBo;
import com.wisedu.lowcode4j.main.qcapub.bo.type.QualityAnalysisRankData;
import com.wisedu.lowcode4j.main.qcapub.constant.QualityAnalysisDimension;
import com.wisedu.lowcode4j.main.qcapub.enums.QualityAnalysisErrorCodeEnum;
import com.wisedu.lowcode4j.main.qcapub.enums.QualityAnalysisGroupType;
import com.wisedu.lowcode4j.main.qcapub.exception.QualityAnalysisBizException;
import com.wisedu.lowcode4j.main.qcapub.vo.JwqcaQtpzVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.Objects;

/**
 * @ClassName: MajorDimensionService
 * @Author: leiyy
 * @Date: 2025/5/14 11:14
 * @Description: 专业维度服务
 */
@Component
public class MajorDimensionService implements DimensionService {

    @Override
    public String buildFxqtQueryCondition(JwqcaQtpzVo config) {
        String condition = QUERY_MAJOR_COLUMN_NAME + " = '" + config.getFxqtZydm() + "' ";
        // 学院不空，拼接学院条件
        if (StrUtil.isNotEmpty(config.getFxqtXydm())) {
            condition += " and " + QUERY_COLLEGE_COLUMN_NAME + " = '" + config.getFxqtXydm() + "' ";
        }
        return condition;
    }

    @Override
    public String buildDbqtQueryCondition(JwqcaQtpzVo config) {
        String condition = QUERY_MAJOR_COLUMN_NAME + " = '" + config.getDbqtZydm() + "' ";
        // 学院不空，拼接学院条件
        if (StrUtil.isNotEmpty(config.getDbqtXydm())) {
            condition += " and " + QUERY_COLLEGE_COLUMN_NAME + " = '" + config.getDbqtXydm() + "' ";
        }
        return condition;
    }

    @Override
    public String buildQueryGroupColumn(JwqcaQtpzVo config) {
        return QUERY_MAJOR_COLUMN_NAME;
    }

    @Override
    public String buildGroupSelectColumns(JwqcaQtpzVo jwqcaQtpz) {
        return QUERY_MAJOR_COLUMN_NAME + " AS ZY ";
    }

    @Override
    public String buildOverviewCondition(JwqcaQtpzVo config, QualityAnalysisGroupType groupType) {
        return "1=1";
    }

    @Override
    public String dimension() {
        return QualityAnalysisDimension.MAJOR;
    }

    @Override
    public String buildQtdblbName(JwqcaQtpzVo config, String zymc) {
        return config.getFxqtZymc();
    }

    @Override
    public String buildSumTypeSchoolAvgValueColumn(JwqcaQtpzVo jwqcaQtpz, JwqcaModelConfigBo modelConfigBo) {
        return MessageFormat.format(SUM_CALC_SCHOOL_AVG_VALUE_TEMPLATE, modelConfigBo.getModelTable(), modelConfigBo.getValueColumnName(), QUERY_MAJOR_COLUMN_NAME);
    }

}
