<?xml version="1.0" encoding="utf-8"?>
<sqltoy xmlns="http://www.sagframe.com/schema/sqltoy"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.sagframe.com/schema/sqltoy http://www.sagframe.com/schema/sqltoy/sqltoy.xsd">

    <sql id="jwqca_zy_zb">
        <value>
            <![CDATA[
                SELECT @value(:modelTable).*, T_JWQCA_ZYJBQK.XNZYMC AS ZYMC_DISPLAY
                FROM @value(:modelTable)
                LEFT JOIN T_JWQCA_ZYJBQK ON @value(:joinColumn) = T_JWQCA_ZYJBQK.TJNF AND @value(:modelTable).@value(:entityColumnName) = T_JWQCA_ZYJBQK.XNZYDM
                WHERE T_JWQCA_ZYJBQK.TJNF = :statisticYear AND @value(:condition)
                ORDER BY T_JWQCA_ZYJBQK.XNZYDM
            ]]>
        </value>
    </sql>

    <sql id="jwqca_qt_xy">
        <value>
            <![CDATA[
                select
                    'fxqt' as id,
                    :fxqtName as groupName,
                    (select count(distinct xh) from INS_BKSJBQK where tjnf = :tjnf and XY = :fxqtXydm) as totalCount,
                    (select count(distinct XNZYDM) from T_JWQCA_ZYJBQK where tjnf = :tjnf and SSDWH = :fxqtXydm) as majorCount
                from DUAL
                union all
                select
                    'dbqt' as id,
                    :dbqtName as groupName,
                    (select count(distinct xh) from INS_BKSJBQK where tjnf = :tjnf and XY = :dbqtXydm) as totalCount,
                    (select count(distinct XNZYDM) from T_JWQCA_ZYJBQK where tjnf = :tjnf and SSDWH = :dbqtXydm) as majorCount
                from DUAL
            ]]>
        </value>
    </sql>

    <sql id="jwqca_qt_zy">
        <value>
            <![CDATA[
                select
                    'fxqt' as id,
                    :fxqtName as groupName,
                    (select count(distinct xh) from INS_BKSJBQK where tjnf = :tjnf and XNZYDLDM = :fxqtZydm) as totalCount,
                    '0' as majorCount
                from T_JWQCA_ZYJBQK
                where tjnf = :tjnf and xnzydm = :fxqtZydm
                union all
                select
                    'dbqt' as id,
                    :dbqtName as groupName,
                    (select count(distinct xh) from INS_BKSJBQK where tjnf = :tjnf and XNZYDLDM = :dbqtZydm) as totalCount,
                    '0' as majorCount
                from T_JWQCA_ZYJBQK
                where tjnf = :tjnf and xnzydm = :dbqtZydm
            ]]>
        </value>
    </sql>
</sqltoy>
