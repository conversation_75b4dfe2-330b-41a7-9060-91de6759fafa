<?xml version="1.0" encoding="UTF-8"?>
<flow>
    <chain name="majorcompareanalys_config_detail" label="群体对比分析配置-专业对比分析详情" version="1.0.0" moduleCode="qtfx" moduleGroupCode="" permitExtFlag="1" apiEncrypt="0" sysFlag="0" apiTimeout="0" configPageCode="">
         THEN( majorcompareanalys_config_detail )
        <elGraph>
            <![CDATA[
                {"newNodes":[{"id":"init_30zrznc2ypq0000","type":"start-node","x":400,"y":300,"properties":{"componentId":"page_init","componentName":"pageInit","name":"开始","logo":"api-kaishi","status":"normal"}},{"id":"logic_5im0z9pxnhk0000","type":"common-node","x":610,"y":300,"properties":{"type":"nodeCustom","name":"群体对比分析-专业对比分析详情","componentName":"nodeCustom","logo":"api-jiedian","level":0,"status":"selected","modelId":"majorcompareanalys_config_detail","extendFlag":0,"contexts":{"com.wisedu.lowcode4j.app.majorcompareanalys.qtfx.context.CompareAnalysisConfigContext":"CompareAnalysisConfigContext"},"componentType":"0"},"text":{"x":610,"y":300,"value":"群体对比分析-专业对比分析详情"}}],"newEdges":[{"id":"logic_cnkjqv7y5dc0000","type":"logic-line","sourceNodeId":"init_30zrznc2ypq0000","targetNodeId":"logic_5im0z9pxnhk0000","startPoint":{"x":450,"y":300},"endPoint":{"x":516,"y":300},"properties":{},"pointsList":[{"x":450,"y":300},{"x":516,"y":300}]}]}
            ]]>
        </elGraph>
        <firstContext>com.wisedu.lowcode4j.app.majorcompareanalys.qtfx.context.CompareAnalysisConfigContext</firstContext>
        <apiContext>com.wisedu.lowcode4j.app.majorcompareanalys.qtfx.context.CompareAnalysisConfigContext</apiContext>
    </chain>
</flow>
