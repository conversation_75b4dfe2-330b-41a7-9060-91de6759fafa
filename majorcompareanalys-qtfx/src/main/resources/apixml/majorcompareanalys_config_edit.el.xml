<?xml version="1.0" encoding="UTF-8"?>
<flow>
    <chain name="majorcompareanalys_config_edit" label="群体对比分析配置-专业对比分析编辑" version="1.0.0" moduleCode="qtfx" moduleGroupCode="" permitExtFlag="1" apiEncrypt="0" sysFlag="0" apiTimeout="0" configPageCode="">
         THEN( majorcompareanalys_config_init , majorcompareanalys_config_validate , majorcompareanalys_config_save )
        <elGraph>
            <![CDATA[
                {"newNodes":[{"id":"init_fzifmcxjoqw0000","type":"start-node","x":400,"y":300,"properties":{"componentId":"page_init","componentName":"pageInit","name":"开始","logo":"api-kaishi","status":"normal"}},{"id":"logic_ctg23m16qdk0000","type":"common-node","x":610,"y":300,"properties":{"type":"nodeCustom","name":"群体对比分析-专业对比分析上下文初始化","componentName":"nodeCustom","logo":"api-jiedian","level":0,"status":"normal","modelId":"majorcompareanalys_config_init","extendFlag":0,"contexts":{"com.wisedu.lowcode4j.app.majorcompareanalys.qtfx.context.CompareAnalysisConfigContext":"CompareAnalysisConfigContext"},"componentType":"0"},"text":{"x":598,"y":300,"value":"群体对比分析-专业对比分析上下文初始化"}},{"id":"logic_1w4z73ipa9j4000","type":"common-node","x":850,"y":300,"properties":{"type":"nodeCustom","name":"群体对比分析-专业对比分析保存校验","componentName":"nodeCustom","logo":"api-jiedian","level":0,"status":"selected","modelId":"majorcompareanalys_config_validate","extendFlag":0,"contexts":{"com.wisedu.lowcode4j.app.majorcompareanalys.qtfx.context.CompareAnalysisConfigContext":"CompareAnalysisConfigContext"},"componentType":"0"},"text":{"x":848,"y":300,"value":"群体对比分析-专业对比分析保存校验"}},{"id":"logic_b02ma9k5h4o0000","type":"common-node","x":1100,"y":300,"properties":{"type":"nodeCustom","name":"群体对比分析-专业对比分析保存","componentName":"nodeCustom","logo":"api-jiedian","level":0,"status":"normal","modelId":"majorcompareanalys_config_save","extendFlag":0,"contexts":{"com.wisedu.lowcode4j.app.majorcompareanalys.qtfx.context.CompareAnalysisConfigContext":"CompareAnalysisConfigContext"},"componentType":"0"},"text":{"x":1100,"y":300,"value":"群体对比分析-专业对比分析保存"}}],"newEdges":[{"id":"logic_ttbq7ugseq8000","type":"logic-line","sourceNodeId":"init_fzifmcxjoqw0000","targetNodeId":"logic_ctg23m16qdk0000","startPoint":{"x":450,"y":300},"endPoint":{"x":516,"y":300},"properties":{},"pointsList":[{"x":450,"y":300},{"x":516,"y":300}]},{"id":"logic_5qqg5v5iehw0000","type":"logic-line","sourceNodeId":"logic_ctg23m16qdk0000","targetNodeId":"logic_1w4z73ipa9j4000","startPoint":{"x":696,"y":300},"endPoint":{"x":762,"y":300},"properties":{},"pointsList":[{"x":696,"y":300},{"x":762,"y":300}]},{"id":"logic_aanhbwdfls40000","type":"logic-line","sourceNodeId":"logic_1w4z73ipa9j4000","targetNodeId":"logic_b02ma9k5h4o0000","startPoint":{"x":942,"y":300},"endPoint":{"x":1008,"y":300},"properties":{},"pointsList":[{"x":942,"y":300},{"x":1008,"y":300}]}]}
            ]]>
        </elGraph>
        <firstContext>com.wisedu.lowcode4j.app.majorcompareanalys.qtfx.context.CompareAnalysisConfigContext</firstContext>
        <apiContext>com.wisedu.lowcode4j.app.majorcompareanalys.qtfx.context.CompareAnalysisConfigContext</apiContext>
    </chain>
</flow>
