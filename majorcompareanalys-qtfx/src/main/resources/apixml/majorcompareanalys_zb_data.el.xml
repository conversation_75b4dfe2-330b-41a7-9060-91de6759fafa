<?xml version="1.0" encoding="UTF-8"?>
<flow>
    <chain name="majorcompareanalys_zb_data" label="查询专业群体对比-指标数据" version="1.0.0" moduleCode="qtfx" moduleGroupCode="" permitExtFlag="1" apiEncrypt="0" sysFlag="0" apiTimeout="0" configPageCode="">
         THEN( majorcompareanalys_zb_data )
        <elGraph>
            <![CDATA[
                {"newNodes":[{"id":"init_a9c29m1iis80000","type":"start-node","x":400,"y":300,"properties":{"componentId":"page_init","componentName":"pageInit","name":"开始","logo":"api-kaishi","status":"normal"}},{"id":"logic_6vz954pl5ow0000","type":"common-node","x":610,"y":300,"properties":{"type":"nodeCustom","name":"查询专业群体对比-指标数据","componentName":"nodeCustom","logo":"api-jiedian","level":0,"status":"selected","modelId":"majorcompareanalys_zb_data","extendFlag":0,"contexts":{"com.wisedu.lowcode4j.common.core.context.QueryContext":"通用查询上下文"},"componentType":"0"},"text":{"x":610,"y":300,"value":"查询专业群体对比-指标数据"}}],"newEdges":[{"id":"logic_ga1whrcpe280000","type":"logic-line","sourceNodeId":"init_a9c29m1iis80000","targetNodeId":"logic_6vz954pl5ow0000","startPoint":{"x":450,"y":300},"endPoint":{"x":516,"y":300},"properties":{},"pointsList":[{"x":450,"y":300},{"x":516,"y":300}]}]}
            ]]>
        </elGraph>
        <firstContext>com.wisedu.lowcode4j.common.core.context.QueryContext</firstContext>
        <apiContext>com.wisedu.lowcode4j.common.core.context.QueryContext</apiContext>
    </chain>
</flow>
