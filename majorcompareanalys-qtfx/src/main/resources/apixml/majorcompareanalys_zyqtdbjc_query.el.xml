<?xml version="1.0" encoding="UTF-8"?>
<flow>
    <chain name="majorcompareanalys_zyqtdbjc_query" label="查询专业群体对比-图表数据" version="1.0.0" moduleCode="qtfx" moduleGroupCode="" permitExtFlag="1" apiEncrypt="0" sysFlag="0" apiTimeout="0" configPageCode="">
         THEN( majorcompareanalys_zyqtdbjc_query )
        <elGraph>
            <![CDATA[
                {"newNodes":[{"id":"init_5jxqagvjsew0000","type":"start-node","x":400,"y":300,"properties":{"componentId":"page_init","componentName":"pageInit","name":"开始","logo":"api-kaishi","status":"normal"}},{"id":"logic_4lpfcpvz2rs0000","type":"common-node","x":610,"y":300,"properties":{"type":"nodeCustom","name":"查询专业群体对比-图表数据","componentName":"nodeCustom","logo":"api-jiedian","level":0,"status":"selected","modelId":"majorcompareanalys_zyqtdbjc_query","extendFlag":0,"contexts":{"com.wisedu.lowcode4j.app.majorcompareanalys.qtfx.context.CompareAnalysisQueryContext":"CompareAnalysisQueryContext"},"componentType":"0"},"text":{"x":610,"y":300,"value":"查询专业群体对比-图表数据"}}],"newEdges":[{"id":"logic_6wkmuqbxf2g0000","type":"logic-line","sourceNodeId":"init_5jxqagvjsew0000","targetNodeId":"logic_4lpfcpvz2rs0000","startPoint":{"x":450,"y":300},"endPoint":{"x":516,"y":300},"properties":{},"pointsList":[{"x":450,"y":300},{"x":516,"y":300}]}]}
            ]]>
        </elGraph>
        <firstContext>com.wisedu.lowcode4j.app.majorcompareanalys.qtfx.context.CompareAnalysisQueryContext</firstContext>
        <apiContext>com.wisedu.lowcode4j.app.majorcompareanalys.qtfx.context.CompareAnalysisQueryContext</apiContext>
    </chain>
</flow>
