<?xml version="1.0" encoding="UTF-8"?>
<flow>
    <chain name="majorcompareanalys_qtfxqtdb_query" label="查询专业群体对比-群体分析群体对比" version="1.0.0" moduleCode="qtfx" moduleGroupCode="" permitExtFlag="1" apiEncrypt="0" sysFlag="0" apiTimeout="0" configPageCode="">
         THEN( majorcompareanalys_qtfxqtdb_query )
        <elGraph>
            <![CDATA[
                {"newNodes":[{"id":"init_3r5tsvlhf400000","type":"start-node","x":400,"y":300,"properties":{"componentId":"page_init","componentName":"pageInit","name":"开始","logo":"api-kaishi","status":"normal"}},{"id":"logic_e2s5g72mw0o0000","type":"common-node","x":610,"y":300,"properties":{"type":"nodeCustom","name":"查询群体分析群体对比","componentName":"nodeCustom","logo":"api-jiedian","level":0,"status":"selected","modelId":"majorcompareanalys_qtfxqtdb_query","extendFlag":0,"contexts":{"com.wisedu.lowcode4j.main.qcapub.context.JwqcaGroupComparisonContext":"JwqcaGroupComparisonContext"},"componentType":"0"},"text":{"x":610,"y":300,"value":"查询群体分析群体对比"}}],"newEdges":[{"id":"logic_flomja7dbog0000","type":"logic-line","sourceNodeId":"init_3r5tsvlhf400000","targetNodeId":"logic_e2s5g72mw0o0000","startPoint":{"x":450,"y":300},"endPoint":{"x":516,"y":300},"properties":{},"pointsList":[{"x":450,"y":300},{"x":516,"y":300}]}]}
            ]]>
        </elGraph>
        <firstContext>com.wisedu.lowcode4j.main.qcapub.context.JwqcaGroupComparisonContext</firstContext>
        <apiContext>com.wisedu.lowcode4j.main.qcapub.context.JwqcaGroupComparisonContext</apiContext>
    </chain>
</flow>
