<?xml version="1.0" encoding="UTF-8"?>
<flow>
    <chain name="majorcompareanalys_qt_query" label="查询专业群体对比-分析群体和对标群体" version="1.0.0" moduleCode="qtfx" moduleGroupCode="" permitExtFlag="1" apiEncrypt="0" sysFlag="0" apiTimeout="0" configPageCode="">
         THEN( majorcompareanalys_fxqtanddbqt_query )
        <elGraph>
            <![CDATA[
                {"newNodes":[{"id":"init_3uwrcv13o6s000","type":"start-node","x":400,"y":300,"properties":{"componentId":"page_init","componentName":"pageInit","name":"开始","logo":"api-kaishi","status":"normal"}},{"id":"logic_15f6q7n23fr4000","type":"common-node","x":610,"y":300,"properties":{"type":"nodeCustom","name":"查询分析群体和对标群体","componentName":"nodeCustom","logo":"api-jiedian","level":0,"status":"selected","modelId":"majorcompareanalys_fxqtanddbqt_query","extendFlag":0,"contexts":{"com.wisedu.lowcode4j.main.qcapub.context.JwqcaGroupContext":"JwqcaGroupContext"},"componentType":"0","isReplace":false},"text":{"x":610,"y":300,"value":"查询分析群体和对标群体"}}],"newEdges":[{"id":"logic_e0vvevw11yg0000","type":"logic-line","sourceNodeId":"init_3uwrcv13o6s000","targetNodeId":"logic_15f6q7n23fr4000","startPoint":{"x":450,"y":300},"endPoint":{"x":516,"y":300},"properties":{},"pointsList":[{"x":450,"y":300},{"x":516,"y":300}]}]}
            ]]>
        </elGraph>
        <firstContext>com.wisedu.lowcode4j.main.qcapub.context.JwqcaGroupContext</firstContext>
        <apiContext>com.wisedu.lowcode4j.main.qcapub.context.JwqcaGroupContext</apiContext>
    </chain>
</flow>
