<template>
  <div>组件1。 测试多语言
      <el-button type="danger" @click="testApi">请求测试</el-button>
      <res-button>测试按钮</res-button>
  </div>
</template>

<script>
import {testService} from "@/core/services/public.service.js";

export default {
    methods:{
    testApi(){
      testService({id:''}).then(res=>{
        console.log(res)
      })
    }
    
  }
}
</script>

<style lang="less" scoped>
div{
    color: @color-primary;
}
</style>