export default {
  options: {
    width: 0,
    showLabel: true,
    labelWidth: 100,
    labelPosition: "left", // top,right
    labelShowType: "default", // ellipsis
    dataId: "", // 请求数据的id，无则不请求
    tableForm: true,
    column: 2,
    autoLoad: true,
    size: "",
    hidden: false,
    readonly: false,
    disabled: false,
    groupType: "default", 
    hideType: "display", 
    validateGroup: "",
  },
  events: {},
  dataOptions: {
    prefix: "",
    remoteType: "model",
    remoteModel: {
      // permKey: "form",
      actionType: "",
    },
    dataSource: {
      type: "in", //in 内置  api 从api选择动作  custom 自定义动作
      url: "", // 请求地址
      code: "",
      params: {
        //  "param1":"",//参数1
        //  "param2": "", //参数2
      },
    },
  },
};
