{"name": "xsqtdbfx", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "node  ./updateVersion.js && vue-cli-service build --target lib --name xsqtdbfx ./src/index.js", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.27.2", "core-js": "^3.22.8", "element-ui": "^2.15.9", "js-cookie": "^3.0.5", "less-loader": "6.x", "moment": "^2.29.4", "simple-git": "^3.27.0", "style-resources-loader": "^1.5.0", "vue": "2.7", "vue-cli-plugin-style-resources-loader": "^0.1.5", "vue-i18n": "8.x", "vue-router": "3.x"}, "devDependencies": {"@vue/cli-plugin-babel": "4.x", "@vue/cli-plugin-eslint": "4.x", "@vue/cli-service": "4.x", "babel-eslint": "^10.1.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "sockjs-client": "1.6.0", "vue-template-compiler": "2.7"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not ie <10"]}