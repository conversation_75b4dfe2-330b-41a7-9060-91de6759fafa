export default {
    facade: [
      {
        // 打开关闭时候的文本
        component: "sub-form",
        key: "showLabel",
        label: "是否展示label",
        subComponent: "switch",
        subForm: [
          {
            actionValue: true,
            render: [
              {
                component: "input-number",
                key: "labelWidth",
                label: "label宽度",
                attr: {
                  inShowValue: 0, // 折叠置为0
                  showValue: 100, // 展开置为100
                  min: 0,
                  step: 10,
                },
              },
              {
                component: "select-render",
                key: "labelPosition",
                label: "label位置",
                render: "select",
                datas: [
                  { label: "left", value: "left" },
                  { label: "right", value: "right" },
                  { label: "top", value: "top" },
                ],
              },
              {
                component: "select-render",
                key: "labelShowType",
                label: "label显示方式",
                render: "select",
                datas: [
                  { label: "全展示", value: "default" },
                  { label: "超出省略号", value: "ellipsis" },
                ],
              },
            ],
          },
        ],
      },
      {
        component: "sub-form",
        key: "tableForm",
        label: "是否表格表单",
        subComponent: "switch",
        // actionValue: false,
        subForm: [
          {
            actionValue:false,
            render:[
              {
                component: "input-number",
                key: "width",
                label: "列项宽度",
                attr: {
                  min: 0,
                  step: 50,
                  inShowValue: 0,
                  showValue: 0,
                },
              },
            ]
          }
          
        ],
      },
      {
        component: "input-number",
        key: "column",
        label: "列数",
        attr: {
          min: 1,
          step: 1,
          "step-strictly": true,
        },
      },
      {
        component: "switch",
        key: "autoLoad",
        label: "是否自动请求数据",
      },
      {
        component: "select-render",
        key: "groupType",
        label: "分组类型",
        render: "select",
        datas: [
          { label: "默认", value: "default" },
          { label: "折叠", value: "fold" },
        ],
      },
    ],
    data: [
      
    ],
    other: [
      {
        component: "switch",
        key: "readonly",
        label: "是否只读",
      }
    ],
  };
  