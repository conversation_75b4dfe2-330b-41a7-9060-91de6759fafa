// IE兼容性 垫片
import '@/core/lang/index.js';
 
// import VueI18n from 'vue-i18n'
import customCom from './core/ComModule.vue'

import version from '@/version.js'
// import enUS from './core/lang/en'
// import zhCN from './core/lang/zh'

// import {getCookie} from './core/utils/util.js'
import comProps from './props'
import comPropsCfg from './props.cfg'
import lifecycleMixin from "@/core/mixins/lifecycle.mixin.js";//组件生命周期管理，自定义组件入口文件一定要带上
const cfgJson=require('../package.json')
//将项目名称设为组件名
const comName=cfgJson.name //自定义组件名称

const components = [customCom]

// const localeLang=getCookie("EMAP_LANG")||'zh'
const install = function (Vue) {
    components.forEach(component => {
      if (component.mixins) {
        component.mixins.push(lifecycleMixin);
      } else {
        component.mixins = [lifecycleMixin];
      }
      Vue.component(comName, component)
    })
    
}

if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue);
}

export default {
  version,
  install,
  comProps,
  comPropsCfg,
  [comName]:customCom
}
