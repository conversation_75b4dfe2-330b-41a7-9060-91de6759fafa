const path = require('path');

const resolve = dir => path.resolve(__dirname, dir);
const fs = require('fs')
const dev_url="http://39.100.144.7:6882"
const cfgJson= JSON.parse(fs.readFileSync(path.resolve(__dirname, 'package.json'), 'utf-8'))
const devMode = process.env.NODE_ENV === 'development'
module.exports = {
    publicPath: './',//公共路径需要依据对应项目自行配置
    productionSourceMap: false,
    outputDir:"./"+cfgJson.name,
    chainWebpack: config => {
        config.resolve.alias
            .set('@', resolve('src'))
    },
    configureWebpack(config) {  
        /**
         * 正式环境下需要剔除内部公共库的引入
         */ 

        config.externals= {
            vue: 'Vue',
            axios:'axios',
            'element-ui':'ELEMENT',
            'vue-i18n':'VueI18n',
            'vue-router':'VueRouter'
          };
      
       config.output.libraryTarget="window"
    },
    transpileDependencies: [//babel转换第三方代码

    ],
    pluginOptions: {
        'style-resources-loader': {
            preProcessor: 'less',
            patterns: [
                'src/core/less/global.less',
            ],
        },
    },
    css: {
        extract: false,
      },
    // devServer: {
    //     port: 6001,
    //     open: false,
    //     proxy: {
    //         '/v6': {
    //             target: url,
    //             changeOrigin: true,
               
    //         }
            
    //     },
    // },
};
