<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
   
    <script>
      var $CommonVar = {
         
        apiPath: 'http://***********:8282/lowcode/', //api请求上下文， 智搭api请求上下文： http://***********:8383/lowcode/
        devPreviewUrl: 'http://************:8080/', //开发时预览地址
        vuecomponentUrl: 'http://***********:8081/fe_components/lowcode',
        isProdEnv: false, // 后端传递判断线上 or 线下：isProdEnv:false---线下，true---线上
        publicKey:"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDNulkiwTZ+7VfHtl43zv6ad6Ql5ZdyrfjzQprG8RetMoKVstSPNb6iLhhBlErW2i5CVQGE3yhc6iEAlLNt4zg9qlnmACeBVZGuMZBWwQZMu6ycJyIi4wG6V0YB53E0a9QaSfcSCBM8ftU0X9TIwQoRSwvLPopXi4DucSixWvTl9QIDAQAB",
        privateKey:"MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAM26WSLBNn7tV8e2XjfO/pp3pCXll3Kt+PNCmsbxF60ygpWy1I81vqIuGEGUStbaLkJVAYTfKFzqIQCUs23jOD2qWeYAJ4FVka4xkFbBBky7rJwnIiLjAbpXRgHncTRr1BpJ9xIIEzx+1TRf1MjBChFLC8s+ileLgO5xKLFa9OX1AgMBAAECgYEAiOWJzuC3PLr/AHxQMd7h+TPH3RfsMXmnAWi+ycdAtBW6Y5b+btWapxz5MxpUuqewxJ8ARcShfUKm91X8GBFtKBlcGcGmqFatcL/hrMrlhZueF7LmzZnMR87FcDdIqweq7xv9GfWLcgI3LLMUrs6r4SZsVgKAm8OneQDtY1XJ9HkCQQD3Z0vGHYvOMoV05iqIJ+OrfW5z2DZzUXtOEb4cK/keHAQf63xks1LbydwOwkstm4bucIvmm2rK06RWywWCYZybAkEA1OBW65lDRx4e2wLhI4XZcpgkVl9Aw6UbXMPkMwDvjWk+u9YteSvbs/eRmMNNUJbqtrGZQiy2jqrL8CIfTmIIrwJAMw2p4VQviXl7eMgWdspkfPsBU/6GHf3uiAm5RW79lW0KnNuna9BlhN1+/7ywbtTtXz7yX8AqpXhPLWnv1Rv3iQJBAMDP8eqzhxyDS69TjFiAg9QnucIBxMdwZLhBNhB8aH3NNeUsuUNnVjhLpLSZMQ4to6qWchpeJXxTdySpw3FbmkECQQCR7xaAdqLYpTNsZ7CU9xXrT4gzgi+cR7xRtc0yF+m1NErJ5riNadJ79gBw1Dn6JbuPcm4VRbPnQaTFTe/I9xPK",
        encryptDataPrefix:'lowcode4j',
     
        
        // scripts:[
        //   'http://***********:8081/fe_components/lowcode/third-party/vxe-table@3.x/xe-utils/xe-utils.umd.min.js',
        //   "http://***********:8081/fe_components/lowcode/third-party/vxe-table@3.x/vxe-table/index.umd.min.js",
        //   "/plugins/making-page/page.making.umd.js",
        //   "/plugins/api-designer/rules.engine.umd.js",
        //   "/plugins/res-com/res.components.umd.js"
        // ]
      }
    </script>
    <!-- 引入样式 -->
    <!-- <link rel="stylesheet" href="http://***********:8081/fe_components/lowcode/third-party/element-ui/lib@2.15.10/theme-chalk/index.css"> -->
    <script src="http://***********:8081/fe_components/lowcode/third-party/lodash/4.17.4/lodash.min.js"></script>
    <script src="http://***********:8081/fe_components/lowcode/global.config.js" ></script>
    <script src="http://***********:8081/fe_components/lowcode/third-party/vue-2.7/vue.min.js"></script>
    <script src="http://***********:8081/fe_components/lowcode/third-party/vue-i18n/v8.27.2/vue-i18n.min.js"></script>
    <script src="http://***********:8081/fe_components/lowcode/third-party/vue-router/dist@3.x/vue-router.min.js"></script>
    <!-- <script src="http://***********:8081/fe_components/lowcode/third-party/axios/axios.0.27.min.js"></script> -->
    <!-- <script src="http://***********:8081/fe_components/lowcode/third-party/element-ui/lib@2.15.10/index.js"></script> -->
    <!-- <script src="http://***********:8081/fe_components/lowcode/third-party/vxe-table@3.x/xe-utils/xe-utils.umd.min.js"></script>
    <script src="http://***********:8081/fe_components/lowcode/third-party/vxe-table@3.x/vxe-table/index.umd.min.js"></script> -->
    <script src="http://***********:8081/fe_components/lowcode/res-com/res.components.umd.js"></script>

    <script src="http://***********:8081/fe_components/lowcode/third-party/requirejs/require.js"></script>
    <script>
      window.require.config({
        baseUrl: 'http://***********:8081/fe_components/lowcode/third-party/',
        urlArgs: 'v=123',
        paths: {
          text: 'requirejs/text',
          json: 'requirejs/json',
          css: 'requirejs/css',
          vs: 'monaco-editor/min/vs',
          Vue:'requirejs/vue.module',
          vue:'requirejs/vue.module',
          _:'requirejs/_.module',
          lodash:'requirejs/_.module',
          axios:'requirejs/axios.module',
          ELEMENT:'requirejs/ELEMENT.module',
          'element-ui':'requirejs/ELEMENT.module',
          Vant:'requirejs/Vant.module',
          vant:'requirejs/Vant.module',
          VueI18n:'requirejs/VueI18n.module',
          'vue-i18n':'requirejs/VueI18n.module',
          VueRouter:'requirejs/VueRouter.module',
          'vue-router':'requirejs/VueRouter.module',
          'page.making':'http://***********:8081/fe_components/lowcode/making-page/page.making.umd',
          'FormMaking':'http://***********:8081/fe_components/lowcode/form-making/FormMaking.umd',
          'ExcelMaking':'http://***********:8081/fe_components/lowcode/excel-making/ExcelMaking.umd',
          'rules.engine':'http://***********:8081/fe_components/lowcode/api-designer/rules.engine.umd',
          'res.mobile.components':'http://***********:8081/fe_components/lowcode/mobile-coms/res.mobile.components.umd'
        },
        'vs/nls': {
          availableLanguages: {
            '*': 'zh-cn',
          },
        },
      })
    </script>
     <!-- vxe-tableh结束 -->
    <title><%= htmlWebpackPlugin.options.title %></title>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
  </body>
</html>
