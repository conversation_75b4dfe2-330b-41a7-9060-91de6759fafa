// 为了实现组件库与底座主题联动，需要使用css变量
/* 圆角 */
@border-radius-base:var(--border-radius-base,4px);
/* 颜色 */
@color-primary: var(--color-primary,#1890ff);
@color-success: var(--color-success,#13ce66);
@color-warning:var(--color-warning,#ff6700);
@color-danger:var(--color-danger,#ff4d4f);
@color-info: var(--color-info,rgba(0, 0, 0, 0.65));
@color-white:var(--color-white,#ffffff);
@color-black:var(--color-black,#515a6e);

@color-text-primary: var(--color-text-primary,#303133);
@color-text-regular: var(--color-text-regular,#606266);
@color-text-secondary: var(--color-text-secondary,#909399);
@color-text-placeholder: var(--color-text-placeholder,#c0c4cc);
@border-color-base: var(--border-color-base,#dcdfe6);
@border-color-light: var(--border-color-light,#e4e7ed);
@border-color-lighter: var(--border-color-lighter,#ebeef5);
@border-color-extra-light: var(--border-color-extra-light,#f2f6fc);
@background-color-base: var(--background-color-base,#f5f7fa);

