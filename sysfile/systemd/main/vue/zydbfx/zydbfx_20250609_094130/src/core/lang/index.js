import Vue from "vue"
import VueI18n from "vue-i18n";
import enUS from "./en-US";
import zhCN from "./zh-CN";
import cookie from 'js-cookie'
// const cfgJson=require('/package.json')
const localeName=window.$GLOBAL_CONFIG?.localeName||'EMAP_LANG'
const lang=cookie.get(localeName)||'zh'
const messages={
    en:{
        ...enUS
    },
    zh:{
        ...zhCN
    }
}

// console.log('lang',lang)
const initLang=function(){
    if(window.localI18n){
        let i18n=window.localI18n
        i18n.setLocaleMessage("en", { ...i18n.messages["en"], ...messages.en  });
        i18n.setLocaleMessage("zh", { ...i18n.messages["zh"], ...messages.zh });
        i18n.locale = lang;
        return window.localI18n
    }else{
        Vue.use(VueI18n);
        window.localI18n = new VueI18n({
            locale: lang,
            messages: {
              en: messages.en,
              zh: messages.zh,
            },
          });
          const init = Vue.prototype._init;
          Vue.prototype._init = function (options) {
            init.call(this, {
              i18n: window.localI18n,
              ...options,
            });
          };
        return window.localI18n
    }
}
const i18n=initLang()
export const $LANG= function() {
    let args = Array.prototype.slice.call(arguments);
    let props=args.slice(1);
    if (i18n&&i18n.te(args[0])){
      return i18n.t(...args)
    } else if(i18n&&i18n.te(args[0],'zh')){
     return i18n.t(args[0],'zh',...props)
    }
    return args[0]||''
  }

Vue.prototype.$LANG=$LANG
Vue.prototype.$LOCAL=lang