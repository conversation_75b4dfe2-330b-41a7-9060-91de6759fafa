//样式公共方法文件
// 超出显示省略号 单行
.ellipsis() {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

// 多行省略
.ellipsis_more(@row : 2) {
    display: -webkit-box;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: @row;
    overflow: hidden;
}

// 超多多行显示省略号
.ellipsiss(@row : 2) {
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: @row;
    line-clamp: @row;
    -webkit-box-orient: vertical;
}
// 边框公用方法
.border-common(@width:1px,@style:solid, @color:#f5f5f5) {
    border: @width @style @color;
}
// 底部边框公用方法
.border-bottom(@width:1px,@style:solid, @color:#f5f5f5) {
    border-bottom: @width @style @color;
}
//上下文字居中
.text-v-center(@h:32px) {
    height: @h;
    line-height: @h;
}
.user-select(@type:none) {
    :not(input, textarea) {
        -moz-user-select: @type;
        -o-user-select: @type;
        -khtml-user-select: @type;
        -webkit-user-select: @type;
        -ms-user-select: @type;
        user-select: @type;
    }
}
