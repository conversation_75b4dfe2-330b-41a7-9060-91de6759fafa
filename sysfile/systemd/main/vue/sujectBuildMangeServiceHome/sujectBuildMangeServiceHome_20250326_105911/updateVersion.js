const moment = require("moment");
const fs = require("fs");

const simpleGit = require("simple-git");

const getBranch = async () => {
  const git = simpleGit();
  let log= await git.log({ n: 1 });
  const status = await git.status();
  const gitInfo = { ...log.latest, tracking: status.tracking };
  console.log('log',gitInfo)
  delete gitInfo.author_email
  delete gitInfo.body
  return gitInfo;
};

const makeVersionFile = function (git) {
  // 初始化数据
  const fileName = "./src/version.js";
  const datetime = moment().format("YYYY-MM-DD HH:mm:ss");
  const defaultVersion = `export default { version: '1.0.0', updateTime: '${datetime}',git:${JSON.stringify( git)}}`;

  // 更新版本信息
  writeJson(defaultVersion);
  // 写入json文件
  function writeJson(version) {
    try {
      console.log(`-> 正在写入文件: `, fileName);
      const write = fs.writeFileSync(fileName, version, {
        flag: "w+",
        encoding: "utf-8",
      });
      console.log(`-> 写入 version 成功`);
    } catch (err) {
      console.log(`-> 写入 version 失败: `, err);
      throw err;
    }
  }
};

getBranch().then((git) => {
	makeVersionFile(git)
}).catch(err=>{
	makeVersionFile({tracking:'无法获取git信息'})
})
