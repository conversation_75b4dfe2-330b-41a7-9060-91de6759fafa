<template>
  <div class="subject_atlas_main">
    <div class="left-content">
      <ClassCount
        :page="page"
        :activeTab="activeTab"
        :countList="countList"
        @jumpDetail="jumpDetail"
      />
      <ClassList
        :activeTab="activeTab"
        :xKJBXXList="xKJBXXList"
        :xkmlflSet="xkmlflSet"
      />
    </div>
    <div class="right-content">
      <!-- <div class="ledgend-list">
        <div
          v-for="(item, idx) in legendMap"
          :key="idx"
          :style="{ '--legend-icon--color': item[0] }"
          @click="clickLegend(item, idx)"
        >
          <svg
            width="13"
            height="13"
            viewBox="0 0 13 13"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M6.5 0L12.1292 3.25V9.75L6.5 13L0.870835 9.75V3.25L6.5 0Z"
              fill="#FFD6DE"
            />
          </svg>

          <span>{{ idx }}</span>
        </div>
      </div> -->
      <!-- <img :src="rightBg" alt="" style="height: 100%; width: 100%" /> -->
      <div class="xktp-wraper" v-if="activeTab == 0">
        <graphChartsCom></graphChartsCom>
        <!-- <div class="main-chart">
          <graphChartsCom
            :data="resourceData1"
            :linksData="linksData1"
          ></graphChartsCom>
        </div>
        <div
          class="child"
          :style="{
            left: positionArr[idx].x + '%',
            top: positionArr[idx].y + '%',
          }"
          v-for="(item, idx) in xkArr1"
          :key="idx"
        >
          <graphChartsCom :data="item" :xkArr1="xkArr1"></graphChartsCom>
        </div> -->
      </div>
      <div class="xktp-wraper" v-if="activeTab == 2">
        <div style="height: 100%; text-align: center">
          <img
            src="../assets/image/zyxwBg.svg"
            style="width: 864px; height: 100%; margin-top: 10px"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import rightBg from "../assets/image/rightBg.png";
import ClassList from "@/core/view/classList.vue";
import ClassCount from "@/core/view/classCount.vue";
import bg from "../assets/image/yuanBg.png";
import ziseYuanBg from "../assets/image/ziseYuanBg.png";
import graphChartsCom from "@/core/component/graphCharts.vue";
import { queryDataByScriptId } from "@/core/services/public.service.js";
import _ from "lodash";
const legendMap = {
  人文艺术: ["rgba(255,117,145,1)", "rgba(255,117,145,0.1)"],
  社会科学: ["rgba(255,162,0,1)", "rgba(255,162,0,0.1)"],
  理学: ["rgba(107, 103, 252, 1)", "rgba(107, 103, 252, 0.1)"],
  工学: ["rgba(22, 93, 255, 1)", "rgba(22, 93, 255, 0.1)"],
  农学: ["rgba(93, 207, 104, 1)", "rgba(93, 207, 104, 0.1)"],
  医学: ["rgba(0, 187, 199, 1)", "rgba(0, 187, 199, 0.1)"],
  交叉学科: ["rgba(218, 104, 247, 1)", "rgba(218, 104, 247, 0.1)"],
};
export default {
  components: { ClassList, ClassCount, graphChartsCom },
  props: {
    page: Object,
    activeTab: Number,
  },
  watch: {
    activeTab(val) {
      if (val == 2) {
        this.getXKJBXX();
      }
    },
    // copyxkArr: {
    //   handler(val) {
    //     console.log(val, "====val222=====");
    //   },
    //   immediate: true,
    //   deep: true,
    // },
  },
  computed: {
    xkArr1() {
      return this.copyxkArr.length ? this.copyxkArr : this.xkArr;
    },
    resourceData1() {
      return this.copyResourceData.length
        ? this.copyResourceData
        : this.resourceData;
    },
    linksData1() {
      return this.copyLinksData.length ? this.copyLinksData : this.linksData;
    },
    defaultLineStyle() {
      return {
        color: "#a0a0a0", // 设置连线颜色
        width: 1,
        opacity: 1,
      };
    },
    borderColor() {
      return ["rgba(255,255,255,1)", "rgba(255,255,255,0.1)"];
    },
    labelColor() {
      return ["rgba(255,255,255,1)", "rgba(255,255,255,0.1)"];
    },
    xkArr() {
      return [
        // [
        //   { label: '高教所', isXY: true },
        //   { label: '教育学', isXY: false, color: legendMap['社会科学'] },
        // ],
        // [
        // { label: '科信所', isXY: true },
        // {
        //   label: '信息资源管理',
        //   isXY: false,
        //   color: legendMap['社会科学'],
        // },
        // ],
        [
          {
            label: "法学院",
            isXY: true,
          },
          {
            label: "法学",
            isXY: false,
            color: legendMap["社会科学"][0],
            lineStyle: this.defaultLineStyle,
            borderColor: this.borderColor[0],
            labelColor: this.labelColor[0],
          },
        ],
        [
          { label: "社会学院", isXY: true },
          {
            label: "社会学",
            isXY: false,
            color: legendMap["社会科学"][0],
            lineStyle: this.defaultLineStyle,
            borderColor: this.borderColor[0],
            labelColor: this.labelColor[0],
          },
          {
            label: "教育学",
            isXY: false,
            color: legendMap["社会科学"][0],
            lineStyle: this.defaultLineStyle,
            borderColor: this.borderColor[0],
            labelColor: this.labelColor[0],
          },
          {
            label: "公共管理学",
            isXY: false,
            color: legendMap["社会科学"][0],
            lineStyle: this.defaultLineStyle,
            borderColor: this.borderColor[0],
            labelColor: this.labelColor[0],
          },
        ],
        [
          { label: "数学院", isXY: true },
          {
            label: "数学",
            isXY: false,
            color: legendMap["理学"][0],
            lineStyle: this.defaultLineStyle,
            borderColor: this.borderColor[0],
            labelColor: this.labelColor[0],
          },
        ],

        [
          { label: "物理学院", isXY: true },
          {
            label: "物理学",
            isXY: false,
            color: legendMap["理学"][0],
            lineStyle: this.defaultLineStyle,
            borderColor: this.borderColor[0],
            labelColor: this.labelColor[0],
          },
        ],
        [
          { label: "信息学院", isXY: true },
          {
            label: "控制科学与工程",
            isXY: false,
            color: legendMap["工学"][0],
            lineStyle: this.defaultLineStyle,
            borderColor: this.borderColor[0],
            labelColor: this.labelColor[0],
          },
          {
            label: "信息与通信工程",
            isXY: false,
            color: legendMap["工学"][0],
            lineStyle: this.defaultLineStyle,
            borderColor: this.borderColor[0],
            labelColor: this.labelColor[0],
          },
          {
            label: "计算机科学与技术",
            isXY: false,
            color: legendMap["工学"][0],
            lineStyle: this.defaultLineStyle,
            borderColor: this.borderColor[0],
            labelColor: this.labelColor[0],
          },
        ],
        [
          { label: "材料学院", isXY: true },
          {
            label: "材料科学与工程",
            isXY: false,
            color: legendMap["工学"][0],
            lineStyle: this.defaultLineStyle,
            borderColor: this.borderColor[0],
            labelColor: this.labelColor[0],
          },
        ],
        [
          { label: "资环学院", isXY: true },
          {
            label: "环境科学与工程",
            isXY: false,
            color: legendMap["工学"][0],
            lineStyle: this.defaultLineStyle,
            borderColor: this.borderColor[0],
            labelColor: this.labelColor[0],
          },
          {
            label: "安全科学与工程",
            isXY: false,
            color: legendMap["工学"][0],
            lineStyle: this.defaultLineStyle,
            borderColor: this.borderColor[0],
            labelColor: this.labelColor[0],
          },
        ],
        [
          { label: "机动学院", isXY: true },
          {
            label: "机械工程",
            isXY: false,
            color: legendMap["工学"][0],
            lineStyle: this.defaultLineStyle,
            borderColor: this.borderColor[0],
            labelColor: this.labelColor[0],
          },
          {
            label: "动力工程及工程热物理",
            isXY: false,
            color: legendMap["工学"][0],
            lineStyle: this.defaultLineStyle,
            borderColor: this.borderColor[0],
            labelColor: this.labelColor[0],
          },
        ],
        [
          { label: "艺术学院", isXY: true },
          {
            label: "设计学",
            isXY: false,
            color: legendMap["交叉学科"][0],
            lineStyle: this.defaultLineStyle,
            borderColor: this.borderColor[0],
            labelColor: this.labelColor[0],
          },
        ],
        [
          { label: "外语学院", isXY: true },
          {
            label: "外国语言文学",
            isXY: false,
            color: legendMap["人文艺术"][0],
            lineStyle: this.defaultLineStyle,
            borderColor: this.borderColor[0],
            labelColor: this.labelColor[0],
          },
        ],
        [
          { label: "马克思主义学院", isXY: true },
          {
            label: "哲学",
            isXY: false,
            color: legendMap["人文艺术"][0],
            lineStyle: this.defaultLineStyle,
            borderColor: this.borderColor[0],
            labelColor: this.labelColor[0],
          },
          {
            label: "马克思主义理论",
            isXY: false,
            color: legendMap["社会科学"][0],
            lineStyle: this.defaultLineStyle,
            borderColor: this.borderColor[0],
            labelColor: this.labelColor[0],
          },
        ],
        [
          { label: "商学院", isXY: true },
          {
            label: "工商管理学",
            isXY: false,
            color: legendMap["社会科学"][0],
            lineStyle: this.defaultLineStyle,
            borderColor: this.borderColor[0],
            labelColor: this.labelColor[0],
          },
          {
            label: "信息资源管理",
            isXY: false,
            color: legendMap["社会科学"][0],
            lineStyle: this.defaultLineStyle,
            borderColor: this.borderColor[0],
            labelColor: this.labelColor[0],
          },
          {
            label: "管理科学与工程",
            isXY: false,
            color: legendMap["社会科学"][0],
            lineStyle: this.defaultLineStyle,
            borderColor: this.borderColor[0],
            labelColor: this.labelColor[0],
          },
          {
            label: "应用经济学",
            isXY: false,
            color: legendMap["社会科学"][0],
            lineStyle: this.defaultLineStyle,
            borderColor: this.borderColor[0],
            labelColor: this.labelColor[0],
          },
        ],
      ];
    },
  },
  data() {
    return {
      xKJBXXList: [],
      xkmlflSet: [],
      countList: {
        xkhj: "",
        sssqd: "",
        bssqd: "",
      },
      rightBg,
      resourceData: [],
      bg,
      ziseYuanBg,
      positionArr: [],
      linksData: [],
      copyLinksData: [],
      legendMap,
      copyxkArr: [],
      copyResourceData: [],
      lineStyle: {
        color: "#a0a0a0", // 设置连线颜色
        width: 1,
        opacity: 0,
      },
    };
  },
  created() {
    this.copyxkArr = _.cloneDeep(this.xkArr);
    this.resourceData = [
      {
        label: "化学工程与技术",
        isXY: false,
        color: legendMap["工学"][0],
        lineStyle: this.defaultLineStyle,
        borderColor: this.borderColor[0],
        labelColor: this.labelColor[0],
      },
      { label: "化学学院", isXY: true },
      {
        label: "化学",
        isXY: false,
        color: legendMap["理学"][0],
        lineStyle: this.defaultLineStyle,
        borderColor: this.borderColor[0],
        labelColor: this.labelColor[0],
      },
      { label: "化工学院", isXY: true },
      {
        label: "石油与天然气工程",
        isXY: false,
        color: legendMap["工学"][0],
        lineStyle: this.defaultLineStyle,
        borderColor: this.borderColor[0],
        labelColor: this.labelColor[0],
      },
      { label: "生工学院", isXY: true },
      {
        label: "生物工程",
        isXY: false,
        color: legendMap["工学"][0],
        lineStyle: this.defaultLineStyle,
        borderColor: this.borderColor[0],
        labelColor: this.labelColor[0],
      },
      {
        label: "生物医学工程",
        isXY: false,
        color: legendMap["工学"][0],
        lineStyle: this.defaultLineStyle,
        borderColor: this.borderColor[0],
        labelColor: this.labelColor[0],
      },
      {
        label: "食品科学与工程",
        isXY: false,
        color: legendMap["工学"][0],
        lineStyle: this.defaultLineStyle,
        borderColor: this.borderColor[0],
        labelColor: this.labelColor[0],
      },
      {
        label: "生物学",
        isXY: false,
        color: legendMap["理学"][0],
        lineStyle: this.defaultLineStyle,
        borderColor: this.borderColor[0],
        labelColor: this.labelColor[0],
      },
      { label: "药学院", isXY: true },
      {
        label: "植物保护",
        isXY: false,
        color: legendMap["农学"][0],
        lineStyle: this.defaultLineStyle,
        borderColor: this.borderColor[0],
        labelColor: this.labelColor[0],
      },
      {
        label: "药学",
        isXY: false,
        color: legendMap["医学"][0],
        lineStyle: this.defaultLineStyle,
        borderColor: this.borderColor[0],
        labelColor: this.labelColor[0],
      },
    ];
    this.linksData = [
      {
        target: "药学院",
        source: "化学工程与技术",
        category: "",
        color: legendMap["工学"][0],
      },
      {
        target: "植物保护",
        source: "药学院",
        category: "",
        color: legendMap["农学"][0],
      },
      {
        target: "药学",
        source: "药学院",
        category: "",
        color: legendMap["医学"][0],
      },
      {
        target: "化学学院",
        source: "化学工程与技术",
        category: "",
        color: legendMap["工学"][0],
      },
      {
        target: "化学",
        source: "化学学院",
        category: "",
        color: legendMap["理学"][0],
      },
      {
        target: "化工学院",
        source: "化学工程与技术",
        category: "",
        color: legendMap["工学"][0],
      },
      {
        target: "石油与天然气工程",
        source: "化工学院",
        category: "",
        color: legendMap["工学"][0],
      },
      {
        target: "生工学院",
        source: "化学工程与技术",
        category: "",
        color: legendMap["工学"][0],
      },
      {
        target: "生物工程",
        source: "生工学院",
        category: "",
        color: legendMap["工学"][0],
      },
      {
        target: "生物医学工程",
        source: "生工学院",
        category: "",
        color: legendMap["工学"][0],
      },
      {
        target: "食品科学与工程",
        source: "生工学院",
        category: "",
        color: legendMap["工学"][0],
      },
      {
        target: "生物学",
        source: "生工学院",
        category: "",
        color: legendMap["理学"][0],
      },
    ];
    this.calcPosition();
  },
  methods: {
    clickLegend(item, idx) {
      this["filterFlag" + idx] = !this["filterFlag" + idx];
      this.copyResourceData = _.cloneDeep(this.resourceData1);
      this.copyResourceData.forEach((m) => {
        if (m.color && (m.color == item[0] || m.color == item[1])) {
          m.lineStyle = this["filterFlag" + idx]
            ? this.lineStyle
            : this.defaultLineStyle;
          m.color = this["filterFlag" + idx] ? item[1] : item[0];
          m.borderColor = this["filterFlag" + idx]
            ? this.borderColor[1]
            : this.borderColor[0];
          m.labelColor = this["filterFlag" + idx]
            ? this.labelColor[1]
            : this.labelColor[0];
        }
      });
      this.copyLinksData = _.cloneDeep(this.linksData1);
      this.copyLinksData.forEach((m) => {
        if (m.color && (m.color == item[0] || m.color == item[1])) {
          m.lineStyle = this["filterFlag" + idx]
            ? this.lineStyle
            : this.defaultLineStyle;
        }
      });
      this.copyxkArr = _.cloneDeep(this.xkArr1);
      this.copyxkArr.forEach((m, ix) => {
        let arr = [];
        for (let index = 0; index < m.length; index++) {
          if (
            m[index].color &&
            (m[index].color == item[0] || m[index].color == item[1])
          ) {
            m[index].lineStyle = this["filterFlag" + idx]
              ? this.lineStyle
              : this.defaultLineStyle;
            m[index].color = this["filterFlag" + idx] ? item[1] : item[0];
            m[index].borderColor = this["filterFlag" + idx]
              ? this.borderColor[1]
              : this.borderColor[0];
            m[index].labelColor = this["filterFlag" + idx]
              ? this.labelColor[1]
              : this.labelColor[0];
          }
          arr.push(m[index]);
        }
        this.$set(this.copyxkArr, ix, arr);
      });
    },
    getXKJBXX() {
      queryDataByScriptId("XKJBXX", {
        arrSchema: [],
        order: "",
        params: {},
        querySetting: [
          {
            name: "xwlx",
            value: "专业型",
            linkOpt: "and",
            builder: "m_value_equal",
          },
        ],
      }).then((res) => {
        if (res.code == "0" && res.data) {
          this.xKJBXXList =
            res.data.map((m) => {
              return {
                ...m,
                xwsqd:
                  m?.sfbsd == "是"
                    ? "博士点"
                    : m?.sfssd == "是"
                    ? "硕士点"
                    : "",
              };
            }) || [];
          this.countList.bssqd = this.xKJBXXList.filter(
            (f) => f?.sfbsd == "是"
          )?.length;
          this.countList.sssqd = this.xKJBXXList.filter(
            (f) => f?.sfssd == "是"
          )?.length;
          this.countList.xkhj = this.xKJBXXList.length;
          this.xkmlflSet = _.groupBy(this.xKJBXXList, "xkmlfl");
        }
      });
    },
    jumpDetail(params) {
      this.$emit("jumpDetail", params);
    },
    calcPosition() {
      let h = 50; // 椭圆中心点的x坐标
      let k = 53; // 椭圆中心点的y坐标
      let a = 37; // 长轴r
      let b = 36; // 短轴r
      let theta = 0; // 椭圆旋转角度 0
      let n = this.xkArr.length;
      // 计算每个点之间的角度差
      let deltaTheta = (2 * Math.PI) / n;
      let points = [];
      for (let i = 0; i < n; i++) {
        // 计算当前点的角度
        let currentTheta = theta + i * deltaTheta;
        // 使用椭圆的参数方程计算坐标
        let x =
          h +
          a * Math.cos(currentTheta) * Math.cos(theta) -
          b * Math.sin(currentTheta) * Math.sin(theta);
        let y =
          k +
          a * Math.cos(currentTheta) * Math.sin(theta) +
          b * Math.sin(currentTheta) * Math.cos(theta);

        this.positionArr.push({ x: x, y: y });
      }
      console.log(points, 294294);
    },
    handleChoose(params) {
      console.log("跳转", params, params.data.category);
      // this.$router.push({
      //   path: 'xkhx',
      //   query: {
      //     xkbh: item.xkbh,
      //     xkmc: item.xkmc,
      //   },
      // });
      const url =
        window.location.origin +
        window.$GLOBAL_CONFIG.apiPath +
        `app/doublefirstclassdata#/doublefirstclassdata/xkhx/xkgl?xkmc=${params.data.name}`;
      window.open(url, "_blank");
    },
    setData(label, isParent, color = "transparent") {
      console.log(color);
      if (isParent) {
        return {
          name: label,
          draggable: true,
          symbolSize: [14, 14],
          itemStyle: {
            color: "#82a7ca",
          },
          label: {
            show: true,
            position: "bottom",
            backgroundColor: "#ffffff",
            borderRadius: 3,
            padding: 2,
            color: "#000000",
            fontSize: 9,
            lineHeight: 12,
            formatter: this.formatter(label),
          },
          category: "学院",
        };
      } else {
        return {
          name: label,
          draggable: true,
          symbolSize: [50, 50],
          itemStyle: {
            color: color,
            borderWidth: 3,
            borderColor: "#fff",
          },
          label: {
            formatter: this.formatter(label),
          },
          category: "学科",
        };
      }
    },
    formatter(val) {
      let pieces = [];
      for (var i = 0; i < val.length; i += 4) {
        pieces.push(val.slice(i, i + 4));
      }
      return pieces.join("\n");
    },
  },
};
</script>

<style lang="less" scoped>
.subject_atlas_main {
  height: 684px;
  border-radius: 7px;
  border: 1px solid #e5e6eb;
  display: flex;
  .left-content {
    width: 312px;
    display: flex;
    flex-direction: column;
  }
  .right-content {
    flex: 1;
    padding: 13px 0;
    border-left: 1px solid #e5e6eb;
    display: flex;
    align-items: center;
    justify-content: center;
    background: url("../assets/image/graphBg.png") 100% 100% no-repeat;
    background-position: center;
    background-size: cover;
    img {
      object-fit: contain;
    }
    .xktp-wraper {
      width: 100%;
      height: 100%;
      position: relative;
      .main-chart {
        height: 373px;
        width: 610px;
        position: absolute;
        top: 53%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
      .child {
        position: absolute;
        transform: translate(-50%, -50%);
      }
    }
    height: 100%;
    position: relative;
    .ledgend-list {
      position: absolute;
      right: 15px;
      top: 14px;
      display: flex;
      align-items: center;
      column-gap: 16px;
      font-family: Microsoft YaHei UI;
      font-size: 13px;
      font-weight: 400;
      line-height: 16.51px;
      letter-spacing: 0.05em;
      text-align: center;
      color: #4e5969;
      div {
        cursor: pointer;
        z-index: 999;
      }
      svg {
        margin-right: 4px;
      }
      svg path {
        fill: var(--legend-icon--color);
      }
    }
  }
}
</style>
