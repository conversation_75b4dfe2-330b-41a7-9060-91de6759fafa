<template>
  <div class="graph_wraper">
    <res-charts
      @click="handleChoose"
      :option="{
        title: {},
        tooltip: {
          show: true,
          formatter: function (params) {
            if (params.dataType === 'node') {
              return params.data.name;
            }
            return '';
          },
        },
        legend: {
          icon: 'path://M6.5 0L12.1292 3.25V9.75L6.5 13L0.870835 9.75V3.25L6.5 0Z',
          data: xkCategories,
          top: 0,
          right: 10,
          itemGap: 20,
          itemWidth: 13,
          itemHeight: 13,
          textStyle: { fontSize: 12, lineHeight: 13, color: '#1d2129' },
        },
        series: [
          {
            type: 'graph',
            layout: 'force',
            roam: true, // 支持缩放和拖拽
            draggable: true, // 节点可拖拽
            label: {
              show: true,
              position: 'inside',
              color: '#fff',
              align: 'center',
              verticalAlign: 'middle',
              offset: [0, 10],
              formatter: function (params) {
                // 获取节点的名字
                let name = params.data.name;

                // 每两个字加一个换行符
                let formatted = name.replace(/(.{2})/g, '$1\n');

                // 限制最多两行，超过两行加省略号
                let lines = formatted.split('\n').filter((line) => line);
                if (lines.length > 2) {
                  return `${lines[0]}\n${lines[1]}...`;
                }

                return formatted;
              },
              lineHeight: 15, // 行高设置
              fontSize: 12, // 字体大小
            },
            force: {
              repulsion: 1000,
              gravity: 0.1, // 向心力
              // 值最大的边长度会趋向于 10，值最小的边长度会趋向于 50 [10,50]
              edgeLength: edgeLength,
            },
            categories: categories,
            data: seriesData,
            links: linksArr,
          },
        ],
      }"
      height="700"
    >
    </res-charts>
  </div>
</template>

<script>
import _ from "lodash";
import bg from "../assets/image/yuanBg.png";
import ziseYuanBg from "../assets/image/ziseYuanBg.png";
const legendColorMap = {
  0: ["rgba(255,117,145,1)", "rgba(255,117,145,0.1)"],
  1: ["rgba(255,162,0,1)", "rgba(255,162,0,0.1)"],
  2: ["rgba(107, 103, 252, 1)", "rgba(107, 103, 252, 0.1)"],
  3: ["rgba(22, 93, 255, 1)", "rgba(22, 93, 255, 0.1)"],
  4: ["rgba(93, 207, 104, 1)", "rgba(93, 207, 104, 0.1)"],
  5: ["rgba(0, 187, 199, 1)", "rgba(0, 187, 199, 0.1)"],
  6: ["rgba(218, 104, 247, 1)", "rgba(218, 104, 247, 0.1)"],
};
export default {
  props: {
    page: Object,
    height: Number,
    data: Array,
    linksData: Array,
    xkArr1: Array,
  },
  data() {
    return {
      legendMap: {},
      resourceData: [],
      bg,
      ziseYuanBg,
      linksArr: [],
      edgeLength: [100, 200],
      xkCategories: [
        "人文艺术",
        "社会科学",
        "理学",
        "工学",
        "农学",
        "医学",
        "交叉学科",
      ],
      xkmlflSet: {}, //根据学科分类分组后的所有学科列表
      // 所有学科数据列表
      allXkList: [
        {
          xk: "应用经济学",
          xkmlfl: "社会科学",
          xkjc: "应经",
          xkbm: "0202",
          pmbh: 15,
          wcbfbz: 30,
          dbwcbfbz: 24,
        },
        {
          xk: "社会学",
          xkmlfl: "社会科学",
          xkjc: "社会",
          xkbm: "0303",
          pmbh: -1,
          wcbfbz: 24,
          dbwcbfbz: 24,
        },
        {
          xk: "马克思主义理论",
          xkmlfl: "社会科学",
          xkjc: "马论",
          xkbm: "0305",
          pmbh: 7,
          wcbfbz: 27,
          dbwcbfbz: 25,
        },
        {
          xk: "数学",
          xkmlfl: "理学",
          xkjc: "数学",
          xkbm: "0701",
          pmbh: 11,
          wcbfbz: 40,
          dbwcbfbz: 37,
        },
        {
          xk: "物理学",
          xkmlfl: "理学",
          xkjc: "物理",
          xkbm: "0702",
          pmbh: 14,
          wcbfbz: 43,
          dbwcbfbz: 38,
        },
        {
          xk: "化学",
          xkmlfl: "理学",
          xkjc: "化学",
          xkbm: "0703",
          pmbh: -2,
          wcbfbz: 8,
          dbwcbfbz: 9,
        },
        {
          xk: "外国语言文学",
          xkmlfl: "人文艺术",
          xkjc: "外文",
          xkbm: "0502",
          pmbh: -1,
          wcbfbz: 46,
          dbwcbfbz: 47,
        },
        {
          xk: "生物学",
          xkmlfl: "理学",
          xkjc: "生物",
          xkbm: "0710",
          pmbh: 60,
          wcbfbz: 49,
          dbwcbfbz: 25,
        },
        {
          xk: "机械工程",
          xkmlfl: "工学",
          xkjc: "机工",
          xkbm: "0802",
          pmbh: 1,
          wcbfbz: 21,
          dbwcbfbz: 21,
        },
        {
          xk: "控制科学与工程",
          xkmlfl: "工学",
          xkjc: "控制",
          xkbm: "0811",
          pmbh: 4,
          wcbfbz: 10,
          dbwcbfbz: 9,
        },
        {
          xk: "材料科学与工程",
          xkmlfl: "工学",
          xkjc: "材料",
          xkbm: "0805",
          pmbh: 0,
          wcbfbz: 14,
          dbwcbfbz: 14,
        },
        {
          xk: "动力工程及工程热物理",
          xkmlfl: "工学",
          xkjc: "动力",
          xkbm: "0807",
          pmbh: 2,
          wcbfbz: 18,
          dbwcbfbz: 15,
        },
        {
          xk: "计算机科学与技术",
          xkmlfl: "工学",
          xkjc: "计科",
          xkbm: "0812",
          pmbh: -2,
          wcbfbz: 31,
          dbwcbfbz: 31,
        },
        {
          xk: "化学工程与技术",
          xkmlfl: "工学",
          xkjc: "化工",
          xkbm: "0817",
          pmbh: 0,
          wcbfbz: 1,
          dbwcbfbz: 1,
        },
        {
          xk: "石油与天然气工程",
          xkmlfl: "工学",
          xkjc: "石油",
          xkbm: "0820",
          pmbh: 0,
          wcbfbz: 50,
          dbwcbfbz: 50,
        },
        {
          xk: "生物工程",
          xkmlfl: "工学",
          xkjc: "生工",
          xkbm: "0836",
          pmbh: -1,
          wcbfbz: 1,
          dbwcbfbz: 1,
        },
        {
          xk: "环境科学与工程",
          xkmlfl: "工学",
          xkjc: "环境",
          xkbm: "0830",
          pmbh: -5,
          wcbfbz: 16,
          dbwcbfbz: 19,
        },
        {
          xk: "生物医学工程",
          xkmlfl: "工学",
          xkjc: "生医",
          xkbm: "0831",
          pmbh: 11,
          wcbfbz: 39,
          dbwcbfbz: 28,
        },
        {
          xk: "管理科学与工程",
          xkmlfl: "社会科学",
          xkjc: "管科",
          xkbm: "1201",
          pmbh: -7,
          wcbfbz: 24,
          dbwcbfbz: 27,
        },
        {
          xk: "药学",
          xkmlfl: "医学",
          xkjc: "药学",
          xkbm: "1007",
          pmbh: 4,
          wcbfbz: 20,
          dbwcbfbz: 18,
        },
        {
          xk: "公共管理学",
          xkmlfl: "社会科学",
          xkjc: "公管",
          xkbm: "1204",
          pmbh: 1,
          wcbfbz: 43,
          dbwcbfbz: 42,
        },
        {
          xk: "工商管理学",
          xkmlfl: "社会科学",
          xkjc: "工商",
          xkbm: "1202",
          pmbh: -1,
          wcbfbz: 24,
          dbwcbfbz: 24,
        },
      ],
      categories: [], //关系图的分类集合
      xkCategoriesArr: [], //关系图data的学科分类数组
      xkArr: [], //关系图data的所有学科数组
    };
  },
  computed: {
    legendColorLength() {
      return Object.keys(legendColorMap).length;
    },
    xkTotalCircleArr() {
      return [
        {
          name: "学科",
          symbolSize: 75,
          category: "理学",
          symbol: "circle",
          itemStyle: {
            color: "#fff",
            borderColor: "#8ABFFC",
            borderWidth: 5,
            opacity: 1, // 确保透明度为1
          },
          label: {
            color: "#1D2129",
            fontSize: 12,
            align: "center",
            verticalAlign: "middle",
            offset: [0, 0],
            formatter: ["{a|学科}", `'{b|${this.allXkList.length}}个'`].join(
              "\n"
            ),
            rich: {
              a: {
                lineHeight: 30,
              },
              b: {
                color: "#1D2129",
                fontSize: 24,
                fontWeight: 800,
              },
            },
          },
        },
      ];
    },
    seriesData() {
      console.log(
        this.xkTotalCircleArr.concat(this.xkCategoriesArr).concat(this.xkArr),
        "---this.xkArr333"
      );
      return this.xkTotalCircleArr
        .concat(this.xkCategoriesArr)
        .concat(this.xkArr);
    },
  },
  watch: {
    data: {
      handler() {
        // this.init();
      },
      deep: true,
    },
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      console.log(this.legendColorLength, "======");
      this.xkCategories.map((m, index) => {
        this.legendMap[m] = legendColorMap[index % this.legendColorLength];
        let obj = {
          name: m,
          itemStyle: { color: this.legendMap[m][0] },
        };
        this.categories.push(obj);
        let cateObj = {
          name: m,
          symbol: "circle",
          symbolSize: 48,
          category: m,
        };
        this.xkCategoriesArr.push(cateObj);
      });
      this.xkmlflSet = _.groupBy(this.allXkList, "xkmlfl");
      for (let item in this.xkmlflSet) {
        console.log(item, "item");
        let xkobj = {
          name: "",
          symbolSize: 38,
          symbol: "circle",
          category: item,
          itemStyle: {
            color: "#fff",
            borderColor: this.legendMap[item][0],
            borderWidth: 2,
          },
          label: { color: "#4E5969", fontSize: 12 },
        };
        let linkobj = {
          source: "学科",
          target: item,
          value:
            Math.random() * this.edgeLength[0] +
            (this.edgeLength[1] - this.edgeLength[0]), // 生成随机长度
          lineStyle: {
            opacity: 1,
            width: 4,
            color: "#8ABFFC",
          },
        };
        this.linksArr.push(linkobj);
        let linkXkobj = {
          source: item,
          target: "",
          value:
            Math.random() * this.edgeLength[0] +
            (this.edgeLength[1] - this.edgeLength[0]), // 生成随机长度
          lineStyle: {
            opacity: 1,
            width: 2,
            color: "#8ABFFC",
          },
        };
        this.xkmlflSet[item].map((m) => {
          xkobj.name = m.xkjc;
          this.xkArr.push(xkobj);
          linkXkobj.target = m.xkjc;
          this.linksArr.push(linkXkobj);
        });
      }
      console.log(this.linksArr, "this.linksArr");
      // this.resourceData = [];
      // this.linksArr = [];
      // this.data?.forEach((item) => {
      //   this.resourceData.push(this.setData(item));
      // });
      // const xkArr = this.data?.filter((item) => !item.isXY);
      // const xyName = this.data?.filter((item) => item.isXY)[0].label;
      // if (this.linksData?.length) {
      //   this.linksArr = this.linksData.map((m) => {
      //     return {
      //       ...m,
      //       lineStyle: m.lineStyle,
      //     };
      //   });
      // } else {
      //   xkArr.forEach((item) => {
      //     this.linksArr.push({
      //       target: item.label,
      //       source: xyName,
      //       category: "",
      //       lineStyle: item.lineStyle,
      //     });
      //   });
      // }
    },
    getRepulsion() {
      let num = 100;
      switch (this.data.length) {
        case 2:
          num = 60;
          break;
        case 3:
          num = 80;
          break;
        case 4:
          num = 90;
          break;
        case 13:
          num = 180;
          break;
        default:
          num = 80;
      }
      return num;
    },
    getHeight() {
      let height = 114;
      switch (this.data.length) {
        case 2:
          height = 114;
          break;
        case 3:
          height = 147;
          break;
        case 4:
          height = 161;
          break;
        case 13:
          height = 373;
          break;
        default:
          height = 181;
      }
      return height;
    },
    getWidth() {
      let width = 140;
      switch (this.data.length) {
        case 2:
          width = 140;
          break;
        case 3:
          width = 178;
          break;
        case 4:
          width = 206;
          break;
        case 13:
          width = 610;
          break;
        default:
          width = 230;
      }
      return width;
    },
    handleChoose(params) {
      console.log("跳转", params.data.name, params.data.category);
      if (params.data.category == "学院") return;
      const url =
        window.location.origin +
        window.$GLOBAL_CONFIG.apiPath +
        `app/doublefirstclassdata#/doublefirstclassdata/xkhx/xkgl?xkmc=${params.data.name}`;
      window.open(url, "_blank");
    },
    // eslint-disable-next-line no-unused-vars
    setData(item) {
      if (item.isXY) {
        return {
          name: item.label,
          draggable: true, // 节点是否可拖拽，只在使用力引导布局的时候有用。
          symbolSize: [14, 14], // 关系图节点标记的大小，可以设置成诸如 10 这样单一的数字，也可以用数组分开表示宽和高，例如 [20, 10] 表示标记宽为20，高为10。
          itemStyle: {
            color: "#82a7ca", // 关系图节点标记的颜色
          },
          label: {
            show: true,
            position: "bottom", // 设置节点1的标签在节点上方显示
            backgroundColor: "#ffffff", // 设置节点1的标签背景色为红色半透明
            borderRadius: 3, // 设置节点1的标签圆角为5px
            padding: 2,
            color: "#000000",
            fontSize: 9,
            lineHeight: 12, // 设置节点1的标签行高为30
          },
          category: "学院", // 数据项所在类目的 index。
        };
      } else {
        return {
          name: item.label,
          draggable: true, // 节点是否可拖拽，只在使用力引导布局的时候有用。
          symbolSize: [50, 50], // 关系图节点标记的大小，可以设置成诸如 10 这样单一的数字，也可以用数组分开表示宽和高，例如 [20, 10] 表示标记宽为20，高为10。
          itemStyle: {
            color: item.color, // 关系图节点标记的颜色
            borderWidth: 3, // 设置节点1的标签边框宽度为3px
            borderColor: item.borderColor || "#fff", // 设置节点1的标签边框颜色为白色
          },
          label: {
            position: "inside",
            formatter: `{a|${this.formatter(item.label)}}`,
            rich: {
              a: {
                fontSize: 9,
                color: item.labelColor || "#fff",
                fontWeight: 600,
                lineHeight: 12,
                align: "center",
                // verticalAlign: 'bottom',
              },
              dot: {
                color: "red",
                width: 7,
                height: 7,
                borderRadius: 7,
                borderWidth: 3,
                borderColor: "#fff",
                backgroundColor: "#f00",
                align: "right",
                verticalAlign: "top",
              },
            },
          },
          category: "学科", // 数据项所在类目的 index。
        };
      }
    },
    formatter(val) {
      let pieces = [];
      for (var i = 0; i < val.length; i += 4) {
        pieces.push(val.slice(i, i + 4));
      }
      return pieces.join("\n");
    },
  },
};
</script>

<style lang="less" scoped></style>
