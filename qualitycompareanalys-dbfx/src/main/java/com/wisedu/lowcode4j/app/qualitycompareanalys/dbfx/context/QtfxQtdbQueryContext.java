package com.wisedu.lowcode4j.app.qualitycompareanalys.dbfx.context;

import cn.hutool.core.map.MapUtil;
import com.wisedu.lowcode4j.app.qualitycompareanalys.dbfx.bo.JwqcaQtdbBo;
import com.wisedu.lowcode4j.common.core.context.BaseContext;
import com.wisedu.lowcode4j.common.core.model.QueryModel;
import com.wisedu.lowcode4j.main.qcapub.constant.QualityAnalysisConstant.*;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 群体分析群体对比
 * <AUTHOR>
 * @date 2025/5/15
 */
@Data
public class QtfxQtdbQueryContext extends BaseContext {

    /**
     * 查询参数
     */
    private QueryModel queryModel;

    /**
     * 优势群体
     */
    private List<JwqcaQtdbBo> ysqt;

    /**
     * 优势群体人数
     */
    private Integer ysqtrs;

    /**
     * 短板群体
     */
    private List<JwqcaQtdbBo> dbqt;

    /**
     * 优势群体人数
     */
    private Integer dbqtrs;

    /**
     * 其他群体
     */
    private List<JwqcaQtdbBo> qtqt;

    /**
     * 优势群体人数
     */
    private Integer qtqtrs;

    @SuppressWarnings("unchecked")
    public void buildData(Map<String, List<JwqcaQtdbBo>> map) {
        List<JwqcaQtdbBo> ysqt = MapUtil.get(map, QTDB.YSQT, List.class);
        List<JwqcaQtdbBo> dbqt = MapUtil.get(map, QTDB.DBQT, List.class);
        List<JwqcaQtdbBo> qtqt = MapUtil.get(map, QTDB.QTQT, List.class);
        this.setYsqtrs(ysqt.stream().mapToInt(JwqcaQtdbBo::getStudentListSize).sum());
        this.setDbqtrs(dbqt.stream().mapToInt(JwqcaQtdbBo::getStudentListSize).sum());
        this.setQtqtrs(qtqt.stream().mapToInt(JwqcaQtdbBo::getStudentListSize).sum());
        this.setYsqt(ysqt);
        this.setDbqt(dbqt);
        this.setQtqt(qtqt);
    }
}
