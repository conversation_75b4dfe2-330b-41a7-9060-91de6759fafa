package com.wisedu.lowcode4j.app.qualitycompareanalys.dbfx.bo;

import com.wisedu.lowcode4j.common.core.annotation.ColumnDefine;
import com.wisedu.lowcode4j.common.core.annotation.ModelDefine;
import com.wisedu.lowcode4j.common.core.model.BaseBizModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Comment;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */


@ApiModel(value = "qtbo", description = "群体")
@Comment("群体")
@ModelDefine(renderType="form")
//end_dynamic_declare
@Data
public class QtBo extends BaseBizModel {

    private static final long serialVersionUID = 4431474721041958393L;

    //region start_dynamic_column
    @ApiModelProperty(name = "name", value = "名称" ,notes = "2025-05-13 16:22:55")
    @Comment("名称")
    @Column(value = "name", width = 128)
    @ColumnDefine(orderIndex=1,columnXtype="text")
    private String name;

    @ApiModelProperty(name = "xsrs", value = "学生人数" ,notes = "2025-05-13 16:23:51")
    @Comment("学生人数")
    @Column(value = "xsrs", width = 128)
    @ColumnDefine(orderIndex=2,columnXtype="text")
    private String xsrs;

    @ApiModelProperty(name = "fgzysl", value = "覆盖专业数量" ,notes = "2025-05-13 16:24:27")
    @Comment("覆盖专业数量")
    @Column(value = "fgzysl", width = 128)
    @ColumnDefine(orderIndex=3,columnXtype="text")
    private String fgzysl;

    @ApiModelProperty(name = "id", value = "id" ,notes = "2025-05-15 14:42:10")
    @Comment("id")
    @Column(value = "id", width = 128)
    @ColumnDefine(columnXtype="text")
    private String id;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades

    //region start_dynamic_dict_column
	//endregion end_dynamic_dict_column
}
