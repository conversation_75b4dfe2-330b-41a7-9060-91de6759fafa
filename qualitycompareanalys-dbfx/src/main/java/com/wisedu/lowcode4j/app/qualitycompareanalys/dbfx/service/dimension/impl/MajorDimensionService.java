package com.wisedu.lowcode4j.app.qualitycompareanalys.dbfx.service.dimension.impl;

import cn.hutool.core.util.StrUtil;
import com.wisedu.lowcode4j.app.qualitycompareanalys.dbfx.service.dimension.StuDimensionService;
import com.wisedu.lowcode4j.main.qcapub.bo.JwqcaModelConfigBo;
import com.wisedu.lowcode4j.main.qcapub.constant.QualityAnalysisDimension;
import com.wisedu.lowcode4j.main.qcapub.enums.QualityAnalysisGroupType;
import com.wisedu.lowcode4j.main.qcapub.vo.JwqcaQtpzVo;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;

/**
 * @ClassName: MajorDimensionService
 * @Author: leiyy
 * @Date: 2025/5/14 11:14
 * @Description: 专业维度服务
 */
@Component
public class MajorDimensionService implements StuDimensionService {

    @Override
    public String buildStuFxqtQueryCondition(JwqcaQtpzVo config) {
        String condition = QUERY_MAJOR_COLUMN_NAME + " = '" + config.getFxqtZydm() + "' ";
        // 学院不空，拼接学院条件
        if (StrUtil.isNotEmpty(config.getFxqtXydm())) {
            condition += " and " + QUERY_COLLEGE_COLUMN_NAME + " = '" + config.getFxqtXydm() + "' ";
        }
        return condition;
    }

    @Override
    public String buildStuDbqtQueryCondition(JwqcaQtpzVo config) {
        String condition = QUERY_MAJOR_COLUMN_NAME + " = '" + config.getDbqtZydm() + "' ";
        // 学院不空，拼接学院条件
        if (StrUtil.isNotEmpty(config.getDbqtXydm())) {
            condition += " and " + QUERY_COLLEGE_COLUMN_NAME + " = '" + config.getDbqtXydm() + "' ";
        }
        return condition;
    }

    @Override
    public String buildStuQueryGroupColumn(JwqcaQtpzVo config) {
        return QUERY_MAJOR_COLUMN_NAME;
    }

    @Override
    public String buildGroupSelectColumns(JwqcaQtpzVo config) {
        return QUERY_MAJOR_COLUMN_NAME + " AS ZY ";
    }

    @Override
    public String buildStuOverviewCondition(JwqcaQtpzVo config, QualityAnalysisGroupType groupType) {
        return "1=1";
    }

    @Override
    public String dimension() {
        return QualityAnalysisDimension.MAJOR;
    }

    @Override
    public String buildStuQtdblbName(JwqcaQtpzVo config, String zymc) {
        return config.getFxqtZymc();
    }

}
