package com.wisedu.lowcode4j.app.qualitycompareanalys.dbfx.service.dimension.impl;

import com.wisedu.lowcode4j.app.qualitycompareanalys.dbfx.service.dimension.StuDimensionService;
import com.wisedu.lowcode4j.main.qcapub.constant.QualityAnalysisDimension;
import com.wisedu.lowcode4j.main.qcapub.enums.QualityAnalysisGroupType;
import com.wisedu.lowcode4j.main.qcapub.vo.JwqcaQtpzVo;
import org.springframework.stereotype.Component;

/**
 * @ClassName: CollegeDimensionService
 * @Author: leiyy
 * @Date: 2025/5/14 11:13
 * @Description: 学院维度服务
 */
@Component
public class CollegeDimensionService implements StuDimensionService {
    /**
     * 构建学生对比-分析群体查询模型
     * @param config 学生群体对比配置
     * @return 查询模型
     */
    @Override
    public String buildStuFxqtQueryCondition(JwqcaQtpzVo config) {
        return QUERY_COLLEGE_COLUMN_NAME + " = '" + config.getFxqtXydm() + "' ";
    }

    @Override
    public String buildStuDbqtQueryCondition(JwqcaQtpzVo config) {
        return QUERY_COLLEGE_COLUMN_NAME + " = '" + config.getDbqtXydm() + "' ";
    }

    @Override
    public String buildStuQueryGroupColumn(JwqcaQtpzVo config) {
        return QUERY_COLLEGE_COLUMN_NAME;
    }

    @Override
    public String buildGroupSelectColumns(JwqcaQtpzVo config) {
        return QUERY_COLLEGE_COLUMN_NAME;
    }

    @Override
    public String buildStuOverviewCondition(JwqcaQtpzVo config, QualityAnalysisGroupType groupType) {
        return "1=1";
    }

    @Override
    public String dimension() {
        return QualityAnalysisDimension.COLLEGE;
    }

    @Override
    public String buildStuQtdblbName(JwqcaQtpzVo config, String zymc) {
        return config.getFxqtXymc() + "-" + zymc;
    }
}
