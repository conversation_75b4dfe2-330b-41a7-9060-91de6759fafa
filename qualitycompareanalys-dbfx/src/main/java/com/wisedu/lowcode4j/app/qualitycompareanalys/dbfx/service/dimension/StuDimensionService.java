package com.wisedu.lowcode4j.app.qualitycompareanalys.dbfx.service.dimension;

import com.wisedu.lowcode4j.main.qcapub.bo.JwqcaModelConfigBo;
import com.wisedu.lowcode4j.main.qcapub.bo.type.QualityAnalysisRankData;
import com.wisedu.lowcode4j.main.qcapub.enums.QualityAnalysisGroupType;
import com.wisedu.lowcode4j.main.qcapub.exception.QualityAnalysisBizException;
import com.wisedu.lowcode4j.main.qcapub.vo.JwqcaQtpzVo;

import static com.wisedu.lowcode4j.app.qualitycompareanalys.constant.QualityStuAnalysisConstant.BASE_TABLE_INFO.*;

/**
 * @InterfaceName: StuDimensionService
 * @Author: leiyy
 * @Date: 2025/5/14 11:07
 * @Description: 对比维度服务
 */
public interface StuDimensionService {
    String QUERY_COLLEGE_COLUMN_NAME = COLLEGE_COLUMN_NAME;
    String QUERY_MAJOR_COLUMN_NAME = MAJOR_COLUMN_NAME;
    String QUERY_GRADE_COLUMN_NAME = GRADE_COLUMN_NAME;

    /**
     * 构建学生对比-分析群体查询模型
     * @param config 学生群体对比配置
     * @return 查询条件
     */
    String buildStuFxqtQueryCondition(JwqcaQtpzVo config);

    /**
     * 构建学生对比-对标群体查询模型
     * @param config 学生群体对比配置
     * @return 查询条件
     */
    String buildStuDbqtQueryCondition(JwqcaQtpzVo config);

    /**
     * 构建查询分组字段 逗号拼接
     * @param config 学生群体对比配置
     * @return 查询分组字段
     */
    String buildStuQueryGroupColumn(JwqcaQtpzVo config);

    /**
     * 获取对比分析-查询分组列字段， 基于QualityAnalysisRankData统一别名
     * @param config 配置信息
     * @return String 查询分组列字段
     */
    String buildGroupSelectColumns(JwqcaQtpzVo config);

    /**
     * 构建学生群体概览查询条件
     * @param config 学生群体对比配置
     * @param groupType 质量分析群体类型
     * @return 查询条件
     */
    String buildStuOverviewCondition(JwqcaQtpzVo config, QualityAnalysisGroupType groupType);

    /**
     * 获取对比维度
     * @return 对比维度值
     */
    String dimension();

    /**
     * 构建学生群体对比列表名称
     * @param config 学生群体对比配置
     * @param zymc 专业名称
     * @return 学生群体对比列表名称
     */
    String buildStuQtdblbName(JwqcaQtpzVo config, String zymc);
}
