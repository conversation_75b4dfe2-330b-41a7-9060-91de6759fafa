package com.wisedu.lowcode4j.app.qualitycompareanalys.dbfx.service.impl;

import com.wisedu.lowcode4j.app.qualitycompareanalys.constant.QualityStuAnalysisConstant;import com.wisedu.lowcode4j.app.qualitycompareanalys.dbfx.service.dimension.StuDimensionManager;
import com.wisedu.lowcode4j.app.qualitycompareanalys.dbfx.service.dimension.StuDimensionService;
import com.wisedu.lowcode4j.common.core.model.QueryModel;
import com.wisedu.lowcode4j.common.db.BaseService;
import com.wisedu.lowcode4j.main.qcapub.bo.JwqcaGroupBo;
import com.wisedu.lowcode4j.main.qcapub.enums.QualityAnalysisErrorCodeEnum;
import com.wisedu.lowcode4j.main.qcapub.enums.QualityAnalysisGroupType;
import com.wisedu.lowcode4j.main.qcapub.exception.QualityAnalysisBizException;
import com.wisedu.lowcode4j.main.qcapub.service.impl.extend.QualityAnalysisConfigExtendService;
import com.wisedu.lowcode4j.main.qcapub.vo.JwqcaQtpzVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.wisedu.lowcode4j.app.qualitycompareanalys.constant.QualityStuAnalysisConstant.BASE_TABLE_INFO;
import static com.wisedu.lowcode4j.main.qcapub.service.impl.extend.QualityAnalysisConfigExtendService.BEAN_NAME;

/**
 * @ClassName: QualityAnalysisStuExtendServiceImpl
 * @Author: leiyy
 * @Date: 2025/6/3 16:02
 * @Description:
 */
@Service(BEAN_NAME)
@Slf4j
public class JwqcaStuAnalysisExtendServiceImpl implements QualityAnalysisConfigExtendService {
    @Autowired
    private StuDimensionManager stuDimensionManager;

    @Autowired
    private BaseService baseService;

    @Override
    public String buildQtQueryCondition(JwqcaQtpzVo jwqcaQtpz, QualityAnalysisGroupType groupType) {
        StuDimensionService dimensionService = getDimensionService(jwqcaQtpz);
        if (QualityAnalysisGroupType.ANALYSIS_GROUP.equals(groupType)) {
            return dimensionService.buildStuFxqtQueryCondition(jwqcaQtpz);
        }
        return dimensionService.buildStuDbqtQueryCondition(jwqcaQtpz);
    }

    @Override
    public String buildQueryGroupColumn(JwqcaQtpzVo jwqcaQtpz) {
        return getDimensionService(jwqcaQtpz).buildStuQueryGroupColumn(jwqcaQtpz);
    }

    @Override
    public String buildGroupSelectColumns(JwqcaQtpzVo jwqcaQtpz) {
        return getDimensionService(jwqcaQtpz).buildGroupSelectColumns(jwqcaQtpz);
    }

    @Override
    public String buildRankCondition(JwqcaQtpzVo jwqcaQtpz, QualityAnalysisGroupType groupType) {
        return getDimensionService(jwqcaQtpz).buildStuOverviewCondition(jwqcaQtpz, groupType);
    }

    @Override
    public String buildBaseTableName() {
        return BASE_TABLE_INFO.BASE_TABLE_NAME;
    }

    @Override
    public String buildBaseTableTimeColumn() {
        return BASE_TABLE_INFO.BASE_TABLE_TIME_COLUMN_NAME;
    }

    @Override
    public String buildBaseTableEntityColumn() {
        return BASE_TABLE_INFO.BASE_TABLE_ENTITY_COLUMN_NAME;
    }

    @Override
    public String buildBaseTableCollegeColumnName() {
        return BASE_TABLE_INFO.COLLEGE_COLUMN_NAME;
    }

    @Override
    public String buildBaseTableMajorColumnName() {
        return BASE_TABLE_INFO.MAJOR_COLUMN_NAME;
    }

    @Override
    public String buildBaseTableGradeColumnName() {
        return BASE_TABLE_INFO.GRADE_COLUMN_NAME;
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<JwqcaGroupBo> queryGroupInfo(String sqlId, QueryModel queryModel) {
        return baseService.findBySql(sqlId, queryModel, JwqcaGroupBo.class);
    }

    @Override
    public String buildGroupComparisonName(JwqcaQtpzVo config, String itemSubName) {
        return getDimensionService(config).buildStuQtdblbName(config, itemSubName);
    }

    @Override
    public String getGroupItemModelSqlTemplate() {
        return QualityStuAnalysisConstant.SQL_TEMPLATE.STU_GROUP_DB_SQL_TEMPLATE;
    }

    /**
     * 获取学生维度服务
     * @param jwqcaQtpz 群体配置
     * @return 学生维度服务
     */
    protected StuDimensionService getDimensionService(JwqcaQtpzVo jwqcaQtpz) {
        if (jwqcaQtpz == null) {
            throw new QualityAnalysisBizException(QualityAnalysisErrorCodeEnum.FXQT_NULL);
        }
        StuDimensionService dimensionService = stuDimensionManager.getDimension(jwqcaQtpz.getFxwd());
        if (dimensionService == null) {
            log.warn("Unsupported analysis dimension: {}", jwqcaQtpz.getFxwd());
            throw new QualityAnalysisBizException(QualityAnalysisErrorCodeEnum.FXWD_NOT_EXIST);
        }
        return dimensionService;
    }
}
