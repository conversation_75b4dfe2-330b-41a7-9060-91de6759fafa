package com.wisedu.lowcode4j.app.qualitycompareanalys.dbfx.bo;

import cn.hutool.core.collection.CollectionUtil;
import lombok.Data;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 群体对比Bo
 * <AUTHOR>
 * @date 2025/5/16
 */
@Data
public class JwqcaQtdbBo {

    /**
     * 群组列表名称
     */
    private String groupListName;

    /**
     * 校内专业大类代码
     */
    private String xnzydldm;

    /**
     * 学生学号列表
     */
    private List<String> studentList;

    /**
     * 学生列表大小
     */
    private Integer studentListSize;

    public static List<JwqcaQtdbBo> buildJwqcaQtdbBoList(List<JwqcaQtdbBo.StudentBo> studentBoList) {
        if (CollectionUtil.isEmpty(studentBoList)) {
            return Collections.emptyList();
        }
        List<JwqcaQtdbBo> qtdbBoList = new ArrayList<>();
        Map<String, List<StudentBo>> groupListStudents = studentBoList.stream().collect(Collectors.groupingBy(JwqcaQtdbBo.StudentBo::getGroupListName));
        for (Map.Entry<String, List<StudentBo>> entry : groupListStudents.entrySet()) {
            String groupListName = entry.getKey();
            List<StudentBo> students = entry.getValue();
            List<String> xhs = students.stream().map(JwqcaQtdbBo.StudentBo::getXh).collect(Collectors.toList());
            JwqcaQtdbBo qtdbBo = new JwqcaQtdbBo();
            qtdbBo.setGroupListName(groupListName);
            qtdbBo.setStudentList(xhs);
            qtdbBo.setStudentListSize(xhs.size());
            qtdbBo.setXnzydldm(students.get(0).getXnzydldm());
            qtdbBoList.add(qtdbBo);
        }
        return qtdbBoList.stream().sorted(Comparator.comparing(JwqcaQtdbBo::getXnzydldm)).collect(Collectors.toList());
    }

    @Data
    public static class StudentBo {

        /**
         * 群组列表名称
         */
        private String groupListName;

        /**
         * 校内专业大类代码
         */
        private String xnzydldm;

        /**
         * 学生学号
         */
        private String xh;
    }
}
