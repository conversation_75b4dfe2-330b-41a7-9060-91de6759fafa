package com.wisedu.lowcode4j.app.qualitycompareanalys.dbfx.service.dimension.impl;

import cn.hutool.core.util.StrUtil;
import com.wisedu.lowcode4j.app.qualitycompareanalys.dbfx.service.dimension.StuDimensionService;
import com.wisedu.lowcode4j.main.qcapub.bo.JwqcaModelConfigBo;
import com.wisedu.lowcode4j.main.qcapub.constant.QualityAnalysisDimension;
import com.wisedu.lowcode4j.main.qcapub.enums.QualityAnalysisGroupType;
import com.wisedu.lowcode4j.main.qcapub.vo.JwqcaQtpzVo;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.Objects;

/**
 * @className : GradeDimensionService
 * @Author: leiyy
 * @Date: 2025/5/14 11:07
 * @Description: 年级维度服务
 */
@Component
public class GradeDimensionService implements StuDimensionService {

    @Override
    public String buildStuFxqtQueryCondition(JwqcaQtpzVo config) {
        String condition = QUERY_GRADE_COLUMN_NAME + " = '" + config.getFxqtNjdm() + "' ";
        // 学院不空，拼接学院条件
        if (StrUtil.isNotEmpty(config.getFxqtXydm())) {
            condition += " and " + QUERY_COLLEGE_COLUMN_NAME + "= '" + config.getFxqtXydm() + "' ";
        }
        // 专业不空，拼接专业条件
        if (StrUtil.isNotEmpty(config.getFxqtZydm())) {
            condition += " and " + QUERY_MAJOR_COLUMN_NAME + "= '" + config.getFxqtZydm() + "' ";
        }
        return condition;
    }

    @Override
    public String buildStuDbqtQueryCondition(JwqcaQtpzVo config) {
        String condition = QUERY_GRADE_COLUMN_NAME + " = '" + config.getDbqtNjdm() + "' ";
        // 学院不空，拼接学院条件
        if (StrUtil.isNotEmpty(config.getDbqtXydm())) {
            condition += " and " + QUERY_COLLEGE_COLUMN_NAME + "= '" + config.getDbqtXydm() + "' ";
        }
        // 专业不空，拼接专业条件
        if (StrUtil.isNotEmpty(config.getDbqtZydm())) {
            condition += " and " + QUERY_MAJOR_COLUMN_NAME + "= '" + config.getDbqtZydm() + "' ";
        }
        return condition;
    }

    @Override
    public String buildStuQueryGroupColumn(JwqcaQtpzVo config) {
        if (StrUtil.isNotEmpty(config.getFxqtZydm())) {
            return QUERY_MAJOR_COLUMN_NAME;
        }
        if (StrUtil.isNotEmpty(config.getFxqtXydm())) {
            return QUERY_COLLEGE_COLUMN_NAME;
        }
        return QUERY_GRADE_COLUMN_NAME;
    }

    @Override
    public String buildGroupSelectColumns(JwqcaQtpzVo config) {
        if (StrUtil.isNotEmpty(config.getFxqtZydm())) {
            return QUERY_MAJOR_COLUMN_NAME + " AS ZY ";
        }
        if (StrUtil.isNotEmpty(config.getFxqtXydm())) {
            return QUERY_COLLEGE_COLUMN_NAME;
        }
        return QUERY_GRADE_COLUMN_NAME;
    }

    @Override
    public String buildStuOverviewCondition(JwqcaQtpzVo config, QualityAnalysisGroupType groupType) {
        if (QualityAnalysisGroupType.ANALYSIS_GROUP.equals(groupType)) {
            return buildStuFxqtOverviewCondition(config);
        }
        return buildStuDbqtOverviewCondition(config);
    }

    @Override
    public String dimension() {
        return QualityAnalysisDimension.GRADE;
    }

    @Override
    public String buildStuQtdblbName(JwqcaQtpzVo config, String zymc) {
        return config.getFxqtNjmc() + "-" + zymc;
    }

    protected String buildStuFxqtOverviewCondition(JwqcaQtpzVo config) {
        String condition = QUERY_GRADE_COLUMN_NAME + " = '" + config.getFxqtNjdm() + "' ";
        // 专业不空
        if (StrUtil.isNotEmpty(config.getFxqtZymc())) {
            return condition;
        }
        // 专业为空，学院不空，拼接学院条件
        if (StrUtil.isNotEmpty(config.getFxqtXymc())) {
            return condition;
        }
        return "1=1";
    }

    protected String buildStuDbqtOverviewCondition(JwqcaQtpzVo config) {
        String condition = QUERY_GRADE_COLUMN_NAME + " = '" + config.getDbqtNjdm() + "' ";
        // 专业不空
        if (StrUtil.isNotEmpty(config.getDbqtZymc())) {
            return condition;
        }
        // 专业为空，学院不空，拼接学院条件
        if (StrUtil.isNotEmpty(config.getDbqtXymc())) {
            return condition;
        }
        return "1=1";
    }
}
