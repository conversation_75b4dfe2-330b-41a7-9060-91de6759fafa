package com.wisedu.lowcode4j.app.qualitycompareanalys.dbfx.context;

import com.wisedu.lowcode4j.app.qualitycompareanalys.dbfx.bo.QtBo;
import com.wisedu.lowcode4j.common.core.context.BaseContext;
import com.wisedu.lowcode4j.common.core.model.QueryModel;
import lombok.Data;

/**
 * 群体查询上下文
 *
 * <AUTHOR>
 * @date 2025/5/13
 */
@Data
public class QtQueryContext extends BaseContext {

    /**
     * 查询参数
     */
    private QueryModel queryModel;

    /**
     * 分析群体
     */
    private QtBo fxqt;

    /**
     * 对标群体
     */
    private QtBo dbqt;

}
