package com.wisedu.lowcode4j.app.qualitycompareanalys.dbfx.service;

import com.wisedu.lowcode4j.app.qualitycompareanalys.bo.Bksxxlb;
import com.wisedu.lowcode4j.common.core.model.QueryModel;
import com.wisedu.lowcode4j.main.qcapub.enums.QualityAnalysisModelClassify;
import org.sagacity.sqltoy.model.Page;

import java.util.List;
import java.util.Map;

/**
 * @InterfaceName: JwqcaStuAnalysisService
 * @Author: leiyy
 * @Date: 2025/5/13 15:31
 * @Description: 群体对比分析学生服务接口
 */
public interface JwqcaStuAnalysisService {

    /**
     * 查询学生分析-指标模型数据
     * @param modelId 模型ID
     * @param statisticYear 统计年份
     * @return Page 模型数据集合
     */
    Page<Map<String, Object>> queryStuModelData(QueryModel queryModel, String modelId, String statisticYear);

    /**
     * 查询群体分析学生列表
     * @param modelClassify 模型分类
     * @param statisticYear 统计年份
     * @param queryModel 查询参数
     * @return 学生列表
     */
    List<Bksxxlb> queryQtfxXslb(QualityAnalysisModelClassify modelClassify, String statisticYear, QueryModel queryModel);
}
