package com.wisedu.lowcode4j.app.qualitycompareanalys.dbfx.service.dimension;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: StuDimensionManager
 * @Author: leiyy
 * @Date: 2025/5/14 11:15
 * @Description: 学生对比-维度管理器
 */
@Service
public class StuDimensionManager {

    private final Map<String, StuDimensionService> stuDimensionService = new HashMap<>();

    @Autowired
    public StuDimensionManager(List<StuDimensionService> stuDimensionServiceList) {
        for (StuDimensionService dimensionService : stuDimensionServiceList) {
            stuDimensionService.put(dimensionService.dimension(), dimensionService);
        }
    }

    public StuDimensionService getDimension(String dimension) {
        return stuDimensionService.get(dimension);
    }
}
