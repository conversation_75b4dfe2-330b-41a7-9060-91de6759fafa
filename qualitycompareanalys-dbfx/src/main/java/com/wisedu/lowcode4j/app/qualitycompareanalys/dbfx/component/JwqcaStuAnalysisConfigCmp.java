package com.wisedu.lowcode4j.app.qualitycompareanalys.dbfx.component;

import cn.hutool.core.bean.BeanUtil;
import com.gitee.starblues.bootstrap.annotation.AutowiredType;
import com.wisedu.lowcode4j.app.db.core.MyTranscational;
import com.wisedu.lowcode4j.app.qualitycompareanalys.dbfx.context.JwqcaStuAnalysisConfigContext;
import com.wisedu.lowcode4j.common.core.context.QueryContext;
import com.wisedu.lowcode4j.main.qcapub.service.QualityAnalysisConfigService;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.annotation.LiteflowMethod;
import com.yomahub.liteflow.core.NodeComponent;
import com.yomahub.liteflow.enums.LiteFlowMethodEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

import static com.wisedu.lowcode4j.app.qualitycompareanalys.constant.QualityStuAnalysisConstant.MODEL_CLASSIFY;

/**
 * @ClassName: JwqcaStuAnalysisConfigCmp
 * @Author: leiyy
 * @Date: 2025/5/13 10:23
 * @Description: 群体对比分析配置组件
 */
@Slf4j
@RequiredArgsConstructor
@LiteflowComponent(name = "群体对比分析配置组件")
public class JwqcaStuAnalysisConfigCmp {
    @Autowired
    @AutowiredType(AutowiredType.Type.MAIN)
    private QualityAnalysisConfigService qualityAnalysisConfigService;

    @LiteflowMethod(
            value = LiteFlowMethodEnum.PROCESS,
            name = "查询群体对比分析本科生指标下拉",
            nodeId = "qualitycompareanalys_student_zb_drop",
            context = {QueryContext.class})
    public void getStudentZbDrop(NodeComponent bindCmp) {
        QueryContext queryContext = bindCmp.getContextBean(QueryContext.class);
        queryContext.setList(qualityAnalysisConfigService.getModelDrop(MODEL_CLASSIFY));
    }

    @LiteflowMethod(
            value = LiteFlowMethodEnum.PROCESS,
            name = "群体对比分析-学生对比分析详情",
            nodeId = "qualitycompareanalys_student_config_detail",
            context = {JwqcaStuAnalysisConfigContext.class})
    public void getStudentConfigDetail(NodeComponent bindCmp) {
        JwqcaStuAnalysisConfigContext queryContext = bindCmp.getContextBean(JwqcaStuAnalysisConfigContext.class);
        queryContext.setJwqcaXsQtpzVo(qualityAnalysisConfigService.getJwcaQtpz(MODEL_CLASSIFY));
        queryContext.setJwqcaXsZbwdVoList(qualityAnalysisConfigService.getJwqcaZbwdList(MODEL_CLASSIFY));
    }

    @LiteflowMethod(
            value = LiteFlowMethodEnum.PROCESS,
            name = "群体对比分析-学生对比分析上下文初始化",
            nodeId = "qualitycompareanalys_student_config_init",
            context = {JwqcaStuAnalysisConfigContext.class})
    public void initStudentConfig(NodeComponent bindCmp) {
        JwqcaStuAnalysisConfigContext stuAnalysisConfigContext = bindCmp.getContextBean(JwqcaStuAnalysisConfigContext.class);
        Map<String, Object> params = bindCmp.getRequestData();
        JwqcaStuAnalysisConfigContext configContext = BeanUtil.copyProperties(params, JwqcaStuAnalysisConfigContext.class);
        stuAnalysisConfigContext.setJwqcaXsQtpzVo(configContext.getJwqcaXsQtpzVo());
        stuAnalysisConfigContext.setJwqcaXsZbwdVoList(configContext.getJwqcaXsZbwdVoList());
    }

    @LiteflowMethod(
            value = LiteFlowMethodEnum.PROCESS,
            name = "群体对比分析-学生对比分析保存校验",
            nodeId = "qualitycompareanalys_student_config_validate",
            context = {JwqcaStuAnalysisConfigContext.class})
    public void saveStudentConfigValidate(NodeComponent bindCmp) {
        // 获取上下文
        JwqcaStuAnalysisConfigContext stuAnalysisConfigContext = bindCmp.getContextBean(JwqcaStuAnalysisConfigContext.class);
        stuAnalysisConfigContext.setModelClassify(MODEL_CLASSIFY);
        qualityAnalysisConfigService.saveModelConfigValidate(stuAnalysisConfigContext);
    }

    @MyTranscational
    @LiteflowMethod(
            value = LiteFlowMethodEnum.PROCESS,
            name = "群体对比分析-学生对比分析保存",
            nodeId = "qualitycompareanalys_student_config_save",
            context = {JwqcaStuAnalysisConfigContext.class})
    public void saveStudentConfig(NodeComponent bindCmp) {
        // 获取上下文
        JwqcaStuAnalysisConfigContext stuAnalysisConfigContext = bindCmp.getContextBean(JwqcaStuAnalysisConfigContext.class);
        stuAnalysisConfigContext.setModelClassify(MODEL_CLASSIFY);
        qualityAnalysisConfigService.saveModelConfig(stuAnalysisConfigContext);
    }
}
