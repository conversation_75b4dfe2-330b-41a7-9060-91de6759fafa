<?xml version="1.0" encoding="UTF-8"?>
<flow>
    <chain name="qualitycompareanalys_student_qt_query" label="查询分析群体和对标群体" version="1.0.0" moduleCode="dbfx" moduleGroupCode="" permitExtFlag="1" apiEncrypt="0" sysFlag="0" apiTimeout="0" configPageCode="">
         THEN( qualitycompareanalys_fxqtanddbqt_query )
        <elGraph>
            <![CDATA[
                {"newNodes":[{"id":"init_bwijauner8o0000","type":"start-node","x":400,"y":130,"properties":{"componentId":"page_init","componentName":"pageInit","name":"开始","logo":"api-kaishi","status":"normal"}},{"id":"e1ea8c00-adfa-4b9e-87ea-8acb62de873f","type":"common-node","x":600,"y":130,"properties":{"type":"nodeCustom","componentName":"common-node","name":"查询分析群体和对标群体","logo":"api-jiedian","status":"selected","modelId":"qualitycompareanalys_fxqtanddbqt_query","extendFlag":0,"contexts":{"com.wisedu.lowcode4j.main.qcapub.context.JwqcaGroupContext":"JwqcaGroupContext"},"componentType":"0","isReplace":false},"text":{"x":600,"y":130,"value":"查询分析群体和对标群体"}}],"newEdges":[{"id":"65c58d65-32c4-4551-a651-47d2c7760234","type":"logic-line","sourceNodeId":"init_bwijauner8o0000","targetNodeId":"e1ea8c00-adfa-4b9e-87ea-8acb62de873f","startPoint":{"x":450,"y":130},"endPoint":{"x":510,"y":130},"properties":{},"pointsList":[{"x":450,"y":130},{"x":510,"y":130}]}]}
            ]]>
        </elGraph>
        <firstContext>com.wisedu.lowcode4j.main.qcapub.context.JwqcaGroupContext</firstContext>
        <apiContext>com.wisedu.lowcode4j.main.qcapub.context.JwqcaGroupContext</apiContext>
    </chain>
</flow>
