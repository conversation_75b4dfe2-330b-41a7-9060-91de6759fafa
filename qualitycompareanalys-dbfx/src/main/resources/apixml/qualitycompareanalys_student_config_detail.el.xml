<?xml version="1.0" encoding="UTF-8"?>
<flow>
    <chain name="qualitycompareanalys_student_config_detail" label="群体对比分析-学生对比分析详情" version="1.0.0" moduleCode="dbfx" moduleGroupCode="" permitExtFlag="1" apiEncrypt="0" sysFlag="0" apiTimeout="0" configPageCode="">
         THEN( qualitycompareanalys_student_config_detail )
        <elGraph>
            <![CDATA[
                {"newNodes":[{"id":"init_9b6zmc3g0t80000","type":"start-node","x":400,"y":300,"properties":{"componentId":"page_init","componentName":"pageInit","name":"开始","logo":"api-kaishi","status":"normal"}},{"id":"logic_596ihun9bek0000","type":"common-node","x":610,"y":300,"properties":{"type":"nodeCustom","name":"群体对比分析-学生对比分析详情","componentName":"nodeCustom","logo":"api-jiedian","level":0,"status":"selected","modelId":"qualitycompareanalys_student_config_detail","extendFlag":0,"contexts":{"com.wisedu.lowcode4j.app.qualitycompareanalys.dbfx.context.JwqcaStuAnalysisConfigContext":"JwqcaStuAnalysisConfigContext"},"componentType":"0"},"text":{"x":610,"y":300,"value":"群体对比分析-学生对比分析详情"}}],"newEdges":[{"id":"logic_3yujcmaghzw0000","type":"logic-line","sourceNodeId":"init_9b6zmc3g0t80000","targetNodeId":"logic_596ihun9bek0000","startPoint":{"x":450,"y":300},"endPoint":{"x":516,"y":300},"properties":{},"pointsList":[{"x":450,"y":300},{"x":516,"y":300}]}]}
            ]]>
        </elGraph>
        <firstContext>com.wisedu.lowcode4j.app.qualitycompareanalys.dbfx.context.JwqcaStuAnalysisConfigContext</firstContext>
        <apiContext>com.wisedu.lowcode4j.app.qualitycompareanalys.dbfx.context.JwqcaStuAnalysisConfigContext</apiContext>
    </chain>
</flow>
