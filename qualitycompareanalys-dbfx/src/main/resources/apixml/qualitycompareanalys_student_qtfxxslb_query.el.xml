<?xml version="1.0" encoding="UTF-8"?>
<flow>
    <chain name="qualitycompareanalys_student_qtfxxslb_query" label="群体分析学生列表" version="1.0.0" moduleCode="dbfx" moduleGroupCode="" permitExtFlag="1" apiEncrypt="0" sysFlag="0" apiTimeout="0" configPageCode="">
         THEN( qualitycompareanalys_qtfxxslb_query )
        <elGraph>
            <![CDATA[
                {"newNodes":[{"id":"init_2g5rm1m1s6as000","type":"start-node","x":400,"y":130,"properties":{"componentId":"page_init","componentName":"pageInit","name":"开始","logo":"api-kaishi","status":"normal"}},{"id":"e1d92bc7-8d8c-428d-8edc-2d3140a44bdf","type":"common-node","x":600,"y":130,"properties":{"type":"nodeCustom","componentName":"common-node","name":"查询群体分析学生列表","logo":"api-jiedian","status":"normal","modelId":"qualitycompareanalys_qtfxxslb_query","extendFlag":0,"contexts":{"com.wisedu.lowcode4j.common.core.context.QueryContext":"通用查询上下文"},"componentType":"0"},"text":{"x":600,"y":130,"value":"查询群体分析学生列表"}}],"newEdges":[{"id":"11735e17-1179-4b8b-be33-ccd2419ffc25","type":"logic-line","sourceNodeId":"init_2g5rm1m1s6as000","targetNodeId":"e1d92bc7-8d8c-428d-8edc-2d3140a44bdf","startPoint":{"x":450,"y":130},"endPoint":{"x":510,"y":130},"properties":{},"pointsList":[{"x":450,"y":130},{"x":510,"y":130}]}]}
            ]]>
        </elGraph>
        <firstContext>com.wisedu.lowcode4j.common.core.context.QueryContext</firstContext>
        <apiContext>com.wisedu.lowcode4j.common.core.context.QueryContext</apiContext>
    </chain>
</flow>
