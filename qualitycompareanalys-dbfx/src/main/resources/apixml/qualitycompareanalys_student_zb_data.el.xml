<?xml version="1.0" encoding="UTF-8"?>
<flow>
    <chain name="qualitycompareanalys_student_zb_data" label="查询学生群体对比-指标数据" version="1.0.0" moduleCode="dbfx" moduleGroupCode="" permitExtFlag="1" apiEncrypt="0" sysFlag="0" apiTimeout="0" configPageCode="">
         THEN( qualitycompareanalys_student_zb_data )
        <elGraph>
            <![CDATA[
                {"newNodes":[{"id":"init_1crecwxsd874000","type":"start-node","x":400,"y":300,"properties":{"componentId":"page_init","componentName":"pageInit","name":"开始","logo":"api-kaishi","status":"normal"}},{"id":"logic_33f22xuoad40000","type":"common-node","x":610,"y":300,"properties":{"type":"nodeCustom","name":"查询学生群体对比-指标数据","componentName":"nodeCustom","logo":"api-jiedian","level":0,"status":"selected","modelId":"qualitycompareanalys_student_zb_data","extendFlag":0,"contexts":{"com.wisedu.lowcode4j.common.core.context.QueryContext":"通用查询上下文"},"componentType":"0","isReplace":false},"text":{"x":610,"y":300,"value":"查询学生群体对比-指标数据"}}],"newEdges":[{"id":"logic_133esqwkh768000","type":"logic-line","sourceNodeId":"init_1crecwxsd874000","targetNodeId":"logic_33f22xuoad40000","startPoint":{"x":450,"y":300},"endPoint":{"x":516,"y":300},"properties":{},"pointsList":[{"x":450,"y":300},{"x":516,"y":300}]}]}
            ]]>
        </elGraph>
        <firstContext>com.wisedu.lowcode4j.common.core.context.QueryContext</firstContext>
        <apiContext>com.wisedu.lowcode4j.common.core.context.QueryContext</apiContext>
    </chain>
</flow>
