<?xml version="1.0" encoding="UTF-8"?>
<flow>
    <chain name="qualitycompareanalys_xsqtdbjc_query" label="查询学生群体对比-图表数据" version="1.0.0" moduleCode="dbfx" moduleGroupCode="" permitExtFlag="1" apiEncrypt="0" sysFlag="0" apiTimeout="0" configPageCode="">
         THEN( qualitycompareanalys_xsqtdbjc_query )
        <elGraph>
            <![CDATA[
                {"newNodes":[{"id":"init_ez4my0aw5ao0000","type":"start-node","x":400,"y":300,"properties":{"componentId":"page_init","componentName":"pageInit","name":"开始","logo":"api-kaishi","status":"normal"}},{"id":"logic_fue9onerb340000","type":"common-node","x":610,"y":300,"properties":{"type":"nodeCustom","name":"查询学生群体对比-图表数据","componentName":"nodeCustom","logo":"api-jiedian","level":0,"status":"selected","modelId":"qualitycompareanalys_xsqtdbjc_query","extendFlag":0,"contexts":{"com.wisedu.lowcode4j.app.qualitycompareanalys.dbfx.context.JwqcaStuAnalysisQueryContext":"JwqcaStuAnalysisQueryContext"},"componentType":"0"},"text":{"x":610,"y":300,"value":"查询学生群体对比-图表数据"}}],"newEdges":[{"id":"logic_76l18g65ss00000","type":"logic-line","sourceNodeId":"init_ez4my0aw5ao0000","targetNodeId":"logic_fue9onerb340000","startPoint":{"x":450,"y":300},"endPoint":{"x":516,"y":300},"properties":{},"pointsList":[{"x":450,"y":300},{"x":516,"y":300}]}]}
            ]]>
        </elGraph>
        <firstContext>com.wisedu.lowcode4j.app.qualitycompareanalys.dbfx.context.JwqcaStuAnalysisQueryContext</firstContext>
        <apiContext>com.wisedu.lowcode4j.app.qualitycompareanalys.dbfx.context.JwqcaStuAnalysisQueryContext</apiContext>
    </chain>
</flow>
