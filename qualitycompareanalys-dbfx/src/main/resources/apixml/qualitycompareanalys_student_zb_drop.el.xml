<?xml version="1.0" encoding="UTF-8"?>
<flow>
    <chain name="qualitycompareanalys_student_zb_drop" label="查询群体对比分析本科生指标下拉" version="1.0.0" moduleCode="dbfx" moduleGroupCode="" permitExtFlag="1" apiEncrypt="0" sysFlag="0" apiTimeout="0" configPageCode="">
         THEN( qualitycompareanalys_student_zb_drop )
        <elGraph>
            <![CDATA[
                {"newNodes":[{"id":"init_btfqiclsjn40000","type":"start-node","x":400,"y":300,"properties":{"componentId":"page_init","componentName":"pageInit","name":"开始","logo":"api-kaishi","parentId":null,"status":"normal"}},{"id":"logic_23l2i18ms9y8000","type":"common-node","x":610,"y":300,"properties":{"type":"nodeCustom","name":"查询群体对比分析本科生指标下拉","componentName":"nodeCustom","logo":"api-jiedian","level":0,"status":"selected","modelId":"qualitycompareanalys_student_zb_drop","extendFlag":0,"contexts":{"com.wisedu.lowcode4j.common.core.context.QueryContext":"通用查询上下文"},"componentType":"0"},"text":{"x":610,"y":300,"value":"查询群体对比分析本科生指标下拉"}}],"newEdges":[{"id":"logic_qkok7uyfa68000","type":"logic-line","sourceNodeId":"init_btfqiclsjn40000","targetNodeId":"logic_23l2i18ms9y8000","startPoint":{"x":450,"y":300},"endPoint":{"x":516,"y":300},"properties":{},"pointsList":[{"x":450,"y":300},{"x":516,"y":300}]}]}
            ]]>
        </elGraph>
        <firstContext>com.wisedu.lowcode4j.common.core.context.QueryContext</firstContext>
        <apiContext>com.wisedu.lowcode4j.common.core.context.QueryContext</apiContext>
    </chain>
</flow>
