<?xml version="1.0" encoding="UTF-8"?>
<flow>
    <chain name="qualitycompareanalys_student_qtfxqtdb_query" label="群体分析群体对比查询" version="1.0.0" moduleCode="dbfx" moduleGroupCode="" permitExtFlag="1" apiEncrypt="0" sysFlag="0" apiTimeout="0" configPageCode="">
         THEN( qualitycompareanalys_qtfxqtdb_query )
        <elGraph>
            <![CDATA[
                {"newNodes":[{"id":"init_ufy4unpzl4g000","type":"start-node","x":400,"y":130,"properties":{"componentId":"page_init","componentName":"pageInit","name":"开始","logo":"api-kaishi","status":"normal"}},{"id":"b28949b7-7fdf-4f86-b1e9-f684ba852f74","type":"common-node","x":600,"y":130,"properties":{"type":"nodeCustom","componentName":"common-node","name":"查询群体分析群体对比","logo":"api-jiedian","status":"hovered","componentType":"0","modelId":"qualitycompareanalys_qtfxqtdb_query","extendFlag":0,"contexts":{"com.wisedu.lowcode4j.main.qcapub.context.JwqcaGroupComparisonContext":"JwqcaGroupComparisonContext"}},"text":{"x":600,"y":130,"value":"查询群体分析群体对比"}}],"newEdges":[{"id":"b4851710-94b1-45f5-8a64-45345d0fbac3","type":"logic-line","sourceNodeId":"init_ufy4unpzl4g000","targetNodeId":"b28949b7-7fdf-4f86-b1e9-f684ba852f74","startPoint":{"x":450,"y":130},"endPoint":{"x":510,"y":130},"properties":{},"pointsList":[{"x":450,"y":130},{"x":510,"y":130}]}]}
            ]]>
        </elGraph>
        <firstContext>com.wisedu.lowcode4j.main.qcapub.context.JwqcaGroupComparisonContext</firstContext>
        <apiContext>com.wisedu.lowcode4j.main.qcapub.context.JwqcaGroupComparisonContext</apiContext>
    </chain>
</flow>
