<?xml version="1.0" encoding="utf-8"?>
<sqltoy xmlns="http://www.sagframe.com/schema/sqltoy"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.sagframe.com/schema/sqltoy http://www.sagframe.com/schema/sqltoy/sqltoy.xsd">

    <sql id="jwqca_qt_nj">
        <value>
            <![CDATA[
                select
                    'fxqt' as id,
                    :fxqtName as groupName,
                    count(*) as totalCount,
                    count(distinct XNZYDLDM) as majorCount
                from INS_BKSJBQK
                where tjnf = :tjnf and nj = :fxqtNjdm
                #[@blank(:fxqtXydm) and XY = :fxqtXydm]
                #[@blank(:fxqtZydm) and xnzydldm = :fxqtZydm]
                union all
                select
                    'dbqt' as id,
                    :dbqtName as groupName,
                    count(*) as totalCount,
                    count(distinct XNZYDLDM) as majorCount
                from INS_BKSJBQK
                where tjnf = :tjnf and nj = :dbqtNjdm
                #[@blank(:dbqtXydm) and XY = :dbqtXydm]
                #[@blank(:dbqtZydm) and xnzydldm = :dbqtZydm]
            ]]>
        </value>
    </sql>

    <sql id="jwqca_qt_xy">
        <value>
            <![CDATA[
                select
                    'fxqt' as id,
                    :fxqtName as groupName,
                    count(*) as totalCount,
                    count(distinct xnzydldm) as majorCount
                from INS_BKSJBQK
                where tjnf = :tjnf and XY = :fxqtXydm
                union all
                select
                    'dbqt' as id,
                    :dbqtName as groupName,
                    count(*) as totalCount,
                    count(distinct xnzydldm) as majorCount
                from INS_BKSJBQK
                where tjnf = :tjnf and XY = :dbqtXydm
            ]]>
        </value>
    </sql>

    <sql id="jwqca_qt_zy">
        <value>
            <![CDATA[
                select
                    'fxqt' as id,
                    :fxqtName as groupName,
                    count(*) as totalCount,
                    '0' as majorCount
                from INS_BKSJBQK
                where tjnf = :tjnf and xnzydldm = :fxqtZydm
                union all
                select
                    'dbqt' as id,
                    :dbqtName as groupName,
                    count(*) as totalCount,
                    '0' as majorCount
                from INS_BKSJBQK
                where tjnf = :tjnf and xnzydldm = :dbqtZydm
            ]]>
        </value>
    </sql>

    <sql id="jwqca_xs_zb">
        <value>
            <![CDATA[
                SELECT @value(:modelTable).*, INS_BKSJBQK.XSXM
                FROM @value(:modelTable)
                LEFT JOIN INS_BKSJBQK ON @value(:joinColumn) = INS_BKSJBQK.TJNF AND @value(:modelTable).@value(:entityColumnName) = INS_BKSJBQK.XH
                WHERE INS_BKSJBQK.TJNF = :statisticYear AND @value(:condition)
                ORDER BY INS_BKSJBQK.XH
            ]]>
        </value>
    </sql>
</sqltoy>
