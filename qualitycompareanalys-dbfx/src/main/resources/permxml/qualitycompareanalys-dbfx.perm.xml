<?xml version="1.0" encoding="UTF-8"?>
<app id="qualitycompareanalys" name="学业表现" groupId="qualitycompareanalysgroup">
    <!--菜单（目录）-->
    <menus id="qualitycompareanalys-dbfx" formRouterName="dbfx" name="质量群体分析" showOrder="2">
        <!--菜单（页面）-->
        <menu bindType="default" formRouterName="xsqtdb" id="qualitycompareanalys-xsqtdb"
              menuPattern="pc"
              name="群体对比"
              showOrder="1">
            <routerParams>{ "appCode": "qualitycompareanalys", "pageCode": "xsqtdb"}</routerParams>
            <!-- 权限字-表单 -->
            <permCode name="群体对比" permCode="formxsqtdb" permCodeId="qualitycompareanalys-xsqtdb"
                      permCodeType="0"/>
            <!-- 权限字-片段 -->
            <permCode name="群体对比" permCode="formxsqtdb:fragmentxsqtdb"
                      permCodeId="fragmentqualitycompareanalys-xsqtdb"
                      permCodeType="1">
                <perm name="获取res静态文件" url="/staticFile/getStaticFile"/>
                <perm name="刷选框下拉接口" url="/jwdatapub/select/**"/>

                <perm name="查询群体对比分析本科生指标下拉" url="/admin/api/query/qualitycompareanalys_student_zb_drop"/>
                <perm name="群体对比分析-学生对比分析详情" url="/admin/api/query/qualitycompareanalys_student_config_detail"/>
                <perm name="群体对比分析-学生对比分析编辑" url="/admin/api/execute/qualitycompareanalys_student_config_edit"/>

                <perm name="群体对比分析-学生对比分析上下文初始化" url="/admin/api/execute/qualitycompareanalys_student_config_init"/>
                <perm name="群体对比分析-学生对比分析保存校验" url="/admin/api/execute/qualitycompareanalys_student_config_validate"/>
                <perm name="群体对比分析-学生对比分析保存" url="/admin/api/execute/qualitycompareanalys_student_config_save"/>

                <perm name="查询学生群体对比-图表数据" url="/admin/api/query/qualitycompareanalys_xsqtdbjc_query"/>
                <perm name="查询学生群体对比-指标列表" url="/admin/api/query/qualitycompareanalys_student_zb_list"/>
                <perm name="查询学生群体对比-指标数据" url="/admin/api/query/qualitycompareanalys_student_zb_data"/>

                <perm name="查询分析群体和对标群体" url="/admin/api/query/qualitycompareanalys_student_qt_query"/>
                <perm name="查询群体分析群体对比" url="/admin/api/query/qualitycompareanalys_student_qtfxqtdb_query"/>
                <perm name="群体分析学生列表-查询" url="/admin/api/query/qualitycompareanalys_student_qtfxxslb_query"/>

                <perm name="群体学生分析-查询学生基本信息" url="/eda/qualitycompareanalys/find/bksxxlb/bksxxlb"/>
                <perm name="群体学生分析-导出" url="/eda/qualitycompareanalys/export/qualityanalysisemptybo"/>
                <perm name="群体分析学生列表-导出" url="/eda/qualitycompareanalys/export/bksxxlb"/>
            </permCode>
            <btn id="qualitycompareanalys-xsqtdb-show" menuPattern="pc" name="显示">
                <permCode name="显示" permCode="formxsqtdb:fragmentxsqtdb:show"
                          permCodeId="qualitycompareanalys-xsqtdb-show"/>
            </btn>
        </menu>
    </menus>
</app>