{"list": [{"isRoot": true, "type": "row", "comType": "page", "__tree_label": "页面", "__tree_node_key": "000026814729847446372", "key": "page_8se4b87p", "uuid": "uuid_flk635dl", "children": [{"__tree_node_key": "2761391828918023", "key": "row_jx1szl7i", "__tree_label": "区域容器", "comType": "row", "type": "row", "isCover": false, "children": [{"__tree_node_key": "9109806987398523", "key": "col_q9rf35df", "__tree_label": "区块容器", "comType": "col", "type": "col", "isCover": false, "children": [{"uuid": "uuid_rjmjlj47", "key": "custom_0ku1yeng", "com": "xsqtdbfx", "comType": "custom", "icon": "pm-icon-custom-com", "comClassify": "custom", "name": "学生群体对比分析", "isCover": false, "cmpCode": "xsqtdbfx", "cmpDomain": "ea", "cmpGroup": "1746781249267001173", "cmpName": "学生群体对比分析", "createBy": "lowcodeadmin", "createTime": "2025-05-09 17:01:57", "id": "1746781317787001173", "updateBy": "lowcodeadmin", "updateTime": "2025-05-09 17:01:57", "options": {"width": 0, "showLabel": true, "labelWidth": 100, "labelPosition": "left", "labelShowType": "default", "dataId": "", "tableForm": true, "column": 2, "autoLoad": true, "size": "", "hidden": false, "readonly": false, "disabled": false, "groupType": "default", "hideType": "display", "validateGroup": "", "undefined": ""}, "events": {"mounted": {}}, "dataOptions": {"prefix": "", "remoteType": "model", "remoteModel": {"actionType": ""}, "dataSource": {"type": "in", "url": "", "code": "", "params": {}}, "remote": true}, "__tree_label": "xsqtdbfx", "comStyles": {"marginBottom": "0"}, "pageCode": "xsqtdb"}], "options": {"paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "pageCode": "xsqtdb", "uuid": "uuid_rrl2q085"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "0 0 0 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": false, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false, "bgType": "bgcolor", "backgroundColor": "", "borderRadius": 0}, "showTitle": false, "title": "容器标题", "layout": "grid", "pageCode": "xsqtdb", "uuid": "uuid_emua7osm"}], "options": {"marginTop": 0, "marginBottom": 12, "marginLeft": 0, "marginRight": 0, "widthType": "0", "maxWidth": 0, "minWidth": 0, "width": "260px"}, "pageCode": "xsqtdb"}], "leftList": [{"isRoot": true, "type": "row", "comType": "page", "__tree_label": "页面", "__tree_node_key": "1483173486577556", "key": "page_07pmypnk", "uuid": "uuid_b8ejz72c", "children": [], "pageCode": "xsqtdb"}], "rightList": [{"isRoot": true, "type": "row", "comType": "page", "__tree_label": "页面", "__tree_node_key": "12729851381108437", "key": "page_ab9a8mta", "uuid": "uuid_hdzadfip", "children": [], "pageCode": "xsqtdb"}], "config": {"dataSource": [], "platform": "pc", "layout": "default", "leftWidth": 260, "rightWidth": 260, "foldingSwitchTop": 50, "foldingSwitch": false}, "dialogJson": [{"__tree_node_key": "1535013473459581", "key": "dialog_g84k3uiq", "__tree_label": "区域容器", "comType": "row", "type": "row", "children": [{"__tree_node_key": "5443350320868798", "key": "col_5855uz28", "__tree_label": "区块容器", "comType": "col", "type": "col", "children": [{"__tree_node_key": "9499211915493062", "key": "row_3as1talk", "__tree_label": "区域容器", "comType": "row", "type": "row", "children": [{"__tree_node_key": "6616938721048203", "key": "col_7ncm7xqo", "__tree_label": "区块容器", "comType": "col", "type": "col", "children": [{"uuid": "uuid_ymy1mcos", "key": "adv-search_xv8bv8mb", "com": "adv-search", "comType": "adv-search", "icon": "pm-icon-advancedSearch", "comClassify": "model", "dataType": "object", "options": {"readonly": false, "interacTable": "adv-table_twmcblzc", "limit": 2, "keyWordsLabel": "关键字", "hideKeyWords": false, "comLinkEnabled": true, "labelWidth": 100, "keyWordsHighlight": true, "size": "", "itemWidth": 320, "singleItemWidth": 400, "tileItemWidth": 320, "searchTime": "", "conditionType": "", "hidden": false, "readyDoSearch": false, "hideNewFields": false, "closeModelSort": false, "showAdv": false, "fullscreen": false, "isCondition": false, "isMore": true, "beforeRender": {"name": "before_render_w6hpl66w", "enName": "", "params": [{"name": "modelItem", "des": "对应模型信息，可以修改模型内容；返回一个模型信息"}]}, "showHidden": true, "appendToBody": true, "undefined": "", "beforeSearch": {}}, "events": {"mounted": {"name": "", "params": [{"name": "event", "des": "内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组)"}]}, "inited": {"tip": "模型和数据获取完成"}, "search": {"name": "adv_search_utx4qjkq_search", "tip": "搜索之前，参数为querySetting(同步)"}, "before-reset": {"name": "adv_search_utx4qjkq_before_reset", "tip": "重置之前(同步)"}, "async-before-reset": {"tip": "重置之前(异步，参数为回调函数，执行查询需要调用回调)"}, "reset": {"tip": "重置后"}, "adv-reset": {"tip": "高级筛选重置后(需开启高级筛选功能)"}, "collapse": {"tip": "收起条件后"}, "expand": {"tip": "展开条件后"}, "item-change": {"tip": "每一项的值触发change后"}, "dict-loaded": {"tip": "每当有某一项的字典数据请求完成后"}, "dict-change": {"tip": "字典项控件的值发生改变时触发"}, "beforeDestroy": {}}, "dataOptions": {"remote": true, "remoteType": "model", "remoteModel": {"actionType": "search", "id": "qualitycompareanalys-bksxxlb", "url": "/admin/model/design/qualitycompareanalys/perm/bksxxlb", "modelCascades": "", "modelName": "bksxxlb", "modelApp": "qualitycompareanalys"}, "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "prefix": "search", "dataSource": {"type": "in", "url": "", "code": "", "params": {}}, "columnsModel": [{"name": "xh"}, {"name": "xsxm"}, {"name": "xb"}, {"name": "nj"}, {"name": "xy"}, {"name": "xnzydldm"}, {"name": "xnzydlmc"}, {"name": "xzb"}, {"name": "bndgpa"}, {"name": "bndsjsxcs"}, {"name": "bndxscgsl"}, {"name": "b<PERSON><PERSON><PERSON>"}, {"name": "xjzt"}, {"name": "tjnf"}], "modelConfig": {"xh": {"placeholder": "请选择", "search.omitted": 0, "search.JSONParam": {}}, "xsxm": {"placeholder": "请选择", "search.omitted": 0, "search.JSONParam": {}}, "nj": {"placeholder": "请选择", "search.omitted": 0}, "xnzydldm": {"placeholder": "请输入", "search.omitted": 0}, "tjnf": {"search.xtype": "number", "placeholder": "请输入", "search.omitted": 0, "search.JSONParam": {"disabled": true}}}}, "name": "高级搜索", "isCover": false, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "adv-search", "pageCode": "xsqtdb"}, {"uuid": "uuid_holb7rhf", "key": "adv-table_twmcblzc", "com": "adv-vxe-table", "comType": "adv-table", "icon": "pm-icon-adv-table", "comClassify": "model", "dataType": "object", "options": {"readonly": false, "customClass": "", "labelWidth": 100, "bindPager": "", "bindComEmpty": {}, "bindComTop": {}, "bindComBottom": {}, "hidden": false, "editable": false, "hideNewFields": false, "closeModelSort": false, "autoLoad": false, "searchReserveData": false, "localDataSort": false, "scheme": true, "comLinkEnabled": true, "appendToBody": true, "tableConfig": {"border": "default", "seq": {"enabled": false, "seqTitle": "序号", "seqWidth": 60, "align": "center", "headerAlign": "center"}, "rowSort": {"enabled": false, "handle": "", "fixed": "left"}, "selectType": {"type": "", "checkTitle": "", "checkWidth": 40, "labelField": "", "reserve": true, "highlight": true, "range": true, "visibleMethodFunc": {}, "checkMethodFunc": {}, "align": "center", "headerAlign": "center"}, "showHeaderOverflow": true, "showOverflow": true, "fullHeight": false, "height": 0, "minHeight": 0, "maxHeight": 1000, "stripe": false, "emptyText": "暂无数据", "align": "left", "header-align": "left", "rowId": "id", "groupField": "tableColumnGroup", "groupEnabled": false, "beforeRenderFunc": {}, "spanMethodFunc": {}, "dataLoadFunc": {}}, "rowConfig": {"isHover": true, "isCurrent": false, "height": 46}, "statConfig": {"isShowStat": false, "statConfigList": []}, "reportConfig": {"reportText": "导出报表", "reportList": []}, "batchDownloadConfig": {"btnText": "批量下载", "btnConfigList": []}, "columnConfig": {"resizable": true, "minWidth": 80, "width": 0, "isHover": true, "isCurrent": false}, "flowBtnConfig": {"labelWidth": 120, "beforeRender": {}, "limit": 30, "fileSize": 100, "labelPosition": "left", "isTable": false, "listType": "text", "undefined": ""}, "customConfig": {"storage": true, "checkMethodFunc": {}}, "operationConfig": {"enabled": true, "position": "right", "tip": "", "title": "操作", "width": 100, "schema": "text", "buttonList": [{"label": "学生画像", "id": "zlal73aa", "uuid": "ei0nhiv8", "type": "primary", "func": {"name": "action_ev_ia10z40t", "params": [{"name": "event", "des": "{row}:对应的行数据;{col}:对应的列数据;{btn}:点击的按钮；{scope}:表格对象"}], "enName": "学生画像"}, "children": []}], "buttonEditList": [], "renderFunc": {}, "template": "", "beforeDialog": {}}, "pagerConfig": {"pageStyle": "normal", "enabled": true, "pageSize": 20, "background": false, "pagerCount": 7, "hideOnSinglePage": false, "position": "right", "pageSizes": [5, 10, 20, 50, 100], "layouts": ["prev", "pager", "next", "sizes", "jumper"], "undefined": ""}, "toolbarConfig": {"flow": false, "import": false, "export": true, "print": false, "enabled": true, "zoom": true, "custom": true, "sort": false, "report": false, "batchDownload": false, "exportOptions": {"tooltip": {}}, "printOptions": {"tooltip": {}}, "zoomOptions": {"tooltip": {}}, "customOptions": {"tooltip": {}}, "importOptions": {"tooltip": {}}, "reportOptions": {"tooltip": {}}, "leftButtonList": {"schema": "text", "buttonList": [], "renderFunc": {}, "beforeDialog": {}}, "rightButtonList": {"schema": "text", "buttonList": [], "renderFunc": {}}}, "exportConfig": {"filename": "学生信息列表", "dateFormat": "", "sheetName": "sheet", "type": "xlsx", "showOneClick": true, "showCustomClick": false, "oneClickBtnText": "导出", "customBtnText": "自定义导出", "types": ["xlsx"], "translateDictFlag": true, "apiUrl": ""}, "treeConfig": {"lazy": false, "transform": false, "parentField": "parentId", "hasChildField": "<PERSON><PERSON><PERSON><PERSON>", "expandAll": false, "accordion": false, "trigger": "default", "indent": 20, "treeNodeField": ""}, "editConfig": {"trigger": "click", "mode": "row", "showStatus": true, "showAsterisk": true, "autoClear": true, "beforeActiveEditMethodFunc": {}, "beforeEditMethodFunc": {}}, "editRules": {}, "importConfig": {"config": "file", "filename": "模板文件", "dateFormat": "", "isGenerateZdb": true, "customParam": ""}, "undefined": ""}, "dataOptions": {"remote": true, "remoteType": "model", "remoteModel": {"actionType": "table", "id": "qualitycompareanalys-bksxxlb", "url": "/admin/model/design/qualitycompareanalys/perm/bksxxlb", "modelCascades": "", "modelName": "bksxxlb", "modelApp": "qualitycompareanalys"}, "remoteDict": {"dictName": "", "fields": {"label": "", "value": "", "children": ""}}, "prefix": "grid", "columnsModel": [{"name": "xh"}, {"name": "xsxm"}, {"name": "xb"}, {"name": "nj"}, {"name": "xy"}, {"name": "xnzydldm"}, {"name": "xnzydlmc"}, {"name": "xzb"}, {"name": "bndgpa"}, {"name": "bndsjsxcs"}, {"name": "bndxscgsl"}, {"name": "b<PERSON><PERSON><PERSON>"}, {"name": "xjzt"}, {"name": "tjnf"}], "dataSource": {"type": "api", "url": "/admin/api/query/qualitycompareanalys_student_qtfxxslb_query", "code": "qualitycompareanalys_student_qtfxxslb_query", "dataPath": "rows", "params": {"qtlx": "{vars.qtlx}", "xnzydldm": "{vars.xnzydldm}", "tjnf": "{vars.tjnf}", "field": "page"}}, "modelConfig": {"xh": {"grid.sortable": 1, "placeholder": "请选择", "grid.width": 0, "grid.omitted": 0}, "xsxm": {"grid.sortable": 1, "placeholder": "请选择", "grid.width": 0, "grid.omitted": 0}, "xb": {"grid.sortable": 1}, "nj": {"grid.sortable": 1}, "xy": {"grid.sortable": 1}, "xnzydldm": {"grid.sortable": 1}, "xnzydlmc": {"grid.sortable": 1}, "xzb": {"grid.sortable": 1}, "bndgpa": {"grid.sortable": 1}, "bndsjsxcs": {"grid.sortable": 1}, "bndxscgsl": {"grid.sortable": 1}, "bndrych": {"grid.sortable": 1}, "xjzt": {"grid.sortable": 1}, "tjnf": {"grid.sortable": 1}}, "dataProps": {"pageSize": 1, "pageNo": 20, "totalSize": 1}}, "events": {"mounted": {"params": [{"name": "event", "des": "内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组)"}]}, "model-inited": {"tip": "模型获取完成"}, "reload-data": {"name": "", "enName": "reload-data", "tip": "数据获取完成"}, "operate-click": {"tip": "操作列按钮点击"}, "radio-change": {"tip": "单选切换"}, "checkbox-change": {"tip": "多选点击"}, "checkbox-all": {"tip": "全选点击"}, "cell-click": {"tip": "单元格点击"}, "scroll": {"tip": "表格滚动"}, "page-change": {"tip": "切换当前页面"}, "size-change": {"tip": "每页数量变化"}, "zoom": {"tip": "表格切换最大化"}, "edit-closed": {"tip": "退出当前编辑"}, "row-sort-change": {"tip": "行拖动排序"}, "export-complete": {"tip": "导出完成"}, "startAndTakeUserTask": {"tip": "流程启动"}, "agree": {"tip": "流程同意"}, "startAndSaveDraft": {"tip": "流程保存草稿"}, "afterClick": {"tip": "按钮事件后触发后回调"}, "flowButtonSucceed": {"tip": "流程按钮执行成功后"}, "beforeDestroy": {}}, "name": "高级表格", "isCover": false, "interactList": [], "comStyles": {"marginBottom": "0"}, "__tree_label": "adv-vxe-table", "pageCode": "xsqtdb"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "events": {"mounted": "", "destroy": ""}, "pageCode": "xsqtdb", "uuid": "uuid_0y7tprhb"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "none", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false, "bgType": "bgcolor", "backgroundColor": "", "borderRadius": 0}, "showTitle": false, "title": "容器标题", "events": {"mounted": "", "destroy": ""}, "layout": "grid", "pageCode": "xsqtdb", "uuid": "uuid_2979qp1q"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "0 2px 12px 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "width": 24, "events": {"mounted": "", "destroy": ""}, "pageCode": "xsqtdb", "uuid": "uuid_w9lqb7bm"}], "options": {"marginTop": 0, "marginBottom": 0, "marginLeft": 0, "marginRight": 0, "boxShadow": "0 2px 12px 0 rgb(0 0 0 / 10%)", "isBoxShadowEnable": true, "paddingTop": 0, "paddingBottom": 0, "paddingLeft": 0, "paddingRight": 0, "hidden": false}, "showTitle": true, "title": "学生信息列表", "events": {"mounted": {"name": "mounted_dialog_vhyb8m7t", "enName": "打开弹框", "params": [{"name": "event", "des": "name:事件名称,dialog:当前弹窗对象"}]}, "destroy": {}}, "layout": "grid", "sign": "dialog", "btnsPosition": "right", "frameType": "1", "widthType": "percent", "percentWidth": "80%", "size": "medium", "pixelWidth": "1184", "commonOptions": {"fullscreen": "false", "closeOnClickModal": "false"}, "drawerOptions": {"frameDirection": "rtl", "wrapperClosable": "true"}, "btnOptions": [], "btnRenderFunc": {}, "windowsize": "1184", "hideAction": true, "pageCode": "xsqtdb", "uuid": "uuid_p7g1z04e"}], "suspendJson": []}