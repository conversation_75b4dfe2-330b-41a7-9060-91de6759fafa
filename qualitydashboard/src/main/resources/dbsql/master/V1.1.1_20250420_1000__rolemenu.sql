-- 新增用户授权菜单记录
DELETE FROM LOWCODE_ROLE_MENU where ROLE_ID = 'role-daadmin' and MENU_ID like '%qualitydashboard%';


INSERT INTO LOWCODE_ROLE_MENU (ROLE_ID, MENU_ID) VALUES ('role-daadmin', 'qualitydashboard-cgzt');
INSERT INTO LOWCODE_ROLE_MENU (ROLE_ID, MENU_ID) VALUES ('role-daadmin', 'qualitydashboard-cgzt-show');
INSERT INTO LOWCODE_ROLE_MENU (ROLE_ID, MENU_ID) VALUES ('role-daadmin', 'qualitydashboard-qxgl');
INSERT INTO LOWCODE_ROLE_MENU (ROLE_ID, MENU_ID) VALUES ('role-daadmin', 'qualitydashboard-qxgl-show');
INSERT INTO LOWCODE_ROLE_MENU (ROLE_ID, MENU_ID) VALUES ('role-daadmin', 'qualitydashboard-xszt');
INSERT INTO LOWCODE_ROLE_MENU (ROLE_ID, MENU_ID) VALUES ('role-daadmin', 'qualitydashboard-xszt-show');
INSERT INTO LOWCODE_ROLE_MENU (ROLE_ID, MENU_ID) VALUES ('role-daadmin', 'qualitydashboard-zljsc');
INSERT INTO LOWCODE_ROLE_MENU (ROLE_ID, MENU_ID) VALUES ('role-daadmin', 'qualitydashboard-zlpg');
INSERT INTO LOWCODE_ROLE_MENU (ROLE_ID, MENU_ID) VALUES ('role-daadmin', 'qualitydashboard-zlpg-show');
INSERT INTO LOWCODE_ROLE_MENU (ROLE_ID, MENU_ID) VALUES ('role-daadmin', 'qualitydashboard-zszt');
INSERT INTO LOWCODE_ROLE_MENU (ROLE_ID, MENU_ID) VALUES ('role-daadmin', 'qualitydashboard-zszt-show');
INSERT INTO LOWCODE_ROLE_MENU (ROLE_ID, MENU_ID) VALUES ('role-daadmin', 'qualitydashboard-zyzt');
INSERT INTO LOWCODE_ROLE_MENU (ROLE_ID, MENU_ID) VALUES ('role-daadmin', 'qualitydashboard-zyzt-show');