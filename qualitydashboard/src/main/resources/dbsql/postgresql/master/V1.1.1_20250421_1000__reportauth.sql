-- 删除质量驾驶舱相关的报表权限
DELETE FROM T_MENU_MODULE_CODE WHERE MENU_ID LIKE 'qualitydashboard-%';
-- 新增质量驾驶舱的报表权限
INSERT INTO T_MENU_MODULE_CODE (ID, MENU_ID, MODULE_CODE, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES ('1745222334004001655', 'qualitydashboard-qxgl', 'zljsc_qxgl_qxgl', 'lowcodeadmin', TIMESTAMP '2025-04-21 15:58:54', 'lowcodeadmin', TIMESTAMP '2025-04-21 15:58:54');
INSERT INTO T_MENU_MODULE_CODE (ID, MENU_ID, MODULE_CODE, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES ('1745222359544001655', 'qualitydashboard-zyzt', 'zljsc_zyzt_zyzt', 'lowcodeadmin', TIMESTAMP '2025-04-21 15:59:19', 'lowcodeadmin', TIMESTAMP '2025-04-21 15:59:19');
INSERT INTO T_MENU_MODULE_CODE (ID, MENU_ID, MODULE_CODE, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES ('1745222379350001655', 'qualitydashboard-zszt', 'zljsc_zszt_zszt', 'lowcodeadmin', TIMESTAMP '2025-04-21 15:59:39', 'lowcodeadmin', TIMESTAMP '2025-04-21 15:59:50');
INSERT INTO T_MENU_MODULE_CODE (ID, MENU_ID, MODULE_CODE, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES ('1745222406990001655', 'qualitydashboard-cgzt', 'zljsc_cgzt_cgzt', 'lowcodeadmin', TIMESTAMP '2025-04-21 16:00:06', 'lowcodeadmin', TIMESTAMP '2025-04-21 16:00:06');
INSERT INTO T_MENU_MODULE_CODE (ID, MENU_ID, MODULE_CODE, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES ('1745222423135001655', 'qualitydashboard-xszt', 'zljsc_xszt_xszt', 'lowcodeadmin', TIMESTAMP '2025-04-21 16:00:23', 'lowcodeadmin', TIMESTAMP '2025-04-21 16:00:23');
INSERT INTO T_MENU_MODULE_CODE (ID, MENU_ID, MODULE_CODE, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME) VALUES ('1745222443611001655', 'qualitydashboard-zlpg', 'zljsc_zlpg_zlpg', 'lowcodeadmin', TIMESTAMP '2025-04-21 16:00:43', 'lowcodeadmin', TIMESTAMP '2025-04-21 16:00:43');
