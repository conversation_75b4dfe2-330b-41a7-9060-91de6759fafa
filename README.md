## 子模块使用说明
尽量不手动操作.gitmodules文件，改为使用子模块命令执行相关的操作，避免异常问题
拉取外框架后，需要执行以下命令
```
//递归地初始化并更新子模块及其所有嵙套的子模块。
git submodule update --init --recursive
```
如果后续有新的模块，可以使用以下命名添加
```
git submodule add <url> <path>：添加新的子模块。
例： git submodule add http://172.16.7.53:9090/dataapp/app-portraitdemo.git lowcode4j-apps/app-portraitdemo

```
如果手动修改了.gitmodules文件，需要执行以下命令:
```
git submodule sync
```

#### 以下可能是会常用到的命令
* git submodule init：初始化子模块，即将 .gitmodules 中的信息复制到 .git/config。
* git submodule update：更新子模块到在主仓库中记录的特定提交。
* git submodule set-branch --branch V1.1.1 jspub 设置子模块的默认分支
* git submodule update --remote modelpub toolpub 同时更新多个子模块
* git submodule deinit lowcode4j-apps/app-dahvcdreport 清除关联子模块
* git submodule update --init --remote lowcode4j-apps/app-stuportrait 注册并更新子模块

## 结构说明
* dataapp: 只用来描述应用结构，目录下的.gitmodules文件，用来描述各个子模块所在的git项目以及分支
* dataapp/pom.xml: 下载该项目后，根据需要修改pom文件的依赖。默认只加上了toolpub和modelpub的依赖。比如一站式填报需要依赖jspub，则手动放开jspub的依赖
* dataapp/lowcode4j-apps/pom.xml: 用来描述低代码应用模块的依赖，默认没有应用的依赖。需要开发哪个应用，则放开对应的依赖。
* dataapp/lowcode4j-apps下的应用，都有各自的git项目，可以独立更新
* dataapp/*pub: 各个pub都是独立的git项目