package com.wisedu.lowcode4j.app.dataqueryai.po;

import com.wisedu.lowcode4j.app.dataqueryai.vo.DataQueryAiModelColumnVo;
import com.wisedu.lowcode4j.common.core.annotation.ColumnDefine;
import com.wisedu.lowcode4j.common.core.annotation.DisableDataFilter;
import com.wisedu.lowcode4j.common.core.annotation.ModelDefine;
import com.wisedu.lowcode4j.common.core.annotation.ModelExt;
import com.wisedu.lowcode4j.common.core.model.BaseDataModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nutz.dao.entity.annotation.ColType;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Comment;
import org.nutz.dao.entity.annotation.Table;
import org.sagacity.sqltoy.config.annotation.SqlToyEntity;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "dataqueryaimodelcolumn", description = "主题域下模型字段")
@Comment("主题域下模型字段")
@ModelDefine(renderType="form")
@Table(value = "t_dqai_model_column")
@SqlToyEntity
@ModelExt(extClass = DataQueryAiModelColumnVo.class)
//end_dynamic_declare
@Data
@DisableDataFilter
public class DataQueryAiModelColumn extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041955336L;

    //region start_dynamic_column
    @ApiModelProperty(name = "topicModelId", value = "主题域下模型ID" ,notes = "2025-03-07 15:28:01")
    @Comment("主题域下模型ID")
    @Column(value = "topic_model_id",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String topicModelId;

    @ApiModelProperty(name = "modelColumnId", value = "字段编码" ,notes = "2025-03-07 15:28:28")
    @Comment("字段编码")
    @Column(value = "model_column_id",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String modelColumnId;

    @ApiModelProperty(name = "columnDbname", value = "字段名称" ,notes = "2025-03-07 15:28:48")
    @Comment("字段名称")
    @Column(value = "column_dbname",type = ColType.AUTO, width = 200,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String columnDbname;

    @ApiModelProperty(name = "columnSynonyms", value = "字段别名" ,notes = "2025-03-07 15:29:15")
    @Comment("字段别名")
    @Column(value = "column_synonyms",type = ColType.TEXT, width = 8000,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String columnSynonyms;

    @ApiModelProperty(name = "columnSemantics", value = "字段用途" ,notes = "2025-03-07 15:29:39")
    @Comment("字段用途")
    @Column(value = "column_semantics",type = ColType.TEXT, width = 8000,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String columnSemantics;

    @ApiModelProperty(name = "searchStrategy", value = "检索策略 0 无 1 字典 2 枚举" ,notes = "2025-03-07 15:28:48")
    @Comment("检索策略 0 无 1 字典 2 枚举")
    @Column(value = "search_strategy",type = ColType.AUTO, width = 200,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String searchStrategy;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
