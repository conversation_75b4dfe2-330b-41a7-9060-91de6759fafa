package com.wisedu.lowcode4j.app.dataqueryai.vo;

import com.wisedu.lowcode4j.app.dataqueryai.po.DataQueryAiChatSqlResult;
import com.wisedu.lowcode4j.common.core.annotation.DisableDataFilter;
import io.swagger.annotations.ApiModel;
import lombok.Data;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "dataqueryaichatsqlresultvoVo", description = "问数结果sql语句存储Vo")
//end_dynamic_declare
@Data
@DisableDataFilter
public class DataQueryAiChatSqlResultVo extends DataQueryAiChatSqlResult {

    private static final long serialVersionUID = 4431474721041959632L;

    //region start_dynamic_dict_column
	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
