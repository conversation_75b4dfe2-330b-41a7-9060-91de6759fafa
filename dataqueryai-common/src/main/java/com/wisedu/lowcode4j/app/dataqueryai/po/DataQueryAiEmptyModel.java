package com.wisedu.lowcode4j.app.dataqueryai.po;

import com.wisedu.lowcode4j.app.dataqueryai.vo.DataQueryAiEmptyModelVo;
import com.wisedu.lowcode4j.common.core.annotation.DisableDataFilter;
import com.wisedu.lowcode4j.common.core.annotation.ModelExt;
import com.wisedu.lowcode4j.common.core.model.BaseDataModel;
import com.wisedu.lowcode4j.common.model.Processor;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.nutz.dao.entity.annotation.Comment;
import org.nutz.dao.entity.annotation.Table;
import org.sagacity.sqltoy.config.annotation.SqlToyEntity;

/**
 * @Description: 多模型查询空模型
 * @Author: 韩俊俊
 * @Date: 2024-02-29 19:58
 * @Version: 1.0
 */
@ApiModel(value = "dataqueryaiemptymodel", description = "AI问数APP的空模型")
@Comment("AI问数APP的空模型")
@SqlToyEntity
@Table
@ModelExt(extClass = DataQueryAiEmptyModelVo.class)
@Processor(processorBean="dataQueryAiEmptyModelProcessor")
//end_dynamic_declare
@Data
@DisableDataFilter
public class DataQueryAiEmptyModel extends BaseDataModel {
    private static final long serialVersionUID = 4431474721041957867L;

    //region start_dynamic_column
    //endregion end_dynamic_column

    //region start_dynamic_cascades
    //region end_dynamic_cascades
}
