package com.wisedu.lowcode4j.app.dataqueryai.po;

import com.wisedu.lowcode4j.app.dataqueryai.vo.DataQueryAiSuggestQuestionVo;
import com.wisedu.lowcode4j.common.core.annotation.ColumnDefine;
import com.wisedu.lowcode4j.common.core.annotation.DisableDataFilter;
import com.wisedu.lowcode4j.common.core.annotation.ModelDefine;
import com.wisedu.lowcode4j.common.core.annotation.ModelExt;
import com.wisedu.lowcode4j.common.core.model.BaseDataModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nutz.dao.entity.annotation.ColType;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Comment;
import org.nutz.dao.entity.annotation.Table;
import org.sagacity.sqltoy.config.annotation.SqlToyEntity;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "dataqueryaisuggestquestion", description = "推荐问题配置表")
@Comment("推荐问题配置表")
@ModelDefine(renderType="form")
@Table(value = "t_dqai_suggest_question")
@SqlToyEntity
@ModelExt(extClass = DataQueryAiSuggestQuestionVo.class)
//end_dynamic_declare
@Data
@DisableDataFilter
public class DataQueryAiSuggestQuestion extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041956018L;

    //region start_dynamic_column
    @ApiModelProperty(name = "topicId", value = "主题域编码" ,notes = "2025-03-07 16:06:46")
    @Comment("主题域编码")
    @Column(value = "topic_id",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String topicId;

    @ApiModelProperty(name = "roleId", value = "角色ID" ,notes = "2025-03-07 16:06:46")
    @Comment("角色ID")
    @Column(value = "role_id",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String roleId;

    @ApiModelProperty(name = "suggestQusetion", value = "推荐的问题" ,notes = "2025-03-07 16:07:07")
    @Comment("推荐的问题")
    @Column(value = "suggest_qusetion",type = ColType.TEXT, width = 8000,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String suggestQusetion;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
