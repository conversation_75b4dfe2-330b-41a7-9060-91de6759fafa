package com.wisedu.lowcode4j.app.dataqueryai.po;

import com.wisedu.lowcode4j.app.dataqueryai.vo.DataQueryAiBgKnowledgeVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;
import org.sagacity.sqltoy.model.SecureType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "dataqueryaibgknowledge", description = "背景提示词管理表")
@Comment("背景提示词管理表")
@ModelDefine(renderType="form")
@Table(value = "t_dqai_bg_knowledge")
@SqlToyEntity
@ModelExt(extClass = DataQueryAiBgKnowledgeVo.class)
//end_dynamic_declare
@Data
@DisableDataFilter
public class DataQueryAiBgKnowledge extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041954648L;

    //region start_dynamic_column
    @ApiModelProperty(name = "keyword", value = "关键词" ,notes = "2025-03-07 15:46:04")
    @Comment("关键词")
    @Column(value = "keyword",type = ColType.AUTO, width = 200,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String keyword;

    @ApiModelProperty(name = "explain", value = "背景知识解释" ,notes = "2025-03-07 15:46:23")
    @Comment("背景知识解释")
    @Column(value = "explain",type = ColType.AUTO, width = 2000,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String explain;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
