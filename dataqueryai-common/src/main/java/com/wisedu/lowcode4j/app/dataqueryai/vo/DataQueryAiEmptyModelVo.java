package com.wisedu.lowcode4j.app.dataqueryai.vo;

import com.wisedu.lowcode4j.app.dataqueryai.po.DataQueryAiEmptyModel;
import com.wisedu.lowcode4j.common.core.annotation.DisableDataFilter;
import io.swagger.annotations.ApiModel;
import lombok.Data;

//start_dynamic_declare
/**
 * @Description: 多模型查询空模型Vo
 * @Author: 韩俊俊
 * @Date: 2024-02-29 19:58
 * @Version: 1.0
 */
@ApiModel(value = "dataqueryaiemptymodelVo", description = "AI问数APP的空模型Vo")
//end_dynamic_declare
@Data
@DisableDataFilter
public class DataQueryAiEmptyModelVo extends DataQueryAiEmptyModel {
    private static final long serialVersionUID = 4431474721041957867L;
}
