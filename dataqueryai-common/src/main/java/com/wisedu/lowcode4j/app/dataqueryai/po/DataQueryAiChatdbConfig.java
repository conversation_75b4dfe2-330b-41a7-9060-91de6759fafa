package com.wisedu.lowcode4j.app.dataqueryai.po;

import com.wisedu.lowcode4j.app.dataqueryai.vo.DataQueryAiChatdbConfigVo;
import com.wisedu.lowcode4j.common.core.annotation.ColumnDefine;
import com.wisedu.lowcode4j.common.core.annotation.DisableDataFilter;
import com.wisedu.lowcode4j.common.core.annotation.ModelDefine;
import com.wisedu.lowcode4j.common.core.annotation.ModelExt;
import com.wisedu.lowcode4j.common.core.model.BaseDataModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nutz.dao.entity.annotation.ColType;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Comment;
import org.nutz.dao.entity.annotation.Table;
import org.sagacity.sqltoy.config.annotation.SqlToyEntity;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "dataqueryaichatdbconfig", description = "模型参数配置表")
@Comment("模型参数配置表")
@ModelDefine(renderType="form")
@Table(value = "t_dqai_chatdb_config")
@SqlToyEntity
@ModelExt(extClass = DataQueryAiChatdbConfigVo.class)
//end_dynamic_declare
@Data
@DisableDataFilter
public class DataQueryAiChatdbConfig extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041951601L;

    //region start_dynamic_column
    @ApiModelProperty(name = "topicId", value = "主题域编码" ,notes = "2025-03-07 15:20:34")
    @Comment("主题域编码")
    @Column(value = "topic_id",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String topicId;

    @ApiModelProperty(name = "configValue", value = "配置项值" ,notes = "2025-03-07 15:48:35")
    @Comment("配置项值")
    @Column(value = "config_value",type = ColType.TEXT, width = 8000,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String configValue;


	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
