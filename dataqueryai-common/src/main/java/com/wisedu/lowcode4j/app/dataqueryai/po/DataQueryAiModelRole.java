package com.wisedu.lowcode4j.app.dataqueryai.po;

import com.wisedu.lowcode4j.app.dataqueryai.vo.DataQueryAiModelRoleVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;
import org.sagacity.sqltoy.model.SecureType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "dataqueryaimodelrole", description = "模型表权限配置表")
@Comment("模型表权限配置表")
@ModelDefine(renderType="form")
@Table(value = "t_dqai_model_role")
@SqlToyEntity
@ModelExt(extClass = DataQueryAiModelRoleVo.class)
//end_dynamic_declare
@Data
@DisableDataFilter
public class DataQueryAiModelRole extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041957750L;

    //region start_dynamic_column
    @ApiModelProperty(name = "userRoleId", value = "用户角色ID" ,notes = "2025-03-07 15:56:10")
    @Comment("用户角色ID")
    @Column(value = "user_role_id",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String userRoleId;

    @ApiModelProperty(name = "topicModelId", value = "主题域下模型ID" ,notes = "2025-03-07 15:56:35")
    @Comment("主题域下模型ID")
    @Column(value = "topic_model_id",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String topicModelId;

    @ApiModelProperty(name = "modelTable", value = "模型表名称" ,notes = "2025-03-07 15:57:02")
    @Comment("模型表名称")
    @Column(value = "model_table",type = ColType.AUTO, width = 200,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String modelTable;

    @ApiModelProperty(name = "visibleRole", value = "是否可见 1 可见  0 不可见" ,notes = "2025-03-07 15:57:29")
    @Comment("是否可见 1 可见  0 不可见")
    @Column(value = "visible_role",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String visibleRole;

    @ApiModelProperty(name = "columnRole", value = "字段权限 1 全部可见，2 部分可见 0 全部不可见" ,notes = "2025-03-07 15:58:41")
    @Comment("字段权限 1 全部可见，2 部分可见 0 全部不可见")
    @Column(value = "column_role",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String columnRole;

    @ApiModelProperty(name = "rowRole", value = "数据行权限 1 全部 2 本学院 3 个人" ,notes = "2025-03-07 15:59:06")
    @Comment("数据行权限 1 全部 2 本学院 3 个人")
    @Column(value = "row_role",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String rowRole;

    @ApiModelProperty(name = "rowRoleRefColumnDbname", value = "行权限关联字段名称" ,notes = "2025-03-07 15:59:35")
    @Comment("行权限关联字段名称")
    @Column(value = "row_role_ref_column_dbname",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String rowRoleRefColumnDbname;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
