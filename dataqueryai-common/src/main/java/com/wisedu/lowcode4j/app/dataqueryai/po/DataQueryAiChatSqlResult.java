package com.wisedu.lowcode4j.app.dataqueryai.po;

import com.wisedu.lowcode4j.app.dataqueryai.vo.DataQueryAiChatSqlResultVo;
import com.wisedu.lowcode4j.common.core.annotation.ColumnDefine;
import com.wisedu.lowcode4j.common.core.annotation.DisableDataFilter;
import com.wisedu.lowcode4j.common.core.annotation.ModelDefine;
import com.wisedu.lowcode4j.common.core.annotation.ModelExt;
import com.wisedu.lowcode4j.common.core.model.BaseDataModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nutz.dao.entity.annotation.*;
import org.sagacity.sqltoy.config.annotation.SqlToyEntity;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "dataqueryaichatsqlresultvo", description = "问数结果sql语句存储")
@Comment("问数结果sql语句存储")
@ModelDefine(renderType="form")
@TableIndexes({
        @Index(fields = {"sqlId"}, name = "idx_chatdb_result_sql_id", unique = false),
})
@Table(value = "t_dqai_chatdb_result_sql")
@SqlToyEntity
@ModelExt(extClass = DataQueryAiChatSqlResultVo.class)
//end_dynamic_declare
@Data
@DisableDataFilter
public class DataQueryAiChatSqlResult extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041957842L;

    //region start_dynamic_column
    @ApiModelProperty(name = "topicId", value = "主题ID" ,notes = "2025-04-03 15:57:30")
    @Comment("主题ID")
    @Column(value = "topic_id",type = ColType.AUTO, width = 100)
    @ColumnDefine(columnXtype="text")
    private String topicId;

    @ApiModelProperty(name = "question", value = "问题" ,notes = "2025-04-03 15:58:25")
    @Comment("问题")
    @Column(value = "question",type = ColType.TEXT, width = 5000)
    @ColumnDefine(columnXtype="text")
    private String question;

    @ApiModelProperty(name = "sqlId", value = "sql编码" ,notes = "2025-04-03 15:58:57")
    @Comment("sql编码")
    @Column(value = "sql_id",type = ColType.AUTO, width = 100)
    @ColumnDefine(columnXtype="text")
    private String sqlId;

    @ApiModelProperty(name = "sqlContent", value = "SQL内容" ,notes = "2025-04-03 15:59:21")
    @Comment("SQL内容")
    @Column(value = "sql_content",type = ColType.TEXT, width = 5000)
    @ColumnDefine(columnXtype="text")
    private String sqlContent;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
