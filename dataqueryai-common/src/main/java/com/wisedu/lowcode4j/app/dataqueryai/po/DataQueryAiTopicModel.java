package com.wisedu.lowcode4j.app.dataqueryai.po;

import com.wisedu.lowcode4j.app.dataqueryai.vo.DataQueryAiTopicModelVo;
import com.wisedu.lowcode4j.common.core.annotation.ColumnDefine;
import com.wisedu.lowcode4j.common.core.annotation.DisableDataFilter;
import com.wisedu.lowcode4j.common.core.annotation.ModelDefine;
import com.wisedu.lowcode4j.common.core.annotation.ModelExt;
import com.wisedu.lowcode4j.common.core.model.BaseDataModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nutz.dao.entity.annotation.ColType;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Comment;
import org.nutz.dao.entity.annotation.Table;
import org.sagacity.sqltoy.config.annotation.SqlToyEntity;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "dataqueryaitopicmodel", description = "AI问数主题域下的模型表")
@Comment("AI问数主题域下的模型表")
@ModelDefine(renderType="form")
@Table(value = "t_dqai_topic_model")
@SqlToyEntity
@ModelExt(extClass = DataQueryAiTopicModelVo.class)
//end_dynamic_declare
@Data
@DisableDataFilter
public class DataQueryAiTopicModel extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041952068L;

    //region start_dynamic_column
    @ApiModelProperty(name = "topicId", value = "主题域编码" ,notes = "2025-03-07 15:20:34")
    @Comment("主题域编码")
    @Column(value = "topic_id",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String topicId;

    @ApiModelProperty(name = "modelId", value = "模型编码" ,notes = "2025-03-07 15:21:00")
    @Comment("模型编码")
    @Column(value = "model_id",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String modelId;

    @ApiModelProperty(name = "modelTable", value = "模型表名称" ,notes = "2025-03-07 15:22:10")
    @Comment("模型表名称")
    @Column(value = "model_table",type = ColType.AUTO, width = 200,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String modelTable;

    @ApiModelProperty(name = "modelName", value = "模型名称" ,notes = "2025-03-07 15:22:10")
    @Comment("模型名称")
    @Column(value = "model_name",type = ColType.AUTO, width = 500,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String modelName;

    @ApiModelProperty(name = "modelType", value = "模型类型：1 预制模型、2 自定义模型" ,notes = "2025-03-07 15:23:18")
    @Comment("模型类型：1 预制模型、2 自定义模型")
    @Column(value = "model_type",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String modelType;

    @ApiModelProperty(name = "modelSynonyms", value = "模型别名" ,notes = "2025-03-07 15:23:48")
    @Comment("模型别名")
    @Column(value = "model_synonyms",type = ColType.TEXT, width = 8000,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String modelSynonyms;

    @ApiModelProperty(name = "modelSemantics", value = "模型用途" ,notes = "2025-03-07 15:24:20")
    @Comment("模型用途")
    @Column(value = "model_semantics",type = ColType.TEXT, width = 8000,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String modelSemantics;

    @ApiModelProperty(name = "aiStudyFlag", value = "当前学习状态 1 学习完毕,2 正在学习,3 未学习" ,notes = "2025-03-07 15:24:54")
    @Comment("当前学习状态 1 学习完毕,2 正在学习,3 未学习")
    @Column(value = "ai_study_flag",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String aiStudyFlag;

    @ApiModelProperty(name = "modifyFlag", value = "是否发生模型修改: 1是 , 0 否" ,notes = "2025-03-07 15:24:54")
    @Comment("是否发生模型修改: 1是 , 0 否")
    @Column(value = "modify_flag",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String modifyFlag;

    @ApiModelProperty(name = "trainError", value = "训练异常堆栈" ,notes = "2025-03-07 15:24:20")
    @Comment("训练异常堆栈")
    @Column(value = "train_error",type = ColType.TEXT, width = 8000,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String trainError;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
