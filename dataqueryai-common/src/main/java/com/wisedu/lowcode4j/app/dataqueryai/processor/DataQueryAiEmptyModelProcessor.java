package com.wisedu.lowcode4j.app.dataqueryai.processor;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.gitee.starblues.bootstrap.annotation.AutowiredType;
import com.wisedu.lowcode4j.common.db.BaseService;
import com.wisedu.lowcode4j.common.model.ModelCache;
import com.wisedu.lowcode4j.common.model.ModelProcessor;
import com.wisedu.lowcode4j.common.model.po.DataModel;
import com.wisedu.lowcode4j.common.model.po.DataModelColumn;
import com.wisedu.lowcode4j.common.model.vo.PageColVo;
import com.wisedu.lowcode4j.common.model.vo.PageSearchColVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * AI问数应用的空模型处理器
 */
@Service("dataQueryAiEmptyModelProcessor")
@Slf4j
public class DataQueryAiEmptyModelProcessor implements ModelProcessor {

    @Autowired
    @AutowiredType(value = AutowiredType.Type.MAIN)
    private BaseService baseService;

    @Override
    public boolean afterModelConfig(List<? extends PageColVo> pageColVos, String actionType, Map params) throws Exception {
        // 参数为空直接返回
        if (params == null || pageColVos == null) {
            log.info("emptyModelProcessor模型干预入参为空：actionType={}", actionType);
            return false;
        }
        // 清楚空模型的系统字段
        pageColVos.clear();
        // 重新去根据传入的模型id组装 pageColVos
        String modelId = (String) params.get("modelId");
        if (StrUtil.isEmpty(modelId)) {
            log.info("emptyModelProcessor模型干预模型id为空：actionType={}, params={}",
                    actionType, params);
            return false;
        }
        if (Objects.equals("datamodelcolumn",modelId)){
            buildDataModelColumn(pageColVos);
            return true;
        }
        return false;
    }

    private void buildDataModelColumn(List<? extends PageColVo> pageColVos) {
        DataModel dataModel = ModelCache.getModelById("main-datamodelcolumn");
        List<DataModelColumn> columns = ObjectUtil.cloneByStream(dataModel.getColumns());

        DataModel topicRoleModel = ModelCache.getModelById("dataqueryai-dataqueryaimodelcolumn");
        if (topicRoleModel != null){
            columns.addAll(ObjectUtil.cloneByStream(topicRoleModel.getColumns()));
        }
        List controls = baseService.convertTypeList(columns, PageSearchColVo.class);
        pageColVos.addAll(controls);
    }
}
