package com.wisedu.lowcode4j.app.dataqueryai.po;

import com.wisedu.lowcode4j.app.dataqueryai.vo.DataQueryAiModelColumnRoleVo;
import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.core.validator.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.model.Processor;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Index;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;
import org.sagacity.sqltoy.model.SecureType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "dataqueryaimodelcolumnrole", description = "模型字段权限配置表")
@Comment("模型字段权限配置表")
@ModelDefine(renderType="form")
@Table(value = "t_dqai_model_column_role")
@SqlToyEntity
@ModelExt(extClass = DataQueryAiModelColumnRoleVo.class)
//end_dynamic_declare
@Data
@DisableDataFilter
public class DataQueryAiModelColumnRole extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041953705L;

    //region start_dynamic_column
    @ApiModelProperty(name = "modelRoleId", value = "模型表权限ID" ,notes = "2025-03-07 16:04:20")
    @Comment("模型表权限ID")
    @Column(value = "model_role_id",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String modelRoleId;

    @ApiModelProperty(name = "modelColumnId", value = "字段编码" ,notes = "2025-03-07 16:04:46")
    @Comment("字段编码")
    @Column(value = "model_column_id",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String modelColumnId;

    @ApiModelProperty(name = "columnDbname", value = "字段名称" ,notes = "2025-03-07 16:05:07")
    @Comment("字段名称")
    @Column(value = "column_dbname",type = ColType.AUTO, width = 200,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String columnDbname;

    @ApiModelProperty(name = "columnAllowed", value = "字段是否允许 1 允许 0 不允许" ,notes = "2025-03-07 16:05:32")
    @Comment("字段是否允许 1 允许 0 不允许")
    @Column(value = "column_allowed",type = ColType.AUTO, width = 10,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String columnAllowed;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
