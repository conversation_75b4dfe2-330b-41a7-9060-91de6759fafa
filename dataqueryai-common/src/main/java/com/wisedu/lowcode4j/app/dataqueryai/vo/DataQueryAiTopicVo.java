package com.wisedu.lowcode4j.app.dataqueryai.vo;

import com.wisedu.lowcode4j.app.dataqueryai.po.DataQueryAiTopic;
import com.wisedu.lowcode4j.common.core.annotation.DisableDataFilter;
import io.swagger.annotations.ApiModel;
import lombok.Data;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "dataqueryaitopicVo", description = "AI问数主题域表Vo")
//end_dynamic_declare
@Data
@DisableDataFilter
public class DataQueryAiTopicVo extends DataQueryAiTopic {

    private static final long serialVersionUID = 4431474721041958470L;

    /**
     * 未学习模型数量
     */
    private long unStudyNum;

    /**
     * 已学习模型数量
     */
    private long studyNum;

    /**
     * 学习中的数量
     */
    private long studyingNum;

    /**
     * 修改模型数量
     */
    private long modifyNum;

    private long maxTotal;

    private long currentNum;

    //region start_dynamic_dict_column
	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
