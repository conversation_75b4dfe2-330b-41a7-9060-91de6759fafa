package com.wisedu.lowcode4j.app.dataqueryai.po;

import com.wisedu.lowcode4j.app.dataqueryai.vo.DataQueryAiTopicVo;
import com.wisedu.lowcode4j.common.core.annotation.ColumnDefine;
import com.wisedu.lowcode4j.common.core.annotation.DisableDataFilter;
import com.wisedu.lowcode4j.common.core.annotation.ModelDefine;
import com.wisedu.lowcode4j.common.core.annotation.ModelExt;
import com.wisedu.lowcode4j.common.core.model.BaseDataModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nutz.dao.entity.annotation.ColType;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Comment;
import org.nutz.dao.entity.annotation.Table;
import org.sagacity.sqltoy.config.annotation.SqlToyEntity;


//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "dataqueryaitopic", description = "AI问数主题域表")
@Comment("AI问数主题域表")
@ModelDefine(renderType="form")
@Table(value = "t_dqai_topic")
@SqlToyEntity
@ModelExt(extClass = DataQueryAiTopicVo.class)
//end_dynamic_declare
@Data
@DisableDataFilter
public class DataQueryAiTopic extends BaseDataModel {

    private static final long serialVersionUID = 4431474721041959438L;

    //region start_dynamic_column
    @ApiModelProperty(name = "topicName", value = "主题域名称" ,notes = "2025-03-07 15:16:58")
    @Comment("主题域名称")
    @Column(value = "topic_name",type = ColType.AUTO, width = 1000,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String topicName;

    @ApiModelProperty(name = "topicDesc", value = "主题域描述" ,notes = "2025-03-07 15:17:34")
    @Comment("主题域描述")
    @Column(value = "topic_desc",type = ColType.AUTO, width = 4000,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String topicDesc;

    @ApiModelProperty(name = "topicIcon", value = "主题域图标" ,notes = "2025-03-07 15:17:34")
    @Comment("主题域图标")
    @Column(value = "topic_icon",type = ColType.AUTO, width = 100,insert=true,update=true,notNull=false)
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String topicIcon;

	//endregion end_dynamic_column

    //region start_dynamic_cascades
	//region end_dynamic_cascades
}
