package com.wisedu.lowcode4j.app.dataqueryai.vo;

import com.wisedu.lowcode4j.app.dataqueryai.po.DataQueryAiTopicModel;
import com.wisedu.lowcode4j.common.core.annotation.ColumnDefine;
import com.wisedu.lowcode4j.common.core.annotation.DisableDataFilter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.entity.annotation.Comment;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "dataqueryaitopicmodelVo", description = "AI问数主题域下的模型表Vo")
//end_dynamic_declare
@Data
@DisableDataFilter
public class DataQueryAiTopicModelVo extends DataQueryAiTopicModel {

    private static final long serialVersionUID = 4431474721041959934L;

    @ApiModelProperty(name = "topicName", value = "主题域名称" ,notes = "2025-03-07 15:28:01")
    @Comment("主题域名称")
    @Column
    @ColumnDefine(columnDisplay=true,columnReadonly=false,columnLinkSelect=true,fuzzySearch=false,columnHidden=false,columnXtype="text")
    private String topicName;

    //region start_dynamic_dict_column
	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
