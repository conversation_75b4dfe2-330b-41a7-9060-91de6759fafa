package com.wisedu.lowcode4j.app.dataqueryai.vo;

import com.wisedu.lowcode4j.common.core.annotation.*;
import com.wisedu.lowcode4j.common.core.constant.*;
import com.wisedu.lowcode4j.common.core.model.*;
import com.wisedu.lowcode4j.common.validator.constant.*;
import com.wisedu.lowcode4j.common.validator.annotation.*;
import com.wisedu.lowcode4j.common.model.Processor;
import com.wisedu.lowcode4j.common.flow.bo.FlowColumn;

import org.nutz.dao.entity.annotation.*;
import org.nutz.dao.entity.annotation.Column;
import org.nutz.dao.interceptor.annotation.*;

import org.sagacity.sqltoy.config.annotation.*;
import org.sagacity.sqltoy.model.MaskType;

import io.swagger.annotations.*;

import lombok.Data;
import java.util.*;
import com.alibaba.fastjson.annotation.*;

import com.wisedu.lowcode4j.app.dataqueryai.po.DataQueryAiModelRole;

//start_dynamic_declare
/**
 * @project lowcode4j
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "dataqueryaimodelrolevoVo", description = "模型表权限配置表Vo")
//end_dynamic_declare
@Data
@DisableDataFilter
public class DataQueryAiModelRoleVo extends DataQueryAiModelRole {

    private static final long serialVersionUID = 4431474721041959335L;

    //region start_dynamic_dict_column
	//endregion end_dynamic_dict_column

    //region start_dynamic_column
	//endregion end_dynamic_column
}
