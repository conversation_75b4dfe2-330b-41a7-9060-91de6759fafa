package com.wisedu.lowcode4j.app.qualitydashboard.zljsc;

import com.gitee.starblues.core.PluginExtensionInfo;
import com.wisedu.lowcode4j.common.core.constant.*;
import org.nutz.lang.Lang;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * 子模块扩展插件信息
 * <AUTHOR>
 * @version 1.0
 * @since 2021-08-01
 */
@Component
public class ZljscExtensionInfo {

    @Autowired
    PluginExtensionInfo extensionInfo;

    @PostConstruct
    public void extensionInfo() {
        Map<String, Object> extMap = extensionInfo.extensionInfo();
        String modelCode = "zljsc";
        //新增模块信息
        putAppModule(modelCode,"质量驾驶舱",extMap);
    }

    private void putAppModule(String moduleCode,String moduleName,Map<String, Object> extMap){
        if(Lang.isEmpty(extMap.get(ApplicationConstant.PLUGIN_MODULE_KEY))){
            extMap.put(ApplicationConstant.PLUGIN_MODULE_KEY, new HashMap<String,String>(16));
        }
        ((Map<String,String>)extMap.get(ApplicationConstant.PLUGIN_MODULE_KEY)).put(moduleCode,moduleName);
    }
}
