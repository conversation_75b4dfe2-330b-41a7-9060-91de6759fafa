define(function (require) {
  return {
    /**
     * 监听指定对象的值发生变化，然后执行特定方法
     */
    pageWatch: {
      'globalVars.name': function (newVal, oldVal) {
        console.log('值改变', newVal, oldVal)
      }
    },
    /**
     * 监听指定对象的值发生变化，然后执行特定方法
     */
    pageComputed: {
      getNewName: function () {
        return this.globalVars.name + '新的'
      }
    },
    var1: "变量",//响应式变量，该变量不能加到组件参数内,但是写到组件模板里
    /**
     * 响应式变量，当值变化时可以影响所有绑定的值,建议把需要绑定到组件参数里的变量申明到这里面
     */
    globalVars: {
      name: '一个变量', //响应式变量name,组件内使用时{{globalVars.name}}
      drawerInstence: null,
      remoteFunction: null,
      frUrl: `${window?.$GLOBAL_CONFIG?.apiPath}toolpub/proxy/fanruan/decision/view/form?modulecode=zljsc_xszt_xszt&appcode=qualitydashboard`,
      pluginObject: {}, //勿删公共js要用
      filterObject: {}, //勿删公共js要用
    },
    /**
     * 固定方法，页面js初始化完成后调用,当前能修改js变量，修改组件初始化属性或者设置组件默认值
     */
    pageCreated: function () {
      this.$request({
        method: 'post',
        url: `/staticFile/getStaticFile`, // 后续接口url按valueKey配置
        data: {
          fileUrl: 'qcet/commonjs/commonJs.js'
        }
      }).then(res => {
        let text = res.data
        let commonJ = new Function('outPage', text)
        let remoteJsFunc = commonJ(this)
        this.globalVars.remoteFunction = remoteJsFunc
      });
    },
    pageActivated() {
      this.globalVars.remoteFunction.addListener();
      window.addEventListener('message', this.setFrameParams, false);
    },
    pageDeactivated() {
      this.globalVars.remoteFunction.destroyListener();
      window.removeEventListener('message', this.setFrameParams, false);
    },
    pageDestroy: function () {
      this.globalVars.remoteFunction.destroyListener();
      window.removeEventListener('message', this.setFrameParams, false);
    },
    /**
     * 描述：
     * @param{event}  内部参数，$com:当前组件,evName:事件名称,evArgs:事件参数(数组) return:undefined

     */
    ev_click_r46v1zwh: function (event) {


    }

  }
})
