<?xml version="1.0" encoding="UTF-8"?>
<app id="qualitydashboard" name="质量驾驶舱" groupId="qualitydashboardgroup">
    <!--菜单（目录）-->
    <menus id="qualitydashboard-zljsc" formRouterName="zljsc" name="质量驾驶舱" showOrder="1">
        <!--菜单（页面）-->
        <menu bindType="default" formRouterName="qxgl" id="qualitydashboard-qxgl"
              menuPattern="pc"
              name="全校概览"
              showOrder="1">
            <routerParams>{ "appCode": "qualitydashboard", "pageCode": "qxgl"}</routerParams>
            <!-- 权限字-表单 -->
            <permCode name="全校概览" permCode="formqxgl" permCodeId="qualitydashboard-qxgl"
                      permCodeType="0"/>
            <!-- 权限字-片段 -->
            <permCode name="全校概览" permCode="formqxgl:fragmentqxgl"
                      permCodeId="fragmentqualitydashboard-qxgl"
                      permCodeType="1">
                <perm name="帆软代理服务接口" url="/toolpub/proxy/**"/>
                <perm name="获取res静态文件" url="/staticFile/getStaticFile"/>
                <perm name="刷选框下拉接口" url="/plugins/qualitydashboard/select/**"/>
            </permCode>
            <btn id="qualitydashboard-qxgl-show" menuPattern="pc" name="显示">
                <permCode name="显示" permCode="formqxgl:fragmentqxgl:show"
                          permCodeId="qualitydashboard-qxgl-show"/>
            </btn>
        </menu>

        <!--菜单（页面）-->
        <menu bindType="default" formRouterName="zyzt" id="qualitydashboard-zyzt"
              menuPattern="pc"
              name="资源主题"
              showOrder="2">
            <routerParams>{ "appCode": "qualitydashboard", "pageCode": "zyzt"}</routerParams>
            <!-- 权限字-表单 -->
            <permCode name="资源主题" permCode="formzyzt" permCodeId="qualitydashboard-zyzt"
                      permCodeType="0"/>
            <!-- 权限字-片段 -->
            <permCode name="资源主题" permCode="formzyzt:fragmentzyzt"
                      permCodeId="fragmentqualitydashboard-zyzt"
                      permCodeType="1">
                <perm name="帆软代理服务接口" url="/toolpub/proxy/**"/>
                <perm name="获取res静态文件" url="/staticFile/getStaticFile"/>
                <perm name="刷选框下拉接口" url="/plugins/qualitydashboard/select/**"/>
            </permCode>
            <btn id="qualitydashboard-zyzt-show" menuPattern="pc" name="显示">
                <permCode name="显示" permCode="formzyzt:fragmentzyzt:show"
                          permCodeId="qualitydashboard-zyzt-show"/>
            </btn>
        </menu>

        <!--菜单（页面）-->
        <menu bindType="default" formRouterName="zszt" id="qualitydashboard-zszt"
              menuPattern="pc"
              name="招生主题"
              showOrder="3">
            <routerParams>{ "appCode": "qualitydashboard", "pageCode": "zszt"}</routerParams>
            <!-- 权限字-表单 -->
            <permCode name="招生主题" permCode="formzszt" permCodeId="qualitydashboard-zszt"
                      permCodeType="0"/>
            <!-- 权限字-片段 -->
            <permCode name="招生主题" permCode="formzszt:fragmentzszt"
                      permCodeId="fragmentqualitydashboard-zszt"
                      permCodeType="1">
                <perm name="帆软代理服务接口" url="/toolpub/proxy/**"/>
                <perm name="获取res静态文件" url="/staticFile/getStaticFile"/>
                <perm name="刷选框下拉接口" url="/plugins/qualitydashboard/select/**"/>
            </permCode>
            <btn id="qualitydashboard-zszt-show" menuPattern="pc" name="显示">
                <permCode name="显示" permCode="formzszt:fragmentzszt:show"
                          permCodeId="qualitydashboard-zszt-show"/>
            </btn>
        </menu>

        <!--菜单（页面）-->
        <menu bindType="default" formRouterName="cgzt" id="qualitydashboard-cgzt"
              menuPattern="pc"
              name="成果主题"
              showOrder="4">
            <routerParams>{ "appCode": "qualitydashboard", "pageCode": "cgzt"}</routerParams>
            <!-- 权限字-表单 -->
            <permCode name="成果主题" permCode="formcgzt" permCodeId="qualitydashboard-cgzt"
                      permCodeType="0"/>
            <!-- 权限字-片段 -->
            <permCode name="成果主题" permCode="formcgzt:fragmentcgzt"
                      permCodeId="fragmentqualitydashboard-cgzt"
                      permCodeType="1">
                <perm name="帆软代理服务接口" url="/toolpub/proxy/**"/>
                <perm name="获取res静态文件" url="/staticFile/getStaticFile"/>
                <perm name="刷选框下拉接口" url="/plugins/qualitydashboard/select/**"/>
            </permCode>
            <btn id="qualitydashboard-cgzt-show" menuPattern="pc" name="显示">
                <permCode name="显示" permCode="formcgzt:fragmentcgzt:show"
                          permCodeId="qualitydashboard-cgzt-show"/>
            </btn>
        </menu>

        <!--菜单（页面）-->
        <menu bindType="default" formRouterName="xszt" id="qualitydashboard-xszt"
              menuPattern="pc"
              name="学生主题"
              showOrder="5">
            <routerParams>{ "appCode": "qualitydashboard", "pageCode": "xszt"}</routerParams>
            <!-- 权限字-表单 -->
            <permCode name="学生主题" permCode="formxszt" permCodeId="qualitydashboard-xszt"
                      permCodeType="0"/>
            <!-- 权限字-片段 -->
            <permCode name="学生主题" permCode="formxszt:fragmentxszt"
                      permCodeId="fragmentqualitydashboard-xszt"
                      permCodeType="1">
                <perm name="帆软代理服务接口" url="/toolpub/proxy/**"/>
                <perm name="获取res静态文件" url="/staticFile/getStaticFile"/>
                <perm name="刷选框下拉接口" url="/plugins/qualitydashboard/select/**"/>
            </permCode>
            <btn id="qualitydashboard-xszt-show" menuPattern="pc" name="显示">
                <permCode name="显示" permCode="formxszt:fragmentxszt:show"
                          permCodeId="qualitydashboard-xszt-show"/>
            </btn>
        </menu>

        <!--菜单（页面）-->
        <menu bindType="default" formRouterName="zlpg" id="qualitydashboard-zlpg"
              menuPattern="pc"
              name="质量评估"
              showOrder="6">
            <routerParams>{ "appCode": "qualitydashboard", "pageCode": "zlpg"}</routerParams>
            <!-- 权限字-表单 -->
            <permCode name="质量评估" permCode="formzlpg" permCodeId="qualitydashboard-zlpg"
                      permCodeType="0"/>
            <!-- 权限字-片段 -->
            <permCode name="质量评估" permCode="formzlpg:fragmentzlpg"
                      permCodeId="fragmentqualitydashboard-zlpg"
                      permCodeType="1">
                <perm name="帆软代理服务接口" url="/toolpub/proxy/**"/>
                <perm name="获取res静态文件" url="/staticFile/getStaticFile"/>
                <perm name="刷选框下拉接口" url="/plugins/qualitydashboard/select/**"/>
            </permCode>
            <btn id="qualitydashboard-zlpg-show" menuPattern="pc" name="显示">
                <permCode name="显示" permCode="formzlpg:fragmentzlpg:show"
                          permCodeId="qualitydashboard-zlpg-show"/>
            </btn>
        </menu>
    </menus>
</app>